Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P1-Identity-HIGH,Identity Management,HIGH,IM-1,template.json,41,App Service 'onefuzz-daily-ui' does not explicitly integrate with Azure Active Directory for identity management.,Enable Azure Active Directory authentication for the App Service to ensure secure identity and access management.,N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-2,template.json,41,App Service 'onefuzz-daily-ui' does not enforce Multi-Factor Authentication (MFA) for users and administrators.,Configure Azure Active Directory authentication with MFA enforcement for all users and administrators accessing the App Service.,N/A,AI,Generic
P1-Identity-MEDIUM,Identity Management,MEDIUM,IM-3,template.json,41,App Service 'onefuzz-daily-ui' does not implement Conditional Access Policies.,Configure Conditional Access Policies in Azure Active Directory to enforce secure access to the App Service.,N/A,AI,Generic
P1-Identity-MEDIUM,Identity Management,MEDIUM,IM-6,template.json,41,App Service 'onefuzz-daily-ui' does not specify the use of Role-Based Access Control (RBAC) for access management.,Assign access rights to the App Service using Azure RBAC to ensure least privilege access.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,120,"App Service 'onefuzz-daily-ui' allows public network access ('publicNetworkAccess': 'Enabled') and has 'ipSecurityRestrictions' with 'ipAddress': 'Any' and 'action': 'Allow', exposing the app to the public internet. This violates the requirement to secure all public endpoints.",Restrict 'ipSecurityRestrictions' to only allow trusted IP ranges and set 'publicNetworkAccess' to 'Disabled' if public access is not required.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,128,"App Service 'onefuzz-daily-ui' allows public network access to the SCM endpoint ('scmIpSecurityRestrictions' with 'ipAddress': 'Any' and 'action': 'Allow'), exposing the SCM endpoint to the public internet. This violates the requirement to secure all public endpoints.",Restrict 'scmIpSecurityRestrictions' to only allow trusted IP ranges and set 'publicNetworkAccess' to 'Disabled' if public access is not required.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-3,template.json,120,"App Service 'onefuzz-daily-ui' does not implement Network Security Groups (NSGs) to control inbound and outbound traffic. 'ipSecurityRestrictions' allows all traffic ('ipAddress': 'Any', 'action': 'Allow').",Configure 'ipSecurityRestrictions' to allow only trusted IP ranges and consider integrating with NSGs for additional network layer protection.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-3,template.json,128,"App Service 'onefuzz-daily-ui' SCM endpoint does not implement Network Security Groups (NSGs) to control inbound and outbound traffic. 'scmIpSecurityRestrictions' allows all traffic ('ipAddress': 'Any', 'action': 'Allow').",Configure 'scmIpSecurityRestrictions' to allow only trusted IP ranges and consider integrating with NSGs for additional network layer protection.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,120,App Service 'onefuzz-daily-ui' is accessible via public endpoints and does not use private endpoints for secure access.,Implement Azure Private Endpoints for the App Service to restrict access to private networks only.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,128,App Service 'onefuzz-daily-ui' SCM endpoint is accessible via public endpoints and does not use private endpoints for secure access.,Implement Azure Private Endpoints for the SCM endpoint to restrict access to private networks only.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,120,App Service 'onefuzz-daily-ui' does not specify the use of customer-managed keys (CMK) or confirm encryption at rest for associated storage/data.,Enable encryption at rest for all associated storage and configure customer-managed keys if required for compliance.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,54,"App Service 'onefuzz-daily-ui' has 'sslState' set to 'Disabled' for hostName 'onefuzz-daily-ui.azurewebsites.net', which allows unencrypted HTTP traffic. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates entries to enforce HTTPS and ensure encryption in transit.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,62,"App Service 'onefuzz-daily-ui' has 'sslState' set to 'Disabled' for hostName 'onefuzz-daily-ui.scm.azurewebsites.net', which allows unencrypted HTTP traffic. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates entries to enforce HTTPS and ensure encryption in transit.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,120,"App Service 'onefuzz-daily-ui' configuration includes a 'publishingUsername' property, which may expose sensitive information if not securely managed. Sensitive data should be stored in Azure Key Vault.",Store sensitive configuration values such as publishing credentials in Azure Key Vault and reference them securely in the template.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,120,App Service 'onefuzz-daily-ui' does not use Azure Key Vault references for application secrets or sensitive configuration data.,Integrate Azure Key Vault references for all application secrets and sensitive configuration data.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-5,template.json,120,App Service 'onefuzz-daily-ui' does not specify backup and recovery settings.,Configure backup and recovery for the App Service to ensure data resilience.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-6,template.json,120,App Service 'onefuzz-daily-ui' does not specify the use of customer-managed keys (CMK) for encryption.,Configure customer-managed keys (CMK) for encryption at rest if required for compliance or regulatory needs.,N/A,AI,Generic
