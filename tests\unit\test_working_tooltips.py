#!/usr/bin/env python3
"""
Test script to validate working tooltip functionality in HTML reports.
"""

import sys
import json
import tempfile
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from security_opt import SecurityPRReviewer
    print("✅ Successfully imported SecurityPRReviewer")
except ImportError as e:
    print(f"❌ Failed to import SecurityPRReviewer: {e}")
    sys.exit(1)

def test_working_tooltips():
    """Test HTML generation with working tooltips."""
    print("\n🌐 Testing HTML Generation with Working Tooltips")
    print("=" * 60)
    
    try:
        # Create temporary directory for test files
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create sample Bicep file
            bicep_content = """
resource storageAccount 'Microsoft.Storage/storageAccounts@2023-01-01' = {
  name: 'teststorage'
  properties: {
    publicNetworkAccess: 'Enabled'  // Security issue for NS-2
    networkAcls: {
      defaultAction: 'Allow'  // Security issue for NS-1
    }
  }
}

resource networkSecurityGroup 'Microsoft.Network/networkSecurityGroups@2023-01-01' = {
  name: 'test-nsg'
  properties: {
    securityRules: []  // Security issue for NS-3
  }
}
"""
            bicep_file = temp_path / "test.bicep"
            bicep_file.write_text(bicep_content)
            
            # Initialize reviewer
            reviewer = SecurityPRReviewer(local_folder=str(temp_path))
            
            # Create sample findings with controls that have URLs
            sample_findings = [
                {
                    "file_path": str(bicep_file),
                    "control_id": "NS-1",
                    "severity": "HIGH",
                    "line": 6,
                    "description": "Network security group rules not properly configured",
                    "remediation": "Configure NSG with deny-by-default rules",
                    "source": "ai_analysis"
                },
                {
                    "file_path": str(bicep_file),
                    "control_id": "NS-2",
                    "severity": "CRITICAL",
                    "line": 4,
                    "description": "Public network access enabled without restrictions",
                    "remediation": "Configure private endpoints and restrict public access",
                    "source": "ai_analysis"
                },
                {
                    "file_path": str(bicep_file),
                    "control_id": "NS-3",
                    "severity": "MEDIUM",
                    "line": 13,
                    "description": "NSG security rules are empty",
                    "remediation": "Add appropriate security rules to NSG",
                    "source": "ai_analysis"
                }
            ]
            
            # Create output directory
            output_dir = Path("final_tooltip_reports")
            output_dir.mkdir(exist_ok=True)
            
            print(f"📊 Generating HTML report with {len(sample_findings)} findings...")
            
            # Export HTML with tooltip functionality
            reviewer.export_findings(sample_findings, format="html", output_dir=str(output_dir))
            
            # Find generated HTML file
            html_files = list(output_dir.glob("*.html"))
            if html_files:
                html_file = html_files[0]
                print(f"✅ Generated HTML report: {html_file}")
                
                # Check if tooltip data is present in HTML
                with open(html_file, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # Check for tooltip functionality
                has_tooltip_function = 'addTooltipFunctionality' in html_content
                has_tooltip_data = 'window.tooltipLinks' in html_content and 'window.tooltipLinks = {};' not in html_content
                has_tooltip_icons = '📚' in html_content
                has_tooltip_css = '.custom-tooltip' in html_content
                
                print(f"   🔧 Tooltip function present: {'✅' if has_tooltip_function else '❌'}")
                print(f"   📊 Tooltip data populated: {'✅' if has_tooltip_data else '❌'}")
                print(f"   📚 Tooltip icons included: {'✅' if has_tooltip_icons else '❌'}")
                print(f"   🎨 Tooltip CSS styling: {'✅' if has_tooltip_css else '❌'}")
                
                # Extract and analyze tooltip data
                if 'window.tooltipLinks = ' in html_content:
                    start = html_content.find('window.tooltipLinks = ') + len('window.tooltipLinks = ')
                    end = html_content.find('};', start) + 1
                    tooltip_data_str = html_content[start:end]
                    
                    try:
                        tooltip_data = json.loads(tooltip_data_str)
                        print(f"   🔗 Tooltip data for controls: {list(tooltip_data.keys())}")
                        
                        total_links = 0
                        for control_id, data in tooltip_data.items():
                            links_count = len(data.get('raw_links', []))
                            has_guidance = bool(data.get('azure_guidance'))
                            total_links += links_count
                            print(f"      {control_id}: {links_count} links, guidance: {'Yes' if has_guidance else 'No'}")
                            
                            # Show first link as example
                            if data.get('raw_links'):
                                first_link = data['raw_links'][0]
                                print(f"         Example link: {first_link}")
                        
                        print(f"   📈 Total links across all controls: {total_links}")
                        
                        # Validate that we have working tooltip data
                        if total_links > 0:
                            print("   🎉 SUCCESS: Tooltip data contains actual URLs!")
                            
                            # Also export CSV to show the enhanced functionality
                            print(f"\n📄 Also generating enhanced CSV report...")
                            reviewer.export_findings(sample_findings, format="csv", output_dir=str(output_dir))
                            
                            csv_files = list(output_dir.glob("*.csv"))
                            if csv_files:
                                csv_file = csv_files[0]
                                print(f"✅ Generated CSV report: {csv_file}")
                                
                                # Check CSV content for reference links
                                with open(csv_file, 'r', encoding='utf-8') as f:
                                    csv_content = f.read()
                                
                                has_reference_links = 'Reference Links' in csv_content
                                has_azure_guidance = 'Azure Guidance' in csv_content
                                has_implementation_context = 'Implementation Context' in csv_content
                                
                                print(f"   📋 CSV Reference Links column: {'✅' if has_reference_links else '❌'}")
                                print(f"   🔵 CSV Azure Guidance column: {'✅' if has_azure_guidance else '❌'}")
                                print(f"   📖 CSV Implementation Context column: {'✅' if has_implementation_context else '❌'}")
                            
                            return True
                        else:
                            print("   ⚠️ WARNING: No URLs found in tooltip data")
                            return False
                            
                    except json.JSONDecodeError as e:
                        print(f"   ❌ Could not parse tooltip data: {e}")
                        return False
                else:
                    print("   ❌ No tooltip data found in HTML")
                    return False
                    
            else:
                print("❌ No HTML file generated")
                return False
                
    except Exception as e:
        print(f"❌ Error during HTML generation test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run working tooltip validation."""
    print("🧪 WORKING TOOLTIP FUNCTIONALITY VALIDATION")
    print("=" * 70)
    
    success = test_working_tooltips()
    
    if success:
        print("\n🎉 TOOLTIP FUNCTIONALITY VALIDATION SUCCESSFUL!")
        print("\n📋 What was validated:")
        print("   ✅ HTML tooltip function implementation")
        print("   ✅ Tooltip data population with real URLs")
        print("   ✅ Tooltip icons and CSS styling")
        print("   ✅ Link extraction from benchmark data")
        print("   ✅ Enhanced CSV export with reference links")
        
        print("\n💡 How to use the generated reports:")
        print("   🌐 HTML: Open in browser, click 📚 icons next to control IDs")
        print("   📄 CSV: Open in Excel/Sheets, check 'Reference Links' column")
        print("   🔗 Links: Direct access to Azure documentation")
        print("   📚 Tooltips: Rich content with Azure guidance and clickable URLs")
        
        print(f"\n📁 Check the 'final_tooltip_reports' directory for generated reports")
        
        return True
    else:
        print("\n❌ Tooltip functionality validation failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
