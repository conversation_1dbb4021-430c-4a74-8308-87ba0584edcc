#!/usr/bin/env python3
"""
Quick test to verify report ordering fix is working.
"""

import os
import sys
import tempfile
from pathlib import Path

# Set environment variables for testing
os.environ['ENFORCE_DOMAIN_PRIORITY'] = 'true'
os.environ['USE_OPTIMIZED_PROMPTS'] = 'true'
os.environ['ANALYSIS_SEED'] = '42'

try:
    from security_opt import SecurityPRReviewer
    print("✅ Successfully imported SecurityPRReviewer")
    
    # Test the sorting function with mock findings
    with tempfile.TemporaryDirectory() as temp_dir:
        reviewer = SecurityPRReviewer(local_folder=temp_dir)
        print("✅ Successfully created SecurityPRReviewer instance")
        
        # Create mock findings with different domains
        mock_findings = [
            {
                "control_id": "DP-1",
                "severity": "HIGH",
                "file_path": "test.json",
                "line": 10,
                "description": "Data protection issue"
            },
            {
                "control_id": "IM-1", 
                "severity": "CRITICAL",
                "file_path": "test.json",
                "line": 5,
                "description": "Identity management issue"
            },
            {
                "control_id": "NS-1",
                "severity": "HIGH", 
                "file_path": "test.json",
                "line": 15,
                "description": "Network security issue"
            }
        ]
        
        print(f"📊 Testing sorting with {len(mock_findings)} mock findings")
        
        # Test the sorting function
        sorted_findings = reviewer._sort_findings_by_priority(mock_findings)
        print("✅ Successfully sorted findings")
        
        # Display results
        print("\n📋 Sorted findings order:")
        for i, finding in enumerate(sorted_findings, 1):
            control_id = finding.get("control_id", "N/A")
            severity = finding.get("severity", "N/A")
            description = finding.get("description", "N/A")
            print(f"   {i}. {control_id} ({severity}) - {description}")
        
        # Check if Identity Management (IM-1) comes first
        if sorted_findings and sorted_findings[0].get("control_id") == "IM-1":
            print("\n✅ SUCCESS: Identity Management issue correctly prioritized first!")
        else:
            print("\n⚠️  WARNING: Identity Management issue not first in order")
        
        print("\n🎉 Quick test completed successfully!")
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ Test error: {e}")
    sys.exit(1)
