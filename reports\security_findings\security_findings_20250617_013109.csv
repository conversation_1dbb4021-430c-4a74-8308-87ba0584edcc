Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P1-Identity-HIGH,Identity Management,HIGH,IM-2,template.json,54,App Service 'onefuzz-daily-ui' does not enforce multi-factor authentication (MFA) for access.,Enable and enforce MFA for all users and administrators accessing the App Service.,N/A,AI,Generic
P1-Identity-MEDIUM,Identity Management,MEDIUM,IM-3,template.json,54,Privileged Identity Management (PIM) is not configured for App Service 'onefuzz-daily-ui'.,Implement Azure AD Privileged Identity Management (PIM) to manage privileged access to the App Service.,N/A,AI,Generic
P1-Identity-MEDIUM,Identity Management,MEDIUM,IM-6,template.json,54,Role-Based Access Control (RBAC) is not explicitly configured for App Service 'onefuzz-daily-ui'.,Assign access rights using RBAC to ensure least privilege for users and applications.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,54,"App Service 'onefuzz-daily-ui' allows public network access ('publicNetworkAccess': 'Enabled') and has 'ipSecurityRestrictions' with 'ipAddress': 'Any' and 'action': 'Allow', permitting unrestricted public access.",Restrict 'ipSecurityRestrictions' to specific IP ranges and set 'publicNetworkAccess' to 'Disabled' or limit to trusted networks only.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,181,"App Service config for 'onefuzz-daily-ui' has 'ipSecurityRestrictions' with 'ipAddress': 'Any' and 'action': 'Allow', which exposes the app to the public internet without restriction.",Remove the 'Allow all' rule and define 'ipSecurityRestrictions' to only allow trusted IP addresses or ranges.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,189,"App Service config for 'onefuzz-daily-ui' has 'scmIpSecurityRestrictions' with 'ipAddress': 'Any' and 'action': 'Allow', exposing the SCM endpoint to the public internet.",Restrict 'scmIpSecurityRestrictions' to trusted IP addresses or ranges to secure the SCM endpoint.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,54,"App Service 'onefuzz-daily-ui' does not use private endpoints; 'publicNetworkAccess' is set to 'Enabled', increasing exposure risk.",Configure a private endpoint for the App Service and set 'publicNetworkAccess' to 'Disabled' to restrict access to private networks.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,54,App Service 'onefuzz-daily-ui' does not specify encryption at rest settings or use customer-managed keys for data storage.,Enable encryption at rest for all associated storage and configure customer-managed keys if required for compliance.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,54,"App Service 'onefuzz-daily-ui' has 'hostNameSslStates' entries with 'sslState' set to 'Disabled' for 'onefuzz-daily-ui.azurewebsites.net' and 'onefuzz-daily-ui.scm.azurewebsites.net', which disables encryption in transit for these hostnames.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates to ensure TLS is enforced for all hostnames.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,181,"App Service config includes 'publishingUsername', which may indicate credentials are stored in the template instead of Azure Key Vault.",Remove sensitive information from the template and reference credentials securely from Azure Key Vault.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-6,template.json,54,App Service 'onefuzz-daily-ui' does not specify use of customer-managed keys (CMK) for encryption.,Configure the App Service to use customer-managed keys for encryption at rest if handling sensitive or regulated data.,N/A,AI,Generic
