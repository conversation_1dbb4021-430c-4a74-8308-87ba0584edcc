# Templates Directory

This directory contains all templates and prompts used by IaC Guardian for generating reports and performing security analysis.

## Directory Structure

```
templates/
├── html/           # HTML report templates
│   ├── base.html   # Base HTML template with Glass UI styling
│   ├── report.html # Main security report template
│   └── components/ # Reusable HTML components
├── prompts/        # AI analysis prompts
│   ├── security/   # Security analysis prompts
│   ├── system/     # System messages
│   └── context/    # Context generation prompts
├── css/            # Stylesheet templates
│   ├── glass-ui.css    # Glass UI framework styles
│   ├── responsive.css  # Responsive design styles
│   └── themes/         # Color themes and variations
└── js/             # JavaScript templates
    ├── report.js   # Report functionality
    ├── dialogs.js  # Modal dialogs and interactions
    └── utils.js    # Utility functions
```

## Template Types

### HTML Templates
- **Base Template**: Core HTML structure with Glass UI framework
- **Report Template**: Security findings report layout
- **Component Templates**: Reusable UI components (cards, buttons, modals)

### Prompt Templates
- **Security Analysis**: Prompts for AI-driven security analysis
- **System Messages**: Role definitions and instructions for AI models
- **Context Generation**: Templates for building analysis context

### CSS Templates
- **Glass UI Framework**: Modern glassmorphism design system
- **Responsive Styles**: Mobile-first responsive design
- **Theme Variations**: Color schemes and visual themes

### JavaScript Templates
- **Interactive Features**: Report interactivity and user interactions
- **Data Visualization**: Charts, graphs, and visual elements
- **Utility Functions**: Common JavaScript utilities

## Usage

Templates are loaded dynamically by the IaC Guardian application. The template system supports:

- Variable substitution using Python string formatting
- Conditional content rendering
- Component composition
- Theme customization

## Template Variables

Common variables used across templates:

- `{timestamp}`: Report generation timestamp
- `{findings}`: Security findings data
- `{summary}`: Analysis summary
- `{metadata}`: Report metadata
- `{graph_section}`: Dependency graph visualization

## Customization

To customize templates:

1. Modify existing template files
2. Add new templates following the naming conventions
3. Update the template loader in the application code
4. Test changes with sample data

## Glass UI Framework

The HTML templates use a custom Glass UI framework with:

- HSL color palette (primary: hue 223, secondary: hue 178)
- Glassmorphism effects with transparency
- Responsive design patterns
- Accessibility features
- Modern typography (Inter font family)
