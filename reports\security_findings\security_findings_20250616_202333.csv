Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
CRITICAL,NS-1,keyvault.bicep,22,"The Key Vault 'networkAcls.defaultAction' is set to 'Allow', which permits unrestricted network access to the Key Vault from any IP address, exposing sensitive secrets and keys to potential attacks. This violates the control's requirement to protect sensitive or critical resources using network security mechanisms.",Set 'networkAcls.defaultAction' to 'Deny' to restrict access to the Key Vault. Specify allowed 'ipRules' and 'virtualNetworkRules' explicitly to limit access only to authorized networks. Review and verify the subnet and IP rules defined.,N/A,AI,Generic
CRITICAL,NS-2,keyvault.bicep,22,"By configuring 'networkAcls.defaultAction' as 'Allow', the Key Vault's public endpoint is unprotected, making it accessible from the public internet. This increases the attack surface and risk of unauthorized access to sensitive secrets.",Set 'networkAcls.defaultAction' to 'Deny' to secure the public endpoint. Only allow authorized networks or specific IP addresses via 'ipRules' or by using private endpoints. Remove public network access if not strictly required.,N/A,AI,Generic
HIGH,NS-5,app-config.bicep,7,"The App Configuration resource does not implement or expose configuration for private endpoints, leaving the endpoint potentially accessible over the public internet. This increases exposure to unauthorized access.","Configure the App Configuration instance to use Azure Private Endpoint, ensuring access is restricted to the private network. Reference and deploy a 'Microsoft.Network/privateEndpoints' resource connected to the App Configuration store.",N/A,AI,Generic
HIGH,NS-2,app-config.bicep,7,The App Configuration store is deployed without explicit restriction on public endpoints. Publicly accessible App Configuration instances are at higher risk of attacks and data exfiltration.,"Limit public endpoint access by enabling private endpoints and configuring IP firewall rules ('publicNetworkAccess': 'Disabled' in properties), controlling which networks can access the service.",N/A,AI,Generic
HIGH,DP-3,app-config.bicep,19,"App Configuration key-values are parameterized as 'object[]', and their values are set directly in the template. If secrets or sensitive values are passed in the 'keyValues' parameter, these could be exposed in deployment logs or template source code, violating best practices for secret management.","Ensure no secrets (such as credentials, API keys, or connection strings) are passed via template parameters or hardcoded in the template. Store all secrets in Azure Key Vault and reference them securely from your application at runtime.",N/A,AI,Generic
HIGH,NS-5,event-grid.bicep,1,"The template provisions Event Grid System Topics and configures event subscriptions with a StorageQueue destination. There is no evidence that private endpoints are enforced for the related Storage Accounts or the Service Bus-like resources (Storage Queues), which can result in exposure of queues over public networks.","Configure private endpoints for all Storage Accounts used with Event Grid and for any Storage Queue used as a destination, ensuring that all access occurs over a private link, not the public internet.",N/A,AI,Generic
HIGH,NS-1,event-grid.bicep,1,No Network Security Groups (NSGs) or Azure Firewall are referenced for protecting the Storage Accounts used as sources or the Storage Queues used as destinations in the event subscription. This leaves resources potentially exposed to unfiltered network traffic.,Apply NSGs or Azure Firewall rules to the subnets containing these storage and queue resources to restrict access to trusted sources and services only.,N/A,AI,Generic
HIGH,NS-2,event-grid.bicep,1,The template does not show any measures to restrict public endpoints for the Azure Storage Accounts or queues. Unrestricted public endpoints can expose sensitive event data or allow unauthorized access.,"Restrict access to public endpoints for all storage and messaging resources, and ensure they are only accessible through private endpoints or restricted firewall rules.",N/A,AI,Generic
HIGH,DP-3,function-settings.bicep,36,"The value for 'APPINSIGHTS_INSTRUMENTATIONKEY' is assigned directly from the 'app_insights_key' parameter within application settings instead of using a managed secret reference (e.g., Azure Key Vault). Application Insights instrumentation keys are sensitive and should not be set directly in app settings.","Store the Application Insights instrumentation key in Azure Key Vault and reference it using a Key Vault reference in the Function App application settings. Only assign the reference in the template, not the raw secret value.",N/A,AI,Generic
HIGH,DP-3,function-settings.bicep,22,The 'ado_access_client_id' parameter is hardcoded with a specific GUID in the template. Hardcoding sensitive identifiers may increase the risk of identity exposure if the identifier is considered confidential or privileged.,"Consider passing sensitive client IDs as secure parameters or storing them in a secure location such as Azure Key Vault, particularly if this identifier is used for privileged access.",N/A,AI,Generic
HIGH,NS-1,function-settings.bicep,25,"The storage account resource 'funcStorage' is declared as an existing resource, but the template does not show or guarantee any network security measures such as NSG rules, private endpoints, or firewall restrictions protecting the storage account used by the Function App.","Ensure that the referenced storage account has NSG or firewall rules in place, restricting network access to only required services, such as the Application Service. Use private endpoints to limit exposure of the storage account.",N/A,AI,Generic
HIGH,NS-2,function-settings.bicep,55,"The application setting 'AzureSignalRConnectionString__serviceUri' constructs a public endpoint URL (https://${signalRName}.service.signalr.net) for SignalR access. No mention is made of access restrictions, private endpoints, or limiting exposure, potentially exposing the endpoint publicly.","Configure SignalR with private endpoints and restrict access using access controls or firewall rules. If public endpoints must be used, ensure strict authentication and authorization controls are in place.",N/A,AI,Generic
HIGH,NS-6,function-settings.bicep,60,"Application setting 'ONEFUZZ_INSTANCE' constructs a public URL (https://${instance_name}.azurewebsites.net), indicating that the Function App will be accessible via a public endpoint. There are no explicit configurations shown for access restrictions (e.g., IP restrictions, service endpoints, or private endpoints).","Restrict public endpoint access by configuring access restrictions on the App Service, or use Private Endpoints to reduce exposure.",N/A,AI,Generic
HIGH,DP-2,function-settings.bicep,1,"The template does not specify or enforce encryption settings for the referenced storage account ('funcStorage'); it relies on the existing resource configuration, which may not have encryption at rest enabled.","Ensure that the referenced storage account has encryption at rest enabled (e.g., by using Storage Service Encryption with customer-managed or Microsoft-managed keys). Verify and enforce this in the storage account resource configuration.",N/A,AI,Generic
HIGH,DP-9,function-settings.bicep,1,"No explicit enforcement of encryption in transit (TLS 1.2 or above) for connections to the Storage Account, AppService, or SignalR Service. Default configurations may not guarantee the minimum required TLS protocol version.","Explicitly set minimum TLS version to 1.2 for all service resources (Storage Account, App Service, SignalR) and update client configurations to enforce secure protocols.",N/A,AI,Generic
HIGH,NS-5,function-settings.bicep,25,"There is no evidence of private endpoints being used for the Storage Account resource ('funcStorage'), meaning the storage remains potentially accessible over the public internet.",Configure the Storage Account with a Private Endpoint to ensure it is accessible only from within the trusted virtual network.,N/A,AI,Generic
HIGH,NS-5,function-settings.bicep,55,"SignalR endpoint is accessed via the public FQDN in settings. There is no evidence of the use of private endpoints for SignalR, increasing the risk of exposure.",Enable private endpoints for the SignalR service and update configuration to use the private DNS zone.,N/A,AI,Generic
HIGH,NS-1,function.bicep,39,The storage account resource 'funcStorage' is referenced as 'existing' and there is no evidence in the template that it is protected by a Network Security Group (NSG) or Azure Firewall. Unprotected storage accounts are at risk from unauthorized access or data exfiltration.,"Ensure that the referenced storage account is restricted via network security controls, either by using private endpoints, service endpoints, or explicitly applying NSG rules or Azure Firewall for allowed networks only.",N/A,AI,Generic
HIGH,NS-2,function.bicep,52,"The Azure Function App 'function' resource does not explicitly restrict public network access. Without settings such as 'publicNetworkAccess: Disabled' or proper VNet integration, the Function App may be accessible from the public internet.","Set 'publicNetworkAccess' to 'Disabled' for the Function App, and ensure that only necessary internal networks can reach the function by using VNet integration and Private Endpoints if possible.",N/A,AI,Generic
HIGH,NS-5,function.bicep,39,"The storage account 'funcStorage' is not configured within this template to use a Private Endpoint, nor is there assurance that private endpoint connectivity is enforced for blob or queue services. This increases the attack surface and risk of data exfiltration.",Configure Private Endpoints for the storage account's services that the function app will use. Ensure the Function App interacts with the storage account exclusively via a Private Endpoint.,N/A,AI,Generic
HIGH,DP-3,function.bicep,78,"The SAS token for storage is constructed within the template ('sas.accountSasToken') and included directly in the logs configuration, potentially exposing sensitive storage access information. This is a risk if the template or deployment output is stored insecurely.","Generate SAS tokens at runtime, not in code or templates. Use managed identities for Azure resource access wherever possible, or securely retrieve SAS from a Key Vault rather than constructing inline within the template.",N/A,AI,Generic
HIGH,NS-1,hub-network.bicep,2,"The defined virtual network and subnet ('hub-vnet', 'hub-subnet') do not have any associated Network Security Groups (NSGs), leaving resources unprotected from unwanted inbound and outbound network traffic. This violates the benchmark's requirement to use NSGs or Azure Firewall to protect resources.",Associate an NSG with the subnet 'hub-subnet' to define and enforce security rules that restrict inbound and outbound traffic according to the principle of least privilege.,N/A,AI,Generic
HIGH,NS-3,hub-network.bicep,2,"No Network Security Groups (NSGs) are implemented for the subnet, which means network traffic (including to App Service with delegated subnet) is uncontrolled at the subnet level. This violates the explicit requirement to use NSGs for controlling network access.",Create an NSG resource and associate it with 'hub-subnet' to control the permissible inbound and outbound network traffic.,N/A,AI,Generic
HIGH,DP-3,instance-config.bicep,5,"Sensitive configuration values, including 'cli_client_id', 'tenant_id', and 'monitoringGCSAuthId' are passed via the 'specificConfig' parameter and appear potentially inline or from external JSON sources, but no use of Azure Key Vault or explicit secure referencing is present. This increases the risk of accidental disclosure of secrets or sensitive information.","Store all secrets, passwords, client IDs, and sensitive configuration data in Azure Key Vault. Reference them in the template using secure parameters or Key Vault references, rather than passing them inline or from unsecured files.",N/A,AI,Generic
HIGH,NS-2,ip-rules.bicep,3,"The file defines extremely broad corporate network IP ranges (e.g., '*******/8', '********/8'), which are likely to contain numerous public address spaces. Allowing traffic from such wide public IP ranges (including /8s) significantly increases exposure to unauthorized access, violating the requirement to secure public endpoints.","Review and restrict IP address ranges to only those absolutely necessary and known to be under your direct control. Avoid using broad public IP ranges and instead allow only specific, trusted, and minimum necessary subnets as per NS-2 guidance.",N/A,AI,Generic
HIGH,NS-3,keyvault.bicep,22,No Network Security Groups (NSGs) are attached to the Key Vault subnet or referenced in the template. NSGs should be used in conjunction with virtual network service endpoints or private links to control inbound and outbound access to the Key Vault.,Associate an NSG with the 'hubSubnetId' referenced by the Key Vault's virtual network rules. Configure the NSG to restrict access only to approved sources and required ports. Explicitly manage traffic to/from the subnet hosting the Key Vault.,N/A,AI,Generic
HIGH,DP-3,operational-insights.bicep,67,"The output 'appInsightsInstrumentationKey' exposes the Application Insights instrumentation key as a deployment output. Instrumentation keys are sensitive secrets and should not be returned in outputs as this risks accidental disclosure through portal, scripts, or CI/CD logs.","Remove the output of 'appInsightsInstrumentationKey'. If access to Application Insights is required, configure RBAC for access. If the key must be provided, use Azure Key Vault to securely store and retrieve it.",N/A,AI,Generic
HIGH,DP-3,operational-insights.bicep,66,"The output 'appInsightsAppId' exposes the Application Insights Application ID. Although less sensitive than the instrumentation key, exposing this value is not generally advisable unless strictly required by downstream automation, as it may aid attackers in reconnaissance.",Limit outputs to only those required by automated processes. Do not expose identifiers unless business needs demand it and ensure access to deployment outputs is tightly controlled.,N/A,AI,Generic
HIGH,NS-1,scaleset-networks.bicep,1,No Network Security Groups (NSGs) or Azure Firewall are associated with the virtual network or its subnet. This means there is no granular access control to protect the attached resources from potential network-based attacks.,"Associate an NSG with the 'scaleset' subnet and define rules according to the principle of least privilege. Optionally, deploy an Azure Firewall to further protect resources.",N/A,AI,Generic
HIGH,NS-3,scaleset-networks.bicep,34,"The 'scaleset' subnet is missing a Network Security Group (NSG). Without an NSG, there is no control over inbound or outbound traffic to this subnet, which increases the risk of exposure to unauthorized access.",Define and associate a Network Security Group (NSG) with the 'scaleset' subnet and specify rules to restrict inbound and outbound traffic as needed for your workloads.,N/A,AI,Generic
HIGH,NS-1,server-farms.bicep,1,"There are no network security controls, such as Network Security Groups (NSGs) or Azure Firewall, protecting the deployed App Service or Key Vault resources. This leaves the resources potentially exposed to untrusted network traffic, in violation of ASB NS-1.","Implement NSGs or use Azure Firewall to restrict inbound and outbound traffic to only what is necessary. For App Services, configure Access Restrictions to limit access to the app. Protect Key Vaults with firewall rules or private endpoints.",N/A,AI,Generic
HIGH,NS-2,server-farms.bicep,1,"The App Service and Key Vault resources are configured without access restrictions or private endpoints, potentially exposing public endpoints to the internet and increasing the attack surface, which violates ASB NS-2.","Configure App Service access restrictions to only allow access from trusted IPs or subnets, and protect Key Vault by enabling firewall rules and/or private endpoints.",N/A,AI,Generic
HIGH,NS-5,server-farms.bicep,1,"The Key Vault referenced in the template ('genevaCertVaultId') is accessed using a public resource ID, and the template does not deploy or require private endpoints for secure resource access, violating ASB NS-5.","Implement Azure Private Endpoints for Key Vault access, and configure App Service access restrictions to only allow connections via private endpoints or trusted networks.",N/A,AI,Generic
HIGH,IM-8,server-farms.bicep,1,"The template does not configure Managed Identities for the App Service or App Service Plan, which is a best practice for secure authentication to Key Vault. This omission can lead to use of less secure authentication methods or hardcoded secrets.",Add a managed identity to the App Service (web app or function app) and grant it the required permissions to the Key Vault to securely access secrets and certificates.,N/A,AI,Generic
HIGH,DP-2,server-farms.bicep,1,There is no explicit configuration enforcing TLS 1.2 or higher for the App Service. Failing to enforce secure protocols puts in-transit data at risk.,Configure the App Service to require HTTPS only and enforce a minimum TLS version of 1.2 or above within the App Service configuration.,N/A,AI,Generic
HIGH,NS-2,signalR.bicep,4,"The Azure SignalR Service resource is being deployed without explicit network access controls or restrictions on public endpoints. By default, Azure SignalR exposes public endpoints, which could lead to unnecessary exposure if not properly restricted.","Restrict public network access to the SignalR Service by enabling private endpoints or setting 'publicNetworkAccess' to 'Disabled'. Additionally, configure IP ACLs to only allow trusted sources to access the service.",N/A,AI,Generic
HIGH,NS-2,storage-accounts.bicep,24,"Storage accounts have 'networkAcls.defaultAction' set to 'Allow', meaning that any client not explicitly denied by IP or virtual network rule will have access. This may expose the storage account to the public internet, increasing risk of unauthorized access, violating secure public endpoint recommendations.",Set 'networkAcls.defaultAction' to 'Deny' to prevent public access by default. Explicitly allow only required IPs and subnets for business needs. Review and minimize the 'bypass' setting.,N/A,AI,Generic
HIGH,NS-1,storage-accounts.bicep,24,"Storage accounts without network-level protections such as NSGs or firewalls allow access from broad networks. The use of 'defaultAction: Allow' and relatively broad bypass settings ('AzureServices, Logging, Metrics') weaken network security posture for storage accounts.",Enforce storage account access by setting 'defaultAction' to 'Deny' and only permitting trusted subnets or IP addresses. Consider an Azure Firewall or implementing NSGs on associated subnets.,N/A,AI,Generic
HIGH,DP-2,storage-accounts.bicep,15,"There is no explicit specification of 'minimumTlsVersion' in the storage account resources. The default Azure setting may allow older, less secure protocols. TLS 1.0/1.1 should be disabled, and only 1.2 or above should be enforced.",Explicitly set the 'minimumTlsVersion' property to 'TLS1_2' for all 'Microsoft.Storage/storageAccounts' resources.,N/A,AI,Generic
MEDIUM,NS-1,autoscale-settings.bicep,1,"No references to Network Security Groups (NSGs) or Azure Firewall are present to protect the resources (such as the storage account used in autoscale rules). Without NSG or Firewall configuration, resources like storage accounts or app service plans may be overly exposed.",Implement Network Security Groups or Azure Firewall rules to protect the underlying compute and storage resources. Restrict access to the storage account and any associated compute resource to only those networks that explicitly require it.,N/A,AI,Generic
MEDIUM,NS-2,autoscale-settings.bicep,42,"The autoscale rules reference a storage account queue endpoint (via func_storage_account_id), but there is no indication that public endpoints for this storage account are protected or restricted.","Restrict public network access to the referenced storage account by enabling 'Allow trusted Microsoft services' only, using service endpoints, private endpoints, or explicitly blocking public network access.",N/A,AI,Generic
MEDIUM,NS-6,event-grid.bicep,1,"No service endpoints are configured for the underlying Storage Accounts or Service Bus-like resources receiving the Event Grid events, increasing the attack surface.",Enable Virtual Network Service Endpoints for reliant storage resources to restrict traffic to only permitted subnets within your Azure Virtual Network.,N/A,AI,Generic
MEDIUM,DP-1,event-grid.bicep,1,The template does not specify settings to enable encryption at rest for Storage Accounts or queues storing sensitive data connected to these Event Grid topics.,Ensure that the target Storage Accounts and Storage Queues have encryption at rest enabled by default; specify encryption parameters or require secure configurations at deployment.,N/A,AI,Generic
MEDIUM,DP-2,event-grid.bicep,1,"No evidence that TLS 1.2 or above is enforced for data in transit between Event Grid, Storage Accounts, and Storage Queues. Lower versions can expose data to interception.",Configure all Storage Accounts and queue endpoints to enforce TLS 1.2 or higher for all API and data connections.,N/A,AI,Generic
MEDIUM,IM-8,function-settings.bicep,70,"The template references use of a user-assigned managed identity for EasyAuth, but does not show that the Function App itself is enabled for system or user-assigned managed identity. Lack of managed identities can result in insecure credential management and poor resource-to-resource authentication practices.","Enable and configure managed identity for the Azure Function App to securely authenticate to dependent resources such as Key Vault or Storage Account, following the principle of least privilege.",N/A,AI,Generic
MEDIUM,DP-2,function.bicep,67,"The App Service site config does not specify the minimum TLS version for HTTPS endpoints. The default may allow TLS 1.0/1.1, which is not recommended per ASB DP-2.",Explicitly set 'minTlsVersion' to '1.2' or higher in the function app siteConfig to ensure all HTTPS traffic uses a secure protocol.,N/A,AI,Generic
MEDIUM,NS-1,instance-config.bicep,23,"The 'network_config' variable statically defines an address space and subnet, but there is no configuration or resource deployment in this file for Network Security Groups (NSGs) or Azure Firewalls to protect associated compute resources. This omits the baseline network protection recommended by ASB.","Define and associate Network Security Groups (NSGs) and/or Azure Firewall resources to segments referenced in 'network_config', specifying appropriate inbound and outbound rules to protect compute instances according to the principle of least privilege.",N/A,AI,Generic
MEDIUM,NS-3,instance-config.bicep,23,No Network Security Groups (NSGs) are defined or referenced for the specified 'address_space' and 'subnet'; this fails to implement granular traffic control around compute resources.,Implement NSGs to control traffic to and from the subnet defined in 'network_config'. Specify restrictive rules based on actual application requirements.,N/A,AI,Generic
MEDIUM,DP-4,instance-config.bicep,15,The template does not define or reference any policy or resource configuration to ensure that managed disks attached to compute resources are encrypted.,Explicitly enable encryption for all managed disks in compute resource definitions or apply an Azure Policy to enforce disk encryption for all VMs.,N/A,AI,Generic
MEDIUM,DP-1,instance-config.bicep,15,"No configuration for encryption at rest is specified for compute resource attached storage or disks, risking non-compliance with encryption at rest requirements.",Ensure all managed disks or attached data/storage are encrypted at rest by specifying encryption settings in VM and disk resources or applying an organizational policy for enforcement.,N/A,AI,Generic
MEDIUM,NS-1,ip-rules.bicep,1,"The template only exports allow rules and does not show any default-deny or network security grouping mechanism (e.g., NSG or Azure Firewall) applied to resources. Without explicit use of NSG or firewall, resources may remain unprotected.",Ensure that these rules are applied as part of or in conjunction with a Network Security Group or Azure Firewall restricting traffic as per the principle of least privilege. Add resources to the template or reference NSGs explicitly to enforce these rules.,N/A,AI,Generic
MEDIUM,NS-5,ip-rules.bicep,1,"There is no indication that private endpoints are being used for Azure resources (e.g., storage, LogicApps) to provide secure access. Relying only on IP allow lists may still expose resources publicly.",Implement Azure Private Endpoints for every supported resource to limit access to only traffic over the private virtual network interface and avoid inbound exposure via public IP addresses.,N/A,AI,Generic
MEDIUM,DP-6,keyvault.bicep,16,"The Key Vault does not enable customer-managed keys (CMK) for additional encryption control (e.g., through 'properties.encryption' block). The default uses Microsoft-managed keys, which may not meet advanced compliance requirements.","If compliance requires, configure the Key Vault to use customer-managed keys by specifying an 'encryption' property referencing an Azure Key Vault key for key encryption. Store the CMK in a separate, secured Key Vault.",N/A,AI,Generic
MEDIUM,DP-3,operational-insights.bicep,70,"The output 'logAnalyticsWorkspaceId' exposes the Log Analytics workspace customer ID. Though not a secret, overexposing identifiers can aid attackers in targeting services.",Only output values that are operationally necessary. Avoid exposing workspace identifiers unless required for integration. Ensure access to deployment outputs is restricted.,N/A,AI,Generic
MEDIUM,NS-2,scaleset-networks.bicep,9,"The deployment creates a Standard public IP for use with a NAT Gateway, exposing outbound traffic with a static public endpoint. There are no controls shown (such as NSG rules or Azure Firewall) to restrict or monitor this exposure.","Ensure proper egress control by associating NSGs with the subnet and restricting outbound rules as narrowly as possible. Optionally, monitor egress traffic with Azure Firewall or Network Watcher.",N/A,AI,Generic
MEDIUM,IM-6,server-farms.bicep,1,"There is no evidence of using Role-Based Access Control (RBAC) on the Key Vault or App Service resources, risking excessive or unnecessary privileges.",Assign carefully scoped RBAC roles to users and/or service principals only as necessary for operating with Key Vault and App Service. Regularly audit and review access assignments.,N/A,AI,Generic
MEDIUM,NS-1,signalR.bicep,4,"There is no implementation of Network Security Groups (NSGs) or Azure Firewall to restrict access to the SignalR Service or control inbound/outbound traffic flows, which is recommended even for PaaS services with public endpoints.",Configure NSGs or Azure Firewall rules at the subnet or virtual network level for downstream services and use IP ACLs or private endpoints to further restrict SignalR Service access as per the principle of least privilege.,N/A,AI,Generic
MEDIUM,NS-3,storage-accounts.bicep,24,"There is no reference to NSGs protecting the subnets associated with the storage account network rules. Without NSGs, there's no granular traffic filtering to/from the resources.",Deploy and associate network security groups (NSGs) with the subnets referenced in 'hubSubnetId' to enforce granular traffic control in and out of the storage accounts.,N/A,AI,Generic
LOW,AM-1,autoscale-settings.bicep,1,"The template does not specify access controls or role assignments related to who or what can modify or access the autoscale settings resource. This leaves least-privilege assignments up to post-deployment operations, risking overly broad access.",Explicitly define role assignments in your deployment or in linked deployments to ensure the autoscale settings and linked resources (such as storage accounts) are only modifiable by least-privileged service principals or users.,N/A,AI,Generic
LOW,DP-3,autoscale-settings.bicep,6,"The template takes several parameters (like owner, workspaceId, autoscale_name, etc.), and there is no indication that secrets or sensitive parameters (such as potential connection strings or keys) are being referenced via secure parameters or Azure Key Vault. If a sensitive value were injected, it risks being managed insecurely.","If any supplied parameter values represent secrets or keys, switch to Azure Key Vault references and ensure secret values are not passed inline to templates. Label parameters appropriately as secure (using Bicep's 'secure' modifier), and avoid direct inline secrets.",N/A,AI,Generic
LOW,DP-3,event-grid.bicep,1,"No evidence of sensitive parameters (such as secrets or connection strings) being included directly in the template; however, if future extensions include such parameters, they should use secure references (e.g., Azure Key Vault references).","Ensure any sensitive information (secrets, connection strings) are referenced securely via Azure Key Vault instead of hardcoding in the template.",N/A,AI,Generic
LOW,IM-2,instance-config.bicep,5,"There are no explicit configurations or parameters enforcing Multi-Factor Authentication (MFA) for administrators or users, increasing the risk of credential compromise.",Enforce MFA for all privileged accounts accessing the compute resources by integrating with Azure AD Conditional Access policies and documenting MFA in operational processes.,N/A,AI,Generic
LOW,IM-3,instance-config.bicep,5,"Conditional access policies are not referenced or surfaced in the configuration, leaving access control enforcement to external governance or policy.","Document and enforce the use of Azure AD Conditional Access policies for all compute resources, particularly for administrator access and sensitive operations.",N/A,AI,Generic
LOW,NS-7,instance-config.bicep,1,"There is no indication of support or enablement for Just-In-Time (JIT) VM Access, which would reduce the attack surface on management ports.",Implement JIT access for VMs at the resource or NSG level to ensure management ports are only accessible when needed for specific time windows.,N/A,AI,Generic
LOW,NS-10,instance-config.bicep,1,"The template does not reference or suggest the use of Azure Bastion for secure VM management, implying possible exposure of SSH/RDP ports.",Deploy Azure Bastion and restrict direct SSH/RDP access to VMs; document usage procedures in deployment configuration.,N/A,AI,Generic
LOW,NS-6,ip-rules.bicep,1,"No use of Virtual Network Service Endpoints is indicated. Without service endpoints, traffic between services and Azure resources may traverse the public internet, increasing risk.",Use Azure Virtual Network Service Endpoints to confine service-to-service communications within the Azure backbone network.,N/A,AI,Generic
LOW,NS-9,ip-rules.bicep,1,"There are no logging or monitoring configurations (e.g., Azure Monitor, Network Watcher) in the template. Absence of network monitoring can hinder the detection of suspicious activity or unauthorized access.",Add resources or configuration settings to enable diagnostic logs and monitoring (such as Flow Logs) on all relevant network resources per NS-9.,N/A,AI,Generic
LOW,DP-3,ip-rules.bicep,29,"The ingestionServiceIps array has a TODO placeholder and may be populated later. If this is left as-is, it may lead to insecure defaults or accidental overexposure if not properly reviewed.","Ensure that before deployment, all TODOs or empty rule sets are properly reviewed and populated with secure, validated entries. Remove placeholder comments and enforce code review checkpoints for such areas.",N/A,AI,Generic
LOW,DP-2,keyvault.bicep,0,"The template does not explicitly configure minimum TLS version for the Key Vault, potentially allowing older insecure TLS protocols and exposing data in transit to downgrade or interception attacks.",Add 'properties.enabledForTemplateDeployment: true' and set 'properties.minimumTlsVersion' to 'TLS1_2' or later within the Key Vault resource to enforce secure communications.,N/A,AI,Generic
LOW,NS-5,scaleset-networks.bicep,41,Private endpoints are not configured for securely accessing PaaS resources within the virtual network or subnet. Direct subnet access without private endpoints may expose sensitive resources.,"Use Azure Private Endpoints to connect critical resources to the subnet privately, minimizing public exposure.",N/A,AI,Generic
LOW,NS-6,scaleset-networks.bicep,41,"No service endpoints are enabled in the subnet configuration ('serviceEndpoints': []), reducing the security of communication with Azure services.",Add service endpoints for required Azure services to the 'serviceEndpoints' array in the subnet properties to securely route traffic and restrict access at the service level.,N/A,AI,Generic
LOW,NS-8,scaleset-networks.bicep,1,"DDoS protection is not enabled for the virtual network, leaving resources susceptible to denial-of-service attacks.","Enable Azure DDoS Protection Standard on the virtual network to mitigate volumetric, protocol, and resource-level DDoS attacks.",N/A,AI,Generic
LOW,DP-3,server-farms.bicep,81,"The resource 'serverFarms_AntMDS_CERTIFICATE_PASSWORD_GENEVACERT' sets the 'CERTIFICATE_PASSWORD_GENEVACERT' value to an empty string, which suggests it may act as a placeholder for a sensitive secret but does not securely provision it. This can lead to mismanagement of secrets.","If a certificate password is needed, store it securely in Azure Key Vault and set this value by referencing the Key Vault secret. Do not leave placeholders for secrets; handle all secrets with managed identities and Key Vault references.",N/A,AI,Generic
