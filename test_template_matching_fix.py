#!/usr/bin/env python3
"""
Test script to verify template-parameter matching is working correctly
"""

import os
import sys
import json
import tempfile
import shutil
from pathlib import Path

# Add src/core to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'core'))

def test_template_matching():
    """Test template-parameter matching with the exact files from your log"""
    
    print("🧪 Testing Template-Parameter Matching Fix")
    print("=" * 70)
    
    # Create temporary test directory
    test_dir = Path(tempfile.mkdtemp())
    print(f"📁 Test directory: {test_dir}")
    
    try:
        # Create test files that match your actual files
        test_files = {
            "Grafana.deploymentTemplate.json": {
                "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
                "contentVersion": "*******",
                "parameters": {
                    "grafanaName": {
                        "type": "string",
                        "metadata": {"description": "Name of the Grafana instance"}
                    }
                },
                "resources": []
            },
            "Grafana.deploymentParameters.json": {
                "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#",
                "contentVersion": "*******",
                "parameters": {
                    "grafanaName": {
                        "value": "test-grafana"
                    }
                }
            },
            "KustoScripts.template.json": {
                "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
                "contentVersion": "*******",
                "parameters": {
                    "clusterName": {
                        "type": "string",
                        "metadata": {"description": "Name of the Kusto cluster"}
                    }
                },
                "resources": []
            },
            "KustoScripts.parameters.json": {
                "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#",
                "contentVersion": "*******",
                "parameters": {
                    "clusterName": {
                        "value": "test-kusto"
                    }
                }
            },
            "roleAssignment.deploymentTemplate.json": {
                "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
                "contentVersion": "*******",
                "parameters": {
                    "principalId": {
                        "type": "string",
                        "metadata": {"description": "Principal ID for role assignment"}
                    }
                },
                "resources": []
            },
            "roleAssignment.deploymentParameters.json": {
                "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#",
                "contentVersion": "*******",
                "parameters": {
                    "principalId": {
                        "value": "test-principal-id"
                    }
                }
            }
        }
        
        # Write test files
        for filename, content in test_files.items():
            file_path = test_dir / filename
            with open(file_path, 'w') as f:
                json.dump(content, f, indent=2)
            print(f"✅ Created: {filename}")
        
        print(f"\n📊 Test Files Created: {len(test_files)}")
        
        # Test the matching logic
        from template_parameter_expander import TemplateParameterExpander
        
        expander = TemplateParameterExpander()
        
        # Test each template file
        template_files = [f for f in test_files.keys() if 'Template' in f or 'template' in f]
        
        print(f"\n🔍 Testing Template-Parameter Matching:")
        print("-" * 50)
        
        successful_matches = 0
        total_templates = len(template_files)
        
        for template_file in template_files:
            template_path = str(test_dir / template_file)
            print(f"\n📄 Template: {template_file}")
            
            # Find matching parameter files
            param_files = expander._find_matching_parameter_files(template_path)
            
            if param_files:
                print(f"   ✅ Found {len(param_files)} parameter file(s):")
                for param_file in param_files:
                    param_name = os.path.basename(param_file)
                    print(f"      - {param_name}")
                successful_matches += 1
            else:
                print(f"   ❌ No parameter files found")
        
        print(f"\n📈 Matching Results:")
        print(f"   • Total templates: {total_templates}")
        print(f"   • Successful matches: {successful_matches}")
        print(f"   • Success rate: {(successful_matches/total_templates)*100:.1f}%")
        
        if successful_matches == total_templates:
            print(f"\n🎉 SUCCESS: All templates matched with their parameter files!")
            print(f"✅ The template-parameter matching is now working correctly.")
        else:
            print(f"\n⚠️  PARTIAL SUCCESS: {total_templates - successful_matches} templates still have no matches")
            
            # Test the pattern configuration
            print(f"\n🔧 Debugging Pattern Configuration:")
            patterns = expander._get_pattern_config()
            print(f"   Template patterns: {patterns['template_patterns']}")
            print(f"   Parameter patterns: {patterns['param_patterns']}")
            print(f"   Template identifiers: {patterns['template_identifiers']}")
            print(f"   Parameter identifiers: {patterns['param_identifiers']}")
        
        # Test environment configuration
        print(f"\n⚙️  Environment Configuration:")
        print(f"   MATCHING_STRATEGY: {os.environ.get('MATCHING_STRATEGY', 'not set')}")
        print(f"   ENABLE_PARAMETER_EXPANSION: {os.environ.get('ENABLE_PARAMETER_EXPANSION', 'not set')}")
        
    finally:
        # Clean up
        shutil.rmtree(test_dir)
        print(f"\n🧹 Cleaned up test directory")

if __name__ == "__main__":
    test_template_matching()
