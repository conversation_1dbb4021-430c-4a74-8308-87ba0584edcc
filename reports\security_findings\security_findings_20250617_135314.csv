File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
app-config.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyValues' flows across template boundary,Secure parameter 'keyValues' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'app_insights_key' flows across template boundary,Secure parameter 'app_insights_key' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyvault_name' flows across template boundary,Secure parameter 'keyvault_name' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
keyvault.bicep,NS-1,Network Security,Protect resources using network security groups,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,Review template dependencies and ensure secure communication between templates,,,,cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'secrets' flows across template boundary,Secure parameter 'secrets' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
storage-accounts.bicep,NS-1,Network Security,Protect resources using network security groups,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,Review template dependencies and ensure secure communication between templates,,,,cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,DP-6,Data Protection,Secure Data with Customer-Managed Keys (CMK),HIGH,1.0,Parameter flow security risk: Sensitive parameters may be exposed through template dependencies,Use Key Vault references for sensitive parameters and validate parameter flow security,,,,cross_reference_analysis,parameter_flow,Validated
app-config.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,8.0,"The App Configuration resource 'Microsoft.AppConfiguration/configurationStores' does not restrict public network access. By default, endpoints are public unless 'publicNetworkAccess' is explicitly set to 'Disabled' or network ACLs are configured.",Add the 'publicNetworkAccess' property set to 'Disabled' or configure 'networkAcls' to restrict access to required IPs only in the App Configuration resource definition.,,,,ai_analysis,,Validated
autoscale-settings.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,2.0,"The template uses a parameter 'server_farm_id' to reference the target resource for autoscale, but there is no evidence of Azure Active Directory (Azure AD) integration or managed identity configuration for identity management. This violates ASB IM-1, which requires Azure AD for all Azure resources.",Configure managed identities for the resources referenced by 'server_farm_id' and ensure Azure AD is used for identity management. Update the template to assign a managed identity and use it for resource access.,,,,ai_analysis,,Validated
autoscale-settings.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,3.0,"The template uses a parameter 'func_storage_account_id' to reference a storage account for metric triggers, but does not configure managed identities or Azure AD integration for secure access. This violates ASB IM-1.",Assign a managed identity to the autoscale resource and configure the storage account to allow access via Azure AD. Update the template to use managed identities for authentication.,,,,ai_analysis,,Validated
autoscale-settings.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,41.0,The 'Microsoft.Insights/diagnosticSettings' resource is created without any reference to managed identities or Azure AD integration for secure access to the Log Analytics workspace. This violates ASB IM-1.,Configure the diagnostic settings resource to use a managed identity for access to the Log Analytics workspace. Update the template to assign and reference a managed identity.,,,,ai_analysis,,Validated
function-settings.bicep,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,36.0,"The parameter 'app_insights_key' is marked as @secure(), but is directly assigned to the 'APPINSIGHTS_INSTRUMENTATIONKEY' application setting in line 36. Storing secrets such as instrumentation keys in application settings can lead to sensitive information disclosure if not referenced securely via Azure Key Vault.","Use a Key Vault reference for 'APPINSIGHTS_INSTRUMENTATIONKEY' instead of passing the key directly. Update the application setting to reference the Key Vault secret, e.g., '@Microsoft.KeyVault(SecretUri=<keyvault-secret-uri>)'.",,,,ai_analysis,,Validated
function.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,54.0,"The App Service resource 'function' (Microsoft.Web/sites@2021-03-01) does not restrict public access. No access restrictions or IP allowlist are configured, exposing the function app to the public internet.","Configure access restrictions for the App Service by adding an 'ipSecurityRestrictions' block in the 'siteConfig' property to allow only required IP addresses or subnets. Alternatively, use Azure Front Door or Application Gateway to control public access.",,,,ai_analysis,,Validated
function.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,38.0,"The Storage Account resource 'funcStorage' (Microsoft.Storage/storageAccounts@2021-08-01) is referenced for logs, but there is no configuration for a Private Endpoint, leaving the storage account accessible over public endpoints.",Configure a Private Endpoint for the storage account by adding a 'Microsoft.Network/privateEndpoints' resource targeting the storage account. Update network rules to deny public access and allow only traffic from the private endpoint.,,,,ai_analysis,,Validated
hub-network.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,8.0,The subnet 'hub-subnet' defined at line 8 does not have a Network Security Group (NSG) associated. NSGs are required to segment and protect network resources.,Associate a Network Security Group (NSG) with the 'hub-subnet' by adding the 'networkSecurityGroup' property referencing an NSG resource.,,,,ai_analysis,,Validated
hub-network.bicep,NS-3,Network Security,Use Network Security Groups (NSGs),CRITICAL,8.0,No Network Security Group (NSG) is configured for the subnet 'hub-subnet' at line 8. NSGs are required to provide network-level access control and deny all inbound traffic by default.,Create an NSG resource and associate it with the 'hub-subnet' to restrict inbound and outbound traffic according to least privilege.,,,,ai_analysis,,Validated
hub-network.bicep,NS-4,Network Security,Use Azure Firewall or third-party firewall,HIGH,2.0,No Azure Firewall or third-party firewall is configured in the template. Advanced network protection is essential for monitoring and controlling traffic.,Deploy an Azure Firewall or a supported third-party firewall in the virtual network and configure appropriate firewall policies.,,,,ai_analysis,,Validated
hub-network.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,8.0,"Private Endpoints are not configured for services such as Storage or Key Vault, even though service endpoints are present at line 15 and 18. Private Endpoints provide enhanced security by enabling private connectivity.",Configure Private Endpoints for Microsoft.Storage and Microsoft.KeyVault services to ensure traffic remains on the Microsoft backbone network.,,,,ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,19.0,"The IP rule '*******/8' in corpNetIps (line 4) is an extremely broad public IP range, which violates NS-2 by not restricting public access to only required IPs.","Restrict allowed IP ranges to only those necessary for business operations. Remove or narrow '*******/8' to specific, trusted IP addresses as required by NS-2.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,20.0,"The IP rule '********/8' in corpNetIps (line 5) is an overly broad public IP range, violating NS-2 by not limiting public access to required IPs.","Replace '********/8' with specific, trusted IP addresses or smaller CIDR blocks that are strictly necessary for access.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,21.0,"The IP rule '20.0.0.0/8' in corpNetIps (line 6) is an overly broad public IP range, violating NS-2 by not limiting public access to required IPs.","Replace '20.0.0.0/8' with specific, trusted IP addresses or smaller CIDR blocks that are strictly necessary for access.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,22.0,"The IP rule '40.0.0.0/8' in corpNetIps (line 7) is an overly broad public IP range, violating NS-2 by not limiting public access to required IPs.","Replace '40.0.0.0/8' with specific, trusted IP addresses or smaller CIDR blocks that are strictly necessary for access.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,23.0,"The IP rule '********/8' in corpNetIps (line 8) is an overly broad public IP range, violating NS-2 by not limiting public access to required IPs.","Replace '********/8' with specific, trusted IP addresses or smaller CIDR blocks that are strictly necessary for access.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,24.0,"The IP rule '********/8' in corpNetIps (line 9) is an overly broad public IP range, violating NS-2 by not limiting public access to required IPs.","Replace '********/8' with specific, trusted IP addresses or smaller CIDR blocks that are strictly necessary for access.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,25.0,"The IP rule '********/8' in corpNetIps (line 10) is an overly broad public IP range, violating NS-2 by not limiting public access to required IPs.","Replace '********/8' with specific, trusted IP addresses or smaller CIDR blocks that are strictly necessary for access.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,26.0,"The IP rule '70.0.0.0/8' in corpNetIps (line 11) is an overly broad public IP range, violating NS-2 by not limiting public access to required IPs.","Replace '70.0.0.0/8' with specific, trusted IP addresses or smaller CIDR blocks that are strictly necessary for access.",,,,ai_analysis,,Validated
keyvault.bicep,DP-6,Data Protection,Secure Data with Customer-Managed Keys (CMK),HIGH,17.0,"Key Vault 'networkAcls.defaultAction' is set to 'Allow', which permits public network access. This does not enforce secure access to keys as required by DP-6.",Set 'networkAcls.defaultAction' to 'Deny' to restrict public network access. Only allow trusted networks or IPs via 'ipRules' and 'virtualNetworkRules'.,,,,ai_analysis,,Validated
operational-insights.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,"The template does not configure Azure Active Directory (Azure AD) integration or managed identities for any resources. According to ASB IM-1, all Azure resources should use Azure AD for identity management.","Configure managed identities for all supported resources (e.g., add 'identity' property to resources such as Microsoft.OperationalInsights/workspaces and Microsoft.Insights/components) and ensure Azure AD is used for authentication and access control.",,,,ai_analysis,,Validated
scaleset-networks.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,38.0,The subnet 'scaleset' defined at line 38 does not have a Network Security Group (NSG) associated. NSGs are required to segment and protect network resources.,Associate a Network Security Group (NSG) with the 'scaleset' subnet to restrict and control network traffic. Define an NSG resource and reference its ID in the subnet's 'networkSecurityGroup' property.,,,,ai_analysis,,Validated
scaleset-networks.bicep,NS-3,Network Security,Use Network Security Groups (NSGs),CRITICAL,38.0,The subnet 'scaleset' at line 38 lacks an explicit NSG configuration. NSGs should be used to deny all inbound traffic by default and only allow necessary traffic.,Create and associate a Network Security Group (NSG) with the 'scaleset' subnet. Configure the NSG to deny all inbound traffic by default and allow only required traffic.,,,,ai_analysis,,Validated
server-farms.bicep,DP-2,Data Protection,Enable encryption in transit,CRITICAL,74.0,"App Service resource 'serverFarms' at line 74 does not explicitly enforce HTTPS-only or minimum TLS version, violating DP-2 (Enable encryption in transit).","Add 'properties: { httpsOnly: true, minimumTlsVersion: ""1.2"" }' to the App Service resource definition to enforce HTTPS and set a secure TLS version.",,,,ai_analysis,,Validated
server-farms.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,74.0,"App Service resource 'serverFarms' at line 74 does not configure managed identities, violating IM-1 (Use Azure Active Directory for Identity Management).","Add 'identity: { type: ""SystemAssigned"" }' to the App Service resource to enable managed identity and integrate with Azure AD.",,,,ai_analysis,,Validated
server-farms.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,74.0,"App Service resource 'serverFarms' at line 74 does not reference any network security group (NSG) or private endpoint, violating NS-1 (Protect resources using network security groups).",Integrate the App Service with a virtual network and apply a network security group (NSG) to restrict access to only required ports and protocols.,,,,ai_analysis,,Validated
server-farms.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,74.0,"App Service resource 'serverFarms' at line 74 does not restrict public access or define access controls for public endpoints, violating NS-2 (Protect public endpoints).",Restrict public access to the App Service by integrating with a private endpoint or configuring access restrictions to allow only required IP addresses.,,,,ai_analysis,,Validated
signalR.bicep,DP-2,Data Protection,Enable encryption in transit,CRITICAL,5.0,"The Microsoft.SignalRService/signalR resource does not explicitly enforce encryption in transit (e.g., minimum TLS version or HTTPS-only).","Add the 'cors' and 'tls' properties to the SignalR resource to enforce HTTPS-only connections and set a minimum TLS version (e.g., 'tls: { minTlsVersion: ""1.2"" }').",,,,ai_analysis,,Validated
signalR.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,5.0,The Microsoft.SignalRService/signalR resource does not configure Azure Active Directory (Azure AD) for identity management.,Configure the 'aad' property in the SignalR resource to enable Azure Active Directory authentication for the service.,,,,ai_analysis,,Validated
signalR.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,5.0,"The Microsoft.SignalRService/signalR resource does not restrict public network access or define allowed IPs, potentially exposing a public endpoint.",Add the 'networkAcl' property to the SignalR resource to restrict public access and specify allowed IPs or subnets.,,,,ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,20.0,"storageAccountFunc resource: 'networkAcls.defaultAction' is set to 'Allow', which permits public network access to the storage account. This violates NS-2, which requires strict access control for public endpoints.",Set 'networkAcls.defaultAction' to 'Deny' in the storageAccountFunc resource to restrict public access. Allow only required IPs or virtual networks.,,,,ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,54.0,"fuzzStorageProperties: 'networkAcls.defaultAction' is set to 'Allow', which permits public network access to the storage account. This violates NS-2, which requires strict access control for public endpoints.",Set 'networkAcls.defaultAction' to 'Deny' in fuzzStorageProperties to restrict public access. Allow only required IPs or virtual networks.,,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 39,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 7,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-17T13:53:14.502398,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
