#!/usr/bin/env python3
"""
Test script to validate that reports display findings in the correct domain priority order.
This addresses the issue where reports still show different ordering despite optimization.
"""

import os
import json
import tempfile
from pathlib import Path
from security_opt import SecurityPRReviewer
import logging
import re

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_template_with_multiple_issues():
    """Create a comprehensive test template with issues across all domains."""
    template_content = {
        "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
        "contentVersion": "*******",
        "resources": [
            {
                "type": "Microsoft.Storage/storageAccounts",
                "apiVersion": "2021-04-01",
                "name": "teststorageaccount",
                "location": "[resourceGroup().location]",
                "sku": {"name": "Standard_LRS"},
                "kind": "StorageV2",
                "properties": {
                    "supportsHttpsTrafficOnly": False,  # Data Protection issue
                    "minimumTlsVersion": "TLS1_0",      # Data Protection issue
                    "networkAcls": {
                        "defaultAction": "Allow"         # Network Security issue
                    },
                    "publicNetworkAccess": "Enabled"    # Network Security issue
                }
            },
            {
                "type": "Microsoft.Network/networkSecurityGroups",
                "apiVersion": "2021-02-01",
                "name": "testNSG",
                "location": "[resourceGroup().location]",
                "properties": {
                    "securityRules": [
                        {
                            "name": "AllowAllInbound",
                            "properties": {
                                "protocol": "*",
                                "sourcePortRange": "*",
                                "destinationPortRange": "*",
                                "sourceAddressPrefix": "*",    # Network Security issue
                                "destinationAddressPrefix": "*",
                                "access": "Allow",
                                "priority": 100,
                                "direction": "Inbound"
                            }
                        }
                    ]
                }
            },
            {
                "type": "Microsoft.Authorization/roleAssignments",
                "apiVersion": "2020-04-01-preview",
                "name": "[guid(resourceGroup().id)]",
                "properties": {
                    "roleDefinitionId": "[concat('/subscriptions/', subscription().subscriptionId, '/providers/Microsoft.Authorization/roleDefinitions/', 'b24988ac-6180-42a0-ab88-20f7382dd24c')]",
                    "principalId": "00000000-0000-0000-0000-000000000000",  # Identity Management issue
                    "principalType": "User"
                }
            }
        ]
    }
    return json.dumps(template_content, indent=2)

def test_report_ordering():
    """Test that reports display findings in correct domain priority order."""
    logger.info("🧪 Testing report ordering with domain prioritization")
    
    # Enable domain prioritization
    os.environ['ENFORCE_DOMAIN_PRIORITY'] = 'true'
    os.environ['USE_OPTIMIZED_PROMPTS'] = 'true'
    os.environ['ANALYSIS_SEED'] = '42'
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test template
        test_file_path = Path(temp_dir) / "test_template.json"
        test_file_path.write_text(create_test_template_with_multiple_issues())
        
        try:
            reviewer = SecurityPRReviewer(local_folder=temp_dir)
            
            # Analyze the template
            files_to_analyze = reviewer.analyze_folder(temp_dir)
            findings = reviewer.analyze_files(files_to_analyze)
            
            if not findings:
                logger.warning("⚠️  No findings generated for ordering test")
                return False
            
            logger.info(f"📊 Generated {len(findings)} findings for ordering test")
            
            # Test the sorting function directly
            sorted_findings = reviewer._sort_findings_by_priority(findings)
            
            # Analyze the ordering
            domain_order = []
            current_domain = None
            
            for finding in sorted_findings:
                control_id = finding.get("control_id", "")
                
                # Get domain from control
                domain = "Unknown"
                if reviewer.benchmark_data and reviewer.benchmark_data.get("controls"):
                    for control in reviewer.benchmark_data["controls"]:
                        if control.get("id") == control_id:
                            domain = control.get("domain", "Unknown")
                            break
                
                if domain != current_domain:
                    current_domain = domain
                    domain_order.append(domain)
            
            logger.info("📋 Domain order in sorted findings:")
            for i, domain in enumerate(domain_order, 1):
                count = sum(1 for f in sorted_findings 
                           if reviewer._get_finding_domain(f) == domain)
                logger.info(f"   {i}. {domain}: {count} findings")
            
            # Check if Identity Management comes first
            expected_order = ["Identity Management", "Network Security", "Data Protection"]
            actual_order = [d for d in domain_order if d in expected_order]
            
            if actual_order == expected_order[:len(actual_order)]:
                logger.info("✅ Domain ordering is correct!")
                return True
            else:
                logger.warning(f"⚠️  Domain ordering issue:")
                logger.warning(f"   Expected: {expected_order}")
                logger.warning(f"   Actual: {actual_order}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error testing report ordering: {str(e)}")
            return False

def test_html_report_structure():
    """Test that HTML reports maintain domain prioritization."""
    logger.info("🧪 Testing HTML report structure")
    
    os.environ['ENFORCE_DOMAIN_PRIORITY'] = 'true'
    
    with tempfile.TemporaryDirectory() as temp_dir:
        test_file_path = Path(temp_dir) / "test_template.json"
        test_file_path.write_text(create_test_template_with_multiple_issues())
        
        try:
            reviewer = SecurityPRReviewer(local_folder=temp_dir)
            
            # Generate findings
            files_to_analyze = reviewer.analyze_folder(temp_dir)
            findings = reviewer.analyze_files(files_to_analyze)
            
            if not findings:
                logger.warning("⚠️  No findings for HTML test")
                return False
            
            # Export to HTML
            html_path = Path(temp_dir) / "test_report.html"
            reviewer._export_findings_to_html(findings, str(html_path))
            
            if html_path.exists():
                # Read and analyze HTML content
                html_content = html_path.read_text(encoding='utf-8')
                
                # Check for domain sections
                domain_sections = re.findall(r'<h3 class="domain-header"[^>]*>([^<]+)</h3>', html_content)
                
                if domain_sections:
                    logger.info("✅ HTML report contains domain sections:")
                    for i, section in enumerate(domain_sections, 1):
                        clean_section = re.sub(r'<[^>]+>', '', section).strip()
                        logger.info(f"   {i}. {clean_section}")
                    return True
                else:
                    # Check for severity groups (fallback structure)
                    severity_groups = re.findall(r'data-severity="([^"]+)"', html_content)
                    if severity_groups:
                        logger.info("✅ HTML report contains severity groups (fallback structure)")
                        return True
                    else:
                        logger.warning("⚠️  HTML report structure unclear")
                        return False
            else:
                logger.error("❌ HTML report file not created")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error testing HTML report: {str(e)}")
            return False

def test_csv_report_ordering():
    """Test that CSV reports maintain domain prioritization."""
    logger.info("🧪 Testing CSV report ordering")
    
    os.environ['ENFORCE_DOMAIN_PRIORITY'] = 'true'
    
    with tempfile.TemporaryDirectory() as temp_dir:
        test_file_path = Path(temp_dir) / "test_template.json"
        test_file_path.write_text(create_test_template_with_multiple_issues())
        
        try:
            reviewer = SecurityPRReviewer(local_folder=temp_dir)
            
            # Generate findings
            files_to_analyze = reviewer.analyze_folder(temp_dir)
            findings = reviewer.analyze_files(files_to_analyze)
            
            if not findings:
                logger.warning("⚠️  No findings for CSV test")
                return False
            
            # Export to CSV
            csv_path = Path(temp_dir) / "test_report.csv"
            reviewer._export_findings_to_csv(findings, str(csv_path))
            
            if csv_path.exists():
                # Read and analyze CSV content
                import csv
                with open(csv_path, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    rows = list(reader)
                
                if rows:
                    # Check if Priority column exists and shows domain order
                    if 'Priority' in rows[0]:
                        priorities = [row['Priority'] for row in rows]
                        logger.info("✅ CSV report contains Priority column:")
                        for i, priority in enumerate(priorities[:5], 1):
                            logger.info(f"   {i}. {priority}")
                        return True
                    else:
                        # Check domain column
                        if 'Domain' in rows[0]:
                            domains = [row['Domain'] for row in rows]
                            logger.info("✅ CSV report contains Domain column:")
                            unique_domains = []
                            for domain in domains:
                                if domain not in unique_domains:
                                    unique_domains.append(domain)
                            for i, domain in enumerate(unique_domains, 1):
                                count = domains.count(domain)
                                logger.info(f"   {i}. {domain}: {count} findings")
                            return True
                        else:
                            logger.warning("⚠️  CSV report missing domain/priority columns")
                            return False
                else:
                    logger.warning("⚠️  CSV report is empty")
                    return False
            else:
                logger.error("❌ CSV report file not created")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error testing CSV report: {str(e)}")
            return False

def run_comprehensive_ordering_test():
    """Run comprehensive tests for report ordering."""
    logger.info("🚀 Starting comprehensive report ordering tests")
    logger.info("=" * 80)
    
    tests = [
        ("Finding Sorting Logic", test_report_ordering),
        ("HTML Report Structure", test_html_report_structure),
        ("CSV Report Ordering", test_csv_report_ordering)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running: {test_name}")
        logger.info("-" * 50)
        
        try:
            success = test_func()
            results[test_name] = "PASSED" if success else "FAILED"
            status = "✅" if success else "❌"
            logger.info(f"{status} {test_name}: {'PASSED' if success else 'FAILED'}")
        except Exception as e:
            results[test_name] = f"ERROR: {str(e)}"
            logger.error(f"❌ {test_name}: ERROR - {str(e)}")
    
    # Summary
    logger.info("\n" + "=" * 80)
    logger.info("📊 REPORT ORDERING TEST SUMMARY")
    logger.info("=" * 80)
    
    passed = sum(1 for result in results.values() if result == "PASSED")
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅" if result == "PASSED" else "❌"
        logger.info(f"{status} {test_name}: {result}")
    
    logger.info(f"\n📈 Overall: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        logger.info("🎉 All report ordering tests passed!")
        logger.info("Reports should now display findings in correct domain priority order:")
        logger.info("   1. Identity Management")
        logger.info("   2. Network Security") 
        logger.info("   3. Data Protection")
        logger.info("   4. Access Management")
        logger.info("   5. Logging and Monitoring")
    else:
        logger.warning("⚠️  Some tests failed - report ordering may still have issues")

if __name__ == "__main__":
    try:
        run_comprehensive_ordering_test()
    except KeyboardInterrupt:
        logger.info("\n🛑 Tests interrupted by user")
    except Exception as e:
        logger.error(f"💥 Test suite failed: {str(e)}")
        raise
