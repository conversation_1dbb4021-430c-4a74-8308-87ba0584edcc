ADVANCED SECURITY ANALYSIS REQUEST

=== ENHANCED TEMPLATE METADATA ===
File Path: {file_path}
Resource Types: {resource_types}
Template Format: {template_format}
Environment Context: {environment_context}
Deployment Scenario: {deployment_scenario}
{template_status}
{cross_references}

{graph_context}

=== AI-ENHANCED CONTEXT ANALYSIS FOR FALSE POSITIVE REDUCTION ===
Variables Analyzed: {variables_count}
Secret-like Variables: {secrets_count}
False Positive Indicators: {false_positive_count}
Confidence Score: {analysis_confidence}%
Historical Pattern Matches: {pattern_matches}

SEMANTIC ANALYSIS INSIGHTS:
{context_insights}

CROSS-TEMPLATE DEPENDENCY ANALYSIS:
{dependency_analysis}

CONDITIONAL LOGIC EVALUATION:
{conditional_logic}

ENVIRONMENT-SPECIFIC CONTEXT:
{environment_context_details}

{variable_analysis}

=== TEMPLATE CONTENT TO ANALYZE (WITH LINE NUMBERS) ===
{numbered_content}

=== COMPREHENSIVE SECURITY FRAMEWORK CONTROLS TO APPLY ===
Total Azure Security Benchmark Controls: {total_controls}
Extended Framework Controls: {extended_controls}
Resource Types Covered: {resource_types}
Framework Coverage: Azure Security Benchmark v3.0, MITRE ATT&CK, Container Security, API Security, DevOps Security

{controls_section}

=== EXTENDED SECURITY FRAMEWORK CONTROLS ===

CONTAINER SECURITY (CS-*) CONTROLS:
{container_security_controls}

API SECURITY (AS-*) CONTROLS:
{api_security_controls}

DEVOPS SECURITY (DS-*) CONTROLS:
{devops_security_controls}

MITRE ATT&CK TECHNIQUE MAPPINGS:
{mitre_attack_mappings}

INDUSTRY FRAMEWORK CROSS-REFERENCES:
{industry_framework_mappings}

=== ENHANCED CONTROL ID VALIDATION CONSTRAINTS ===

🔒 VALID CONTROL IDs (ONLY use these exact IDs):
{valid_control_ids}

❌ FICTIONAL CONTROL IDs (DO NOT use these examples):
{fictional_control_ids}

⚠️  CRITICAL: Any control_id not in the VALID list above is FICTIONAL and will be rejected.

{analysis_instructions}
