Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
CRITICAL,DP-3,Parameters\LacpStamp.Parameters-LacpStampResources.json,74,"Parameter 'dasStorageAccountKey' at line 74 references a storage account key value directly in the template. Sensitive information such as storage account keys should be stored in Azure Key Vault, not in template parameters or outputs.",Store the storage account key in Azure Key Vault and reference it securely using a Key Vault reference in the template. Remove direct exposure of sensitive keys from template parameters.,N/A,AI,Generic
CRITICAL,DP-1,Templates\IngestionStorageAccount.Template.json,38,Storage account resource at line 38 does not explicitly enable encryption at rest. The 'encryption' property is missing from the resource definition.,"Add the 'encryption' property to the storage account resource at line 38, specifying 'services', 'keySource', and other required encryption settings to ensure encryption at rest is enabled as per ASB DP-1.",N/A,AI,Generic
CRITICAL,DP-1,Templates\IngestionStorageAccount.Template.json,59,Storage account resource at line 59 does not explicitly enable encryption at rest. The 'encryption' property is missing from the resource definition.,"Add the 'encryption' property to the storage account resource at line 59, specifying 'services', 'keySource', and other required encryption settings to ensure encryption at rest is enabled as per ASB DP-1.",N/A,AI,Generic
CRITICAL,NS-1,Templates\IngestionStorageAccount.Template.json,38,Storage account resource at line 38 does not restrict network access using network security groups (NSGs) or Azure Firewall. No 'networkAcls' property is defined.,"Add the 'networkAcls' property to the storage account resource at line 38 to restrict access to trusted networks only, as required by ASB NS-1.",N/A,AI,Generic
CRITICAL,NS-1,Templates\IngestionStorageAccount.Template.json,59,Storage account resource at line 59 does not restrict network access using network security groups (NSGs) or Azure Firewall. No 'networkAcls' property is defined.,"Add the 'networkAcls' property to the storage account resource at line 59 to restrict access to trusted networks only, as required by ASB NS-1.",N/A,AI,Generic
CRITICAL,NS-2,Templates\IngestionStorageAccount.Template.json,38,"Storage account resource at line 38 does not restrict public network access. The 'networkAcls' property is missing, which may allow public endpoints.",Explicitly set the 'networkAcls' property with 'defaultAction' set to 'Deny' and specify allowed subnets or IPs for the storage account at line 38 to protect public endpoints as per ASB NS-2.,N/A,AI,Generic
CRITICAL,NS-2,Templates\IngestionStorageAccount.Template.json,59,"Storage account resource at line 59 does not restrict public network access. The 'networkAcls' property is missing, which may allow public endpoints.",Explicitly set the 'networkAcls' property with 'defaultAction' set to 'Deny' and specify allowed subnets or IPs for the storage account at line 59 to protect public endpoints as per ASB NS-2.,N/A,AI,Generic
CRITICAL,NS-1,Templates\LacpBilling.Template.json,3932,Microsoft.Storage/storageAccounts resource at line 3932 does not specify network rules or restrict network access. Storage accounts should be protected using network security groups (NSGs) or Azure Firewall to prevent unauthorized access.,Add 'networkAcls' property to the storage account resource to restrict access to selected networks and subnets. Consider integrating with Azure Firewall or NSGs to control inbound and outbound traffic.,N/A,AI,Generic
CRITICAL,NS-2,Templates\LacpBilling.Template.json,3932,"Microsoft.Storage/storageAccounts resource at line 3932 does not restrict public network access. Public endpoints are not explicitly disabled, increasing the risk of exposure.",Set the 'publicNetworkAccess' property to 'Disabled' in the storage account resource to prevent public access. Ensure only private endpoints or selected networks are allowed.,N/A,AI,Generic
CRITICAL,DP-1,Templates\LacpBillingExhaust.Template.json,32,Microsoft.Kusto/clusters resource at line 32 does not explicitly enable encryption at rest. The 'properties' section does not include any encryption settings.,Add 'properties.enableDoubleEncryption': true or specify encryption settings under the 'properties' section for the Microsoft.Kusto/clusters resource to ensure encryption at rest is enabled.,N/A,AI,Generic
CRITICAL,DP-2,Templates\LacpBillingExhaust.Template.json,32,Microsoft.Kusto/clusters resource at line 32 does not explicitly enforce encryption in transit (TLS 1.2+). No configuration is present to require minimum TLS version.,Add 'properties.enginePublicIpAccess': 'Disabled' and specify 'properties.publicNetworkAccess': 'Disabled' or set 'properties.minimumTlsVersion': '1.2' for the Microsoft.Kusto/clusters resource to enforce encryption in transit.,N/A,AI,Generic
CRITICAL,DP-3,Templates\LacpBillingExhaust.Template.json,1,"Template does not use Azure Key Vault for storing sensitive information such as connection strings, secrets, or credentials. All parameters and outputs are handled as plain strings.","Store sensitive data such as connection strings, secrets, or credentials in Azure Key Vault and reference them securely in the template.",N/A,AI,Generic
CRITICAL,NS-1,Templates\LacpBillingExhaust.Template.json,32,Microsoft.Kusto/clusters resource at line 32 does not specify any network security groups (NSGs) or Azure Firewall configuration to protect the resource.,Associate the Microsoft.Kusto/clusters resource with a subnet protected by an NSG or configure Azure Firewall rules to restrict access.,N/A,AI,Generic
CRITICAL,NS-2,Templates\LacpBillingExhaust.Template.json,32,Microsoft.Kusto/clusters resource at line 32 does not restrict public endpoints. No 'publicNetworkAccess' or similar property is set to limit exposure.,Set 'properties.publicNetworkAccess': 'Disabled' or restrict public endpoints for the Microsoft.Kusto/clusters resource to minimize exposure.,N/A,AI,Generic
CRITICAL,NS-3,Templates\LacpBillingExhaust.Template.json,32,Microsoft.Kusto/clusters resource at line 32 is not associated with any Network Security Group (NSG) to control inbound and outbound traffic.,"Deploy the Microsoft.Kusto/clusters resource within a subnet that has an NSG applied, and configure NSG rules to restrict traffic as required.",N/A,AI,Generic
CRITICAL,DP-1,Templates\LacpBillingExhaustExport.Template.json,61,"The resource 'Microsoft.UsageBilling/accounts/dataExports' does not specify encryption at rest for exported data. The template lacks explicit configuration for encryption at rest, violating DP-1.",Configure the data export resource to enable encryption at rest. Ensure that the destination supports and enforces encryption for all stored data.,N/A,AI,Generic
CRITICAL,DP-2,Templates\LacpBillingExhaustExport.Template.json,61,"The resource 'Microsoft.UsageBilling/accounts/dataExports' does not specify encryption in transit (TLS 1.2+) for data transfers. The template does not enforce secure transport for exported data, violating DP-2.",Ensure that all data exports use secure endpoints supporting TLS 1.2 or higher for data in transit. Explicitly configure the resource to require secure transport.,N/A,AI,Generic
CRITICAL,DP-3,Templates\LacpBillingExhaustExport.Template.json,61,"Sensitive connection URIs ('adxExhaustUri', 'adxExhaustDataIngestionUri') are passed as parameters and directly referenced in the resource without integration with Azure Key Vault, violating DP-3.",Store sensitive connection URIs in Azure Key Vault and reference them securely in the template using Key Vault integration.,N/A,AI,Generic
CRITICAL,NS-1,Templates\LacpGeo.Template.json,74,"Key Vault 'Microsoft.KeyVault/vaults' at line 74 does not restrict network access using network security groups (NSGs) or Azure Firewall. No network rules are defined, which may expose the Key Vault to public access.",Configure the Key Vault to use private endpoints or set network ACLs to restrict access to trusted networks only. Add 'networkAcls' property with appropriate 'virtualNetworkRules' and 'ipRules'.,N/A,AI,Generic
CRITICAL,NS-2,Templates\LacpGeo.Template.json,180,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 180 has 'publicNetworkAccess' set to 'Enabled', exposing the database to the public internet.",Set 'publicNetworkAccess' to 'Disabled' and use private endpoints to restrict access to the CosmosDB account.,N/A,AI,Generic
CRITICAL,NS-1,Templates\LacpGeo.Template.json,180,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 180 does not have virtual network filters enabled ('isVirtualNetworkFilterEnabled' is false), allowing unrestricted network access.",Set 'isVirtualNetworkFilterEnabled' to true and define 'virtualNetworkRules' to restrict access to trusted subnets.,N/A,AI,Generic
CRITICAL,NS-1,Templates\LacpGlobal.Template.json,61,"CosmosDB account 'publicNetworkAccess' is set to 'Enabled' and 'isVirtualNetworkFilterEnabled' is false at line 61, exposing the resource to the public internet without network security groups or Azure Firewall.",Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to true. Restrict access using virtual network rules or integrate with Azure Firewall.,N/A,AI,Generic
CRITICAL,NS-2,Templates\LacpGlobal.Template.json,61,"CosmosDB account at line 61 has public network access enabled ('publicNetworkAccess': 'Enabled') and no IP rules or virtual network rules, resulting in an unprotected public endpoint.",Disable public network access by setting 'publicNetworkAccess' to 'Disabled' or configure 'ipRules' and 'virtualNetworkRules' to restrict access.,N/A,AI,Generic
CRITICAL,NS-3,Templates\LacpGlobal.Template.json,61,CosmosDB account at line 61 does not use Network Security Groups (NSGs) or equivalent network controls. 'isVirtualNetworkFilterEnabled' is false and 'virtualNetworkRules' is empty.,Enable 'isVirtualNetworkFilterEnabled' and define 'virtualNetworkRules' to restrict network access using NSGs.,N/A,AI,Generic
CRITICAL,NS-1,Templates\LacpGlobal.Template.json,133,Key Vault at line 133 does not restrict network access using NSGs or Azure Firewall. No network ACLs or firewall rules are defined.,Configure Key Vault 'networkAcls' to restrict access to trusted networks and integrate with NSGs or Azure Firewall.,N/A,AI,Generic
CRITICAL,NS-2,Templates\LacpGlobal.Template.json,133,"Key Vault at line 133 is deployed without network restrictions, exposing a public endpoint.",Restrict Key Vault access by configuring 'networkAcls' to allow only trusted IPs or subnets and disable public network access.,N/A,AI,Generic
CRITICAL,NS-3,Templates\LacpGlobal.Template.json,133,Key Vault at line 133 does not use Network Security Groups (NSGs) or equivalent network controls. No 'networkAcls' or virtual network rules are present.,Configure 'networkAcls' for the Key Vault to restrict access to specific virtual networks and subnets using NSGs.,N/A,AI,Generic
CRITICAL,NS-1,Templates\LacpGlobal.Template.json,109,Storage Account at line 109 does not restrict network access using NSGs or Azure Firewall. No network ACLs or firewall rules are defined.,Configure Storage Account 'networkAcls' to restrict access to trusted networks and integrate with NSGs or Azure Firewall.,N/A,AI,Generic
CRITICAL,NS-2,Templates\LacpGlobal.Template.json,109,"Storage Account at line 109 is deployed without network restrictions, exposing a public endpoint.",Restrict Storage Account access by configuring 'networkAcls' to allow only trusted IPs or subnets and disable public network access.,N/A,AI,Generic
CRITICAL,NS-3,Templates\LacpGlobal.Template.json,109,Storage Account at line 109 does not use Network Security Groups (NSGs) or equivalent network controls. No 'networkAcls' or virtual network rules are present.,Configure 'networkAcls' for the Storage Account to restrict access to specific virtual networks and subnets using NSGs.,N/A,AI,Generic
CRITICAL,NS-1,Templates\LacpRegion.Template.json,1007,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1007 has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false, exposing the resource to the public internet without network security group or firewall protection. This violates ASB NS-1.",Set 'publicNetworkAccess' to 'Disabled' and enable 'isVirtualNetworkFilterEnabled' with appropriate 'virtualNetworkRules' to restrict access to trusted networks. Implement NSGs or Azure Firewall as required.,N/A,AI,Generic
CRITICAL,NS-20,Templates\LacpRegion.Template.json,1007,CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1007 exposes a public endpoint ('publicNetworkAccess': 'Enabled') and does not restrict access via 'ipRules' or 'virtualNetworkRules'. This increases the attack surface and violates ASB NS-20.,Disable public network access by setting 'publicNetworkAccess' to 'Disabled' and configure private endpoints or restrict access using 'ipRules' and 'virtualNetworkRules'.,N/A,AI,Generic
CRITICAL,NS-18,Templates\LacpStamp.Template.json,1002,"Microsoft.Storage/storageAccounts resource at line 1002 does not specify network security groups (NSGs), firewall rules, or private endpoints. Storage accounts should be protected from public network access.",Restrict public network access to the storage account by configuring network rules to allow only trusted subnets or private endpoints. Add 'networkAcls' with 'defaultAction' set to 'Deny' and specify allowed subnets or private endpoints.,N/A,AI,Generic
CRITICAL,NS-18,Templates\LacpStamp.Template.json,1027,"Microsoft.Storage/storageAccounts resource at line 1027 does not specify network security groups (NSGs), firewall rules, or private endpoints. Storage accounts should be protected from public network access.",Restrict public network access to the storage account by configuring network rules to allow only trusted subnets or private endpoints. Add 'networkAcls' with 'defaultAction' set to 'Deny' and specify allowed subnets or private endpoints.,N/A,AI,Generic
CRITICAL,NS-18,Templates\LacpStamp.Template.json,1052,"Microsoft.Storage/storageAccounts resource at line 1052 does not specify network security groups (NSGs), firewall rules, or private endpoints. Storage accounts should be protected from public network access.",Restrict public network access to the storage account by configuring network rules to allow only trusted subnets or private endpoints. Add 'networkAcls' with 'defaultAction' set to 'Deny' and specify allowed subnets or private endpoints.,N/A,AI,Generic
CRITICAL,NS-20,Templates\LacpStamp.Template.json,751,Microsoft.Network/trafficManagerProfiles resource at line 751 exposes public DNS endpoints without restriction. Public endpoints should be secured to minimize exposure.,"Restrict access to the public endpoints by using Azure Front Door, Application Gateway with WAF, or by implementing IP restrictions and authentication at the application layer.",N/A,AI,Generic
CRITICAL,NS-20,Templates\LacpStamp.Template.json,789,Microsoft.Network/trafficManagerProfiles resource at line 789 exposes public DNS endpoints without restriction. Public endpoints should be secured to minimize exposure.,"Restrict access to the public endpoints by using Azure Front Door, Application Gateway with WAF, or by implementing IP restrictions and authentication at the application layer.",N/A,AI,Generic
CRITICAL,NS-1,Templates\ReadAdxExhaust.Template.json,22,Microsoft.Kusto/clusters resource does not specify any network security groups (NSGs) or Azure Firewall configuration to protect the cluster. Absence of explicit network security controls may expose the resource to unauthorized network access.,Configure the Microsoft.Kusto/clusters resource to use private endpoints and associate it with a subnet protected by a Network Security Group (NSG) or Azure Firewall. Restrict inbound and outbound traffic to only required sources and destinations as per ASB NS-1.,N/A,AI,Generic
CRITICAL,NS-2,Templates\ReadAdxExhaust.Template.json,22,"Microsoft.Kusto/clusters resource does not restrict public network access. By default, Azure Data Explorer clusters are accessible over the public internet unless explicitly configured otherwise.","Set the 'publicNetworkAccess' property to 'Disabled' or configure private endpoints for the Microsoft.Kusto/clusters resource to prevent public exposure, as required by ASB NS-2.",N/A,AI,Generic
CRITICAL,NS-3,Templates\ReadAdxExhaust.Template.json,22,Microsoft.Kusto/clusters resource is not associated with any Network Security Group (NSG). Lack of NSG association may allow unrestricted network traffic to the resource.,"Deploy the Microsoft.Kusto/clusters resource within a subnet that is protected by a Network Security Group (NSG) with rules that restrict access to only necessary IP ranges and ports, as per ASB NS-3.",N/A,AI,Generic
CRITICAL,DP-1,Templates\ReadUsageAccount.Template.json,15,The Microsoft.UsageBilling/accounts resource does not specify encryption at rest settings. ASB DP-1 requires all data storage to be encrypted at rest.,Configure the Microsoft.UsageBilling/accounts resource to enable encryption at rest by specifying the appropriate encryption settings in the resource properties.,N/A,AI,Generic
CRITICAL,DP-2,Templates\ReadUsageAccount.Template.json,15,The Microsoft.UsageBilling/accounts resource does not specify enforcement of encryption in transit (TLS 1.2+). ASB DP-2 requires all data transfers to use TLS 1.2 or higher.,Ensure that the Microsoft.UsageBilling/accounts resource enforces encryption in transit by configuring the resource to require TLS 1.2 or higher for all communications.,N/A,AI,Generic
CRITICAL,DP-3,Templates\ReadUsageAccount.Template.json,15,No configuration found for storing sensitive information such as keys or secrets in Azure Key Vault for the Microsoft.UsageBilling/accounts resource. ASB DP-3 requires sensitive data to be stored securely.,"Store any sensitive information, such as access keys or secrets, in Azure Key Vault and reference them securely in the template.",N/A,AI,Generic
CRITICAL,NS-10,Templates\TrafficManagerEndpoints.Template.json,38,"The resource 'Microsoft.Network/trafficManagerProfiles/externalEndpoints' defines a public endpoint without explicit security controls to restrict access, violating ASB NS-10 (Protect public endpoints).","Restrict public access to the Traffic Manager external endpoint by implementing access controls such as IP whitelisting, authentication, or moving to private endpoints where possible.",N/A,AI,Generic
HIGH,IM-8,Templates\LacpBillingExhaust.Template.json,1,Template does not configure managed identities for any resources. Principal assignments use explicit principal IDs instead of managed identities.,"Enable and assign managed identities for resources that require access to other Azure resources, and use these managed identities in principal assignments.",N/A,AI,Generic
HIGH,NS-21,Templates\LacpGeo.Template.json,180,CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 180 does not use private endpoints. 'virtualNetworkRules' is empty and 'publicNetworkAccess' is enabled.,Implement private endpoints for the CosmosDB account and disable public network access.,N/A,AI,Generic
HIGH,NS-5,Templates\LacpGlobal.Template.json,61,CosmosDB account at line 61 does not use private endpoints. 'publicNetworkAccess' is enabled and no 'privateEndpointConnections' are defined.,Implement Azure Private Endpoints for the CosmosDB account to restrict access to private networks only.,N/A,AI,Generic
HIGH,NS-5,Templates\LacpGlobal.Template.json,133,Key Vault at line 133 does not use private endpoints. No 'privateEndpointConnections' are defined.,Implement Azure Private Endpoints for the Key Vault to restrict access to private networks only.,N/A,AI,Generic
HIGH,NS-5,Templates\LacpGlobal.Template.json,109,Storage Account at line 109 does not use private endpoints. No 'privateEndpointConnections' are defined.,Implement Azure Private Endpoints for the Storage Account to restrict access to private networks only.,N/A,AI,Generic
HIGH,NS-22,Templates\LacpRegion.Template.json,1007,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1007 does not use private endpoints ('publicNetworkAccess' is 'Enabled', 'isVirtualNetworkFilterEnabled' is false, and 'virtualNetworkRules' is empty), which is a network security weakness per ASB NS-22.","Implement Azure Private Endpoints for the CosmosDB account to ensure secure, private connectivity from trusted networks.",N/A,AI,Generic
HIGH,NS-23,Templates\LacpStamp.Template.json,1002,Microsoft.Storage/storageAccounts resource at line 1002 does not use private endpoints. Private endpoints should be used to securely access storage accounts.,Add a 'privateEndpointConnections' property to the storage account resource to enable private endpoint access.,N/A,AI,Generic
HIGH,NS-23,Templates\LacpStamp.Template.json,1027,Microsoft.Storage/storageAccounts resource at line 1027 does not use private endpoints. Private endpoints should be used to securely access storage accounts.,Add a 'privateEndpointConnections' property to the storage account resource to enable private endpoint access.,N/A,AI,Generic
HIGH,NS-23,Templates\LacpStamp.Template.json,1052,Microsoft.Storage/storageAccounts resource at line 1052 does not use private endpoints. Private endpoints should be used to securely access storage accounts.,Add a 'privateEndpointConnections' property to the storage account resource to enable private endpoint access.,N/A,AI,Generic
HIGH,NS-23,Templates\LacpStamp.Template.json,751,Microsoft.Network/trafficManagerProfiles resource at line 751 does not use private endpoints. Public DNS endpoints are exposed.,Configure private endpoints for the Traffic Manager profile or restrict access using Azure Front Door or Application Gateway.,N/A,AI,Generic
HIGH,NS-23,Templates\LacpStamp.Template.json,789,Microsoft.Network/trafficManagerProfiles resource at line 789 does not use private endpoints. Public DNS endpoints are exposed.,Configure private endpoints for the Traffic Manager profile or restrict access using Azure Front Door or Application Gateway.,N/A,AI,Generic
HIGH,AM-1,Templates\RoleAssignment.Template.json,38,"Role assignment at line 38 grants 'Contributor' role to the 'Ev2BuildoutServicePrincipalId' principal. The Contributor role provides broad permissions, which may exceed least privilege requirements.","Review the permissions required by 'Ev2BuildoutServicePrincipalId' and assign a more restrictive, custom role with only the necessary permissions as per ASB AM-1.",N/A,AI,Generic
MEDIUM,IM-8,Templates\ReadUsageAccount.Template.json,15,The Microsoft.UsageBilling/accounts resource does not explicitly enable a managed identity. ASB IM-8 recommends using managed identities for secure resource-to-resource authentication.,Add the 'identity' property with 'type': 'SystemAssigned' to the Microsoft.UsageBilling/accounts resource to enable a managed identity.,N/A,AI,Generic
