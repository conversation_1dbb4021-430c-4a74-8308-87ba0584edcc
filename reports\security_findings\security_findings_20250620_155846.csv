File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
network_demo.tf,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,24.0,"The 'source_address_prefix' property in the 'azurerm_network_security_group.demo' resource is set to '0.0.0.0/0' on line 24, allowing inbound SSH (port 22) from any IP address. This exposes the resource to the entire internet, enabling initial access, brute-force attacks, and lateral movement into the network. The blast radius includes any VM or resource associated with this NSG, potentially compromising all assets in the subnet.","Restrict 'source_address_prefix' to trusted IP ranges (e.g., corporate office or jumpbox IPs) instead of '0.0.0.0/0'. Implement a deny-by-default approach and only allow necessary management access from secure locations. Example: source_address_prefix = ""************/32"".",,,,ai_analysis,,Validated
network_demo.tf,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,43.0,"The 'allow_blob_public_access' property in the 'azurerm_storage_account.demo' resource is set to 'true' on line 43, enabling public anonymous access to blobs. This allows any unauthenticated user on the internet to read data from the storage account, creating a direct data exfiltration vector and increasing the blast radius to all data stored in public containers.",Set 'allow_blob_public_access' to 'false' to disable anonymous public access. Use private endpoints and restrict access to trusted networks or identities. Example: allow_blob_public_access = false.,,,,ai_analysis,,Validated
network_demo.tf,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,46.0,"The 'https_traffic_only' property in the 'azurerm_storage_account.demo' resource is set to 'false' on line 46, allowing unencrypted HTTP connections. This exposes sensitive data in transit to interception and man-in-the-middle attacks, enabling attackers to read or modify data between clients and the storage account.",Set 'https_traffic_only' to 'true' to enforce encryption for all data in transit. Example: https_traffic_only = true. Ensure all clients use HTTPS endpoints for storage access.,,,,ai_analysis,,Validated
storage_demo.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,26.0,"The 'minimumTlsVersion' property is set to 'TLS1_0', which enables weak encryption for data in transit. Attackers can exploit known vulnerabilities in TLS 1.0 to intercept or modify data between clients and the storage account, leading to data exfiltration or tampering. The blast radius includes all data transferred to and from this storage account.",Set 'minimumTlsVersion' to 'TLS1_2' or higher in the storage account properties to enforce strong encryption for data in transit. Example: minimumTlsVersion: 'TLS1_2'.,,,,ai_analysis,,Validated
storage_demo.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,23.0,"The 'supportsHttpsTrafficOnly' property is set to false, allowing unencrypted HTTP traffic to the storage account. This exposes data in transit to interception and man-in-the-middle attacks, enabling attackers to steal or manipulate sensitive data. The blast radius includes all clients and services communicating with this storage account.",Set 'supportsHttpsTrafficOnly' to true in the storage account properties to enforce HTTPS-only access. Example: supportsHttpsTrafficOnly: true.,,,,ai_analysis,,Validated
storage_demo.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,17.0,"The 'allowBlobPublicAccess' property is set to true, enabling anonymous public access to blobs in the storage account. Attackers can access, enumerate, and exfiltrate any data stored in public containers without authentication, leading to data breaches and compliance violations. The blast radius is all data in publicly accessible blob containers.",Set 'allowBlobPublicAccess' to false in the storage account properties to disable anonymous public access. Example: allowBlobPublicAccess: false.,,,,ai_analysis,,Validated
storage_demo.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,40.0,"The 'networkAcls.defaultAction' property is set to 'Allow', which permits all network traffic (including from the public internet) to access the storage account unless explicitly denied. This exposes the storage account to unauthorized access, brute-force attacks, and data exfiltration. The blast radius is the entire storage account and all its data.","Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only trusted networks or private endpoints. Example: networkAcls: { defaultAction: 'Deny', ... }.",,,,ai_analysis,,Validated
storage_demo.bicep,NS-8,Network Security,Detect and disable insecure services and protocols,HIGH,26.0,"The 'minimumTlsVersion' property is set to 'TLS1_0', which is an insecure protocol. Attackers can exploit weaknesses in TLS 1.0 to decrypt or manipulate traffic, bypassing encryption controls and increasing the risk of credential theft or data compromise.",Update 'minimumTlsVersion' to 'TLS1_2' or higher to disable insecure protocols. Example: minimumTlsVersion: 'TLS1_2'.,,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 8,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 0,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T15:58:46.190918,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
