#!/usr/bin/env python3
"""
Test URL extraction and tooltip functionality.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src" / "core"))

def test_url_extraction():
    print("🔍 Testing URL extraction and tooltip functionality...")
    
    try:
        os.chdir(project_root / "src" / "core")
        from security_opt import SecurityPRReviewer
        
        reviewer = SecurityPRReviewer(local_folder=str(project_root / "test_graph"))
        
        # Test URL extraction for a specific control
        control_id = "NS-1"
        print(f"\n📋 Testing URL extraction for control: {control_id}")
        
        # Check if control exists in index
        if control_id in reviewer.control_id_index:
            control_info = reviewer.control_id_index[control_id]
            print(f"✅ Control found in index: {control_info.get('name', 'Unknown')}")
            print(f"📄 Domain: {control_info.get('domain', 'Unknown')}")

            # Debug: Print all available fields for this control
            print(f"\n🔍 Debug: All fields for {control_id}:")
            for key, value in control_info.items():
                if isinstance(value, str) and len(value) > 100:
                    print(f"  {key}: {value[:100]}...")
                else:
                    print(f"  {key}: {value}")
        else:
            print(f"❌ Control {control_id} not found in index")
            return

        # Also check the benchmark data directly
        print(f"\n🔍 Debug: Checking benchmark data directly...")
        if hasattr(reviewer, 'benchmark_data') and reviewer.benchmark_data:
            controls = reviewer.benchmark_data.get('controls', [])
            for control in controls:
                if control.get('id') == control_id:
                    print(f"📋 Found control in benchmark data:")
                    for key, value in control.items():
                        if isinstance(value, str) and len(value) > 100:
                            print(f"  {key}: {value[:100]}...")
                        else:
                            print(f"  {key}: {value}")
                    break
            else:
                print(f"❌ Control {control_id} not found in benchmark data")
        
        # Test link extraction
        print(f"\n🔗 Testing link extraction for {control_id}...")
        links_info = reviewer._extract_control_links(control_id)
        
        print(f"📊 Link extraction results:")
        print(f"  - Formatted links: {len(links_info.get('formatted_links', ''))} characters")
        print(f"  - Azure guidance: {len(links_info.get('azure_guidance', ''))} characters")
        print(f"  - Implementation context: {len(links_info.get('implementation_context', ''))} characters")
        print(f"  - Raw links: {len(links_info.get('raw_links', []))} URLs")
        
        if links_info.get('raw_links'):
            print(f"\n📚 Found URLs:")
            for i, url in enumerate(links_info['raw_links'], 1):
                print(f"  {i}. {url}")
        else:
            print("❌ No URLs found!")
            
        if links_info.get('formatted_links'):
            print(f"\n📝 Formatted links:")
            print(f"  {links_info['formatted_links']}")
        else:
            print("❌ No formatted links!")
            
        if links_info.get('azure_guidance'):
            print(f"\n🔵 Azure guidance:")
            print(f"  {links_info['azure_guidance'][:200]}...")
        else:
            print("❌ No Azure guidance!")
            
        if links_info.get('implementation_context'):
            print(f"\n🛠️ Implementation context:")
            print(f"  {links_info['implementation_context'][:200]}...")
        else:
            print("❌ No implementation context!")
            
        # Test with a few more controls
        test_controls = ["NS-2", "DP-3", "IM-1"]
        for test_control in test_controls:
            if test_control in reviewer.control_id_index:
                test_links = reviewer._extract_control_links(test_control)
                url_count = len(test_links.get('raw_links', []))
                print(f"📋 {test_control}: {url_count} URLs found")
            else:
                print(f"❌ {test_control}: Not found in index")
                
    except Exception as e:
        print(f"❌ Error testing URL extraction: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_url_extraction()
