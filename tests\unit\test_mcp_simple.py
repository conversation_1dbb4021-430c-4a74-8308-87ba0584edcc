#!/usr/bin/env python3
"""
Simple test for IaC Guardian MCP Server
Tests basic functionality without complex async operations.
"""

import os
import sys
import tempfile
import json
from pathlib import Path

# Set environment variables
os.environ['ENFORCE_DOMAIN_PRIORITY'] = 'true'
os.environ['USE_OPTIMIZED_PROMPTS'] = 'true'
os.environ['ANALYSIS_SEED'] = '42'

def test_imports():
    """Test that all required modules can be imported."""
    print("🧪 Testing imports...")
    
    try:
        import mcp
        print("✅ MCP library imported successfully")
    except ImportError as e:
        print(f"❌ MCP import failed: {e}")
        return False
    
    try:
        from security_opt import SecurityPRReviewer
        print("✅ SecurityPRReviewer imported successfully")
    except ImportError as e:
        print(f"❌ SecurityPRReviewer import failed: {e}")
        return False
    
    try:
        import mcp_server
        print("✅ MCP server module imported successfully")
    except ImportError as e:
        print(f"❌ MCP server import failed: {e}")
        return False
    
    return True

def test_security_reviewer():
    """Test SecurityPRReviewer basic functionality."""
    print("🧪 Testing SecurityPRReviewer...")
    
    try:
        from security_opt import SecurityPRReviewer
        
        with tempfile.TemporaryDirectory() as temp_dir:
            reviewer = SecurityPRReviewer(local_folder=temp_dir)
            print("✅ SecurityPRReviewer created successfully")
            
            # Test benchmark loading
            benchmark = reviewer.prepare_benchmark()
            if benchmark and benchmark.get('controls'):
                print(f"✅ Benchmark loaded with {len(benchmark['controls'])} controls")
            else:
                print("⚠️ Benchmark loaded but no controls found")
            
            return True
            
    except Exception as e:
        print(f"❌ SecurityPRReviewer test failed: {e}")
        return False

def test_mcp_server_functions():
    """Test MCP server helper functions."""
    print("🧪 Testing MCP server functions...")
    
    try:
        import mcp_server
        
        # Test format functions
        mock_findings = [
            {
                "control_id": "IM-1",
                "severity": "CRITICAL",
                "file_path": "test.json",
                "line": 10,
                "description": "Test identity issue",
                "remediation": "Fix identity configuration"
            }
        ]
        
        # Test summary format
        summary = mcp_server.format_summary(mock_findings, "test.json")
        if "Security Analysis Summary" in summary:
            print("✅ Summary formatting works")
        else:
            print("⚠️ Summary formatting issue")
        
        # Test markdown format (skip if security_reviewer not initialized)
        try:
            markdown = mcp_server.format_markdown_report(mock_findings, "test.json")
            if "Security Analysis Report" in markdown:
                print("✅ Markdown formatting works")
            else:
                print("⚠️ Markdown formatting issue")
        except Exception as e:
            print(f"⚠️ Markdown formatting test skipped: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ MCP server functions test failed: {e}")
        return False

def test_file_analysis():
    """Test file analysis with a simple template."""
    print("🧪 Testing file analysis...")
    
    try:
        from security_opt import SecurityPRReviewer
        
        # Create a test ARM template with security issues
        test_template = {
            "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
            "contentVersion": "*******",
            "resources": [
                {
                    "type": "Microsoft.Storage/storageAccounts",
                    "apiVersion": "2021-04-01",
                    "name": "teststorage",
                    "location": "[resourceGroup().location]",
                    "properties": {
                        "supportsHttpsTrafficOnly": False  # Security issue
                    }
                }
            ]
        }
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test file
            test_file = Path(temp_dir) / "test.json"
            test_file.write_text(json.dumps(test_template, indent=2))
            
            # Analyze the file
            reviewer = SecurityPRReviewer(local_folder=temp_dir)
            files_to_analyze = reviewer.analyze_folder(temp_dir)
            
            if files_to_analyze:
                print(f"✅ Found {len(files_to_analyze)} files to analyze")
                
                # Try to analyze (this might take time with AI)
                try:
                    findings = reviewer.analyze_files(files_to_analyze)
                    print(f"✅ Analysis completed with {len(findings)} findings")
                except Exception as e:
                    print(f"⚠️ Analysis failed (expected if no AI credentials): {e}")
                
            else:
                print("⚠️ No files found to analyze")
            
            return True
            
    except Exception as e:
        print(f"❌ File analysis test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting IaC Guardian MCP Server Simple Tests")
    print("=" * 60)
    
    tests = [
        ("Import Tests", test_imports),
        ("SecurityPRReviewer Test", test_security_reviewer),
        ("MCP Server Functions", test_mcp_server_functions),
        ("File Analysis Test", test_file_analysis)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 40)
        
        try:
            success = test_func()
            results[test_name] = "PASSED" if success else "FAILED"
            status = "✅" if success else "❌"
            print(f"{status} {test_name}: {'PASSED' if success else 'FAILED'}")
        except Exception as e:
            results[test_name] = f"ERROR: {str(e)}"
            print(f"❌ {test_name}: ERROR - {str(e)}")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result == "PASSED")
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅" if result == "PASSED" else "❌"
        print(f"{status} {test_name}: {result}")
    
    print(f"\n📈 Overall: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! MCP server is ready to use.")
        print("\nNext steps:")
        print("1. Configure VS Code settings (see MCP_SETUP_GUIDE.md)")
        print("2. Restart VS Code")
        print("3. Use @iac-guardian tools in Copilot Chat")
    else:
        print("⚠️ Some tests failed - check the errors above")

if __name__ == "__main__":
    main()
