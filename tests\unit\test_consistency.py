#!/usr/bin/env python3
"""
Test script to validate consistency improvements in security analysis.
This script runs the same analysis multiple times and compares results.
"""

import os
import json
import hashlib
import tempfile
from pathlib import Path
from security_opt import SecurityPRReviewer
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_template():
    """Create a sample ARM template for testing consistency."""
    template_content = {
        "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
        "contentVersion": "*******",
        "resources": [
            {
                "type": "Microsoft.Storage/storageAccounts",
                "apiVersion": "2021-04-01",
                "name": "teststorageaccount",
                "location": "[resourceGroup().location]",
                "sku": {
                    "name": "Standard_LRS"
                },
                "kind": "StorageV2",
                "properties": {
                    "supportsHttpsTrafficOnly": False,  # Security issue
                    "minimumTlsVersion": "TLS1_0",      # Security issue
                    "networkAcls": {
                        "defaultAction": "Allow"         # Security issue
                    },
                    "publicNetworkAccess": "Enabled"    # Security issue
                }
            },
            {
                "type": "Microsoft.Network/networkSecurityGroups",
                "apiVersion": "2021-02-01",
                "name": "testNSG",
                "location": "[resourceGroup().location]",
                "properties": {
                    "securityRules": [
                        {
                            "name": "AllowAllInbound",
                            "properties": {
                                "protocol": "*",
                                "sourcePortRange": "*",
                                "destinationPortRange": "*",
                                "sourceAddressPrefix": "*",    # Security issue
                                "destinationAddressPrefix": "*",
                                "access": "Allow",
                                "priority": 100,
                                "direction": "Inbound"
                            }
                        }
                    ]
                }
            }
        ]
    }
    return json.dumps(template_content, indent=2)

def hash_findings(findings):
    """Create a hash of findings for comparison."""
    # Sort findings by control_id and line for consistent hashing
    sorted_findings = sorted(findings, key=lambda x: (x.get('control_id', ''), x.get('line', 0)))
    
    # Create a simplified representation for hashing
    simplified = []
    for finding in sorted_findings:
        simplified.append({
            'control_id': finding.get('control_id', ''),
            'severity': finding.get('severity', ''),
            'line': finding.get('line', 0),
            'description': finding.get('description', '')[:100]  # First 100 chars
        })
    
    findings_str = json.dumps(simplified, sort_keys=True)
    return hashlib.md5(findings_str.encode()).hexdigest()

def run_consistency_test(num_runs=3):
    """Run consistency test with multiple analysis runs."""
    logger.info(f"Starting consistency test with {num_runs} runs")
    
    # Create temporary directory and test file
    with tempfile.TemporaryDirectory() as temp_dir:
        test_file_path = Path(temp_dir) / "test_template.json"
        test_file_path.write_text(create_test_template())
        
        results = []
        hashes = []
        
        for run_num in range(1, num_runs + 1):
            logger.info(f"Running analysis #{run_num}")
            
            # Test with optimized prompts
            os.environ['USE_OPTIMIZED_PROMPTS'] = 'true'
            os.environ['ANALYSIS_SEED'] = '42'
            
            try:
                reviewer = SecurityPRReviewer(local_folder=temp_dir)
                findings = reviewer.analyze_folder(temp_dir)
                
                # Store results
                results.append({
                    'run': run_num,
                    'findings_count': len(findings),
                    'findings': findings
                })
                
                # Calculate hash for comparison
                findings_hash = hash_findings(findings)
                hashes.append(findings_hash)
                
                logger.info(f"Run #{run_num}: Found {len(findings)} findings, hash: {findings_hash[:8]}")
                
                # Log severity distribution
                severity_counts = {}
                for finding in findings:
                    severity = finding.get('severity', 'UNKNOWN')
                    severity_counts[severity] = severity_counts.get(severity, 0) + 1
                
                logger.info(f"Run #{run_num} severity distribution: {severity_counts}")
                
            except Exception as e:
                logger.error(f"Error in run #{run_num}: {str(e)}")
                results.append({
                    'run': run_num,
                    'error': str(e)
                })
                hashes.append('ERROR')
    
    return results, hashes

def analyze_consistency(results, hashes):
    """Analyze consistency of results."""
    logger.info("\n" + "="*60)
    logger.info("CONSISTENCY ANALYSIS RESULTS")
    logger.info("="*60)
    
    # Check if all hashes are identical
    unique_hashes = set(h for h in hashes if h != 'ERROR')
    
    if len(unique_hashes) == 1:
        logger.info("✅ PERFECT CONSISTENCY: All runs produced identical results")
        consistency_rate = 100.0
    elif len(unique_hashes) == 0:
        logger.error("❌ ALL RUNS FAILED")
        consistency_rate = 0.0
    else:
        logger.warning(f"⚠️  INCONSISTENT RESULTS: Found {len(unique_hashes)} different result sets")
        consistency_rate = (hashes.count(max(set(hashes), key=hashes.count)) / len(hashes)) * 100
    
    logger.info(f"Consistency Rate: {consistency_rate:.1f}%")
    
    # Analyze finding counts
    successful_results = [r for r in results if 'error' not in r]
    if successful_results:
        finding_counts = [r['findings_count'] for r in successful_results]
        min_findings = min(finding_counts)
        max_findings = max(finding_counts)
        avg_findings = sum(finding_counts) / len(finding_counts)
        
        logger.info(f"Finding counts - Min: {min_findings}, Max: {max_findings}, Avg: {avg_findings:.1f}")
        
        if min_findings == max_findings:
            logger.info("✅ CONSISTENT FINDING COUNTS: All runs found same number of issues")
        else:
            logger.warning(f"⚠️  VARIABLE FINDING COUNTS: Range {min_findings}-{max_findings}")
    
    # Show detailed comparison if inconsistent
    if len(unique_hashes) > 1:
        logger.info("\nDETAILED COMPARISON:")
        for i, result in enumerate(successful_results):
            logger.info(f"Run {result['run']}: {result['findings_count']} findings, hash: {hashes[i][:8]}")
            
            # Show first few findings for comparison
            for j, finding in enumerate(result['findings'][:3]):
                logger.info(f"  Finding {j+1}: {finding.get('control_id', 'N/A')} - {finding.get('severity', 'N/A')} - Line {finding.get('line', 'N/A')}")
    
    return consistency_rate

def test_with_and_without_optimization():
    """Test consistency with and without prompt optimization."""
    logger.info("\n" + "="*60)
    logger.info("TESTING WITH AND WITHOUT OPTIMIZATION")
    logger.info("="*60)
    
    # Test without optimization
    logger.info("\n--- Testing WITHOUT optimization ---")
    os.environ['USE_OPTIMIZED_PROMPTS'] = 'false'
    results_unopt, hashes_unopt = run_consistency_test(3)
    consistency_unopt = analyze_consistency(results_unopt, hashes_unopt)
    
    # Test with optimization
    logger.info("\n--- Testing WITH optimization ---")
    os.environ['USE_OPTIMIZED_PROMPTS'] = 'true'
    results_opt, hashes_opt = run_consistency_test(3)
    consistency_opt = analyze_consistency(results_opt, hashes_opt)
    
    # Compare results
    logger.info("\n" + "="*60)
    logger.info("OPTIMIZATION IMPACT SUMMARY")
    logger.info("="*60)
    logger.info(f"Without optimization: {consistency_unopt:.1f}% consistency")
    logger.info(f"With optimization:    {consistency_opt:.1f}% consistency")
    
    improvement = consistency_opt - consistency_unopt
    if improvement > 0:
        logger.info(f"✅ IMPROVEMENT: +{improvement:.1f}% consistency gain")
    elif improvement == 0:
        logger.info("➡️  NO CHANGE: Same consistency level")
    else:
        logger.warning(f"⚠️  REGRESSION: -{abs(improvement):.1f}% consistency loss")

if __name__ == "__main__":
    try:
        # Run basic consistency test
        logger.info("Starting IaC Guardian GPT Consistency Test")
        
        # Test with optimization enabled
        results, hashes = run_consistency_test(5)
        consistency_rate = analyze_consistency(results, hashes)
        
        # Test comparison if time permits
        if consistency_rate < 100:
            logger.info("\nRunning comparison test to identify optimization impact...")
            test_with_and_without_optimization()
        
        logger.info("\nConsistency test completed!")
        
    except KeyboardInterrupt:
        logger.info("\nTest interrupted by user")
    except Exception as e:
        logger.error(f"Test failed with error: {str(e)}")
        raise
