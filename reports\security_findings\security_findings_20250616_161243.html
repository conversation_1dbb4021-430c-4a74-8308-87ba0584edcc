
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Security Findings Report</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    h1 { color: #333; }
                    .summary { background: #f5f5f5; padding: 15px; margin: 20px 0; border-radius: 5px; }
                    .severity-group { margin: 20px 0; }
                    .severity-header { 
                        padding: 10px; 
                        border-radius: 5px;
                        margin: 10px 0;
                        color: white;
                        font-weight: bold;
                    }
                    .critical { background-color: #dc3545; }
                    .high { background-color: #fd7e14; }
                    .medium { background-color: #ffc107; color: black; }
                    .low { background-color: #17a2b8; }
                    .finding {
                        border: 1px solid #ddd;
                        padding: 15px;
                        margin: 10px 0;
                        border-radius: 5px;
                        background: white;
                    }
                    .code-snippet {
                        background: #f8f9fa;
                        padding: 10px;
                        border-radius: 3px;
                        font-family: monospace;
                        white-space: pre-wrap;
                        margin: 10px 0;
                    }
                    .label { font-weight: bold; color: #555; }
                </style>
            </head>
            <body>
                <h1>🔒 Security Findings Report</h1>
                
            <div class="summary">
                <h2>Summary</h2>
                <p><strong>Total Findings:</strong> 75</p>
                <p><strong>Files Affected:</strong> 14</p>
                <p><strong>Findings by Severity:</strong></p>
                <ul>
            <li>🔴 CRITICAL: 9</li><li>🟠 HIGH: 26</li><li>🟡 MEDIUM: 27</li><li>🔵 LOW: 13</li></ul></div>
                
                    <div class="severity-group">
                        <div class="severity-header critical">🔴 CRITICAL Severity Findings</div>
                    
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> LacpGeo.Template.json</p>
                            <p><span class="label">Line:</span> 135</p>
                            <p><span class="label">Issue:</span> The Key Vault resource does not restrict network access, lacking Virtual Network service endpoints or private endpoints. This leaves the Key Vault accessible from any public IP, which is not compliant with ASB network security guidelines and exposes sensitive secrets and keys.</p>
                            <p><span class="label">Remediation:</span> Configure the Key Vault to be accessible only from approved private IP addresses, subnets, or via Private Endpoint. Add &#x27;networkAcls&#x27; to the Key Vault properties to restrict public access and enable default action &#x27;Deny&#x27; for non-specified networks.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-8</p>
                            <p><span class="label">File:</span> LacpGeo.Template.json</p>
                            <p><span class="label">Line:</span> 234</p>
                            <p><span class="label">Issue:</span> Azure Cosmos DB has &#x27;publicNetworkAccess&#x27; set to &#x27;Enabled&#x27; and &#x27;isVirtualNetworkFilterEnabled&#x27; set to &#x27;false&#x27;, which means the database is accessible over the public Internet. This increases attack surface for data exfiltration and breaches.</p>
                            <p><span class="label">Remediation:</span> Set &#x27;publicNetworkAccess&#x27; to &#x27;Disabled&#x27; and &#x27;isVirtualNetworkFilterEnabled&#x27; to &#x27;true&#x27; for Cosmos DB. Configure specific &#x27;virtualNetworkRules&#x27; or use Private Endpoint to limit access to trusted networks only.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> LacpGeo.Template.json</p>
                            <p><span class="label">Line:</span> 237</p>
                            <p><span class="label">Issue:</span> Azure Cosmos DB has no virtual network rules and no IP rules; therefore, the service is fully exposed to the public Internet, violating the requirement to protect public endpoints.</p>
                            <p><span class="label">Remediation:</span> Configure &#x27;virtualNetworkRules&#x27; and/or &#x27;ipRules&#x27; on the Cosmos DB account. Limit access to required Azure subnets and specific trusted IP ranges only.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> LacpGlobal.Template.json</p>
                            <p><span class="label">Line:</span> 52</p>
                            <p><span class="label">Issue:</span> The Cosmos DB account (&#x27;Microsoft.DocumentDB/databaseAccounts&#x27;) has &#x27;publicNetworkAccess&#x27; set to &#x27;Enabled&#x27; and &#x27;isVirtualNetworkFilterEnabled&#x27; set to &#x27;false&#x27;, exposing the database to the public Internet without any virtual network restrictions. This leaves the database accessible from anywhere, violating network segmentation best practices.</p>
                            <p><span class="label">Remediation:</span> Set &#x27;publicNetworkAccess&#x27; to &#x27;Disabled&#x27; or &#x27;isVirtualNetworkFilterEnabled&#x27; to &#x27;true&#x27;, and define proper &#x27;virtualNetworkRules&#x27; and/or &#x27;ipRules&#x27; to restrict access to trusted networks only as required by ASB NS-1.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> LacpGlobal.Template.json</p>
                            <p><span class="label">Line:</span> 52</p>
                            <p><span class="label">Issue:</span> No virtual network restrictions or IP-based access rules are set for the Cosmos DB account; both &#x27;virtualNetworkRules&#x27; and &#x27;ipRules&#x27; arrays are empty. This means the resource is publicly accessible from any internet address by default.</p>
                            <p><span class="label">Remediation:</span> Add IP firewall rules under &#x27;ipRules&#x27; to restrict access to only trusted IP addresses or enable &#x27;virtualNetworkRules&#x27; to limit access to selected subnets, as mandated by ASB NS-2.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> LacpRegion.Template.json</p>
                            <p><span class="label">Line:</span> 1092</p>
                            <p><span class="label">Issue:</span> The Cosmos DB account sets &#x27;publicNetworkAccess&#x27; to &#x27;Enabled&#x27; and &#x27;isVirtualNetworkFilterEnabled&#x27; to false, meaning the account is open to the public internet with no network restrictions, violating the control to use NSGs or Azure Firewall for protection.</p>
                            <p><span class="label">Remediation:</span> Set &#x27;publicNetworkAccess&#x27; to &#x27;Disabled&#x27; or &#x27;isVirtualNetworkFilterEnabled&#x27; to &#x27;true&#x27; and define appropriate &#x27;virtualNetworkRules&#x27; to restrict access to trusted networks only. Implement appropriate NSG or firewall rules to reduce exposure.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> LacpRegion.Template.json</p>
                            <p><span class="label">Line:</span> 1092</p>
                            <p><span class="label">Issue:</span> Cosmos DB resource is exposed to the public Internet with no IP restrictions or VNet rules enforced. &#x27;ipRules&#x27; and &#x27;virtualNetworkRules&#x27; are empty with public access enabled.</p>
                            <p><span class="label">Remediation:</span> Restrict public access by setting &#x27;publicNetworkAccess&#x27; to &#x27;Disabled&#x27;, defining allowed IP addresses in &#x27;ipRules&#x27;, or enforcing virtual network rules to ensure only trusted networks can access the Cosmos DB.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> LacpStamp.Parameters-LacpStampResources.json</p>
                            <p><span class="label">Line:</span> 67</p>
                            <p><span class="label">Issue:</span> The parameter &#x27;dasStorageAccountKey&#x27; contains a direct reference to a storage account key output, potentially exposing sensitive storage access credentials within deployment parameters. According to ASB DP-3, sensitive data such as storage keys must be securely managed using Azure Key Vault and not passed inline in templates or parameters.</p>
                            <p><span class="label">Remediation:</span> Store the storage account key securely in Azure Key Vault and reference it from the template using a secureObject parameter type. Update your deployment process to retrieve sensitive values dynamically from Key Vault at runtime rather than supplying them directly in parameter files.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> LacpStamp.Parameters-LacpStampResources.json</p>
                            <p><span class="label">Line:</span> 66</p>
                            <p><span class="label">Issue:</span> The parameter &#x27;dasStorageAccountName&#x27; is defined using a service resource deployment output and may leak sensitive details about the storage account. While names are generally less sensitive than keys, exposing them in template parameters can aid attackers in reconnaissance.</p>
                            <p><span class="label">Remediation:</span> Avoid exposing storage account names in publicly accessible or widely distributed files. If necessary, retrieve resource identifiers securely using Azure Key Vault references where possible.</p>
                        </div></div>
                    <div class="severity-group">
                        <div class="severity-header high">🟠 HIGH Severity Findings</div>
                    
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> IngestionStorageAccount.Template.json</p>
                            <p><span class="label">Line:</span> 46</p>
                            <p><span class="label">Issue:</span> The storage account resources do not have any network restrictions such as networkRules defined. By default, Azure Storage accounts allow traffic from all networks (public endpoint enabled), which exposes the storage accounts to the internet and increases the attack surface for unauthorized access.</p>
                            <p><span class="label">Remediation:</span> Add the &#x27;networkAcls&#x27; property to explicitly restrict access to the storage account. Set &#x27;defaultAction&#x27; to &#x27;Deny&#x27;, and allow trusted subnets or private endpoints as appropriate. Example: &#x27;networkAcls&#x27;: {{ &#x27;defaultAction&#x27;: &#x27;Deny&#x27;, &#x27;bypass&#x27;: &#x27;AzureServices&#x27;, &#x27;ipRules&#x27;: [], &#x27;virtualNetworkRules&#x27;: [] }}.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> IngestionStorageAccount.Template.json</p>
                            <p><span class="label">Line:</span> 46</p>
                            <p><span class="label">Issue:</span> Public network access is not disabled for the storage accounts, which means the storage endpoints are accessible over the internet. This exposure can be exploited if authentication or other defenses fail.</p>
                            <p><span class="label">Remediation:</span> Explicitly set &#x27;publicNetworkAccess&#x27; to &#x27;Disabled&#x27; in the storage account properties to prevent public access. If public access is necessary, restrict it to specific IP addresses or virtual networks using &#x27;networkAcls&#x27;.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> LacpBilling.Template.json</p>
                            <p><span class="label">Line:</span> 619</p>
                            <p><span class="label">Issue:</span> The storage accounts are deployed without any network restrictions. There is no use of private endpoints, network rules, or service endpoints, nor are there Network Security Groups (NSGs) or Azure Firewall mentioned to restrict access. Thus, the storage accounts may be accessible from any network, violating ASB control NS-1.</p>
                            <p><span class="label">Remediation:</span> Configure the storage account with network rules to allow access only from approved virtual networks or IP address ranges. Use private endpoints where feasible, and consider integrating with Azure Firewall or NSGs to strictly control allowed traffic.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-6</p>
                            <p><span class="label">File:</span> LacpBilling.Template.json</p>
                            <p><span class="label">Line:</span> 619</p>
                            <p><span class="label">Issue:</span> Storage account resources are created without configuring &#x27;networkAcls&#x27; to restrict public network access, nor is there any mention of using private endpoints. This leaves the storage account endpoints potentially accessible over public internet, increasing the risk of data exposure.</p>
                            <p><span class="label">Remediation:</span> Set the &#x27;networkAcls&#x27; property on storage accounts to restrict public network access, allowing traffic only from specific trusted subnets. Consider enabling private endpoints to provide access only over your trusted virtual network.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> LacpBilling.Template.json</p>
                            <p><span class="label">Line:</span> 619</p>
                            <p><span class="label">Issue:</span> The storage accounts have not explicitly enabled network rules to restrict access to public endpoints. If default network settings are not changed, the storage account endpoints might be public.</p>
                            <p><span class="label">Remediation:</span> Set the storage account&#x27;s &#x27;networkAcls.defaultAction&#x27; to &#x27;Deny&#x27; and explicitly allow access only from necessary, trusted subnets or public IPs. Where possible, use private endpoints to fully remove public access.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> LacpBillingExhaust.Template.json</p>
                            <p><span class="label">Line:</span> 44</p>
                            <p><span class="label">Issue:</span> The Microsoft.Kusto cluster resource (ADX) is being deployed without any explicit network security controls (NSG, firewall rules, or private endpoints). By default, Azure Data Explorer (ADX) clusters are accessible over the public network unless network restrictions are configured.</p>
                            <p><span class="label">Remediation:</span> Configure the ADX cluster with either a private endpoint or restrict public network access using IP firewall rules. If required, place the cluster within a VNET and apply Network Security Groups (NSGs) and disable public network access to minimize exposure.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> LacpBillingExhaust.Template.json</p>
                            <p><span class="label">Line:</span> 44</p>
                            <p><span class="label">Issue:</span> The ADX cluster resource does not specify any protection for public endpoints. This introduces the risk of exposing the service to the internet.</p>
                            <p><span class="label">Remediation:</span> Explicitly configure the ADX cluster&#x27;s &#x27;publicNetworkAccess&#x27; property to &#x27;Disabled&#x27; or restrict allowed IPs to only trusted sources. Alternatively, use private endpoints for all access and ensure no public IP addresses are exposed.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> LacpBillingExhaustExport.Template.json</p>
                            <p><span class="label">Line:</span> 8</p>
                            <p><span class="label">Issue:</span> Parameters &#x27;adxExhaustDataIngestionUri&#x27; and &#x27;adxExhaustUri&#x27; are defined as plain string parameters. If secrets such as keys, tokens, or connection strings are passed directly as parameter values, this may risk accidental exposure of sensitive information. This violates best practices for managing sensitive data, which recommend using Azure Key Vault.</p>
                            <p><span class="label">Remediation:</span> Refactor the template to reference sensitive secrets (e.g., database URIs, connection strings, or authentication tokens) from Azure Key Vault using secure reference parameters instead of plain strings. Document for users that these parameters must not contain credentials unless secured.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> LacpGlobal.Template.json</p>
                            <p><span class="label">Line:</span> 193</p>
                            <p><span class="label">Issue:</span> Storage accounts (&#x27;Microsoft.Storage/storageAccounts&#x27;) lack network restriction properties (such as &#x27;networkAcls&#x27;). There is no reference to any Network Security Groups (NSGs), virtual network rules, or firewall settings, making the storage accounts publicly accessible by default.</p>
                            <p><span class="label">Remediation:</span> Implement &#x27;networkAcls&#x27; to restrict access by IP address or virtual network, and consider limiting public network access by setting &#x27;publicNetworkAccess&#x27; to &#x27;Disabled&#x27;, in line with ASB NS-1.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> LacpGlobal.Template.json</p>
                            <p><span class="label">Line:</span> 193</p>
                            <p><span class="label">Issue:</span> Public network access is not restricted for Storage Accounts, and there are no NSGs or firewall rules specified, exposing the accounts to the internet.</p>
                            <p><span class="label">Remediation:</span> Set &#x27;publicNetworkAccess&#x27; to &#x27;Disabled&#x27; for storage accounts unless absolutely necessary, and define &#x27;networkAcls&#x27; to limit public endpoint exposure, as per ASB NS-2.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> LacpRegion.Parameters-LacpRegionResources.json</p>
                            <p><span class="label">Line:</span> 16</p>
                            <p><span class="label">Issue:</span> The parameter &#x27;isBoundariesRestricted&#x27; is set to &#x27;false&#x27;, indicating that network boundary restrictions are not enforced. This could result in Logic Apps and associated resources being accessible from the public internet, exposing them to unauthorized access or attacks.</p>
                            <p><span class="label">Remediation:</span> Set &#x27;isBoundariesRestricted&#x27; to &#x27;true&#x27; to enforce network boundaries. Restrict public access to Logic Apps and associated storage accounts by enabling private endpoints, service endpoints, or using network security groups (NSGs) to restrict allowed IPs and subnets.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> LacpRegion.Parameters-LacpRegionResources.json</p>
                            <p><span class="label">Line:</span> 45</p>
                            <p><span class="label">Issue:</span> Resource names related to storage and sensitive environments appear in clear text (&#x27;amsBackupStorageAccountName&#x27;, &#x27;dasCustomerConfigStorageAccountName&#x27;, etc.), but there is no evidence that secrets, connection strings, or sensitive credentials are being securely managed via Azure Key Vault. Storing secrets or credentials in plain text parameters is a security risk.</p>
                            <p><span class="label">Remediation:</span> Ensure all secrets, connection strings, and sensitive data required by Logic Apps and other resources are referenced from Azure Key Vault, not embedded in parameter files or code, even for deployment purposes. Use Key Vault references where needed.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> LacpRegion.Parameters-LacpRegionResources.json</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> This parameter file does not include or reference any network security controls (such as Network Security Groups, Private Endpoints, or Azure Firewall) for protecting Logic Apps and related storage accounts. Lack of network controls may result in public exposure.</p>
                            <p><span class="label">Remediation:</span> In the resource deployment template, ensure that NSGs, private endpoints, or Azure Firewall are in place to restrict access to Logic Apps and dependent services (storage, Key Vault, etc.). Update the parameters or associated templates to require secure network configuration.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> LacpRegion.Template.json</p>
                            <p><span class="label">Line:</span> 666</p>
                            <p><span class="label">Issue:</span> Storage accounts (e.g., &#x27;dataPullerEventHubStorageAccountName&#x27;) are created with no network access restrictions (no &#x27;networkAcls&#x27; block present), which means by default public network access is enabled, violating the requirement for network protections.</p>
                            <p><span class="label">Remediation:</span> Add the &#x27;networkAcls&#x27; property to each storage account, set &#x27;defaultAction&#x27; to &#x27;Deny&#x27;, and explicitly allow only required subnets and trusted IP ranges via &#x27;virtualNetworkRules&#x27; or &#x27;ipRules&#x27;.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> LacpRegion.Template.json</p>
                            <p><span class="label">Line:</span> 666</p>
                            <p><span class="label">Issue:</span> Public endpoints for storage accounts are not protected; missing explicit restriction on network access leaves storage accounts publicly reachable.</p>
                            <p><span class="label">Remediation:</span> Restrict public network access for storage accounts by setting &#x27;networkAcls.defaultAction&#x27; to &#x27;Deny&#x27;. Allow access only from trusted VNets and IPs, or set &#x27;publicNetworkAccess&#x27; to &#x27;Disabled&#x27;.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> LacpRegion.Template.json</p>
                            <p><span class="label">Line:</span> 1803</p>
                            <p><span class="label">Issue:</span> Storage account keys and Cosmos DB keys are retrieved with listKeys() and written as plaintext values in Key Vault secrets. Exposure of these connection strings increases the risk if Key Vault access policies are too permissive. Additionally, the connection string is constructed including the account key within the template logic.</p>
                            <p><span class="label">Remediation:</span> Whenever possible, prefer use of Azure AD authentication for services rather than distributing Storage/Cosmos DB account keys anywhere, even in Key Vault. If keys are needed, restrict Key Vault access policies to only trusted identities following least privilege. Rotate keys regularly.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> LacpRegion.Template.json</p>
                            <p><span class="label">Line:</span> 2766</p>
                            <p><span class="label">Issue:</span> The template output section exports the storage account name and its primary key directly as outputs. This can potentially expose sensitive information to users or automation tools consuming the output.</p>
                            <p><span class="label">Remediation:</span> Remove storage account key from template outputs. Instead, retrieve secrets at runtime by authorized identities from Azure Key Vault. If output is necessary for automation, ensure output channels are secured and limit access only to required and trusted systems.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-3</p>
                            <p><span class="label">File:</span> LacpRegion.Template.json</p>
                            <p><span class="label">Line:</span> 666</p>
                            <p><span class="label">Issue:</span> There are no Network Security Groups (NSGs) deployed or associated with any subnets that might have access to storage accounts. This increases exposure to unintended inbound/outbound traffic.</p>
                            <p><span class="label">Remediation:</span> Deploy NSGs and associate them with relevant subnets. Define rules that restrict traffic to/from storage accounts and other sensitive resources, based on least-privilege access.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> LacpStamp.Template.json</p>
                            <p><span class="label">Line:</span> 605</p>
                            <p><span class="label">Issue:</span> The Microsoft.Storage/storageAccounts resources do not employ any network restrictions, such as using &#x27;networkAcls&#x27; to limit access to selected networks. Allowing unrestricted access exposes the storage accounts to the public internet and increases the risk of unauthorized access.</p>
                            <p><span class="label">Remediation:</span> Update each storage account resource to define &#x27;networkAcls&#x27; with &#x27;defaultAction&#x27; set to &#x27;Deny&#x27;, and explicitly allow trusted virtual networks and IP addresses only. Consider leveraging Private Endpoints for sensitive storage resources per ASB NS-1.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> LacpStamp.Template.json</p>
                            <p><span class="label">Line:</span> 605</p>
                            <p><span class="label">Issue:</span> Storage Accounts (Microsoft.Storage/storageAccounts) are created without network restrictions or private endpoints, resulting in publicly accessible endpoints by default. This exposes storage to internet traffic.</p>
                            <p><span class="label">Remediation:</span> Restrict public network access by configuring &#x27;networkAcls&#x27; with &#x27;defaultAction&#x27;:&#x27;Deny&#x27; and, where possible, use Azure Private Endpoints to allow access only through internal networks.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> ReadAdxExhaust.Template.json</p>
                            <p><span class="label">Line:</span> 17</p>
                            <p><span class="label">Issue:</span> The Kusto cluster resource does not specify any network security configuration such as private endpoints, VNet integration, or network access restrictions. This means the cluster is exposed with default public endpoints, violating the control to protect critical resources using NSGs or Azure Firewall.</p>
                            <p><span class="label">Remediation:</span> Configure the Kusto cluster to disable public network access and enable private endpoints, or restrict access using network security groups (NSGs) or Azure Firewall. Add the `publicNetworkAccess` property set to `Disabled` and integrate with a virtual network.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> ReadAdxExhaust.Template.json</p>
                            <p><span class="label">Line:</span> 17</p>
                            <p><span class="label">Issue:</span> The deployment enables default public endpoints for the Kusto (ADX) cluster by omitting explicit endpoint configuration, which means cluster APIs may be accessible from the internet.</p>
                            <p><span class="label">Remediation:</span> Explicitly disable public network access by setting the `publicNetworkAccess` property to &#x27;Disabled&#x27;, and provision a private endpoint to restrict access to authorized networks only.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> IM-6</p>
                            <p><span class="label">File:</span> RoleAssignment.Template.json</p>
                            <p><span class="label">Line:</span> 60</p>
                            <p><span class="label">Issue:</span> The service principal parameter &#x27;Ev2BuildoutServicePrincipalId&#x27; is assigned the &#x27;Contributor&#x27; role at the subscription scope without evidence of required scope limitation or minimal permissions. Assigning broad &#x27;Contributor&#x27; rights to a service principal may violate least privilege and RBAC best practices.</p>
                            <p><span class="label">Remediation:</span> Review the permissions required by the service principal and assign only the minimum necessary access at the lowest viable scope (e.g., specific resource group or resource instead of subscription level). Consider using custom roles if built-in roles are too broad.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> IM-7</p>
                            <p><span class="label">File:</span> RoleAssignment.Template.json</p>
                            <p><span class="label">Line:</span> 60</p>
                            <p><span class="label">Issue:</span> Assigning &#x27;Contributor&#x27; access to application or service principals (&#x27;Ev2BuildoutServicePrincipalId&#x27;) increases security risk if the principal credentials are not properly protected or if excessive permissions are granted.</p>
                            <p><span class="label">Remediation:</span> Ensure that service principals follow strong credential hygiene (e.g., use certificate or managed identity authentication, restrict credential visibility, rotate credentials regularly). Limit role assignment to the minimum necessary permissions and regularly review application identities.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> AM-1</p>
                            <p><span class="label">File:</span> RoleAssignment.Template.json</p>
                            <p><span class="label">Line:</span> 60</p>
                            <p><span class="label">Issue:</span> The role assignment grants blanket &#x27;Contributor&#x27; access to a principal, potentially violating the principle of least privilege.</p>
                            <p><span class="label">Remediation:</span> Restrict access by splitting permissions into more targeted roles (e.g., use built-in roles with reduced permissions or custom roles) and assign only those necessary for task fulfillment. Apply at lowest possible scope.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> TrafficManagerEndpoints.Template.json</p>
                            <p><span class="label">Line:</span> 47</p>
                            <p><span class="label">Issue:</span> Traffic Manager external endpoints are created with public DNS names derived from cluster parameters (e.g., &#x27;&lt;prefix&gt;-&lt;index&gt;-&lt;suffix&gt;.&lt;region&gt;.&lt;domain&gt;&#x27;). The template does not apply any access restriction, authentication, or validation of endpoint exposure, which could result in unintentional public exposure.</p>
                            <p><span class="label">Remediation:</span> Validate whether these endpoints must be publicly accessible. Restrict access to necessary IP ranges or front-end these endpoints with additional security controls (e.g., Azure Firewall, Web Application Firewall, or authentication solutions). If possible, implement network-level restrictions at the target resource level to limit unwanted public access.</p>
                        </div></div>
                    <div class="severity-group">
                        <div class="severity-header medium">🟡 MEDIUM Severity Findings</div>
                    
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-7</p>
                            <p><span class="label">File:</span> LacpBilling.Template.json</p>
                            <p><span class="label">Line:</span> 619</p>
                            <p><span class="label">Issue:</span> No Network Security Groups (NSGs) are referenced or associated with any network resource related to the storage accounts. NSGs should be used to control the network traffic (inbound/outbound) to your resources.</p>
                            <p><span class="label">Remediation:</span> Associate the subnets used by storage accounts or by any consumers of this storage with Network Security Groups to ensure only approved network traffic is allowed.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> IM-8</p>
                            <p><span class="label">File:</span> LacpBilling.Template.json</p>
                            <p><span class="label">Line:</span> 680</p>
                            <p><span class="label">Issue:</span> Role assignments are granted to managed identities at the storage account scope but there is no documentation or RBAC configuration ensuring these identities have only the minimum permissions required. Principle of least privilege should be enforced for all role assignments.</p>
                            <p><span class="label">Remediation:</span> Review the managed identity and ensure the role assignment &#x27;Storage Queue Data Contributor&#x27; is strictly necessary. Assign only the minimum set of roles and scopes necessary for the application&#x27;s function, and avoid broad role assignments.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> IM-8</p>
                            <p><span class="label">File:</span> LacpBillingExhaust.Template.json</p>
                            <p><span class="label">Line:</span> 84</p>
                            <p><span class="label">Issue:</span> Principal assignments for Azure Data Explorer (ADX) are based on provided object IDs, and use principalType &#x27;App&#x27;, but there is no evidence that managed identities are used for these assignments. Hardcoding application/service principal IDs can be less secure than managed identities.</p>
                            <p><span class="label">Remediation:</span> Prefer using managed identities for Azure resources for authentication where possible. If the consuming resource supports managed identity, assign role-based access to it instead of using generic service principal objectIDs.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> AM-1</p>
                            <p><span class="label">File:</span> LacpBillingExhaust.Template.json</p>
                            <p><span class="label">Line:</span> 100</p>
                            <p><span class="label">Issue:</span> Multiple assignments are made giving &#x27;User&#x27; and &#x27;Ingestor&#x27; roles to principal IDs at the database level, but the template does not limit the privileges or justify why these roles are necessary. There&#x27;s a risk of excessive permissions.</p>
                            <p><span class="label">Remediation:</span> Regularly review and limit privileged roles (such as &#x27;User&#x27; and &#x27;Ingestor&#x27;) assigned to only those identities strictly requiring them. Remove unnecessary assignments and follow the principle of least privilege.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-1</p>
                            <p><span class="label">File:</span> LacpBillingExhaust.Template.json</p>
                            <p><span class="label">Line:</span> 44</p>
                            <p><span class="label">Issue:</span> There is no explicit configuration ensuring encryption at rest for the Azure Data Explorer (ADX) cluster or associated resources. While encryption at rest is enabled by default in Azure, best practice is to use customer-managed keys (CMK) if regulated or sensitive data is processed.</p>
                            <p><span class="label">Remediation:</span> If the cluster processes regulated or sensitive data, enable customer-managed keys (CMK) for encryption at rest by specifying &#x27;keyVaultProperties&#x27; in the ADX cluster resource. Otherwise, document that default Microsoft-managed encryption is used.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> LacpBillingExhaust.Template.json</p>
                            <p><span class="label">Line:</span> 123</p>
                            <p><span class="label">Issue:</span> Sensitive group identity &#x27;aadgroup=<EMAIL>&#x27; is assigned in-script for database access. Disclosure of this setting may expose sensitive details such as internal group names or email addresses.</p>
                            <p><span class="label">Remediation:</span> Do not expose sensitive group or identity details in code. If necessary, reference identities using Azure AD object IDs, and store sensitive settings in a secure parameters file or Key Vault.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> LacpBillingExhaustExport.Template.json</p>
                            <p><span class="label">Line:</span> 57</p>
                            <p><span class="label">Issue:</span> The template provisions data exports to an Azure Data Explorer (ADX) cluster but does not specify any network security configuration. Without explicit controls for VNET integration or private endpoints, these resources or their data transfers might be exposed over public endpoints.</p>
                            <p><span class="label">Remediation:</span> Ensure that ADX clusters and associated resources are configured with private endpoints or VNET integration, and restrict public network access. Update deployment templates to optionally accept subnet and private endpoint configuration parameters.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-1</p>
                            <p><span class="label">File:</span> LacpBillingExhaustExport.Template.json</p>
                            <p><span class="label">Line:</span> 57</p>
                            <p><span class="label">Issue:</span> The template does not explicitly enforce encryption settings for data exports or at-rest storage in ADX or intermediate Azure resources. Without explicit settings, encryption defaults may be used but customer-managed keys (CMK) or advanced encryption options are not enforced.</p>
                            <p><span class="label">Remediation:</span> Update resource configuration to specify encryption settings explicitly. If ADX or related storage is used, document and ensure that at-rest encryption is enabled per resource, and use customer-managed keys where applicable.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> IM-6</p>
                            <p><span class="label">File:</span> LacpGeo.Template.json</p>
                            <p><span class="label">Line:</span> 143</p>
                            <p><span class="label">Issue:</span> The Key Vault uses access policies instead of Azure RBAC, which does not align with the latest RBAC model and best practices for centralized and scalable access management.</p>
                            <p><span class="label">Remediation:</span> Transition to using Azure RBAC for Key Vault to manage access at the Azure AD role level. Set &#x27;enableRbacAuthorization&#x27;: true in the Key Vault properties and migrate access control to role assignments.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> AM-1</p>
                            <p><span class="label">File:</span> LacpGeo.Template.json</p>
                            <p><span class="label">Line:</span> 146</p>
                            <p><span class="label">Issue:</span> Key Vault access policies grant both &#x27;Get&#x27; and &#x27;List&#x27; permissions for keys and secrets to multiple principals. Granting &#x27;List&#x27; is broader than usually required, and least-privilege principle recommends only providing required access.</p>
                            <p><span class="label">Remediation:</span> Review and restrict Key Vault access policies to only those permissions needed by each principal. Remove &#x27;List&#x27; permissions unless explicitly required.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-3</p>
                            <p><span class="label">File:</span> LacpGlobal.Template.json</p>
                            <p><span class="label">Line:</span> 193</p>
                            <p><span class="label">Issue:</span> There is no evidence of Network Security Groups (NSGs) being implemented for the storage accounts to control access at the network layer.</p>
                            <p><span class="label">Remediation:</span> Deploy NSGs with restrictive inbound and outbound rules on any subnet associated with storage accounts to enforce least privilege and network segmentation. Reference: ASB NS-3.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> AM-1</p>
                            <p><span class="label">File:</span> LacpGlobal.Template.json</p>
                            <p><span class="label">Line:</span> 242</p>
                            <p><span class="label">Issue:</span> Key Vault access policies for some principals grant broad permissions including &#x27;Get&#x27;, &#x27;List&#x27;, and for some, &#x27;Set&#x27;, &#x27;Delete&#x27;, &#x27;Recover&#x27;, &#x27;Backup&#x27;, and &#x27;Restore&#x27; to keys and secrets. This may not follow the least privilege principle.</p>
                            <p><span class="label">Remediation:</span> Review and minimize Key Vault access policy permissions. Only grant each identity the specific permissions required for its role. For example, service principals that only retrieve secrets should not have &#x27;Set&#x27;, &#x27;Delete&#x27;, &#x27;Recover&#x27;, or management rights. Reference: ASB AM-1.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> LacpGlobal.Template.json</p>
                            <p><span class="label">Line:</span> 456</p>
                            <p><span class="label">Issue:</span> Cosmos DB primary master key and Storage Account connection strings, which are sensitive secrets, are programmatically stored into Key Vault via inline template code. If template source control or deployment logs are not properly protected, secrets exposure is possible.</p>
                            <p><span class="label">Remediation:</span> Ensure template files are not stored in public or insecure repositories, and deployment logs/output are securely protected and not exposing the key/secret values. Consider using secure deployment practices, not exposing returned values in logs, and rotating credentials after automation runs. Reference: ASB DP-3.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-2</p>
                            <p><span class="label">File:</span> LacpRegion.Parameters-LacpRegionResources.json</p>
                            <p><span class="label">Line:</span> 39</p>
                            <p><span class="label">Issue:</span> The parameter &#x27;minimalCosmosDbTlsVersion&#x27; is set to &#x27;Tls12&#x27;, which is currently acceptable. However, there is no evidence in this parameter file that TLS enforcement settings are applied to all relevant endpoints, e.g., for Logic Apps, Storage Accounts, or other data resources (besides Cosmos DB).</p>
                            <p><span class="label">Remediation:</span> Explicitly enforce TLS 1.2 or higher for all relevant resources (e.g., Logic Apps, Storage Accounts, Event Hubs) at resource configuration level in your deployment templates and policy. Periodically review supported TLS versions to phase out weaker protocols.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> IM-8</p>
                            <p><span class="label">File:</span> LacpRegion.Parameters-LacpRegionResources.json</p>
                            <p><span class="label">Line:</span> 7</p>
                            <p><span class="label">Issue:</span> The deployment parameters reference service principals directly (e.g., &#x27;lacpAadServicePrincipal&#x27;, &#x27;ev2BuildoutAppObjectId&#x27;, &#x27;cosmosDbEsgPrincipalId&#x27;, &#x27;azureDeployAppId&#x27;). Managed Identities should be used for Logic Apps for secure and automatic identity management instead of direct use of service principals or their object IDs.</p>
                            <p><span class="label">Remediation:</span> Configure Logic Apps and supporting resources to use System-Assigned or User-Assigned Managed Identities instead of embedding service principal object IDs. Assign required permissions using managed identities as per the Least Privilege principle.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-1</p>
                            <p><span class="label">File:</span> LacpRegion.Template.json</p>
                            <p><span class="label">Line:</span> 666</p>
                            <p><span class="label">Issue:</span> Storage account resources do not specify customer-managed keys (CMKs) for encryption at rest; only default encryption is assumed.</p>
                            <p><span class="label">Remediation:</span> Enable customer-managed keys (CMK) for storage accounts by specifying &#x27;encryption.keySource&#x27; as &#x27;Microsoft.Keyvault&#x27; and referencing an Azure Key Vault. This is recommended for highly sensitive data.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> AM-1</p>
                            <p><span class="label">File:</span> LacpRegion.Template.json</p>
                            <p><span class="label">Line:</span> 84</p>
                            <p><span class="label">Issue:</span> Role assignments for storage/data access (e.g., StorageBlobDataContributor, StorageQueueDataContributor, StorageTableDataContributor) are applied to a managed identity at the resource group or subscription level (based on variables), which might exceed the least-privilege principle if not properly scoped.</p>
                            <p><span class="label">Remediation:</span> Review and restrict the scope of role assignments so they apply to the minimum necessary resource (e.g., only to the specific storage account). Remove unused roles and regularly audit assignments.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> LacpStamp.Parameters-LacpStampResources.json</p>
                            <p><span class="label">Line:</span> 80</p>
                            <p><span class="label">Issue:</span> The parameter &#x27;globalKeyVaultName&#x27; contains an explicit Key Vault resource name. While this itself is not immediately a secret, referencing Key Vault names in clear text parameter files can aid attackers in targeting or reconnaissance.</p>
                            <p><span class="label">Remediation:</span> Consider using environment-specific variables and obscured resource naming strategies. If scripts or deployments require the Key Vault name, retrieve it securely within CI/CD pipelines rather than exposing it in parameter files.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-3</p>
                            <p><span class="label">File:</span> LacpStamp.Template.json</p>
                            <p><span class="label">Line:</span> 605</p>
                            <p><span class="label">Issue:</span> There are no Network Security Groups (NSGs) or subnet assignments present for the storage accounts, which means traffic is not controlled at the network layer.</p>
                            <p><span class="label">Remediation:</span> Deploy storage accounts within subnets that are protected by NSGs configured with least-privilege rules, or, where Private Endpoints are used, ensure the private endpoint subnet is protected by an NSG.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-1</p>
                            <p><span class="label">File:</span> LacpStamp.Template.json</p>
                            <p><span class="label">Line:</span> 605</p>
                            <p><span class="label">Issue:</span> There is no explicit configuration for enabling customer-managed keys (CMK) for encryption at rest in storage accounts, which could limit compliance with strict data protection requirements.</p>
                            <p><span class="label">Remediation:</span> Where required by policy, enable encryption with customer-managed keys by setting up &#x27;encryption&#x27; block in the storage account properties with appropriate keyVaultUri and keyName.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> IM-1</p>
                            <p><span class="label">File:</span> LacpStamp.Template.json</p>
                            <p><span class="label">Line:</span> 605</p>
                            <p><span class="label">Issue:</span> Storage account access is not integrated with Azure AD for data plane operations (e.g., RBAC or Azure AD authentication for blobs/queues/tables/files is not enabled), and access keys are distributed via Key Vault.</p>
                            <p><span class="label">Remediation:</span> Enable Azure Active Directory authentication for storage services (blobs, queues, tables) by setting &#x27;AzureADAuthentication&#x27; where possible, and grant access via RBAC instead of distributing account keys.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> AM-1</p>
                            <p><span class="label">File:</span> LacpStamp.Template.json</p>
                            <p><span class="label">Line:</span> 358</p>
                            <p><span class="label">Issue:</span> The Key Vault access policies and Role Assignments in the template grant extensive permissions to several identities, including &#x27;lacpAadObjectId&#x27;, with permissions such as &#x27;Get&#x27;, &#x27;List&#x27;, &#x27;Update&#x27;, &#x27;Create&#x27;, &#x27;Import&#x27;, &#x27;Delete&#x27;, and &#x27;Recover&#x27; for keys and secrets. This may violate the least privilege principle if those permissions are not strictly necessary for operational requirements.</p>
                            <p><span class="label">Remediation:</span> Review all Role Assignments and Key Vault access policies; grant only the minimum set of permissions required for each principal to perform its function. Limit destructive operations (e.g., &#x27;Delete&#x27;, &#x27;Purge&#x27;) to trusted administrators.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> IM-8</p>
                            <p><span class="label">File:</span> ReadAdxExhaust.Template.json</p>
                            <p><span class="label">Line:</span> 17</p>
                            <p><span class="label">Issue:</span> The template does not provision a managed identity for the Kusto cluster resource. Managed identities are recommended for secure authentication between Azure resources, reducing the risk associated with credentials in code.</p>
                            <p><span class="label">Remediation:</span> Add a managed identity configuration to the Kusto cluster by including an `identity` block in the resource definition. Assign required permissions to the managed identity via Azure RBAC.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-1</p>
                            <p><span class="label">File:</span> ReadAdxExhaust.Template.json</p>
                            <p><span class="label">Line:</span> 17</p>
                            <p><span class="label">Issue:</span> There is no explicit configuration for encryption at rest for the Kusto cluster. By default, Kusto uses platform-managed keys, but customer-managed keys (CMK) are recommended for greater control and compliance.</p>
                            <p><span class="label">Remediation:</span> Enable customer-managed keys for encryption at rest by specifying the `keyVaultProperties` property referencing an Azure Key Vault and Key. Ensure Key Vault is properly secured.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> IM-8</p>
                            <p><span class="label">File:</span> ReadUsageAccount.Template.json</p>
                            <p><span class="label">Line:</span> 34</p>
                            <p><span class="label">Issue:</span> The Logic App deployment outputs a reference to a system-assigned managed identity principal ID, but there is no &#x27;identity&#x27; property assigned to the &#x27;Microsoft.UsageBilling/accounts&#x27; resource itself. This implies that managed identity is referenced but not actually enabled, which prevents secure resource-to-resource authentication using Managed Identity as recommended by the benchmark.</p>
                            <p><span class="label">Remediation:</span> Add an &#x27;identity&#x27; block with &#x27;type&#x27;: &#x27;SystemAssigned&#x27; to the resource definition for &#x27;Microsoft.UsageBilling/accounts&#x27; to enable a managed identity. Remove the output if the managed identity is not intended or ensure it is configured if required.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> IM-5</p>
                            <p><span class="label">File:</span> RoleAssignment.Template.json</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> Role assignments are being created, but there is no evidence in this template that identity or role assignment activities are being logged for audit and security purposes (e.g., via diagnostic settings).</p>
                            <p><span class="label">Remediation:</span> Enable and configure Azure Activity Logs or resource diagnostic settings to capture and monitor identity and access changes, including role assignments. Regularly review logs for unusual activity.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> AM-3</p>
                            <p><span class="label">File:</span> RoleAssignment.Template.json</p>
                            <p><span class="label">Line:</span> 60</p>
                            <p><span class="label">Issue:</span> Privileged role assignments (such as &#x27;Contributor&#x27;) are not integrated with Azure Privileged Identity Management (PIM) in this template. This increases the risk of permanent privileged access.</p>
                            <p><span class="label">Remediation:</span> Where possible, use Azure Privileged Identity Management (PIM) to make privileged role assignments (like Contributor) eligible and time-bound rather than permanent.</p>
                        </div></div>
                    <div class="severity-group">
                        <div class="severity-header low">🔵 LOW Severity Findings</div>
                    
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-1</p>
                            <p><span class="label">File:</span> LacpBilling.Template.json</p>
                            <p><span class="label">Line:</span> 626</p>
                            <p><span class="label">Issue:</span> Customer-managed keys (CMK) for encryption at rest are not configured for storage accounts, only the default encryption-at-rest is implicitly used. For highly confidential workloads, using customer-managed keys is recommended over Microsoft-managed keys.</p>
                            <p><span class="label">Remediation:</span> Enable encryption using a customer-managed key by adding the &#x27;encryption&#x27; property with the relevant Key Vault or managed HSM key details to the storage account resource.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> LacpBilling.Template.json</p>
                            <p><span class="label">Line:</span> 0</p>
                            <p><span class="label">Issue:</span> There is no evidence that sensitive data (such as secrets, keys, passwords) is being stored in Azure Key Vault. The template does not attempt to use key vaults for secure storage of secrets or keys.</p>
                            <p><span class="label">Remediation:</span> Store any sensitive information required by the deployment, such as connection strings or secret data, in Azure Key Vault. Reference those secrets via secure parameters or access policies rather than inline or hardcoded within IaC.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-2</p>
                            <p><span class="label">File:</span> LacpBilling.Template.json</p>
                            <p><span class="label">Line:</span> 626</p>
                            <p><span class="label">Issue:</span> While the template enforces TLS1_2 for storage account minimum TLS version, it is critical to ensure that all communications to the storage account require encryption in transit and that older TLS versions are not allowed.</p>
                            <p><span class="label">Remediation:</span> Continue to enforce &#x27;minimumTlsVersion&#x27; as &#x27;TLS1_2&#x27; and consider periodically auditing TLS settings for all storage accounts to ensure legacy protocols are blocked.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-2</p>
                            <p><span class="label">File:</span> LacpBillingExhaust.Template.json</p>
                            <p><span class="label">Line:</span> 44</p>
                            <p><span class="label">Issue:</span> The template does not specify policies for enforcing TLS 1.2+ for data in transit to the Azure Data Explorer cluster, which may allow lower version TLS connections.</p>
                            <p><span class="label">Remediation:</span> Ensure all communications to Azure Data Explorer enforce TLS 1.2 or higher. If configurable, set allowed TLS versions to 1.2+ and update client services accordingly.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-6</p>
                            <p><span class="label">File:</span> LacpGeo.Template.json</p>
                            <p><span class="label">Line:</span> 244</p>
                            <p><span class="label">Issue:</span> Key Vault SKU is set to &#x27;Standard,&#x27; and there is no evidence of Customer-Managed Keys (CMK) or HSM-backed keys for Cosmos DB encryption. CMK is recommended for sensitive workloads requiring stricter data control.</p>
                            <p><span class="label">Remediation:</span> Consider using Key Vault Premium SKU for supporting HSM-protected keys, and enable Cosmos DB encryption with a customer-managed key stored in Key Vault if required by compliance or business needs.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-1</p>
                            <p><span class="label">File:</span> LacpGeo.Template.json</p>
                            <p><span class="label">Line:</span> 244</p>
                            <p><span class="label">Issue:</span> Cosmos DB encryption at rest is not explicitly configured using customer-managed keys (CMK); it defaults to Microsoft-managed keys. Use of CMK provides additional control over encryption keys for data at rest.</p>
                            <p><span class="label">Remediation:</span> Configure Cosmos DB to use customer-managed keys (CMK) stored in Azure Key Vault to encrypt data at rest according to your organization&#x27;s security and compliance policies.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-1</p>
                            <p><span class="label">File:</span> LacpGlobal.Template.json</p>
                            <p><span class="label">Line:</span> 193</p>
                            <p><span class="label">Issue:</span> Encryption at rest is not explicitly referenced for Storage Accounts, though Azure enables this by default. If custom keys or key management is in scope, this should be specified.</p>
                            <p><span class="label">Remediation:</span> Verify that customer-managed keys (CMK) are used for storage account encryption at rest where required for compliance. Reference: ASB DP-1.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-2</p>
                            <p><span class="label">File:</span> LacpGlobal.Template.json</p>
                            <p><span class="label">Line:</span> 201</p>
                            <p><span class="label">Issue:</span> The storage account specifies &#x27;minimumTlsVersion&#x27; as &#x27;TLS1_2&#x27; and &#x27;supportsHttpsTrafficOnly&#x27; as true, which aligns with encryption in transit best practices. No issue found, but ensure for all deployed storage accounts these are enforced.</p>
                            <p><span class="label">Remediation:</span> Continually enforce &#x27;minimumTlsVersion&#x27; &gt;= &#x27;TLS1_2&#x27; and &#x27;supportsHttpsTrafficOnly&#x27;: true for all storage accounts.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-2</p>
                            <p><span class="label">File:</span> LacpRegion.Template.json</p>
                            <p><span class="label">Line:</span> 678</p>
                            <p><span class="label">Issue:</span> All configured storage accounts have &#x27;minimumTlsVersion&#x27; set to &#x27;TLS1_2&#x27; and &#x27;supportsHttpsTrafficOnly&#x27; set to true, complying with best practices. No explicit violation found, but ensure all downstream services and clients connecting to storage enforce TLS 1.2+.</p>
                            <p><span class="label">Remediation:</span> Confirm all client applications connecting to storage accounts enforce TLS 1.2 or higher.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-2</p>
                            <p><span class="label">File:</span> LacpStamp.Template.json</p>
                            <p><span class="label">Line:</span> 609</p>
                            <p><span class="label">Issue:</span> The storage accounts explicitly set &#x27;minimumTlsVersion&#x27; to &#x27;TLS1_2&#x27; and &#x27;supportsHttpsTrafficOnly&#x27; to true, which is in line with best practice; no violation found in the provided configuration.</p>
                            <p><span class="label">Remediation:</span> None required. Continue to enforce TLS 1.2 or greater and HTTPS-only traffic.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> LacpStamp.Template.json</p>
                            <p><span class="label">Line:</span> 815</p>
                            <p><span class="label">Issue:</span> Sensitive connection strings and account keys for storage accounts are being stored in Azure Key Vault as secrets, which aligns with best practices for secret management.</p>
                            <p><span class="label">Remediation:</span> Ensure access to the Key Vault is tightly controlled and audited; periodically rotate secrets and review Key Vault access policies for least privilege.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-1</p>
                            <p><span class="label">File:</span> LacpStamp.Template.json</p>
                            <p><span class="label">Line:</span> 884</p>
                            <p><span class="label">Issue:</span> Azure Redis Cache resources do not explicitly specify customer-managed keys (CMK) for encryption. By default, Azure-managed keys are used, which meets baseline encryption requirements but may not satisfy stricter compliance needs.</p>
                            <p><span class="label">Remediation:</span> If regulatory or enterprise policy requires, configure Azure Cache for Redis with customer-managed keys by referencing a Key Vault-managed key.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> ReadAdxExhaust.Template.json</p>
                            <p><span class="label">Line:</span> 17</p>
                            <p><span class="label">Issue:</span> The template outputs the cluster URI and data ingestion URI, but does not handle sensitive data such as keys, passwords, or secrets directly in the template. However, it is important to ensure that outputs do not inadvertently expose access tokens or other secrets if parameters are extended in future.</p>
                            <p><span class="label">Remediation:</span> Review all parameter and output values to ensure sensitive information is managed via Azure Key Vault and never outputted or hardcoded in templates.</p>
                        </div></div>
            </body>
            </html>
            