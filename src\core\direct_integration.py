#!/usr/bin/env python3
"""
Direct integration script to add enhanced resource-control mappings to security_opt.py
"""

import logging
from pathlib import Path

logger = logging.getLogger(__name__)

def add_enhanced_mapper_import():
    """Add the enhanced mapper import to security_opt.py"""
    security_opt_path = Path("security_opt.py")
    
    try:
        with open(security_opt_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if import already exists
        if 'from enhanced_resource_control_mappings import EnhancedResourceControlMapper' in content:
            print("✅ Enhanced mapper import already exists")
            return True
        
        # Add import after existing imports
        import_line = 'from enhanced_resource_control_mappings import EnhancedResourceControlMapper\n'
        
        # Find a good place to insert the import (after other imports)
        lines = content.split('\n')
        insert_index = 0
        
        for i, line in enumerate(lines):
            if line.startswith('import ') or line.startswith('from '):
                insert_index = i + 1
        
        # Insert the import
        lines.insert(insert_index, import_line.strip())
        
        # Write back
        with open(security_opt_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        print("✅ Added enhanced mapper import")
        return True
        
    except Exception as e:
        logger.error(f"Error adding import: {e}")
        return False

def add_enhanced_mapper_initialization():
    """Add enhanced mapper initialization to the __init__ method"""
    security_opt_path = Path("security_opt.py")
    
    try:
        with open(security_opt_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if initialization already exists
        if 'self.enhanced_mapper = EnhancedResourceControlMapper()' in content:
            print("✅ Enhanced mapper initialization already exists")
            return True
        
        # Find the __init__ method and add initialization
        init_addition = '''        # Initialize enhanced resource-control mapper
        try:
            self.enhanced_mapper = EnhancedResourceControlMapper()
            logger.info("✅ Enhanced resource-control mapper initialized successfully")
        except Exception as e:
            logger.warning(f"⚠️ Enhanced mapper initialization failed: {e}")
            self.enhanced_mapper = None
        '''
        
        # Find where to insert (after self.benchmark_data = {})
        if 'self.benchmark_data = {}' in content:
            content = content.replace(
                'self.benchmark_data = {}',
                f'self.benchmark_data = {{}}\n{init_addition}'
            )
        else:
            # Find __init__ method and add at the end
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'def __init__(self' in line:
                    # Find the end of the __init__ method
                    indent_level = len(line) - len(line.lstrip())
                    for j in range(i + 1, len(lines)):
                        if lines[j].strip() and len(lines[j]) - len(lines[j].lstrip()) <= indent_level:
                            # Insert before this line
                            lines.insert(j, init_addition)
                            break
                    break
            content = '\n'.join(lines)
        
        # Write back
        with open(security_opt_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Added enhanced mapper initialization")
        return True
        
    except Exception as e:
        logger.error(f"Error adding initialization: {e}")
        return False

def create_enhanced_methods_file():
    """Create a separate file with enhanced methods that can be imported"""
    methods_content = '''"""
Enhanced methods for security_opt.py integration
"""

import logging
import re
from typing import Dict, List

logger = logging.getLogger(__name__)

def get_controls_for_resource_enhanced(self, resource_type: str) -> List[Dict]:
    """Get comprehensive ASB controls for Azure resource type using enhanced mappings."""
    try:
        if hasattr(self, 'enhanced_mapper') and self.enhanced_mapper:
            # Use enhanced mapper for comprehensive control coverage
            controls = self.enhanced_mapper.get_controls_for_resource_type(resource_type)
            
            # Convert to expected format and add additional details
            formatted_controls = []
            for control in controls:
                control_details = {
                    "id": control['id'],
                    "domain": control['domain'],
                    "name": control['name'],
                    "security_principle": control.get('security_principle', ''),
                    "azure_guidance": control.get('azure_guidance', ''),
                    "implementation_context": control.get('implementation_context', ''),
                    "stakeholders": control.get('stakeholders', ''),
                    "urls": control.get('urls', [])
                }
                formatted_controls.append(control_details)
            
            logger.debug(f"Enhanced mapper returned {len(formatted_controls)} controls for {resource_type}")
            return formatted_controls
        else:
            logger.warning("Enhanced mapper not available, falling back to legacy mappings")
            return self._get_legacy_controls_for_resource(resource_type)
            
    except Exception as e:
        logger.error(f"Error getting controls for resource {resource_type}: {e}")
        return self._get_legacy_controls_for_resource(resource_type)

def extract_control_links_enhanced(self, control_id: str) -> Dict:
    """Extract links and references from enhanced CSV data for a specific control."""
    try:
        if hasattr(self, 'enhanced_mapper') and self.enhanced_mapper:
            # Use enhanced mapper for comprehensive link extraction
            links_info = self.enhanced_mapper.get_control_links(control_id)
            
            # Ensure all required fields are present
            return {
                "formatted_links": links_info.get("formatted_links", ""),
                "azure_guidance": links_info.get("azure_guidance", ""),
                "implementation_context": links_info.get("implementation_context", ""),
                "raw_links": links_info.get("raw_links", [])
            }
        else:
            logger.warning("Enhanced mapper not available for link extraction")
            return self._legacy_extract_control_links(control_id)
            
    except Exception as e:
        logger.error(f"Error extracting links for control {control_id}: {e}")
        return {
            "formatted_links": "",
            "azure_guidance": "",
            "implementation_context": "",
            "raw_links": []
        }
'''
    
    try:
        with open("enhanced_security_methods.py", 'w', encoding='utf-8') as f:
            f.write(methods_content)
        
        print("✅ Created enhanced methods file")
        return True
        
    except Exception as e:
        logger.error(f"Error creating methods file: {e}")
        return False

def main():
    """Main integration function"""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    print("🔧 Direct Integration of Enhanced Resource-Control Mappings")
    print("=" * 60)
    
    success = True
    
    # Create backup
    try:
        security_opt_path = Path("security_opt.py")
        backup_path = Path("security_opt_backup.py")
        
        if security_opt_path.exists():
            with open(security_opt_path, 'r', encoding='utf-8') as f:
                content = f.read()
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print("✅ Backup created")
        
    except Exception as e:
        print(f"❌ Backup failed: {e}")
        return
    
    # Add import
    if not add_enhanced_mapper_import():
        print("❌ Failed to add import")
        success = False
    
    # Add initialization
    if not add_enhanced_mapper_initialization():
        print("❌ Failed to add initialization")
        success = False
    
    # Create enhanced methods file
    if not create_enhanced_methods_file():
        print("❌ Failed to create enhanced methods file")
        success = False
    
    if success:
        print("\n🎉 Direct integration completed successfully!")
        print("\nNext steps:")
        print("1. Test the updated security_opt.py")
        print("2. Manually replace get_controls_for_resource method if needed")
        print("3. Manually replace _extract_control_links method if needed")
        print("4. Use enhanced_security_methods.py as reference")
    else:
        print("\n❌ Integration failed")

if __name__ == "__main__":
    main()
