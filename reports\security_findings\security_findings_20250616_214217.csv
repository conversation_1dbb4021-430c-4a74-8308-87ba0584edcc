Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
CRITICAL,DP-3,Parameters\LacpStamp.Parameters-LacpStampResources.json,74,"Parameter 'dasStorageAccountKey' at line 74 contains a storage account key value reference, which is sensitive information. Storing or passing sensitive keys directly in parameters violates ASB DP-3: Manage sensitive information disclosure.",Store sensitive keys such as 'dasStorageAccountKey' in Azure Key Vault and reference them securely using Key Vault references in your template parameters. Remove direct key values from parameter files.,N/A,AI,Generic
CRITICAL,DP-4,Templates\IngestionStorageAccount.Template.json,38,Storage account resource does not specify 'encryption' property. Encryption at rest is not explicitly enabled as required by ASB DP-1.,"Add the 'encryption' property to the storage account resource with 'services', 'keySource', and 'enabled' set to true to ensure encryption at rest.",N/A,AI,Generic
CRITICAL,DP-4,Templates\IngestionStorageAccount.Template.json,61,Storage account resource does not specify 'encryption' property. Encryption at rest is not explicitly enabled as required by ASB DP-1.,"Add the 'encryption' property to the storage account resource with 'services', 'keySource', and 'enabled' set to true to ensure encryption at rest.",N/A,AI,Generic
CRITICAL,DP-6,Templates\IngestionStorageAccount.Template.json,38,Storage account resource does not specify use of Azure Key Vault for sensitive information or keys. ASB DP-3 requires sensitive data like keys to be stored in Azure Key Vault.,Configure the storage account to use Azure Key Vault for key management by specifying 'keyVaultProperties' under the 'encryption' property.,N/A,AI,Generic
CRITICAL,DP-6,Templates\IngestionStorageAccount.Template.json,61,Storage account resource does not specify use of Azure Key Vault for sensitive information or keys. ASB DP-3 requires sensitive data like keys to be stored in Azure Key Vault.,Configure the storage account to use Azure Key Vault for key management by specifying 'keyVaultProperties' under the 'encryption' property.,N/A,AI,Generic
CRITICAL,NS-1,Templates\IngestionStorageAccount.Template.json,38,Storage account resource does not specify network security groups (NSGs) or Azure Firewall. ASB NS-1 requires protection of storage accounts using NSGs or Azure Firewall.,"Restrict network access to the storage account by configuring network rules, NSGs, or Azure Firewall.",N/A,AI,Generic
CRITICAL,NS-1,Templates\IngestionStorageAccount.Template.json,61,Storage account resource does not specify network security groups (NSGs) or Azure Firewall. ASB NS-1 requires protection of storage accounts using NSGs or Azure Firewall.,"Restrict network access to the storage account by configuring network rules, NSGs, or Azure Firewall.",N/A,AI,Generic
CRITICAL,NS-2,Templates\IngestionStorageAccount.Template.json,38,Storage account resource does not restrict public network access. ASB NS-2 requires securing all public endpoints.,Set 'publicNetworkAccess' property to 'Disabled' or configure network rules to restrict public access.,N/A,AI,Generic
CRITICAL,NS-2,Templates\IngestionStorageAccount.Template.json,61,Storage account resource does not restrict public network access. ASB NS-2 requires securing all public endpoints.,Set 'publicNetworkAccess' property to 'Disabled' or configure network rules to restrict public access.,N/A,AI,Generic
CRITICAL,NS-1,Templates\LacpBilling.Template.json,670,"Microsoft.Storage/storageAccounts resource does not restrict network access using network security groups (NSGs), Azure Firewall, or private endpoints. No 'networkAcls' property is defined, so the storage account may be accessible from all networks.","Restrict network access to the storage account by configuring the 'networkAcls' property to allow only required subnets or private endpoints, and deny public network access.",N/A,AI,Generic
CRITICAL,DP-1,Templates\LacpBillingExhaust.Template.json,34,Microsoft.Kusto/clusters resource at line 34 does not specify 'encryption' or 'keyVaultProperties' for encryption at rest. Azure Security Benchmark DP-1 requires explicit configuration of encryption at rest.,Add the 'encryption' property to the Microsoft.Kusto/clusters resource and configure either service-managed or customer-managed keys for encryption at rest.,N/A,AI,Generic
CRITICAL,DP-6,Templates\LacpBillingExhaust.Template.json,34,Microsoft.Kusto/clusters resource at line 34 does not configure customer-managed keys (CMK) for data encryption. Azure Security Benchmark DP-6 recommends using CMK for sensitive data.,Configure the 'keyVaultProperties' property in the Microsoft.Kusto/clusters resource to use a customer-managed key from Azure Key Vault.,N/A,AI,Generic
CRITICAL,NS-1,Templates\LacpBillingExhaust.Template.json,34,Microsoft.Kusto/clusters resource at line 34 does not specify any network security controls such as network security groups (NSGs) or Azure Firewall. Azure Security Benchmark NS-1 requires protection of sensitive resources using NSGs or Azure Firewall.,Associate the Microsoft.Kusto/clusters resource with a subnet protected by a network security group (NSG) or configure Azure Firewall rules to restrict access.,N/A,AI,Generic
CRITICAL,NS-2,Templates\LacpBillingExhaust.Template.json,34,Microsoft.Kusto/clusters resource at line 34 does not restrict public endpoints or specify public network access settings. Azure Security Benchmark NS-2 requires securing all public endpoints.,Set the 'publicNetworkAccess' property to 'Disabled' or restrict access to trusted networks using IP rules or private endpoints.,N/A,AI,Generic
CRITICAL,DP-1,Templates\LacpBillingExhaustExport.Template.json,56,"The resource 'Microsoft.UsageBilling/accounts/dataExports' at line 56 does not specify encryption at rest for exported data. The template lacks explicit configuration for encryption of data at rest, violating ASB DP-1.",Configure the data export resource to enable encryption at rest. Ensure that the destination storage or data sink for exports is encrypted using Azure-managed or customer-managed keys.,N/A,AI,Generic
CRITICAL,DP-2,Templates\LacpBillingExhaustExport.Template.json,56,"The resource 'Microsoft.UsageBilling/accounts/dataExports' at line 56 does not specify encryption in transit (TLS 1.2+) for data transfers. The template lacks explicit configuration to enforce secure data transfer, violating ASB DP-2.",Ensure that all data exports use secure endpoints (HTTPS/TLS 1.2+) for data transfer. Explicitly configure the resource to require encryption in transit for all connections.,N/A,AI,Generic
CRITICAL,DP-3,Templates\LacpBillingExhaustExport.Template.json,56,"Sensitive connection information such as 'adxExhaustUri' and 'adxExhaustDataIngestionUri' are passed as plain string parameters at line 13 and 14, and referenced in the resource at line 56, without integration with Azure Key Vault. This violates ASB DP-3.",Store sensitive connection URIs in Azure Key Vault and reference them securely in the template using Key Vault references instead of plain string parameters.,N/A,AI,Generic
CRITICAL,NS-1,Templates\LacpGeo.Template.json,74,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 74 does not have network security groups (NSGs) or Azure Firewall protection. 'isVirtualNetworkFilterEnabled' is set to false and 'virtualNetworkRules' is empty, exposing the resource to potential network threats.",Enable 'isVirtualNetworkFilterEnabled' and configure 'virtualNetworkRules' to restrict access to trusted subnets or use Azure Firewall to protect the CosmosDB account.,N/A,AI,Generic
CRITICAL,NS-2,Templates\LacpGeo.Template.json,74,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 74 has 'publicNetworkAccess' set to 'Enabled', exposing the database to the public internet.",Set 'publicNetworkAccess' to 'Disabled' to prevent public access and use private endpoints or virtual network rules for secure access.,N/A,AI,Generic
CRITICAL,NS-1,Templates\LacpGlobal.Template.json,70,"CosmosDB account 'publicNetworkAccess' is set to 'Enabled' and 'isVirtualNetworkFilterEnabled' is set to false at line 70, exposing the resource to the public internet without network security groups or Azure Firewall.",Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to true. Restrict access using network security groups or Azure Firewall.,N/A,AI,Generic
CRITICAL,NS-2,Templates\LacpGlobal.Template.json,70,"CosmosDB account at line 70 has public network access enabled ('publicNetworkAccess': 'Enabled') and no IP or virtual network rules, exposing a public endpoint.",Disable public network access by setting 'publicNetworkAccess' to 'Disabled' and configure IP or virtual network rules to restrict access.,N/A,AI,Generic
CRITICAL,NS-1,Templates\LacpGlobal.Template.json,170,"Storage account at line 170 does not specify network rules, NSGs, or firewall restrictions, leaving it exposed to the public internet.",Configure network rules to restrict access to the storage account. Use NSGs or Azure Firewall to limit access to trusted networks.,N/A,AI,Generic
CRITICAL,NS-2,Templates\LacpGlobal.Template.json,170,"Storage account at line 170 does not restrict public endpoints, exposing the resource to the public internet.",Restrict public access by configuring network rules and disabling public endpoints for the storage account.,N/A,AI,Generic
CRITICAL,NS-1,Templates\LacpGlobal.Template.json,210,"Key Vault at line 210 does not specify network ACLs, NSGs, or firewall rules, leaving it accessible from the public internet.",Configure Key Vault network ACLs to restrict access to trusted networks. Use NSGs or Azure Firewall to limit access.,N/A,AI,Generic
CRITICAL,NS-2,Templates\LacpGlobal.Template.json,210,"Key Vault at line 210 does not restrict public endpoints, exposing the resource to the public internet.",Restrict public access by configuring network ACLs and disabling public endpoints for the Key Vault.,N/A,AI,Generic
CRITICAL,NS-1,Templates\LacpRegion.Template.json,1007,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1007 has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false, exposing the database to the public internet without network security controls.","Set 'publicNetworkAccess' to 'Disabled' and enable 'isVirtualNetworkFilterEnabled' with appropriate 'virtualNetworkRules' to restrict access to trusted networks only, as required by ASB NS-1.",N/A,AI,Generic
CRITICAL,NS-21,Templates\LacpRegion.Template.json,1007,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1007 exposes a public endpoint ('publicNetworkAccess': 'Enabled'), increasing risk of unauthorized access.","Set 'publicNetworkAccess' to 'Disabled' to remove the public endpoint, or restrict access using network rules, as required by ASB NS-21.",N/A,AI,Generic
CRITICAL,DP-7,Templates\LacpStamp.Template.json,1102,"The Key Vault secret '[concat(variables('stampSharedKeyVaultName'), '/', parameters('dasStorageAccountName'),'-key')]' stores a sensitive storage account key directly from the parameter 'dasStorageAccountKey'. This violates the requirement to avoid exposing sensitive information in parameters or template files.","Remove the direct use of sensitive storage account keys in parameters. Instead, use secure deployment mechanisms such as referencing a Key Vault secret or using managed identities to retrieve secrets at runtime. Ensure sensitive values are never hardcoded or passed as parameters.",N/A,AI,Generic
CRITICAL,NS-1,Templates\LacpStamp.Template.json,563,"The storage account '[variables('tipLACPStampStorageAccountName')]' does not specify network rules, NSGs, or firewall restrictions. This exposes the resource to potential unauthorized access.","Configure network rules for the storage account to restrict access. Use 'networkAcls' to allow only trusted subnets, private endpoints, or specific IP ranges. Apply NSGs or Azure Firewall as appropriate.",N/A,AI,Generic
CRITICAL,NS-1,Templates\LacpStamp.Template.json,601,"The storage accounts '[variables('storageAccounts')[copyIndex()].name]' do not specify network rules, NSGs, or firewall restrictions. This exposes the resources to potential unauthorized access.","Configure network rules for each storage account to restrict access. Use 'networkAcls' to allow only trusted subnets, private endpoints, or specific IP ranges. Apply NSGs or Azure Firewall as appropriate.",N/A,AI,Generic
CRITICAL,NS-1,Templates\LacpStamp.Template.json,655,"The shim storage accounts '[variables('shimStorageAccounts')[copyIndex()].name]' do not specify network rules, NSGs, or firewall restrictions. This exposes the resources to potential unauthorized access.","Configure network rules for each shim storage account to restrict access. Use 'networkAcls' to allow only trusted subnets, private endpoints, or specific IP ranges. Apply NSGs or Azure Firewall as appropriate.",N/A,AI,Generic
CRITICAL,NS-21,Templates\LacpStamp.Template.json,563,"The storage account '[variables('tipLACPStampStorageAccountName')]' is created without private endpoints or network restrictions, resulting in a public endpoint exposure.",Add a private endpoint to the storage account and restrict public network access by setting 'publicNetworkAccess' to 'Disabled'.,N/A,AI,Generic
CRITICAL,NS-21,Templates\LacpStamp.Template.json,601,"The storage accounts '[variables('storageAccounts')[copyIndex()].name]' are created without private endpoints or network restrictions, resulting in public endpoint exposure.",Add private endpoints to each storage account and restrict public network access by setting 'publicNetworkAccess' to 'Disabled'.,N/A,AI,Generic
CRITICAL,NS-21,Templates\LacpStamp.Template.json,655,"The shim storage accounts '[variables('shimStorageAccounts')[copyIndex()].name]' are created without private endpoints or network restrictions, resulting in public endpoint exposure.",Add private endpoints to each shim storage account and restrict public network access by setting 'publicNetworkAccess' to 'Disabled'.,N/A,AI,Generic
CRITICAL,DP-1,Templates\ReadAdxExhaust.Template.json,23,Microsoft.Kusto/clusters resource does not specify 'encryption' or 'keyVaultProperties' for encryption at rest. Encryption at rest is not explicitly enabled or configured.,Add the 'encryption' property to the Microsoft.Kusto/clusters resource and configure either service-managed or customer-managed keys to ensure encryption at rest.,N/A,AI,Generic
CRITICAL,DP-6,Templates\ReadAdxExhaust.Template.json,23,Microsoft.Kusto/clusters resource does not reference Azure Key Vault for managing sensitive information such as keys. No 'keyVaultProperties' or integration with Key Vault is present.,Configure the 'keyVaultProperties' property in the Microsoft.Kusto/clusters resource to store and manage sensitive keys in Azure Key Vault.,N/A,AI,Generic
CRITICAL,NS-1,Templates\ReadAdxExhaust.Template.json,23,Microsoft.Kusto/clusters resource does not specify any network security controls such as network security groups (NSGs) or Azure Firewall. No 'virtualNetworkConfiguration' or firewall rules are present.,Add 'virtualNetworkConfiguration' and/or configure firewall rules to restrict network access to the Microsoft.Kusto/clusters resource.,N/A,AI,Generic
CRITICAL,NS-2,Templates\ReadAdxExhaust.Template.json,23,Microsoft.Kusto/clusters resource does not restrict public endpoints. No configuration is present to disable or secure public network access.,Configure the Microsoft.Kusto/clusters resource to disable public network access or restrict it using firewall rules and private endpoints.,N/A,AI,Generic
CRITICAL,NS-15,Templates\TrafficManagerEndpoints.Template.json,38,"The resource 'Microsoft.Network/trafficManagerProfiles/externalEndpoints' at line 38 defines a public endpoint without explicit security controls such as network security groups (NSGs), Azure Firewall, or private endpoints. This exposes the endpoint to the public internet, violating ASB control NS-15 (Protect public endpoints).","Restrict public access to the external endpoint by implementing network security controls such as NSGs, Azure Firewall, or by using private endpoints. Ensure only authorized traffic can reach the endpoint.",N/A,AI,Generic
HIGH,NS-5,Templates\IngestionStorageAccount.Template.json,38,Storage account resource does not configure private endpoints. ASB NS-5 recommends using private endpoints for secure access.,"Configure a private endpoint for the storage account to ensure secure, private connectivity.",N/A,AI,Generic
HIGH,NS-5,Templates\IngestionStorageAccount.Template.json,61,Storage account resource does not configure private endpoints. ASB NS-5 recommends using private endpoints for secure access.,"Configure a private endpoint for the storage account to ensure secure, private connectivity.",N/A,AI,Generic
HIGH,NS-5,Templates\LacpBilling.Template.json,670,"Microsoft.Storage/storageAccounts resource does not implement private endpoints. No private endpoint configuration is present, increasing exposure risk.",Create and associate a private endpoint for the storage account to ensure access is only possible from within your private network.,N/A,AI,Generic
HIGH,NS-5,Templates\LacpBillingExhaust.Template.json,34,Microsoft.Kusto/clusters resource at line 34 does not configure private endpoints. Azure Security Benchmark NS-5 recommends using private endpoints for secure access.,"Configure a private endpoint for the Microsoft.Kusto/clusters resource to ensure secure, private connectivity.",N/A,AI,Generic
HIGH,NS-5,Templates\LacpGeo.Template.json,74,CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 74 does not use private endpoints. 'publicNetworkAccess' is 'Enabled' and no private endpoint configuration is present.,"Configure a private endpoint for the CosmosDB account to ensure secure, private connectivity from within your virtual network.",N/A,AI,Generic
HIGH,NS-5,Templates\LacpGlobal.Template.json,70,"CosmosDB account at line 70 does not use private endpoints, increasing exposure risk.",Implement private endpoints for the CosmosDB account to restrict access to trusted networks only.,N/A,AI,Generic
HIGH,NS-5,Templates\LacpGlobal.Template.json,170,"Storage account at line 170 does not use private endpoints, increasing exposure risk.",Implement private endpoints for the storage account to restrict access to trusted networks only.,N/A,AI,Generic
HIGH,NS-5,Templates\LacpGlobal.Template.json,210,"Key Vault at line 210 does not use private endpoints, increasing exposure risk.",Implement private endpoints for the Key Vault to restrict access to trusted networks only.,N/A,AI,Generic
HIGH,NS-23,Templates\LacpRegion.Template.json,1007,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1007 does not use private endpoints ('publicNetworkAccess': 'Enabled', 'isVirtualNetworkFilterEnabled': false), missing recommended network isolation.","Implement private endpoints for the CosmosDB account and set 'publicNetworkAccess' to 'Disabled' to ensure secure, private connectivity as required by ASB NS-23.",N/A,AI,Generic
HIGH,NS-24,Templates\LacpStamp.Template.json,563,The storage account '[variables('tipLACPStampStorageAccountName')]' does not use private endpoints for secure access.,"Configure a private endpoint for the storage account to ensure secure, private connectivity.",N/A,AI,Generic
HIGH,NS-24,Templates\LacpStamp.Template.json,601,The storage accounts '[variables('storageAccounts')[copyIndex()].name]' do not use private endpoints for secure access.,"Configure private endpoints for each storage account to ensure secure, private connectivity.",N/A,AI,Generic
HIGH,NS-24,Templates\LacpStamp.Template.json,655,The shim storage accounts '[variables('shimStorageAccounts')[copyIndex()].name]' do not use private endpoints for secure access.,"Configure private endpoints for each shim storage account to ensure secure, private connectivity.",N/A,AI,Generic
HIGH,NS-5,Templates\ReadAdxExhaust.Template.json,23,Microsoft.Kusto/clusters resource does not use private endpoints for secure access. No 'privateEndpointConnections' or related configuration is present.,"Add a private endpoint connection to the Microsoft.Kusto/clusters resource to ensure secure, private access.",N/A,AI,Generic
HIGH,AM-1,Templates\RoleAssignment.Template.json,38,"Role assignment at line 38 grants 'Contributor' role to the 'Ev2BuildoutServicePrincipalId' principal. The Contributor role provides broad permissions, which may exceed least privilege requirements.","Review the permissions required by the 'Ev2BuildoutServicePrincipalId' principal and assign a custom role or a built-in role with only the necessary permissions, following the principle of least privilege as per ASB AM-1.",N/A,AI,Generic
MEDIUM,DP-7,Templates\IngestionStorageAccount.Template.json,38,Storage account resource does not specify customer-managed keys (CMK) for encryption. ASB DP-6 recommends securing data with CMK.,Configure the storage account to use customer-managed keys by specifying 'keyVaultProperties' and referencing a Key Vault key.,N/A,AI,Generic
MEDIUM,DP-7,Templates\IngestionStorageAccount.Template.json,61,Storage account resource does not specify customer-managed keys (CMK) for encryption. ASB DP-6 recommends securing data with CMK.,Configure the storage account to use customer-managed keys by specifying 'keyVaultProperties' and referencing a Key Vault key.,N/A,AI,Generic
MEDIUM,DP-8,Templates\LacpBilling.Template.json,670,Microsoft.Storage/storageAccounts resource does not specify customer-managed keys (CMK) for encryption at rest. Only platform-managed keys are implied.,Configure the storage account to use customer-managed keys (CMK) for encryption at rest by adding the 'encryption.keySource' property set to 'Microsoft.Keyvault' and specifying the key vault and key URI.,N/A,AI,Generic
MEDIUM,DP-7,Templates\ReadAdxExhaust.Template.json,23,Microsoft.Kusto/clusters resource does not use customer-managed keys (CMK) for encryption. Only default encryption is implied.,Update the Microsoft.Kusto/clusters resource to use customer-managed keys by specifying the 'keyVaultProperties' with a valid Key Vault key URI.,N/A,AI,Generic
MEDIUM,IM-8,Templates\ReadUsageAccount.Template.json,19,The resource 'Microsoft.UsageBilling/accounts' does not explicitly configure a managed identity. ASB IM-8 recommends using managed identities for secure resource-to-resource authentication.,Add the 'identity' property with 'type': 'SystemAssigned' to the resource definition to enable a managed identity.,N/A,AI,Generic
