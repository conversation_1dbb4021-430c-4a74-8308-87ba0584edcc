#!/usr/bin/env python3
"""
Get Azure Security Benchmark controls for a specific resource type.
Usage: python get_security_controls.py <resource_type>
"""

import sys
import os

# Add current directory to path
sys.path.append('.')

def get_controls(resource_type):
    """Get security controls for a resource type."""
    try:
        from security_opt import SecurityPRReviewer
        
        # Set environment variables for optimal analysis
        os.environ.setdefault('ENFORCE_DOMAIN_PRIORITY', 'true')
        os.environ.setdefault('USE_OPTIMIZED_PROMPTS', 'true')
        os.environ.setdefault('ANALYSIS_SEED', '42')
        
        reviewer = SecurityPRReviewer(local_folder='.')
        controls = reviewer._find_relevant_controls(resource_type)
        
        print(f'\n🛡️ AZURE SECURITY CONTROLS FOR {resource_type.upper()}')
        print('='*60)
        
        if not controls:
            print(f'❌ No controls found for resource type: {resource_type}')
            print('\n💡 Available resource types: Storage, Network, Compute, Database, Web, etc.')
            return
        
        # Group controls by domain for better organization
        by_domain = {}
        for control in controls:
            domain = control.get('domain', 'Unknown')
            if domain not in by_domain:
                by_domain[domain] = []
            by_domain[domain].append(control)
        
        # Display controls by domain
        for domain, domain_controls in by_domain.items():
            if domain != 'Unknown':
                print(f'\n🏷️ {domain}')
                print('-' * 40)
            
            for c in domain_controls[:5]:  # Show first 5 per domain
                control_id = c.get("id", "N/A")
                name = c.get("name", "N/A")
                description = c.get("description", "N/A")
                severity = c.get("severity", "N/A")
                
                # Severity emoji
                severity_emoji = {
                    "CRITICAL": "🔴",
                    "HIGH": "🟠", 
                    "MEDIUM": "🟡",
                    "LOW": "🔵"
                }.get(severity, "⚪")
                
                print(f'📋 {control_id}: {name}')
                if severity != "N/A":
                    print(f'   {severity_emoji} Severity: {severity}')
                print(f'   📝 {description[:120]}...')
                print()
        
        print(f'📊 Total Controls Found: {len(controls)}')
        
        # Show domain distribution
        if len(by_domain) > 1:
            print(f'\n📈 Domain Distribution:')
            for domain, domain_controls in by_domain.items():
                if domain != 'Unknown':
                    print(f'   • {domain}: {len(domain_controls)} controls')
        
        print(f'\n💡 Use these controls as guidance for securing your {resource_type} resources')
        
    except ImportError as e:
        print(f'❌ Import error: {e}')
        print('Please ensure you are in the IaC Guardian directory')
    except Exception as e:
        print(f'❌ Error getting controls: {e}')

def main():
    """Main function."""
    if len(sys.argv) != 2:
        print('Usage: python get_security_controls.py <resource_type>')
        print('Examples:')
        print('  python get_security_controls.py Storage')
        print('  python get_security_controls.py Network')
        print('  python get_security_controls.py Compute')
        sys.exit(1)
    
    resource_type = sys.argv[1]
    get_controls(resource_type)

if __name__ == "__main__":
    main()
