Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,template.json,120,"App Service 'onefuzz-daily-ui' is not protected by network security groups (NSGs) or Azure Firewall, and allows unrestricted public access.",Protect the App Service with NSGs or Azure Firewall to restrict access to only trusted networks and IP addresses.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,120,"App Service 'onefuzz-daily-ui' allows public network access ('publicNetworkAccess': 'Enabled') and has 'ipSecurityRestrictions' with 'ipAddress': 'Any', 'action': 'Allow', which exposes the app to the public internet without restriction.",Restrict 'ipSecurityRestrictions' to specific IP ranges or private networks. Set 'publicNetworkAccess' to 'Disabled' or configure access restrictions to limit exposure.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,134,"App Service 'onefuzz-daily-ui' SCM endpoint allows public network access ('scmIpSecurityRestrictions' with 'ipAddress': 'Any', 'action': 'Allow'), exposing the SCM endpoint to the public internet.",Restrict 'scmIpSecurityRestrictions' to specific IP ranges or private networks. Do not allow unrestricted public access to SCM endpoints.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-3,template.json,120,App Service 'onefuzz-daily-ui' does not implement Network Security Groups (NSGs) to control inbound and outbound traffic.,Implement NSGs to restrict inbound and outbound traffic to the App Service to only required sources and destinations.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,120,"App Service 'onefuzz-daily-ui' does not use private endpoints and allows public network access, increasing exposure risk.",Implement Azure Private Endpoints for the App Service to restrict access to private networks only.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,120,App Service 'onefuzz-daily-ui' does not specify customer-managed keys or explicit encryption at rest configuration.,Enable encryption at rest using Azure-managed or customer-managed keys for all data storage associated with the App Service.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,54,"App Service 'onefuzz-daily-ui' has 'sslState' set to 'Disabled' for hostName 'onefuzz-daily-ui.azurewebsites.net', which allows unencrypted HTTP traffic. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates entries to enforce HTTPS. Ensure all endpoints require TLS 1.2 or higher.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,61,"App Service 'onefuzz-daily-ui' has 'sslState' set to 'Disabled' for hostName 'onefuzz-daily-ui.scm.azurewebsites.net', which allows unencrypted HTTP traffic. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates entries to enforce HTTPS. Ensure all endpoints require TLS 1.2 or higher.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,120,"App Service 'onefuzz-daily-ui' does not reference Azure Key Vault for application secrets or sensitive configuration, risking sensitive information disclosure.",Store all application secrets and sensitive configuration in Azure Key Vault and reference them securely from the App Service.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-6,template.json,120,App Service 'onefuzz-daily-ui' does not use customer-managed keys (CMK) for encryption at rest.,Configure the App Service to use customer-managed keys for encryption at rest if handling sensitive or regulated data.,N/A,AI,Generic
