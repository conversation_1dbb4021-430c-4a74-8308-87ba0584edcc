<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Assessment Report - IaC Guardian</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            /* Modern Color Palette */
            --primary-blue: #1e40af;
            --primary-blue-light: #3b82f6;
            --secondary-blue: #0ea5e9;
            --accent-purple: #8b5cf6;
            --success-green: #10b981;
            --warning-amber: #f59e0b;
            --danger-red: #ef4444;
            --info-cyan: #06b6d4;

            /* Neutral Colors */
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;

            /* Semantic Colors */
            --critical-bg: #fef2f2;
            --critical-border: #fecaca;
            --critical-text: #dc2626;
            --high-bg: #fffbeb;
            --high-border: #fed7aa;
            --high-text: #ea580c;
            --medium-bg: #fefce8;
            --medium-border: #fde68a;
            --medium-text: #ca8a04;
            --low-bg: #f0f9ff;
            --low-border: #bae6fd;
            --low-text: #0284c7;

            /* Layout */
            --max-width: 1400px;
            --border-radius: 12px;
            --border-radius-sm: 8px;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--gray-700);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-size: 14px;
        }

        .main-container {
            max-width: var(--max-width);
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        /* Header Section */
        .report-header {
            background: white;
            border-radius: var(--border-radius);
            padding: 3rem 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-lg);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .report-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue), var(--accent-purple));
        }

        .report-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
        }

        .report-subtitle {
            font-size: 1.125rem;
            color: var(--gray-600);
            font-weight: 400;
            margin-bottom: 1rem;
        }

        .report-meta {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 1.5rem;
            font-size: 0.875rem;
            color: var(--gray-500);
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* Controls Section */
        .controls-section {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }

        .controls-grid {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 1.5rem;
            align-items: center;
        }

        .search-container {
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius-sm);
            font-size: 0.875rem;
            transition: all 0.2s ease;
            background: var(--gray-50);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
        }

        .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
        }

        .filter-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 0.5rem 1rem;
            border: 2px solid transparent;
            border-radius: 2rem;
            background: var(--gray-100);
            color: var(--gray-600);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            white-space: nowrap;
        }

        .filter-btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .filter-btn.active {
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .filter-btn.all.active { background: var(--primary-blue); }
        .filter-btn.critical.active { background: var(--danger-red); }
        .filter-btn.high.active { background: var(--warning-amber); }
        .filter-btn.medium.active { background: var(--medium-text); }
        .filter-btn.low.active { background: var(--info-cyan); }

        /* Multi-select filter enhancements */
        .filter-btn.active {
            position: relative;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .multi-select-info {
            text-align: center;
            margin-top: 0.5rem;
        }

        .filter-summary {
            text-align: center;
            font-weight: 500;
        }

        /* Animation for filter changes */
        .severity-group {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        .severity-group[style*="display: none"] {
            opacity: 0;
            transform: translateY(-10px);
        }

        .finding-item {
            transition: opacity 0.2s ease;
        }

        .finding-item[style*="display: none"] {
            opacity: 0;
        }

        /* Summary Section */
        .summary-section {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }

        .summary-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-blue);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--gray-600);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .severity-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .severity-badge {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .severity-badge:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .severity-badge.critical {
            background: var(--critical-bg);
            border: 1px solid var(--critical-border);
            color: var(--critical-text);
        }

        .severity-badge.high {
            background: var(--high-bg);
            border: 1px solid var(--high-border);
            color: var(--high-text);
        }

        .severity-badge.medium {
            background: var(--medium-bg);
            border: 1px solid var(--medium-border);
            color: var(--medium-text);
        }

        .severity-badge.low {
            background: var(--low-bg);
            border: 1px solid var(--low-border);
            color: var(--low-text);
        }

        .severity-count {
            font-size: 1.25rem;
            font-weight: 700;
        }

        .severity-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* Findings Section */
        .findings-container {
            margin-bottom: 2rem;
        }

        .severity-group {
            background: white;
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
            box-shadow: var(--shadow);
            overflow: hidden;
            border: 1px solid var(--gray-200);
        }

        .severity-group:last-child {
            margin-bottom: 0;
        }

        /* Domain Section Styles */
        .domain-section {
            margin-bottom: 2rem;
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius);
            background: white;
            box-shadow: var(--shadow);
        }

        .domain-header {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: white;
            padding: 1.5rem 2rem;
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        .domain-header i {
            font-size: 1.5rem;
        }

        .domain-section .severity-group {
            margin: 0;
            border-radius: 0;
            border: none;
            border-bottom: 1px solid var(--gray-200);
            box-shadow: none;
        }

        .domain-section .severity-group:last-child {
            border-bottom: none;
            border-radius: 0 0 var(--border-radius) var(--border-radius);
        }

        .severity-header {
            padding: 1.25rem 1.5rem;
            font-weight: 600;
            font-size: 1.125rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 1px solid var(--gray-200);
        }

        .severity-header:hover {
            background: var(--gray-50);
        }

        .severity-header-left {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .severity-icon {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.875rem;
        }

        .severity-header.critical {
            background: var(--critical-bg);
            color: var(--critical-text);
            border-left: 4px solid var(--critical-text);
        }

        .severity-header.critical .severity-icon {
            background: var(--critical-text);
        }

        .severity-header.high {
            background: var(--high-bg);
            color: var(--high-text);
            border-left: 4px solid var(--high-text);
        }

        .severity-header.high .severity-icon {
            background: var(--high-text);
        }

        .severity-header.medium {
            background: var(--medium-bg);
            color: var(--medium-text);
            border-left: 4px solid var(--medium-text);
        }

        .severity-header.medium .severity-icon {
            background: var(--medium-text);
        }

        .severity-header.low {
            background: var(--low-bg);
            color: var(--low-text);
            border-left: 4px solid var(--low-text);
        }

        .severity-header.low .severity-icon {
            background: var(--low-text);
        }

        .severity-title {
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .severity-count {
            background: rgba(255, 255, 255, 0.9);
            color: inherit;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 700;
            margin-left: 0.5rem;
        }

        .toggle-icon {
            transition: transform 0.2s ease;
            color: var(--gray-500);
        }

        .severity-header.collapsed .toggle-icon {
            transform: rotate(-90deg);
        }

        .findings-list {
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .findings-list.collapsed {
            max-height: 0;
        }

        .finding-item {
            border-bottom: 1px solid var(--gray-200);
            padding: 1.5rem;
            transition: all 0.2s ease;
            background: white;
        }

        .finding-item:last-child {
            border-bottom: none;
        }

        .finding-item:hover {
            background: var(--gray-50);
        }

        .finding-header {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .finding-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
            flex-shrink: 0;
        }

        .finding-icon.critical { background: var(--danger-red); }
        .finding-icon.high { background: var(--warning-amber); }
        .finding-icon.medium { background: var(--medium-text); }
        .finding-icon.low { background: var(--info-cyan); }

        .finding-content {
            flex: 1;
            min-width: 0;
        }

        .finding-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            flex-wrap: wrap;
        }

        .control-id {
            background: var(--primary-blue);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .finding-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1rem;
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.375rem;
        }

        .meta-icon {
            color: var(--gray-400);
            width: 1rem;
        }

        .finding-description {
            color: var(--gray-700);
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .remediation-section {
            background: var(--success-green);
            background: linear-gradient(135deg, var(--success-green) 0%, #059669 100%);
            color: white;
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            margin-top: 1rem;
        }

        .remediation-title {
            font-weight: 600;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .remediation-content {
            font-size: 0.875rem;
            line-height: 1.5;
            opacity: 0.95;
        }

        .code-snippet {
            background: var(--gray-900);
            color: var(--gray-100);
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 0.8125rem;
            line-height: 1.5;
            white-space: pre-wrap;
            margin: 1rem 0;
            overflow-x: auto;
            border: 1px solid var(--gray-700);
        }

        .no-findings {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--gray-500);
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }

        .no-findings-icon {
            font-size: 3rem;
            color: var(--gray-300);
            margin-bottom: 1rem;
        }

        .no-findings h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
        }

        /* Footer */
        .report-footer {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-top: 2rem;
            box-shadow: var(--shadow);
            text-align: center;
        }

        .export-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }

        .export-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius-sm);
            background: var(--primary-blue);
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.875rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .export-btn:hover {
            background: var(--primary-blue-light);
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .footer-info {
            color: var(--gray-600);
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .footer-info strong {
            color: var(--gray-900);
        }

        /* Responsive Design */

        /* Large Desktop (1200px+) */
        @media (min-width: 1200px) {
            .main-container {
                padding: 3rem 2rem;
            }

            .stats-grid {
                grid-template-columns: repeat(4, 1fr);
            }

            .severity-overview {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        /* Desktop (992px - 1199px) */
        @media (min-width: 992px) and (max-width: 1199px) {
            .main-container {
                padding: 2rem 1.5rem;
            }

            .report-title {
                font-size: 2.25rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .severity-overview {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Tablet (768px - 991px) */
        @media (min-width: 768px) and (max-width: 991px) {
            .main-container {
                padding: 1.5rem 1rem;
            }

            .report-header {
                padding: 2rem 1.5rem;
            }

            .report-title {
                font-size: 2rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .report-meta {
                flex-direction: column;
                gap: 0.5rem;
            }

            .controls-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .filter-buttons {
                justify-content: center;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .severity-overview {
                grid-template-columns: repeat(2, 1fr);
            }

            .finding-title {
                font-size: 1rem;
            }

            .finding-meta {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        /* Mobile Large (576px - 767px) */
        @media (min-width: 576px) and (max-width: 767px) {
            .main-container {
                padding: 1rem 0.75rem;
            }

            .report-header {
                padding: 1.5rem 1rem;
            }

            .report-title {
                font-size: 1.75rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .report-subtitle {
                font-size: 1rem;
            }

            .report-meta {
                flex-direction: column;
                gap: 0.25rem;
                font-size: 0.8125rem;
            }

            .controls-section {
                padding: 1rem;
            }

            .controls-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .filter-buttons {
                justify-content: center;
                gap: 0.375rem;
            }

            .filter-btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.6875rem;
            }

            .summary-section {
                padding: 1.5rem 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .severity-overview {
                grid-template-columns: 1fr;
            }

            .finding-item {
                padding: 1rem;
            }

            .finding-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.75rem;
            }

            .finding-title {
                font-size: 0.9375rem;
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .export-actions {
                flex-direction: column;
                align-items: center;
            }
        }

        /* Mobile Small (up to 575px) */
        @media (max-width: 575px) {
            .main-container {
                padding: 0.75rem 0.5rem;
            }

            .report-header {
                padding: 1.25rem 0.75rem;
                margin-bottom: 1rem;
            }

            .report-title {
                font-size: 1.5rem;
                flex-direction: column;
                gap: 0.375rem;
            }

            .report-subtitle {
                font-size: 0.9375rem;
            }

            .report-meta {
                flex-direction: column;
                gap: 0.25rem;
                font-size: 0.75rem;
            }

            .controls-section {
                padding: 0.75rem;
            }

            .search-input {
                font-size: 1rem; /* Prevents zoom on iOS */
                padding: 0.75rem 1rem 0.75rem 2.25rem;
            }

            .filter-buttons {
                gap: 0.25rem;
            }

            .filter-btn {
                padding: 0.375rem 0.625rem;
                font-size: 0.625rem;
                min-width: auto;
            }

            .summary-section {
                padding: 1.25rem 0.75rem;
            }

            .summary-title {
                font-size: 1.25rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            .stat-card {
                padding: 1rem;
            }

            .stat-number {
                font-size: 2rem;
            }

            .severity-overview {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .severity-header {
                padding: 1rem;
                font-size: 1rem;
            }

            .finding-item {
                padding: 0.75rem;
            }

            .finding-icon {
                width: 2rem;
                height: 2rem;
                font-size: 0.875rem;
            }

            .finding-title {
                font-size: 0.875rem;
            }

            .control-id {
                font-size: 0.6875rem;
                padding: 0.1875rem 0.5rem;
            }

            .finding-meta {
                font-size: 0.8125rem;
            }

            .finding-description {
                font-size: 0.875rem;
            }

            .code-snippet {
                font-size: 0.75rem;
                padding: 0.75rem;
            }

            .export-actions {
                flex-direction: column;
                gap: 0.5rem;
            }

            .export-btn {
                width: 100%;
                justify-content: center;
            }
        }

        /* Print Styles */
        @media print {
            body {
                background: white !important;
            }

            .main-container {
                max-width: none;
                padding: 0;
            }

            .report-header,
            .controls-section,
            .summary-section,
            .severity-group,
            .report-footer {
                box-shadow: none !important;
                break-inside: avoid;
            }

            .controls-section,
            .export-actions {
                display: none !important;
            }

            .findings-list {
                max-height: none !important;
            }
        }
    </style>

    <script>
        // Modern JavaScript for enhanced interactivity with multi-select filtering
        let searchTimeout;
        let allFindings = [];
        let activeFilters = new Set(['all']); // Support multiple active filters

        document.addEventListener('DOMContentLoaded', function() {
            initializeReport();
            setupEventListeners();
            loadFindings();
        });

        function initializeReport() {
            // Initialize filter buttons with multi-select support
            const filterButtons = document.querySelectorAll('.filter-btn');
            filterButtons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    // Allow Ctrl/Cmd + click for multi-select
                    const isMultiSelect = e.ctrlKey || e.metaKey;
                    toggleFilter(this.dataset.severity, isMultiSelect);
                });
            });

            // Initialize search
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.addEventListener('input', debounceSearch);
            }

            // Initialize collapsible sections
            const severityHeaders = document.querySelectorAll('.severity-header');
            severityHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    toggleSeverityGroup(this);
                });
            });

            // Add instructions for multi-select
            addMultiSelectInstructions();
        }

        function addMultiSelectInstructions() {
            const controlsSection = document.querySelector('.controls-section');
            if (controlsSection) {
                const instructions = document.createElement('div');
                instructions.className = 'multi-select-info';
                instructions.innerHTML = `
                    <small style="color: var(--gray-500); font-size: 0.75rem; margin-top: 0.5rem; display: block;">
                        <i class="fas fa-info-circle"></i>
                        Tip: Hold Ctrl/Cmd and click to select multiple severity levels
                    </small>
                `;
                controlsSection.appendChild(instructions);
            }
        }

        function setupEventListeners() {
            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    clearSearch();
                    resetFilters();
                }
                if (e.ctrlKey && e.key === 'f') {
                    e.preventDefault();
                    focusSearch();
                }
            });
        }

        function debounceSearch() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch();
            }, 300);
        }

        function performSearch() {
            const searchTerm = document.querySelector('.search-input').value.toLowerCase();
            applyFilters(searchTerm);
        }

        function toggleFilter(severity, isMultiSelect = false) {
            if (severity === 'all') {
                // If "All" is clicked, reset to show all
                activeFilters.clear();
                activeFilters.add('all');
            } else {
                if (isMultiSelect) {
                    // Multi-select mode
                    if (activeFilters.has('all')) {
                        activeFilters.clear();
                    }

                    if (activeFilters.has(severity)) {
                        activeFilters.delete(severity);
                    } else {
                        activeFilters.add(severity);
                    }

                    // If no filters selected, default to "all"
                    if (activeFilters.size === 0) {
                        activeFilters.add('all');
                    }
                } else {
                    // Single select mode (default behavior)
                    activeFilters.clear();
                    activeFilters.add(severity);
                }
            }

            updateFilterButtons();
            applyFilters();
            updateUrlHash();
        }

        function updateFilterButtons() {
            const filterButtons = document.querySelectorAll('.filter-btn');
            filterButtons.forEach(btn => {
                const severity = btn.dataset.severity;
                const isActive = activeFilters.has(severity);
                btn.classList.toggle('active', isActive);

                // Add visual indicator for multi-select
                if (activeFilters.size > 1 && !activeFilters.has('all')) {
                    btn.style.position = 'relative';
                    if (isActive && !btn.querySelector('.multi-indicator')) {
                        const indicator = document.createElement('span');
                        indicator.className = 'multi-indicator';
                        indicator.innerHTML = '✓';
                        indicator.style.cssText = `
                            position: absolute;
                            top: -2px;
                            right: -2px;
                            background: var(--success-green);
                            color: white;
                            border-radius: 50%;
                            width: 16px;
                            height: 16px;
                            font-size: 10px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-weight: bold;
                        `;
                        btn.appendChild(indicator);
                    }
                } else {
                    // Remove multi-select indicators
                    const indicator = btn.querySelector('.multi-indicator');
                    if (indicator) {
                        indicator.remove();
                    }
                }
            });
        }

        function applyFilters(searchTerm = '') {
            if (!searchTerm) {
                searchTerm = document.querySelector('.search-input').value.toLowerCase();
            }

            const severityGroups = document.querySelectorAll('.severity-group');
            let totalVisibleCount = 0;

            severityGroups.forEach(group => {
                const groupSeverity = group.dataset.severity;
                const findings = group.querySelectorAll('.finding-item');
                let groupVisibleCount = 0;

                // Check if this severity group should be visible
                const severityMatches = activeFilters.has('all') || activeFilters.has(groupSeverity);

                findings.forEach(finding => {
                    const text = finding.textContent.toLowerCase();
                    const searchMatches = searchTerm === '' || text.includes(searchTerm);
                    const isVisible = severityMatches && searchMatches;

                    finding.style.display = isVisible ? 'block' : 'none';
                    if (isVisible) {
                        groupVisibleCount++;
                        totalVisibleCount++;
                    }
                });

                // Show/hide the entire severity group
                group.style.display = groupVisibleCount > 0 ? 'block' : 'none';
            });

            updateNoResultsMessage(totalVisibleCount === 0);
            updateFilterSummary();
        }

        function updateFilterSummary() {
            // Update or create filter summary
            let summary = document.querySelector('.filter-summary');
            if (!summary) {
                summary = document.createElement('div');
                summary.className = 'filter-summary';
                summary.style.cssText = `
                    margin-top: 0.5rem;
                    padding: 0.5rem;
                    background: var(--gray-100);
                    border-radius: var(--border-radius-sm);
                    font-size: 0.8125rem;
                    color: var(--gray-600);
                `;
                document.querySelector('.controls-section').appendChild(summary);
            }

            if (activeFilters.has('all')) {
                summary.textContent = 'Showing all severity levels';
            } else {
                const filterList = Array.from(activeFilters).map(f => f.charAt(0).toUpperCase() + f.slice(1)).join(', ');
                summary.textContent = `Showing: ${filterList} severity levels`;
            }
        }

        function resetFilters() {
            activeFilters.clear();
            activeFilters.add('all');
            updateFilterButtons();
            applyFilters();
            updateUrlHash();
        }

        function updateUrlHash() {
            const params = new URLSearchParams();
            if (!activeFilters.has('all')) {
                params.set('filters', Array.from(activeFilters).join(','));
            }
            const searchTerm = document.querySelector('.search-input').value;
            if (searchTerm) {
                params.set('search', searchTerm);
            }

            const hash = params.toString();
            if (hash) {
                window.location.hash = hash;
            } else {
                window.history.replaceState(null, null, window.location.pathname);
            }
        }

        function loadFromUrlHash() {
            const hash = window.location.hash.substring(1);
            if (hash) {
                const params = new URLSearchParams(hash);
                const filters = params.get('filters');
                const search = params.get('search');

                if (filters) {
                    activeFilters.clear();
                    filters.split(',').forEach(filter => activeFilters.add(filter.trim()));
                    updateFilterButtons();
                }

                if (search) {
                    document.querySelector('.search-input').value = search;
                }

                applyFilters();
            }
        }

        function toggleSeverityGroup(header) {
            const group = header.parentElement;
            const findingsList = group.querySelector('.findings-list');
            const isCollapsed = header.classList.contains('collapsed');

            if (isCollapsed) {
                header.classList.remove('collapsed');
                findingsList.classList.remove('collapsed');
                findingsList.style.maxHeight = findingsList.scrollHeight + 'px';
            } else {
                header.classList.add('collapsed');
                findingsList.classList.add('collapsed');
                findingsList.style.maxHeight = '0';
            }
        }

        function clearSearch() {
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.value = '';
                applyFilters();
            }
        }

        function focusSearch() {
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }

        function updateNoResultsMessage(show) {
            const noResults = document.querySelector('.no-findings');
            if (noResults) {
                noResults.style.display = show ? 'block' : 'none';
                if (show) {
                    // Update message based on active filters
                    const message = noResults.querySelector('p');
                    if (activeFilters.has('all')) {
                        message.textContent = 'Try adjusting your search terms';
                    } else {
                        const filterList = Array.from(activeFilters).join(', ');
                        message.textContent = `No ${filterList} severity findings match your search. Try different filters or search terms.`;
                    }
                }
            }
        }

        function exportToJson() {
            // Get currently visible findings for export
            const visibleFindings = [];
            document.querySelectorAll('.finding-item').forEach(finding => {
                if (finding.style.display !== 'none') {
                    const severityGroup = finding.closest('.severity-group');
                    const severity = severityGroup ? severityGroup.dataset.severity.toUpperCase() : 'UNKNOWN';
                    const controlId = finding.querySelector('.control-id')?.textContent || 'UNKNOWN';
                    const description = finding.querySelector('.finding-description')?.textContent || '';
                    const remediation = finding.querySelector('.remediation-content')?.textContent || '';
                    const filePath = finding.querySelector('.meta-item:first-child span')?.textContent || '';
                    const lineText = finding.querySelector('.meta-item:last-child span')?.textContent || '';
                    const line = lineText.replace('Line ', '') || '0';

                    visibleFindings.push({
                        control_id: controlId,
                        severity: severity,
                        file_path: filePath,
                        line: parseInt(line) || 0,
                        description: description.trim(),
                        remediation: remediation.trim()
                    });
                }
            });

            const data = {
                timestamp: new Date().toISOString(),
                filters_applied: Array.from(activeFilters),
                total_findings: visibleFindings.length,
                findings: visibleFindings,
                summary: {
                    critical: visibleFindings.filter(f => f.severity === 'CRITICAL').length,
                    high: visibleFindings.filter(f => f.severity === 'HIGH').length,
                    medium: visibleFindings.filter(f => f.severity === 'MEDIUM').length,
                    low: visibleFindings.filter(f => f.severity === 'LOW').length
                }
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], {
                type: 'application/json'
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `security-findings-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function loadFindings() {
            // Initialize findings data - this would be populated with actual findings
            allFindings = [];
        }

        // Initialize from URL hash on page load
        window.addEventListener('load', function() {
            loadFromUrlHash();
        });

        // Handle browser back/forward
        window.addEventListener('hashchange', function() {
            loadFromUrlHash();
        });
    </script>
</head>

<body>
    <div class="main-container">
        <!-- Header Section -->
        <header class="report-header">
            <h1 class="report-title">
                <i class="fas fa-shield-alt"></i>
                Security Assessment Report
            </h1>
            <p class="report-subtitle">Infrastructure as Code Security Analysis</p>
            <div class="report-meta">
                <div class="meta-item">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Generated: June 16, 2025 at 11:10 PM</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-robot"></i>
                    <span>IaC Guardian</span>
                </div>
            </div>
        </header>

        <!-- Controls Section -->
        <section class="controls-section">
            <div class="controls-grid">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search findings..." aria-label="Search findings">
                    <i class="fas fa-search search-icon"></i>
                </div>
                <div class="filter-buttons">
                    <button class="filter-btn all active" data-severity="all">All</button>
                    <button class="filter-btn critical" data-severity="critical">Critical</button>
                    <button class="filter-btn high" data-severity="high">High</button>
                    <button class="filter-btn medium" data-severity="medium">Medium</button>
                    <button class="filter-btn low" data-severity="low">Low</button>
                </div>
            </div>
        </section>

        
        <!-- Summary Section -->
        <section class="summary-section">
            <h2 class="summary-title">
                <i class="fas fa-chart-bar"></i>
                Executive Summary
            </h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">64</div>
                    <div class="stat-label">Total Findings</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">5</div>
                    <div class="stat-label">Files Affected</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">50</div>
                    <div class="stat-label">High Priority Issues</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">23</div>
                    <div class="stat-label">Security Controls</div>
                </div>
            </div>
            <div class="severity-overview">
                <div class="severity-badge critical">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        Critical
                    </div>
                    <div class="severity-count">12</div>
                </div>
                <div class="severity-badge high">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        High
                    </div>
                    <div class="severity-count">38</div>
                </div>
                <div class="severity-badge medium">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        Medium
                    </div>
                    <div class="severity-count">13</div>
                </div>
                <div class="severity-badge low">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        Low
                    </div>
                    <div class="severity-count">1</div>
                </div>
            </div>
        </section>

        <div class="findings-container">
            
            <div class="domain-section">
                <h3 class="domain-header">
                    <i class="fas fa-shield-alt"></i>
                    Identity Management (11 findings)
                </h3>
                <section class="severity-group" data-severity="high" data-domain="identity-management">
                    <header class="severity-header high">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">High Severity</div>
                            <div class="severity-count">8</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">IM-1</span>
                                <span class="finding-description">Azure Active Directory (Azure AD) is not referenced for identity management of compute resources (IM-1).</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Integrate Azure AD for identity and access management of all compute resources.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">IM-1</span>
                                <span class="finding-description">Identity Management control IM-1 is missing for resource types: AppService, Container, LogicApps. Azure Active Directory is not used for identity management.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>missing_controls.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 215</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Integrate AppService, Container, and LogicApps resources with Azure Active Directory for secure identity and access management.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">IM-1</span>
                                <span class="finding-description">Authentication is not enforced for all users. The 'requireAuthentication' property is set to false, which means unauthenticated access is allowed to the App Service. This violates ASB IM-1, which requires secure identity management using Azure AD.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>security_findings.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 74</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'requireAuthentication' to true in the 'globalValidation' section to enforce authentication for all users accessing the App Service.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">IM-2</span>
                                <span class="finding-description">Multi-Factor Authentication (MFA) is not enabled or referenced for users or administrators of compute resources (IM-2).</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Require and enforce MFA for all users and administrators accessing compute resources.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">IM-2</span>
                                <span class="finding-description">Identity Management control IM-2 is missing for resource types: AppService, Container, LogicApps. Multi-Factor Authentication (MFA) is not enabled.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>missing_controls.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 228</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Enable Multi-Factor Authentication (MFA) for all users and administrators accessing AppService, Container, and LogicApps resources.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">IM-3</span>
                                <span class="finding-description">Identity Management control IM-3 is missing for resource types: AppService, Container, LogicApps. Conditional access policies are not implemented.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>missing_controls.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 241</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Implement conditional access policies for AppService, Container, and LogicApps to enforce secure access.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">IM-6</span>
                                <span class="finding-description">Identity Management control IM-6 is missing for resource types: AppService, Container, LogicApps. Role-Based Access Control (RBAC) is not used.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>missing_controls.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 280</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Assign access rights using RBAC for AppService, Container, and LogicApps to limit privileges to what is necessary.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">IM-8</span>
                                <span class="finding-description">Identity Management control IM-8 is missing for resource types: AppService, Container, LogicApps. Managed identities are not used for secure resource-to-resource authentication.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>missing_controls.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 306</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Enable and use managed identities for AppService, Container, and LogicApps resources to ensure secure authentication between resources.</div>
                            </div>
                        </div>
                    </article>
                    </div>
                </section>
                <section class="severity-group" data-severity="medium" data-domain="identity-management">
                    <header class="severity-header medium">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">Medium Severity</div>
                            <div class="severity-count">3</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">IM-3</span>
                                <span class="finding-description">Conditional access policies are not configured for compute resources (IM-3).</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Implement Azure AD conditional access policies to enforce secure access to compute resources.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">IM-6</span>
                                <span class="finding-description">Role-Based Access Control (RBAC) is not configured for compute resources (IM-6).</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Assign access rights to compute resources using Azure RBAC with least privilege principles.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">IM-8</span>
                                <span class="finding-description">Managed identities are not configured for compute resources (IM-8).</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Enable and use managed identities for secure authentication between compute resources and other Azure services.</div>
                            </div>
                        </div>
                    </article>
                    </div>
                </section>
            </div>
            <div class="domain-section">
                <h3 class="domain-header">
                    <i class="fas fa-shield-alt"></i>
                    Network Security (30 findings)
                </h3>
                <section class="severity-group" data-severity="critical" data-domain="network-security">
                    <header class="severity-header critical">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">Critical Severity</div>
                            <div class="severity-count">6</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-1</span>
                                <span class="finding-description">No network security groups (NSGs) or Azure Firewall are configured to protect compute resources (NS-1).</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure NSGs or Azure Firewall to restrict and monitor network traffic to compute resources.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-1</span>
                                <span class="finding-description">Network Security control NS-1 is not referenced in the template. Network security groups (NSGs) or Azure Firewall are not configured to protect AppService, Container, LogicApps resources.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>missing_controls.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure network security groups (NSGs) or Azure Firewall to protect all AppService, Container, and LogicApps resources.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">The template does not secure public endpoints for compute resources, increasing exposure risk (NS-2).</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Restrict or secure all public endpoints for compute resources using NSGs, firewalls, or private endpoints.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">Network Security control NS-2 is not referenced in the template. Public endpoints for AppService, Container, LogicApps are not secured.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>missing_controls.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Secure all public endpoints for AppService, Container, and LogicApps to minimize exposure.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-3</span>
                                <span class="finding-description">Network Security Groups (NSGs) are not implemented to control traffic to compute resources (NS-3).</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Implement NSGs to control inbound and outbound traffic for all compute resources.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-3</span>
                                <span class="finding-description">Network Security control NS-3 is not referenced in the template. Network Security Groups (NSGs) are not implemented to control traffic for AppService, Container, LogicApps.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>missing_controls.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Implement Network Security Groups (NSGs) to control inbound and outbound traffic for AppService, Container, and LogicApps resources.</div>
                            </div>
                        </div>
                    </article>
                    </div>
                </section>
                <section class="severity-group" data-severity="high" data-domain="network-security">
                    <header class="severity-header high">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">High Severity</div>
                            <div class="severity-count">21</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-1</span>
                                <span class="finding-description">The 'networkAcls' block in 'test_keyvault.bicep' at line 13 allows access from all networks, which exposes the Key Vault to unnecessary security risks. ASB Control NS-1 requires protection of sensitive resources using network security groups or Azure Firewall.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>test_analysis_output.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 13</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only trusted networks or private endpoints.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">Public endpoint protection for storage accounts is set to 'Disabled' in 'disableUnrestrictedNetworkToStorageAccountMonitoringEffect', which does not audit or deny unrestricted network access.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>AzureSecurityCenter.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 320</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'disableUnrestrictedNetworkToStorageAccountMonitoringEffect' to 'Audit' or 'Deny' to ensure public endpoints are protected.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">Network access restriction for storage accounts is set to 'Audit' in 'storageAccountsShouldRestrictNetworkAccessUsingVirtualNetworkRulesMonitoringEffect', which only audits but does not enforce restriction.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>AzureSecurityCenter.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 324</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'storageAccountsShouldRestrictNetworkAccessUsingVirtualNetworkRulesMonitoringEffect' to 'Deny' to enforce network access restrictions using virtual network rules.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">Public network access for MariaDB, MySQL, and PostgreSQL servers is set to 'Audit' in 'publicNetworkAccessShouldBeDisabledForMariaDbServersMonitoringEffect', 'publicNetworkAccessShouldBeDisabledForMySqlServersMonitoringEffect', and 'publicNetworkAccessShouldBeDisabledForPostgreSqlServersMonitoringEffect', which only audits but does not enforce disabling public access.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>AzureSecurityCenter.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 328</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'publicNetworkAccessShouldBeDisabledForMariaDbServersMonitoringEffect', 'publicNetworkAccessShouldBeDisabledForMySqlServersMonitoringEffect', and 'publicNetworkAccessShouldBeDisabledForPostgreSqlServersMonitoringEffect' to 'Deny' to enforce disabling public network access.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">Private endpoint enforcement for PostgreSQL, MariaDB, and MySQL servers is set to 'AuditIfNotExists' in 'privateEndpointShouldBeEnabledForPostgresqlServersMonitoringEffect', 'privateEndpointShouldBeEnabledForMariadbServersMonitoringEffect', and 'privateEndpointShouldBeEnabledForMysqlServersMonitoringEffect', which only audits but does not enforce private endpoint usage.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>AzureSecurityCenter.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 332</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'privateEndpointShouldBeEnabledForPostgresqlServersMonitoringEffect', 'privateEndpointShouldBeEnabledForMariadbServersMonitoringEffect', and 'privateEndpointShouldBeEnabledForMysqlServersMonitoringEffect' to 'Deny' to enforce private endpoint usage.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">Azure Cache for Redis private endpoint enforcement is set to 'AuditIfNotExists' in 'azureCacheForRedisShouldUsePrivateEndpointMonitoringEffect', which only audits but does not enforce private endpoint usage.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>AzureSecurityCenter.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 336</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'azureCacheForRedisShouldUsePrivateEndpointMonitoringEffect' to 'Deny' to enforce private endpoint usage for Azure Cache for Redis.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">Container registry unrestricted network access is set to 'Audit' in 'containerRegistriesShouldNotAllowUnrestrictedNetworkAccessMonitoringEffect', which only audits but does not enforce restriction.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>AzureSecurityCenter.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 340</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'containerRegistriesShouldNotAllowUnrestrictedNetworkAccessMonitoringEffect' to 'Deny' to enforce network access restrictions for container registries.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">Container registry private link enforcement is set to 'Audit' in 'containerRegistriesShouldUsePrivateLinkMonitoringEffect', which only audits but does not enforce private link usage.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>AzureSecurityCenter.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 344</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'containerRegistriesShouldUsePrivateLinkMonitoringEffect' to 'Deny' to enforce private link usage for container registries.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">App Configuration private link enforcement is set to 'AuditIfNotExists' in 'appConfigurationShouldUsePrivateLinkMonitoringEffect', which only audits but does not enforce private link usage.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>AzureSecurityCenter.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 348</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'appConfigurationShouldUsePrivateLinkMonitoringEffect' to 'Deny' to enforce private link usage for App Configuration.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">Azure Event Grid domains and topics private link enforcement is set to 'Audit' in 'azureEventGridDomainsShouldUsePrivateLinkMonitoringEffect' and 'azureEventGridTopicsShouldUsePrivateLinkMonitoringEffect', which only audits but does not enforce private link usage.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>AzureSecurityCenter.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 352</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'azureEventGridDomainsShouldUsePrivateLinkMonitoringEffect' and 'azureEventGridTopicsShouldUsePrivateLinkMonitoringEffect' to 'Deny' to enforce private link usage.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">Azure SignalR Service private link enforcement is set to 'Audit' in 'azureSignalRServiceShouldUsePrivateLinkMonitoringEffect', which only audits but does not enforce private link usage.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>AzureSecurityCenter.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 356</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'azureSignalRServiceShouldUsePrivateLinkMonitoringEffect' to 'Deny' to enforce private link usage for Azure SignalR Service.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">Azure Machine Learning workspaces private link enforcement is set to 'Audit' in 'azureMachineLearningWorkspacesShouldUsePrivateLinkMonitoringEffect', which only audits but does not enforce private link usage.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>AzureSecurityCenter.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 360</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'azureMachineLearningWorkspacesShouldUsePrivateLinkMonitoringEffect' to 'Deny' to enforce private link usage for Azure Machine Learning workspaces.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">VM Image Builder templates private link enforcement is set to 'Audit' in 'vmImageBuilderTemplatesShouldUsePrivateLinkMonitoringEffect', which only audits but does not enforce private link usage.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>AzureSecurityCenter.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 364</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'vmImageBuilderTemplatesShouldUsePrivateLinkMonitoringEffect' to 'Deny' to enforce private link usage for VM Image Builder templates.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">Cognitive Services accounts network access restriction is set to 'Audit' in 'cognitiveServicesAccountsShouldRestrictNetworkAccessMonitoringEffect', which only audits but does not enforce network access restriction.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>AzureSecurityCenter.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 368</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'cognitiveServicesAccountsShouldRestrictNetworkAccessMonitoringEffect' to 'Deny' to enforce network access restriction for Cognitive Services accounts.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">API Management services virtual network enforcement is set to 'Audit' in 'aPIManagementServicesShouldUseAVirtualNetworkMonitoringEffect', which only audits but does not enforce virtual network usage.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>AzureSecurityCenter.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 372</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'aPIManagementServicesShouldUseAVirtualNetworkMonitoringEffect' to 'Deny' to enforce virtual network usage for API Management services.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">Azure Cosmos DB accounts firewall rules enforcement is set to 'Audit' in 'azureCosmosDBAccountsShouldHaveFirewallRulesMonitoringEffect', which only audits but does not enforce firewall rules.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>AzureSecurityCenter.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 376</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'azureCosmosDBAccountsShouldHaveFirewallRulesMonitoringEffect' to 'Deny' to enforce firewall rules for Azure Cosmos DB accounts.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">Storage account private link enforcement is set to 'AuditIfNotExists' in 'storageAccountShouldUseAPrivateLinkConnectionMonitoringEffect', which only audits but does not enforce private link usage.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>AzureSecurityCenter.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 380</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'storageAccountShouldUseAPrivateLinkConnectionMonitoringEffect' to 'Deny' to enforce private link usage for storage accounts.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">The 'networkAcls.defaultAction' property in 'test_keyvault.bicep' at line 13 is set to 'Allow', permitting public network access to the Key Vault. This increases the risk of unauthorized access. ASB Control NS-2 requires securing all public endpoints.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>test_analysis_output.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 13</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only trusted networks or private endpoints.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-5</span>
                                <span class="finding-description">Private endpoints are not configured for compute resources, increasing network exposure (NS-5).</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Implement private endpoints to securely access compute resources and minimize public exposure.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-5</span>
                                <span class="finding-description">Network Security control NS-5 is not referenced in the template. Private endpoints are not implemented for AppService, Container, LogicApps.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>missing_controls.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Implement private endpoints for AppService, Container, and LogicApps to securely access resources.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-9</span>
                                <span class="finding-description">Network Security control NS-9 is not referenced in the template. Network traffic monitoring is not enabled for AppService, Container, LogicApps.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>missing_controls.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Enable Azure Monitor and Network Watcher to monitor and log network traffic for AppService, Container, and LogicApps resources.</div>
                            </div>
                        </div>
                    </article>
                    </div>
                </section>
                <section class="severity-group" data-severity="medium" data-domain="network-security">
                    <header class="severity-header medium">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">Medium Severity</div>
                            <div class="severity-count">3</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-10</span>
                                <span class="finding-description">Azure Bastion is not configured for secure VM access, potentially exposing management ports (NS-10).</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Deploy Azure Bastion to securely manage VM access without exposing SSH/RDP ports.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">The App Service is not configured with access restrictions (IP restrictions or service endpoints), which means it is potentially accessible from any network, including the public internet. This does not meet ASB NS-2 requirements to protect public endpoints.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>security_findings.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 38</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure 'ipSecurityRestrictions' in the App Service configuration to restrict access to only trusted IP addresses or networks.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-7</span>
                                <span class="finding-description">Just-in-Time (JIT) VM access is not enabled for compute resources, potentially exposing management ports (NS-7).</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Enable JIT VM access to restrict management port exposure for compute resources.</div>
                            </div>
                        </div>
                    </article>
                    </div>
                </section>
            </div>
            <div class="domain-section">
                <h3 class="domain-header">
                    <i class="fas fa-shield-alt"></i>
                    Data Protection (15 findings)
                </h3>
                <section class="severity-group" data-severity="critical" data-domain="data-protection">
                    <header class="severity-header critical">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">Critical Severity</div>
                            <div class="severity-count">6</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-1</span>
                                <span class="finding-description">The template does not specify encryption at rest for any compute resource data (DP-1).</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure encryption at rest for all compute resource storage, such as enabling disk encryption.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-1</span>
                                <span class="finding-description">Data Protection control DP-1 is missing for resource types: AppService, Container, LogicApps. Encryption at rest is not enabled.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>missing_controls.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 137</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Enable encryption at rest for all data storage in AppService, Container, and LogicApps resources.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-2</span>
                                <span class="finding-description">The template does not specify encryption in transit (e.g., TLS 1.2+) for data associated with compute resources (DP-2).</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Ensure all data in transit to and from compute resources is encrypted using TLS 1.2 or higher.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-2</span>
                                <span class="finding-description">Data Protection control DP-2 is not referenced in the template. Encryption in transit (TLS 1.2+) is not explicitly enabled for AppService, Container, LogicApps.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>missing_controls.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure all AppService, Container, and LogicApps resources to enforce TLS 1.2 or higher for all data transfers.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-3</span>
                                <span class="finding-description">No configuration for managing sensitive information disclosure (e.g., storing secrets in Azure Key Vault) is present for compute resources (DP-3).</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Store all sensitive data, such as credentials and keys, in Azure Key Vault and reference them securely in the template.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-3</span>
                                <span class="finding-description">Data Protection control DP-3 is not referenced in the template. Sensitive information such as keys and secrets are not explicitly stored in Azure Key Vault for AppService, Container, LogicApps.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>missing_controls.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Store all sensitive data, including keys and secrets, in Azure Key Vault for AppService, Container, and LogicApps resources.</div>
                            </div>
                        </div>
                    </article>
                    </div>
                </section>
                <section class="severity-group" data-severity="high" data-domain="data-protection">
                    <header class="severity-header high">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">High Severity</div>
                            <div class="severity-count">5</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-3</span>
                                <span class="finding-description">Soft delete is not enabled for the Key Vault in 'test_keyvault.bicep' at line 9. Without soft delete, deleted secrets, keys, and certificates cannot be recovered, increasing the risk of accidental or malicious data loss. ASB Control DP-3 requires managing sensitive information disclosure.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>test_analysis_output.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 9</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'enableSoftDelete' to true to ensure deleted items can be recovered.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-3</span>
                                <span class="finding-description">Purge protection is not enabled for the Key Vault in 'test_keyvault.bicep' at line 10. Without purge protection, a malicious actor could permanently delete (purge) secrets, keys, or certificates, even if soft delete is enabled. ASB Control DP-3 requires managing sensitive information disclosure.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>test_analysis_output.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 10</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'enablePurgeProtection' to true to prevent permanent deletion of Key Vault objects.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-4</span>
                                <span class="finding-description">The template does not specify the use of managed disks with encryption for compute resources (DP-4).</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Use Azure managed disks with encryption enabled for all compute resources.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-5</span>
                                <span class="finding-description">Data Protection control DP-5 is missing for resource types: AppService, Container, LogicApps. Backup and recovery strategies are not implemented.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>missing_controls.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 150</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Implement backup and recovery strategies for all critical data in AppService, Container, and LogicApps resources.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-6</span>
                                <span class="finding-description">Data Protection control DP-6 is missing for resource types: AppService, Container, LogicApps. Customer-managed keys (CMK) are not used for encryption.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>missing_controls.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 163</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure AppService, Container, and LogicApps resources to use customer-managed keys (CMK) for controlling encryption keys.</div>
                            </div>
                        </div>
                    </article>
                    </div>
                </section>
                <section class="severity-group" data-severity="medium" data-domain="data-protection">
                    <header class="severity-header medium">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">Medium Severity</div>
                            <div class="severity-count">3</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-1</span>
                                <span class="finding-description">The App Service does not have 'clientCertEnabled' set, which means client certificate authentication is not enforced. This weakens transport security for sensitive applications and does not align with ASB DP-1, which recommends strong data protection.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>security_findings.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 38</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Add 'clientCertEnabled': true to the App Service properties if client certificate authentication is required for your scenario.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-6</span>
                                <span class="finding-description">Key Vault secrets and keys expiration enforcement is set to 'Disabled' in 'secretsExpirationSetEffect' and 'keysExpirationSetEffect'. This does not enforce expiration dates for secrets and keys, reducing security effectiveness.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>AzureSecurityCenter.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 273</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'secretsExpirationSetEffect' and 'keysExpirationSetEffect' to 'Audit' or 'Deny' to enforce expiration dates for Key Vault secrets and keys.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-6</span>
                                <span class="finding-description">Customer-managed keys (CMK) are not configured for encryption of compute resource data (DP-6).</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure customer-managed keys (CMK) for encrypting data associated with compute resources.</div>
                            </div>
                        </div>
                    </article>
                    </div>
                </section>
                <section class="severity-group" data-severity="low" data-domain="data-protection">
                    <header class="severity-header low">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">Low Severity</div>
                            <div class="severity-count">1</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-3</span>
                                <span class="finding-description">There is no evidence of HTTPS minimum TLS version enforcement. The App Service should enforce a minimum TLS version (e.g., 1.2) to ensure secure transport, as recommended by ASB DP-3.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>security_findings.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 38</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'minTlsVersion' property (e.g., '1.2') in the App Service configuration to enforce a secure TLS version.</div>
                            </div>
                        </div>
                    </article>
                    </div>
                </section>
            </div>
            <div class="domain-section">
                <h3 class="domain-header">
                    <i class="fas fa-shield-alt"></i>
                    Access Management (8 findings)
                </h3>
                <section class="severity-group" data-severity="high" data-domain="access-management">
                    <header class="severity-header high">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">High Severity</div>
                            <div class="severity-count">4</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">AM-1</span>
                                <span class="finding-description">The template does not specify any access controls or permissions for compute resources, violating the least privilege principle (AM-1).</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Explicitly define and assign least privilege access roles for all compute resources using Azure RBAC in the template.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">AM-1</span>
                                <span class="finding-description">Access Management control AM-1 is missing for resource types: AppService, Container, LogicApps. This indicates least privilege access is not enforced.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>missing_controls.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 10</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Review and assign least privilege access for all users and applications in AppService, Container, and LogicApps resources. Limit permissions to only what is required.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">AM-3</span>
                                <span class="finding-description">Access Management control AM-3 is missing for resource types: AppService, Container, LogicApps. Privileged Identity Management (PIM) is not implemented.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>missing_controls.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 36</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Enable and configure Azure Privileged Identity Management (PIM) for AppService, Container, and LogicApps to manage and control privileged access.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">AM-5</span>
                                <span class="finding-description">Access Management control AM-5 is missing for resource types: AppService, Container, LogicApps. Logging for access management activities is not enabled.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>missing_controls.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 62</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Enable logging for all access management activities on AppService, Container, and LogicApps resources to monitor and audit access changes.</div>
                            </div>
                        </div>
                    </article>
                    </div>
                </section>
                <section class="severity-group" data-severity="medium" data-domain="access-management">
                    <header class="severity-header medium">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">Medium Severity</div>
                            <div class="severity-count">4</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">AM-2</span>
                                <span class="finding-description">The template does not include any configuration for periodic review of access rights for compute resources (AM-2).</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Implement mechanisms or document processes to regularly review and update access assignments for compute resources.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">AM-2</span>
                                <span class="finding-description">Access Management control AM-2 is missing for resource types: AppService, Container, LogicApps. Regular access rights review is not configured.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>missing_controls.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 23</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Implement periodic access reviews for AppService, Container, and LogicApps resources to ensure only necessary access is retained.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">AM-3</span>
                                <span class="finding-description">Privileged Identity Management (PIM) is not referenced or configured for compute resources in the template (AM-3).</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Integrate Azure Privileged Identity Management (PIM) for managing privileged access to compute resources.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">AM-4</span>
                                <span class="finding-description">Access Management control AM-4 is missing for resource types: AppService, Container, LogicApps. Access reviews are not configured.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>missing_controls.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 49</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure access reviews for AppService, Container, and LogicApps to ensure only necessary access remains.</div>
                            </div>
                        </div>
                    </article>
                    </div>
                </section>
            </div>
            <div class="no-findings" style="display: none;">
                <i class="fas fa-search no-findings-icon"></i>
                <h3>No findings match your search criteria</h3>
                <p>Try adjusting your search terms or filters</p>
            </div>
        </div>

        <!-- Footer Section -->
        <footer class="report-footer">
            <div class="export-actions">
                <button class="export-btn" onclick="window.print()">
                    <i class="fas fa-print"></i>
                    Print Report
                </button>
                <button class="export-btn" onclick="exportToJson()">
                    <i class="fas fa-download"></i>
                    Export JSON
                </button>
            </div>
            <div class="footer-info">
                <p><strong>Generated by IaC Guardian</strong> • June 16, 2025 at 11:10 PM</p>
                <p><strong>Disclaimer:</strong> This report is AI-generated and should be reviewed by security professionals.</p>
            </div>
        </footer>
    </div>
</body>
</html>