File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
network_demo.tf,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,24.0,"The security_rule in azurerm_network_security_group.demo allows inbound SSH (port 22) from any source (source_address_prefix = ""0.0.0.0/0""), exposing the resource to the entire internet. This enables initial access for attackers, who can attempt brute-force or exploit SSH vulnerabilities, potentially leading to full compromise of any attached VM and lateral movement within the network.","Restrict the source_address_prefix for SSH to trusted IP ranges only (e.g., your corporate IP or a jump host). Change 'source_address_prefix = ""0.0.0.0/0""' to a specific, limited CIDR block. Consider removing public SSH access entirely and using Azure Bastion or VPN for secure management.",,,,ai_analysis,,Validated
network_demo.tf,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,43.0,"The allow_blob_public_access property in azurerm_storage_account.demo is set to true, enabling public anonymous access to blobs. This exposes all data in public containers to the internet, allowing attackers to exfiltrate sensitive data without authentication, significantly increasing the blast radius of a compromise.",Set 'allow_blob_public_access = false' to disable anonymous public access to blobs. Use private endpoints and restrict access to trusted networks or identities. Review all containers for public access and remediate as needed.,,,,ai_analysis,,Validated
network_demo.tf,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,46.0,"The https_traffic_only property in azurerm_storage_account.demo is set to false, allowing unencrypted HTTP connections. This permits attackers to intercept or modify data in transit using man-in-the-middle attacks, compromising confidentiality and integrity of data transferred to and from the storage account.",Set 'https_traffic_only = true' to enforce encrypted HTTPS connections for all access to the storage account. Ensure all clients and applications use HTTPS endpoints exclusively.,,,,ai_analysis,,Validated
storage_demo.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,20.0,"The 'minimumTlsVersion' property is set to 'TLS1_0', which allows weak and deprecated TLS protocols for data in transit. Attackers can exploit known vulnerabilities in TLS 1.0 to intercept or modify data between clients and the storage account, enabling man-in-the-middle attacks and data exfiltration. The blast radius includes all data transferred to and from this storage account.",Set 'minimumTlsVersion' to 'TLS1_2' or higher to enforce strong encryption for data in transit. Update the configuration to: minimumTlsVersion: 'TLS1_2'.,,,,ai_analysis,,Validated
storage_demo.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,23.0,"The 'supportsHttpsTrafficOnly' property is set to false, allowing unencrypted HTTP traffic to the storage account. This exposes data in transit to interception and tampering by attackers, enabling credential theft and data exfiltration. The blast radius includes all data and credentials sent over HTTP to this storage account.",Set 'supportsHttpsTrafficOnly' to true to enforce HTTPS-only access. Update the configuration to: supportsHttpsTrafficOnly: true.,,,,ai_analysis,,Validated
storage_demo.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,17.0,"The 'allowBlobPublicAccess' property is set to true, enabling public anonymous access to blob data. Attackers can access and exfiltrate any data stored in public containers without authentication, leading to data exposure and loss of confidentiality. The blast radius includes all blob data in containers with public access enabled.",Set 'allowBlobPublicAccess' to false to disable anonymous public access. Update the configuration to: allowBlobPublicAccess: false.,,,,ai_analysis,,Validated
storage_demo.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,40.0,"The 'networkAcls.defaultAction' property is set to 'Allow', which permits all network traffic (including from the public internet) to access the storage account unless explicitly denied. This exposes the storage account to unauthorized access, brute-force attacks, and data exfiltration. The blast radius includes all data and services exposed via the storage account's public endpoints.","Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only trusted networks or private endpoints. Update the configuration to: networkAcls: { defaultAction: 'Deny', ... }.",,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 7,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 0,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T15:53:08.608450,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
