Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P1-Identity-HIGH,Identity Management,HIGH,IM-1,template.json,217,App Service configuration for 'onefuzz-daily-ui' does not explicitly integrate with Azure Active Directory for identity management. Leveraging Azure AD is required for secure identity and access management.,Integrate App Service authentication with Azure Active Directory to enforce secure identity management.,N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-2,template.json,217,App Service configuration for 'onefuzz-daily-ui' does not enforce Multi-Factor Authentication (MFA) for users and administrators. MFA is required for secure access.,Configure Azure AD authentication for the App Service and enforce MFA for all users and administrators.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,128,"App Service 'onefuzz-daily-ui' has 'publicNetworkAccess' set to 'Enabled', exposing the application to the public internet. This increases the attack surface and violates the requirement to secure all public endpoints.",Set 'publicNetworkAccess' to 'Disabled' and use private endpoints or access restrictions to minimize public exposure.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,217,"App Service configuration for 'onefuzz-daily-ui' has 'publicNetworkAccess' set to 'Enabled', exposing the application to the public internet. This increases the attack surface and violates the requirement to secure all public endpoints.",Set 'publicNetworkAccess' to 'Disabled' in the site config and use private endpoints or access restrictions to minimize public exposure.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-3,template.json,236,"App Service 'onefuzz-daily-ui' has 'ipSecurityRestrictions' allowing 'Any' IP address with action 'Allow', which permits unrestricted inbound access. This violates the requirement to control inbound and outbound traffic using NSGs.",Restrict 'ipSecurityRestrictions' to only allow trusted IP ranges and deny all others to limit exposure.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-3,template.json,245,"App Service 'onefuzz-daily-ui' has 'scmIpSecurityRestrictions' allowing 'Any' IP address with action 'Allow', which permits unrestricted inbound access to the SCM endpoint. This violates the requirement to control inbound and outbound traffic using NSGs.",Restrict 'scmIpSecurityRestrictions' to only allow trusted IP ranges and deny all others to limit exposure.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,128,"App Service 'onefuzz-daily-ui' does not use private endpoints, exposing the resource to the public internet. Using private endpoints is recommended to securely access resources.",Configure a private endpoint for the App Service to restrict access to trusted networks only.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,217,App Service configuration for 'onefuzz-daily-ui' does not specify encryption at rest or use of customer-managed keys. This violates the requirement to enable encryption at rest for all data storage.,Enable encryption at rest for App Service and configure customer-managed keys if required for compliance.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,61,"App Service 'onefuzz-daily-ui' has 'sslState' set to 'Disabled' for hostName 'onefuzz-daily-ui.azurewebsites.net', which allows unencrypted HTTP traffic. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates entries to enforce HTTPS and ensure encryption in transit.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,69,"App Service 'onefuzz-daily-ui' has 'sslState' set to 'Disabled' for hostName 'onefuzz-daily-ui.scm.azurewebsites.net', which allows unencrypted HTTP traffic. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates entries to enforce HTTPS and ensure encryption in transit.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,217,"App Service configuration for 'onefuzz-daily-ui' includes a 'publishingUsername' property, which may expose sensitive information if not securely managed. Sensitive data should be stored in Azure Key Vault.",Remove hardcoded sensitive information from the configuration and reference secrets from Azure Key Vault using managed identities.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-5,template.json,217,App Service configuration for 'onefuzz-daily-ui' does not specify backup and recovery settings. Implementing backup and recovery is recommended for critical data.,Configure regular backups for the App Service and ensure recovery procedures are in place.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-6,template.json,217,App Service configuration for 'onefuzz-daily-ui' does not specify the use of customer-managed keys (CMK) for encryption. Using CMK is recommended for sensitive data.,Configure App Service to use customer-managed keys for encryption at rest if required by your data protection policies.,N/A,AI,Generic
