#!/usr/bin/env python

import unittest
import os
import shutil
from pathlib import Path

def setup_test_environment():
    """Set up the test environment with required configurations"""
    # Set environment variables for testing
    os.environ["ENABLE_PARAMETER_EXPANSION"] = "true"
    os.environ["MATCHING_STRATEGY"] = "smart"
    os.environ["MATCH_BY_SUFFIX"] = "true"
    os.environ["ANALYZE_PARAM_FILES"] = "true"
    os.environ["COMBINE_FINDINGS"] = "true"
    os.environ["TEMPLATE_IDENTIFIERS"] = "Template,template,main,deploy,ArmTemplate"
    os.environ["PARAMETER_IDENTIFIERS"] = "Param,Parameter,params,parameters,ArmParam"

def cleanup_test_files():
    """Clean up test files after running tests"""
    test_dir = Path("test-files")
    if test_dir.exists():
        shutil.rmtree(test_dir)

if __name__ == '__main__':
    try:
        # Set up test environment
        setup_test_environment()

        # Discover and run tests
        test_loader = unittest.TestLoader()
        tests = test_loader.discover('.', pattern='test_*.py')
        
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(tests)

        # Check if all tests passed
        if result.wasSuccessful():
            print("\n✅ All tests passed successfully!")
        else:
            print("\n❌ Some tests failed.")
            exit(1)

    finally:
        # Clean up test files
        cleanup_test_files()
