"""
Azure Security Benchmark Focused Patterns
Implements security patterns for specific control domains:
- IM: Identity Management (IM-1 to IM-9)
- NS: Network Security (NS-2 to NS-10)
- AM: Access Management (AM-1 to AM-5)
- DP: Data Protection (DP-1 to DP-8)
- PA: Posture and Vulnerability Assessment (PA-1 to PA-8)
- PV: Platform and Workload Protection (PV-1 to PV-7)
"""

import re
import logging
from dataclasses import dataclass, field
from typing import Dict, List, Pattern, Optional
from enum import Enum
from pathlib import Path

logger = logging.getLogger(__name__)

class Severity(Enum):
    """Security issue severity levels."""
    CRITICAL = "CRITICAL"
    HIGH = "HIGH"
    MEDIUM = "MEDIUM"
    LOW = "LOW"

@dataclass
class SecurityPattern:
    """Represents a security pattern to detect."""
    pattern: Pattern[str]
    severity: Severity
    control_id: str
    control_domain: str
    description: str
    remediation: str
    resource_types: List[str] = field(default_factory=list)
    file_extensions: List[str] = field(default_factory=list)

# ============================================================================
# IDENTITY MANAGEMENT (IM) PATTERNS
# ============================================================================

class IdentityManagementPatterns:
    """Security patterns for Identity Management controls (IM-1 to IM-9)."""
    
    @staticmethod
    def get_patterns() -> List[SecurityPattern]:
        return [
            # IM-1: Use centralized identity and authentication system
            SecurityPattern(
                pattern=re.compile(r"admin_enabled\s*=\s*false", re.IGNORECASE),
                severity=Severity.HIGH,
                control_id="IM-1",
                control_domain="Identity Management",
                description="Azure AD authentication is disabled. Use centralized identity management.",
                remediation="Enable Azure AD authentication by setting admin_enabled = true",
                resource_types=["SQL", "AppService", "Container"],
                file_extensions=[".tf", ".bicep"]
            ),
            
            # IM-2: Use managed identities
            SecurityPattern(
                pattern=re.compile(r"identity\s*{\s*type\s*=\s*[\"']None[\"']", re.IGNORECASE),
                severity=Severity.MEDIUM,
                control_id="IM-2",
                control_domain="Identity Management",
                description="Resource is not using managed identity for authentication.",
                remediation="Configure managed identity by setting type = 'SystemAssigned' or 'UserAssigned'",
                resource_types=["Compute", "AppService", "Container", "DataFactory"],
                file_extensions=[".tf"]
            ),
            
            # IM-3: Protect identity credentials
            SecurityPattern(
                pattern=re.compile(r"(client_id|client_secret|tenant_id)\s*=\s*[\"'][^\"']+[\"']", re.IGNORECASE),
                severity=Severity.CRITICAL,
                control_id="IM-3",
                control_domain="Identity Management",
                description="Service principal credentials are hardcoded in configuration.",
                remediation="Store credentials in Azure Key Vault and use managed identity to access them",
                resource_types=[],  # Applies to all
                file_extensions=[]
            ),
            
            # IM-4: Use strong authentication methods
            SecurityPattern(
                pattern=re.compile(r"password_authentication\s*=\s*true", re.IGNORECASE),
                severity=Severity.MEDIUM,
                control_id="IM-4",
                control_domain="Identity Management",
                description="Password authentication is enabled instead of key-based authentication.",
                remediation="Disable password authentication and use SSH keys or certificates",
                resource_types=["Compute"],
                file_extensions=[".tf"]
            ),
            
            # IM-5: Monitor and audit identity activities
            SecurityPattern(
                pattern=re.compile(r"audit_log_enabled\s*=\s*false", re.IGNORECASE),
                severity=Severity.MEDIUM,
                control_id="IM-5",
                control_domain="Identity Management",
                description="Identity audit logging is disabled.",
                remediation="Enable audit logging to monitor identity-related activities",
                resource_types=["SQL", "KeyVault", "Storage"],
                file_extensions=[".tf", ".bicep"]
            ),
            
            # IM-7: Protect access to critical resources
            SecurityPattern(
                pattern=re.compile(r"enable_rbac_authorization\s*=\s*false", re.IGNORECASE),
                severity=Severity.HIGH,
                control_id="IM-7",
                control_domain="Identity Management",
                description="RBAC authorization is disabled for critical resources.",
                remediation="Enable RBAC authorization to control access based on roles",
                resource_types=["KeyVault", "Container", "Storage"],
                file_extensions=[".tf", ".bicep"]
            ),
        ]

# ============================================================================
# NETWORK SECURITY (NS) PATTERNS
# ============================================================================

class NetworkSecurityPatterns:
    """Security patterns for Network Security controls (NS-2 to NS-10)."""
    
    @staticmethod
    def get_patterns() -> List[SecurityPattern]:
        return [
            # NS-2: Secure cloud services with network controls
            SecurityPattern(
                pattern=re.compile(r"public_network_access_enabled\s*=\s*true", re.IGNORECASE),
                severity=Severity.HIGH,
                control_id="NS-2",
                control_domain="Network Security",
                description="Public network access is enabled, exposing resources to the internet.",
                remediation="Disable public network access and use private endpoints",
                resource_types=["Storage", "SQL", "KeyVault", "CosmosDB"],
                file_extensions=[".tf", ".bicep"]
            ),
            
            # NS-3: Deploy dedicated private endpoints
            SecurityPattern(
                pattern=re.compile(r"private_endpoint_connections\s*=\s*\[\s*\]", re.IGNORECASE),
                severity=Severity.MEDIUM,
                control_id="NS-3",
                control_domain="Network Security",
                description="No private endpoints configured for the resource.",
                remediation="Configure private endpoints for secure connectivity",
                resource_types=["Storage", "SQL", "KeyVault", "AppService"],
                file_extensions=[".tf", ".bicep"]
            ),
            
            # NS-4: Deploy network security groups
            SecurityPattern(
                pattern=re.compile(r"network_security_group_id\s*=\s*(null|\"\")", re.IGNORECASE),
                severity=Severity.HIGH,
                control_id="NS-4",
                control_domain="Network Security",
                description="No Network Security Group associated with the subnet or network interface.",
                remediation="Associate a properly configured NSG with the resource",
                resource_types=["Network", "Compute"],
                file_extensions=[".tf"]
            ),
            
            # NS-5: Deploy DDoS protection
            SecurityPattern(
                pattern=re.compile(r"enable_ddos_protection\s*=\s*false", re.IGNORECASE),
                severity=Severity.MEDIUM,
                control_id="NS-5",
                control_domain="Network Security",
                description="DDoS protection is disabled for the virtual network.",
                remediation="Enable DDoS protection for internet-facing resources",
                resource_types=["Network"],
                file_extensions=[".tf", ".bicep"]
            ),
            
            # NS-6: Deploy web application firewall
            SecurityPattern(
                pattern=re.compile(r"waf_configuration\s*{\s*enabled\s*=\s*false", re.IGNORECASE),
                severity=Severity.HIGH,
                control_id="NS-6",
                control_domain="Network Security",
                description="Web Application Firewall is disabled.",
                remediation="Enable WAF to protect web applications from common attacks",
                resource_types=["Network", "AppService"],
                file_extensions=[".tf"]
            ),
            
            # NS-7: Simplify network connectivity
            SecurityPattern(
                pattern=re.compile(r"source_address_prefix\s*=\s*[\"']\*[\"']", re.IGNORECASE),
                severity=Severity.HIGH,
                control_id="NS-7",
                control_domain="Network Security",
                description="NSG rule allows traffic from any source (*).",
                remediation="Restrict source addresses to specific IP ranges or service tags",
                resource_types=["Network"],
                file_extensions=[".tf", ".bicep"]
            ),
            
            # NS-9: Ensure secure access to virtual networks
            SecurityPattern(
                pattern=re.compile(r"allow_virtual_network_access\s*=\s*true.*allow_forwarded_traffic\s*=\s*true", re.IGNORECASE | re.DOTALL),
                severity=Severity.MEDIUM,
                control_id="NS-9",
                control_domain="Network Security",
                description="Virtual network peering allows forwarded traffic without restrictions.",
                remediation="Review and restrict forwarded traffic in VNet peering configurations",
                resource_types=["Network"],
                file_extensions=[".tf", ".bicep"]
            ),
        ]

# ============================================================================
# ACCESS MANAGEMENT (AM) PATTERNS
# ============================================================================

class AccessManagementPatterns:
    """Security patterns for Access Management controls (AM-1 to AM-5)."""
    
    @staticmethod
    def get_patterns() -> List[SecurityPattern]:
        return [
            # AM-1: Control access based on criticality
            SecurityPattern(
                pattern=re.compile(r"bypass\s*=\s*[\"']AzureServices[\"']", re.IGNORECASE),
                severity=Severity.MEDIUM,
                control_id="AM-1",
                control_domain="Access Management",
                description="Network rules bypass Azure services without proper access control.",
                remediation="Implement specific access controls instead of broad bypasses",
                resource_types=["Storage", "KeyVault"],
                file_extensions=[".tf", ".bicep"]
            ),
            
            # AM-2: Use only approved applications
            SecurityPattern(
                pattern=re.compile(r"allowed_applications\s*=\s*\[\s*[\"']\*[\"']\s*\]", re.IGNORECASE),
                severity=Severity.HIGH,
                control_id="AM-2",
                control_domain="Access Management",
                description="All applications are allowed without restriction.",
                remediation="Specify a list of approved applications only",
                resource_types=["AppService", "Container"],
                file_extensions=[".tf", ".bicep"]
            ),
            
            # AM-3: Manage lifecycle of identities and access
            SecurityPattern(
                pattern=re.compile(r"access_policy\s*{[^}]*expiry_date\s*=\s*null", re.IGNORECASE | re.DOTALL),
                severity=Severity.MEDIUM,
                control_id="AM-3",
                control_domain="Access Management",
                description="Access policy has no expiration date set.",
                remediation="Set expiration dates for access policies to enforce lifecycle management",
                resource_types=["KeyVault", "Storage"],
                file_extensions=[".tf"]
            ),
            
            # AM-4: Limit access to critical assets
            SecurityPattern(
                pattern=re.compile(r"default_action\s*=\s*[\"']Allow[\"']", re.IGNORECASE),
                severity=Severity.HIGH,
                control_id="AM-4",
                control_domain="Access Management",
                description="Default network action allows all traffic to critical assets.",
                remediation="Set default_action = 'Deny' and explicitly allow only required access",
                resource_types=["Storage", "KeyVault", "SQL"],
                file_extensions=[".tf", ".bicep"]
            ),
            
            # AM-5: Monitor and alert on account anomalies
            SecurityPattern(
                pattern=re.compile(r"threat_detection_policy\s*{\s*state\s*=\s*[\"']Disabled[\"']", re.IGNORECASE),
                severity=Severity.MEDIUM,
                control_id="AM-5",
                control_domain="Access Management",
                description="Threat detection for account anomalies is disabled.",
                remediation="Enable threat detection to monitor for suspicious activities",
                resource_types=["SQL", "Storage"],
                file_extensions=[".tf", ".bicep"]
            ),
        ]

# ============================================================================
# DATA PROTECTION (DP) PATTERNS
# ============================================================================

class DataProtectionPatterns:
    """Security patterns for Data Protection controls (DP-1 to DP-8)."""
    
    @staticmethod
    def get_patterns() -> List[SecurityPattern]:
        return [
            # DP-1: Discovery, classification and labeling of sensitive data
            SecurityPattern(
                pattern=re.compile(r"data_classification_enabled\s*=\s*false", re.IGNORECASE),
                severity=Severity.MEDIUM,
                control_id="DP-1",
                control_domain="Data Protection",
                description="Data classification is disabled for sensitive data.",
                remediation="Enable data classification to identify and protect sensitive information",
                resource_types=["SQL", "Storage"],
                file_extensions=[".tf", ".bicep"]
            ),
            
            # DP-2: Monitor anomalies and threats targeting sensitive data
            SecurityPattern(
                pattern=re.compile(r"advanced_threat_protection_enabled\s*=\s*false", re.IGNORECASE),
                severity=Severity.HIGH,
                control_id="DP-2",
                control_domain="Data Protection",
                description="Advanced threat protection is disabled for data resources.",
                remediation="Enable ATP to monitor threats targeting sensitive data",
                resource_types=["SQL", "Storage", "CosmosDB"],
                file_extensions=[".tf", ".bicep"]
            ),
            
            # DP-3: Encrypt sensitive data in transit
            SecurityPattern(
                pattern=re.compile(r"(enable_https_traffic_only|supportsHttpsTrafficOnly)\s*[=:]\s*false", re.IGNORECASE),
                severity=Severity.HIGH,
                control_id="DP-3",
                control_domain="Data Protection",
                description="HTTPS-only traffic is not enforced, allowing unencrypted data in transit.",
                remediation="Enable HTTPS-only traffic to ensure encryption in transit",
                resource_types=["Storage", "AppService"],
                file_extensions=[".tf", ".bicep", ".json"]
            ),
            
            # DP-4: Enable data at rest encryption by default
            SecurityPattern(
                pattern=re.compile(r"encryption_at_rest_enabled\s*=\s*false", re.IGNORECASE),
                severity=Severity.HIGH,
                control_id="DP-4",
                control_domain="Data Protection",
                description="Encryption at rest is disabled.",
                remediation="Enable encryption at rest for all data storage resources",
                resource_types=["Storage", "SQL", "CosmosDB"],
                file_extensions=[".tf", ".bicep"]
            ),
            
            # DP-5: Use customer-managed keys when required
            SecurityPattern(
                pattern=re.compile(r"customer_managed_key\s*{\s*enabled\s*=\s*false", re.IGNORECASE),
                severity=Severity.MEDIUM,
                control_id="DP-5",
                control_domain="Data Protection",
                description="Customer-managed keys are not used for encryption.",
                remediation="Enable customer-managed keys for enhanced control over encryption",
                resource_types=["Storage", "SQL", "KeyVault"],
                file_extensions=[".tf", ".bicep"]
            ),
            
            # DP-6: Use secure key and certificate management
            SecurityPattern(
                pattern=re.compile(r"(soft_delete_enabled|enableSoftDelete)\s*[=:]\s*false", re.IGNORECASE),
                severity=Severity.HIGH,
                control_id="DP-6",
                control_domain="Data Protection",
                description="Soft delete is disabled for key/secret management.",
                remediation="Enable soft delete to protect against accidental deletion",
                resource_types=["KeyVault"],
                file_extensions=[".tf", ".bicep", ".json"]
            ),
            
            # DP-7: Use secure methods for data residual handling
            SecurityPattern(
                pattern=re.compile(r"secure_delete_enabled\s*=\s*false", re.IGNORECASE),
                severity=Severity.MEDIUM,
                control_id="DP-7",
                control_domain="Data Protection",
                description="Secure delete is not enabled for data residual handling.",
                remediation="Enable secure delete to ensure proper data disposal",
                resource_types=["Storage", "Compute"],
                file_extensions=[".tf", ".bicep"]
            ),
            
            # DP-8: Ensure security of data lifecycle management
            SecurityPattern(
                pattern=re.compile(r"backup_retention_days\s*=\s*0", re.IGNORECASE),
                severity=Severity.MEDIUM,
                control_id="DP-8",
                control_domain="Data Protection",
                description="No backup retention policy configured.",
                remediation="Configure appropriate backup retention period based on requirements",
                resource_types=["SQL", "Storage", "CosmosDB"],
                file_extensions=[".tf", ".bicep"]
            ),
        ]

# ============================================================================
# POSTURE AND VULNERABILITY ASSESSMENT (PA) PATTERNS
# ============================================================================

class PostureAssessmentPatterns:
    """Security patterns for Posture and Vulnerability Assessment controls (PA-1 to PA-8)."""
    
    @staticmethod
    def get_patterns() -> List[SecurityPattern]:
        return [
            # PA-1: Monitor all Azure resources
            SecurityPattern(
                pattern=re.compile(r"diagnostic_settings\s*=\s*\[\s*\]", re.IGNORECASE),
                severity=Severity.MEDIUM,
                control_id="PA-1",
                control_domain="Posture and Vulnerability Assessment",
                description="No diagnostic settings configured for monitoring.",
                remediation="Configure diagnostic settings to monitor resource activities",
                resource_types=[],  # Applies to all
                file_extensions=[".tf", ".bicep"]
            ),
            
            # PA-2: Enable vulnerability scanning
            SecurityPattern(
                pattern=re.compile(r"vulnerability_assessment\s*{\s*enabled\s*=\s*false", re.IGNORECASE),
                severity=Severity.HIGH,
                control_id="PA-2",
                control_domain="Posture and Vulnerability Assessment",
                description="Vulnerability assessment is disabled.",
                remediation="Enable vulnerability assessment for continuous security scanning",
                resource_types=["SQL", "Compute", "Container"],
                file_extensions=[".tf", ".bicep"]
            ),
            
            # PA-3: Enable automated patching
            SecurityPattern(
                pattern=re.compile(r"automatic_os_upgrade\s*=\s*false", re.IGNORECASE),
                severity=Severity.MEDIUM,
                control_id="PA-3",
                control_domain="Posture and Vulnerability Assessment",
                description="Automated OS patching is disabled.",
                remediation="Enable automatic OS upgrades to ensure timely security patches",
                resource_types=["Compute"],
                file_extensions=[".tf", ".bicep"]
            ),
            
            # PA-4: Enable security configurations
            SecurityPattern(
                pattern=re.compile(r"security_rule\s*{\s*access\s*=\s*[\"']Allow[\"'].*priority\s*=\s*100", re.IGNORECASE | re.DOTALL),
                severity=Severity.HIGH,
                control_id="PA-4",
                control_domain="Posture and Vulnerability Assessment",
                description="High-priority allow rule may override security configurations.",
                remediation="Review and adjust security rule priorities to maintain proper security posture",
                resource_types=["Network"],
                file_extensions=[".tf", ".bicep"]
            ),
            
            # PA-6: Use automated tools to monitor configuration
            SecurityPattern(
                pattern=re.compile(r"auto_provisioning_enabled\s*=\s*false", re.IGNORECASE),
                severity=Severity.MEDIUM,
                control_id="PA-6",
                control_domain="Posture and Vulnerability Assessment",
                description="Automated provisioning of monitoring agents is disabled.",
                remediation="Enable auto-provisioning for consistent monitoring coverage",
                resource_types=["Compute", "Container"],
                file_extensions=[".tf", ".bicep"]
            ),
            
            # PA-7: Review and reconcile access regularly
            SecurityPattern(
                pattern=re.compile(r"access_review_enabled\s*=\s*false", re.IGNORECASE),
                severity=Severity.MEDIUM,
                control_id="PA-7",
                control_domain="Posture and Vulnerability Assessment",
                description="Access review is not enabled for the resource.",
                remediation="Enable access reviews to regularly audit and reconcile permissions",
                resource_types=["KeyVault", "Storage", "SQL"],
                file_extensions=[".tf", ".bicep"]
            ),
            
            # PA-8: Choose Azure Policy for governance
            SecurityPattern(
                pattern=re.compile(r"policy_assignment\s*{\s*enabled\s*=\s*false", re.IGNORECASE),
                severity=Severity.LOW,
                control_id="PA-8",
                control_domain="Posture and Vulnerability Assessment",
                description="Azure Policy assignments are disabled.",
                remediation="Enable Azure Policy assignments for consistent governance",
                resource_types=[],  # Applies to all
                file_extensions=[".tf", ".bicep"]
            ),
        ]

# ============================================================================
# PLATFORM AND WORKLOAD PROTECTION (PV) PATTERNS
# ============================================================================

class PlatformProtectionPatterns:
    """Security patterns for Platform and Workload Protection controls (PV-1 to PV-7)."""
    
    @staticmethod
    def get_patterns() -> List[SecurityPattern]:
        return [
            # PV-1: Establish secure configurations
            SecurityPattern(
                pattern=re.compile(r"security_baseline_enabled\s*=\s*false", re.IGNORECASE),
                severity=Severity.HIGH,
                control_id="PV-1",
                control_domain="Platform and Workload Protection",
                description="Security baseline configurations are not enabled.",
                remediation="Enable security baseline to establish secure configurations",
                resource_types=["Compute", "Container", "AppService"],
                file_extensions=[".tf", ".bicep"]
            ),
            
            # PV-2: Apply secure configurations
            SecurityPattern(
                pattern=re.compile(r"os_disk\s*{\s*caching\s*=\s*[\"']None[\"']", re.IGNORECASE),
                severity=Severity.LOW,
                control_id="PV-2",
                control_domain="Platform and Workload Protection",
                description="OS disk caching is disabled, may impact security monitoring performance.",
                remediation="Enable appropriate caching for OS disks to support security operations",
                resource_types=["Compute"],
                file_extensions=[".tf", ".bicep"]
            ),
            
            # PV-3: Establish secure Azure service configurations
            SecurityPattern(
                pattern=re.compile(r"minimum_tls_version\s*=\s*[\"']1\.[01][\"']", re.IGNORECASE),
                severity=Severity.HIGH,
                control_id="PV-3",
                control_domain="Platform and Workload Protection",
                description="Service is using outdated TLS version.",
                remediation="Set minimum TLS version to 1.2 or higher",
                resource_types=["Storage", "SQL", "AppService"],
                file_extensions=[".tf", ".bicep"]
            ),
            
            # PV-4: Secure authentication to Azure services
            SecurityPattern(
                pattern=re.compile(r"disable_local_auth\s*=\s*false", re.IGNORECASE),
                severity=Severity.MEDIUM,
                control_id="PV-4",
                control_domain="Platform and Workload Protection",
                description="Local authentication is enabled alongside Azure AD.",
                remediation="Disable local authentication and use Azure AD exclusively",
                resource_types=["Storage", "EventHub", "ServiceBus"],
                file_extensions=[".tf", ".bicep"]
            ),
            
            # PV-5: Secure deployment processes
            SecurityPattern(
                pattern=re.compile(r"deployment_mode\s*=\s*[\"']Incremental[\"']", re.IGNORECASE),
                severity=Severity.LOW,
                control_id="PV-5",
                control_domain="Platform and Workload Protection",
                description="Using incremental deployment mode which may leave orphaned resources.",
                remediation="Consider using Complete mode for controlled deployments",
                resource_types=[],  # Deployment configuration
                file_extensions=[".tf", ".bicep"]
            ),
            
            # PV-6: Rapidly deploy updates
            SecurityPattern(
                pattern=re.compile(r"enable_auto_scaling\s*=\s*false", re.IGNORECASE),
                severity=Severity.MEDIUM,
                control_id="PV-6",
                control_domain="Platform and Workload Protection",
                description="Auto-scaling is disabled, limiting rapid update deployment.",
                remediation="Enable auto-scaling to support rapid updates and patches",
                resource_types=["AppService", "Container", "Compute"],
                file_extensions=[".tf", ".bicep"]
            ),
            
            # PV-7: Backup and recovery
            SecurityPattern(
                pattern=re.compile(r"backup_enabled\s*=\s*false", re.IGNORECASE),
                severity=Severity.HIGH,
                control_id="PV-7",
                control_domain="Platform and Workload Protection",
                description="Backup is not enabled for the resource.",
                remediation="Enable backup to ensure recovery capabilities",
                resource_types=["SQL", "Storage", "Compute", "AppService"],
                file_extensions=[".tf", ".bicep"]
            ),
        ]

# ============================================================================
# MAIN COMPOSITE DETECTOR
# ============================================================================

class AzureSecurityBenchmarkDetector:
    """Main detector that combines all Azure Security Benchmark patterns."""
    
    def __init__(self):
        self.pattern_groups = {
            "Identity Management": IdentityManagementPatterns.get_patterns(),
            "Network Security": NetworkSecurityPatterns.get_patterns(),
            "Access Management": AccessManagementPatterns.get_patterns(),
            "Data Protection": DataProtectionPatterns.get_patterns(),
            "Posture and Vulnerability Assessment": PostureAssessmentPatterns.get_patterns(),
            "Platform and Workload Protection": PlatformProtectionPatterns.get_patterns()
        }
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
    def detect_issues(self, file_path: str, content: str, resource_type: str) -> List[Dict]:
        """Detect security issues based on Azure Security Benchmark patterns."""
        findings = []
        file_extension = Path(file_path).suffix.lower()
        
        # Normalize line endings
        content = content.replace('\r\n', '\n')
        lines = content.split('\n')
        
        # Check all pattern groups
        for domain, patterns in self.pattern_groups.items():
            for pattern in patterns:
                # Check if pattern applies to this file
                if not self._pattern_applies(pattern, resource_type, file_extension):
                    continue
                
                # Find all matches
                for match in pattern.pattern.finditer(content):
                    line_number = content[:match.start()].count('\n') + 1
                    
                    # Get the actual line content
                    if 0 <= line_number - 1 < len(lines):
                        matching_line = lines[line_number - 1].strip()
                        
                        finding = {
                            "file_path": file_path,
                            "line": line_number,
                            "severity": pattern.severity.value,
                            "control_id": pattern.control_id,
                            "control_domain": pattern.control_domain,
                            "description": pattern.description,
                            "remediation": pattern.remediation,
                            "matching_content": matching_line,
                            "resource_type": resource_type
                        }
                        findings.append(finding)
                        
                        self.logger.debug(
                            f"Found {pattern.severity.value} issue for {pattern.control_id} "
                            f"in {file_path}:{line_number}"
                        )
        
        # Sort by control domain and severity
        severity_order = {
            Severity.CRITICAL: 0,
            Severity.HIGH: 1,
            Severity.MEDIUM: 2,
            Severity.LOW: 3
        }
        
        findings.sort(key=lambda f: (
            f["control_domain"],
            severity_order.get(Severity[f["severity"]], 4),
            f["control_id"]
        ))
        
        return findings
    
    def _pattern_applies(self, pattern: SecurityPattern, resource_type: str, file_extension: str) -> bool:
        """Check if pattern applies to given resource type and file."""
        # If no restrictions, pattern applies to all
        if not pattern.resource_types and not pattern.file_extensions:
            return True
        
        # Check resource type match
        resource_match = not pattern.resource_types or resource_type in pattern.resource_types
        
        # Check file extension match
        extension_match = not pattern.file_extensions or file_extension in pattern.file_extensions
        
        return resource_match and extension_match
    
    def get_summary_by_domain(self, findings: List[Dict]) -> Dict[str, Dict]:
        """Get summary of findings grouped by control domain."""
        summary = {}
        
        for finding in findings:
            domain = finding["control_domain"]
            if domain not in summary:
                summary[domain] = {
                    "total": 0,
                    "by_severity": {"CRITICAL": 0, "HIGH": 0, "MEDIUM": 0, "LOW": 0},
                    "control_ids": set()
                }
            
            summary[domain]["total"] += 1
            summary[domain]["by_severity"][finding["severity"]] += 1
            summary[domain]["control_ids"].add(finding["control_id"])
        
        # Convert sets to lists for JSON serialization
        for domain in summary:
            summary[domain]["control_ids"] = sorted(list(summary[domain]["control_ids"]))
        
        return summary

# Example usage
if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(level=logging.DEBUG)
    
    # Example Terraform content with various security issues
    terraform_content = """
    resource "azurerm_storage_account" "example" {
        name                     = "examplestorage"
        resource_group_name      = azurerm_resource_group.example.name
        location                 = azurerm_resource_group.example.location
        account_tier             = "Standard"
        account_replication_type = "LRS"
        
        # DP-3 violation
        enable_https_traffic_only = false
        
        # PV-3 violation
        minimum_tls_version = "TLS1_0"
        
        # NS-2 violation
        public_network_access_enabled = true
        
        # AM-4 violation
        network_rules {
            default_action = "Allow"
        }
        
        # IM-3 violation
        client_id = "12345-67890-abcdef"
        
        # PA-1 violation
        diagnostic_settings = []
    }
    
    resource "azurerm_key_vault" "example" {
        name                = "examplekv"
        location            = azurerm_resource_group.example.location
        resource_group_name = azurerm_resource_group.example.name
        sku_name           = "standard"
        tenant_id          = data.azurerm_client_config.current.tenant_id
        
        # DP-6 violation
        soft_delete_enabled = false
        
        # IM-7 violation
        enable_rbac_authorization = false
    }
    """
    
    # Create detector and analyze
    detector = AzureSecurityBenchmarkDetector()
    findings = detector.detect_issues(
        file_path="example.tf",
        content=terraform_content,
        resource_type="Storage"
    )
    
    # Print findings
    print(f"\nFound {len(findings)} security issues:\n")
    for finding in findings:
        print(f"{finding['severity']} - {finding['control_id']} ({finding['control_domain']})")
        print(f"  File: {finding['file_path']}:{finding['line']}")
        print(f"  Issue: {finding['description']}")
        print(f"  Code: {finding['matching_content']}")
        print(f"  Fix: {finding['remediation']}")
        print()
    
    # Print summary
    summary = detector.get_summary_by_domain(findings)
    print("\nSummary by Control Domain:")
    for domain, stats in summary.items():
        print(f"\n{domain}:")
        print(f"  Total issues: {stats['total']}")
        print(f"  By severity: {stats['by_severity']}")
        print(f"  Controls affected: {', '.join(stats['control_ids'])}")
