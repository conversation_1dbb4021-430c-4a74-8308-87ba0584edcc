Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P6-Other-CRITICAL,Unknown,CRITICAL,NS-2,template.json,120,"App Service siteConfig.ipSecurityRestrictions allows all IP addresses (ipAddress: 'Any', action: 'Allow'), which exposes the application to unrestricted public access.",Restrict ipSecurityRestrictions to only allow trusted IP ranges. Remove or replace the rule with 'ipAddress': 'Any' and define specific allowed IP addresses or ranges.,N/A,AI,Generic
P6-Other-CRITICAL,Unknown,CRITICAL,NS-2,template.json,130,"App Service siteConfig.scmIpSecurityRestrictions allows all IP addresses (ipAddress: 'Any', action: 'Allow'), which exposes the SCM (deployment) endpoint to unrestricted public access.",Restrict scmIpSecurityRestrictions to only allow trusted IP ranges. Remove or replace the rule with 'ipAddress': 'Any' and define specific allowed IP addresses or ranges.,N/A,AI,Generic
P6-Other-<PERSON>IG<PERSON>,Unknown,HIGH,DP-2,template.json,110,"App Service siteConfig.phpVersion is set to '5.6', which is an outdated and unsupported PHP version, increasing the risk of vulnerabilities.","Upgrade phpVersion to a supported and secure version (e.g., '8.0' or later) in siteConfig.",N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,DP-2,template.json,112,"App Service siteConfig.netFrameworkVersion is set to 'v4.0', which may be unsupported or lack security updates.",Upgrade netFrameworkVersion to the latest supported version in siteConfig.,N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,NS-2,template.json,80,"App Service properties.publicNetworkAccess is set to 'Enabled', allowing public network access to the application.",Set properties.publicNetworkAccess to 'Disabled' to restrict public network access and require private endpoints or VNet integration.,N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,NS-2,template.json,117,"App Service siteConfig.publicNetworkAccess is set to 'Enabled', allowing public network access to the application.",Set siteConfig.publicNetworkAccess to 'Disabled' to restrict public network access and require private endpoints or VNet integration.,N/A,AI,Generic
P6-Other-MEDIUM,Unknown,MEDIUM,DP-6,template.json,114,"App Service siteConfig.requestTracingEnabled is set to false, which may reduce the ability to trace requests for security investigations.",Enable requestTracingEnabled in siteConfig to support security investigations and troubleshooting.,N/A,AI,Generic
P6-Other-MEDIUM,Unknown,MEDIUM,DP-6,template.json,116,"App Service siteConfig.httpLoggingEnabled is set to false, which disables HTTP request logging and may hinder security monitoring and incident response.",Enable httpLoggingEnabled in siteConfig to ensure HTTP request logs are collected for monitoring and auditing.,N/A,AI,Generic
P6-Other-MEDIUM,Unknown,MEDIUM,DP-6,template.json,118,"App Service siteConfig.detailedErrorLoggingEnabled is set to false, which may limit diagnostic capabilities for troubleshooting security incidents.",Enable detailedErrorLoggingEnabled in siteConfig to improve diagnostic capabilities.,N/A,AI,Generic
P6-Other-LOW,Unknown,LOW,NS-3,template.json,88,"App Service properties.siteConfig.http20Enabled is set to false, which disables HTTP/2 and may reduce security and performance benefits.",Enable properties.siteConfig.http20Enabled to support HTTP/2 for improved security and performance.,N/A,AI,Generic
P6-Other-LOW,Unknown,LOW,NS-3,template.json,108,"App Service siteConfig.http20Enabled is set to false, which disables HTTP/2 and may reduce security and performance benefits.",Enable http20Enabled in siteConfig to support HTTP/2 for improved security and performance.,N/A,AI,Generic
