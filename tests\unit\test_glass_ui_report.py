#!/usr/bin/env python3
"""
Test script to generate Glass UI HTML security reports.
Demonstrates the new glassmorphism design with enhanced visual effects.
"""

import os
import sys
from pathlib import Path
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from security_opt import SecurityPRReviewer

def create_sample_findings():
    """Create sample security findings for testing Glass UI report."""
    return [
        {
            "control_id": "IM-1",
            "severity": "CRITICAL",
            "file_path": "main.bicep",
            "line": 45,
            "description": "Azure Key Vault access policy allows overly broad permissions. The current configuration grants 'all' permissions to service principals, which violates the principle of least privilege.",
            "remediation": "Restrict Key Vault access policies to specific required permissions only. Use role-based access control (RBAC) instead of access policies where possible.",
            "domain": "Identity Management"
        },
        {
            "control_id": "NS-2",
            "severity": "HIGH",
            "file_path": "network.bicep",
            "line": 23,
            "description": "Network Security Group allows inbound traffic from any source (0.0.0.0/0) on port 22 (SSH). This creates a significant security risk by exposing SSH access to the entire internet.",
            "remediation": "Restrict SSH access to specific IP ranges or use Azure Bastion for secure remote access. Consider implementing just-in-time (JIT) access for administrative connections.",
            "domain": "Network Security"
        },
        {
            "control_id": "DP-3",
            "severity": "HIGH",
            "file_path": "storage.bicep",
            "line": 67,
            "description": "Storage account is configured without encryption at rest using customer-managed keys. Data is encrypted with Microsoft-managed keys only, which may not meet compliance requirements.",
            "remediation": "Configure customer-managed encryption keys (CMK) using Azure Key Vault. Enable infrastructure encryption for additional security layer.",
            "domain": "Data Protection"
        },
        {
            "control_id": "AM-2",
            "severity": "MEDIUM",
            "file_path": "rbac.bicep",
            "line": 12,
            "description": "Role assignment grants excessive permissions at subscription scope. The 'Contributor' role is assigned when more specific roles would be appropriate.",
            "remediation": "Use principle of least privilege by assigning specific roles like 'Storage Blob Data Contributor' or 'Virtual Machine Contributor' instead of broad 'Contributor' role.",
            "domain": "Access Management"
        },
        {
            "control_id": "LM-1",
            "severity": "MEDIUM",
            "file_path": "monitoring.bicep",
            "line": 89,
            "description": "Diagnostic settings are not configured for critical Azure resources. This limits visibility into security events and compliance monitoring.",
            "remediation": "Enable diagnostic settings for all critical resources. Configure log forwarding to Azure Monitor, Log Analytics workspace, or SIEM solution.",
            "domain": "Logging and Monitoring"
        },
        {
            "control_id": "NS-4",
            "severity": "LOW",
            "file_path": "firewall.bicep",
            "line": 156,
            "description": "Azure Firewall is not configured with threat intelligence-based filtering. This reduces the effectiveness of network-level threat detection.",
            "remediation": "Enable threat intelligence-based filtering on Azure Firewall. Configure appropriate threat intelligence feeds and alerting mechanisms.",
            "domain": "Network Security"
        },
        {
            "control_id": "DP-1",
            "severity": "LOW",
            "file_path": "database.bicep",
            "line": 34,
            "description": "Azure SQL Database is not configured with Advanced Threat Protection. This limits detection of suspicious database activities.",
            "remediation": "Enable Advanced Threat Protection for Azure SQL Database. Configure appropriate alerting and response procedures for detected threats.",
            "domain": "Data Protection"
        }
    ]

def test_glass_ui_report():
    """Test the new Glass UI HTML report generation."""
    print("🎨 Testing Glass UI HTML Report Generation...")
    
    # Create test directory
    test_dir = Path("glass_ui_reports")
    test_dir.mkdir(exist_ok=True)
    
    # Create sample findings
    sample_findings = create_sample_findings()
    
    # Initialize reviewer (using local mode for testing)
    reviewer = SecurityPRReviewer(local_folder="./test")
    
    # Generate Glass UI HTML report
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    html_path = test_dir / f"glass_ui_security_report_{timestamp}.html"
    
    print(f"📄 Generating Glass UI report with {len(sample_findings)} sample findings...")
    reviewer._export_findings_to_html(sample_findings, str(html_path))
    
    print(f"✅ Glass UI HTML report generated successfully!")
    print(f"📍 Report location: {html_path.absolute()}")
    print(f"📊 Report contains {len(sample_findings)} findings across multiple domains")
    print(f"💾 File size: {html_path.stat().st_size:,} bytes")
    
    # Display Glass UI features
    print("\n🌟 Glass UI Features Included:")
    print("   ✨ Glassmorphism design with backdrop blur effects")
    print("   🎭 Semi-transparent cards with glass-like appearance")
    print("   🌈 Gradient backgrounds with animated elements")
    print("   💫 Smooth animations and hover effects")
    print("   📱 Fully responsive design for all screen sizes")
    print("   🎯 Interactive filtering and search functionality")
    print("   🎨 Modern color palette with glass transparency")
    print("   ⚡ Enhanced performance with CSS animations")
    
    print(f"\n🌐 Open in browser: file://{html_path.absolute()}")
    print("\n🔍 Test the Glass UI features by:")
    print("   1. Opening the HTML file in a modern browser")
    print("   2. Hovering over cards to see glass effects")
    print("   3. Using the search and filter functionality")
    print("   4. Resizing the window to test responsiveness")
    print("   5. Scrolling to see parallax background effects")
    print("   6. Clicking buttons to see ripple animations")
    
    return html_path

def compare_with_original():
    """Compare Glass UI report with original design."""
    print("\n📊 Glass UI vs Original Design Comparison:")
    print("┌─────────────────────────┬─────────────────┬─────────────────┐")
    print("│ Feature                 │ Original        │ Glass UI        │")
    print("├─────────────────────────┼─────────────────┼─────────────────┤")
    print("│ Background              │ Solid gradient  │ Animated layers │")
    print("│ Cards                   │ Solid white     │ Glass effect    │")
    print("│ Transparency            │ None            │ Multiple levels │")
    print("│ Animations              │ Basic           │ Advanced        │")
    print("│ Visual Depth            │ Flat            │ 3D layered      │")
    print("│ Interactive Effects     │ Simple hover    │ Complex hover   │")
    print("│ Modern Appeal           │ Standard        │ Premium         │")
    print("│ Performance             │ Good            │ Optimized       │")
    print("└─────────────────────────┴─────────────────┴─────────────────┘")

if __name__ == "__main__":
    try:
        # Generate Glass UI report
        report_path = test_glass_ui_report()
        
        # Show comparison
        compare_with_original()
        
        # Optional: Open in default browser
        import webbrowser
        user_input = input("\n🚀 Would you like to open the Glass UI report in your browser? (y/n): ")
        if user_input.lower() in ['y', 'yes']:
            webbrowser.open(f"file://{report_path.absolute()}")
            print("🌐 Glass UI report opened in your default browser!")
        
        print("\n🎉 Glass UI HTML report test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error testing Glass UI report: {str(e)}")
        import traceback
        traceback.print_exc()
