#!/usr/bin/env python3
"""
Test script to verify Azure OpenAI model support for both GPT-4o and O1 models.
"""

import os
import sys
import json
import tempfile
from pathlib import Path

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_model_parameter_detection():
    """Test the model parameter detection function."""
    print("🧪 Testing model parameter detection...")
    
    try:
        from security_opt import get_openai_api_params
        
        # Test GPT-4o model
        gpt4o_params = get_openai_api_params(
            deployment_name="gpt-4o",
            max_tokens=1000,
            temperature=0.1,
            seed=42,
            response_format={"type": "json_object"}
        )
        
        print("✅ GPT-4o parameters:")
        print(f"   - Model: {gpt4o_params['model']}")
        print(f"   - Max completion tokens: {gpt4o_params.get('max_completion_tokens')}")
        print(f"   - Temperature: {gpt4o_params.get('temperature')}")
        print(f"   - Seed: {gpt4o_params.get('seed')}")
        print(f"   - Response format: {gpt4o_params.get('response_format')}")
        
        # Test O1-mini model
        o1_mini_params = get_openai_api_params(
            deployment_name="o1-mini",
            max_tokens=1000,
            temperature=0.1,
            seed=42,
            response_format={"type": "json_object"}
        )
        
        print("✅ O1-mini parameters:")
        print(f"   - Model: {o1_mini_params['model']}")
        print(f"   - Max completion tokens: {o1_mini_params.get('max_completion_tokens')}")
        print(f"   - Temperature: {o1_mini_params.get('temperature', 'Not set (correct for O1)')}")
        print(f"   - Seed: {o1_mini_params.get('seed', 'Not set (correct for O1)')}")
        print(f"   - Response format: {o1_mini_params.get('response_format', 'Not set (correct for O1)')}")
        
        # Test O1-preview model
        o1_preview_params = get_openai_api_params(
            deployment_name="o1-preview",
            max_tokens=1000,
            temperature=0.1,
            seed=42,
            response_format={"type": "json_object"}
        )
        
        print("✅ O1-preview parameters:")
        print(f"   - Model: {o1_preview_params['model']}")
        print(f"   - Max completion tokens: {o1_preview_params.get('max_completion_tokens')}")
        print(f"   - Temperature: {o1_preview_params.get('temperature', 'Not set (correct for O1)')}")
        print(f"   - Seed: {o1_preview_params.get('seed', 'Not set (correct for O1)')}")
        print(f"   - Response format: {o1_preview_params.get('response_format', 'Not set (correct for O1)')}")
        
        # Verify O1 models don't have restricted parameters
        assert 'temperature' not in o1_mini_params, "O1-mini should not have temperature parameter"
        assert 'seed' not in o1_mini_params, "O1-mini should not have seed parameter"
        assert 'response_format' not in o1_mini_params, "O1-mini should not have response_format parameter"
        
        assert 'temperature' not in o1_preview_params, "O1-preview should not have temperature parameter"
        assert 'seed' not in o1_preview_params, "O1-preview should not have seed parameter"
        assert 'response_format' not in o1_preview_params, "O1-preview should not have response_format parameter"
        
        # Verify GPT-4o has all parameters
        assert 'temperature' in gpt4o_params, "GPT-4o should have temperature parameter"
        assert 'seed' in gpt4o_params, "GPT-4o should have seed parameter"
        assert 'response_format' in gpt4o_params, "GPT-4o should have response_format parameter"
        
        print("✅ All parameter detection tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Model parameter detection test failed: {e}")
        return False

def test_security_reviewer_initialization():
    """Test SecurityPRReviewer initialization with different models."""
    print("\n🧪 Testing SecurityPRReviewer initialization...")
    
    try:
        from security_opt import SecurityPRReviewer
        
        # Test with different deployment names
        test_deployments = ["gpt-4o", "o1-mini", "o1-preview", "gpt-4"]
        
        for deployment in test_deployments:
            print(f"   Testing with deployment: {deployment}")
            
            # Set environment variable for this test
            original_deployment = os.environ.get("AZURE_OPENAI_DEPLOYMENT")
            os.environ["AZURE_OPENAI_DEPLOYMENT"] = deployment
            
            try:
                with tempfile.TemporaryDirectory() as temp_dir:
                    reviewer = SecurityPRReviewer(local_folder=temp_dir)
                    print(f"   ✅ Successfully initialized with {deployment}")
            except Exception as e:
                print(f"   ⚠️ Initialization with {deployment} failed (expected if no credentials): {e}")
            finally:
                # Restore original deployment
                if original_deployment:
                    os.environ["AZURE_OPENAI_DEPLOYMENT"] = original_deployment
                elif "AZURE_OPENAI_DEPLOYMENT" in os.environ:
                    del os.environ["AZURE_OPENAI_DEPLOYMENT"]
        
        print("✅ SecurityPRReviewer initialization tests completed!")
        return True
        
    except Exception as e:
        print(f"❌ SecurityPRReviewer initialization test failed: {e}")
        return False

def test_environment_configuration():
    """Test environment configuration for different models."""
    print("\n🧪 Testing environment configuration...")
    
    try:
        # Test that environment variables are properly read
        test_configs = {
            "gpt-4o": {
                "AZURE_OPENAI_DEPLOYMENT": "gpt-4o",
                "AZURE_OPENAI_API_VERSION": "2024-02-01"
            },
            "o1-mini": {
                "AZURE_OPENAI_DEPLOYMENT": "o1-mini",
                "AZURE_OPENAI_API_VERSION": "2024-02-01"
            },
            "o1-preview": {
                "AZURE_OPENAI_DEPLOYMENT": "o1-preview",
                "AZURE_OPENAI_API_VERSION": "2024-02-01"
            }
        }
        
        for model_name, config in test_configs.items():
            print(f"   Testing configuration for {model_name}:")
            
            # Temporarily set environment variables
            original_values = {}
            for key, value in config.items():
                original_values[key] = os.environ.get(key)
                os.environ[key] = value
                print(f"     - {key}: {value}")
            
            # Test that the configuration is readable
            deployment = os.environ.get("AZURE_OPENAI_DEPLOYMENT")
            api_version = os.environ.get("AZURE_OPENAI_API_VERSION")
            
            assert deployment == config["AZURE_OPENAI_DEPLOYMENT"], f"Deployment mismatch for {model_name}"
            assert api_version == config["AZURE_OPENAI_API_VERSION"], f"API version mismatch for {model_name}"
            
            # Restore original values
            for key, original_value in original_values.items():
                if original_value is not None:
                    os.environ[key] = original_value
                elif key in os.environ:
                    del os.environ[key]
            
            print(f"   ✅ Configuration test passed for {model_name}")
        
        print("✅ All environment configuration tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Environment configuration test failed: {e}")
        return False

def test_model_name_variations():
    """Test various model name patterns."""
    print("\n🧪 Testing model name variations...")
    
    try:
        from security_opt import get_openai_api_params
        
        # Test different naming patterns for O1 models
        o1_patterns = [
            "o1-mini",
            "o1_mini", 
            "o-1-mini",
            "o1-preview",
            "o1_preview",
            "o-1-preview"
        ]
        
        for pattern in o1_patterns:
            params = get_openai_api_params(
                deployment_name=pattern,
                max_tokens=1000,
                temperature=0.1
            )
            
            # Should not have temperature for O1 models
            assert 'temperature' not in params, f"Pattern {pattern} should be detected as O1 model"
            print(f"   ✅ Pattern '{pattern}' correctly detected as O1 model")
        
        # Test GPT-4o patterns
        gpt4o_patterns = [
            "gpt-4o",
            "gpt-4",
            "gpt-35-turbo",
            "custom-gpt-4o-deployment"
        ]
        
        for pattern in gpt4o_patterns:
            params = get_openai_api_params(
                deployment_name=pattern,
                max_tokens=1000,
                temperature=0.1
            )
            
            # Should have temperature for non-O1 models
            assert 'temperature' in params, f"Pattern {pattern} should be detected as standard model"
            print(f"   ✅ Pattern '{pattern}' correctly detected as standard model")
        
        print("✅ All model name variation tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Model name variation test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Azure OpenAI Model Support Tests")
    print("=" * 60)
    
    tests = [
        ("Model Parameter Detection", test_model_parameter_detection),
        ("SecurityPRReviewer Initialization", test_security_reviewer_initialization),
        ("Environment Configuration", test_environment_configuration),
        ("Model Name Variations", test_model_name_variations)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 40)
        
        try:
            success = test_func()
            results[test_name] = "PASSED" if success else "FAILED"
            status = "✅" if success else "❌"
            print(f"{status} {test_name}: {'PASSED' if success else 'FAILED'}")
        except Exception as e:
            results[test_name] = f"ERROR: {str(e)}"
            print(f"❌ {test_name}: ERROR - {str(e)}")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result == "PASSED")
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅" if result == "PASSED" else "❌"
        print(f"{status} {test_name}: {result}")
    
    print(f"\n📈 Overall: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Model support is working correctly.")
        print("\nSupported models:")
        print("- GPT-4o: Full parameter support")
        print("- O1-mini: Optimized parameters (no temperature/seed/response_format)")
        print("- O1-preview: Optimized parameters (no temperature/seed/response_format)")
        print("\nNext steps:")
        print("1. Configure your Azure OpenAI deployment")
        print("2. Set AZURE_OPENAI_DEPLOYMENT to your preferred model")
        print("3. Run security analysis with your chosen model")
    else:
        print("⚠️ Some tests failed - check the errors above")

if __name__ == "__main__":
    main()
