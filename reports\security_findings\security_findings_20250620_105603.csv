File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
simple_test.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,25.0,"The 'supportsHttpsTrafficOnly' property for the Microsoft.Storage/storageAccounts resource is set to false. This allows unencrypted HTTP connections, enabling attackers to intercept or modify data in transit via man-in-the-middle attacks. The blast radius includes all data transferred to and from the storage account, risking exposure or tampering of sensitive information.","Set 'supportsHttpsTrafficOnly' to true in the storage account properties to enforce encrypted HTTPS connections for all data in transit. Example: ""supportsHttpsTrafficOnly"": true.",,,,ai_analysis,,Validated
simple_test.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,27.0,"The 'minimumTlsVersion' property for the Microsoft.Storage/storageAccounts resource is set to 'TLS1_0', which is deprecated and vulnerable to multiple cryptographic attacks. Attackers can exploit weak TLS versions to decrypt or manipulate data in transit, increasing the risk of data exfiltration or session hijacking.","Set 'minimumTlsVersion' to 'TLS1_2' or higher in the storage account properties to enforce strong encryption for data in transit. Example: ""minimumTlsVersion"": ""TLS1_2"".",,,,ai_analysis,,Validated
simple_test.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,29.0,"The 'networkAcls.defaultAction' property for the Microsoft.Storage/storageAccounts resource is set to 'Allow', which permits public network access from any source. This exposes the storage account to the internet, enabling attackers to attempt unauthorized access, brute force, or data exfiltration. The blast radius includes all data stored in the account and any connected resources.","Set 'networkAcls.defaultAction' to 'Deny' and configure explicit network rules or private endpoints to restrict access to trusted networks only. Example: ""defaultAction"": ""Deny"".",,,,ai_analysis,,Validated
simple_test.json,DP-4,Data Protection,Enable data at rest encryption by default,MEDIUM,26.0,"The 'allowBlobPublicAccess' property for the Microsoft.Storage/storageAccounts resource is set to true, allowing public anonymous access to blob data. This increases the risk of unintentional data exposure and enables attackers to enumerate or exfiltrate sensitive blobs without authentication.","Set 'allowBlobPublicAccess' to false in the storage account properties to prevent anonymous public access to blob data. Example: ""allowBlobPublicAccess"": false.",,,,ai_analysis,,Validated
simple_test.json,NS-8,Network Security,Detect and disable insecure services and protocols,HIGH,27.0,"The 'minimumTlsVersion' property for the Microsoft.Storage/storageAccounts resource is set to 'TLS1_0', which is an insecure protocol. Attackers can exploit known vulnerabilities in TLS 1.0 to compromise confidentiality and integrity of data in transit.","Set 'minimumTlsVersion' to 'TLS1_2' or higher to disable insecure protocols and enforce secure communications. Example: ""minimumTlsVersion"": ""TLS1_2"".",,,,ai_analysis,,Validated
simple_test.json,DP-8,Data Protection,Ensure security of key and certificate repository,CRITICAL,29.0,"The 'networkAcls.defaultAction' property for the Microsoft.KeyVault/vaults resource is set to 'Allow', exposing the Key Vault to public network access. This enables attackers to attempt unauthorized access to cryptographic keys and secrets, increasing the risk of credential theft and privilege escalation across dependent resources.","Set 'networkAcls.defaultAction' to 'Deny' and configure private endpoints or trusted network rules to restrict access to the Key Vault. Example: ""defaultAction"": ""Deny"".",,,,ai_analysis,,Validated
simple_test.json,DP-8,Data Protection,Ensure security of key and certificate repository,CRITICAL,50.0,"The 'enableSoftDelete' property for the Microsoft.KeyVault/vaults resource is set to false, disabling soft delete protection. Attackers or malicious insiders can permanently delete keys and secrets, making recovery impossible and enabling denial-of-service or destructive attacks.","Set 'enableSoftDelete' to true to enable soft delete protection, allowing recovery of deleted keys and secrets. Example: ""enableSoftDelete"": true.",,,,ai_analysis,,Validated
simple_test.json,DP-8,Data Protection,Ensure security of key and certificate repository,CRITICAL,51.0,"The 'enablePurgeProtection' property for the Microsoft.KeyVault/vaults resource is set to false, disabling purge protection. Attackers can permanently purge deleted keys and secrets, preventing recovery and increasing the risk of irreversible data loss or privilege escalation.","Set 'enablePurgeProtection' to true to prevent permanent deletion of keys and secrets. Example: ""enablePurgeProtection"": true.",,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 8,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 0,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T10:56:03.129514,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
