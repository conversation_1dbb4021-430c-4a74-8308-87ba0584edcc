#!/usr/bin/env python3
"""
Debug script to find the actual line numbers of security issues.
This will help us correct the expected line numbers in the test.
"""

def create_test_content():
    """Create the exact same content as the test file."""
    return """// Azure Bicep Template - Security Test File
// This file contains intentional security issues for testing
// Each issue is placed at a specific line number for verification

param storageAccountName string = 'testsecuritystorage'
param location string = resourceGroup().location
param environment string = 'development'

// Resource Group (lines 9-13)
resource rg 'Microsoft.Resources/resourceGroups@2021-04-01' = {
  name: 'rg-security-test'
  location: location
}

// Storage Account with multiple security issues
resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {
  name: storageAccountName
  location: location
  kind: 'StorageV2'
  sku: {
    name: 'Standard_LRS'
  }
  properties: {
    // SECURITY ISSUE #1: Public blob access (EXACTLY LINE 24)
    allowBlobPublicAccess: true
    
    // SECURITY ISSUE #2: Weak TLS version (EXACTLY LINE 27)
    minimumTlsVersion: 'TLS1_0'
    
    // SECURITY ISSUE #3: HTTP traffic allowed (EXACTLY LINE 30)
    supportsHttpsTrafficOnly: false
    
    // Good configuration
    encryption: {
      services: {
        blob: {
          enabled: true
        }
        file: {
          enabled: true
        }
      }
      keySource: 'Microsoft.Storage'
    }
    
    // SECURITY ISSUE #4: Default network access (EXACTLY LINE 45)
    networkAcls: {
      defaultAction: 'Allow'
    }
  }
  
  tags: {
    Environment: environment
    Purpose: 'Security Testing'
  }
}

// Network Security Group with issues
resource nsg 'Microsoft.Network/networkSecurityGroups@2021-02-01' = {
  name: 'nsg-security-test'
  location: location
  properties: {
    securityRules: [
      {
        name: 'AllowSSHFromInternet'
        properties: {
          priority: 1000
          access: 'Allow'
          direction: 'Inbound'
          protocol: 'Tcp'
          // SECURITY ISSUE #5: SSH from anywhere (EXACTLY LINE 68)
          sourceAddressPrefix: '0.0.0.0/0'
          sourcePortRange: '*'
          destinationAddressPrefix: '*'
          destinationPortRange: '22'
        }
      }
      {
        name: 'AllowRDPFromInternet'
        properties: {
          priority: 1001
          access: 'Allow'
          direction: 'Inbound'
          protocol: 'Tcp'
          // SECURITY ISSUE #6: RDP from anywhere (EXACTLY LINE 81)
          sourceAddressPrefix: '0.0.0.0/0'
          sourcePortRange: '*'
          destinationAddressPrefix: '*'
          destinationPortRange: '3389'
        }
      }
    ]
  }
}

// Key Vault with issues
resource keyVault 'Microsoft.KeyVault/vaults@2021-04-01-preview' = {
  name: 'kv-security-test'
  location: location
  properties: {
    sku: {
      family: 'A'
      name: 'standard'
    }
    tenantId: subscription().tenantId
    // SECURITY ISSUE #7: Soft delete disabled (EXACTLY LINE 100)
    enableSoftDelete: false
    
    // SECURITY ISSUE #8: Purge protection disabled (EXACTLY LINE 103)
    enablePurgeProtection: false
    
    accessPolicies: [
      {
        tenantId: subscription().tenantId
        objectId: 'some-object-id'
        // SECURITY ISSUE #9: Excessive permissions (EXACTLY LINE 110)
        permissions: {
          keys: ['all']
          secrets: ['all']
          certificates: ['all']
        }
      }
    ]
  }
}

// Output section
output storageAccountId string = storageAccount.id
output keyVaultId string = keyVault.id"""

def find_actual_line_numbers():
    """Find the actual line numbers of security patterns."""
    content = create_test_content()
    lines = content.split('\n')
    
    # Patterns to search for
    patterns = {
        'allowBlobPublicAccess: true': 'Public blob access',
        'minimumTlsVersion: \'TLS1_0\'': 'Weak TLS version',
        'supportsHttpsTrafficOnly: false': 'HTTP traffic allowed',
        'defaultAction: \'Allow\'': 'Default network access',
        'sourceAddressPrefix: \'0.0.0.0/0\'': 'Source address 0.0.0.0/0',
        'enableSoftDelete: false': 'Soft delete disabled',
        'enablePurgeProtection: false': 'Purge protection disabled',
        'keys: [\'all\']': 'Excessive permissions'
    }
    
    print("🔍 Finding actual line numbers for security patterns:")
    print("=" * 60)
    
    found_patterns = {}
    
    for line_num, line_content in enumerate(lines, 1):
        line_stripped = line_content.strip()
        
        for pattern, description in patterns.items():
            if pattern in line_content:
                if description not in found_patterns:
                    found_patterns[description] = []
                found_patterns[description].append({
                    'line': line_num,
                    'content': line_stripped
                })
    
    # Display results
    for description, matches in found_patterns.items():
        print(f"\n📍 {description}:")
        for i, match in enumerate(matches, 1):
            occurrence = f" (occurrence {i})" if len(matches) > 1 else ""
            print(f"   Line {match['line']}{occurrence}: {match['content']}")
    
    # Show lines around expected positions for debugging
    print(f"\n🔍 Content around expected line positions:")
    print("=" * 60)
    
    expected_lines = [24, 27, 30, 45, 68, 81, 100, 103, 110]
    for expected_line in expected_lines:
        print(f"\nAround line {expected_line}:")
        start = max(1, expected_line - 2)
        end = min(len(lines), expected_line + 2)
        
        for line_num in range(start, end + 1):
            marker = ">>> " if line_num == expected_line else "    "
            line_content = lines[line_num - 1] if line_num <= len(lines) else "(EOF)"
            print(f"{marker}{line_num:3d}: {line_content}")

if __name__ == "__main__":
    find_actual_line_numbers()
