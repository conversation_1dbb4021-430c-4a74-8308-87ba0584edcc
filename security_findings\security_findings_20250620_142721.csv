File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
Parameters\LacpBillingExhaust.Parameters-LacpBillingExhaust.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'usageAccountSystemAssignedIdentityPrincipalId' flows across template boundary,Secure parameter 'usageAccountSystemAssignedIdentityPrincipalId' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyVaultSuffix' flows across template boundary,Secure parameter 'keyVaultSuffix' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'regionalCosmosAccountLocation' flows across template boundary,Secure parameter 'regionalCosmosAccountLocation' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'dasStorageAccountKey' flows across template boundary,Secure parameter 'dasStorageAccountKey' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'globalKeyVaultName' flows across template boundary,Secure parameter 'globalKeyVaultName' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'createSharedKeyVault' flows across template boundary,Secure parameter 'createSharedKeyVault' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,40.0,"The parameter 'isBoundariesRestricted' is explicitly set to 'false' on line 41, indicating that network or resource boundaries are not restricted. This configuration can enable lateral movement and initial access by allowing resources to communicate across boundaries without segmentation, increasing the blast radius of a potential compromise. Attackers can exploit this lack of segmentation to move laterally and access sensitive resources across the environment.",Set 'isBoundariesRestricted' to 'true' to enforce network segmentation boundaries. Ensure that all workloads are deployed in isolated virtual networks with appropriate network security groups (NSGs) and application security groups (ASGs) to restrict traffic between segments. Review and update your segmentation strategy to align with enterprise security requirements as per Azure Security Benchmark NS-1.,,,,ai_analysis,,Validated
Templates\IngestionStorageAccount.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,44.0,"The storage account resource at line 44 does not include any configuration for private endpoints or explicit disabling of public network access. By default, storage accounts are accessible over the public internet, which enables initial access and data exfiltration attack vectors. The blast radius includes all data stored in the account, and lateral movement is possible if credentials are compromised.",Restrict public network access by setting 'publicNetworkAccess' to 'Disabled' and configure a private endpoint for the storage account. This ensures only resources within the trusted network can access the storage account.,,,,ai_analysis,,Validated
Templates\IngestionStorageAccount.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,67.0,"The storage account resource at line 67 does not configure private endpoints or disable public network access. This leaves the storage account exposed to the public internet, increasing the risk of unauthorized access, data exfiltration, and lateral movement by attackers.",Set 'publicNetworkAccess' to 'Disabled' and create a private endpoint for the storage account to ensure only authorized network resources can access it.,,,,ai_analysis,,Validated
Templates\LacpBillingExhaust.Template.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,61.0,"The 'trustedExternalTenants' property at line 061 allows access from an external Azure AD tenant (value: '72f988bf-86f1-41af-91ab-2d7cd011db47'). Allowing external tenants without strict access controls can enable unauthorized access, privilege escalation, and lateral movement by users or applications from outside the primary tenant, increasing the risk of identity compromise and expanding the attack surface.","Restrict 'trustedExternalTenants' to only those with a documented business need and implement granular access controls for any external identities. Regularly review and audit external tenant access. Where possible, use Azure AD Conditional Access and centralized identity management to govern cross-tenant access as per Azure Security Benchmark IM-1.",,,,ai_analysis,,Validated
Templates\LacpGeo.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,225.0,"The CosmosDB resource at line 225 has 'publicNetworkAccess' set to 'Enabled', which allows public network access to the database account. This exposes the CosmosDB instance to the internet, enabling initial access, lateral movement, and potential data exfiltration if credentials are compromised. Attackers can scan for and target publicly accessible endpoints, increasing the blast radius to all data within the CosmosDB account.",Set 'publicNetworkAccess' to 'Disabled' in the CosmosDB resource configuration to restrict access to private endpoints only. Implement Azure Private Link and configure virtual network rules to ensure only trusted networks can access the database. Example: 'publicNetworkAccess': 'Disabled'.,,,,ai_analysis,,Validated
Templates\LacpGeo.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,228.0,"The CosmosDB resource at line 228 has 'isVirtualNetworkFilterEnabled' set to 'false', which disables virtual network filtering. This allows connections from any network, including the public internet, increasing the risk of unauthorized access, lateral movement, and data exfiltration. Attackers can exploit this misconfiguration to access sensitive data without network restrictions.",Set 'isVirtualNetworkFilterEnabled' to 'true' and define appropriate 'virtualNetworkRules' to restrict access to trusted subnets. This enforces network boundaries and reduces the attack surface. Example: 'isVirtualNetworkFilterEnabled': true.,,,,ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,108.0,"The CosmosDB resource at Line 105 has 'publicNetworkAccess' explicitly set to 'Enabled', and 'isVirtualNetworkFilterEnabled' is set to false at Line 108. This configuration allows unrestricted public network access to the CosmosDB account, enabling attackers to attempt direct access from the internet. This significantly increases the attack surface for initial access, brute force, and data exfiltration, and bypasses network segmentation and private endpoint best practices. The blast radius includes potential compromise of all data in the CosmosDB account.","Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to true. Implement Azure Private Link by configuring private endpoints for the CosmosDB account, and restrict access to trusted VNets only. Example: 'publicNetworkAccess': 'Disabled', 'isVirtualNetworkFilterEnabled': true. Reference: ASB NS-2.",,,,ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,360.0,"The Key Vault resource at Line 358 has 'enabledForDeployment' set to true, and at Line 360, 'enabledForTemplateDeployment' is also true. While these settings are sometimes required for automation, they can expose the Key Vault to broader Azure service access, increasing the risk of unauthorized access if not combined with network restrictions. There is no evidence of private endpoint configuration or network ACLs restricting access, which enables potential lateral movement and data exfiltration if an attacker gains access to the subscription or a misconfigured service principal.","Restrict Key Vault access by configuring private endpoints and setting network ACLs to allow only trusted VNets and subnets. If deployment access is required, ensure it is combined with strict network rules. Reference: ASB NS-2.",,,,ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,200.0,"The Storage Account resource at Line 200 does not specify any network rules or private endpoint configuration. By default, this allows public network access to the storage account, exposing it to the internet and enabling attackers to attempt unauthorized access, brute force, or data exfiltration. The absence of network restrictions increases the blast radius to all data stored in the account.","Configure 'networkAcls' to restrict access to selected VNets and subnets, and deploy private endpoints for the storage account. Set 'publicNetworkAccess' to 'Disabled' if possible. Reference: ASB NS-2.",,,,ai_analysis,,Validated
Templates\LacpGlobal.Template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,MEDIUM,200.0,"The Storage Account resource at Line 200 does not enable advanced threat protection (Azure Defender for Storage). Without this, anomalous activities such as unauthorized data exfiltration or suspicious access patterns may go undetected, increasing the risk of data breaches.",Enable Azure Defender for Storage on all storage accounts to monitor and alert on anomalous and potentially malicious activities. Reference: ASB DP-2.,,,,ai_analysis,,Validated
Templates\LacpRegion.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,561.0,"The CosmosDB account resource at line 558 has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false. This configuration allows the CosmosDB account to be accessed from any public network, exposing it to the internet and enabling initial access, lateral movement, and data exfiltration attack vectors. The blast radius includes all data stored in the CosmosDB account, and attackers could bypass network controls to access sensitive data.","Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to true. Configure 'virtualNetworkRules' to allow access only from trusted VNets and subnets. Deploy a Private Endpoint for the CosmosDB account to restrict access to private networks only, in accordance with Azure Security Benchmark NS-2.",,,,ai_analysis,,Validated
Templates\LacpRegion.Template.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,561.0,"The CosmosDB account resource at line 561 has 'isVirtualNetworkFilterEnabled' set to false, which means network traffic is not restricted to secure, private networks. This increases the risk of data in transit being intercepted or modified by attackers, as connections may occur over insecure networks.","Set 'isVirtualNetworkFilterEnabled' to true and configure 'virtualNetworkRules' to restrict access to trusted subnets. Ensure all client connections enforce TLS 1.2 or higher for secure data in transit, as required by Azure Security Benchmark DP-3.",,,,ai_analysis,,Validated
Templates\RoleAssignment.Template.json,IM-2,Identity Management,Protect identity and authentication systems,HIGH,5.0,"The role assignment on line 72 grants the 'Contributor' role to the principal specified by 'Ev2BuildoutServicePrincipalId'. The Contributor role provides broad permissions, including the ability to modify resources, which can enable privilege escalation and lateral movement if the service principal is compromised. There is no evidence of strong authentication controls (such as MFA) or privileged identity management being enforced for this principal, violating Azure Security Benchmark IM-2 guidance to restrict privileged roles and require strong authentication for privileged access.","Restrict the 'Contributor' role assignment to the minimum necessary scope and ensure the service principal uses strong authentication controls, such as Azure AD Conditional Access with MFA. Implement Privileged Identity Management (PIM) for this role and monitor high-risk activities. Review and limit the permissions granted to the service principal to follow the principle of least privilege.",,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 19,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 6,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T14:27:21.799699,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
