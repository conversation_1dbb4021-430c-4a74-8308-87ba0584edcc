Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
CRITICAL,NS-2,LacpGeo.Template.json,182,"The Cosmos DB resource ('Microsoft.DocumentDB/databaseAccounts') is configured with 'publicNetworkAccess': 'Enabled' and 'isVirtualNetworkFilterEnabled': false, meaning it is exposed to the public internet with no virtual network protections. This unnecessarily exposes the database to potential internet-based attacks.","Set 'publicNetworkAccess' to 'Disabled' or, at a minimum, set 'isVirtualNetworkFilterEnabled' to true and configure 'virtualNetworkRules' to only allow trusted subnets. Add 'ipRules' if specific IPs must have access, but avoid broad or open access.",N/A,AI,Generic
CRITICAL,NS-1,LacpGlobal.Template.json,58,"The Cosmos DB account ('Microsoft.DocumentDB/databaseAccounts') has `publicNetworkAccess` set to 'Enabled' and `isVirtualNetworkFilterEnabled` set to 'false', leaving it accessible over public endpoints without any VNet restriction or firewall.",Set `publicNetworkAccess` to 'Disabled' and `isVirtualNetworkFilterEnabled` to 'true'. Define appropriate `virtualNetworkRules` or `ipRules` to restrict access to trusted networks only.,N/A,AI,Generic
CRITICAL,NS-2,LacpGlobal.Template.json,58,"Cosmos DB account has public network access enabled and no IP or VNet restrictions (`ipRules` and `virtualNetworkRules` are empty), exposing sensitive data to the internet.",Restrict public network access by disabling `publicNetworkAccess` or by setting IP/network rules to only allow access from trusted sources.,N/A,AI,Generic
CRITICAL,NS-1,LacpRegion.Template.json,942,"The Cosmos DB account 'Microsoft.DocumentDB/databaseAccounts' has publicNetworkAccess set to 'Enabled' and isVirtualNetworkFilterEnabled set to 'false', allowing access from the public Internet. This exposes sensitive database resources to potential threats.","Set 'publicNetworkAccess' to 'Disabled' or enable 'isVirtualNetworkFilterEnabled' and specify appropriate virtual network or IP rules. Restrict network access using private endpoints, virtual network service endpoints, and/or network security groups to limit exposure.",N/A,AI,Generic
CRITICAL,NS-2,LacpRegion.Template.json,942,Cosmos DB resource allows public access by setting 'publicNetworkAccess' to 'Enabled' and 'virtualNetworkRules' to an empty array. There are no firewall or network filter rules to restrict access.,Restrict public access by setting 'publicNetworkAccess' to 'Disabled' and configuring 'virtualNetworkRules' to restrict access to only trusted VNets and IP ranges.,N/A,AI,Generic
CRITICAL,DP-3,LacpRegion.Template.json,4125,"Output section exposes the DAS Storage Account key as a regular deployment output ('dasStorageAccountKey'), risking disclosure, especially if outputs are logged or visible to users who don't need sensitive key material.","Remove the 'dasStorageAccountKey' from outputs. Retrieve and use such sensitive data at runtime, only within trusted application code, or restrict outputs to authorized automation workflows with proper access controls.",N/A,AI,Generic
CRITICAL,DP-3,LacpStamp.Parameters-LacpStampResources.json,69,"The parameter 'dasStorageAccountKey' is assigned a value using external output referencing, which likely results in a Storage Account access key being included in the deployment parameters. This is a violation of the sensitive information management principle, as keys should not be distributed or referenced in deployment parameters directly; they must be stored securely in Azure Key Vault.","Remove the direct reference to the Storage Account key in deployment parameters. Use Azure Key Vault references for secrets, ensuring that applications and deployments fetch secrets securely at runtime. Review deployment logic to ensure secrets are never exposed within deployment parameter files or templates.",N/A,AI,Generic
HIGH,NS-1,IngestionStorageAccount.Template.json,0,"The storage accounts provisioned in this template do not have any network restrictions (such as Virtual Network Service Endpoints, Private Endpoint configuration, or Firewall rules). By default, this allows public network access, which exposes the storage accounts to the public internet.",Add the 'networkAcls' property for each storage account resource and configure 'defaultAction' to 'Deny'. Allow access only from specific trusted networks (such as selected VNets or IP ranges). Consider deploying Private Endpoints to eliminate public internet exposure.,N/A,AI,Generic
HIGH,NS-2,IngestionStorageAccount.Template.json,0,"The template does not restrict access to public endpoints for storage accounts. With current settings, unless further network ACLs are applied elsewhere, storage accounts are exposed to the public Internet.","Restrict public access by updating the 'networkAcls' property with 'defaultAction' set to 'Deny', and only allowing specific networks or IPs. Deploy Private Endpoints to ensure no public access.",N/A,AI,Generic
HIGH,NS-1,LacpBilling.Template.json,434,"The storage account resource does not implement any network security controls such as Virtual Network (VNet) integration or network rules (e.g., defaultAction, networkAcls). This means the Storage Account is accessible from all networks by default, increasing the attack surface.",Set networkAcls property for the storage account to restrict access to selected networks and/or trusted services. Consider VNet integration or setting 'defaultAction' to 'Deny' and explicitly allow trusted subnets only.,N/A,AI,Generic
HIGH,NS-6,LacpBilling.Template.json,434,"The Storage Account resource does not restrict or block public endpoints. Storage accounts are deployed without setting networkAcls or disabling public network access, leaving them exposed to public internet.","Explicitly set the 'networkAcls' property for storage accounts with 'defaultAction' as 'Deny' and 'bypass' as appropriate (e.g., 'AzureServices'). Also, set 'publicNetworkAccess' to 'Disabled' unless there’s a justified need.",N/A,AI,Generic
HIGH,NS-1,LacpBillingExhaust.Template.json,33,"The Microsoft.Kusto (ADX) cluster resource in this template does not specify any network security controls, such as a private endpoint or restricting public network access. By default, ADX clusters are internet-accessible unless explicitly protected, which violates the requirement to protect critical resources using Network Security Groups or Azure Firewall.","Update the Microsoft.Kusto/clusters resource to disable public network access (set 'publicNetworkAccess' to 'Disabled'), deploy into a virtual network with private endpoints, and/or explicitly restrict allowed IP ranges to only trusted sources. Integrate with Azure Firewall or NSGs to further limit inbound and outbound access.",N/A,AI,Generic
HIGH,NS-2,LacpBillingExhaust.Template.json,33,ADX cluster endpoints (including ingestion and management APIs) are by default public. This increases risk of unauthorized access or attack surface for these services.,"Restrict public endpoint exposure by enabling private endpoints for the ADX cluster, or configure authorizedIpRange or 'publicNetworkAccess' property to restrict access. Only explicitly allow trusted IP addresses/subnets.",N/A,AI,Generic
HIGH,DP-1,LacpBillingExhaust.Template.json,33,"There is no explicit configuration for encryption at rest for the Kusto (ADX) cluster or databases. Although ADX provides encryption at rest by default, the template does not confirm that a customer-managed key (CMK) is enabled.","Configure the Kusto cluster to use customer-managed keys by specifying the 'keyVaultProperties' section. If not using CMK, document justification for relying only on platform-managed keys; otherwise, integrate with Azure Key Vault for key management.",N/A,AI,Generic
HIGH,AM-1,LacpBillingExhaust.Template.json,67,"The principal assignment script grants database 'viewers' to an AAD group ('<EMAIL>'), but the scope or membership of this group is not limited or validated, possibly violating least-privilege.","Audit the '<EMAIL>' group to ensure only intended users are members. Where possible, create more granular roles or use assigned groups specific to job functions. Document access justification for each group.",N/A,AI,Generic
HIGH,NS-1,LacpGeo.Template.json,182,No Network Security Groups (NSGs) or Azure Firewall rules are present to restrict access to the Cosmos DB or Key Vault resources. Sensitive resources should not be left accessible by default networking settings.,"Deploy NSGs or Azure Firewall rules to restrict all inbound/outbound traffic to Key Vault and Cosmos DB to only authorized networks such as trusted VNets, IP ranges, or private endpoints.",N/A,AI,Generic
HIGH,NS-3,LacpGeo.Template.json,182,"'isVirtualNetworkFilterEnabled': false for Cosmos DB disables VNet service endpoints or VNet access controls. This means there are no NSG-level protections, increasing exposure.",Enable 'isVirtualNetworkFilterEnabled' for Cosmos DB to apply VNet-based access restrictions and then use NSGs to enforce granular network access policies.,N/A,AI,Generic
HIGH,NS-2,LacpGeo.Template.json,59,"Azure Key Vault ('Microsoft.KeyVault/vaults') is deployed without private endpoint configuration or network ACLs, which leaves the Key Vault accessible over the public internet by default.",Configure Key Vault with private endpoints or set network ACLs ('networkAcls') to restrict access to trusted VNets only. Disable public network access where possible.,N/A,AI,Generic
HIGH,NS-1,LacpGlobal.Template.json,131,"Azure Storage Account resources (`Microsoft.Storage/storageAccounts`) do not specify any network rules, firewall, service endpoints, or private endpoints, making them potentially accessible from any network by default.",Restrict storage account network access by configuring `networkAcls` property to allow only trusted virtual networks and specific IP addresses. Consider enabling private endpoints where possible.,N/A,AI,Generic
HIGH,NS-2,LacpGlobal.Template.json,131,"Azure Storage Accounts have no network restrictions (`networkAcls` not defined). Public endpoints remain accessible, increasing exposure.","Add `networkAcls` to restrict access to trusted networks, disable public blobs if not required, and consider using a private endpoint.",N/A,AI,Generic
HIGH,NS-3,LacpGlobal.Template.json,58,Neither Cosmos DB nor Storage resources reference Network Security Groups (NSGs) or route traffic through an Azure Firewall.,Associate storage and database resources with subnets protected by NSGs. Use firewall rules and VNet service endpoints to restrict resource access.,N/A,AI,Generic
HIGH,NS-2,LacpRegion.Parameters-LacpRegionResources.json,23,"Parameter 'isBoundariesRestricted' is set to 'false', indicating that network boundaries are not enforced. This potentially exposes Logic Apps and other integrated resources to the public internet, violating best practices for securing public endpoints.","Set 'isBoundariesRestricted' to 'true' to enforce network boundary restrictions. Ensure public endpoints are only accessible through secure channels such as private endpoints, VPN, or IP allow-listing, in alignment with Azure Security Benchmark NS-2.",N/A,AI,Generic
HIGH,NS-1,LacpRegion.Template.json,700,"None of the Storage Account resources (e.g., regionalStorageAccountName, tipStorageAccountName, dataPullerEventHubStorageAccountName, amsBackupStorageAccountName) have network rules or NSG/firewall restrictions defined; default rules allow public access from all networks.",Restrict access to the Storage Accounts using 'networkAcls' with appropriate 'virtualNetworkRules' and/or 'ipRules'. Set 'defaultAction' to 'Deny' in 'networkAcls' and allow access only from trusted networks. Consider using private endpoints.,N/A,AI,Generic
HIGH,NS-2,LacpRegion.Template.json,700,"Storage Account resources are lacking network restrictions (no 'networkAcls', 'virtualNetworkRules', or 'ipRules'), exposing storage endpoints to the public Internet.","Configure 'networkAcls' on each Storage Account to allow access only from specific virtual networks or IP address ranges. For highly sensitive data, disable public access entirely by setting only private endpoints.",N/A,AI,Generic
HIGH,IM-1,LacpRegion.Template.json,944,"Cosmos DB resource disables local authentication with 'disableLocalAuth': true but sets 'publicNetworkAccess' to 'Enabled', offering Azure AD-based controls but not leveraging network-level restrictions for identity perimeter defense.",Combine Azure AD authentication with network controls. Enforce access only from trusted networks or VNets and ensure only required identities are granted access.,N/A,AI,Generic
HIGH,DP-3,LacpRegion.Template.json,1872,"Key Vault secrets are being created from dynamically composed connection strings, directly embedding storage account keys or Cosmos DB master keys as secret values. Such sensitive key material should only be stored in Key Vault, not elsewhere and must not be exposed in logs or outputs.","Ensure secrets are only available to applications and users with a business need. Review secret naming, lifecycle, and access policy procedures. Use managed identities over shared keys where possible for accessing Storage/Cosmos DB.",N/A,AI,Generic
HIGH,NS-1,LacpRegion.Template.json,1761,"Key Vault is deployed without any network rules or firewall restrictions. This means it is accessible from all networks by default, increasing risk of unauthorized access.",Configure 'networkAcls' for the Key Vault resource to restrict access to specific virtual networks or IP ranges. Consider enabling private endpoint connectivity to further reduce exposure.,N/A,AI,Generic
HIGH,DP-3,LacpStamp.Parameters-LacpStampResources.json,68,"The parameter 'dasStorageAccountName' may expose the name of a potentially sensitive storage account via deployment outputs, increasing risk of targeted attacks if the file becomes public. While not a secret, storage account names should be referenced securely and not unnecessarily exposed.","Avoid passing sensitive resource names as parameters unless necessary. Where possible, access by managed identity without exposing sensitive infrastructure details in parameter files.",N/A,AI,Generic
HIGH,DP-3,LacpStamp.Parameters-LacpStampResources.json,77,"The Key Vault name 'globalKeyVaultName' is parameterized, but there is no indication that other secrets (such as Storage Account keys) are sourced from Key Vault or that Key Vault references are actually used consistently. This presents a risk that secrets may not be managed securely.","Ensure all secrets (e.g., Storage Account keys, connection strings) are retrieved and managed exclusively through Azure Key Vault with controlled access and not handled in plaintext in deployment files or parameters.",N/A,AI,Generic
HIGH,NS-1,LacpStamp.Template.json,599,"Storage accounts are deployed without network rules (for example, no 'networkAcls', private endpoints, or service endpoints). This means access to these storage accounts is not restricted at the network layer, which increases risk of unauthorized access.",Apply 'networkAcls' to storage accounts to restrict traffic only to required subnets and trusted Azure services. Consider using private endpoints for storage accounts and deny access from all public networks unless explicitly required.,N/A,AI,Generic
HIGH,NS-2,LacpStamp.Template.json,599,"There is no evidence that public endpoints for storage accounts are restricted. By default, when no 'networkAcls' is set, storage accounts are accessible over public Internet endpoints.",Set 'publicNetworkAccess' to 'Disabled' or configure 'networkAcls' to restrict public access unless there is a specific business requirement. Prefer access over private endpoints.,N/A,AI,Generic
HIGH,AM-1,LacpStamp.Template.json,1286,"The Key Vault accessPolicy for '[parameters('lacpAadObjectId')]' grants full administrative permissions for keys, secrets, and certificates (including 'Delete', 'Recover', 'Backup', 'Restore', etc.). This can lead to excessive access and violates the least privilege principle.",Review and reduce assigned permissions to only what is required for operational purposes. Do not grant broad administrative permissions unless absolutely necessary. Split duties and assign only needed rights per identity.,N/A,AI,Generic
HIGH,NS-1,ReadAdxExhaust.Template.json,17,"No network security groups (NSGs) or firewall rules are configured on the Microsoft.Kusto cluster, potentially leaving it exposed to unwanted network traffic.",Implement NSGs or Azure Firewall in the virtual network where the Kusto cluster resides to restrict traffic only to required sources and destinations. Restrict management and data plane operations to necessary IP ranges.,N/A,AI,Generic
HIGH,IM-6,RoleAssignment.Template.json,54,"Contributor role assignment to the Ev2BuildoutServicePrincipalId may violate the principle of least privilege (RBAC). The Contributor role grants broad privileges, including manage, delete, and create permissions across resources. This violates ASB IM-6 and AM-1 if the service principal does not require such wide permissions.","Review the required permissions for Ev2BuildoutServicePrincipalId and assign a more granular, least-privilege role instead of the generic Contributor role. If only specific actions are needed, create a custom role with only those permissions.",N/A,AI,Generic
HIGH,IM-7,RoleAssignment.Template.json,54,"Service principal receives broad Contributor role without evidence of privileged identity management or access reviews for application identities. Excessive permissions on application identities can lead to lateral movement or privilege escalation. (ASB: IM-7, AM-3, AM-4)",Apply Azure Privileged Identity Management (PIM) to the service principal and ensure periodic access reviews are performed. Limit permissions for application identities to the minimum required for their function.,N/A,AI,Generic
HIGH,NS-2,TrafficManagerEndpoints.Template.json,51,"The Traffic Manager external endpoint uses a 'target' property that constructs a publicly-addressable DNS name. It does not define any access restrictions, whitelisting, or other security controls around public reachability. This may expose the underlying endpoints directly to the internet, violating the requirement to minimize public exposure of endpoints.","Evaluate whether these endpoints must be public. Where possible, restrict exposure via IP whitelisting, authentication at the endpoint, or consider using internal-only profiles. Document and regularly review the necessity of public access to limit exposure.",N/A,AI,Generic
MEDIUM,NS-3,IngestionStorageAccount.Template.json,0,"There is no evidence of any Network Security Groups (NSGs) being used to restrict access where applicable (e.g., when integrating with VNets). Storage accounts are not explicitly associated with networks protected by NSGs.","Integrate storage accounts with VNets using service endpoints or private endpoints, and deploy NSGs on subnets to control access.",N/A,AI,Generic
MEDIUM,IM-1,IngestionStorageAccount.Template.json,0,There is no configuration enforcing Azure Active Directory (Azure AD) authentication for accessing data in the storage accounts. Relying on default key-based access poses a risk.,Set the 'defaultAction' to 'Deny' in 'networkAcls' and require Azure AD authentication for blob and queue access by setting 'isUserAssignedIdentity' and enabling 'Azure Active Directory Domain Services Authentication' where supported.,N/A,AI,Generic
MEDIUM,DP-1,IngestionStorageAccount.Template.json,0,"While the template leverages storage account encryption at rest by default, it does not specify the use of customer-managed keys (CMK), instead relying on platform-managed keys (PMK). Relying on PMK may not meet stricter compliance requirements.","Explicitly configure the 'encryption' property for storage accounts to use customer-managed keys (CMK) protected in Azure Key Vault, if greater control or compliance is required.",N/A,AI,Generic
MEDIUM,DP-3,IngestionStorageAccount.Template.json,0,"There is no integration with Azure Key Vault for storing sensitive information such as storage access keys, secrets, or customer-managed encryption keys.",Integrate with Azure Key Vault and use it for managing sensitive secrets and encryption keys. Avoid exposing storage account keys in code or templates.,N/A,AI,Generic
MEDIUM,NS-3,LacpBilling.Template.json,434,"There is no use of Network Security Groups (NSGs) to control traffic to the Storage Accounts. Although NSGs are commonly used for subnets/VMs, for storage-related endpoints, network restrictions should be handled via networkAcls.",Ensure storage accounts are integrated with VNets and restrict network access using NSGs at the subnet level where appropriate.,N/A,AI,Generic
MEDIUM,DP-3,LacpBilling.Template.json,434,"No measures for sensitive information management are present. For instance, customer-managed keys or secrets (for CMK encryption) are not retrieved from Azure Key Vault, and there’s no evidence sensitive parameters are handled securely.","Store and reference secrets, keys, and connection strings using Azure Key Vault. Do not hardcode any sensitive information in the template or parameters; instead, reference securely stored values.",N/A,AI,Generic
MEDIUM,AM-1,LacpBilling.Template.json,534,"Role assignments for the billing storage accounts and UsageBilling account assign broad built-in roles, with no evidence of review for least privilege. There is a risk of overprivileging service identities.","Review the assigned roles to confirm that they grant only the necessary permissions for billing/queue processing. Where possible, scope roles more narrowly and/or use custom roles with minimum rights.",N/A,AI,Generic
MEDIUM,NS-3,LacpBillingExhaust.Template.json,33,"There is no reference to Network Security Groups (NSGs) on subnets or interfaces for the deployed Kusto cluster, meaning traffic to and from the cluster is not controlled or segmented.",Deploy the ADX cluster inside a subnet protected by NSGs. Define inbound and outbound rules in the NSG to allow only required traffic to the cluster and block all unnecessary access.,N/A,AI,Generic
MEDIUM,DP-2,LacpBillingExhaust.Template.json,33,There is no configuration or evidence of enforcing TLS 1.2+ for data-in-transit to/from the Kusto cluster. ADX supports TLS 1.2+ by default but does not prevent older versions unless explicitly set.,"Ensure that connections to Kusto endpoints enforce TLS 1.2 or greater. Review client configurations and, if possible, set 'minimalTlsVersion' property in the Kusto cluster resource to '1.2'.",N/A,AI,Generic
MEDIUM,IM-8,LacpBillingExhaust.Template.json,52,"Principal assignments to the database leverage principalId parameters, but there is no clear indication that Managed Identities (system-assigned or user-assigned) are used for these applications. Instead, generic 'App' principal type is referenced without validating it is indeed an Azure Managed Identity.","Where possible, use Azure Managed Identities ('principalType': 'ManagedIdentity') for application authentication and eliminate use of generic 'App' IDs. Validate and update templates and consuming applications to utilize Managed Identities, enabling managed service-to-service authentication.",N/A,AI,Generic
MEDIUM,DP-3,LacpBillingExhaust.Template.json,1,"Sensitive secrets such as keys or connection strings are not present in the template, but parameters like 'podIdentityObjectID' and 'usageAccountSystemAssignedIdentityPrincipalId' could be considered sensitive and are defined as plain string type. There is no reference to using Azure Key Vault for sensitive parameter injection.","Mark sensitive parameters as 'secureString' to avoid exposure in deployment logs/history. When possible, retrieve secrets directly from Azure Key Vault using template references rather than passing via parameters, minimizing risk of disclosure.",N/A,AI,Generic
MEDIUM,DP-3,LacpBillingExhaustExport.Template.json,14,"Sensitive connection URIs (adxExhaustUri, adxExhaustDataIngestionUri) are accepted directly as parameters and referenced as strings. If not supplied securely (e.g., retrieved from Azure Key Vault), this may lead to unintentional disclosure or accidental plaintext exposure, violating sensitive information management principles.","Ensure sensitive URIs and credentials are referenced from secure locations, such as Azure Key Vault, and never stored or passed in plaintext where avoidable. Update pipeline to use Azure Key Vault references for all secrets and sensitive parameters.",N/A,AI,Generic
MEDIUM,IM-6,LacpGeo.Template.json,66,Access to Key Vault is managed using access policies. Azure recommends using RBAC for managing access to Key Vault rather than Access Policies for enforcing least privilege and centralized role management.,Transition Key Vault to RBAC permission model by setting 'properties.enabledForRBACAuthorization': true and manage access through Azure RBAC roles instead of access policies.,N/A,AI,Generic
MEDIUM,DP-6,LacpGeo.Template.json,59,The Key Vault is deployed with the default 'Standard' SKU and no evidence of Customer-Managed Keys (CMK) or HSM-backed keys for encryption of sensitive data-at-rest.,"Evaluate enabling Key Vault's Premium SKU with HSM-backed keys if required for compliance, and ensure that encryption keys for critical data are stored and managed using CMK in Key Vault.",N/A,AI,Generic
MEDIUM,AM-1,LacpGlobal.Template.json,175,"Key Vault access policies grant 'Get' and 'List' permissions for 'keys', 'secrets', and 'certificates' to multiple principals, and one principal has broad management rights (including 'Delete', 'Recover', 'Backup', etc.), which may violate least privilege.",Review and refine Key Vault access policies. Assign only the minimum required permissions to each principal (consider using Key Vault RBAC). Limit use of broad permissions to trusted roles with a justified need.,N/A,AI,Generic
MEDIUM,DP-2,LacpGlobal.Template.json,142,"The storage account enforces `minimumTlsVersion` as `TLS1_2` and `supportsHttpsTrafficOnly` as `true`, which is compliant; nonetheless, ensure clients use TLS 1.2+ and that these settings are maintained or upgraded when TLS1_3 general availability occurs.",No major change needed. Monitor Microsoft recommendations for newer TLS versions and update `minimumTlsVersion` when support is broadly available.,N/A,AI,Generic
MEDIUM,DP-3,LacpGlobal.Template.json,222,"Cosmos DB primary key and storage account connection strings are generated and securely stored as secrets in Azure Key Vault, which aligns with the control. However, these secrets are programmatically created in the template using ARM functions, which may expose keys in deployment outputs or logs if outputted elsewhere.",Ensure that these values are not output or logged beyond secure Key Vault creation. Validate deployment pipeline practices to prevent secrets from surface exposure.,N/A,AI,Generic
MEDIUM,DP-2,LacpRegion.Parameters-LacpRegionResources.json,42,"Parameter 'minimalCosmosDbTlsVersion' is set to 'Tls12', which aligns with the minimum TLS version required; however, if this value is overridden by a lower version in deployment or not strictly enforced elsewhere, it may allow for weaker encryption in transit.",Ensure that 'minimalCosmosDbTlsVersion' is enforced at resource configuration with 'Tls1_2' or higher. Periodically audit the Cosmos DB configuration to confirm that only secure protocols are in use for all connections.,N/A,AI,Generic
MEDIUM,DP-1,LacpStamp.Template.json,599,"Encryption at rest uses default settings. There is no explicit specification of customer-managed keys (CMK) for storage account encryption, so Microsoft-managed keys are used by default. While this satisfies baseline at-rest encryption, using CMKs provides greater security and control.","Where appropriate, specify a customer-managed key in the storage account properties to enhance encryption control, particularly for sensitive or regulated workloads.",N/A,AI,Generic
MEDIUM,AM-1,LacpStamp.Template.json,1343,"In the nested Key Vault access policy for 'GrantManagedIdentityAccessToGlobalKeyVault', the managed identity is given full permissions on keys, secrets, and certificates, which could result in over-privileged access.","Reassess least privilege. Limit the access policies to only required operations (e.g., omit destructive operations like 'Delete', 'Purge', and broad administrative rights unless truly necessary for this identity).",N/A,AI,Generic
MEDIUM,NS-2,ReadAdxExhaust.Template.json,17,"The Microsoft.Kusto cluster resource does not specify any networking configuration (such as private endpoints or restricting public network access). By default, this could expose the cluster over a public endpoint, which violates the requirement to secure public endpoints and minimize exposure.","Explicitly configure the Kusto cluster with 'publicNetworkAccess' set to 'Disabled', and use private endpoints or service endpoints to control network access. Restrict access to only trusted networks.",N/A,AI,Generic
MEDIUM,IM-8,ReadAdxExhaust.Template.json,17,Managed identities are not defined for the Microsoft.Kusto cluster. Not enabling managed identities violates best practices for secure resource-to-resource authentication.,"Enable a system-assigned or user-assigned managed identity for the Kusto cluster and use this identity for secure resource access, such as Azure Key Vault or other services.",N/A,AI,Generic
MEDIUM,DP-1,ReadAdxExhaust.Template.json,17,"The template does not explicitly specify encryption at rest settings (e.g., customer-managed keys) for the Microsoft.Kusto cluster. Although default encryption is provided by Azure, not specifying or enforcing encryption options may lead to non-compliance with stricter security standards.",Explicitly configure encryption at rest with customer-managed keys (CMK) for the Kusto cluster if organizational policy requires. Specify the 'keyVaultProperties' block referencing a Key Vault key.,N/A,AI,Generic
MEDIUM,DP-3,ReadAdxExhaust.Template.json,17,"There are no indications of secret management practices in the template (for example, secret references for sensitive values). Failure to use Azure Key Vault for sensitive configuration can result in accidental disclosure.","When the cluster requires configuration of sensitive information (e.g., admin credentials, keys, connection strings), reference these from Azure Key Vault instead of inline parameters or variables.",N/A,AI,Generic
MEDIUM,IM-8,ReadUsageAccount.Template.json,28,"The template suggests support for system-assigned managed identity (via variables and output statement), but the actual resource definition for Microsoft.UsageBilling/accounts does not contain an 'identity' block. This means managed identity will NOT be enabled for this resource, missing an opportunity for secure, credential-free resource authentication.","Add the 'identity' property to the resource definition, e.g., ""identity"": { ""type"": ""SystemAssigned"" }, to enable a managed identity, in accordance with ASB IM-8 best practices.",N/A,AI,Generic
MEDIUM,IM-4,RoleAssignment.Template.json,42,"Role assignments to security groups or principals in the template occur without evidence of periodic review or removal of unused access, potentially resulting in excessive or outdated permissions (ASB: IM-4, AM-4).",Establish a process for regular review and recertification of these role assignments. Implement automated access reviews via Azure AD Access Reviews.,N/A,AI,Generic
MEDIUM,IM-8,RoleAssignment.Template.json,54,"Template assigns access to service principals (Ev2BuildoutServicePrincipalId, KeyVaultPrincipalId) but does not use or reference managed identities, missing a key ASB recommendation for secure resource authentication (ASB: IM-8).","Where possible, replace service principals with managed identities for Azure resources to improve security and reduce secret management overhead.",N/A,AI,Generic
LOW,DP-1,LacpBilling.Template.json,434,"No explicit encryption configuration is defined for the storage accounts. While Azure enables encryption at rest by default, the template does not specify customer-managed key (CMK) encryption or confirm encryption settings.","Explicitly configure storage account encryption (e.g., using 'encryption' property) and, if required by policy, use customer-managed keys from Azure Key Vault for added control.",N/A,AI,Generic
LOW,IM-1,LacpBilling.Template.json,441,"The storage account is configured with 'defaultToOAuthAuthentication: true', and 'allowSharedKeyAccess: false', which aligns with Azure AD usage, but there is no verification that all applications are required to use Azure AD for access (e.g., via blob soft delete or data-level access policies).","Enforce Azure AD authentication for all sensitive data operations and audit users/apps with access to ensure only intended identities are permitted, using Azure AD RBAC.",N/A,AI,Generic
LOW,DP-2,LacpGeo.Template.json,182,"Minimal TLS version for Cosmos DB ('minimalTlsVersion') is parameterized, but there is no default enforcement or check for TLS 1.2 or above, which could result in deployments with weaker encryption in transit.","Validate in deployment pipeline or template that 'minimalCosmosDbTlsVersion' is set to 'TLS1_2' or later. If user-provided, document that lower versions are not permitted.",N/A,AI,Generic
LOW,DP-1,LacpGlobal.Template.json,131,"Encryption at rest is enabled by default for new Storage Accounts and Cosmos DB. There is no explicit override, so they are compliant. However, customer-managed keys (CMK) are not configured, so data is protected only by Microsoft-managed keys.",Consider enabling customer-managed keys (CMK) for both storage accounts and Cosmos DB to enhance control and auditability of data encryption.,N/A,AI,Generic
LOW,DP-1,LacpRegion.Template.json,942,There is no explicit configuration for customer-managed keys (CMK) or advanced encryption options for Cosmos DB or the Storage Accounts; only default Microsoft-managed encryption is presumed.,"If regulatory or business needs require, enable customer-managed keys (CMK) for Cosmos DB and all storage accounts. Store encryption keys in Azure Key Vault and reference them in the resource configuration.",N/A,AI,Generic
LOW,DP-3,LacpStamp.Template.json,1937,"Secrets for Redis and storage account keys are stored in Key Vault, which is good. However, for '[concat(variables('stampSharedKeyVaultName'), '/', parameters('dasStorageAccountName'),'-key')]', the secret value is passed via parameter '[parameters('dasStorageAccountKey')]', which may create risk if the parameter is supplied inline or through insecure means.",Ensure the sensitive parameter 'dasStorageAccountKey' is securely passed at deployment time and not checked into version control or exposed in deployment logs.,N/A,AI,Generic
