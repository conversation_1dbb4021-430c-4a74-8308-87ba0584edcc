Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P1-Identity-HIGH,Identity Management,HIGH,IM-1,template.json,44,App Service 'onefuzz-daily-ui' does not explicitly integrate with Azure Active Directory for identity management.,Enable Azure Active Directory authentication for the App Service to ensure secure identity and access management.,N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-2,template.json,44,App Service 'onefuzz-daily-ui' does not enforce Multi-Factor Authentication (MFA) for users and administrators.,Configure Azure Active Directory authentication with MFA enforcement for all users and administrators accessing the App Service.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,44,App Service 'onefuzz-daily-ui' exposes public endpoints with 'publicNetworkAccess' set to 'Enabled' and 'ipSecurityRestrictions' allowing 'Any' IP address. This configuration allows unrestricted public access to the application.,Restrict 'ipSecurityRestrictions' to only allow trusted IP ranges and set 'publicNetworkAccess' to 'Disabled' if public access is not required.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,181,"App Service config for 'onefuzz-daily-ui' has 'ipSecurityRestrictions' with 'ipAddress' set to 'Any' and 'action' set to 'Allow', which permits unrestricted public access to the app.",Update 'ipSecurityRestrictions' to allow only specific trusted IP addresses or ranges. Remove or modify the rule that allows 'Any' IP address.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,191,"App Service config for 'onefuzz-daily-ui' has 'scmIpSecurityRestrictions' with 'ipAddress' set to 'Any' and 'action' set to 'Allow', which permits unrestricted public access to the SCM (deployment) endpoint.",Update 'scmIpSecurityRestrictions' to allow only specific trusted IP addresses or ranges. Remove or modify the rule that allows 'Any' IP address.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,44,"App Service 'onefuzz-daily-ui' is configured with 'publicNetworkAccess' set to 'Enabled' and does not use private endpoints, increasing exposure to the public internet.",Configure a private endpoint for the App Service and set 'publicNetworkAccess' to 'Disabled' to restrict access to private networks only.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,44,"App Service 'onefuzz-daily-ui' does not specify encryption at rest settings. While App Service uses platform-managed encryption by default, explicit configuration for customer-managed keys (CMK) is not present.","If handling sensitive data, configure App Service to use customer-managed keys (CMK) for encryption at rest by integrating with Azure Key Vault.",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,44,"App Service 'onefuzz-daily-ui' has 'hostNameSslStates' with 'sslState' set to 'Disabled' for 'onefuzz-daily-ui.azurewebsites.net' and 'onefuzz-daily-ui.scm.azurewebsites.net', which disables encryption in transit for these endpoints. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates to ensure TLS is enforced for all endpoints.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,44,App Service 'onefuzz-daily-ui' does not explicitly reference Azure Key Vault for storing sensitive information such as secrets or connection strings.,Store all sensitive configuration values and secrets in Azure Key Vault and reference them securely from the App Service configuration.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-6,template.json,44,"App Service 'onefuzz-daily-ui' does not use customer-managed keys (CMK) for encryption at rest, relying only on platform-managed keys.",Integrate App Service with Azure Key Vault and configure customer-managed keys for enhanced control over encryption at rest.,N/A,AI,Generic
