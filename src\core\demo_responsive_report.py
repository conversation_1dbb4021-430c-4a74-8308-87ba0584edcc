#!/usr/bin/env python3
"""
Demo script to generate a responsive HTML report with sample data.
"""

import os
import tempfile
from pathlib import Path
from security_opt import SecurityPRReviewer


def create_demo_report():
    """Create a demo responsive HTML report with comprehensive sample data"""
    
    # Create temporary directory for demo
    demo_dir = Path("demo_reports")
    demo_dir.mkdir(exist_ok=True)
    
    # Set up minimal environment
    os.environ["ENABLE_PARAMETER_EXPANSION"] = "false"
    
    # Create comprehensive sample findings to showcase all features
    sample_findings = [
        {
            "control_id": "NS-1",
            "severity": "CRITICAL",
            "file_path": "network/security-groups.bicep",
            "line": 25,
            "description": "Network Security Group allows unrestricted inbound access from the internet on port 22 (SSH). This creates a significant security risk as it exposes SSH services to potential brute force attacks and unauthorized access attempts.",
            "remediation": "Restrict SSH access to specific IP ranges or use Azure Bastion for secure remote access. Configure NSG rules to allow SSH only from trusted networks or management subnets.",
            "matching_content": "securityRules: [\n  {\n    name: 'AllowSSH'\n    properties: {\n      access: 'Allow'\n      direction: 'Inbound'\n      sourceAddressPrefix: '*'\n      destinationPortRange: '22'\n    }\n  }\n]"
        },
        {
            "control_id": "DP-3",
            "severity": "CRITICAL", 
            "file_path": "storage/storage-account.bicep",
            "line": 15,
            "description": "Storage account is configured to allow public blob access without any network restrictions. This exposes sensitive data to unauthorized access from anywhere on the internet.",
            "remediation": "Disable public blob access and configure network access rules to restrict access to specific virtual networks or IP ranges. Enable private endpoints for secure access.",
            "matching_content": "properties: {\n  allowBlobPublicAccess: true\n  networkAcls: {\n    defaultAction: 'Allow'\n  }\n}"
        },
        {
            "control_id": "IM-2",
            "severity": "HIGH",
            "file_path": "identity/key-vault.bicep", 
            "line": 8,
            "description": "Key Vault is not using Azure Active Directory authentication and relies on access policies instead of RBAC. This limits fine-grained access control and audit capabilities.",
            "remediation": "Enable Azure AD authentication for Key Vault and migrate from access policies to Azure RBAC for better security and compliance. Configure appropriate RBAC roles for different user groups.",
            "matching_content": "properties: {\n  enableRbacAuthorization: false\n  accessPolicies: [\n    // Legacy access policy configuration\n  ]\n}"
        },
        {
            "control_id": "LT-1",
            "severity": "HIGH",
            "file_path": "monitoring/diagnostic-settings.bicep",
            "line": 12,
            "description": "Diagnostic settings are not configured for critical Azure resources, preventing proper security monitoring and compliance auditing.",
            "remediation": "Configure diagnostic settings for all critical resources to send logs to Log Analytics workspace. Enable security-relevant log categories for threat detection and compliance.",
            "matching_content": "// Missing diagnostic settings configuration\nresource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {\n  name: storageAccountName\n  // No diagnostic settings configured\n}"
        },
        {
            "control_id": "ES-1",
            "severity": "MEDIUM",
            "file_path": "compute/virtual-machines.bicep",
            "line": 45,
            "description": "Virtual machines are not configured with disk encryption at rest. Unencrypted disks pose a data security risk if physical media is compromised.",
            "remediation": "Enable Azure Disk Encryption for all VM disks using Azure Key Vault for key management. Configure both OS and data disk encryption.",
            "matching_content": "storageProfile: {\n  osDisk: {\n    createOption: 'FromImage'\n    managedDisk: {\n      storageAccountType: 'Premium_LRS'\n      // Missing encryption configuration\n    }\n  }\n}"
        },
        {
            "control_id": "NS-3",
            "severity": "MEDIUM",
            "file_path": "network/application-gateway.bicep",
            "line": 30,
            "description": "Application Gateway is not configured with Web Application Firewall (WAF) protection, leaving web applications vulnerable to common attacks.",
            "remediation": "Enable WAF on Application Gateway with OWASP Core Rule Set. Configure custom rules for application-specific protection and enable prevention mode.",
            "matching_content": "properties: {\n  sku: {\n    name: 'Standard_v2'\n    tier: 'Standard_v2'\n    // Missing WAF configuration\n  }\n}"
        },
        {
            "control_id": "AC-4",
            "severity": "LOW",
            "file_path": "database/sql-server.bicep",
            "line": 18,
            "description": "SQL Server is configured with SQL authentication enabled alongside Azure AD authentication. This creates additional attack vectors and complexity.",
            "remediation": "Disable SQL authentication and use only Azure AD authentication for better security and centralized identity management.",
            "matching_content": "properties: {\n  administratorLogin: 'sqladmin'\n  administratorLoginPassword: 'P@ssw0rd123!'\n  azureADOnlyAuthentication: false\n}"
        },
        {
            "control_id": "BC-2",
            "severity": "LOW",
            "file_path": "backup/recovery-vault.bicep",
            "line": 22,
            "description": "Recovery Services Vault does not have cross-region restore enabled, limiting disaster recovery options.",
            "remediation": "Enable cross-region restore for Recovery Services Vault to ensure business continuity in case of regional outages.",
            "matching_content": "properties: {\n  storageModelType: 'LocallyRedundant'\n  crossRegionRestoreFlag: false\n}"
        }
    ]
    
    print("🚀 Creating comprehensive responsive HTML security report demo...")
    
    # Initialize reviewer
    reviewer = SecurityPRReviewer(local_folder=str(demo_dir))
    
    # Generate responsive HTML report
    html_path = demo_dir / "responsive_security_report_demo.html"
    reviewer._export_findings_to_html(sample_findings, str(html_path))
    
    print(f"✅ Demo report generated successfully!")
    print(f"📄 Report location: {html_path.absolute()}")
    print(f"📊 Report contains {len(sample_findings)} findings across all severity levels")
    print(f"💾 File size: {html_path.stat().st_size:,} bytes")
    
    print("\n🎯 Responsive Features Demonstrated:")
    print("   • 6 responsive breakpoints (1400px+ down to 320px)")
    print("   • Touch-optimized interface for mobile devices")
    print("   • Interactive search and filtering")
    print("   • Collapsible severity sections")
    print("   • Dark mode support (system preference)")
    print("   • Print-optimized layout")
    print("   • Full keyboard navigation")
    print("   • Screen reader accessibility")
    print("   • URL hash bookmarking")
    print("   • JSON export functionality")
    
    print("\n📱 Test the responsive design by:")
    print("   1. Opening the HTML file in your browser")
    print("   2. Resizing the browser window to see breakpoints")
    print("   3. Using browser dev tools to simulate mobile devices")
    print("   4. Testing touch interactions on mobile/tablet")
    print("   5. Trying keyboard navigation (Tab, Arrow keys, Ctrl+F)")
    print("   6. Testing print preview (Ctrl+P)")
    
    print(f"\n🌐 Open in browser: file://{html_path.absolute()}")
    
    return html_path


if __name__ == "__main__":
    demo_path = create_demo_report()
    
    # Optional: Open in default browser (uncomment if desired)
    # import webbrowser
    # webbrowser.open(f"file://{demo_path.absolute()}")
