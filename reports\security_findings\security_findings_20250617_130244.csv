Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,template.json,153,"App Service config 'ipSecurityRestrictions' allows 'Any' IP with 'Allow all', exposing the app to unrestricted public access.",Restrict 'ipSecurityRestrictions' to only required IP ranges and deny all other access by default.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,template.json,162,"App Service config 'scmIpSecurityRestrictions' allows 'Any' IP with 'Allow all', exposing the SCM endpoint to unrestricted public access.",Restrict 'scmIpSecurityRestrictions' to only required IP ranges and deny all other access by default.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,153,"App Service config 'ipSecurityRestrictions' allows public access from any IP, violating public endpoint protection requirements.",Restrict 'ipSecurityRestrictions' to specific trusted IP addresses or ranges.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,162,"App Service config 'scmIpSecurityRestrictions' allows public access from any IP, violating public endpoint protection requirements.",Restrict 'scmIpSecurityRestrictions' to specific trusted IP addresses or ranges.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-3,template.json,153,"App Service config 'ipSecurityRestrictions' allows all inbound traffic by default, violating NSG best practices.",Configure 'ipSecurityRestrictions' to deny all inbound traffic by default and allow only necessary traffic.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-3,template.json,162,"App Service config 'scmIpSecurityRestrictions' allows all inbound traffic by default, violating NSG best practices.",Configure 'scmIpSecurityRestrictions' to deny all inbound traffic by default and allow only necessary traffic.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,38,"App Service hostNameSslStates: SSL is disabled for 'onefuzz-daily-ui.azurewebsites.net' and 'onefuzz-daily-ui.scm.azurewebsites.net', violating encryption in transit requirements.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates to enforce HTTPS for all endpoints.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,120,"App Service config contains 'publishingUsername' property, which may expose sensitive information in code.",Remove 'publishingUsername' from the template and use Azure Key Vault references for all credentials.,N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,NS-18,template.json,153,"App Service does not use Private Endpoints. Public network access is enabled, increasing attack surface.",Configure a Private Endpoint for the App Service and set 'publicNetworkAccess' to 'Disabled'.,N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,NS-18,template.json,162,"App Service SCM endpoint does not use Private Endpoints. Public network access is enabled, increasing attack surface.",Configure a Private Endpoint for the SCM endpoint and set 'publicNetworkAccess' to 'Disabled'.,N/A,AI,Generic
P6-Other-MEDIUM,Unknown,MEDIUM,NS-15,template.json,153,App Service config 'ipSecurityRestrictions' allows public access from any IP. Best practice is to restrict public endpoints.,Restrict 'ipSecurityRestrictions' to only required IPs or use Azure Front Door/Application Gateway for additional protection.,N/A,AI,Generic
P6-Other-MEDIUM,Unknown,MEDIUM,NS-15,template.json,162,App Service config 'scmIpSecurityRestrictions' allows public access from any IP. Best practice is to restrict public endpoints.,Restrict 'scmIpSecurityRestrictions' to only required IPs or use Azure Front Door/Application Gateway for additional protection.,N/A,AI,Generic
P6-Other-MEDIUM,Unknown,MEDIUM,NS-22,template.json,128,"App Service 'httpLoggingEnabled' is set to false, so network traffic is not being logged.",Enable 'httpLoggingEnabled' in the App Service configuration to log network traffic for monitoring and auditing.,N/A,AI,Generic
