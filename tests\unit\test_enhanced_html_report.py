#!/usr/bin/env python3
"""
Test script for enhanced HTML report generation.
"""

import os
import tempfile
import unittest
from pathlib import Path
from security_opt import SecurityPRReviewer


class TestEnhancedHTMLReport(unittest.TestCase):
    """Test cases for enhanced HTML report generation"""

    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.test_path = Path(self.test_dir)
        
        # Set up minimal environment for SecurityPRReviewer
        os.environ["ENABLE_PARAMETER_EXPANSION"] = "false"

    def tearDown(self):
        """Clean up test environment"""
        import shutil
        shutil.rmtree(self.test_dir, ignore_errors=True)

    def test_enhanced_html_generation(self):
        """Test enhanced HTML report generation with sample findings"""
        
        # Create sample findings data
        sample_findings = [
            {
                "control_id": "NS-1",
                "severity": "CRITICAL",
                "file_path": "network.bicep",
                "line": 25,
                "description": "Network Security Group not configured for subnet protection",
                "remediation": "Add NSG resource and associate with subnet to control traffic flow",
                "matching_content": "subnet: {\n  name: 'default'\n  addressPrefix: '10.0.0.0/24'\n}"
            },
            {
                "control_id": "DP-3",
                "severity": "HIGH", 
                "file_path": "storage.bicep",
                "line": 15,
                "description": "Storage account allows public access without restrictions",
                "remediation": "Configure network access rules to deny public access by default",
                "matching_content": "properties: {\n  allowBlobPublicAccess: true\n}"
            },
            {
                "control_id": "IM-1",
                "severity": "MEDIUM",
                "file_path": "keyvault.bicep", 
                "line": 8,
                "description": "Key Vault access policies not using Azure AD authentication",
                "remediation": "Enable Azure AD authentication and configure RBAC for Key Vault access"
            },
            {
                "control_id": "LT-1",
                "severity": "LOW",
                "file_path": "monitoring.bicep",
                "line": 12,
                "description": "Diagnostic settings not configured for all resources",
                "remediation": "Add diagnostic settings to send logs to Log Analytics workspace"
            }
        ]
        
        # Initialize reviewer
        reviewer = SecurityPRReviewer(local_folder=str(self.test_path))
        
        # Generate HTML report
        html_path = self.test_path / "test_report.html"
        reviewer._export_findings_to_html(sample_findings, str(html_path))
        
        # Verify HTML file was created
        self.assertTrue(html_path.exists())
        
        # Read and verify HTML content
        html_content = html_path.read_text(encoding='utf-8')
        
        # Check for enhanced features
        self.assertIn("Security Findings Report", html_content)
        self.assertIn("Infrastructure as Code Security Analysis", html_content)
        
        # Check for modern CSS features
        self.assertIn("font-awesome", html_content)
        self.assertIn("linear-gradient", html_content)
        self.assertIn("box-shadow", html_content)
        self.assertIn("border-radius", html_content)
        
        # Check for interactive elements
        self.assertIn("searchInput", html_content)
        self.assertIn("filter-btn", html_content)
        self.assertIn("severity-header", html_content)
        
        # Check for JavaScript functionality
        self.assertIn("addEventListener", html_content)
        self.assertIn("filterFindings", html_content)
        self.assertIn("exportToJson", html_content)
        
        # Check for responsive design
        self.assertIn("@media", html_content)
        self.assertIn("max-width: 768px", html_content)
        
        # Check for print styles
        self.assertIn("@media print", html_content)
        
        # Check for summary statistics
        self.assertIn("Executive Summary", html_content)
        self.assertIn("Total Findings", html_content)
        self.assertIn("Files Affected", html_content)
        self.assertIn("High Priority Issues", html_content)
        
        # Check for severity breakdown
        self.assertIn("severity-stat critical", html_content)
        self.assertIn("severity-stat high", html_content)
        self.assertIn("severity-stat medium", html_content)
        self.assertIn("severity-stat low", html_content)
        
        # Check for findings content
        self.assertIn("NS-1", html_content)
        self.assertIn("DP-3", html_content)
        self.assertIn("IM-1", html_content)
        self.assertIn("LT-1", html_content)
        
        # Check for file references
        self.assertIn("network.bicep", html_content)
        self.assertIn("storage.bicep", html_content)
        self.assertIn("keyvault.bicep", html_content)
        self.assertIn("monitoring.bicep", html_content)
        
        # Check for code snippets
        self.assertIn("code-snippet", html_content)
        self.assertIn("subnet:", html_content)
        self.assertIn("allowBlobPublicAccess", html_content)
        
        # Check for remediation sections
        self.assertIn("Recommended Fix", html_content)
        self.assertIn("Add NSG resource", html_content)
        self.assertIn("Configure network access", html_content)
        
        print(f"✅ Enhanced HTML report generated successfully: {html_path}")
        print(f"📊 Report contains {len(sample_findings)} findings")
        print(f"📄 HTML file size: {html_path.stat().st_size} bytes")

    def test_empty_findings_html(self):
        """Test HTML generation with no findings"""
        
        reviewer = SecurityPRReviewer(local_folder=str(self.test_path))
        html_path = self.test_path / "empty_report.html"
        
        # This should log a warning and return without creating file
        reviewer._export_findings_to_html([], str(html_path))
        
        # File should not be created for empty findings
        self.assertFalse(html_path.exists())

    def test_html_export_integration(self):
        """Test the full export_findings method with HTML format"""
        
        sample_findings = [
            {
                "control_id": "NS-2",
                "severity": "HIGH",
                "file_path": "app.bicep",
                "line": 30,
                "description": "Application Gateway not configured with WAF",
                "remediation": "Enable Web Application Firewall on Application Gateway"
            }
        ]
        
        reviewer = SecurityPRReviewer(local_folder=str(self.test_path))
        
        # Test HTML-only export
        reviewer.export_findings(sample_findings, format="html", output_dir=str(self.test_path))
        
        # Check that HTML file was created
        html_files = list(self.test_path.glob("security_findings_*.html"))
        self.assertEqual(len(html_files), 1)
        
        html_file = html_files[0]
        html_content = html_file.read_text(encoding='utf-8')
        
        # Verify content
        self.assertIn("NS-2", html_content)
        self.assertIn("app.bicep", html_content)
        self.assertIn("Application Gateway", html_content)
        
        print(f"✅ Integration test passed: {html_file}")

    def test_html_accessibility_features(self):
        """Test HTML accessibility features"""
        
        sample_findings = [
            {
                "control_id": "AC-1",
                "severity": "MEDIUM",
                "file_path": "test.bicep",
                "line": 1,
                "description": "Test finding for accessibility",
                "remediation": "Test remediation"
            }
        ]
        
        reviewer = SecurityPRReviewer(local_folder=str(self.test_path))
        html_path = self.test_path / "accessibility_test.html"
        reviewer._export_findings_to_html(sample_findings, str(html_path))
        
        html_content = html_path.read_text(encoding='utf-8')
        
        # Check for accessibility features
        self.assertIn('lang="en"', html_content)  # Language attribute
        self.assertIn('meta name="viewport"', html_content)  # Viewport meta tag
        # Note: No images in current implementation, so no alt text needed
        
        # Check for semantic HTML
        self.assertIn('<h1>', html_content)
        self.assertIn('<h2>', html_content)
        self.assertIn('<button', html_content)
        
        # Check for ARIA attributes or roles (if implemented)
        # self.assertIn('role=', html_content)  # Uncomment if ARIA roles added
        
        print("✅ Accessibility features verified")


if __name__ == "__main__":
    # Run tests with verbose output
    unittest.main(verbosity=2)
