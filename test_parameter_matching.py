#!/usr/bin/env python3
"""
Test script to verify parameter file matching logic
"""

import os
import sys
import logging
from pathlib import Path

# Add src/core to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'core'))

from template_parameter_expander import TemplateParameterExpander

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')

def test_parameter_matching():
    """Test parameter file matching with various scenarios"""
    
    # Create test directory
    test_dir = Path("test_matching")
    test_dir.mkdir(exist_ok=True)
    
    # Create test files matching your directory structure
    test_files = {
        # Template files
        "Grafana.deploymentTemplate.json": '{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "resources": []}',
        "KustoScripts.template.json": '{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "resources": []}',
        "roleAssignment.deploymentTemplate.json": '{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "resources": []}',

        # Parameter files (these should NOT be identified as templates)
        "Grafana.deploymentParameters.json": '{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"param1": {"value": "test"}}}',
        "KustoScripts.parameters.json": '{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"param2": {"value": "test"}}}',
        "roleAssignment.deploymentParameters.json": '{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"param3": {"value": "test"}}}',
    }
    
    # Write test files
    for filename, content in test_files.items():
        (test_dir / filename).write_text(content)
    
    # Initialize expander
    expander = TemplateParameterExpander()
    
    # Test cases matching your directory structure
    test_cases = [
        ("Grafana.deploymentTemplate.json", ["Grafana.deploymentParameters.json"]),
        ("KustoScripts.template.json", ["KustoScripts.parameters.json"]),
        ("roleAssignment.deploymentTemplate.json", ["roleAssignment.deploymentParameters.json"]),
    ]
    
    print("🧪 Testing Parameter File Matching")
    print("=" * 50)
    
    for template_file, expected_params in test_cases:
        template_path = str(test_dir / template_file)
        print(f"\n📄 Template: {template_file}")
        
        # Find matching parameter files
        found_params = expander._find_matching_parameter_files(template_path)
        found_basenames = [os.path.basename(p) for p in found_params]
        
        print(f"   Expected: {expected_params}")
        print(f"   Found:    {found_basenames}")
        
        # Check if all expected files were found
        all_found = all(param in found_basenames for param in expected_params)
        status = "✅ PASS" if all_found else "❌ FAIL"
        print(f"   Status:   {status}")
        
        if not all_found:
            missing = [p for p in expected_params if p not in found_basenames]
            print(f"   Missing:  {missing}")
    
    # Test with environment variables
    print(f"\n🔧 Testing with Custom Environment Variables")
    print("=" * 50)
    
    # Set custom patterns
    os.environ['TEMPLATE_PATTERNS'] = '*Template*,*template*,main.json,*.json,*app*.json'
    os.environ['PARAMETER_PATTERNS'] = '*Param*,*Parameter*,*params*,*parameters*'
    os.environ['TEMPLATE_IDENTIFIERS'] = 'Template,template,main,deploy,ArmTemplate'
    os.environ['PARAMETER_IDENTIFIERS'] = 'Param,Parameter,params,parameters,ArmParam'
    
    # Test again with custom patterns
    template_path = str(test_dir / "ArmTemplate-FabricAppA.json")
    found_params = expander._find_matching_parameter_files(template_path)
    found_basenames = [os.path.basename(p) for p in found_params]
    
    print(f"Template: ArmTemplate-FabricAppA.json")
    print(f"Found:    {found_basenames}")
    print(f"Expected: ['Grafana.deploymentParameters.json']")

    # Test file type identification
    print(f"\n🔍 Testing File Type Identification")
    print("=" * 50)

    for filename, content in test_files.items():
        file_path = str(test_dir / filename)
        template_type = expander._determine_template_type(content)

        # Determine if it should be a template or parameter file
        is_param_file = any(keyword in filename.lower() for keyword in ['parameters', 'deploymentparameters'])
        expected_type = "ARM_PARAMETER" if is_param_file else "ARM"

        print(f"File: {filename}")
        print(f"  Detected Type: {template_type}")
        print(f"  Expected Type: {expected_type}")

        if is_param_file and template_type == "ARM_PARAMETER":
            print(f"  ✅ OK: Parameter file correctly identified")
        elif is_param_file and template_type == "ARM":
            print(f"  ❌ ERROR: Parameter file incorrectly identified as ARM template!")
        elif not is_param_file and template_type == "ARM":
            print(f"  ✅ OK: Template file correctly identified")
        else:
            print(f"  ⚠️  UNKNOWN: Unexpected result")
        print()

    # Cleanup
    import shutil
    shutil.rmtree(test_dir)

    print(f"\n✨ Test completed!")

if __name__ == "__main__":
    test_parameter_matching()
