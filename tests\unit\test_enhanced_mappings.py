#!/usr/bin/env python3
"""
Test script for enhanced resource-control mappings
"""

import logging
import sys
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_csv_files():
    """Test if CSV files exist and are readable."""
    print("🔍 Testing CSV Files...")
    
    csv_files = {
        "Identity Management": "SecurityBenchmarks/identity_management.csv",
        "Network Security": "SecurityBenchmarks/network_security_with_urls.csv", 
        "Data Protection": "SecurityBenchmarks/data_protection.csv"
    }
    
    all_exist = True
    for domain, path in csv_files.items():
        csv_path = Path(path)
        if csv_path.exists():
            print(f"✅ {domain}: {path}")
            
            # Try to read first few lines
            try:
                with open(csv_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()[:3]
                    print(f"   📄 {len(lines)} header lines read")
                    if lines:
                        print(f"   📋 Headers: {lines[0].strip()}")
            except Exception as e:
                print(f"   ❌ Error reading file: {e}")
                all_exist = False
        else:
            print(f"❌ {domain}: {path} - NOT FOUND")
            all_exist = False
    
    return all_exist

def test_enhanced_mapper():
    """Test the enhanced mapper functionality."""
    print("\n🧪 Testing Enhanced Mapper...")
    
    try:
        from enhanced_resource_control_mappings import EnhancedResourceControlMapper
        
        print("✅ Import successful")
        
        # Initialize mapper
        mapper = EnhancedResourceControlMapper()
        print("✅ Mapper initialized")
        
        # Test control database
        control_count = len(mapper.control_database)
        print(f"📊 Loaded {control_count} controls")
        
        # Test URL links database
        links_count = len(mapper.url_links_database)
        print(f"🔗 Found {links_count} controls with URLs")
        
        # Test resource mappings
        resource_count = len(mapper.resource_mappings)
        print(f"🏗️ Generated {resource_count} resource category mappings")
        
        # Test specific control lookup
        test_controls = ["IM-1", "NS-1", "DP-1"]
        print(f"\n🔍 Testing specific controls:")
        
        for control_id in test_controls:
            if control_id in mapper.control_database:
                control = mapper.control_database[control_id]
                links = mapper.get_control_links(control_id)
                url_count = len(links.get('raw_links', []))
                print(f"   ✅ {control_id} ({control['domain']}): {url_count} URLs")
            else:
                print(f"   ❌ {control_id}: Not found")
        
        # Test resource type mapping
        print(f"\n🏗️ Testing resource type mappings:")
        test_resources = [
            "Microsoft.Storage/storageAccounts",
            "Microsoft.KeyVault/vaults",
            "Microsoft.Network/networkSecurityGroups"
        ]
        
        for resource_type in test_resources:
            controls = mapper.get_controls_for_resource_type(resource_type)
            print(f"   📋 {resource_type}: {len(controls)} applicable controls")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        logger.exception("Detailed error:")
        return False

def test_integration_readiness():
    """Test if the system is ready for integration."""
    print("\n🔧 Testing Integration Readiness...")
    
    # Check if security_opt.py exists
    security_opt_path = Path("security_opt.py")
    if security_opt_path.exists():
        print("✅ security_opt.py found")
    else:
        print("❌ security_opt.py not found")
        return False
    
    # Check if integration script exists
    integration_path = Path("integrate_enhanced_mappings.py")
    if integration_path.exists():
        print("✅ Integration script found")
    else:
        print("❌ Integration script not found")
        return False
    
    return True

def main():
    """Main test function."""
    print("🧪 Enhanced Resource-Control Mapping System Test")
    print("=" * 60)
    
    success = True
    
    # Test CSV files
    if not test_csv_files():
        print("❌ CSV file test failed")
        success = False
    
    # Test enhanced mapper
    if not test_enhanced_mapper():
        print("❌ Enhanced mapper test failed")
        success = False
    
    # Test integration readiness
    if not test_integration_readiness():
        print("❌ Integration readiness test failed")
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 All tests passed! System is ready for integration.")
        print("\nNext steps:")
        print("1. Run: python integrate_enhanced_mappings.py")
        print("2. Test the updated security_opt.py")
        print("3. Generate reports with enhanced URL links")
    else:
        print("❌ Some tests failed. Please fix the issues before proceeding.")
        sys.exit(1)

if __name__ == "__main__":
    main()
