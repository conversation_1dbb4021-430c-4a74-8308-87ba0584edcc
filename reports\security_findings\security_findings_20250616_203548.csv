Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
CRITICAL,NS-2,LacpGeo.Template.json,116,"Cosmos DB resource ('Microsoft.DocumentDB/databaseAccounts') is configured with 'publicNetworkAccess': 'Enabled', which exposes the database to the public internet. This increases the risk of unauthorized access or data breaches.",Set 'publicNetworkAccess' to 'Disabled' to restrict access to the Cosmos DB account. Use private endpoints or virtual network service endpoints to securely access the database.,N/A,AI,Generic
CRITICAL,NS-2,LacpGlobal.Template.json,38,"CosmosDB Account 'publicNetworkAccess' is explicitly set to 'Enabled', exposing the database to the public internet. This greatly increases the risk of unauthorized or malicious access.","Set 'publicNetworkAccess' to 'Disabled' and use Private Endpoints for secure, internal-only access to the CosmosDB account.",N/A,AI,Generic
CRITICAL,NS-5,LacpGlobal.Template.json,0,"There are no Private Endpoints configured for Storage Accounts, CosmosDB, or Key Vault. These resources are accessible via public endpoints, which increases the attack surface.",Deploy Private Endpoints for each resource to ensure all traffic remains within the private Azure network.,N/A,AI,Generic
CRITICAL,NS-1,LacpRegion.Template.json,1226,"The CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' is deployed with 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false. This means the resource is exposed to the public internet without network protection from NSGs or firewalls, violating ASB NS-1.","Set 'publicNetworkAccess' to 'Disabled', enable 'isVirtualNetworkFilterEnabled', configure 'virtualNetworkRules', and restrict access to trusted networks only using NSG or Azure Firewall.",N/A,AI,Generic
CRITICAL,NS-2,LacpRegion.Template.json,1226,"CosmosDB is publicly accessible ('publicNetworkAccess':'Enabled', 'ipRules':[]), offering no restriction to public endpoints, violating ASB NS-2.",Restrict CosmosDB to only approved IP addresses and/or virtual networks. Set 'publicNetworkAccess' to 'Disabled' or add IP/vnet restrictions.,N/A,AI,Generic
CRITICAL,DP-3,LacpStamp.Parameters-LacpStampResources.json,60,"The 'dasStorageAccountKey' parameter contains the output of a deployment, which may expose a sensitive storage account key inline in parameter values. Storing or transmitting secrets or keys within template parameters (even if referencing other deployments) may risk sensitive information disclosure.","Never store or transmit secrets, such as storage account keys, in parameter files or as plain values. Use Azure Key Vault references for all secret values, and ensure the deployment retrieves sensitive material securely via secure references instead of plain output.",N/A,AI,Generic
CRITICAL,DP-3,LacpStamp.Parameters-LacpStampResources.json,59,"The 'dasStorageAccountName' parameter is likely used as an identifier, but combining it with an inline storage key ('dasStorageAccountKey') in the same template may indicate loose secret management. If this is an actual secret or sensitive name, it should be stored in Key Vault, not inline.","Verify if 'dasStorageAccountName' is a public identifier or a sensitive value. For sensitive identifiers or names (e.g., keys, secrets, or confidential naming), store only in Key Vault and use secure references in templates.",N/A,AI,Generic
HIGH,NS-1,IngestionStorageAccount.Template.json,1,"The storage account resources do not implement any network security controls such as Network Security Groups (NSGs), Azure Firewall, or Private Endpoints. By default, Azure Storage accounts permit traffic from all networks unless network rules restricting access are in place. This leaves the storage account potentially exposed to the public internet or other undesired sources.","Restrict network access to the storage accounts by configuring the 'networkAcls' property to deny public network access and only allow traffic from selected virtual networks, subnets, or by using private endpoints. Ensure that 'publicNetworkAccess' is set to 'Disabled' where public access is not required.",N/A,AI,Generic
HIGH,NS-2,IngestionStorageAccount.Template.json,1,"No restrictions are configured for public endpoints on the storage accounts, leaving them potentially accessible from the internet. No 'networkAcls' property or 'publicNetworkAccess' setting is present, so public endpoints may remain enabled by default.","Explicitly set 'publicNetworkAccess' to 'Disabled' or configure 'networkAcls' to restrict access only to trusted networks or via private endpoints, thus eliminating unnecessary public exposure.",N/A,AI,Generic
HIGH,NS-1,LacpBilling.Template.json,579,No network security controls (Network Security Groups or Azure Firewall) are defined to protect the deployed storage accounts. ASB NS-1 recommends using NSGs or Azure Firewall to protect sensitive resources such as storage accounts.,"Integrate Private Endpoints for each storage account and restrict network access by disabling public network access. Alternatively, associate NSGs at the subnet level (if accounts are in a VNet) or require traffic to only permitted subnets/IPs using firewall rules.",N/A,AI,Generic
HIGH,NS-2,LacpBilling.Template.json,584,"Storage account deployment does not specify that public network access is disabled. Absence of the 'publicNetworkAccess' property or explicit 'Allow' may expose the storage accounts to the public internet, increasing risk according to ASB NS-2.",Add the 'publicNetworkAccess': 'Disabled' property in each storage account's properties block to ensure accounts are only accessible over private endpoints or from designated networks.,N/A,AI,Generic
HIGH,NS-2,LacpBillingExhaust.Template.json,27,"The Azure Data Explorer (Kusto) cluster does not specify any network access controls, private endpoints, or public network restrictions. By default, this exposes the service to the public internet, increasing the risk of unauthorized access.","Restrict public network access to the Kusto cluster by configuring 'publicNetworkAccess' property to 'Disabled' and use Private Endpoints for secure access. If public access is required, use IP whitelisting and implement Service Endpoints or Private Link as appropriate.",N/A,AI,Generic
HIGH,NS-1,LacpBillingExhaust.Template.json,27,"There are no network security groups (NSGs), firewalls, or other network-layer protections defined for the cluster or associated resources. This increases attack surface and does not meet the requirement to protect resources with network security controls.","Define and associate NSGs or implement Azure Firewall to restrict access to the cluster to only trusted networks or subnets. For clusters in a VNet, ensure subnet-level NSGs are configured according to least privilege.",N/A,AI,Generic
HIGH,DP-3,LacpBillingExhaustExport.Template.json,19,"Potential sensitive information (adxExhaustUri and adxExhaustDataIngestionUri) is provided as parameters of type 'string' without evidence of Key Vault reference integration or 'secureString' type, risking accidental exposure or improper handling of secrets such as Kusto cluster URIs or authentication endpoints.",Mark sensitive parameters like 'adxExhaustUri' and 'adxExhaustDataIngestionUri' as 'secureString' and recommend Key Vault references for secrets or sensitive endpoint URIs. Ensure all secrets or sensitive connection strings are securely stored and referenced.,N/A,AI,Generic
HIGH,NS-1,LacpGeo.Template.json,116,"Cosmos DB does not have any virtual network filters or rules enabled ('isVirtualNetworkFilterEnabled': false, 'virtualNetworkRules': []). This means there is no network-level control restricting access to the resource.",Enable 'isVirtualNetworkFilterEnabled' and define the required 'virtualNetworkRules' to restrict access to the Cosmos DB resource from only authorized subnets.,N/A,AI,Generic
HIGH,NS-5,LacpGeo.Template.json,116,"Cosmos DB does not use private endpoints for connectivity. Without private endpoints, resources are accessible over the public Internet, increasing exposure and attack surface.",Enable and configure Azure Private Endpoints for Cosmos DB to restrict resource access to within the trusted virtual network boundary.,N/A,AI,Generic
HIGH,NS-13,LacpGlobal.Template.json,42,CosmosDB resource has 'isVirtualNetworkFilterEnabled' set to false and no virtualNetworkRules. This allows unrestricted external access rather than restricting to specific trusted subnets.,Enable 'isVirtualNetworkFilterEnabled' and define 'virtualNetworkRules' with the appropriate subnet(s) to restrict access.,N/A,AI,Generic
HIGH,NS-6,LacpGlobal.Template.json,42,"CosmosDB account does not use Azure Virtual Network Service Endpoints, leaving it accessible over public internet routes.",Enable VNet Service Endpoints in your Azure Virtual Network and restrict CosmosDB account access only to subnets with those endpoints.,N/A,AI,Generic
HIGH,NS-7,LacpGlobal.Template.json,0,"No Network Security Groups (NSGs) are defined or associated with the deployed resources (Storage, Key Vault, CosmosDB), which increases exposure to unregulated network traffic.",Define and assign Network Security Groups with appropriate rules to restrict inbound and outbound access to these resources.,N/A,AI,Generic
HIGH,NS-1,LacpGlobal.Template.json,0,"Sensitive resources (Storage Account, Key Vault, CosmosDB) are deployed without network boundary control (NSGs or Firewall), violating ASB recommendations for network isolation.","Protect these resources using NSGs, deploy Azure Firewall, or both to restrict and monitor traffic to required sources only.",N/A,AI,Generic
HIGH,NS-14,LacpRegion.Template.json,1226,"CosmosDB does not use private endpoints and is exposed to public networks ('isVirtualNetworkFilterEnabled': false, 'virtualNetworkRules': []). This violates the ASB guidance to use private endpoints.",Add a private endpoint to CosmosDB and disable public network access. Configure 'virtualNetworkRules' to allow traffic only from trusted VNets.,N/A,AI,Generic
HIGH,DP-6,LacpRegion.Template.json,1226,"CosmosDB does not specify use of customer-managed keys (CMK) in the configuration. According to ASB DP-6, sensitive data should be encrypted using CMK.",Configure CosmosDB to use customer-managed keys by specifying a Key Vault key resource in the 'keyVaultKeyUri' property for encryption.,N/A,AI,Generic
HIGH,DP-9,LacpRegion.Template.json,1226,Minimal TLS version for CosmosDB is parameterized and may not enforce TLS 1.2+. ASB DP-9 requires encryption in transit with a minimum of TLS 1.2.,"Ensure 'minimalTlsVersion' is set to at least 'TLS1_2', preferably 'TLS1_2' or higher.",N/A,AI,Generic
HIGH,DP-3,LacpRegion.Template.json,1583,"Sensitive secrets such as primary account keys and master keys are programmatically extracted and written into Key Vault via template logic. If these are not access-restricted or if Key Vault is not securely managed, this can create unnecessary exposure of sensitive keys.","Use Key Vault references, managed identities, and RBAC to tightly control access to these secrets. Regularly rotate keys and ensure auditing is enabled on Key Vault.",N/A,AI,Generic
HIGH,NS-5,LacpRegion.Template.json,408,"Azure Storage Accounts do not use private endpoints, leaving them potentially exposed. ASB NS-5 recommends private endpoints for all critical storage.",Deploy Azure Private Endpoints for each storage account resource and disable public network access.,N/A,AI,Generic
HIGH,NS-2,LacpRegion.Template.json,408,"Some Storage Accounts have no explicit network access restrictions or firewall settings beyond 'allowBlobPublicAccess' set to false, thereby not minimizing public exposure as required by ASB NS-2.",Restrict public network access for all storage accounts. Set 'networkAcls'/'firewall' to only permit access from required VNets/Subnets or approved IPs. Consider 'publicNetworkAccess': 'Disabled'.,N/A,AI,Generic
HIGH,NS-1,LacpStamp.Template.json,1,"Storage Accounts and Key Vaults are deployed without any reference to network security controls such as Network Security Groups (NSGs), Azure Firewall, or firewall rules at the resource level. This leaves them potentially exposed to public network access.","Restrict Storage Account and Key Vault network access by configuring network rules (allow only necessary subnets, use 'selected networks', deny public network access by default, and/or require Azure Firewall or NSGs at associated subnets).",N/A,AI,Generic
HIGH,NS-2,LacpStamp.Template.json,1,Storage Accounts and Key Vaults do not have firewall rules or private endpoints configured and are accessible from all networks by default. This may expose these resources and their secrets to the public internet.,"Configure Key Vault and Storage Account to only allow access from trusted networks and/or via private endpoints. Set firewall rules to block public access unless strictly required. Where possible, set 'publicNetworkAccess' to 'Disabled'.",N/A,AI,Generic
HIGH,NS-5,LacpStamp.Template.json,1,"No private endpoints are defined for any Storage Account, Key Vault, or Redis resources. All communication occurs over public endpoints, increasing exposure to network attacks.","Implement Azure Private Endpoints for Storage Accounts, Key Vaults, and other sensitive resources so that access is only possible within a private virtual network.",N/A,AI,Generic
HIGH,NS-3,LacpStamp.Template.json,1,"No Network Security Groups (NSGs) are defined or referenced anywhere in the template. Without NSGs, resources are not protected at the subnet level.","Deploy and configure NSGs for subnets containing sensitive workloads, and apply least-privilege inbound and outbound network rules.",N/A,AI,Generic
HIGH,AM-1,LacpStamp.Template.json,1,"Several Key Vault access policies grant broad permissions (including all actions on keys, secrets, and certificates) to service principals and user objects. This violates the principle of least privilege.",Audit and reduce Key Vault access policies. Assign only minimally necessary permissions per principal and avoid granting broad 'all actions' rights unless strictly required.,N/A,AI,Generic
HIGH,NS-1,ReadAdxExhaust.Template.json,15,"The Microsoft.Kusto/cluster resource is deployed without any configuration for network security groups (NSGs) or Azure Firewall, potentially exposing the Kusto cluster to untrusted networks. This violates network protection requirements for sensitive Azure resources.","Configure the Kusto cluster to be deployed within a subnet protected by an NSG with least privilege rules, or restrict inbound access using Azure Firewall or Private Endpoints. Where available, use the 'virtualNetworkConfiguration' property in the resource to restrict cluster access to selected VNets.",N/A,AI,Generic
HIGH,NS-2,ReadAdxExhaust.Template.json,15,"No explicit configuration is present to prevent the creation of a public endpoint for the Kusto (ADX) cluster. By default, Microsoft.Kusto/cluster resources may expose a public endpoint unless network restrictions are applied.","Explicitly configure private endpoints or set up Service Endpoints to ensure that only trusted network locations can reach this resource. If public network access is not required, disable it.",N/A,AI,Generic
HIGH,IM-8,ReadUsageAccount.Template.json,19,"The 'Microsoft.UsageBilling/accounts' resource does not declare a managed identity ('identity' block is missing). According to ASB IM-8, managed identities should be used for secure resource-to-resource authentication. The template variable 'enableSystemAssignedIdentity' is set to true but is not utilized in the resource definition.",Add an 'identity' property with 'type' set to 'SystemAssigned' to the resource definition for 'Microsoft.UsageBilling/accounts' to enable managed identity support.,N/A,AI,Generic
HIGH,IM-6,RoleAssignment.Template.json,48,"The template assigns the built-in 'Contributor' role to the 'Ev2BuildoutServicePrincipalId'. The Contributor role grants broad permissions, including the ability to modify most resources, which may exceed what is needed for the service principal. This violates the principle of least privilege.","Review the permissions required by the 'Ev2BuildoutServicePrincipalId' and assign a more restrictive, custom role if feasible. Limit the scope of the assignment where possible to avoid unnecessary exposure.",N/A,AI,Generic
HIGH,IM-7,RoleAssignment.Template.json,48,Assigning broad 'Contributor' permissions to an application identity (service principal) increases the risk surface if the identity is compromised. Application identities should have restricted and purpose-specific permissions.,Restrict the application/service principal's access by using a least-privilege custom role and assigning only specific actions needed. Regularly review and rotate credentials for the service principal.,N/A,AI,Generic
HIGH,AM-1,RoleAssignment.Template.json,48,"The role assignment grants full Contributor rights to 'Ev2BuildoutServicePrincipalId', which contravenes the least privilege best practice.","Limit assignment to only necessary actions using custom RBAC roles, and regularly review access assignments.",N/A,AI,Generic
HIGH,NS-1,TrafficManagerEndpoints.Template.json,43,"There is no reference to Azure Firewall or Network Security Group (NSG) protection for the 'Microsoft.Network/trafficManagerProfiles/externalEndpoints' resource. All endpoints created via this template may be unprotected, violating ASB NS-1.",Integrate Azure Firewall or associate Network Security Groups with the attached resources to control and restrict network traffic. Ensure only required traffic is permitted.,N/A,AI,Generic
HIGH,NS-2,TrafficManagerEndpoints.Template.json,43,"Traffic Manager external endpoints inherently expose public endpoints. Without restrictions or validation on the 'target' property, this configuration could expose backend services publicly, violating ASB NS-2.","Restrict public accessibility by whitelisting trusted IPs, using authentication, or considering alternatives such as internal load balancers or private endpoints where possible.",N/A,AI,Generic
HIGH,NS-3,TrafficManagerEndpoints.Template.json,43,"No Network Security Groups are referenced or enforced for network-level traffic control on deployed endpoints, which violates ASB NS-3.","Deploy and associate appropriate NSGs to control inbound and outbound traffic to all Azure resources exposed to a network, including external endpoints created by this template.",N/A,AI,Generic
MEDIUM,NS-3,IngestionStorageAccount.Template.json,1,"There is no reference to the use or association of a Network Security Group (NSG) for controlling inbound/outbound traffic to storage-related subnets or access paths. Without NSGs, network-layer protection is lacking.","Attach NSGs to the relevant subnets where the storage accounts may be accessible, and configure rules to allow only necessary traffic from expected sources.",N/A,AI,Generic
MEDIUM,DP-1,IngestionStorageAccount.Template.json,1,"While the storage accounts will have encryption at rest enabled by default in Azure, the template does not specify explicit customer-managed keys (CMK) for encryption nor makes any reference to encryption settings. This leaves the setup using only platform-managed keys (PMK) and provides no assurance of customer control.","Explicitly set 'encryption' properties for the storage accounts, referencing a customer-managed key in Azure Key Vault if required for your compliance or regulatory needs.",N/A,AI,Generic
MEDIUM,DP-3,IngestionStorageAccount.Template.json,1,"The template does not leverage Azure Key Vault for the storage of sensitive configuration such as keys or connection strings. If these are managed elsewhere or injected later, the template gives no assurance.","Reference Azure Key Vault for sensitive secret, key, and configuration storage required by storage accounts, and avoid keeping any sensitive values in the template or in plain text.",N/A,AI,Generic
MEDIUM,DP-1,LacpBillingExhaust.Template.json,27,"There is no explicit configuration for encryption at rest for the Kusto cluster. By default, platform-managed keys are used, but customer-managed keys (CMK) are recommended for sensitive data to meet compliance and data sovereignty requirements.",Configure the Kusto cluster to use customer-managed keys (CMK) for at-rest encryption if compliance or security policy mandates it. Ensure the 'keyVaultProperties' or equivalent is specified to link a managed Key Vault key.,N/A,AI,Generic
MEDIUM,DP-2,LacpBillingExhaust.Template.json,27,There is no explicit enforcement of TLS version for connections to the Azure Data Explorer cluster. Using older TLS versions increases the risk of interception and cryptographic attacks.,Explicitly specify minimum TLS version as 1.2 or later for the Kusto cluster resource by setting the 'minimumTlsVersion' property (if supported by the resource/API version) to enforce strong cryptographic protocols.,N/A,AI,Generic
MEDIUM,AM-1,LacpBillingExhaust.Template.json,78,"The '.add database ... viewers (""aadgroup=<EMAIL>"")' script grants database viewer access to a broad group (likely all users of the '<EMAIL>' AAD group) without documented justification or evidence of least privilege.",Ensure only required users and groups are granted viewer access. Review the composition of the '<EMAIL>' group and restrict access to only those who need it for business purposes.,N/A,AI,Generic
MEDIUM,NS-2,LacpBillingExhaustExport.Template.json,84,The Data Export resource's 'adxParameters.kustoUri' and 'dataIngestionUri' are directly injected from parameters. There is no evidence that private endpoints or network security restrictions are enforced on the referenced Azure Data Explorer (ADX)/Kusto endpoints. This exposes a risk of public accessibility.,Ensure that the referenced ADX/Kusto endpoints use private endpoints or have IP firewall rules to restrict access. Validate the URIs reference non-public endpoints and restrict access to only necessary resources.,N/A,AI,Generic
MEDIUM,DP-2,LacpBillingExhaustExport.Template.json,84,"There is no indication that data exported to ADX (via 'exportType': 'Adx') enforces or validates encryption-in-transit (e.g., minimum TLS version) or at-rest for exported data. While ADX by default uses encrypted connections, the template does not enforce or validate this control.","Explicitly document and, if possible, configure the minimum TLS version or enforce encryption requirements on the ADX endpoints. Ensure destination storage/services enforce encryption at rest.",N/A,AI,Generic
MEDIUM,NS-3,LacpGeo.Template.json,116,No Network Security Groups (NSGs) are configured for network access control to sensitive resources such as Cosmos DB or Key Vault. NSGs are essential to restrict traffic and reduce attack surface.,Attach Network Security Groups (NSGs) with restrictive rules to subnets used by these services. Allow traffic only from trusted sources and required ports.,N/A,AI,Generic
MEDIUM,NS-6,LacpGeo.Template.json,116,Cosmos DB is not configured to use Virtual Network Service Endpoints. Service Endpoints provide additional network isolation and protection for Azure service resources.,Enable Virtual Network Service Endpoints for your Cosmos DB resource to ensure only traffic within configured subnets can access the account.,N/A,AI,Generic
MEDIUM,NS-14,LacpGlobal.Template.json,0,"No Virtual Network Service Endpoints are enabled for Storage Account or Key Vault resources, leaving them accessible over the public Azure backbone.",Enable Service Endpoints for Microsoft.Storage and Microsoft.KeyVault in your virtual networks and restrict access to trusted subnets only.,N/A,AI,Generic
MEDIUM,AM-1,LacpGlobal.Template.json,105,"Key Vault 'lacp-<cloudName>-kv' access policies grant broad privileges ('Get', 'List', 'Set', 'Delete', 'Recover', 'Backup', 'Restore') to a single principal (lacpAadObjectId), potentially violating least privilege.",Review and reduce the assigned permissions to each identity according to the principle of least privilege. Only assign necessary actions for each role.,N/A,AI,Generic
MEDIUM,DP-6,LacpGlobal.Template.json,105,"No indication of Customer-Managed Keys (CMKs) for Key Vault, Storage Account, or CosmosDB: all are likely encrypted at rest using Microsoft-managed keys (default behavior), which may not meet stricter data security or compliance needs.","Integrate customer-managed keys (CMKs) for each data resource (Storage Account, CosmosDB, Key Vault) using Azure Key Vault-backed keys.",N/A,AI,Generic
MEDIUM,NS-17,LacpRegion.Template.json,1226,"CosmosDB account has 'isVirtualNetworkFilterEnabled' set to false and 'virtualNetworkRules': [], indicating no Service Endpoints or NSGs are enforced.",Enable 'isVirtualNetworkFilterEnabled' and configure 'virtualNetworkRules' to restrict access through VNets with service endpoints.,N/A,AI,Generic
MEDIUM,DP-1,LacpRegion.Template.json,408,"Azure Storage Accounts do not specify customer-managed keys (CMK) for encryption at rest, even though they use storage account defaults. ASB DP-1 and DP-6 require explicitly enabling and documenting encryption.","Explicitly configure CMK-based encryption for storage accounts handling sensitive data, and document key management practices.",N/A,AI,Generic
MEDIUM,DP-3,LacpRegion.Template.json,408,"Several storage account connection strings are written as Key Vault secrets. While this is better than inline secrets, storing secrets as connection strings (account keys) is discouraged.","Eliminate use of shared keys/connection strings for authentication in favor of managed identities. If connection strings are necessary, closely restrict access to these secrets in Key Vault.",N/A,AI,Generic
MEDIUM,DP-5,LacpRegion.Template.json,952,"Backup and recovery policies (for Storage and DataFactory) are defined, but there is no explicit monitoring or alerting on backup failures in the template.",Implement monitoring and alerting for backup failures and validate recovery operations regularly.,N/A,AI,Generic
MEDIUM,NS-15,LacpStamp.Template.json,1,No use of Virtual Network Service Endpoints for the Azure Storage Accounts or Key Vaults. Service Endpoints could provide additional protection by securing the service to only traffic from selected virtual networks.,Enable Virtual Network Service Endpoints for Storage Accounts and Key Vaults where Private Endpoints are not feasible.,N/A,AI,Generic
MEDIUM,DP-6,LacpStamp.Template.json,1,"No configuration exists for Storage Accounts, Key Vaults, or Redis to use Customer-Managed Keys (CMK) for encryption at rest. This means Microsoft-managed keys are used, which may not meet stricter compliance requirements.","For sensitive workloads, configure supported resources to use Customer-Managed Keys (CMK) for encryption at rest using Azure Key Vault.",N/A,AI,Generic
MEDIUM,DP-5,LacpStamp.Template.json,1,"There is no explicit configuration for backup and recovery on Key Vaults, Storage Accounts, or Redis Cache. Lack of defined backup strategies can risk data loss.","Ensure backup strategies are defined for critical data stores, and configure periodic backups for Storage Accounts and Redis as appropriate.",N/A,AI,Generic
MEDIUM,IM-3,LacpStamp.Template.json,1,There is no evidence of Conditional Access Policies being enforced for privileged or sensitive operations.,"Ensure Conditional Access Policies are enabled in Azure AD, especially for administrative users and service principals with access to critical assets.",N/A,AI,Generic
MEDIUM,IM-8,ReadAdxExhaust.Template.json,15,"The template does not assign or make use of managed identities for the Kusto cluster, which can be used for secure resource-to-resource authentication without secrets.","Enable and configure a user-assigned or system-assigned managed identity for the Kusto cluster, and ensure subsequent services rely on this identity for authentication.",N/A,AI,Generic
MEDIUM,DP-1,ReadAdxExhaust.Template.json,15,"There is no evidence of encryption at rest settings being defined for the Kusto cluster. While ADX clusters use encryption at rest by default, explicit configuration of customer-managed keys (CMK) for encryption is recommended for compliance and advanced security needs.","If higher assurance is needed, explicitly configure customer-managed keys for data at rest encryption on the Kusto cluster using the appropriate 'keyVaultProperties' property.",N/A,AI,Generic
MEDIUM,DP-2,ReadAdxExhaust.Template.json,15,The template does not configure or restrict the minimum TLS version for data in transit. Only secure versions (TLS 1.2 or higher) should be permitted for all data transfers.,"Explicitly set the minimum TLS version (preferably 1.2 or above) in the configuration for the ADX cluster, if supported. Review resource settings to verify secure protocol enforcement.",N/A,AI,Generic
MEDIUM,IM-5,RoleAssignment.Template.json,1,"The template does not configure logging/auditing for the role assignments being created, which is required to monitor identity and access activities.","Ensure that Azure Activity Logs and monitoring for role assignments are enabled at the subscription level where these role assignments are created. Although this is not configured directly in this template, it is necessary to enable monitoring and alerts for changes in privileged access.",N/A,AI,Generic
MEDIUM,NS-12,TrafficManagerEndpoints.Template.json,43,"No use of Azure Private Endpoints is specified for 'Microsoft.Network/trafficManagerProfiles/externalEndpoints', increasing exposure and missing ASB NS-5 requirements for private connectivity.","Where possible, leverage Private Endpoints to restrict traffic between Azure resources to the Microsoft backbone network.",N/A,AI,Generic
MEDIUM,NS-13,TrafficManagerEndpoints.Template.json,43,"Absence of Virtual Network Service Endpoints in this template exposes a risk that traffic between Azure services traverses the public internet, conflicting with ASB NS-6.",Enable Virtual Network Service Endpoints for all supported services to ensure traffic stays within the Azure backbone network.,N/A,AI,Generic
LOW,DP-2,LacpGlobal.Template.json,90,"Storage Account enforces TLS1_2 minimum, but CosmosDB's 'minimalTlsVersion' parameter is set based on a variable and not enforced in the template content; the template allows for misconfiguration.",Explicitly enforce 'minimalTlsVersion' as 'TLS1_2' or higher for all CosmosDB and Storage Accounts to ensure encryption in transit.,N/A,AI,Generic
LOW,NS-9,LacpGlobal.Template.json,0,"No logging or monitoring is enabled for Storage Accounts, Key Vault, or CosmosDB (e.g., diagnostic settings or activity/log analytics).","Enable diagnostic logs and metrics for all storage, CosmosDB, and Key Vault resources, and configure them to be sent to a Log Analytics workspace.",N/A,AI,Generic
LOW,DP-16,LacpGlobal.Template.json,38,"CosmosDB instance uses Continuous backup policy (Continuous30Days), which covers ongoing data recovery scenarios, but review other resources to ensure backup and recovery is consistently configured across all critical data stores.","Ensure backup and recovery strategies are implemented and retained for all critical data (e.g., snapshots for storage accounts, Key Vault recovery enabled).",N/A,AI,Generic
LOW,IM-8,LacpRegion.Template.json,229,"Role assignments often grant built-in Data Contributor or broad Contributor roles, though the permissions required are unclear. ASB IM-8 requires least-privilege assignment.",Review role assignments and restrict permissions to actions strictly necessary for each identity.,N/A,AI,Generic
LOW,IM-11,LacpStamp.Template.json,1,There is no mention of access reviews or use of Privileged Identity Management (PIM) in the RBAC or key vault access policy definitions.,Configure Azure AD Access Reviews for RBAC assignments and use Privileged Identity Management (PIM) for all privileged roles.,N/A,AI,Generic
LOW,NS-8,TrafficManagerEndpoints.Template.json,43,"There is no indication of DDoS Protection being enabled on publicly exposed endpoints, as required by ASB NS-8.",Consider enabling Azure DDoS Protection Standard on the relevant virtual networks.,N/A,AI,Generic
LOW,NS-9,TrafficManagerEndpoints.Template.json,43,"No configuration is observed for enabling network traffic monitoring or diagnostic settings for the created resources, as required by ASB NS-9.",Enable Azure Monitor diagnostics and Network Watcher flow logs for all networked resources to monitor traffic and detect anomalies.,N/A,AI,Generic
