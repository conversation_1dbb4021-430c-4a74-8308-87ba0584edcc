#!/usr/bin/env python3
"""
Concrete test for accurate line number detection.
This test creates files with known content and verifies exact line numbers programmatically.
"""

import os
import sys
from pathlib import Path
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from security_opt import SecurityPRReviewer

def create_test_file_with_known_issues():
    """Create a test file with security issues at specific, known line numbers."""
    
    # Create a file with exactly known content and line numbers
    bicep_content = """// Azure Bicep Template - Security Test File
// This file contains intentional security issues for testing
// Each issue is placed at a specific line number for verification

param storageAccountName string = 'testsecuritystorage'
param location string = resourceGroup().location
param environment string = 'development'

// Resource Group (lines 9-13)
resource rg 'Microsoft.Resources/resourceGroups@2021-04-01' = {
  name: 'rg-security-test'
  location: location
}

// Storage Account with multiple security issues
resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {
  name: storageAccountName
  location: location
  kind: 'StorageV2'
  sku: {
    name: 'Standard_LRS'
  }
  properties: {
    // SECURITY ISSUE #1: Public blob access (EXACTLY LINE 24)
    allowBlobPublicAccess: true
    
    // SECURITY ISSUE #2: Weak TLS version (EXACTLY LINE 27)
    minimumTlsVersion: 'TLS1_0'
    
    // SECURITY ISSUE #3: HTTP traffic allowed (EXACTLY LINE 30)
    supportsHttpsTrafficOnly: false
    
    // Good configuration
    encryption: {
      services: {
        blob: {
          enabled: true
        }
        file: {
          enabled: true
        }
      }
      keySource: 'Microsoft.Storage'
    }
    
    // SECURITY ISSUE #4: Default network access (EXACTLY LINE 45)
    networkAcls: {
      defaultAction: 'Allow'
    }
  }
  
  tags: {
    Environment: environment
    Purpose: 'Security Testing'
  }
}

// Network Security Group with issues
resource nsg 'Microsoft.Network/networkSecurityGroups@2021-02-01' = {
  name: 'nsg-security-test'
  location: location
  properties: {
    securityRules: [
      {
        name: 'AllowSSHFromInternet'
        properties: {
          priority: 1000
          access: 'Allow'
          direction: 'Inbound'
          protocol: 'Tcp'
          // SECURITY ISSUE #5: SSH from anywhere (EXACTLY LINE 68)
          sourceAddressPrefix: '0.0.0.0/0'
          sourcePortRange: '*'
          destinationAddressPrefix: '*'
          destinationPortRange: '22'
        }
      }
      {
        name: 'AllowRDPFromInternet'
        properties: {
          priority: 1001
          access: 'Allow'
          direction: 'Inbound'
          protocol: 'Tcp'
          // SECURITY ISSUE #6: RDP from anywhere (EXACTLY LINE 81)
          sourceAddressPrefix: '0.0.0.0/0'
          sourcePortRange: '*'
          destinationAddressPrefix: '*'
          destinationPortRange: '3389'
        }
      }
    ]
  }
}

// Key Vault with issues
resource keyVault 'Microsoft.KeyVault/vaults@2021-04-01-preview' = {
  name: 'kv-security-test'
  location: location
  properties: {
    sku: {
      family: 'A'
      name: 'standard'
    }
    tenantId: subscription().tenantId
    // SECURITY ISSUE #7: Soft delete disabled (EXACTLY LINE 100)
    enableSoftDelete: false
    
    // SECURITY ISSUE #8: Purge protection disabled (EXACTLY LINE 103)
    enablePurgeProtection: false
    
    accessPolicies: [
      {
        tenantId: subscription().tenantId
        objectId: 'some-object-id'
        // SECURITY ISSUE #9: Excessive permissions (EXACTLY LINE 110)
        permissions: {
          keys: ['all']
          secrets: ['all']
          certificates: ['all']
        }
      }
    ]
  }
}

// Output section
output storageAccountId string = storageAccount.id
output keyVaultId string = keyVault.id"""

    return bicep_content

def test_programmatic_line_detection():
    """Test programmatic line number detection with known content."""
    
    print("🧪 Testing Programmatic Line Number Detection...")
    
    # Create test file with known content
    test_file = "test_security_issues.bicep"
    content = create_test_file_with_known_issues()
    
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ Created test file: {test_file}")
    
    # Initialize reviewer
    reviewer = SecurityPRReviewer(local_folder="./")
    
    # Define expected security issues with CORRECTED line numbers based on actual content
    expected_issues = [
        {
            'description': 'Public blob access enabled',
            'search_patterns': ['allowBlobPublicAccess: true', 'allowblobpublicaccess:true'],
            'expected_line': 25,  # Corrected from 24 to 25
            'severity': 'HIGH'
        },
        {
            'description': 'Weak TLS version',
            'search_patterns': ['minimumTlsVersion: \'TLS1_0\'', 'TLS1_0', 'minimumtlsversion:tls1_0'],
            'expected_line': 28,  # Corrected from 27 to 28
            'severity': 'MEDIUM'
        },
        {
            'description': 'HTTP traffic allowed',
            'search_patterns': ['supportsHttpsTrafficOnly: false', 'supportshttpstrafficonly:false'],
            'expected_line': 31,  # Corrected from 30 to 31
            'severity': 'HIGH'
        },
        {
            'description': 'Default network access allow',
            'search_patterns': ['defaultAction: \'Allow\'', 'defaultAction: Allow', 'defaultaction:allow'],
            'expected_line': 48,  # Corrected from 46 to 48
            'severity': 'MEDIUM'
        },
        {
            'description': 'SSH from anywhere (first occurrence)',
            'search_patterns': ['sourceAddressPrefix: \'0.0.0.0/0\'', '0.0.0.0/0', 'sourceaddressprefix:0.0.0.0/0'],
            'expected_line': 72,  # Corrected from 68 to 72
            'severity': 'CRITICAL'
        },
        {
            'description': 'RDP from anywhere (second occurrence)',
            'search_patterns': ['sourceAddressPrefix: \'0.0.0.0/0\'', 'destinationPortRange: \'3389\''],  # Use RDP port to distinguish
            'expected_line': 86,  # Corrected from 81 to 86
            'severity': 'CRITICAL',
            'context_search': True  # Search for RDP-specific context
        },
        {
            'description': 'Soft delete disabled',
            'search_patterns': ['enableSoftDelete: false', 'enablesoftdelete:false'],
            'expected_line': 107,  # Corrected from 100 to 107
            'severity': 'HIGH'
        },
        {
            'description': 'Purge protection disabled',
            'search_patterns': ['enablePurgeProtection: false', 'enablepurgeprotection:false'],
            'expected_line': 110,  # Corrected from 103 to 110
            'severity': 'HIGH'
        },
        {
            'description': 'Excessive Key Vault permissions',
            'search_patterns': ['keys: [\'all\']', 'keys:[\'all\']', 'keys:all'],
            'expected_line': 118,  # Corrected from 111 to 118
            'severity': 'MEDIUM'
        }
    ]
    
    print(f"\n🔍 Testing {len(expected_issues)} security issues...")

    # First, let's debug by showing what's actually at the expected lines
    print(f"\n🔍 DEBUG: Showing actual content at expected lines...")
    with open(test_file, 'r', encoding='utf-8') as f:
        file_lines = f.readlines()

    for issue in expected_issues:
        expected_line = issue['expected_line']
        if 1 <= expected_line <= len(file_lines):
            actual_content = file_lines[expected_line - 1].strip()
            print(f"   Line {expected_line}: '{actual_content}' (Expected: {issue['description']})")
        else:
            print(f"   Line {expected_line}: OUT OF RANGE (file has {len(file_lines)} lines)")

    # Test each issue
    results = []
    for i, issue in enumerate(expected_issues, 1):
        print(f"\n--- Test {i}: {issue['description']} ---")

        # Handle special search cases
        if issue.get('context_search', False):
            # For RDP case, search for lines containing both source address and RDP port
            with open(test_file, 'r', encoding='utf-8') as f:
                file_lines = f.readlines()

            # Find lines with RDP port (3389) and then look for nearby 0.0.0.0/0
            rdp_lines = []
            for line_num, line_content in enumerate(file_lines, 1):
                if '3389' in line_content:
                    # Look for 0.0.0.0/0 in nearby lines (within 10 lines)
                    start_search = max(0, line_num - 10)
                    end_search = min(len(file_lines), line_num + 10)

                    for search_line_num in range(start_search, end_search):
                        search_content = file_lines[search_line_num]
                        if '0.0.0.0/0' in search_content:
                            rdp_lines.append({
                                'line_number': search_line_num + 1,
                                'matched_content': search_content.strip(),
                                'pattern_matched': '0.0.0.0/0 (RDP context)',
                                'match_strategy': 'context_search'
                            })
                            break

            if rdp_lines:
                line_result = {
                    'found': True,
                    'line_number': rdp_lines[0]['line_number'],
                    'matched_content': rdp_lines[0]['matched_content'],
                    'pattern_matched': rdp_lines[0]['pattern_matched'],
                    'match_strategy': rdp_lines[0]['match_strategy']
                }
                print(f"   Found RDP-related 0.0.0.0/0 using context search")
            else:
                line_result = {'found': False, 'error': 'No RDP context matches found'}

        elif issue.get('skip_first_match', False):
            # Find all occurrences and get the second one
            all_matches = reviewer._find_all_pattern_occurrences(test_file, issue['search_patterns'])
            if len(all_matches) >= 2:
                line_result = {
                    'found': True,
                    'line_number': all_matches[1]['line_number'],  # Second occurrence
                    'matched_content': all_matches[1]['matched_content'],
                    'pattern_matched': all_matches[1]['pattern_matched'],
                    'match_strategy': all_matches[1]['match_strategy']
                }
                print(f"   Found {len(all_matches)} occurrences, using second one")
            elif len(all_matches) == 1:
                line_result = {
                    'found': True,
                    'line_number': all_matches[0]['line_number'],
                    'matched_content': all_matches[0]['matched_content'],
                    'pattern_matched': all_matches[0]['pattern_matched'],
                    'match_strategy': all_matches[0]['match_strategy']
                }
                print(f"   Only found 1 occurrence (expected 2+)")
            else:
                line_result = {'found': False, 'error': 'No matches found'}
        else:
            # Use the standard single match finder
            line_result = reviewer._find_exact_line_number(test_file, issue['search_patterns'])

        if line_result['found']:
            found_line = line_result['line_number']
            expected_line = issue['expected_line']

            # Check accuracy
            if found_line == expected_line:
                print(f"✅ EXACT MATCH: Found at line {found_line} (expected {expected_line})")
                accuracy = "PERFECT"
            elif abs(found_line - expected_line) <= 2:
                print(f"🟡 CLOSE MATCH: Found at line {found_line} (expected {expected_line}, diff: {abs(found_line - expected_line)})")
                accuracy = "CLOSE"
            else:
                print(f"❌ INACCURATE: Found at line {found_line} (expected {expected_line}, diff: {abs(found_line - expected_line)})")
                accuracy = "WRONG"

            print(f"   Pattern matched: '{line_result['pattern_matched']}'")
            print(f"   Match strategy: {line_result.get('match_strategy', 'unknown')}")
            print(f"   Line content: '{line_result['matched_content']}'")

            results.append({
                'issue': issue['description'],
                'expected': expected_line,
                'found': found_line,
                'accuracy': accuracy,
                'content': line_result['matched_content']
            })
        else:
            print(f"❌ NOT FOUND: {line_result.get('error', 'Unknown error')}")
            if 'patterns_searched' in line_result:
                print(f"   Patterns searched: {line_result['patterns_searched']}")
            results.append({
                'issue': issue['description'],
                'expected': expected_line,
                'found': None,
                'accuracy': "NOT_FOUND",
                'content': None
            })
    
    # Test file content with context
    print(f"\n📄 Testing File Content with ±100 Line Context...")
    
    for result in results[:3]:  # Test first 3 issues
        if result['found']:
            context_result = reviewer._get_file_content_with_context(
                test_file, 
                result['found'], 
                context_lines=100
            )
            
            if context_result['success']:
                print(f"\n✅ Context for line {result['found']} ({result['issue']}):")
                print(f"   Total lines: {context_result['total_lines']}")
                print(f"   Showing lines: {context_result['start_line']}-{context_result['end_line']}")
                print(f"   Context size: ±{context_result['context_size']} lines")
                
                # Show a few lines around the target
                target_line = result['found']
                for line_info in context_result['content_lines']:
                    if abs(line_info['number'] - target_line) <= 2:
                        marker = ">>> " if line_info['highlighted'] else "    "
                        print(f"{marker}{line_info['number']:3d}: {line_info['content']}")
    
    # Generate summary
    print(f"\n📊 Accuracy Summary:")
    perfect_matches = sum(1 for r in results if r['accuracy'] == 'PERFECT')
    close_matches = sum(1 for r in results if r['accuracy'] == 'CLOSE')
    wrong_matches = sum(1 for r in results if r['accuracy'] == 'WRONG')
    not_found = sum(1 for r in results if r['accuracy'] == 'NOT_FOUND')
    
    print(f"   ✅ Perfect matches: {perfect_matches}/{len(results)} ({perfect_matches/len(results)*100:.1f}%)")
    print(f"   🟡 Close matches (±2 lines): {close_matches}/{len(results)} ({close_matches/len(results)*100:.1f}%)")
    print(f"   ❌ Wrong matches: {wrong_matches}/{len(results)} ({wrong_matches/len(results)*100:.1f}%)")
    print(f"   🔍 Not found: {not_found}/{len(results)} ({not_found/len(results)*100:.1f}%)")
    
    overall_accuracy = (perfect_matches + close_matches) / len(results) * 100
    print(f"   🎯 Overall accuracy: {overall_accuracy:.1f}%")
    
    # Generate HTML report with accurate line numbers
    print(f"\n📄 Generating HTML report with accurate line numbers...")
    
    # Create findings with the detected line numbers
    findings = []
    for result in results:
        if result['found']:
            findings.append({
                'control_id': f"TEST-{len(findings)+1:02d}",
                'severity': 'HIGH',
                'file_path': test_file,
                'line': result['found'],
                'description': result['issue'],
                'remediation': f"Fix the security issue on line {result['found']}",
                'domain': 'Security Testing'
            })
    
    # Generate report
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    html_path = f"accurate_line_numbers_report_{timestamp}.html"
    
    reviewer._export_findings_to_html(findings, html_path)
    
    print(f"✅ HTML report generated: {html_path}")
    print(f"📊 Report contains {len(findings)} findings with programmatically verified line numbers")
    
    # Clean up
    try:
        os.remove(test_file)
        print(f"🧹 Cleaned up test file: {test_file}")
    except Exception as e:
        print(f"⚠️ Could not remove test file: {e}")
    
    return html_path, overall_accuracy

if __name__ == "__main__":
    try:
        html_path, accuracy = test_programmatic_line_detection()
        
        print(f"\n🎉 Test completed!")
        print(f"📈 Line number accuracy: {accuracy:.1f}%")
        print(f"📄 Report: {html_path}")
        
        # Optional: Open in browser
        import webbrowser
        user_input = input(f"\n🚀 Open the accurate line numbers report in your browser? (y/n): ")
        if user_input.lower() in ['y', 'yes']:
            webbrowser.open(f"file://{os.path.abspath(html_path)}")
            print("🌐 Report opened in your browser!")
            print("\n🔍 In the report:")
            print("   1. Click 'View Code' buttons to see ±100 lines of context")
            print("   2. Verify that highlighted lines match the actual security issues")
            print("   3. Check that line numbers are exactly accurate")
            print("   4. Notice the context information showing line ranges")
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
