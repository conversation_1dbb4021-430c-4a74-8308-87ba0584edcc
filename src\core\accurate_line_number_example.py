#!/usr/bin/env python3
"""
Example implementation of accurate line number tracking for IaC Guardian.
This shows how to integrate precise line number detection into your security analysis.
"""

import os
import re
import json
from typing import Dict, List, Optional, Tuple
from pathlib import Path

class AccurateLineNumberTracker:
    """Handles accurate line number tracking for security findings."""
    
    def __init__(self):
        self.security_patterns = {
            'bicep': {
                'public_blob_access': {
                    'pattern': r'allowBlobPublicAccess:\s*true',
                    'severity': 'HIGH',
                    'description': 'Storage account allows public blob access'
                },
                'weak_tls': {
                    'pattern': r'minimumTlsVersion:\s*["\']TLS1_[01]["\']',
                    'severity': 'MEDIUM',
                    'description': 'Weak TLS version configured'
                },
                'no_https_only': {
                    'pattern': r'supportsHttpsTrafficOnly:\s*false',
                    'severity': 'HIGH',
                    'description': 'HTTPS-only traffic not enforced'
                }
            },
            'terraform': {
                'public_access': {
                    'pattern': r'allow_blob_public_access\s*=\s*true',
                    'severity': 'HIGH',
                    'description': 'Public blob access enabled'
                },
                'weak_encryption': {
                    'pattern': r'account_encryption_source\s*=\s*["\']Microsoft.Storage["\']',
                    'severity': 'MEDIUM',
                    'description': 'Using Microsoft-managed keys instead of customer-managed'
                }
            },
            'yaml': {
                'privileged_container': {
                    'pattern': r'privileged:\s*true',
                    'severity': 'CRITICAL',
                    'description': 'Container running in privileged mode'
                },
                'no_security_context': {
                    'pattern': r'securityContext:\s*{}',
                    'severity': 'MEDIUM',
                    'description': 'Empty security context'
                }
            }
        }
    
    def analyze_file_with_accurate_lines(self, file_path: str) -> List[Dict]:
        """Analyze file and return findings with accurate line numbers."""
        
        if not os.path.exists(file_path):
            return []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"Error reading file {file_path}: {e}")
            return []
        
        file_extension = Path(file_path).suffix.lower()
        file_type = self._get_file_type(file_extension)
        
        if file_type not in self.security_patterns:
            return []
        
        findings = []
        patterns = self.security_patterns[file_type]
        
        for issue_type, config in patterns.items():
            line_results = self._find_pattern_lines(content, config['pattern'])
            
            for result in line_results:
                # Validate the finding
                validation = self._validate_finding(file_path, result['line_number'], result['matched_content'])
                
                finding = {
                    'control_id': f"{file_type.upper()}-{issue_type.upper()}",
                    'severity': config['severity'],
                    'file_path': file_path,
                    'line': result['line_number'],
                    'description': config['description'],
                    'matched_content': result['matched_content'],
                    'context_before': result['context_before'],
                    'context_after': result['context_after'],
                    'validation': validation,
                    'confidence': self._calculate_confidence(validation),
                    'remediation': self._get_remediation(issue_type)
                }
                
                findings.append(finding)
        
        return findings
    
    def _get_file_type(self, extension: str) -> str:
        """Determine file type from extension."""
        type_mapping = {
            '.bicep': 'bicep',
            '.tf': 'terraform',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.json': 'json'
        }
        return type_mapping.get(extension, 'unknown')
    
    def _find_pattern_lines(self, content: str, pattern: str, context_lines: int = 3) -> List[Dict]:
        """Find all lines matching the pattern with context."""
        
        lines = content.split('\n')
        results = []
        
        try:
            regex = re.compile(pattern, re.IGNORECASE)
        except re.error:
            # Fallback to literal string search
            regex = None
        
        for i, line in enumerate(lines, 1):
            match_found = False
            
            if regex:
                match_found = regex.search(line) is not None
            else:
                match_found = pattern.lower() in line.lower()
            
            if match_found:
                start_idx = max(0, i - context_lines - 1)
                end_idx = min(len(lines), i + context_lines)
                
                results.append({
                    'line_number': i,
                    'matched_content': line.strip(),
                    'context_before': lines[start_idx:i-1],
                    'context_after': lines[i:end_idx]
                })
        
        return results
    
    def _validate_finding(self, file_path: str, line_number: int, expected_content: str) -> Dict:
        """Validate that the finding is accurate."""
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if line_number <= 0 or line_number > len(lines):
                return {
                    'valid': False,
                    'error': f"Line {line_number} out of range (1-{len(lines)})",
                    'confidence': 0.0
                }
            
            actual_line = lines[line_number - 1].strip()
            expected_clean = expected_content.strip()
            
            # Exact match
            if expected_clean == actual_line:
                return {
                    'valid': True,
                    'match_type': 'exact',
                    'confidence': 1.0,
                    'actual_content': actual_line
                }
            
            # Partial match
            if expected_clean in actual_line or actual_line in expected_clean:
                similarity = len(expected_clean) / max(len(actual_line), len(expected_clean))
                return {
                    'valid': True,
                    'match_type': 'partial',
                    'confidence': similarity,
                    'actual_content': actual_line
                }
            
            # Find closest match
            closest_line, closest_line_num = self._find_closest_line(lines, expected_content, line_number)
            
            return {
                'valid': False,
                'match_type': 'none',
                'confidence': 0.3,
                'actual_content': actual_line,
                'suggested_line': closest_line_num,
                'suggested_content': closest_line
            }
            
        except Exception as e:
            return {
                'valid': False,
                'error': f"Validation failed: {str(e)}",
                'confidence': 0.0
            }
    
    def _find_closest_line(self, lines: List[str], search_content: str, around_line: int, radius: int = 5) -> Tuple[str, int]:
        """Find the closest matching line within a radius."""
        
        search_clean = search_content.strip().lower()
        best_match = ""
        best_line_num = around_line
        best_score = 0.0
        
        start = max(1, around_line - radius)
        end = min(len(lines), around_line + radius)
        
        for i in range(start - 1, end):
            line_content = lines[i].strip().lower()
            
            # Simple similarity score
            if search_clean in line_content:
                score = len(search_clean) / len(line_content)
            elif line_content in search_clean:
                score = len(line_content) / len(search_clean)
            else:
                # Character overlap score
                overlap = sum(1 for a, b in zip(search_clean, line_content) if a == b)
                score = overlap / max(len(search_clean), len(line_content))
            
            if score > best_score:
                best_score = score
                best_match = lines[i].strip()
                best_line_num = i + 1
        
        return best_match, best_line_num
    
    def _calculate_confidence(self, validation: Dict) -> float:
        """Calculate confidence score for the finding."""
        
        if not validation.get('valid', False):
            return validation.get('confidence', 0.0)
        
        match_type = validation.get('match_type', 'none')
        base_confidence = validation.get('confidence', 0.0)
        
        # Adjust confidence based on match type
        if match_type == 'exact':
            return min(1.0, base_confidence * 1.0)
        elif match_type == 'partial':
            return min(0.9, base_confidence * 0.9)
        else:
            return min(0.5, base_confidence * 0.5)
    
    def _get_remediation(self, issue_type: str) -> str:
        """Get remediation advice for the issue type."""
        
        remediation_map = {
            'public_blob_access': 'Set allowBlobPublicAccess to false and use private endpoints or SAS tokens for access.',
            'weak_tls': 'Update minimumTlsVersion to TLS1_2 or higher for better security.',
            'no_https_only': 'Set supportsHttpsTrafficOnly to true to enforce HTTPS-only traffic.',
            'public_access': 'Disable public blob access and implement proper access controls.',
            'weak_encryption': 'Use customer-managed encryption keys (CMK) for better security control.',
            'privileged_container': 'Remove privileged: true and use specific capabilities instead.',
            'no_security_context': 'Define proper security context with runAsNonRoot and readOnlyRootFilesystem.'
        }
        
        return remediation_map.get(issue_type, 'Review and fix the security issue according to best practices.')
    
    def get_file_content_for_dialog(self, file_path: str, line_number: int, context: int = 10) -> Dict:
        """Get file content for code dialog display."""
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            start_line = max(1, line_number - context)
            end_line = min(len(lines), line_number + context)
            
            content_lines = []
            for i in range(start_line, end_line + 1):
                content_lines.append({
                    'number': i,
                    'content': lines[i-1].rstrip('\n'),
                    'highlighted': i == line_number
                })
            
            return {
                'success': True,
                'file_path': file_path,
                'total_lines': len(lines),
                'highlighted_line': line_number,
                'content_lines': content_lines
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'file_path': file_path
            }

def test_accurate_line_numbers():
    """Test the accurate line number tracking."""
    
    # Create test files
    test_files = {
        'test.bicep': '''param storageAccountName string
param location string = resourceGroup().location

resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {
  name: storageAccountName
  location: location
  kind: 'StorageV2'
  sku: {
    name: 'Standard_LRS'
  }
  properties: {
    allowBlobPublicAccess: true  // Line 12 - Security issue
    minimumTlsVersion: 'TLS1_0'  // Line 13 - Weak TLS
    supportsHttpsTrafficOnly: false  // Line 14 - No HTTPS enforcement
  }
}''',
        
        'test.tf': '''resource "azurerm_storage_account" "example" {
  name                     = "examplestorage"
  resource_group_name      = azurerm_resource_group.example.name
  location                 = azurerm_resource_group.example.location
  account_tier             = "Standard"
  account_replication_type = "LRS"
  
  allow_blob_public_access = true    # Line 8 - Security issue
  min_tls_version         = "TLS1_0" # Line 9 - Weak TLS
  https_traffic_only      = false    # Line 10 - No HTTPS
}'''
    }
    
    tracker = AccurateLineNumberTracker()
    
    for filename, content in test_files.items():
        # Write test file
        with open(filename, 'w') as f:
            f.write(content)
        
        # Analyze file
        findings = tracker.analyze_file_with_accurate_lines(filename)
        
        print(f"\n📄 Analysis Results for {filename}:")
        print(f"Found {len(findings)} security issues")
        
        for finding in findings:
            confidence = finding['confidence']
            confidence_emoji = "🟢" if confidence > 0.8 else "🟡" if confidence > 0.5 else "🔴"
            
            print(f"  {confidence_emoji} Line {finding['line']}: {finding['description']}")
            print(f"     Confidence: {confidence:.2f} | Severity: {finding['severity']}")
            print(f"     Content: {finding['matched_content']}")
        
        # Test code dialog content
        if findings:
            first_finding = findings[0]
            dialog_content = tracker.get_file_content_for_dialog(
                filename, 
                first_finding['line']
            )
            
            if dialog_content['success']:
                print(f"\n📋 Code Dialog Content (±10 lines around line {first_finding['line']}):")
                for line_info in dialog_content['content_lines']:
                    marker = ">>> " if line_info['highlighted'] else "    "
                    print(f"{marker}{line_info['number']:3d}: {line_info['content']}")
        
        # Clean up test file
        os.remove(filename)
    
    print("\n✅ Line number accuracy testing completed!")

if __name__ == "__main__":
    test_accurate_line_numbers()
