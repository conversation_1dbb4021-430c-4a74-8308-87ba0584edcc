#!/usr/bin/env python3
"""
Setup script for IaC Guardian MCP Server
Installs dependencies and configures the MCP server for VS Code integration.
"""

import json
import os
import subprocess
import sys
from pathlib import Path

def install_dependencies():
    """Install required dependencies for the MCP server."""
    print("📦 Installing MCP server dependencies...")

    try:
        # First try minimal requirements (just MCP)
        print("Installing minimal MCP requirements...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements-mcp-minimal.txt"
        ])
        print("✅ MCP dependencies installed successfully")

        # Test if we can import the security_opt module
        try:
            import security_opt
            print("✅ Existing IaC Guardian dependencies found")
        except ImportError:
            print("⚠️ Some IaC Guardian dependencies missing, installing full requirements...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", "requirements-mcp.txt"
            ])
            print("✅ Full dependencies installed successfully")

        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        print("💡 Try installing manually: pip install mcp")
        return False

def create_vscode_settings():
    """Create VS Code settings for MCP integration."""
    print("⚙️ Creating VS Code settings for MCP integration...")
    
    # VS Code settings directory
    vscode_dir = Path(".vscode")
    vscode_dir.mkdir(exist_ok=True)
    
    settings_file = vscode_dir / "settings.json"
    
    # Current working directory
    cwd = Path.cwd().as_posix()
    
    # MCP configuration for VS Code
    mcp_settings = {
        "github.copilot.chat.experimental.mcp.enabled": True,
        "github.copilot.chat.experimental.mcp.servers": {
            "iac-guardian": {
                "command": "python",
                "args": ["mcp_server.py"],
                "cwd": cwd,
                "env": {
                    "ENFORCE_DOMAIN_PRIORITY": "true",
                    "USE_OPTIMIZED_PROMPTS": "true",
                    "ANALYSIS_SEED": "42",
                    "PYTHONPATH": cwd
                }
            }
        }
    }
    
    # Read existing settings if they exist
    existing_settings = {}
    if settings_file.exists():
        try:
            with open(settings_file, 'r') as f:
                existing_settings = json.load(f)
        except json.JSONDecodeError:
            print("⚠️ Warning: Existing settings.json is invalid, creating new one")
    
    # Merge settings
    existing_settings.update(mcp_settings)
    
    # Write updated settings
    with open(settings_file, 'w') as f:
        json.dump(existing_settings, f, indent=2)
    
    print(f"✅ VS Code settings created: {settings_file}")
    return True

def create_launch_script():
    """Create a launch script for testing the MCP server."""
    print("🚀 Creating launch script...")
    
    launch_script = """#!/usr/bin/env python3
\"\"\"
Launch script for IaC Guardian MCP Server
Use this to test the MCP server locally.
\"\"\"

import asyncio
import sys
from mcp_server import main

if __name__ == "__main__":
    print("🚀 Starting IaC Guardian MCP Server...")
    print("📝 Use Ctrl+C to stop the server")
    print("🔗 Server will be available for VS Code Copilot integration")
    print("-" * 60)
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")
        sys.exit(1)
"""
    
    with open("launch_mcp.py", "w", encoding='utf-8') as f:
        f.write(launch_script)
    
    # Make executable on Unix systems
    if os.name != 'nt':
        os.chmod("launch_mcp.py", 0o755)
    
    print("✅ Launch script created: launch_mcp.py")
    return True

def create_test_script():
    """Create a test script for the MCP server."""
    print("🧪 Creating test script...")
    
    test_script = '''#!/usr/bin/env python3
"""
Test script for IaC Guardian MCP Server
Tests the MCP server functionality locally.
"""

import asyncio
import json
import tempfile
from pathlib import Path
from mcp_server import handle_call_tool, security_reviewer
from security_opt import SecurityPRReviewer

async def test_analyze_file():
    """Test file analysis functionality."""
    print("🧪 Testing file analysis...")
    
    # Create a test ARM template with security issues
    test_template = {
        "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
        "contentVersion": "*******",
        "resources": [
            {
                "type": "Microsoft.Storage/storageAccounts",
                "apiVersion": "2021-04-01",
                "name": "teststorage",
                "location": "[resourceGroup().location]",
                "properties": {
                    "supportsHttpsTrafficOnly": False,  # Security issue
                    "minimumTlsVersion": "TLS1_0"       # Security issue
                }
            }
        ]
    }
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(test_template, f, indent=2)
        temp_file = f.name
    
    try:
        # Initialize global security reviewer
        global security_reviewer
        security_reviewer = SecurityPRReviewer(local_folder=".")
        
        # Test the analyze_iac_file tool
        result = await handle_call_tool("analyze_iac_file", {
            "file_path": temp_file,
            "format": "markdown"
        })
        
        print("✅ File analysis test completed")
        print("📄 Result preview:")
        print(result[0].text[:200] + "..." if len(result[0].text) > 200 else result[0].text)
        
    finally:
        Path(temp_file).unlink(missing_ok=True)

async def test_get_controls():
    """Test security controls retrieval."""
    print("🧪 Testing security controls retrieval...")
    
    global security_reviewer
    if security_reviewer is None:
        security_reviewer = SecurityPRReviewer(local_folder=".")
    
    result = await handle_call_tool("get_security_controls", {
        "resource_type": "Storage",
        "domain": "Data Protection"
    })
    
    print("✅ Security controls test completed")
    print("📄 Result preview:")
    print(result[0].text[:200] + "..." if len(result[0].text) > 200 else result[0].text)

async def main():
    """Run all tests."""
    print("🚀 Starting IaC Guardian MCP Server Tests")
    print("=" * 60)
    
    try:
        await test_analyze_file()
        print()
        await test_get_controls()
        print()
        print("🎉 All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
'''
    
    with open("test_mcp.py", "w", encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ Test script created: test_mcp.py")
    return True

def main():
    """Main setup function."""
    print("🔧 Setting up IaC Guardian MCP Server")
    print("=" * 50)
    
    success = True
    
    # Install dependencies
    if not install_dependencies():
        success = False
    
    print()
    
    # Create VS Code settings
    if not create_vscode_settings():
        success = False
    
    print()
    
    # Create launch script
    if not create_launch_script():
        success = False
    
    print()
    
    # Create test script
    if not create_test_script():
        success = False
    
    print()
    print("=" * 50)
    
    if success:
        print("🎉 Setup completed successfully!")
        print()
        print("📋 Next steps:")
        print("1. Restart VS Code to load the new MCP server configuration")
        print("2. Test the server: python test_mcp.py")
        print("3. Use the tools in VS Code Copilot Chat:")
        print("   - @iac-guardian analyze_iac_file")
        print("   - @iac-guardian analyze_iac_folder")
        print("   - @iac-guardian get_security_controls")
        print("   - @iac-guardian validate_security_config")
        print()
        print("🔗 The MCP server will provide security analysis tools directly in VS Code!")
    else:
        print("❌ Setup failed. Please check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
