File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
network_demo.tf,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,24.0,"The 'source_address_prefix' property in the 'azurerm_network_security_group.demo' resource is set to '0.0.0.0/0' on line 24, allowing inbound SSH (port 22) from any IP address. This exposes the resource to the entire internet, enabling attackers to attempt brute-force attacks, gain initial access, and potentially move laterally within the network. The blast radius includes any resource protected by this NSG, increasing the risk of compromise.","Restrict the 'source_address_prefix' to a specific trusted IP range or management subnet (e.g., your corporate IP or a jumpbox). Change line 24 to a more restrictive value, such as 'source_address_prefix = ""***********/24""'. Additionally, consider implementing just-in-time (JIT) access for management ports and ensure deny-by-default rules are in place.",,,,ai_analysis,,Validated
network_demo.tf,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,43.0,"The 'allow_blob_public_access' property in the 'azurerm_storage_account.demo' resource is set to 'true' on line 43, enabling public anonymous access to blobs. This allows any unauthenticated user on the internet to read data stored in the storage account, creating a direct data exfiltration vector and significantly increasing the blast radius of a potential breach.","Set 'allow_blob_public_access' to 'false' on line 43 to disable anonymous public access. Additionally, review all storage account access policies and implement private endpoints or service endpoints to restrict access to trusted networks only.",,,,ai_analysis,,Validated
network_demo.tf,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,46.0,"The 'https_traffic_only' property in the 'azurerm_storage_account.demo' resource is set to 'false' on line 46, allowing unencrypted HTTP connections. This exposes sensitive data in transit to interception and man-in-the-middle attacks, enabling attackers to read or modify data as it traverses the network.",Set 'https_traffic_only' to 'true' on line 46 to enforce encryption for all data in transit. Ensure all clients and applications accessing the storage account use HTTPS endpoints exclusively.,,,,ai_analysis,,Validated
storage_demo.bicep,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,17.0,"The 'allowBlobPublicAccess: true' configuration on the storage account enables public anonymous access to blob data. This exposes all blobs in the account to unauthenticated users, allowing attackers to enumerate, read, and exfiltrate sensitive data without any authentication. The blast radius includes all data stored in public containers, potentially leading to data breaches and regulatory violations.","Set 'allowBlobPublicAccess' to false on line 17 to disable anonymous public access. Review all containers to ensure none are configured for public access. Implement Azure Purview or Azure Information Protection to classify and label sensitive data, and regularly audit access policies as per DP-1.",,,,ai_analysis,,Validated
storage_demo.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,20.0,"The 'minimumTlsVersion: 'TLS1_0'' configuration allows clients to connect using outdated and insecure TLS 1.0. Attackers can exploit known vulnerabilities in TLS 1.0 to intercept or modify data in transit, enabling man-in-the-middle attacks and credential theft. The blast radius includes all data transmitted to and from the storage account.",Set 'minimumTlsVersion' to 'TLS1_2' or higher on line 20 to enforce strong encryption for data in transit. Review all client applications to ensure compatibility with TLS 1.2+. This aligns with DP-3 requirements for encrypting sensitive data in transit.,,,,ai_analysis,,Validated
storage_demo.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,23.0,"The 'supportsHttpsTrafficOnly: false' configuration allows unencrypted HTTP connections to the storage account. Attackers can intercept or tamper with data sent over HTTP, leading to credential theft, data exfiltration, and session hijacking. The blast radius includes all data and operations performed over insecure channels.",Set 'supportsHttpsTrafficOnly' to true on line 23 to enforce HTTPS-only access. Ensure all clients use HTTPS endpoints. This is required to protect data in transit as per DP-3.,,,,ai_analysis,,Validated
storage_demo.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,40.0,"The 'networkAcls.defaultAction: 'Allow'' configuration permits all network traffic, including from the public internet, to access the storage account unless explicitly denied. This exposes the storage account to unauthorized access, brute-force attacks, and data exfiltration. The blast radius is the entire storage account and all its data.","Set 'networkAcls.defaultAction' to 'Deny' on line 40 to block all network access by default. Explicitly allow only trusted networks or private endpoints. Implement Private Link for the storage account to restrict access to private networks, as required by NS-2.",,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 7,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 0,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T11:54:34.001743,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
