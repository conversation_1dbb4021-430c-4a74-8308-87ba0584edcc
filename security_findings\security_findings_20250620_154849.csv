File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
app-config.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyValues' flows across template boundary,Secure parameter 'keyValues' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'app_insights_key' flows across template boundary,Secure parameter 'app_insights_key' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyvault_name' flows across template boundary,Secure parameter 'keyvault_name' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
keyvault.bicep,NS-1,Network Security,Establish network segmentation boundaries,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,Review template dependencies and ensure secure communication between templates,,,,cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'secrets' flows across template boundary,Secure parameter 'secrets' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
storage-accounts.bicep,NS-1,Network Security,Establish network segmentation boundaries,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,Review template dependencies and ensure secure communication between templates,,,,cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,DP-6,Data Protection,Use a secure key management process,HIGH,1.0,Parameter flow security risk: Sensitive parameters may be exposed through template dependencies,Use Key Vault references for sensitive parameters and validate parameter flow security,,,,cross_reference_analysis,parameter_flow,Validated
function-settings.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,47.0,"The 'funcStorage' resource (Microsoft.Storage/storageAccounts) on line 46 is declared as 'existing' without explicit enforcement of 'secureTransferRequired' or minimum TLS version. Without secure transfer, attackers can intercept or modify data in transit between clients and the storage account, enabling credential theft or data exfiltration. The blast radius includes all data stored in this storage account and any services depending on it.","Explicitly enforce 'secureTransferRequired: true' and set 'minimumTlsVersion' to 'TLS1_2' or higher in the storage account configuration. If the resource is managed outside this template, ensure these settings are enforced in the source template or via Azure Policy.",,,,ai_analysis,,Validated
function-settings.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,47.0,"The 'funcStorage' resource (Microsoft.Storage/storageAccounts) on line 46 is referenced as 'existing' with no evidence of private endpoint usage or public network access restriction. If public network access is enabled, attackers can directly target the storage account from the internet, increasing the risk of brute-force, enumeration, or data exfiltration attacks. The blast radius includes all data and services relying on this storage account.","Restrict public network access by setting 'publicNetworkAccess' to 'Disabled' and deploy a private endpoint for the storage account. If the resource is managed externally, ensure these controls are enforced in the source template or via Azure Policy.",,,,ai_analysis,,Validated
function-settings.bicep,IM-3,Identity Management,Manage application identities securely and automatically,MEDIUM,50.0,"The 'function' resource (Microsoft.Web/sites) on line 50 is declared as 'existing' with no evidence of managed identity configuration. Without a managed identity, applications may use hardcoded credentials or less secure authentication methods, increasing the risk of credential exposure and privilege escalation. Attackers could exploit weak identity management to move laterally or escalate privileges within the environment.","Enable a system-assigned or user-assigned managed identity for the App Service (Function App) and update application code to use Azure AD authentication for resource access. If the resource is managed outside this template, ensure managed identity is enabled in the source template.",,,,ai_analysis,,Validated
function.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,89.0,"The 'clientAffinityEnabled: true' property in the App Service resource (Line 89) enables client affinity (ARR Affinity), which can increase the attack surface by allowing session stickiness based on client IP. This can be abused for session hijacking, lateral movement, or targeting specific users, especially if the application is exposed to the internet. The blast radius includes potential compromise of user sessions and increased predictability for attackers.",Set 'clientAffinityEnabled' to false in the App Service configuration to disable session stickiness unless absolutely required. Review application requirements and use stateless authentication mechanisms where possible. Example: clientAffinityEnabled: false. Reference: ASB NS-1 (Establish network segmentation boundaries).,,,,ai_analysis,,Validated
function.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,91.0,"The 'vnetContentShareEnabled: true' property in the App Service resource (Line 91) enables mounting Azure File shares from the virtual network, which can increase the attack surface for lateral movement and data exfiltration if the subnet is not properly secured. Attackers gaining access to the function app could leverage this to access sensitive file shares within the VNet, increasing the blast radius to all accessible storage resources.","Restrict 'vnetContentShareEnabled' to only required scenarios and ensure the subnet is protected with NSGs and private endpoints. Regularly audit file share permissions and network access. If not required, set vnetContentShareEnabled: false. Reference: ASB NS-2 (Secure cloud services with network controls).",,,,ai_analysis,,Validated
function.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,92.0,"The 'vnetImagePullEnabled: true' property in the App Service resource (Line 92) allows the function app to pull container images from private registries within the VNet. If the VNet or registry is not properly secured, this can be exploited for lateral movement or to pull malicious images, increasing the risk of compromise across the network.","Ensure that the VNet and container registry are secured with NSGs, private endpoints, and strict access controls. Only enable 'vnetImagePullEnabled' if necessary and monitor image sources. Reference: ASB NS-2 (Secure cloud services with network controls).",,,,ai_analysis,,Validated
hub-network.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,14.0,"The subnet 'hub-subnet' defined at line 14 does not specify a networkSecurityGroup property, meaning it is not associated with a Network Security Group (NSG). Without an NSG, there is no deny-by-default network boundary, enabling potential lateral movement and initial access attack vectors. Attackers who gain access to this subnet could move laterally to other resources, increasing the blast radius of a compromise.",Associate a Network Security Group (NSG) with the 'hub-subnet' by adding the 'networkSecurityGroup' property to the subnet definition. Define restrictive inbound and outbound rules to enforce a deny-by-default posture and only allow required traffic. Example: 'networkSecurityGroup: { id: <resourceId of NSG> }'.,,,,ai_analysis,,Validated
instance-config.bicep,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,46.0,"The variable 'monitoringGCSAuthId' on line 37 is assigned the value 'airseccosinetest.geneva.keyvault.airaspcerts.cloudapp.net', which appears to be a Key Vault DNS name or identifier. Storing Key Vault identifiers or references in plain variables without explicit classification or labeling as sensitive data can enable attackers to enumerate and target key vaults for credential theft or privilege escalation. If this variable is not properly classified and protected, it increases the risk of sensitive data discovery and exfiltration, expanding the blast radius to all resources protected by the referenced Key Vault.","Classify and label the 'monitoringGCSAuthId' variable as sensitive data using Azure Purview or Azure Information Protection. Ensure all Key Vault references are inventoried and protected according to data classification policies. Apply sensitivity labels and restrict access to this variable to only those identities and services that require it, following the principle of least privilege.",,,,ai_analysis,,Validated
instance-config.bicep,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,67.0,"The output 'appConfigFields' on line 67 exposes the value of 'dynamicConfig.Admins' as a plain string in deployment outputs. If 'dynamicConfig.Admins' contains sensitive information such as administrator usernames or email addresses, this output can be accessed by anyone with read access to the deployment, enabling attackers to enumerate privileged accounts and target them for phishing, brute force, or privilege escalation attacks. The blast radius includes potential compromise of all admin accounts listed, increasing the risk of initial access and lateral movement.","Remove sensitive data such as administrator lists from deployment outputs. If output is required, mask or redact sensitive values before exposing them. Implement Azure Purview or Azure Information Protection to classify and label sensitive data, and ensure outputs do not disclose privileged account information. Reference: ASB DP-1.",,,,ai_analysis,,Validated
keyvault.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,28.0,"The 'networkAcls.defaultAction' property is set to 'Allow' for the Key Vault resource. This configuration allows public network access to the Key Vault, enabling attackers to attempt unauthorized access over the network. If an attacker can discover the Key Vault endpoint, they may attempt brute-force, credential stuffing, or exploit misconfigurations to gain access, increasing the risk of data exfiltration and lateral movement. The blast radius includes all secrets and keys stored in the Key Vault, potentially compromising sensitive data and downstream resources.","Set 'networkAcls.defaultAction' to 'Deny' to restrict public network access. Only allow access from explicitly defined IP addresses or virtual networks using 'ipRules' and 'virtualNetworkRules'. Example: 'defaultAction: ""Deny""'.",,,,ai_analysis,,Validated
keyvault.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,29.0,"The 'networkAcls.bypass' property is set to 'AzureServices', which allows all Azure services to bypass network ACLs and access the Key Vault. This increases the attack surface, as any Azure service (potentially in other subscriptions or tenants) could access the Key Vault if misconfigured, enabling lateral movement and unauthorized data access. The blast radius includes all secrets and keys in the Key Vault, risking data exposure and privilege escalation.","Set 'networkAcls.bypass' to 'None' to prevent Azure services from bypassing network ACLs. Only allow access from trusted services by explicitly configuring access policies or managed identities. Example: 'bypass: ""None""'.",,,,ai_analysis,,Validated
keyvault.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,20.0,"The 'enabledForDeployment' property is set to 'true', which allows Azure Resource Manager (ARM) deployments to retrieve secrets from the Key Vault. If the Key Vault is accessible from public networks (as configured), attackers who gain access to the deployment context or exploit misconfigured permissions could retrieve sensitive secrets, increasing the risk of data exfiltration and privilege escalation.",Set 'enabledForDeployment' to 'false' unless absolutely required. Restrict this capability to only trusted deployment pipelines and ensure network ACLs are set to 'Deny' with explicit allow rules for trusted sources.,,,,ai_analysis,,Validated
keyvault.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,21.0,"The 'enabledForTemplateDeployment' property is set to 'true', which allows Azure Resource Manager (ARM) template deployments to access secrets in the Key Vault. Combined with permissive network ACLs, this increases the risk of unauthorized access to secrets during deployments, enabling attackers to exfiltrate sensitive data or escalate privileges if they compromise deployment pipelines.",Set 'enabledForTemplateDeployment' to 'false' unless required for specific deployment scenarios. Ensure network ACLs are set to 'Deny' and only allow trusted deployment sources.,,,,ai_analysis,,Validated
operational-insights.bicep,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,173.0,"The output 'appInsightsInstrumentationKey' on line 173 exposes the Application Insights Instrumentation Key (insightsComponents.properties.InstrumentationKey) as a plain output. This key is a sensitive credential that can be used by attackers to send unauthorized telemetry or exfiltrate data, enabling initial access and increasing the blast radius if leaked. Attackers with this key can impersonate legitimate telemetry sources, evade detection, and potentially disrupt monitoring integrity.","Remove the output of 'appInsightsInstrumentationKey' from the template or replace it with a secure reference mechanism such as Azure Key Vault. Ensure that sensitive keys are never exposed in outputs and are only accessible to authorized identities. Implement data classification and labeling to prevent accidental disclosure of sensitive data in outputs, as per Azure Security Benchmark DP-1.",,,,ai_analysis,,Validated
scaleset-networks.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,66.0,"The property 'defaultOutboundAccess: true' is set on the subnet configuration. This enables default outbound internet access for all resources in the subnet, creating an initial access vector for attackers and significantly increasing the blast radius for lateral movement and data exfiltration. Without explicit egress controls, any compromised resource can communicate with the internet, bypassing network segmentation and exposing the environment to command-and-control, data theft, and malware delivery.","Set 'defaultOutboundAccess' to false or remove it entirely from the subnet configuration. Instead, explicitly control outbound access using Network Security Groups (NSGs) and Azure Firewall to enforce a deny-by-default policy and only allow required egress traffic. Associate the subnet with an NSG to restrict outbound and inbound traffic according to least privilege principles, as required by ASB NS-1.",,,,ai_analysis,,Validated
server-farms.bicep,IM-3,Identity Management,Manage application identities securely and automatically,MEDIUM,111.0,"The 'Microsoft.Web/serverfarms' resource defined on line 111 does not specify a managed identity. Without a managed identity, applications or services running in this App Service Plan may require credentials to be stored in code or configuration, increasing the risk of credential exposure and enabling attackers to escalate privileges or move laterally if they compromise the environment.",Add an 'identity' block to the 'Microsoft.Web/serverfarms' resource to enable a system-assigned managed identity. This allows secure access to Azure resources (such as Key Vault) without storing credentials in code or configuration. Example: 'identity: { type: 'SystemAssigned' }'.,,,,ai_analysis,,Validated
server-farms.bicep,DP-1,Data Protection,Discover classify and label sensitive data,MEDIUM,168.0,"Line 168 sets 'settingValue' for 'CERTIFICATE_PASSWORD_GENEVACERT' to an empty string. This may indicate that a certificate password is not being set or managed, which could result in untracked sensitive data or improper handling of secrets. Attackers could exploit weak or missing secret management to access sensitive data or escalate privileges.","Ensure that all sensitive data, such as certificate passwords, are properly classified, inventoried, and managed using Azure Purview or Azure Information Protection. Do not leave secret values empty; instead, reference secrets from Azure Key Vault and apply sensitivity labels as appropriate.",,,,ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,29.0,"The 'defaultAction' property in 'networkAcls' for the storage accounts (e.g., storageAccountFunc) is set to 'Allow'. This configuration allows public network access to the storage account, enabling attackers to access storage resources from any network not explicitly denied. This significantly increases the attack surface for initial access, data exfiltration, and lateral movement, as any IP not in the allowlist can reach the storage account unless otherwise restricted.","Set 'networkAcls.defaultAction' to 'Deny' on all storage account resources to block public network access by default. Only explicitly allow trusted IPs or virtual networks via 'ipRules' and 'virtualNetworkRules'. For further defense-in-depth, deploy private endpoints for storage accounts and disable public network access entirely.",,,,ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,29.0,"The 'defaultAction' property in 'networkAcls' for the fuzz storage account (via fuzzStorageProperties) is set to 'Allow'. This exposes the storage account to public network access, enabling attackers to connect from any network not explicitly denied. This configuration increases the risk of unauthorized access, data exfiltration, and lateral movement across the environment.","Change 'networkAcls.defaultAction' to 'Deny' in 'fuzzStorageProperties' to restrict access to only explicitly allowed IPs and virtual networks. Additionally, implement private endpoints and disable public network access for all storage accounts to minimize exposure.",,,,ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,29.0,"The 'networkAcls' property in 'fuzzStorageProperties' is used for additional storage accounts (storageAccountsCorpus), and its 'defaultAction' is set to 'Allow'. This means all storage accounts created in this loop are publicly accessible unless specifically restricted, enabling broad attack vectors for initial access and data exfiltration.","Update 'fuzzStorageProperties.networkAcls.defaultAction' to 'Deny' to ensure all storage accounts created with this property are not accessible from public networks. Only allow access from trusted IPs or VNets, and deploy private endpoints to further restrict access.",,,,ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,38.0,"The CORS configuration for the 'blobServicesFuzz' resource allows all headers ('allowedHeaders: [""*""]') and all exposed headers ('exposedHeaders: [""*""]'). Combined with public network access, this enables attackers from any allowed origin to send arbitrary headers and receive all response headers, increasing the risk of data exfiltration and cross-origin attacks.",Restrict 'allowedHeaders' and 'exposedHeaders' in the CORS rules to only those necessary for legitimate application functionality. Avoid using wildcards ('*') and ensure that public network access is disabled to prevent abuse of CORS from untrusted origins.,,,,ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,38.0,"The CORS configuration for the 'blobServicesCorpus' resource allows all headers ('allowedHeaders: [""*""]') and all exposed headers ('exposedHeaders: [""*""]'). When combined with public network access, this enables attackers from any allowed origin to send arbitrary headers and access all response headers, facilitating data exfiltration and cross-origin attacks.",Limit 'allowedHeaders' and 'exposedHeaders' in the CORS rules to only those required for application functionality. Remove the wildcard ('*') usage and ensure public network access is disabled to prevent exploitation of CORS by untrusted origins.,,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 29,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 7,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T15:48:49.681241,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
