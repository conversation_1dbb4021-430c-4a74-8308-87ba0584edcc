File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
Grafana.deploymentTemplate.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,38.0,"The 'publicNetworkAccess' property is set to 'Enabled' for the Microsoft.Dashboard/grafana resource. This exposes the Grafana instance to the public internet, enabling initial access and remote exploitation vectors (e.g., brute force, zero-day, credential stuffing). Attackers can directly target the management interface or APIs, increasing the blast radius to include potential data exfiltration, lateral movement, and privilege escalation within the Azure environment.","Set 'publicNetworkAccess' to 'Disabled' to restrict access to private endpoints only. Implement Azure Private Link and ensure only trusted VNets/subnets can access the Grafana instance. Review and update network security groups (NSGs) to enforce least privilege network access. Example: ""publicNetworkAccess"": ""Disabled"".

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
Grafana.deploymentTemplate.json,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,38.0,"The Grafana instance is deployed with 'publicNetworkAccess' set to 'Enabled', which means it is accessible from any network, bypassing network segmentation and deny-by-default principles. This configuration allows attackers to scan, enumerate, and attack the service from the public internet, increasing the risk of unauthorized access and lateral movement within the cloud environment.","Disable public network access by setting 'publicNetworkAccess' to 'Disabled'. Place the resource in a segmented VNet and apply strict NSG rules to limit inbound and outbound traffic. Ensure only required subnets and IPs have access to the Grafana instance.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
Grafana.deploymentTemplate.json,NS-3,Network Security,Deploy firewall at the edge of enterprise network,CRITICAL,38.0,"With 'publicNetworkAccess' set to 'Enabled', there is no evidence of Azure Firewall or equivalent network filtering protecting the Grafana instance. This lack of advanced filtering allows unrestricted traffic from the internet, exposing the application to exploitation and increasing the risk of data compromise and service disruption.","Deploy Azure Firewall or a similar network security appliance in front of the Grafana instance. Route all inbound and outbound traffic through the firewall and configure rules to allow only necessary traffic from trusted sources. Combine with disabling public network access for defense-in-depth.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal) | [Virtual network traffic routing](https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview) | [Azure Firewall Manager](https://docs.microsoft.com/azure/firewall-manager/overview) | [Hub-spoke topology](https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Use Azure Firewall for stateful application layer traffic restriction and central management. Create user-defined routes (UDR) for complex topologies. Block known bad IPs and high-risk protocols.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal) | [Virtual network traffic routing](https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview) | [Azure Firewall Manager](https://docs.microsoft.com/azure/firewall-manager/overview) | [Hub-spoke topology](https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Use Azure Firewall for stateful application layer traffic restriction and central management. Create user-defined routes (UDR) for complex topologies. Block known bad IPs and high-risk protocols.,"Enhanced Implementation Context:
• Azure Firewall deployment: https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal
• Virtual network traffic routing: https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview
• Azure Firewall Manager: https://docs.microsoft.com/azure/firewall-manager/overview
• Hub-spoke topology: https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 4.4, 4.8, 13.10
• NIST SP800-53 r4: AC-4, SC-7, CM-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Management ports should be closed on your virtual machines
• Management ports of virtual machines should be protected with just-in-time network access control
• IP Forwarding on your virtual machine should be disabled
• All Internet traffic should be routed via your deployed Azure Firewall",ai_analysis,,Validated
Grafana.deploymentTemplate.json,NS-7,Network Security,Simplify network security configuration,HIGH,38.0,"The template does not implement centralized network security management or adaptive network hardening for the publicly accessible Grafana instance. This omission increases the risk of misconfiguration and delayed response to emerging threats, allowing attackers to exploit open network paths.","Integrate Azure Firewall Manager and enable Adaptive Network Hardening for all internet-facing resources. Regularly review and apply recommended security rules to minimize attack surface and automate response to new threats.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/security-center/security-center-adaptive-network-hardening) | [Azure Firewall Manager](https://docs.microsoft.com/azure/firewall-manager/overview) | [ARM template for Firewall policy](https://docs.microsoft.com/azure/firewall-manager/quick-firewall-policy) | [Network security automation](https://docs.microsoft.com/azure/automation/automation-network-configuration) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Use Microsoft Defender for Cloud Adaptive Network Hardening for NSG recommendations. Use Azure Firewall Manager for centralized policy and route management. Implement ARM templates for consistent depl...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/security-center/security-center-adaptive-network-hardening) | [Azure Firewall Manager](https://docs.microsoft.com/azure/firewall-manager/overview) | [ARM template for Firewall policy](https://docs.microsoft.com/azure/firewall-manager/quick-firewall-policy) | [Network security automation](https://docs.microsoft.com/azure/automation/automation-network-configuration) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Use Microsoft Defender for Cloud Adaptive Network Hardening for NSG recommendations. Use Azure Firewall Manager for centralized policy and route management. Implement ARM templates for consistent deployment.,"Enhanced Implementation Context:
• Adaptive Network Hardening: https://docs.microsoft.com/azure/security-center/security-center-adaptive-network-hardening
• Azure Firewall Manager: https://docs.microsoft.com/azure/firewall-manager/overview
• ARM template for Firewall policy: https://docs.microsoft.com/azure/firewall-manager/quick-firewall-policy
• Network security automation: https://docs.microsoft.com/azure/automation/automation-network-configuration
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 4.4, 4.8
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• Use Azure Firewall Manager for centralized policy management
• Implement consistent network security configurations",ai_analysis,,Validated
Grafana.deploymentTemplate.json,NS-8,Network Security,Detect and disable insecure services and protocols,HIGH,38.0,"There is no explicit enforcement of secure protocols (e.g., TLS 1.2+) for the Grafana instance, and public network access is enabled. Attackers could exploit insecure protocols to intercept or manipulate data in transit, leading to credential theft or session hijacking.","Explicitly configure the Grafana instance to require the latest TLS version (1.2 or higher) for all connections. Disable support for legacy and insecure protocols. Monitor protocol usage and enforce secure transfer policies.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Secure protocol configuration](https://docs.microsoft.com/azure/security/fundamentals/network-best-practices) | [Network security monitoring](https://docs.microsoft.com/azure/security/fundamentals/network-monitoring) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Use Azure Sentinel's Insecure Protocol Workbook to discover insecure services like SSL/TLSv1 SSHv1 SMBv1 LM/NTLMv1. Disable insecure protocols or use compensating controls like NSG/Firewall blocking.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Secure protocol configuration](https://docs.microsoft.com/azure/security/fundamentals/network-best-practices) | [Network security monitoring](https://docs.microsoft.com/azure/security/fundamentals/network-monitoring) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Use Azure Sentinel's Insecure Protocol Workbook to discover insecure services like SSL/TLSv1 SSHv1 SMBv1 LM/NTLMv1. Disable insecure protocols or use compensating controls like NSG/Firewall blocking.,"Enhanced Implementation Context:
• Azure Sentinel insecure protocols workbook: https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks
• TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem
• Secure protocol configuration: https://docs.microsoft.com/azure/security/fundamentals/network-best-practices
• Network security monitoring: https://docs.microsoft.com/azure/security/fundamentals/network-monitoring
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 4.4, 4.8
• NIST SP800-53 r4: CM-2, CM-6, CM-7
• PCI-DSS v3.2.1: 4.1, A2.1, A2.2, A2.3

Azure Policy Examples:
• Latest TLS version should be used in your API App
• Latest TLS version should be used in your Web App
• Latest TLS version should be used in your Function App
• Secure transfer to storage accounts should be enabled",ai_analysis,,Validated
KustoScripts.template.json,DP-1,Data Protection,Discover classify and label sensitive data,HIGH,53.0,"The 'scriptContent' property at line 53 references a variable ('variables('$fxv#0')') that may contain sensitive data or business logic, but there is no evidence of data classification, labeling, or inventory. Without data discovery and classification, attackers who gain access to the Kusto database or scripts could exfiltrate sensitive data or manipulate business logic, increasing the blast radius of a compromise.","Implement Azure Purview or Azure Information Protection to scan, classify, and label all data assets referenced in Kusto scripts. Ensure all sensitive data is inventoried and classified according to organizational policy. Reference ASB control DP-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources)

🔵 Azure Guidance: Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other loca...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources),Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other locations.,"Enhanced Implementation Context:
• Data classification overview: https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification
• Azure Purview sensitivity labels: https://docs.microsoft.com/azure/purview/create-sensitivity-label
• Azure Information Protection: https://docs.microsoft.com/azure/information-protection/what-is-information-protection
• Azure SQL Data Discovery: https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification
• Azure Purview data sources: https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources

Compliance Mappings:
• CIS Controls v8: 3.2, 3.7, 3.13
• NIST SP800-53 r4: RA-2, SC-28
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Sensitive data in your SQL databases should be classified
• Implement data discovery and classification across all data stores
• Deploy Azure Purview for enterprise data governance
• Configure sensitivity labels for all data assets",ai_analysis,,Validated
KustoScripts.template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,HIGH,55.0,"The 'continueOnErrors' property is set to true at line 55, which may allow scripts to proceed even if errors occur. This can mask unauthorized data access or exfiltration attempts, as failures in data protection logic may go undetected, enabling attackers to bypass controls and increase the blast radius.","Set 'continueOnErrors' to false for all production scripts to ensure that any error, especially those related to data access or protection, halts execution and triggers alerts. Implement monitoring for anomalous script execution and error patterns using Azure Defender for Storage or SQL. Reference ASB control DP-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
KustoScripts.template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,105.0,"""continueOnErrors"": true is set for Kusto script deployment. This setting allows the deployment to proceed even if the script fails, which can mask unauthorized or anomalous data access or exfiltration attempts. Attackers could exploit this by introducing malicious or incomplete scripts that bypass detection, increasing the risk of data exposure or loss of monitoring integrity. The blast radius includes undetected data exfiltration or tampering in the Kusto database.","Set ""continueOnErrors"" to false for all Kusto script deployments to ensure that any script failure halts the deployment and triggers alerting. Implement monitoring and alerting for failed script executions to detect and respond to anomalous activities. Reference: ASB DP-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
KustoScripts.template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,176.0,"The 'continueOnErrors' property is set to true in the Kusto script deployment. This configuration allows the deployment to proceed even if errors occur during script execution. Attackers could exploit this by introducing malicious or incomplete scripts that fail silently, bypassing detection and monitoring controls. This increases the risk of unauthorized data access, data exfiltration, or the deployment of insecure or incomplete data processing logic, expanding the potential blast radius to all data processed by the script.","Set 'continueOnErrors' to false to ensure that any error during script execution halts the deployment and triggers alerting. Implement monitoring and alerting for failed script executions. Review all scripts for security and data protection compliance before deployment. Reference: ASB Control DP-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
KustoScripts.template.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,291.0,"The configuration property 'continueOnErrors' is set to true on line 291. This setting allows the deployment to proceed even if errors occur, which can result in incomplete or misconfigured resources. Attackers may exploit this by causing partial deployments, leading to inconsistent security controls, missing encryption, or unprotected endpoints. The blast radius includes potential exposure of sensitive data, unmonitored resources, and weakened defense-in-depth posture.","Set 'continueOnErrors' to false to ensure that any deployment error halts the process, allowing for immediate investigation and remediation. This enforces strict deployment integrity and ensures all security controls (including encryption in transit) are applied consistently. Review all deployment scripts to ensure error handling is enforced as per Azure Security Benchmark DP-3.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account)

🔵 Azure Guidance: Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windo...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account),Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windows remote management.,"Enhanced Implementation Context:
• Double encryption for Azure data in transit: https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit
• Encryption in transit overview: https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit
• TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem
• Azure Storage secure transfer: https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account

Compliance Mappings:
• CIS Controls v8: 3.10
• NIST SP800-53 r4: SC-8
• PCI-DSS v3.2.1: 3.5, 3.6, 4.1

Azure Policy Examples:
• Kubernetes clusters should be accessible only over HTTPS
• Only secure connections to your Azure Cache for Redis should be enabled
• FTPS only should be required in your Function App
• Secure transfer to storage accounts should be enabled
• Function App should only be accessible over HTTPS
• Latest TLS version should be used in your API App
• Web Application should only be accessible over HTTPS
• Enforce SSL connection should be enabled for PostgreSQL database servers
• Latest TLS version should be used in your Web App",ai_analysis,,Validated
KustoScripts.template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,319.0,"""continueOnErrors"": true disables strict error handling for Logic Apps script execution. If errors occur during data processing or script execution, the workflow will continue, potentially allowing incomplete, corrupted, or unauthorized data to be processed or exposed. Attackers could exploit this to bypass security checks, escalate privileges, or exfiltrate sensitive data without detection, increasing the blast radius of a compromise.","Set ""continueOnErrors"" to false to enforce strict error handling. This ensures that any failure in the script or data processing halts the workflow, preventing unauthorized or incomplete data operations. Additionally, implement monitoring and alerting for failed executions to detect and respond to anomalous activities. Reference: Azure Security Benchmark DP-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
roleAssignment.deploymentTemplate.json,IM-2,Identity Management,Protect identity and authentication systems,CRITICAL,11.0,"The 'builtInRoleType' parameter is set to 'Owner' by default, and line 26 assigns the 'Owner' roleDefinitionId (8e3af657-a8ff-443c-a75c-2fe8c4bcb635) to the specified principal. Assigning the Owner role grants full administrative privileges over the subscription, enabling initial access, privilege escalation, lateral movement, and potential full compromise of all Azure resources if the principal is compromised. The blast radius includes all resources within the subscription, making this a high-value target for attackers.","Restrict the use of the 'Owner' role. Assign the least privileged built-in role necessary for the principal's function (e.g., 'Contributor' or custom roles with limited permissions). Implement Privileged Identity Management (PIM) to require just-in-time elevation and enforce multi-factor authentication (MFA) for all privileged roles. Regularly review and audit role assignments. Reference: Azure Security Benchmark v3.0 IM-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score) | [Active Directory security best practices](https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory) | [Azure AD security baseline](https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline) | [Privileged Identity Management](https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Use Azure AD security baseline and Identity Secure Score to evaluate security posture. Implement limited administrative roles user risk policies MFA for administrative roles and block legacy authentic...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score) | [Active Directory security best practices](https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory) | [Azure AD security baseline](https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline) | [Privileged Identity Management](https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),Use Azure AD security baseline and Identity Secure Score to evaluate security posture. Implement limited administrative roles user risk policies MFA for administrative roles and block legacy authentication.,"Enhanced Implementation Context:
• Azure AD Identity Secure Score: https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score
• Active Directory security best practices: https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory
• Azure AD security baseline: https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline
• Privileged Identity Management: https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 5.4, 6.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8, SI-4
• PCI-DSS v3.2.1: 8.2, 8.3

Azure Policy Examples:
• No applicable built-in policy (requires configuration-based implementation)
• Use Azure AD Identity Secure Score recommendations
• Implement Azure AD security baseline configurations
• Monitor privileged account activities through Azure AD logs",ai_analysis,,Validated
roleAssignment.deploymentTemplate.json,IM-6,Identity Management,Use strong authentication controls,CRITICAL,5.0,"The 'principalId' property is assigned the Owner role without any evidence of enforced strong authentication (such as MFA) or conditional access policies. This enables an attack vector where compromise of the principal's credentials leads to immediate full administrative access, increasing the risk of privilege escalation and total environment compromise.","Enforce strong authentication controls for all accounts assigned privileged roles, including mandatory multi-factor authentication (MFA) and passwordless authentication where possible. Apply conditional access policies to restrict access based on trusted locations and device compliance. Reference: Azure Security Benchmark v3.0 IM-6.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/authentication/howto-mfa-getstarted) | [Passwordless authentication options](https://docs.microsoft.com/azure/active-directory/authentication/concept-authentication-passwordless) | [Azure AD password policies](https://docs.microsoft.com/azure/active-directory/authentication/concept-sspr-policy#password-policies-that-only-apply-to-cloud-user-accounts) | [Azure AD Password Protection](https://docs.microsoft.com/azure/active-directory/authentication/concept-password-ban-bad) | [Block legacy authentication](https://docs.microsoft.com/azure/active-directory/conditional-access/block-legacy-authentication)

🔵 Azure Guidance: Use Azure AD passwordless authentication as default method (Windows Hello Microsoft Authenticator FIDO2 Keys). Enable Azure MFA for all users with conditional access policies. Block legacy authenticat...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/authentication/howto-mfa-getstarted) | [Passwordless authentication options](https://docs.microsoft.com/azure/active-directory/authentication/concept-authentication-passwordless) | [Azure AD password policies](https://docs.microsoft.com/azure/active-directory/authentication/concept-sspr-policy#password-policies-that-only-apply-to-cloud-user-accounts) | [Azure AD Password Protection](https://docs.microsoft.com/azure/active-directory/authentication/concept-password-ban-bad) | [Block legacy authentication](https://docs.microsoft.com/azure/active-directory/conditional-access/block-legacy-authentication),Use Azure AD passwordless authentication as default method (Windows Hello Microsoft Authenticator FIDO2 Keys). Enable Azure MFA for all users with conditional access policies. Block legacy authentication.,"Enhanced Implementation Context:
• Azure MFA deployment: https://docs.microsoft.com/azure/active-directory/authentication/howto-mfa-getstarted
• Passwordless authentication options: https://docs.microsoft.com/azure/active-directory/authentication/concept-authentication-passwordless
• Azure AD password policies: https://docs.microsoft.com/azure/active-directory/authentication/concept-sspr-policy#password-policies-that-only-apply-to-cloud-user-accounts
• Azure AD Password Protection: https://docs.microsoft.com/azure/active-directory/authentication/concept-password-ban-bad
• Block legacy authentication: https://docs.microsoft.com/azure/active-directory/conditional-access/block-legacy-authentication

Compliance Mappings:
• CIS Controls v8: 6.3, 6.4
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-5, IA-8
• PCI-DSS v3.2.1: 7.2, 8.2, 8.3, 8.4

Azure Policy Examples:
• Authentication to Linux machines should require SSH keys
• MFA should be enabled on accounts with write permissions on your subscription
• MFA should be enabled on accounts with owner permissions on your subscription
• MFA should be enabled on accounts with read permissions on your subscription",ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 13,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 0,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-24T16:31:05.567100,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
