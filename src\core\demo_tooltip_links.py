#!/usr/bin/env python3
"""
Demonstration script for tooltip links functionality.
Creates sample reports with enhanced CSV and HTML exports including tooltip links.
"""

import sys
import json
from pathlib import Path
import tempfile

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from security_opt import SecurityPRReviewer
    print("✅ Successfully imported SecurityPRReviewer")
except ImportError as e:
    print(f"❌ Failed to import SecurityPRReviewer: {e}")
    sys.exit(1)

def create_sample_bicep_file():
    """Create a sample Bicep file for analysis."""
    bicep_content = """
// Sample Azure Storage Account with security issues for demonstration
param storageAccountName string = 'demostorage${uniqueString(resourceGroup().id)}'
param location string = resourceGroup().location

resource storageAccount 'Microsoft.Storage/storageAccounts@2023-01-01' = {
  name: storageAccountName
  location: location
  sku: {
    name: 'Standard_LRS'
  }
  kind: 'StorageV2'
  properties: {
    // Security Issue: Public network access enabled
    publicNetworkAccess: 'Enabled'
    
    // Security Issue: HTTPS not enforced
    supportsHttpsTrafficOnly: false
    
    // Security Issue: Minimum TLS version not set
    minimumTlsVersion: 'TLS1_0'
    
    // Security Issue: No encryption configuration
    encryption: {
      services: {
        blob: {
          enabled: false
        }
        file: {
          enabled: false
        }
      }
    }
    
    // Security Issue: Network rules allow all
    networkAcls: {
      defaultAction: 'Allow'
      bypass: 'AzureServices'
    }
  }
}

// Sample Key Vault with security issues
resource keyVault 'Microsoft.KeyVault/vaults@2023-07-01' = {
  name: 'demo-keyvault-${uniqueString(resourceGroup().id)}'
  location: location
  properties: {
    sku: {
      family: 'A'
      name: 'standard'
    }
    tenantId: subscription().tenantId
    
    // Security Issue: Public network access enabled
    publicNetworkAccess: 'Enabled'
    
    // Security Issue: Soft delete not enabled
    enableSoftDelete: false
    
    // Security Issue: Purge protection not enabled
    enablePurgeProtection: false
    
    // Security Issue: RBAC not enabled
    enableRbacAuthorization: false
    
    accessPolicies: []
  }
}

output storageAccountId string = storageAccount.id
output keyVaultId string = keyVault.id
"""
    return bicep_content

def demo_tooltip_links():
    """Demonstrate tooltip links functionality with sample analysis."""
    print("\n🎯 TOOLTIP LINKS DEMONSTRATION")
    print("=" * 60)
    
    try:
        # Create temporary directory for demo
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create sample Bicep file
            bicep_file = temp_path / "demo-storage.bicep"
            bicep_file.write_text(create_sample_bicep_file())
            
            print(f"📁 Created sample Bicep file: {bicep_file.name}")
            
            # Initialize reviewer
            reviewer = SecurityPRReviewer(local_folder=str(temp_path))
            
            # Analyze the sample file
            print("🔍 Analyzing sample file...")
            files = reviewer.analyze_folder(str(temp_path))
            
            if not files:
                print("❌ No files found for analysis")
                return False
            
            print(f"📋 Found {len(files)} file(s) to analyze")
            
            # Create sample findings (simulating AI analysis results)
            sample_findings = [
                {
                    "file_path": str(bicep_file),
                    "control_id": "NS-2",
                    "severity": "HIGH",
                    "line": 12,
                    "description": "Public network access is enabled for storage account, exposing it to internet threats",
                    "remediation": "Disable public network access and configure private endpoints for secure connectivity",
                    "source": "ai_analysis"
                },
                {
                    "file_path": str(bicep_file),
                    "control_id": "DP-2",
                    "severity": "CRITICAL",
                    "line": 16,
                    "description": "HTTPS traffic is not enforced, allowing insecure data transmission",
                    "remediation": "Enable supportsHttpsTrafficOnly to enforce secure HTTPS connections",
                    "source": "ai_analysis"
                },
                {
                    "file_path": str(bicep_file),
                    "control_id": "DP-3",
                    "severity": "HIGH",
                    "line": 19,
                    "description": "Minimum TLS version is set to insecure TLS 1.0",
                    "remediation": "Set minimumTlsVersion to 'TLS1_2' or higher for secure encryption",
                    "source": "ai_analysis"
                },
                {
                    "file_path": str(bicep_file),
                    "control_id": "DP-6",
                    "severity": "CRITICAL",
                    "line": 23,
                    "description": "Storage encryption is disabled for blob and file services",
                    "remediation": "Enable encryption for all storage services and use customer-managed keys",
                    "source": "ai_analysis"
                },
                {
                    "file_path": str(bicep_file),
                    "control_id": "IM-1",
                    "severity": "MEDIUM",
                    "line": 50,
                    "description": "Key Vault RBAC authorization is not enabled",
                    "remediation": "Enable RBAC authorization for fine-grained access control",
                    "source": "ai_analysis"
                }
            ]
            
            print(f"🔍 Generated {len(sample_findings)} sample security findings")
            
            # Create output directory
            output_dir = Path("demo_reports")
            output_dir.mkdir(exist_ok=True)
            
            # Export enhanced reports with tooltip links
            print("\n📊 Exporting enhanced reports with tooltip links...")
            
            # Export CSV with tooltip links
            print("📄 Generating enhanced CSV report...")
            reviewer.export_findings(sample_findings, format="csv", output_dir=str(output_dir))
            
            # Export HTML with tooltip links
            print("🌐 Generating enhanced HTML report...")
            reviewer.export_findings(sample_findings, format="html", output_dir=str(output_dir))
            
            # List generated files
            csv_files = list(output_dir.glob("*.csv"))
            html_files = list(output_dir.glob("*.html"))
            
            print(f"\n✅ Generated reports in {output_dir}:")
            
            if csv_files:
                csv_file = csv_files[0]
                print(f"📄 CSV Report: {csv_file}")
                print(f"   📊 Features: Reference links, Azure guidance, implementation context")
                print(f"   🔗 Link format: [Description](URL) for easy access")
                
                # Show sample CSV content
                try:
                    with open(csv_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()[:5]  # First 5 lines
                    print(f"   📋 Sample content (first 5 lines):")
                    for i, line in enumerate(lines, 1):
                        print(f"      {i}: {line.strip()[:80]}...")
                except Exception as e:
                    print(f"   ⚠️ Could not read CSV content: {e}")
            
            if html_files:
                html_file = html_files[0]
                print(f"🌐 HTML Report: {html_file}")
                print(f"   🎯 Features: Interactive tooltips, validation metadata, Glass UI styling")
                print(f"   📚 Tooltip icons: Click on 📚 icons next to control IDs for links")
                print(f"   🔍 Validation stats: Success rates and correction statistics")
                
                # Check HTML file size
                try:
                    file_size = html_file.stat().st_size
                    print(f"   📏 File size: {file_size:,} bytes ({file_size/1024:.1f} KB)")
                except Exception as e:
                    print(f"   ⚠️ Could not get file size: {e}")
            
            # Demonstrate link extraction for specific controls
            print(f"\n🔗 Link extraction examples:")
            
            for control_id in ["NS-2", "DP-2", "DP-6"]:
                links_info = reviewer._extract_control_links(control_id)
                print(f"   {control_id}:")
                print(f"     📚 Links found: {len(links_info.get('raw_links', []))}")
                print(f"     🔵 Azure guidance: {'Yes' if links_info.get('azure_guidance') else 'No'}")
                print(f"     📖 Implementation context: {'Yes' if links_info.get('implementation_context') else 'No'}")
                
                if links_info.get('formatted_links'):
                    print(f"     🔗 Formatted: {links_info['formatted_links'][:100]}...")
            
            print(f"\n🎉 Tooltip links demonstration completed successfully!")
            print(f"📁 Check the {output_dir} directory for generated reports")
            
            return True
            
    except Exception as e:
        print(f"❌ Error during demonstration: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the tooltip links demonstration."""
    print("🎯 TOOLTIP LINKS FUNCTIONALITY DEMONSTRATION")
    print("=" * 70)
    
    success = demo_tooltip_links()
    
    if success:
        print("\n✅ Demonstration completed successfully!")
        print("\n📋 What was demonstrated:")
        print("   • Enhanced CSV export with reference links")
        print("   • Enhanced HTML export with interactive tooltips")
        print("   • Link extraction from Azure Security Benchmark")
        print("   • Automatic description generation for URLs")
        print("   • Validation metadata integration")
        print("   • Glass UI styling for tooltips")
        
        print("\n💡 How to use:")
        print("   • CSV: Open in Excel/Sheets, check 'Reference Links' column")
        print("   • HTML: Open in browser, click 📚 icons for tooltips")
        print("   • Links: Direct access to Azure documentation")
        print("   • Guidance: Microsoft's official recommendations")
        
        return True
    else:
        print("\n❌ Demonstration failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
