File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
C:\Users\<USER>\AppData\Local\Temp\tmppftu_d75\demo-storage.bicep,NS-2,Network Security,Protect public endpoints,HIGH,12.0,"Public network access is enabled for storage account, exposing it to internet threats",Disable public network access and configure private endpoints for secure connectivity,,,,ai_analysis,,Validated
C:\Users\<USER>\AppData\Local\Temp\tmppftu_d75\demo-storage.bicep,DP-2,Data Protection,Enable encryption in transit,CRITICAL,16.0,"HTTPS traffic is not enforced, allowing insecure data transmission",Enable supportsHttpsTrafficOnly to enforce secure HTTPS connections,,,,ai_analysis,,Validated
C:\Users\<USER>\AppData\Local\Temp\tmppftu_d75\demo-storage.bicep,DP-3,Data Protection,Manage sensitive information disclosure,HIGH,19.0,Minimum TLS version is set to insecure TLS 1.0,Set minimumTlsVersion to 'TLS1_2' or higher for secure encryption,,,,ai_analysis,,Validated
C:\Users\<USER>\AppData\Local\Temp\tmppftu_d75\demo-storage.bicep,DP-6,Data Protection,Secure Data with Customer-Managed Keys (CMK),CRITICAL,23.0,Storage encryption is disabled for blob and file services,Enable encryption for all storage services and use customer-managed keys,,,,ai_analysis,,Validated
C:\Users\<USER>\AppData\Local\Temp\tmppftu_d75\demo-storage.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,MEDIUM,50.0,Key Vault RBAC authorization is not enabled,Enable RBAC authorization for fine-grained access control,,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 5,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 0,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-17T13:54:23.138878,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
