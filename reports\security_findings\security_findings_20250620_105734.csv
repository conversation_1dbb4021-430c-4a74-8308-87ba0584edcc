File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
simple_test.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,25.0,"The 'supportsHttpsTrafficOnly' property for the Microsoft.Storage/storageAccounts resource is set to false. This allows unencrypted HTTP connections, enabling attackers to intercept or modify data in transit via man-in-the-middle attacks. The blast radius includes all data transferred to and from the storage account, potentially exposing sensitive information and authentication tokens.","Set 'supportsHttpsTrafficOnly' to true in the storage account properties to enforce encrypted HTTPS connections for all data in transit. Example: ""supportsHttpsTrafficOnly"": true.",,,,ai_analysis,,Validated
simple_test.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,27.0,"The 'minimumTlsVersion' property for the Microsoft.Storage/storageAccounts resource is set to 'TLS1_0'. TLS 1.0 is deprecated and vulnerable to multiple cryptographic attacks, allowing attackers to decrypt or tamper with data in transit. This increases the risk of data exfiltration and session hijacking across all clients connecting to the storage account.","Set 'minimumTlsVersion' to 'TLS1_2' or higher in the storage account properties to enforce strong encryption for data in transit. Example: ""minimumTlsVersion"": ""TLS1_2"".",,,,ai_analysis,,Validated
simple_test.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,29.0,"The 'networkAcls.defaultAction' property for the Microsoft.Storage/storageAccounts resource is set to 'Allow', which permits public network access from any source. This exposes the storage account to the internet, enabling attackers to attempt unauthorized access, brute force, or data exfiltration. The blast radius includes all data stored in the account and any connected resources.","Set 'networkAcls.defaultAction' to 'Deny' and configure explicit network rules or private endpoints to restrict access to trusted networks only. Example: ""defaultAction"": ""Deny"".",,,,ai_analysis,,Validated
simple_test.json,DP-4,Data Protection,Enable data at rest encryption by default,MEDIUM,26.0,"The 'allowBlobPublicAccess' property for the Microsoft.Storage/storageAccounts resource is set to true, allowing anonymous public access to blob data. This can lead to unintentional data exposure and increases the risk of data exfiltration if sensitive blobs are made public.","Set 'allowBlobPublicAccess' to false to prevent anonymous public access to blob data. Example: ""allowBlobPublicAccess"": false.",,,,ai_analysis,,Validated
simple_test.json,NS-8,Network Security,Detect and disable insecure services and protocols,HIGH,27.0,"The 'minimumTlsVersion' property for the Microsoft.Storage/storageAccounts resource is set to 'TLS1_0', which is an insecure protocol. Attackers can exploit known weaknesses in TLS 1.0 to decrypt or manipulate network traffic, undermining the security of data in transit.","Set 'minimumTlsVersion' to 'TLS1_2' or higher to disable insecure protocols and enforce strong encryption. Example: ""minimumTlsVersion"": ""TLS1_2"".",,,,ai_analysis,,Validated
simple_test.json,DP-8,Data Protection,Ensure security of key and certificate repository,CRITICAL,29.0,"The 'networkAcls.defaultAction' property for the Microsoft.KeyVault/vaults resource is set to 'Allow', permitting public network access to the Key Vault. This exposes cryptographic keys and secrets to the internet, enabling attackers to attempt unauthorized access, brute force, or enumeration attacks. The blast radius includes all secrets and keys managed by this Key Vault and any dependent resources.","Set 'networkAcls.defaultAction' to 'Deny' and configure private endpoints or explicit network rules to restrict access to trusted networks only. Example: ""defaultAction"": ""Deny"".",,,,ai_analysis,,Validated
simple_test.json,DP-8,Data Protection,Ensure security of key and certificate repository,CRITICAL,50.0,"The 'enableSoftDelete' property for the Microsoft.KeyVault/vaults resource is set to false, disabling soft delete. Without soft delete, deleted keys and secrets are immediately and permanently removed, allowing attackers or insiders to destroy cryptographic material without recovery, increasing the risk of permanent data loss and denial of service.","Set 'enableSoftDelete' to true to enable soft delete and allow recovery of deleted keys and secrets. Example: ""enableSoftDelete"": true.",,,,ai_analysis,,Validated
simple_test.json,DP-8,Data Protection,Ensure security of key and certificate repository,CRITICAL,51.0,"The 'enablePurgeProtection' property for the Microsoft.KeyVault/vaults resource is set to false, disabling purge protection. This allows immediate and irreversible deletion of keys and secrets, enabling attackers or malicious insiders to permanently destroy cryptographic material, increasing the risk of unrecoverable data loss.","Set 'enablePurgeProtection' to true to prevent permanent deletion of keys and secrets until the retention period expires. Example: ""enablePurgeProtection"": true.",,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 8,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 0,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T10:57:34.087090,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
