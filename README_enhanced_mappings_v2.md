# Enhanced Resource Control Mappings V2

## Overview

The Enhanced Resource Control Mappings V2 system extends the original mapping capabilities with advanced features for comprehensive security analysis and reporting. This version maintains the core principle of applying ALL controls to EVERY resource type while adding sophisticated analysis capabilities.

## Key Features

### 1. **Priority and Severity Mapping**
- Automatic priority determination based on control keywords
- Severity levels (CRITICAL, HIGH, MEDIUM, LOW) based on domain and impact
- Impact scoring system (0-100) for quantitative analysis

### 2. **Control Relationships**
- Identifies related controls based on shared tags and principles
- Helps understand control dependencies and overlaps
- Enables more efficient implementation planning

### 3. **Compliance Framework Mapping**
- Maps controls to major compliance frameworks:
  - PCI-DSS (Payment Card Industry)
  - HIPAA (Healthcare)
  - SOC2 (Service Organization Control)
  - ISO27001 (Information Security)
  - GDPR (Data Protection)
  - NIST (Cybersecurity Framework)

### 4. **Risk Assessment**
- Resource categories classified by risk level (HIGH, MEDIUM, LOW)
- Common vulnerabilities tracked for each resource type
- Focus areas defined for targeted security efforts

### 5. **Enhanced URL Extraction**
- Support for Microsoft short links (aka.ms)
- Better URL description extraction
- Formatted links for reports with meaningful descriptions

### 6. **Security Posture Reporting**
- Comprehensive security analysis for resource collections
- Priority action identification
- Compliance coverage tracking
- Risk summary generation

### 7. **Extended Resource Categories**
Added support for 20+ new Azure resource categories including:
- Data & Analytics: DataFactory, DataLake, Synapse, Purview
- AI/ML: MachineLearning, CognitiveServices
- IoT: IoTHub, StreamAnalytics
- Security: Defender, AzureArc
- Monitoring: LogAnalytics, ApplicationInsights
- And many more...

## Usage

### Basic Usage

```python
from src.core.enhanced_resource_control_mappings_v2 import EnhancedResourceControlMapperV2

# Initialize the mapper
mapper = EnhancedResourceControlMapperV2()

# Get controls for a specific resource type
resource_type = "Microsoft.Storage/storageAccounts"
controls = mapper.get_controls_for_resource_type(resource_type)

# Get control details
for control in controls[:5]:
    control_id = control['id']
    severity = mapper.get_control_severity(control_id)
    frameworks = mapper.get_compliance_frameworks(control_id)
    links = mapper.get_control_links(control_id)
    
    print(f"Control: {control_id}")
    print(f"  Priority: {control.get('priority')}")
    print(f"  Severity: {severity['severity']}")
    print(f"  Compliance: {', '.join(frameworks)}")
    print(f"  URLs: {len(links.get('raw_links', []))}")
```

### Advanced Features

#### 1. Security Posture Analysis

```python
# Analyze security posture for multiple resources
resource_types = [
    "Microsoft.Storage/storageAccounts",
    "Microsoft.KeyVault/vaults",
    "Microsoft.Sql/servers"
]

posture_report = mapper.generate_security_posture_report(resource_types)

# Access report data
print(f"Total Controls: {posture_report['total_controls']}")
print(f"Risk Summary: {posture_report['risk_summary']}")
print(f"Priority Actions: {posture_report['priority_actions']}")
```

#### 2. Control Relationships

```python
# Find related controls
control_id = "IM-1"
related_controls = mapper.get_related_controls(control_id)

for relation in related_controls:
    print(f"Related: {relation['control_id']}")
    print(f"Type: {relation['relationship_type']}")
    print(f"Common: {relation['common_elements']}")
```

#### 3. Risk Assessment

```python
# Get risk information for a resource
category = mapper._determine_resource_category("Microsoft.KeyVault/vaults")
if category:
    mapping = mapper.resource_mappings[category]
    print(f"Risk Level: {mapping['risk_level']}")
    print(f"Vulnerabilities: {mapping['common_vulnerabilities']}")
    print(f"Focus Areas: {mapping['focus_areas']}")
```

## Data Export

The system can export all mappings and analysis data:

```python
# Export enhanced mappings
mapper.export_enhanced_mappings("enhanced_mappings_v2.json")
```

Exported data includes:
- Complete control database with priorities and characteristics
- Resource mappings with risk assessments
- URL links database
- Control relationships
- Severity mappings
- Compliance framework mappings

## Testing

Run the comprehensive test suite:

```bash
python test_enhanced_mappings.py
```

The test suite covers:
1. Control priority and severity mapping
2. Control relationship discovery
3. Compliance framework mapping
4. Risk assessment functionality
5. Security posture report generation
6. Enhanced URL extraction
7. New resource category support
8. Export/import functionality

## Architecture

```
EnhancedResourceControlMapperV2
├── Control Database
│   ├── Control metadata (priority, automation, monitoring)
│   ├── URL links extraction
│   └── Domain classification
├── Resource Mappings
│   ├── 60+ resource categories
│   ├── Risk levels
│   ├── Common vulnerabilities
│   └── Focus areas
├── Analysis Engines
│   ├── Control relationship builder
│   ├── Severity mapper
│   ├── Compliance framework mapper
│   └── Security posture analyzer
└── Reporting
    ├── Summary reports
    ├── Security posture reports
    └── JSON export
```

## Key Improvements Over V1

| Feature | V1 | V2 |
|---------|----|----|
| Control Priority | ❌ | ✅ Automatic priority detection |
| Severity Mapping | ❌ | ✅ Domain-based severity levels |
| Impact Scoring | ❌ | ✅ 0-100 impact scores |
| Control Relationships | ❌ | ✅ Tag and principle-based relationships |
| Compliance Mapping | ❌ | ✅ 6 major frameworks |
| Risk Assessment | Basic | ✅ Comprehensive with vulnerabilities |
| Resource Categories | 40 | ✅ 60+ categories |
| URL Extraction | Basic | ✅ Enhanced with aka.ms support |
| Security Posture | ❌ | ✅ Full posture reporting |
| Automation Detection | ❌ | ✅ Identifies automatable controls |
| Monitoring Requirements | ❌ | ✅ Flags monitoring needs |

## Performance Considerations

- Initial load time: ~2-5 seconds (depending on CSV file sizes)
- Memory usage: ~50-100MB for full control database
- Query performance: O(1) for control lookups
- Report generation: <1 second for typical resource sets

## Future Enhancements

Planned features for V3:
- Machine learning-based control recommendations
- Integration with Azure Policy definitions
- Real-time security score calculation
- Custom compliance framework support
- API endpoint for microservice integration
- Caching layer for improved performance
- Multi-language control descriptions

## Contributing

To add new features or resource categories:

1. Update resource categories in `_generate_comprehensive_mappings()`
2. Add vulnerability patterns in `_get_common_vulnerabilities()`
3. Define focus areas in `_determine_focus_areas()`
4. Update risk classifications in `_determine_risk_level()`
5. Run tests to ensure compatibility

## License

This enhanced mapping system is part of the IaC Guardian project and follows the same licensing terms.
