Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
CRITICAL,NS-1,IngestionStorageAccount.Template.json,30.0,"The Storage Account resources do not specify any networkSecurityGroup, Azure Firewall, or default network restriction setting. By default, Azure Storage Accounts permit public network access unless 'networkAcls' are configured. This violates the requirement to explicitly protect resources using NSGs or Azure Firewall.","Explicitly define the 'networkAcls.defaultAction' property for each storage account to 'Deny', and configure 'bypass' and 'ipRules' as required for legitimate access. You should also set up Azure Firewall rules for advanced scenarios. Ensure network access is restricted to only required sources (e.g., private endpoints, trusted IPs/VNets).",N/A,AI,Generic
CRITICAL,NS-2,LacpGeo.Template.json,118.0,"Cosmos DB database account ('Microsoft.DocumentDB/databaseAccounts') is configured with 'publicNetworkAccess' set to 'Enabled', making it accessible over the public internet and increasing the exposure of sensitive data.",Set 'publicNetworkAccess' to 'Disabled' in the Cosmos DB account configuration to prevent access from the public internet. Use private endpoints or VNet service endpoints to restrict access.,N/A,AI,Generic
CRITICAL,NS-2,LacpGlobal.Template.json,53.0,"CosmosDB is exposed with 'publicNetworkAccess: Enabled' and has no IP rules or VNET rules set (empty lists for 'ipRules' and 'virtualNetworkRules'), resulting in a public endpoint accessible from any IP, contrary to best practices for minimizing exposure.","Disable public network access or set restrictive IP rules and/or implement VNET filters. Preferably, configure private endpoints for CosmosDB to prevent public access.",N/A,AI,Generic
CRITICAL,NS-2,LacpGlobal.Template.json,155.0,"Azure Storage Accounts are configured without IP restrictions or other network rules, exposing them to public endpoints. This increases the risk of data exfiltration or compromise.","Configure the storage accounts' 'networkAcls' property: set 'defaultAction' to 'Deny', specify allowed IPs/subnets, and use private endpoints to eliminate public internet exposure.",N/A,AI,Generic
CRITICAL,NS-2,LacpRegion.Template.json,761.0,The CosmosDB account ('Microsoft.DocumentDB/databaseAccounts') is configured with 'publicNetworkAccess': 'Enabled' and 'isVirtualNetworkFilterEnabled': false. This means the service is fully accessible from the internet and lacks any network restrictions or access controls. This significantly increases exposure to accidental or malicious access.,Set 'publicNetworkAccess' to 'Disabled' and enable 'isVirtualNetworkFilterEnabled'. Define explicit 'virtualNetworkRules' and/or use Private Endpoints to restrict access to trusted networks/services.,N/A,AI,Generic
CRITICAL,NS-1,LacpRegion.Template.json,761.0,CosmosDB account lacks any network restrictions (both NSGs and Azure Firewall are not defined or enforced; 'isVirtualNetworkFilterEnabled': false) and allows access from any IP address/public network. This violates best practices for critical resource protection.,Enable network-level protection: set 'isVirtualNetworkFilterEnabled' to true and configure 'virtualNetworkRules' for trusted subnets and/or use Azure Firewall and NSG rules as applicable.,N/A,AI,Generic
CRITICAL,DP-3,LacpStamp.Parameters-LacpStampResources.json,57.0,The parameter 'dasStorageAccountKey' is being set directly in template parameters instead of being securely referenced from Azure Key Vault or via a secure reference. Storing storage account keys directly in parameters risks sensitive information disclosure.,Store all sensitive secrets (such as storage account keys) in Azure Key Vault and use secret references in templates instead of passing or hardcoding sensitive values directly in parameter files.,N/A,AI,Generic
CRITICAL,NS-2,LacpStamp.Template.json,,"All Storage Accounts and Key Vaults are missing private endpoint configurations and do not restrict public network access. This can expose them to the public internet, violating the requirement to secure public endpoints.","Configure either service endpoints, private endpoints, or resource firewall rules for all Storage Accounts and Key Vaults to disallow public network access and restrict access to specific virtual networks or IP addresses.",N/A,AI,Generic
CRITICAL,NS-2,ReadAdxExhaust.Template.json,20.0,"No property indicating disabling of public endpoints or restriction to a private endpoint for the Microsoft.Kusto/clusters resource. By default, public endpoints may be enabled on Azure Data Explorer clusters, increasing the risk of unwanted external access.",Explicitly set the publicNetworkAccess property to 'Disabled' or define private endpoints to block public access to the Kusto cluster.,N/A,AI,Generic
HIGH,NS-2,IngestionStorageAccount.Template.json,30.0,"The template does not restrict public endpoint access for the Storage Accounts, making them accessible from the public internet by default. 'networkAcls' configuration is missing. This exposes sensitive data to potential unauthorized access.","Add the 'networkAcls' property to each storage account definition to block public network access by setting 'defaultAction' to 'Deny'. Configure exceptions only for trusted networks or private endpoints as needed, following the principle of least privilege.",N/A,AI,Generic
HIGH,NS-3,IngestionStorageAccount.Template.json,30.0,"There are no network rules or references to Network Security Groups (NSGs) that would protect this storage account. Even though NSGs are not directly applied to storage accounts, you should leverage storage firewall rules (networkAcls) and integrate with VNets/subnets protected by NSGs.","Integrate storage accounts with private endpoints or VNet service endpoints, and use NSGs on those subnets to further restrict traffic as per your security requirements.",N/A,AI,Generic
HIGH,NS-1,LacpBilling.Template.json,0.0,"No network security controls such as Azure Firewall or NSGs are defined or associated with the provisioned Storage Accounts. Storage accounts should be protected against unauthorized network access, especially as they are sensitive resources.",Configure network security on Storage Accounts by setting up private endpoints or configuring firewall and virtual networks to restrict access only to approved networks. Add necessary NSG or Azure Firewall rules to strictly limit access.,N/A,AI,Generic
HIGH,NS-2,LacpBilling.Template.json,0.0,"Storage Accounts are deployed without explicit network access controls; unless otherwise restricted, the accounts could have public endpoints enabled. Public endpoints increase exposure to the Internet.",Explicitly restrict public access by configuring the Storage Account network rules to allow only necessary private endpoint or selected subnet traffic. Consider disabling public network access entirely.,N/A,AI,Generic
HIGH,NS-1,LacpBillingExhaust.Template.json,27.0,"The Microsoft.Kusto cluster resource does not specify any network security controls such as private endpoints, virtual networking configuration, Network Security Groups (NSGs), or Azure Firewall integration. By default, this may expose the cluster to public internet access, violating the requirement to protect sensitive resources using NSGs or Azure Firewall.","Deploy the Microsoft.Kusto cluster in a virtual network using the 'virtualNetworkConfiguration' property and configure NSGs and, if appropriate, Azure Firewall to control traffic to and from the service. Use private endpoints to restrict public exposure.",N/A,AI,Generic
HIGH,NS-2,LacpBillingExhaust.Template.json,27.0,"The Microsoft.Kusto cluster resource allows access from a trusted external tenant and does not restrict public endpoints. Without explicit network controls, the cluster may be accessible from the public internet, which increases the risk of unauthorized exposure.","Restrict Kusto cluster access by enabling private endpoints and disallowing public network access. Limit access only to trusted networks and identities, and ensure that only approved external tenants are permitted.",N/A,AI,Generic
HIGH,DP-3,LacpBillingExhaustExport.Parameters-LacpBillingExhaustExport.json,10.0,"The parameter 'adxExhaustDataIngestionUri' is assigned via a cross-template/module reference. If the referenced output contains secrets, connection strings, or sensitive URIs, these are exposed in clear text in an expanded (resolved) parameter file. Storing or passing sensitive data directly within templates or parameter files violates secure data handling practices.","Store all sensitive information, such as secrets or connection strings, in Azure Key Vault and reference them securely using Key Vault references. Avoid placing sensitive values in templates or parameter files, even if resolved via cross-template outputs.",N/A,AI,Generic
HIGH,DP-3,LacpBillingExhaustExport.Parameters-LacpBillingExhaustExport.json,15.0,"The parameter 'adxExhaustUri' is assigned via a cross-template/module reference. If the referenced output contains any sensitive endpoint or credential information, it risks disclosure through the expanded parameter file, violating secure data management standards.","Ensure that all secrets, keys, and sensitive endpoints are stored in Azure Key Vault. Reference such values securely from Key Vault rather than outputting them directly into templates or parameter files.",N/A,AI,Generic
HIGH,DP-3,LacpBillingExhaustExport.Template.json,7.0,"Sensitive connection information (e.g., 'adxExhaustDataIngestionUri' and 'adxExhaustUri') are passed as plain string parameters, instead of being securely referenced from Azure Key Vault. This increases risk of accidental disclosure of secrets or sensitive URIs.","Store sensitive values, such as connection URIs or credentials, in Azure Key Vault and use secureKeyVault reference in parameter definitions. This will prevent accidental exposure and allow for secure secret rotation.",N/A,AI,Generic
HIGH,NS-3,LacpGeo.Template.json,120.0,"Cosmos DB account ('Microsoft.DocumentDB/databaseAccounts') has 'isVirtualNetworkFilterEnabled' set to 'false' and no 'virtualNetworkRules', indicating lack of network filtering and controls over access to the resource.",Set 'isVirtualNetworkFilterEnabled' to 'true' and configure appropriate 'virtualNetworkRules' to allow access only from trusted VNets/subnets.,N/A,AI,Generic
HIGH,NS-8,LacpGeo.Template.json,118.0,"Cosmos DB account does not implement private endpoints, thereby exposing database access over public networks.",Configure Azure Private Endpoints for Cosmos DB accounts to restrict access to internal Azure networks only.,N/A,AI,Generic
HIGH,NS-1,LacpGeo.Template.json,6.0,No evidence of Network Security Groups (NSGs) or Azure Firewall protecting Key Vault or Cosmos DB resources to restrict network-level access.,"Implement NSGs and/or an Azure Firewall to tightly control inbound/outbound traffic to critical resources, including Key Vault and Cosmos DB.",N/A,AI,Generic
HIGH,DP-2,LacpGeo.Template.json,132.0,"Cosmos DB 'minimalTlsVersion' is parameterized and not enforced to be TLS 1.2 or higher in the template. Without explicit value, there is a risk of using deprecated or insecure TLS versions.",Set and enforce 'minimalTlsVersion' to 'TLS1_2' or higher to comply with secure encryption in transit standards.,N/A,AI,Generic
HIGH,NS-1,LacpGlobal.Template.json,53.0,"The CosmosDB account ('Microsoft.DocumentDB/databaseAccounts') has 'publicNetworkAccess' set to 'Enabled', and 'isVirtualNetworkFilterEnabled' is 'false', meaning the account is accessible from any network, violating recommendations to protect sensitive resources like databases using network controls such as firewalls or NSGs.","Set 'publicNetworkAccess' to 'Disabled' and enable 'isVirtualNetworkFilterEnabled'. Add appropriate virtual network rules to restrict access only to trusted subnets/networks. Alternatively, use Private Endpoints for secure access.",N/A,AI,Generic
HIGH,NS-6,LacpGlobal.Template.json,53.0,CosmosDB has 'isVirtualNetworkFilterEnabled' set to 'false' and does not use virtual network service endpoints. This leaves the service exposed to potentially untrusted networks.,Enable 'isVirtualNetworkFilterEnabled' and configure virtual network service endpoints to limit traffic from specific trusted VNETs.,N/A,AI,Generic
HIGH,NS-5,LacpGlobal.Template.json,53.0,"CosmosDB is not deployed with a private endpoint. Without private endpoints, sensitive data services can be exposed to the public internet.",Deploy and use Azure Private Endpoints to CosmosDB to ensure all traffic remains within the Azure backbone and block public endpoint access.,N/A,AI,Generic
HIGH,NS-1,LacpGlobal.Template.json,155.0,"Azure Storage Accounts ('Microsoft.Storage/storageAccounts') have no network rules defined for access control, nor any NSGs or Firewall configurations. By default, this leaves storage endpoints publicly accessible.","Restrict storage account network access by defining 'networkAcls' to allow only trusted IP ranges, subnets, or privatelinks, and set 'publicNetworkAccess' to 'Disabled' where possible.",N/A,AI,Generic
HIGH,NS-5,LacpGlobal.Template.json,155.0,"Storage accounts lack the use of Azure Private Endpoints, leading to their endpoints being accessible from the public internet.",Integrate private endpoints for all storage accounts and disable public endpoint access.,N/A,AI,Generic
HIGH,NS-6,LacpGlobal.Template.json,155.0,"Storage accounts have no implementation of Virtual Network Service Endpoints, missing the opportunity to route only trusted subnet traffic securely.",Add Virtual Network Service Endpoints to the storage accounts and limit access to trusted virtual network subnets.,N/A,AI,Generic
HIGH,DP-2,LacpGlobal.Template.json,53.0,"CosmosDB's property 'minimalTlsVersion' is parameterized, but unless it is always enforced to be 'TLS1_2' or higher by parameter validation, there is a risk that lower or noncompliant versions may be set.","Statically set 'minimalTlsVersion' to 'TLS1_2' and ensure template cannot deploy a lower TLS version. If parameterized, validate that only accepted (>=TLS 1.2) values are possible.",N/A,AI,Generic
HIGH,DP-2,LacpGlobal.Template.json,155.0,"Storage accounts are correctly configured with 'minimumTlsVersion: TLS1_2' and 'supportsHttpsTrafficOnly: true', which satisfies this control. No issue found for storage, but for full compliance check ensure that nothing allows protocol downgrades anywhere else.","None required for current storage settings, but maintain static TLS1.2 enforcement in all resources and avoid parameterizing this value without strict validation.",N/A,AI,Generic
HIGH,NS-6,LacpRegion.Template.json,761.0,"CosmosDB account is not secured with virtual network service endpoints; network filter is disabled with 'isVirtualNetworkFilterEnabled': false, and 'virtualNetworkRules': []. No Azure service endpoint or Private Link is used to restrict service access.",Enable 'isVirtualNetworkFilterEnabled' and use service endpoints or Private Endpoints to significantly reduce attack surface to the CosmosDB account.,N/A,AI,Generic
HIGH,NS-5,LacpRegion.Template.json,761.0,"CosmosDB account does not implement a private endpoint, leaving the service accessible via public endpoints.","Create and associate an Azure Private Endpoint to the CosmosDB account to ensure only private, internal network access is possible.",N/A,AI,Generic
HIGH,NS-2,LacpRegion.Template.json,647.0,"The Key Vault ('Microsoft.KeyVault/vaults') resource does not specify any 'networkAcls' or public network settings. By default, Key Vaults are accessible from the internet unless explicitly restricted. This exposes secret material to broad network access.",Configure 'networkAcls' on the Key Vault to restrict access to trusted virtual networks only and/or disable public network access. Prefer using Private Endpoints.,N/A,AI,Generic
HIGH,NS-5,LacpRegion.Template.json,647.0,"Key Vault is not configured with a private endpoint, so access is potentially public.",Deploy a private endpoint for Key Vault to ensure that all access occurs through the Azure backbone network only.,N/A,AI,Generic
HIGH,NS-6,LacpRegion.Template.json,647.0,"Key Vault lacks virtual network service endpoints; 'networkAcls' are not configured, and access is not restricted at the network layer.",Implement virtual network service endpoints or private endpoints for the Key Vault.,N/A,AI,Generic
HIGH,NS-2,LacpRegion.Template.json,297.0,"Multiple Storage Accounts (e.g., 'dataPullerEventHubStorageAccountName', 'amsBackupStorageAccountName', 'dasCustomerConfigStorageAccountName' etc.) do not define 'networkAcls', 'publicNetworkAccess', or service endpoints. This means the storage accounts are accessible from any network address over the internet by default.",Restrict 'publicNetworkAccess' to 'Disabled' on all Storage Accounts and configure network ACLs and/or Private Endpoints to only allow trusted traffic.,N/A,AI,Generic
HIGH,NS-5,LacpRegion.Template.json,297.0,"Storage Accounts do not have Private Endpoints configured, exposing storage services to the public internet.",Implement Private Endpoints for Storage Accounts to ensure access is restricted to private network traffic.,N/A,AI,Generic
HIGH,NS-6,LacpRegion.Template.json,297.0,"Storage Accounts lack configuration for service endpoints and network restrictions (missing 'networkAcls', 'serviceEndpoints' properties), making them accessible from untrusted networks.",Apply Virtual Network Service Endpoints or Private Endpoints to restrict access to the Storage Accounts.,N/A,AI,Generic
HIGH,DP-3,LacpRegion.Template.json,1048.0,"Sensitive keys (such as CosmosDB and Storage Account connection strings) are stored as secrets in Key Vault, but the actual access key values are concatenated and exposed via outputs (see 'dasStorageAccountKey'). Exposing secrets through ARM outputs risks credential leakage.",Never expose sensitive secrets or access keys in template outputs. Remove the output 'dasStorageAccountKey' and use secure reference architectures for passing secrets to applications.,N/A,AI,Generic
HIGH,NS-1,LacpStamp.Template.json,,No network security groups (NSGs) or Azure Firewall are defined or associated with any Storage Accounts or Key Vault resources. ASB requires critical resources such as Storage Accounts and Key Vaults to be protected by NSGs or Azure Firewall to restrict network access.,"Associate each Storage Account and Key Vault with NSGs and/or restrict access using Azure Firewall. For Storage Accounts and Key Vaults, restrict public access and only allow trusted subnets or virtual networks via firewalls or network rules.",N/A,AI,Generic
HIGH,NS-5,LacpStamp.Template.json,,"Neither the Storage Accounts nor the Key Vaults are configured with private endpoints. ASB recommends using private endpoints for all sensitive resources (such as Storage, Key Vault) to restrict access to traffic only from within the virtual network.",Add 'Microsoft.Network/privateEndpoints' resources for each Storage Account and Key Vault. Link private endpoints to the needed subnets in your virtual network. Ensure public network access is disabled when private endpoints are in use.,N/A,AI,Generic
HIGH,NS-6,LacpStamp.Template.json,,The Storage Accounts and Key Vaults are not configured with virtual network service endpoints. This means traffic is not secured to only flow over Azure's backbone network.,"Add 'networkAcls' with 'virtualNetworkRules' or 'ipRules' configuration to Storage Account and Key Vault resources, explicitly allowing only trusted VNets, subnets, or IP addresses.",N/A,AI,Generic
HIGH,AM-1,LacpStamp.Template.json,,"Several Key Vault access policies grant broad permissions, including all key and secret management actions, to individual identities or AAD objects. This increases attack surface and can violate least privilege access (AM-1).",Review Key Vault access policies and restrict to only the minimal permissions required by each principal. Remove unused or excessive permissions and regularly audit access policies.,N/A,AI,Generic
HIGH,AM-1,LacpStamp.Template.json,,A role assignment ('Data Owner' via Redis accessPolicyAssignments) gives managed identities full ownership access over Redis caches. This is excessive unless the application truly requires full administrative rights.,"Review and reduce access from 'Data Owner' where possible to 'Data Contributor' or other limited roles, following the least privilege principle.",N/A,AI,Generic
HIGH,NS-1,ReadAdxExhaust.Template.json,20.0,"The Microsoft.Kusto/clusters resource does not specify any network security configuration, such as attaching to a private virtual network, restricting access via NSGs, or using Azure Firewall. By default, Kusto clusters without network restrictions may be exposed to the public internet or broader Azure network, which violates network segmentation best practices.",Configure the Kusto cluster with virtual network integration and use Network Security Groups or Azure Firewall to restrict access only to approved networks or resources.,N/A,AI,Generic
HIGH,NS-3,ReadAdxExhaust.Template.json,20.0,The template does not define or reference any Network Security Groups (NSGs) to control inbound or outbound traffic on the Kusto cluster.,Deploy the cluster within a subnet protected by an NSG with rules that tightly control inbound and outbound traffic to only necessary sources and ports.,N/A,AI,Generic
HIGH,IM-8,ReadUsageAccount.Template.json,16.0,The resource 'Microsoft.UsageBilling/accounts' is deployed without explicitly enabling a system-assigned managed identity. This violates the control requiring managed identities for Azure resources to securely support authentication and avoid hardcoded credentials.,Add an 'identity' block with 'type': 'SystemAssigned' to the 'Microsoft.UsageBilling/accounts' resource in the template to enable system-assigned managed identity.,N/A,AI,Generic
HIGH,IM-6,RoleAssignment.Template.json,55.0,"The template assigns the built-in Contributor role (full access, except managing assignments) to the service principal referenced by 'Ev2BuildoutServicePrincipalId'. This grants broad permissions and may violate the principle of least privilege as required by RBAC in ASB.",Review the required permissions for the service principal and assign a more restrictive custom role or a built-in role that limits permissions to only what is necessary for its operation.,N/A,AI,Generic
HIGH,IM-7,RoleAssignment.Template.json,55.0,"The Contributor role is assigned to an application/service principal without explicit controls or comments regarding protection against misuse, over-privileging, or credential security. Applications should be assigned minimal permissions and additional protections according to ASB.","Restrict the service principal's permissions via custom roles where possible, document the privilege rationale, and ensure that the service principal credentials are managed securely (e.g., in Azure Key Vault).",N/A,AI,Generic
HIGH,AM-1,RoleAssignment.Template.json,55.0,"The Contributor role assignment grants wide-ranging permissions, potentially far in excess of what is strictly required for the service principal's function. This violates the least privilege principle.",Analyze actual access requirements and assign narrowly scoped permissions. Use built-in roles with fewer privileges or create a custom role with only the relevant actions permitted.,N/A,AI,Generic
HIGH,NS-2,TrafficManagerEndpoints.Template.json,44.0,"The template defines 'Microsoft.Network/trafficManagerProfiles/externalEndpoints' which by design can expose endpoints to public internet access. There is no evidence of IP whitelisting, access restrictions, or network restriction mechanisms being applied, leaving the endpoints potentially exposed and vulnerable to unauthorized access.","Apply strict traffic restrictions to the exposed endpoints using IP whitelists, Azure Firewall rules, or Network Security Groups where possible. Utilize allowed IP ranges or restrict by specific traffic patterns to limit exposure to only trusted sources.",N/A,AI,Generic
HIGH,NS-1,TrafficManagerEndpoints.Template.json,44.0,No Azure Firewall or Network Security Group (NSG) resource is defined or referenced in the deployment template for protecting the Traffic Manager profiles and their external endpoints. This violates defense-in-depth security by not placing protective controls around public-facing resources.,Include an Azure Firewall or NSG configuration in the template to control and monitor traffic to and from the deployed endpoints. Ensure only permitted traffic based on business requirements is allowed.,N/A,AI,Generic
HIGH,NS-3,TrafficManagerEndpoints.Template.json,44.0,"There is no evidence of a Network Security Group (NSG) configuration or association with the endpoints defined in the template. Without NSGs, there are no controls on allowed inbound or outbound traffic at the resource or subnet level.",Deploy and associate NSGs with the relevant subnets or resources to restrict traffic to the minimum required and block unauthorized access.,N/A,AI,Generic
MEDIUM,IM-1,IngestionStorageAccount.Template.json,30.0,"The template does not enable or require Azure AD authentication for the Storage Accounts. There is no configuration for 'azureFilesIdentityBasedAuthentication' or 'defaultAzureCredential' integration, risking weaker authentication methods being used.",Enable Azure AD authentication for Blob or File services within Storage Accounts by specifying the 'identity' property and related access controls. Ensure clients use Azure AD for accessing storage and assign appropriate RBAC roles for access.,N/A,AI,Generic
MEDIUM,DP-3,IngestionStorageAccount.Template.json,30.0,"The template does not reference Azure Key Vault for secrets, access keys, or SAS tokens, nor does it restrict direct inline declaration of sensitive data, increasing risk of inadvertent exposure or poor secrets management.","Ensure all storage account keys, connection strings, and secrets are managed via Azure Key Vault. Never pass secrets or SAS tokens directly in templates or parameter files.",N/A,AI,Generic
MEDIUM,NS-3,LacpBilling.Template.json,0.0,The template does not assign any Network Security Groups (NSGs) to control inbound or outbound network access to the Storage Accounts. NSGs provide an additional layer of protection within the Azure Virtual Network.,"Associate the Storage Accounts with subnets that have NSGs configured to control network traffic, or use service endpoints/private endpoints combined with NSGs to limit access.",N/A,AI,Generic
MEDIUM,DP-3,LacpBilling.Template.json,0.0,"No usage of Key Vault is observed for storing sensitive configuration or secrets. If this solution ever stores sensitive configuration, secrets, access keys, or connection strings in storage (outside this template), it is critical to ensure those are protected.","Review all secret management practices for the application and ensure that any sensitive data, such as connection strings or keys, are stored in Azure Key Vault instead of being hardcoded, stored in Storage, or outputted directly.",N/A,AI,Generic
MEDIUM,IM-8,LacpBillingExhaust.Template.json,57.0,"Principal assignments use application principal IDs directly (as 'App' principalType) instead of leveraging managed identities for Azure resources. This can lead to less secure, potentially long-lived credentials and less effective lifecycle management.","Where possible, use managed identities (principalType: 'ManagedIdentity') for principal assignments to leverage Azure's secure identity management and credential rotation capabilities.",N/A,AI,Generic
MEDIUM,AM-1,LacpBillingExhaust.Template.json,27.0,"The script 'LACPDataBasePermission' adds the AAD group '<EMAIL>' as a database viewer. Without additional context or restrictions, this may grant more access than intended, violating the principle of least privilege.",Review the AAD group membership and confirm that only users requiring access are included. Assign viewer role only to users or groups with a justified business need.,N/A,AI,Generic
MEDIUM,DP-1,LacpBillingExhaust.Template.json,27.0,"The encryption at rest configuration for the Microsoft.Kusto cluster is not explicitly set in the template. While Azure Data Explorer encrypts data at rest by default, explicit configuration for customer-managed keys (CMK) is recommended for higher security assurances.","Explicitly configure the 'keyVaultProperties' property in the Kusto cluster resource for customer-managed keys, or confirm default encryption meets compliance obligations.",N/A,AI,Generic
MEDIUM,DP-2,LacpBillingExhaustExport.Template.json,51.0,No explicit configuration present to enforce encryption at rest for data exported to Azure Data Explorer (ADX) or destination resources. Lack of explicit encryption settings could lead to storage of unencrypted data.,"Verify that destination resources (e.g., ADX clusters) are configured for encryption at rest, preferably with customer-managed keys. If the template provisions such resources, include relevant encryption settings.",N/A,AI,Generic
MEDIUM,DP-1,LacpBillingExhaustExport.Template.json,51.0,Template does not specify or enforce encryption at rest for exported data or intermediary storage. Sensitive or critical billing exports should be protected against unauthorized access.,Ensure destination storage accounts and databases used by data exports have encryption at rest enabled. Reference or deploy only resources that meet this requirement.,N/A,AI,Generic
MEDIUM,NS-6,LacpGeo.Template.json,118.0,"Cosmos DB does not use virtual network service endpoints, exposing the resource to traffic from all Azure networks and increasing the attack surface.",Enable virtual network service endpoints for Cosmos DB to restrict access to only specified virtual networks.,N/A,AI,Generic
MEDIUM,IM-6,LacpGeo.Template.json,57.0,"Key Vault access policies assign 'Get' and 'List' permissions for keys, secrets, and certificates to a managed identities security group (permissions: 'keys': [ 'Get', 'List' ], etc.) without additional restriction, which may provide more access than required.",Review and scope down access policy permissions for all principals to ensure they follow the principle of least privilege. Remove unnecessary permissions from access policies.,N/A,AI,Generic
MEDIUM,IM-6,LacpGeo.Template.json,85.0,Additional Key Vault access policies grant 'Get' and 'List' permissions broadly to security groups and application identities. Excess privilege increases risk if these identities are compromised.,"Carefully review which principals require access and to what resources. Apply just-in-time or least privilege access models, and regularly audit assignments.",N/A,AI,Generic
MEDIUM,DP-6,LacpGeo.Template.json,118.0,"Cosmos DB account does not explicitly specify the use of customer-managed keys (CMK) for encryption at rest, relying on the default Microsoft-managed keys.",Integrate Cosmos DB with Azure Key Vault using customer-managed keys for data-at-rest encryption to ensure maximum control over cryptographic material.,N/A,AI,Generic
MEDIUM,IM-5,LacpGeo.Template.json,164.0,"There is no evidence in the template of diagnostic settings, audit logs, or monitoring configured for activity on Key Vault or Cosmos DB. This limits visibility into identity and access activities.",Enable diagnostic logging and activity monitoring for Key Vault and Cosmos DB via Azure Monitor to detect unauthorized or suspicious access.,N/A,AI,Generic
MEDIUM,NS-3,LacpGlobal.Template.json,155.0,"No Network Security Groups are associated with the subnets that would access storage accounts, missing an opportunity to further restrict traffic.",Assign appropriate Network Security Groups (NSGs) to the storage account's VNET/subnet and define inbound/outbound rules according to the least privilege principle.,N/A,AI,Generic
MEDIUM,DP-6,LacpGlobal.Template.json,53.0,Neither CosmosDB nor storage accounts have Customer-Managed Keys (CMK) enabled. This is mandatory for compliance in some industries.,Add and configure the Key Vault-managed encryption key for both CosmosDB ('keyVaultKeyUri') and storage ('encryption.keySource' as 'Microsoft.Keyvault').,N/A,AI,Generic
MEDIUM,AM-1,LacpGlobal.Template.json,180.0,"Key Vault access policies grant extensive and potentially excessive permissions (including full management of keys, secrets, certificates) to multiple principals, possibly violating least privilege principles.",Review and minimize Key Vault access policy permissions. Grant only the specific permissions needed by each identity. Remove unnecessary management or high-privilege actions from non-administrative identities.,N/A,AI,Generic
MEDIUM,IM-6,LacpGlobal.Template.json,180.0,"Key Vault access is controlled by access policies, but modern best practice is to enforce access via RBAC (Role-Based Access Control), and tightly scope permissions to individual roles, which is not shown here.","Implement RBAC access control for Key Vaults instead of (or in addition to) access policies, to manage permissions more granularly and centrally, and reduce risk of privilege creep.",N/A,AI,Generic
MEDIUM,DP-5,LacpGlobal.Template.json,53.0,"Backup policies for CosmosDB are set ('Continuous30Days'), but no equivalent backup or replication is specified for storage accounts.","Implement and configure backup strategies for storage accounts (such as blob versioning, blob soft delete, point-in-time restore), in addition to CosmosDB backup policy.",N/A,AI,Generic
MEDIUM,DP-6,LacpRegion.Template.json,761.0,The CosmosDB account does not appear to be using Customer Managed Keys (CMK) for encryption; only default server-side encryption is implied.,Enable Customer Managed Keys (CMK) in CosmosDB settings and specify a Key Vault key reference for more granular control over data encryption keys.,N/A,AI,Generic
MEDIUM,DP-6,LacpRegion.Template.json,647.0,"Key Vault resource has 'enabledForDiskEncryption': false, which means it cannot be used for encrypting VMs or disks with customer-managed keys. This may limit the ability to use CMK across the environment.",Set 'enabledForDiskEncryption' to true on Key Vault to allow it to be used with Azure Disk Encryption for customer-managed keys.,N/A,AI,Generic
MEDIUM,DP-3,LacpRegion.Template.json,1048.0,"Sensitive information (access keys and connection strings) are being constructed and stored explicitly as Key Vault secrets. While Key Vault is being used, ensure proper RBAC on Key Vault to tightly restrict secret access.",Verify that only necessary identities (using least privilege RBAC) have access to the stored secrets in Key Vault.,N/A,AI,Generic
MEDIUM,IM-6,LacpStamp.Template.json,,"Multiple 'Microsoft.Authorization/roleAssignments' grant broad contributor roles (including 'Logic App Contributor', 'Storage Data Contributor', 'RBAC Admin') to managed identities at scope. Over-assignment risks privilege escalation.",Review the roles assigned to each identity. Only assign roles necessary for operation and avoid 'Contributor' roles unless strictly required; prefer resource/data-level roles over management plane access.,N/A,AI,Generic
MEDIUM,DP-6,LacpStamp.Template.json,,"The template does not enable or reference Customer Managed Keys (CMK) for Storage (Storage Accounts, Redis) or Key Vault resources. CMKs offer improved control and compliance for encryption.","Configure Storage Accounts, Redis, and Key Vault resources to use customer-managed keys by setting the appropriate 'encryption' property with a Key Vault key URI.",N/A,AI,Generic
MEDIUM,DP-5,LacpStamp.Template.json,,"The deployment does not include backup or recovery configuration for Storage Accounts, Redis instances, or Key Vaults, which is required for critical data assets.","Implement Azure Backup, configure soft-delete and purging protection for Key Vaults (which is partially set), and ensure Redis/Storage has backup and recovery mechanisms enabled.",N/A,AI,Generic
MEDIUM,IM-5,LacpStamp.Template.json,,"The template does not enable logging or auditing for Key Vault, Storage, or Identity access activities (such as diagnostic settings or activity logs). Missing audit trails impairs detection of unauthorized access.","Enable diagnostic settings for Key Vaults, Storage Accounts, and managed identities to forward logs to a Log Analytics workspace or SIEM for monitoring and alerting.",N/A,AI,Generic
MEDIUM,DP-1,ReadAdxExhaust.Template.json,20.0,"The template does not explicitly enable customer-managed key (CMK) encryption for the Microsoft.Kusto/clusters resource. While Azure Data Explorer provides server-side encryption by default, not explicitly enabling or managing encryption with customer-managed keys may not meet stringent compliance requirements.",Define the keyVaultProperties block within the cluster configuration to specify a customer-managed key for encryption at rest if required by compliance or organizational policy.,N/A,AI,Generic
MEDIUM,DP-3,ReadAdxExhaust.Template.json,20.0,"Sensitive or parameterized values (e.g., sku name) are passed as parameters but there is no reference to secure parameters or Key Vault integration for secrets or confidential information. While there is no direct evidence of hardcoded secrets, absence of explicit secure parameter handling could allow sensitive data exposure in future template changes.","Ensure all sensitive parameters are handled as 'secureString' and integrate with Azure Key Vault for secret retrieval instead of direct parameter input, especially if expanding the template to cover secrets like admin passwords or keys.",N/A,AI,Generic
MEDIUM,IM-7,ReadIdentity.Template.json,24.0,"The template's outputs section exposes sensitive properties (clientId and principalId) for a user-assigned managed identity. While these values are not secrets, making them available as outputs can increase the risk of information disclosure and could aid attackers in enumeration or lateral movement if template outputs are broadly accessible. ASB Control IM-7 recommends securing and protecting application identities and their information.","Only output identity properties when absolutely necessary for downstream resources, and ensure outputs are not exposed to unauthorised callers or automation. Avoid excessive exposure in shared CI/CD logs or deployment outputs.",N/A,AI,Generic
MEDIUM,NS-5,TrafficManagerEndpoints.Template.json,44.0,The deployment template does not implement Private Endpoints for the traffic manager profiles or their external endpoints. This leaves resources accessible over public networks instead of securely over a private Azure backbone.,"Where supported, configure Private Endpoints for back-end resources to ensure traffic between Azure services remains on the private network and is not exposed to the public Internet.",N/A,AI,Generic
MEDIUM,NS-9,TrafficManagerEndpoints.Template.json,44.0,"The template lacks any configuration for logging or monitoring of network traffic to and from the external endpoints. Without monitoring, detection of suspicious activity or troubleshooting is significantly more difficult.",Enable Azure Monitor diagnostic settings and Network Watcher flow logs for the Traffic Manager resources and associated endpoints to collect and analyze traffic data.,N/A,AI,Generic
LOW,DP-1,IngestionStorageAccount.Template.json,30.0,"There is no explicit statement of encryption at rest being enabled. While Azure Storage enables Microsoft-managed encryption at rest by default, the template does not indicate use of customer-managed keys (CMK), which may be a compliance requirement in some contexts.","Explicitly configure 'encryption' properties in the storage account resource, and if required, specify customer-managed keys (CMK) via a Key Vault reference in the 'encryption.keySource' property.",N/A,AI,Generic
LOW,DP-1,LacpBilling.Template.json,0.0,"While the template leverages StorageV2 and sets TLS 1.2 with HTTPS only, it does not specify encryption key settings for encryption at rest (e.g., customer-managed keys). However, default Microsoft-managed encryption at rest is applied with StorageV2.","If regulatory compliance or higher assurance for data protection is required, enable and configure customer-managed keys (CMK) for encryption at rest via the 'encryption' block, referencing an Azure Key Vault key.",N/A,AI,Generic
LOW,IM-7,LacpBilling.Template.json,0.0,"Though managed identities are used, there is no explicit limiting of the managed identity's access or permissions in the template beyond role assignments. Over-provisioned identities can increase risk.",Review assigned roles and limit permissions for managed identities to the minimal set required. Regularly audit the managed identity access and ensure it cannot be used for lateral movement.,N/A,AI,Generic
LOW,DP-2,LacpBillingExhaust.Template.json,27.0,"The template does not specify the minimum TLS version for the Microsoft.Kusto cluster. Azure Data Explorer supports TLS 1.2, but explicit enforcement of TLS version is not present in the template.","Explicitly set 'minimalTlsVersion' property (e.g., '1.2') in the Kusto cluster resource to ensure all data in transit uses TLS 1.2 or higher.",N/A,AI,Generic
LOW,IM-8,LacpGeo.Template.json,243.0,"Principal assignments for Cosmos DB use a 'managedIdentitiesSecurityGroupId', but there is no explicit declaration or reference to Azure Managed Identity resources within the template.","Where possible, prefer using Azure Managed Identity objects directly for resource-to-resource authentication, and ensure group memberships are tightly managed.",N/A,AI,Generic
LOW,IM-3,LacpGeo.Template.json,6.0,Template does not include requirements or references to Conditional Access Policies for identities with privileges over Key Vault or Cosmos DB.,"Implement, document, and reference Conditional Access Policies for privileged accounts and applications to enforce secure access.",N/A,AI,Generic
LOW,DP-3,LacpGeo.Template.json,98.0,"Cosmos DB account's primary master key is being extracted via ARM function and stored as a secret in Key Vault, but there is no evidence of Key Vault RBAC (access policies are used instead), which may lead to weaker granularity and separation.",Consider using Key Vault RBAC access control mode with finely scoped Azure RBAC assignments and limit the use of access policies.,N/A,AI,Generic
LOW,DP-1,LacpGlobal.Template.json,53.0,"CosmosDB and Azure Storage Accounts both have platform-managed encryption at rest by default. However, the template does not specify support for Customer-Managed Keys (CMK), potentially missing stricter security requirements for regulated data.","Where sensitive or regulated data is stored, configure resources to use CMK for encryption at rest by referencing a Key Vault-managed key. Add 'keyVaultKeyUrl' or relevant CMK property to CosmosDB and storage resources.",N/A,AI,Generic
LOW,DP-3,LacpGlobal.Template.json,235.0,"CosmosDB and Storage account secret values (primary keys, connection strings) are placed as secrets in Key Vault, which aligns with best practice. However, access policies may be too permissive.","Review Key Vault access policies to ensure secrets are only accessible by necessary identities/applications. Remove broad or unnecessary 'Get', 'List', or management permissions, and use RBAC where possible.",N/A,AI,Generic
LOW,NS-18,LacpRegion.Template.json,1.0,There is no evidence that network traffic monitoring (with Azure Monitor/Network Watcher) is configured for any resource in this template.,"Enable diagnostic logging and network traffic monitoring (using Azure Monitor and Network Watcher) for all high-value resources, especially those exposed to public networks.",N/A,AI,Generic
LOW,IM-4,LacpStamp.Template.json,,There is no evidence of regular review or automation to manage and prune privileged Azure AD accounts or Key Vault access policies in the template.,Implement access review processes (manually or with Azure AD Access Reviews) to periodically validate the necessity and appropriateness of privileged user/service principal access.,N/A,AI,Generic
LOW,NS-9,LacpStamp.Template.json,,No explicit configuration to log network traffic to/log from resources (such as diagnostic settings for Storage or network watcher).,"Enable diagnostic logging at the network and resource level for all critical assets, using Azure Monitor Network Watcher and resource-level diagnostic settings.",N/A,AI,Generic
LOW,DP-3,LacpStamp.Template.json,,"The secret '[concat(variables('stampSharedKeyVaultName'), '/', parameters('dasStorageAccountName'),'-key')]' retrieves its value directly from a parameter 'dasStorageAccountKey', which could be an in-memory or parameter-passed sensitive secret. If this isn't sourced securely (e.g., passed in plain ARM parameter files), it risks disclosure.","Ensure sensitive parameters are securely referenced from Key Vault, not passed inline. If using parameters for secrets, pass them as Key Vault references and not in plain ARM files.",N/A,AI,Generic
LOW,IM-8,ReadIdentity.Template.json,24.0,"The template references a user-assigned managed identity but does not define the identity resource within this template, nor does it validate its existence or correct configuration. ASB Control IM-8 requires managed identities to be used for secure authentication, and best practice is to ensure the referenced identity exists and has appropriate minimal permissions.","Explicitly declare and configure the required managed identity within the deployment template when possible, or add validation steps to confirm its existence, scope, and security posture. Ensure the identity has the least privileges required for its workload.",N/A,AI,Generic
