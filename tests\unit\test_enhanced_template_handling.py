#!/usr/bin/env python3
"""
Test script for enhanced template handling improvements.
"""

import os
import json
import tempfile
import unittest
from pathlib import Path
from template_parameter_expander import TemplateParameterExpander


class TestEnhancedTemplateHandling(unittest.TestCase):
    """Test cases for enhanced template handling features"""

    def setUp(self):
        """Set up test environment"""
        self.expander = TemplateParameterExpander()
        self.test_dir = tempfile.mkdtemp()
        self.test_path = Path(self.test_dir)

    def tearDown(self):
        """Clean up test environment"""
        import shutil
        shutil.rmtree(self.test_dir, ignore_errors=True)

    def test_enhanced_arm_functions(self):
        """Test enhanced ARM template function support"""
        template_content = '''
        {
            "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
            "contentVersion": "*******",
            "parameters": {
                "appName": {
                    "type": "string",
                    "defaultValue": "testapp"
                }
            },
            "variables": {
                "resourceGroupName": "[resourceGroup().name]",
                "uniqueName": "[uniqueString(resourceGroup().id, parameters('appName'))]",
                "fullName": "[concat(parameters('appName'), '-', variables('uniqueName'))]"
            },
            "resources": [
                {
                    "type": "Microsoft.Storage/storageAccounts",
                    "name": "[variables('fullName')]",
                    "location": "[resourceGroup().location]"
                }
            ]
        }
        '''
        
        expanded_content, params_used = self.expander.expand_template(template_content)
        
        # Verify parameter expansion
        self.assertIn("testapp", expanded_content)
        self.assertEqual(params_used["appName"], "testapp")
        
        # Verify function expansion
        self.assertNotIn("[resourceGroup()", expanded_content)
        self.assertNotIn("[uniqueString(", expanded_content)
        self.assertNotIn("[concat(", expanded_content)

    def test_bicep_module_detection(self):
        """Test Bicep module reference detection"""
        bicep_content = '''
        param appName string = 'testapp'
        param location string = resourceGroup().location

        module storage 'modules/storage.bicep' = {
          name: 'storageModule'
          params: {
            storageAccountName: appName
            location: location
          }
        }

        module network './modules/network.bicep' = {
          name: 'networkModule'
          params: {
            vnetName: '${appName}-vnet'
          }
        }
        '''
        
        # Create test module files
        modules_dir = self.test_path / "modules"
        modules_dir.mkdir()
        
        storage_module = modules_dir / "storage.bicep"
        storage_module.write_text('''
        param storageAccountName string
        param location string
        
        resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {
          name: storageAccountName
          location: location
        }
        ''')
        
        network_module = modules_dir / "network.bicep"
        network_module.write_text('''
        param vnetName string
        
        resource vnet 'Microsoft.Network/virtualNetworks@2021-02-01' = {
          name: vnetName
        }
        ''')
        
        # Test expansion with module resolution
        main_template = self.test_path / "main.bicep"
        main_template.write_text(bicep_content)
        
        expanded_content, params_used = self.expander.expand_template_with_references(
            bicep_content, None, str(main_template)
        )
        
        # Verify module references are processed
        self.assertIn("EXPANDED_MODULE", expanded_content)
        self.assertIn("storageModule", expanded_content)
        self.assertIn("networkModule", expanded_content)

    def test_cross_reference_detection(self):
        """Test cross-reference detection"""
        # ARM template with linked template
        arm_with_link = '''
        {
            "resources": [
                {
                    "type": "Microsoft.Resources/deployments",
                    "properties": {
                        "templateLink": {
                            "uri": "./linked-template.json"
                        }
                    }
                }
            ]
        }
        '''
        
        # Bicep with module
        bicep_with_module = '''
        module myModule 'modules/storage.bicep' = {
          name: 'storage'
        }
        '''
        
        # Test ARM cross-reference detection
        from security_opt import SecurityPRReviewer
        reviewer = SecurityPRReviewer(local_folder=self.test_dir)
        
        self.assertTrue(reviewer._has_cross_references(arm_with_link))
        self.assertTrue(reviewer._has_cross_references(bicep_with_module))
        self.assertFalse(reviewer._has_cross_references('{"resources": []}'))

    def test_parameter_security_validation(self):
        """Test parameter security validation"""
        param_content = '''
        {
            "parameters": {
                "adminPassword": {
                    "type": "string",
                    "value": "PlainTextPassword123!"
                },
                "clientSecret": {
                    "type": "string", 
                    "value": "secret123"
                },
                "appName": {
                    "type": "string",
                    "value": "myapp"
                }
            }
        }
        '''
        
        # Create parameter file
        param_file = self.test_path / "parameters.json"
        param_file.write_text(param_content)
        
        # Load parameter values (this triggers security validation)
        param_values = self.expander._load_parameter_values(str(param_file))
        
        # Check for security findings
        findings = self.expander.get_security_findings()
        
        # Should find issues with adminPassword and clientSecret
        sensitive_params = {finding["description"].split("'")[1] for finding in findings}
        self.assertIn("adminPassword", sensitive_params)
        self.assertIn("clientSecret", sensitive_params)

    def test_nested_function_expansion(self):
        """Test nested ARM function expansion"""
        template_content = '''
        {
            "variables": {
                "nestedFunction": "[concat('prefix-', uniqueString(resourceGroup().id))]",
                "complexExpression": "[if(equals(parameters('env'), 'prod'), concat('prod-', variables('nestedFunction')), 'dev-simple')]"
            }
        }
        '''
        
        params = {"env": "prod"}
        expanded_content, _ = self.expander.expand_template(template_content, params)
        
        # Verify nested functions are expanded
        self.assertNotIn("[concat(", expanded_content)
        self.assertNotIn("[uniqueString(", expanded_content)
        self.assertNotIn("[if(", expanded_content)
        self.assertNotIn("[equals(", expanded_content)

    def test_error_handling(self):
        """Test error handling for invalid templates"""
        # Invalid JSON
        invalid_json = '{"invalid": json}'
        
        expanded_content, params_used = self.expander.expand_template(invalid_json)
        
        # Should return original content on error
        self.assertEqual(expanded_content, invalid_json)
        self.assertEqual(params_used, {})

    def test_reference_depth_limiting(self):
        """Test reference depth limiting to prevent infinite recursion"""
        # Set low depth limit for testing
        self.expander.max_reference_depth = 2
        
        # Create circular reference scenario
        template_a = self.test_path / "template_a.json"
        template_b = self.test_path / "template_b.json"
        
        template_a.write_text('''
        {
            "resources": [
                {
                    "type": "Microsoft.Resources/deployments",
                    "properties": {
                        "templateLink": {
                            "uri": "./template_b.json"
                        }
                    }
                }
            ]
        }
        ''')
        
        template_b.write_text('''
        {
            "resources": [
                {
                    "type": "Microsoft.Resources/deployments", 
                    "properties": {
                        "templateLink": {
                            "uri": "./template_a.json"
                        }
                    }
                }
            ]
        }
        ''')
        
        # This should not cause infinite recursion
        expanded_content, _ = self.expander.expand_template_with_references(
            template_a.read_text(), None, str(template_a)
        )
        
        # Should complete without error
        self.assertIsInstance(expanded_content, str)


if __name__ == "__main__":
    # Run tests
    unittest.main(verbosity=2)
