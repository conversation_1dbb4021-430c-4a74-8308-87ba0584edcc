# IaC Guardian MCP Server - Troubleshooting Guide

## Issue: Server Connection Error

You're seeing this error:
```
Connection state: Error Error sending message to http://127.0.0.1:8123/: TypeError: fetch failed
Server exited before responding to `initialize` request.
```

This indicates VS Code is trying to connect via HTTP instead of stdio (standard input/output).

## Solution 1: Manual VS Code Settings Configuration

1. **Open VS Code Settings**
   - Press `Ctrl+Shift+P`
   - Type "Preferences: Open Settings (JSON)"
   - Click on it

2. **Add MCP Configuration**
   Add this to your settings.json:

```json
{
  "github.copilot.chat.experimental.mcp.enabled": true,
  "github.copilot.chat.experimental.mcp.servers": {
    "iac-guardian": {
      "command": "python",
      "args": ["-u", "mcp_server.py"],
      "cwd": "C:/Users/<USER>/REPOS/IaCGuardianGPT",
      "env": {
        "ENFORCE_DOMAIN_PRIORITY": "true",
        "USE_OPTIMIZED_PROMPTS": "true",
        "ANALYSIS_SEED": "42",
        "PYTHONPATH": "C:/Users/<USER>/REPOS/IaCGuardianGPT"
      }
    }
  }
}
```

3. **Save and Restart VS Code**

## Solution 2: Alternative MCP Configuration

If the above doesn't work, try this alternative configuration:

```json
{
  "github.copilot.chat.experimental.mcp.enabled": true,
  "github.copilot.chat.experimental.mcp.servers": {
    "iac-guardian": {
      "command": "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/python.exe",
      "args": ["-u", "C:/Users/<USER>/REPOS/IaCGuardianGPT/mcp_server.py"],
      "env": {
        "ENFORCE_DOMAIN_PRIORITY": "true",
        "USE_OPTIMIZED_PROMPTS": "true",
        "ANALYSIS_SEED": "42"
      }
    }
  }
}
```

## Solution 3: Test MCP Server Manually

1. **Test the server directly:**
```bash
cd C:/Users/<USER>/REPOS/IaCGuardianGPT
python mcp_server.py
```

2. **If it starts without errors, the server is working**

3. **Test with MCP client:**
```bash
python test_mcp_simple.py
```

## Solution 4: Check VS Code Extensions

1. **Ensure GitHub Copilot is updated:**
   - Go to Extensions (Ctrl+Shift+X)
   - Search for "GitHub Copilot"
   - Update if available

2. **Check for MCP support:**
   - MCP support in VS Code Copilot is experimental
   - Ensure you have the latest version

## Solution 5: Alternative Integration Method

If MCP doesn't work, you can use the tools directly:

### Direct Python Usage
```bash
# Analyze a file
python -c "
from security_opt import SecurityPRReviewer
import tempfile
import json

reviewer = SecurityPRReviewer(local_folder='.')
files = reviewer.analyze_folder('./your-templates')
findings = reviewer.analyze_files(files)
print(json.dumps(findings, indent=2))
"
```

### Create Custom VS Code Task
Add to `.vscode/tasks.json`:

```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "IaC Security Analysis",
      "type": "shell",
      "command": "python",
      "args": ["security_opt.py", "--local-folder", "${workspaceFolder}", "--export-format", "html"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new"
      },
      "problemMatcher": []
    }
  ]
}
```

Then use `Ctrl+Shift+P` → "Tasks: Run Task" → "IaC Security Analysis"

## Solution 6: Debug MCP Connection

1. **Enable VS Code Developer Tools:**
   - Help → Toggle Developer Tools
   - Check Console for MCP-related errors

2. **Check MCP Server Logs:**
   - Look for server startup messages
   - Check if the server process starts correctly

## Solution 7: Fallback - Command Line Usage

If VS Code integration doesn't work, you can still use all the functionality:

### Analyze Single File
```bash
python -c "
import asyncio
from mcp_server import analyze_iac_file
result = asyncio.run(analyze_iac_file({
    'file_path': './your-template.json',
    'format': 'markdown'
}))
print(result[0].text)
"
```

### Analyze Folder
```bash
python -c "
import asyncio
from mcp_server import analyze_iac_folder
result = asyncio.run(analyze_iac_folder({
    'folder_path': './your-templates',
    'format': 'summary',
    'export_report': True
}))
print(result[0].text)
"
```

### Get Security Controls
```bash
python -c "
import asyncio
from mcp_server import get_security_controls
result = asyncio.run(get_security_controls({
    'resource_type': 'Storage',
    'domain': 'Data Protection'
}))
print(result[0].text)
"
```

## Common Issues and Fixes

### Issue: Python not found
**Fix:** Use full Python path in VS Code settings

### Issue: Module import errors
**Fix:** Ensure PYTHONPATH is set correctly in environment

### Issue: Permission errors
**Fix:** Run VS Code as administrator (Windows) or check file permissions

### Issue: MCP not supported
**Fix:** Update VS Code and GitHub Copilot extension

## Verification Steps

1. ✅ **MCP Server Works:** `python test_mcp_simple.py` passes
2. ✅ **VS Code Settings:** Configuration is properly formatted JSON
3. ✅ **Extensions Updated:** GitHub Copilot is latest version
4. ✅ **Restart VS Code:** After configuration changes
5. ✅ **Check Logs:** VS Code Developer Tools show no errors

## Alternative: Use as Regular Python Tool

Even without VS Code integration, you have a powerful security analysis tool:

```bash
# Quick analysis
python security_opt.py --local-folder ./templates --export-format html

# Generate reports
python -c "
from security_opt import SecurityPRReviewer
reviewer = SecurityPRReviewer(local_folder='./templates')
files = reviewer.analyze_folder('./templates')
findings = reviewer.analyze_files(files)
reviewer.export_findings(findings, 'both', 'security_reports')
print(f'Analysis complete: {len(findings)} findings')
"
```

The security analysis functionality is fully working - the MCP integration is just one way to access it!
