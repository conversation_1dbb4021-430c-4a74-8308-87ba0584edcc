Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P1-Identity-HIGH,Identity Management,HIGH,IM-1,template.json,61,App Service does not explicitly integrate with Azure Active Directory for identity management.,Configure Azure Active Directory authentication for the App Service.,N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-2,template.json,61,App Service does not enforce Multi-Factor Authentication (MFA) for users or administrators.,Enable and enforce MFA for all users and administrators accessing the App Service.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,61,"App Service 'publicNetworkAccess' is set to 'Enabled' and ipSecurityRestrictions allow 'Any' IP, exposing the application to the public internet.",Restrict 'publicNetworkAccess' to 'Disabled' or configure 'ipSecurityRestrictions' to allow only trusted IP ranges.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,180,"App Service config 'ipSecurityRestrictions' allows 'Any' IP with action 'Allow', exposing the application to the public internet.",Remove the 'Allow all' rule or restrict 'ipSecurityRestrictions' to only trusted IP addresses.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,188,"App Service config 'scmIpSecurityRestrictions' allows 'Any' IP with action 'Allow', exposing the SCM endpoint to the public internet.",Remove the 'Allow all' rule or restrict 'scmIpSecurityRestrictions' to only trusted IP addresses.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,61,App Service is accessible via public endpoints and does not use private endpoints.,Configure a private endpoint for the App Service to restrict access to internal networks only.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,61,App Service does not specify use of customer-managed keys or explicit encryption at rest settings.,"Configure App Service to use encryption at rest and, if required, customer-managed keys for all data storage.",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,61,"App Service hostNameSslStates: SSL is disabled for 'onefuzz-daily-ui.azurewebsites.net' and 'onefuzz-daily-ui.scm.azurewebsites.net', violating encryption in transit requirements.",Enable SSL (set 'sslState' to 'SniEnabled' or 'IpBasedEnabled') for all hostNameSslStates to ensure TLS 1.2+ is enforced for all endpoints.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,180,"App Service config includes 'publishingUsername' in plain text, which is sensitive information.",Remove 'publishingUsername' from the template and use Azure Key Vault references for sensitive credentials.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-6,template.json,61,App Service does not specify use of customer-managed keys (CMK) for encryption.,Configure App Service to use customer-managed keys for data encryption if handling sensitive or regulated data.,N/A,AI,Generic
P4-Access-HIGH,Access Management,HIGH,AM-1,template.json,61,"App Service allows unrestricted public access due to 'ipSecurityRestrictions' allowing 'Any' IP, violating least privilege access.",Restrict 'ipSecurityRestrictions' to only allow required IP addresses or ranges.,N/A,AI,Generic
