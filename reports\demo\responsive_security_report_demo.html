
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Security Findings Report - IaC Guardian</title>
                <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
                <style>
                    :root {
                        --primary-color: #2c3e50;
                        --secondary-color: #3498db;
                        --success-color: #27ae60;
                        --warning-color: #f39c12;
                        --danger-color: #e74c3c;
                        --info-color: #17a2b8;
                        --light-bg: #f8f9fa;
                        --dark-bg: #343a40;
                        --border-color: #dee2e6;
                        --text-color: #495057;
                        --shadow: 0 2px 4px rgba(0,0,0,0.1);
                        --border-radius: 8px;
                    }

                    * {
                        margin: 0;
                        padding: 0;
                        box-sizing: border-box;
                    }

                    body {
                        font-family: 'Se<PERSON>e <PERSON>', Tahoma, Geneva, Verdana, sans-serif;
                        line-height: 1.6;
                        color: var(--text-color);
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        min-height: 100vh;
                    }

                    .container {
                        max-width: 1200px;
                        margin: 0 auto;
                        padding: 20px;
                    }

                    .header {
                        background: white;
                        border-radius: var(--border-radius);
                        padding: 30px;
                        margin-bottom: 30px;
                        box-shadow: var(--shadow);
                        text-align: center;
                    }

                    .header h1 {
                        color: var(--primary-color);
                        font-size: 2.5rem;
                        margin-bottom: 10px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 15px;
                    }

                    .header .subtitle {
                        color: var(--text-color);
                        font-size: 1.1rem;
                        opacity: 0.8;
                    }

                    .controls {
                        background: white;
                        border-radius: var(--border-radius);
                        padding: 20px;
                        margin-bottom: 30px;
                        box-shadow: var(--shadow);
                        display: flex;
                        flex-wrap: wrap;
                        gap: 15px;
                        align-items: center;
                    }

                    .search-box {
                        flex: 1;
                        min-width: 250px;
                        position: relative;
                    }

                    .search-box input {
                        width: 100%;
                        padding: 12px 45px 12px 15px;
                        border: 2px solid var(--border-color);
                        border-radius: var(--border-radius);
                        font-size: 14px;
                        transition: border-color 0.3s;
                    }

                    .search-box input:focus {
                        outline: none;
                        border-color: var(--secondary-color);
                    }

                    .search-box i {
                        position: absolute;
                        right: 15px;
                        top: 50%;
                        transform: translateY(-50%);
                        color: var(--text-color);
                        opacity: 0.5;
                    }

                    .filter-buttons {
                        display: flex;
                        gap: 10px;
                        flex-wrap: wrap;
                    }

                    .filter-btn {
                        padding: 8px 16px;
                        border: 2px solid;
                        border-radius: 20px;
                        background: white;
                        cursor: pointer;
                        transition: all 0.2s ease-in-out;
                        font-size: 12px;
                        font-weight: 600;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                        position: relative;
                        overflow: hidden;
                        min-height: 36px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }

                    .filter-btn:hover {
                        transform: translateY(-1px);
                        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                    }

                    .filter-btn.active {
                        border-width: 3px;
                        font-weight: 700;
                        transform: scale(1.05);
                        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                    }

                    .filter-btn.critical {
                        border-color: var(--danger-color);
                        color: var(--danger-color);
                    }

                    .filter-btn.critical.active {
                        border-color: var(--danger-color);
                        color: var(--danger-color);
                    }

                    .filter-btn.high {
                        border-color: var(--warning-color);
                        color: var(--warning-color);
                    }

                    .filter-btn.high.active {
                        border-color: var(--warning-color);
                        color: var(--warning-color);
                    }

                    .filter-btn.medium {
                        border-color: #ffc107;
                        color: #856404;
                    }

                    .filter-btn.medium.active {
                        border-color: #ffc107;
                        color: #856404;
                    }

                    .filter-btn.low {
                        border-color: var(--info-color);
                        color: var(--info-color);
                    }

                    .filter-btn.low.active {
                        border-color: var(--info-color);
                        color: var(--info-color);
                    }

                    .filter-btn.all {
                        border-color: var(--secondary-color);
                        color: var(--secondary-color);
                    }

                    .filter-btn.all.active {
                        border-color: var(--secondary-color);
                        color: var(--secondary-color);
                    }

                    .summary {
                        background: white;
                        border-radius: var(--border-radius);
                        padding: 30px;
                        margin-bottom: 30px;
                        box-shadow: var(--shadow);
                    }

                    .summary h2 {
                        color: var(--primary-color);
                        margin-bottom: 20px;
                        font-size: 1.5rem;
                    }

                    .stats-grid {
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                        gap: 20px;
                        margin-bottom: 20px;
                    }

                    .stat-card {
                        background: var(--light-bg);
                        padding: 20px;
                        border-radius: var(--border-radius);
                        text-align: center;
                        border-left: 4px solid var(--secondary-color);
                        min-height: 120px;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                    }

                    .stat-number {
                        font-size: 2rem;
                        font-weight: bold;
                        color: var(--primary-color);
                    }

                    .stat-label {
                        color: var(--text-color);
                        font-size: 0.9rem;
                        margin-top: 5px;
                    }

                    .severity-breakdown {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 15px;
                        margin-top: 20px;
                    }

                    .severity-stat {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        padding: 10px 15px;
                        border-radius: 20px;
                        background: var(--light-bg);
                        font-weight: 600;
                    }

                    .severity-stat.critical {
                        background: rgba(231, 76, 60, 0.1);
                        color: var(--danger-color);
                    }

                    .severity-stat.high {
                        background: rgba(243, 156, 18, 0.1);
                        color: var(--warning-color);
                    }

                    .severity-stat.medium {
                        background: rgba(255, 193, 7, 0.1);
                        color: #856404;
                    }

                    .severity-stat.low {
                        background: rgba(23, 162, 184, 0.1);
                        color: var(--info-color);
                    }

                    .findings-container {
                        background: transparent;
                        padding: 0;
                        margin: 0;
                    }

                    .severity-group {
                        background: white;
                        border: 2px solid #ddd;
                        border-radius: 12px;
                        margin: 0 0 40px 0;
                        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
                        overflow: hidden;
                        position: static;
                        width: 100%;
                        box-sizing: border-box;
                        display: block;
                        isolation: isolate;
                    }

                    .severity-group:last-child {
                        margin-bottom: 0;
                    }

                    .severity-header {
                        padding: 20px 30px;
                        color: white;
                        font-weight: bold;
                        font-size: 1.2rem;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        cursor: pointer;
                        transition: all 0.3s;
                        position: relative;
                        margin: 0;
                        border: none;
                        width: 100%;
                        box-sizing: border-box;
                    }

                    .severity-header:hover {
                        opacity: 0.9;
                    }

                    .severity-header .toggle-icon {
                        margin-left: auto;
                        transition: transform 0.3s;
                    }

                    .severity-header.collapsed .toggle-icon {
                        transform: rotate(-90deg);
                    }

                    .severity-header .count {
                        background: rgba(255, 255, 255, 0.2);
                        padding: 4px 12px;
                        border-radius: 15px;
                        font-size: 0.9rem;
                    }

                    .critical {
                        background: linear-gradient(135deg, #e74c3c, #c0392b);
                    }

                    .high {
                        background: linear-gradient(135deg, #f39c12, #e67e22);
                    }

                    .medium {
                        background: linear-gradient(135deg, #ffc107, #f39c12);
                        color: #856404 !important;
                    }

                    .low {
                        background: linear-gradient(135deg, #17a2b8, #138496);
                    }

                    .findings-list {
                        max-height: 600px;
                        overflow-y: auto;
                        transition: max-height 0.3s ease-out;
                        scroll-behavior: smooth;
                        scrollbar-width: thin;
                        scrollbar-color: var(--border-color) var(--light-bg);
                        background: white;
                        margin: 0;
                        padding: 0;
                        border: none;
                        width: 100%;
                        box-sizing: border-box;
                    }

                    .findings-list.collapsed {
                        max-height: 0;
                        overflow: hidden;
                    }

                    .finding {
                        border-bottom: 1px solid #f0f0f0;
                        padding: 25px 30px;
                        transition: background-color 0.3s;
                        position: relative;
                        min-height: 200px;
                        display: flex;
                        flex-direction: column;
                        background: white;
                        margin: 0;
                        border-left: none;
                        border-right: none;
                        border-top: none;
                        width: 100%;
                        box-sizing: border-box;
                    }

                    .finding:last-child {
                        border-bottom: none;
                    }

                    .finding:hover {
                        background: var(--light-bg);
                    }

                    .finding-header {
                        display: flex;
                        align-items: flex-start;
                        gap: 15px;
                        margin-bottom: 15px;
                    }

                    .finding-icon {
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: white;
                        font-size: 1.2rem;
                        flex-shrink: 0;
                    }

                    .finding-icon.critical {
                        background: var(--danger-color);
                    }

                    .finding-icon.high {
                        background: var(--warning-color);
                    }

                    .finding-icon.medium {
                        background: #ffc107;
                        color: #856404;
                    }

                    .finding-icon.low {
                        background: var(--info-color);
                    }

                    .finding-content {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                    }

                    .finding-title {
                        font-size: 1.1rem;
                        font-weight: 600;
                        color: var(--primary-color);
                        margin-bottom: 8px;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                    }

                    .control-badge {
                        background: var(--secondary-color);
                        color: white;
                        padding: 2px 8px;
                        border-radius: 12px;
                        font-size: 0.8rem;
                        font-weight: 500;
                    }

                    .finding-meta {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 15px;
                        margin-bottom: 15px;
                        font-size: 0.9rem;
                        color: var(--text-color);
                    }

                    .meta-item {
                        display: flex;
                        align-items: center;
                        gap: 5px;
                    }

                    .meta-item i {
                        color: var(--secondary-color);
                    }

                    .finding-description {
                        margin-bottom: 15px;
                        line-height: 1.6;
                    }

                    .finding-remediation {
                        background: var(--light-bg);
                        padding: 15px;
                        border-radius: var(--border-radius);
                        border-left: 4px solid var(--success-color);
                    }

                    .remediation-title {
                        font-weight: 600;
                        color: var(--success-color);
                        margin-bottom: 8px;
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    }

                    .code-snippet {
                        background: #2d3748;
                        color: #e2e8f0;
                        padding: 15px;
                        border-radius: var(--border-radius);
                        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                        font-size: 0.9rem;
                        line-height: 1.4;
                        white-space: pre-wrap;
                        margin: 15px 0;
                        overflow-x: auto;
                        border: 1px solid #4a5568;
                    }

                    .no-findings {
                        text-align: center;
                        padding: 60px 30px;
                        color: var(--text-color);
                    }

                    .no-findings i {
                        font-size: 3rem;
                        color: var(--border-color);
                        margin-bottom: 20px;
                    }

                    .footer {
                        background: white;
                        border-radius: var(--border-radius);
                        padding: 20px 30px;
                        margin-top: 30px;
                        box-shadow: var(--shadow);
                        text-align: center;
                        color: var(--text-color);
                        font-size: 0.9rem;
                    }

                    .export-buttons {
                        display: flex;
                        gap: 10px;
                        justify-content: center;
                        margin-bottom: 15px;
                    }

                    .export-btn {
                        padding: 8px 16px;
                        border: none;
                        border-radius: var(--border-radius);
                        background: var(--secondary-color);
                        color: white;
                        cursor: pointer;
                        transition: background-color 0.3s;
                        font-size: 0.9rem;
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    }

                    .export-btn:hover {
                        background: #2980b9;
                    }

                    /* Advanced Responsive Design */

                    /* Extra Large Screens (1400px+) */
                    @media (min-width: 1400px) {
                        .container {
                            max-width: 1400px;
                            padding: 30px;
                        }

                        .stats-grid {
                            grid-template-columns: repeat(4, 1fr);
                            gap: 25px;
                        }

                        .stat-card {
                            min-height: 140px;
                            padding: 25px;
                        }

                        .finding {
                            padding: 30px 40px;
                            min-height: 220px;
                        }

                        .header h1 {
                            font-size: 3rem;
                        }

                        .controls {
                            padding: 25px;
                        }

                        .search-box input {
                            padding: 14px 50px 14px 18px;
                            font-size: 16px;
                        }
                    }

                    /* Large Screens (1200px - 1399px) */
                    @media (min-width: 1200px) and (max-width: 1399px) {
                        .container {
                            max-width: 1200px;
                            padding: 25px;
                        }

                        .stats-grid {
                            grid-template-columns: repeat(4, 1fr);
                            gap: 22px;
                        }

                        .stat-card {
                            min-height: 130px;
                            padding: 20px;
                        }

                        .finding {
                            min-height: 210px;
                            padding: 25px 35px;
                        }

                        .header h1 {
                            font-size: 2.8rem;
                        }

                        .controls {
                            padding: 22px;
                        }
                    }

                    /* Medium-Large Screens (992px - 1199px) */
                    @media (min-width: 992px) and (max-width: 1199px) {
                        .container {
                            max-width: 992px;
                            padding: 20px;
                        }

                        .stats-grid {
                            grid-template-columns: repeat(2, 1fr);
                            gap: 18px;
                        }

                        .stat-card {
                            min-height: 120px;
                            padding: 18px;
                        }

                        .finding {
                            min-height: 200px;
                            padding: 22px 28px;
                        }

                        .header h1 {
                            font-size: 2.6rem;
                        }

                        .controls {
                            flex-wrap: wrap;
                            padding: 18px;
                        }

                        .search-box {
                            min-width: 300px;
                        }

                        .filter-btn {
                            padding: 7px 14px;
                            font-size: 11px;
                        }
                    }

                    /* Medium Screens (768px - 991px) - Tablets */
                    @media (min-width: 768px) and (max-width: 991px) {
                        .container {
                            max-width: 100%;
                            padding: 20px 15px;
                        }

                        .header {
                            padding: 25px 20px;
                            margin-bottom: 25px;
                        }

                        .header h1 {
                            font-size: 2.3rem;
                            gap: 12px;
                        }

                        .controls {
                            flex-direction: column;
                            gap: 20px;
                            padding: 18px;
                        }

                        .search-box {
                            min-width: 100%;
                        }

                        .search-box input {
                            padding: 12px 42px 12px 14px;
                            font-size: 15px;
                        }

                        .filter-buttons {
                            justify-content: center;
                            flex-wrap: wrap;
                            gap: 12px;
                        }

                        .filter-btn {
                            padding: 8px 14px;
                            font-size: 11px;
                            min-height: 40px;
                        }

                        .stats-grid {
                            grid-template-columns: repeat(2, 1fr);
                            gap: 15px;
                        }

                        .stat-card {
                            padding: 15px;
                            min-height: 110px;
                        }

                        .stat-number {
                            font-size: 1.8rem;
                        }

                        .severity-breakdown {
                            justify-content: center;
                            gap: 10px;
                        }

                        .severity-stat {
                            padding: 8px 12px;
                            font-size: 0.9rem;
                        }

                        .severity-group {
                            margin-bottom: 25px;
                        }

                        .finding {
                            padding: 20px 15px;
                            min-height: 180px;
                        }

                        .finding-icon {
                            width: 35px;
                            height: 35px;
                            font-size: 1.1rem;
                        }

                        .finding-title {
                            font-size: 1rem;
                        }

                        .control-badge {
                            font-size: 0.75rem;
                            padding: 1px 6px;
                        }

                        .finding-meta {
                            gap: 12px;
                        }

                        .code-snippet {
                            font-size: 0.8rem;
                            padding: 12px;
                        }
                    }

                    /* Small Screens (576px - 767px) - Large Phones */
                    @media (min-width: 576px) and (max-width: 767px) {
                        .container {
                            padding: 15px 10px;
                        }

                        .header {
                            padding: 20px 15px;
                            margin-bottom: 20px;
                        }

                        .header h1 {
                            font-size: 2rem;
                            flex-direction: column;
                            gap: 8px;
                        }

                        .header .subtitle {
                            font-size: 1rem;
                        }

                        .controls {
                            flex-direction: column;
                            padding: 15px;
                            gap: 15px;
                        }

                        .search-box input {
                            padding: 12px 42px 12px 14px;
                            font-size: 16px; /* Prevents zoom on iOS */
                            border-radius: 8px;
                        }

                        .filter-buttons {
                            justify-content: center;
                            gap: 8px;
                        }

                        .filter-btn {
                            padding: 8px 14px;
                            font-size: 11px;
                            border-radius: 18px;
                            min-height: 42px;
                            min-width: 60px;
                        }

                        .summary {
                            padding: 20px 15px;
                            margin-bottom: 20px;
                        }

                        .summary h2 {
                            font-size: 1.3rem;
                            margin-bottom: 15px;
                        }

                        .stats-grid {
                            grid-template-columns: repeat(2, 1fr);
                            gap: 12px;
                        }

                        .stat-card {
                            padding: 14px;
                            min-height: 105px;
                        }

                        .stat-number {
                            font-size: 1.6rem;
                        }

                        .stat-label {
                            font-size: 0.85rem;
                        }

                        .severity-breakdown {
                            justify-content: center;
                            gap: 8px;
                        }

                        .severity-stat {
                            padding: 7px 11px;
                            font-size: 0.85rem;
                            border-radius: 16px;
                        }

                        .severity-header {
                            padding: 16px 20px;
                            font-size: 1.1rem;
                        }

                        .severity-header .count {
                            font-size: 0.85rem;
                            padding: 4px 9px;
                        }

                        .severity-group {
                            margin-bottom: 20px;
                        }

                        .finding {
                            padding: 16px 14px;
                            min-height: 165px;
                        }

                        .finding-header {
                            flex-direction: row;
                            gap: 12px;
                        }

                        .finding-icon {
                            width: 34px;
                            height: 34px;
                            font-size: 1rem;
                        }

                        .finding-title {
                            font-size: 0.95rem;
                            flex-direction: column;
                            align-items: flex-start;
                            gap: 5px;
                        }

                        .control-badge {
                            font-size: 0.72rem;
                            padding: 2px 6px;
                        }

                        .finding-meta {
                            flex-direction: column;
                            gap: 6px;
                            font-size: 0.85rem;
                        }

                        .finding-description {
                            font-size: 0.9rem;
                            margin-bottom: 12px;
                            line-height: 1.5;
                        }

                        .finding-remediation {
                            padding: 12px;
                        }

                        .remediation-title {
                            font-size: 0.9rem;
                            margin-bottom: 6px;
                        }

                        .code-snippet {
                            font-size: 0.75rem;
                            padding: 10px;
                            margin: 10px 0;
                        }

                        .export-buttons {
                            flex-direction: column;
                            gap: 8px;
                        }

                        .export-btn {
                            padding: 12px 18px;
                            font-size: 0.85rem;
                            min-height: 44px;
                        }
                    }

                    /* Extra Small Screens (up to 575px) - Small Phones */
                    @media (max-width: 575px) {
                        .container {
                            padding: 12px 8px;
                        }

                        .header {
                            padding: 18px 12px;
                            margin-bottom: 18px;
                        }

                        .header h1 {
                            font-size: 1.9rem;
                            flex-direction: column;
                            gap: 6px;
                        }

                        .header .subtitle {
                            font-size: 0.95rem;
                        }

                        .controls {
                            padding: 14px;
                            gap: 14px;
                        }

                        .search-box input {
                            padding: 10px 38px 10px 12px;
                            font-size: 16px; /* Prevents zoom on iOS */
                            border-radius: 8px;
                        }

                        .search-box i {
                            right: 12px;
                        }

                        .filter-buttons {
                            gap: 8px;
                            justify-content: center;
                        }

                        .filter-btn {
                            padding: 7px 12px;
                            font-size: 10px;
                            border-radius: 14px;
                            min-height: 40px;
                            min-width: 50px;
                        }

                        .summary {
                            padding: 18px 12px;
                            margin-bottom: 18px;
                        }

                        .summary h2 {
                            font-size: 1.25rem;
                            margin-bottom: 14px;
                        }

                        .stats-grid {
                            grid-template-columns: 1fr;
                            gap: 12px;
                        }

                        .stat-card {
                            padding: 12px;
                            text-align: left;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            min-height: 85px;
                            border-radius: 10px;
                        }

                        .stat-number {
                            font-size: 1.5rem;
                            font-weight: 700;
                        }

                        .stat-label {
                            font-size: 0.8rem;
                            font-weight: 500;
                        }

                        .severity-breakdown {
                            flex-direction: column;
                            gap: 8px;
                        }

                        .severity-stat {
                            padding: 6px 10px;
                            font-size: 0.82rem;
                            border-radius: 14px;
                            justify-content: center;
                        }

                        .severity-header {
                            padding: 14px 16px;
                            font-size: 1.05rem;
                        }

                        .severity-header .count {
                            font-size: 0.8rem;
                            padding: 3px 7px;
                        }

                        .severity-group {
                            margin-bottom: 18px;
                        }

                        .finding {
                            padding: 14px 10px;
                            min-height: 145px;
                        }

                        .finding-header {
                            flex-direction: column;
                            align-items: flex-start;
                            gap: 10px;
                        }

                        .finding-icon {
                            width: 30px;
                            height: 30px;
                            font-size: 0.95rem;
                            align-self: flex-start;
                        }

                        .finding-content {
                            width: 100%;
                        }

                        .finding-title {
                            font-size: 0.92rem;
                            flex-direction: column;
                            align-items: flex-start;
                            gap: 5px;
                        }

                        .control-badge {
                            font-size: 0.68rem;
                            padding: 2px 5px;
                        }

                        .finding-meta {
                            flex-direction: column;
                            gap: 5px;
                            font-size: 0.82rem;
                        }

                        .meta-item {
                            gap: 4px;
                        }

                        .meta-item i {
                            font-size: 0.78rem;
                        }

                        .finding-description {
                            font-size: 0.87rem;
                            margin-bottom: 12px;
                            line-height: 1.45;
                        }

                        .finding-remediation {
                            padding: 12px;
                        }

                        .remediation-title {
                            font-size: 0.87rem;
                            margin-bottom: 6px;
                        }

                        .code-snippet {
                            font-size: 0.72rem;
                            padding: 10px;
                            margin: 10px 0;
                            line-height: 1.35;
                        }

                        .footer {
                            padding: 18px 12px;
                            margin-top: 22px;
                        }

                        .export-buttons {
                            flex-direction: column;
                            gap: 8px;
                        }

                        .export-btn {
                            padding: 10px 14px;
                            font-size: 0.82rem;
                            min-height: 44px;
                        }

                        .footer p {
                            font-size: 0.78rem;
                            line-height: 1.35;
                        }
                    }

                    /* Landscape Orientation Optimizations */
                    @media (max-height: 500px) and (orientation: landscape) {
                        .header {
                            padding: 12px 20px;
                            margin-bottom: 12px;
                        }

                        .header h1 {
                            font-size: 1.6rem;
                            margin-bottom: 6px;
                        }

                        .header .subtitle {
                            font-size: 0.85rem;
                        }

                        .controls {
                            padding: 12px;
                            gap: 12px;
                        }

                        .summary {
                            padding: 16px;
                            margin-bottom: 12px;
                        }

                        .stats-grid {
                            grid-template-columns: repeat(4, 1fr);
                            gap: 10px;
                        }

                        .stat-card {
                            padding: 10px;
                            min-height: 75px;
                        }

                        .stat-number {
                            font-size: 1.3rem;
                        }

                        .severity-breakdown {
                            gap: 6px;
                        }

                        .severity-stat {
                            padding: 5px 9px;
                            font-size: 0.78rem;
                        }

                        .finding {
                            padding: 12px;
                            min-height: 125px;
                        }

                        .findings-list {
                            max-height: 320px;
                        }

                        .filter-btn {
                            padding: 6px 12px;
                            font-size: 10px;
                            min-height: 32px;
                        }
                    }

                    /* High DPI / Retina Display Optimizations */
                    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
                        .finding-icon {
                            border: 0.5px solid rgba(255, 255, 255, 0.1);
                        }

                        .code-snippet {
                            border-width: 0.5px;
                        }

                        .control-badge {
                            border: 0.5px solid rgba(255, 255, 255, 0.2);
                        }
                    }

                    /* Reduced Motion Preferences */
                    @media (prefers-reduced-motion: reduce) {
                        * {
                            animation-duration: 0.01ms !important;
                            animation-iteration-count: 1 !important;
                            transition-duration: 0.01ms !important;
                        }

                        .severity-header .toggle-icon {
                            transition: none;
                        }

                        .findings-list {
                            transition: none;
                        }
                    }

                    /* Dark Mode Support (if system preference) */
                    @media (prefers-color-scheme: dark) {
                        :root {
                            --light-bg: #2d3748;
                            --border-color: #4a5568;
                            --text-color: #e2e8f0;
                        }

                        body {
                            background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
                        }

                        .header, .controls, .summary, .findings-container, .footer {
                            background: #1a202c;
                            color: #e2e8f0;
                        }

                        .search-box input {
                            background: #2d3748;
                            color: #e2e8f0;
                            border-color: #4a5568;
                        }

                        .search-box input:focus {
                            border-color: var(--secondary-color);
                        }

                        .filter-btn {
                            background: #2d3748;
                            color: #e2e8f0;
                        }

                        .stat-card {
                            background: #2d3748;
                        }

                        .finding:hover {
                            background: #2d3748;
                        }

                        .finding-remediation {
                            background: #2d3748;
                        }
                    }

                    /* Touch Device Optimizations */
                    .touch-device .filter-btn {
                        min-height: 44px; /* iOS recommended touch target size */
                        min-width: 44px;
                    }

                    .touch-device .severity-header {
                        min-height: 48px;
                        cursor: pointer;
                        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
                    }

                    .touch-device .export-btn {
                        min-height: 44px;
                        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
                    }

                    /* Focus Management */
                    .filter-btn:focus,
                    .severity-header:focus,
                    .export-btn:focus {
                        outline: 2px solid var(--secondary-color);
                        outline-offset: 2px;
                    }

                    .search-box input:focus {
                        outline: 2px solid var(--secondary-color);
                        outline-offset: 2px;
                    }

                    /* Screen Reader Only Content */
                    .sr-only {
                        position: absolute;
                        width: 1px;
                        height: 1px;
                        padding: 0;
                        margin: -1px;
                        overflow: hidden;
                        clip: rect(0, 0, 0, 0);
                        white-space: nowrap;
                        border: 0;
                    }

                    /* Loading States */
                    .loading {
                        opacity: 0.6;
                        pointer-events: none;
                    }

                    .loading::after {
                        content: '';
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        width: 20px;
                        height: 20px;
                        margin: -10px 0 0 -10px;
                        border: 2px solid var(--border-color);
                        border-top-color: var(--secondary-color);
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                    }

                    @keyframes spin {
                        to { transform: rotate(360deg); }
                    }

                    /* Smooth Scrolling */
                    html {
                        scroll-behavior: smooth;
                    }

                    /* Selection Styling */
                    ::selection {
                        background: var(--secondary-color);
                        color: white;
                    }

                    ::-moz-selection {
                        background: var(--secondary-color);
                        color: white;
                    }

                    /* Enhanced Scrollbar Styling for Webkit Browsers */
                    .findings-list::-webkit-scrollbar {
                        width: 10px;
                    }

                    .findings-list::-webkit-scrollbar-track {
                        background: var(--light-bg);
                        border-radius: 6px;
                        margin: 4px 0;
                    }

                    .findings-list::-webkit-scrollbar-thumb {
                        background: linear-gradient(180deg, var(--secondary-color), var(--border-color));
                        border-radius: 6px;
                        border: 2px solid var(--light-bg);
                    }

                    .findings-list::-webkit-scrollbar-thumb:hover {
                        background: linear-gradient(180deg, #2980b9, var(--text-color));
                    }

                    /* Ultra Small Screens (up to 360px) - Very Small Phones */
                    @media (max-width: 360px) {
                        .container {
                            padding: 8px 4px;
                        }

                        .header {
                            padding: 12px 8px;
                            margin-bottom: 12px;
                        }

                        .header h1 {
                            font-size: 1.6rem;
                        }

                        .controls {
                            padding: 10px;
                            gap: 10px;
                        }

                        .filter-btn {
                            padding: 6px 10px;
                            font-size: 9px;
                            min-height: 36px;
                            min-width: 45px;
                        }

                        .summary {
                            padding: 12px 8px;
                        }

                        .stat-card {
                            padding: 8px;
                            min-height: 75px;
                        }

                        .stat-number {
                            font-size: 1.3rem;
                        }

                        .finding {
                            padding: 10px 6px;
                            min-height: 130px;
                        }

                        .finding-icon {
                            width: 26px;
                            height: 26px;
                            font-size: 0.85rem;
                        }

                        .finding-title {
                            font-size: 0.85rem;
                        }

                        .finding-description {
                            font-size: 0.8rem;
                        }
                    }

                    /* Consistent Box Alignment */
                    .stat-card, .finding {
                        box-sizing: border-box;
                        overflow: hidden;
                    }

                    /* Improved Visual Hierarchy */
                    .finding-header {
                        margin-bottom: auto;
                    }

                    .finding-remediation {
                        margin-top: auto;
                    }

                    /* Ensure proper layout flow */
                    .findings-container > .severity-group {
                        display: block;
                        clear: both;
                        float: none;
                        position: static;
                        transform: none;
                    }

                    .container {
                        overflow: visible;
                    }

                    /* Force proper stacking */
                    .severity-group + .severity-group {
                        margin-top: 0;
                    }

                    /* Print Optimizations */
                    @media print {
                        body {
                            background: white !important;
                            color: black !important;
                        }

                        .container {
                            max-width: none !important;
                            padding: 0 !important;
                        }

                        .controls, .export-buttons {
                            display: none !important;
                        }

                        .findings-list {
                            max-height: none !important;
                            overflow: visible !important;
                        }

                        .finding {
                            break-inside: avoid;
                            page-break-inside: avoid;
                            border: 1px solid #ccc !important;
                            margin-bottom: 10px !important;
                        }

                        .severity-header {
                            break-after: avoid;
                            page-break-after: avoid;
                        }

                        .code-snippet {
                            background: #f5f5f5 !important;
                            color: black !important;
                            border: 1px solid #ccc !important;
                        }

                        .header h1 {
                            color: black !important;
                        }

                        .summary h2 {
                            color: black !important;
                        }

                        /* Ensure all text is black for printing */
                        * {
                            color: black !important;
                            background: white !important;
                        }

                        .critical, .high, .medium, .low {
                            background: white !important;
                            color: black !important;
                            border: 2px solid black !important;
                        }

                        .finding-icon {
                            background: white !important;
                            color: black !important;
                            border: 2px solid black !important;
                        }
                    }

                    /* Accessibility Enhancements */
                    @media (prefers-contrast: high) {
                        :root {
                            --border-color: #000;
                            --text-color: #000;
                        }

                        .finding {
                            border: 2px solid #000;
                        }

                        .filter-btn {
                            border-width: 2px;
                        }

                        .search-box input {
                            border-width: 2px;
                        }
                    }

                    /* Large Text Support */
                    @media (min-resolution: 120dpi) {
                        body {
                            font-size: 18px;
                        }

                        .filter-btn {
                            font-size: 14px;
                            padding: 10px 18px;
                        }

                        .search-box input {
                            font-size: 16px;
                            padding: 14px 50px 14px 18px;
                        }
                    }

                    @media print {
                        body {
                            background: white;
                        }

                        .container {
                            max-width: none;
                            padding: 0;
                        }

                        .controls, .export-buttons {
                            display: none;
                        }

                        .findings-list {
                            max-height: none !important;
                        }

                        .finding {
                            break-inside: avoid;
                            page-break-inside: avoid;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>
                            <i class="fas fa-shield-alt"></i>
                            Security Findings Report
                        </h1>
                        <div class="subtitle">Infrastructure as Code Security Analysis</div>
                    </div>

                    <div class="controls" role="toolbar" aria-label="Search and filter controls">
                        <div class="search-box">
                            <label for="searchInput" class="sr-only">Search findings</label>
                            <input type="text"
                                   id="searchInput"
                                   placeholder="Search findings by description, file, or control ID..."
                                   aria-label="Search findings by description, file, or control ID"
                                   autocomplete="off"
                                   spellcheck="false">
                            <i class="fas fa-search" aria-hidden="true"></i>
                        </div>
                        <div class="filter-buttons" role="group" aria-label="Filter by severity level">
                            <button class="filter-btn all active"
                                    data-severity="all"
                                    aria-pressed="true"
                                    aria-label="Show all findings">
                                <i class="fas fa-list" aria-hidden="true"></i> All
                            </button>
                            <button class="filter-btn critical"
                                    data-severity="critical"
                                    aria-pressed="false"
                                    aria-label="Show only critical severity findings">
                                <span aria-hidden="true">🔴</span> Critical
                            </button>
                            <button class="filter-btn high"
                                    data-severity="high"
                                    aria-pressed="false"
                                    aria-label="Show only high severity findings">
                                <span aria-hidden="true">🟠</span> High
                            </button>
                            <button class="filter-btn medium"
                                    data-severity="medium"
                                    aria-pressed="false"
                                    aria-label="Show only medium severity findings">
                                <span aria-hidden="true">🟡</span> Medium
                            </button>
                            <button class="filter-btn low"
                                    data-severity="low"
                                    aria-pressed="false"
                                    aria-label="Show only low severity findings">
                                <span aria-hidden="true">🔵</span> Low
                            </button>
                        </div>
                    </div>

                    
                <div class="summary">
                    <h2><i class="fas fa-chart-bar"></i> Executive Summary</h2>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">8</div>
                            <div class="stat-label">Total Findings</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">8</div>
                            <div class="stat-label">Files Affected</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">4</div>
                            <div class="stat-label">High Priority Issues</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">8</div>
                            <div class="stat-label">Security Controls</div>
                        </div>
                    </div>
                    <div class="severity-breakdown">
                        <div class="severity-stat critical">
                            <span>🔴</span>
                            <span><strong>2</strong> Critical</span>
                        </div>
                        <div class="severity-stat high">
                            <span>🟠</span>
                            <span><strong>2</strong> High</span>
                        </div>
                        <div class="severity-stat medium">
                            <span>🟡</span>
                            <span><strong>2</strong> Medium</span>
                        </div>
                        <div class="severity-stat low">
                            <span>🔵</span>
                            <span><strong>2</strong> Low</span>
                        </div>
                    </div>
                </div>

                    <div class="findings-container">
                        
                    <section class="severity-group" data-severity="critical" aria-labelledby="severity-critical-header">
                        <h3 class="severity-header critical"
                            id="severity-critical-header"
                            role="button"
                            tabindex="0"
                            aria-expanded="true"
                            aria-controls="severity-critical-list"
                            aria-label="CRITICAL severity findings section, 2 findings">
                            <span><span aria-hidden="true">🔴</span> CRITICAL Severity Findings</span>
                            <span class="count" aria-label="2 findings">2</span>
                            <i class="fas fa-chevron-down toggle-icon" aria-hidden="true"></i>
                        </h3>
                        <div class="findings-list"
                             id="severity-critical-list"
                             role="region"
                             aria-labelledby="severity-critical-header">
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-critical--4828328429530675754"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon critical"
                                         role="img"
                                         aria-label="CRITICAL severity indicator">
                                        <i class="fas fa-exclamation-triangle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-critical--4828328429530675754">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-1">NS-1</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">network/security-groups.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">25</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> Network Security Group allows unrestricted inbound access from the internet on port 22 (SSH). This creates a significant security risk as it exposes SSH services to potential brute force attacks and unauthorized access attempts.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Restrict SSH access to specific IP ranges or use Azure Bastion for secure remote access. Configure NSG rules to allow SSH only from trusted networks or management subnets.</div>
                                </div>
                                <div class="code-snippet">securityRules: [
  {
    name: &#x27;AllowSSH&#x27;
    properties: {
      access: &#x27;Allow&#x27;
      direction: &#x27;Inbound&#x27;
      sourceAddressPrefix: &#x27;*&#x27;
      destinationPortRange: &#x27;22&#x27;
    }
  }
]</div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-critical-5083675575664353620"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon critical"
                                         role="img"
                                         aria-label="CRITICAL severity indicator">
                                        <i class="fas fa-exclamation-triangle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-critical-5083675575664353620">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID DP-3">DP-3</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">storage/storage-account.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">15</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> Storage account is configured to allow public blob access without any network restrictions. This exposes sensitive data to unauthorized access from anywhere on the internet.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Disable public blob access and configure network access rules to restrict access to specific virtual networks or IP ranges. Enable private endpoints for secure access.</div>
                                </div>
                                <div class="code-snippet">properties: {
  allowBlobPublicAccess: true
  networkAcls: {
    defaultAction: &#x27;Allow&#x27;
  }
}</div>
                            </div>
                        </div>
                    </div>
                    <section class="severity-group" data-severity="high" aria-labelledby="severity-high-header">
                        <h3 class="severity-header high"
                            id="severity-high-header"
                            role="button"
                            tabindex="0"
                            aria-expanded="true"
                            aria-controls="severity-high-list"
                            aria-label="HIGH severity findings section, 2 findings">
                            <span><span aria-hidden="true">🟠</span> HIGH Severity Findings</span>
                            <span class="count" aria-label="2 findings">2</span>
                            <i class="fas fa-chevron-down toggle-icon" aria-hidden="true"></i>
                        </h3>
                        <div class="findings-list"
                             id="severity-high-list"
                             role="region"
                             aria-labelledby="severity-high-header">
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high-3157838038052774420"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high-3157838038052774420">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID IM-2">IM-2</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">identity/key-vault.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">8</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> Key Vault is not using Azure Active Directory authentication and relies on access policies instead of RBAC. This limits fine-grained access control and audit capabilities.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Enable Azure AD authentication for Key Vault and migrate from access policies to Azure RBAC for better security and compliance. Configure appropriate RBAC roles for different user groups.</div>
                                </div>
                                <div class="code-snippet">properties: {
  enableRbacAuthorization: false
  accessPolicies: [
    // Legacy access policy configuration
  ]
}</div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high-5101541277283205478"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high-5101541277283205478">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID LT-1">LT-1</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">monitoring/diagnostic-settings.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">12</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> Diagnostic settings are not configured for critical Azure resources, preventing proper security monitoring and compliance auditing.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Configure diagnostic settings for all critical resources to send logs to Log Analytics workspace. Enable security-relevant log categories for threat detection and compliance.</div>
                                </div>
                                <div class="code-snippet">// Missing diagnostic settings configuration
resource storageAccount &#x27;Microsoft.Storage/storageAccounts@2021-04-01&#x27; = {
  name: storageAccountName
  // No diagnostic settings configured
}</div>
                            </div>
                        </div>
                    </div>
                    <section class="severity-group" data-severity="medium" aria-labelledby="severity-medium-header">
                        <h3 class="severity-header medium"
                            id="severity-medium-header"
                            role="button"
                            tabindex="0"
                            aria-expanded="true"
                            aria-controls="severity-medium-list"
                            aria-label="MEDIUM severity findings section, 2 findings">
                            <span><span aria-hidden="true">🟡</span> MEDIUM Severity Findings</span>
                            <span class="count" aria-label="2 findings">2</span>
                            <i class="fas fa-chevron-down toggle-icon" aria-hidden="true"></i>
                        </h3>
                        <div class="findings-list"
                             id="severity-medium-list"
                             role="region"
                             aria-labelledby="severity-medium-header">
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-medium-7486570934661326788"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon medium"
                                         role="img"
                                         aria-label="MEDIUM severity indicator">
                                        <i class="fas fa-exclamation" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-medium-7486570934661326788">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID ES-1">ES-1</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">compute/virtual-machines.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">45</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> Virtual machines are not configured with disk encryption at rest. Unencrypted disks pose a data security risk if physical media is compromised.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Enable Azure Disk Encryption for all VM disks using Azure Key Vault for key management. Configure both OS and data disk encryption.</div>
                                </div>
                                <div class="code-snippet">storageProfile: {
  osDisk: {
    createOption: &#x27;FromImage&#x27;
    managedDisk: {
      storageAccountType: &#x27;Premium_LRS&#x27;
      // Missing encryption configuration
    }
  }
}</div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-medium--5015269261891113431"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon medium"
                                         role="img"
                                         aria-label="MEDIUM severity indicator">
                                        <i class="fas fa-exclamation" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-medium--5015269261891113431">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-3">NS-3</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">network/application-gateway.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">30</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> Application Gateway is not configured with Web Application Firewall (WAF) protection, leaving web applications vulnerable to common attacks.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Enable WAF on Application Gateway with OWASP Core Rule Set. Configure custom rules for application-specific protection and enable prevention mode.</div>
                                </div>
                                <div class="code-snippet">properties: {
  sku: {
    name: &#x27;Standard_v2&#x27;
    tier: &#x27;Standard_v2&#x27;
    // Missing WAF configuration
  }
}</div>
                            </div>
                        </div>
                    </div>
                    <section class="severity-group" data-severity="low" aria-labelledby="severity-low-header">
                        <h3 class="severity-header low"
                            id="severity-low-header"
                            role="button"
                            tabindex="0"
                            aria-expanded="true"
                            aria-controls="severity-low-list"
                            aria-label="LOW severity findings section, 2 findings">
                            <span><span aria-hidden="true">🔵</span> LOW Severity Findings</span>
                            <span class="count" aria-label="2 findings">2</span>
                            <i class="fas fa-chevron-down toggle-icon" aria-hidden="true"></i>
                        </h3>
                        <div class="findings-list"
                             id="severity-low-list"
                             role="region"
                             aria-labelledby="severity-low-header">
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-low-1462541309756621890"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon low"
                                         role="img"
                                         aria-label="LOW severity indicator">
                                        <i class="fas fa-info-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-low-1462541309756621890">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID AC-4">AC-4</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">database/sql-server.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">18</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> SQL Server is configured with SQL authentication enabled alongside Azure AD authentication. This creates additional attack vectors and complexity.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Disable SQL authentication and use only Azure AD authentication for better security and centralized identity management.</div>
                                </div>
                                <div class="code-snippet">properties: {
  administratorLogin: &#x27;sqladmin&#x27;
  administratorLoginPassword: &#x27;P@ssw0rd123!&#x27;
  azureADOnlyAuthentication: false
}</div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-low--581489217471536340"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon low"
                                         role="img"
                                         aria-label="LOW severity indicator">
                                        <i class="fas fa-info-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-low--581489217471536340">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID BC-2">BC-2</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">backup/recovery-vault.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">22</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> Recovery Services Vault does not have cross-region restore enabled, limiting disaster recovery options.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Enable cross-region restore for Recovery Services Vault to ensure business continuity in case of regional outages.</div>
                                </div>
                                <div class="code-snippet">properties: {
  storageModelType: &#x27;LocallyRedundant&#x27;
  crossRegionRestoreFlag: false
}</div>
                            </div>
                        </div>
                    </div>
                        <div id="noFindings" class="no-findings" style="display: none;">
                            <i class="fas fa-search"></i>
                            <h3>No findings match your search criteria</h3>
                            <p>Try adjusting your search terms or filters</p>
                        </div>
                    </div>

                    <div class="footer">
                        <div class="export-buttons">
                            <button class="export-btn" onclick="window.print()">
                                <i class="fas fa-print"></i> Print Report
                            </button>
                            <button class="export-btn" onclick="exportToJson()">
                                <i class="fas fa-download"></i> Export JSON
                            </button>
                        </div>
                        <p>Generated by IaC Guardian GPT• June 16, 2025 at 07:49 PM</p>
                        <p><strong>Disclaimer:</strong> This report is AI-generated and should be reviewed by security professionals.</p>
                    </div>
                </div>

                <script>
                    // Enhanced JavaScript for interactivity and mobile optimization
                    document.addEventListener('DOMContentLoaded', function() {
                        const searchInput = document.getElementById('searchInput');
                        const filterButtons = document.querySelectorAll('.filter-btn');
                        const severityHeaders = document.querySelectorAll('.severity-header');
                        const findings = document.querySelectorAll('.finding');
                        const noFindings = document.getElementById('noFindings');

                        let currentFilter = 'all';
                        let currentSearch = '';
                        let searchTimeout;
                        let isTouch = false;

                        // Detect touch device
                        if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
                            isTouch = true;
                            document.body.classList.add('touch-device');
                        }

                        // Enhanced search functionality with debouncing
                        searchInput.addEventListener('input', function() {
                            clearTimeout(searchTimeout);
                            searchTimeout = setTimeout(() => {
                                currentSearch = this.value.toLowerCase();
                                filterFindings();
                            }, 300); // Debounce for better performance
                        });

                        // Clear search on escape key
                        searchInput.addEventListener('keydown', function(e) {
                            if (e.key === 'Escape') {
                                this.value = '';
                                currentSearch = '';
                                filterFindings();
                            }
                        });

                        // Enhanced filter functionality with keyboard support
                        filterButtons.forEach((button, index) => {
                            button.addEventListener('click', function() {
                                setActiveFilter(this);
                            });

                            // Keyboard navigation for filters
                            button.addEventListener('keydown', function(e) {
                                if (e.key === 'Enter' || e.key === ' ') {
                                    e.preventDefault();
                                    setActiveFilter(this);
                                } else if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                                    e.preventDefault();
                                    const nextButton = filterButtons[index + 1] || filterButtons[0];
                                    nextButton.focus();
                                } else if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                                    e.preventDefault();
                                    const prevButton = filterButtons[index - 1] || filterButtons[filterButtons.length - 1];
                                    prevButton.focus();
                                }
                            });
                        });

                        function setActiveFilter(button) {
                            filterButtons.forEach(btn => {
                                btn.classList.remove('active');
                                btn.setAttribute('aria-pressed', 'false');
                            });
                            button.classList.add('active');
                            button.setAttribute('aria-pressed', 'true');
                            currentFilter = button.dataset.severity;
                            filterFindings();

                            // Announce to screen readers
                            announceToScreenReader(`Filtered to ${currentFilter === 'all' ? 'all findings' : currentFilter + ' severity findings'}`);
                        }

                        // Enhanced collapsible sections with touch support
                        severityHeaders.forEach(header => {
                            let touchStartY = 0;
                            let touchEndY = 0;

                            // Mouse/keyboard events
                            header.addEventListener('click', function(e) {
                                if (!isTouch) {
                                    toggleSection(this);
                                }
                            });

                            header.addEventListener('keydown', function(e) {
                                if (e.key === 'Enter' || e.key === ' ') {
                                    e.preventDefault();
                                    toggleSection(this);
                                }
                            });

                            // Touch events for mobile
                            if (isTouch) {
                                header.addEventListener('touchstart', function(e) {
                                    touchStartY = e.touches[0].clientY;
                                }, { passive: true });

                                header.addEventListener('touchend', function(e) {
                                    touchEndY = e.changedTouches[0].clientY;
                                    const touchDiff = Math.abs(touchStartY - touchEndY);

                                    // Only toggle if it's a tap, not a scroll
                                    if (touchDiff < 10) {
                                        toggleSection(this);
                                    }
                                }, { passive: true });
                            }
                        });

                        function toggleSection(header) {
                            const isCollapsed = header.classList.contains('collapsed');
                            header.classList.toggle('collapsed');
                            const findingsList = header.nextElementSibling;
                            findingsList.classList.toggle('collapsed');

                            // Update ARIA attributes for accessibility
                            header.setAttribute('aria-expanded', !isCollapsed);

                            // Announce to screen readers
                            const severityText = header.textContent.trim();
                            announceToScreenReader(`${severityText} section ${isCollapsed ? 'expanded' : 'collapsed'}`);
                        }

                        // Enhanced filtering with performance optimization
                        function filterFindings() {
                            let visibleCount = 0;
                            const severityGroups = document.querySelectorAll('.severity-group');

                            // Use requestAnimationFrame for smooth UI updates
                            requestAnimationFrame(() => {
                                severityGroups.forEach(group => {
                                    const severity = group.dataset.severity;
                                    const groupFindings = group.querySelectorAll('.finding');
                                    let groupVisibleCount = 0;

                                    groupFindings.forEach(finding => {
                                        const text = finding.textContent.toLowerCase();
                                        const matchesSearch = currentSearch === '' || text.includes(currentSearch);
                                        const matchesFilter = currentFilter === 'all' || severity === currentFilter;

                                        if (matchesSearch && matchesFilter) {
                                            finding.style.display = 'block';
                                            groupVisibleCount++;
                                            visibleCount++;
                                        } else {
                                            finding.style.display = 'none';
                                        }
                                    });

                                    // Show/hide entire severity group
                                    if (groupVisibleCount > 0 && (currentFilter === 'all' || severity === currentFilter)) {
                                        group.style.display = 'block';
                                    } else {
                                        group.style.display = 'none';
                                    }
                                });

                                // Show/hide no findings message
                                if (visibleCount === 0) {
                                    noFindings.style.display = 'block';
                                    announceToScreenReader('No findings match your search criteria');
                                } else {
                                    noFindings.style.display = 'none';
                                    announceToScreenReader(`Showing ${visibleCount} finding${visibleCount !== 1 ? 's' : ''}`);
                                }

                                // Update URL hash for bookmarking (optional)
                                updateUrlHash();
                            });
                        }

                        // Screen reader announcements
                        function announceToScreenReader(message) {
                            const announcement = document.createElement('div');
                            announcement.setAttribute('aria-live', 'polite');
                            announcement.setAttribute('aria-atomic', 'true');
                            announcement.className = 'sr-only';
                            announcement.textContent = message;
                            document.body.appendChild(announcement);

                            setTimeout(() => {
                                document.body.removeChild(announcement);
                            }, 1000);
                        }

                        // URL hash management for bookmarking
                        function updateUrlHash() {
                            const params = new URLSearchParams();
                            if (currentFilter !== 'all') params.set('filter', currentFilter);
                            if (currentSearch) params.set('search', currentSearch);

                            const hash = params.toString();
                            if (hash) {
                                window.history.replaceState(null, null, '#' + hash);
                            } else {
                                window.history.replaceState(null, null, window.location.pathname);
                            }
                        }

                        // Load state from URL hash
                        function loadFromUrlHash() {
                            const hash = window.location.hash.substring(1);
                            if (hash) {
                                const params = new URLSearchParams(hash);
                                const filter = params.get('filter');
                                const search = params.get('search');

                                if (filter && filter !== 'all') {
                                    const filterButton = document.querySelector(`[data-severity="${filter}"]`);
                                    if (filterButton) {
                                        setActiveFilter(filterButton);
                                    }
                                }

                                if (search) {
                                    searchInput.value = search;
                                    currentSearch = search.toLowerCase();
                                    filterFindings();
                                }
                            }
                        }

                        // Keyboard shortcuts
                        document.addEventListener('keydown', function(e) {
                            // Ctrl/Cmd + F to focus search
                            if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                                e.preventDefault();
                                searchInput.focus();
                            }

                            // Escape to clear search and filters
                            if (e.key === 'Escape' && document.activeElement !== searchInput) {
                                searchInput.value = '';
                                currentSearch = '';
                                const allButton = document.querySelector('[data-severity="all"]');
                                if (allButton) setActiveFilter(allButton);
                            }
                        });

                        // Initialize from URL hash
                        loadFromUrlHash();

                        // Handle browser back/forward
                        window.addEventListener('hashchange', loadFromUrlHash);

                        // Performance monitoring (optional)
                        if (window.performance && window.performance.mark) {
                            window.performance.mark('report-interactive');
                        }
                    });

                    function exportToJson() {
                        const findings = [];
                        document.querySelectorAll('.finding').forEach(finding => {
                            const control = finding.querySelector('.control-badge')?.textContent || '';
                            const description = finding.querySelector('.finding-description')?.textContent || '';
                            const remediation = finding.querySelector('.finding-remediation')?.textContent || '';
                            const file = finding.querySelector('.meta-item:nth-child(1)')?.textContent.replace('File: ', '') || '';
                            const line = finding.querySelector('.meta-item:nth-child(2)')?.textContent.replace('Line: ', '') || '';

                            findings.push({
                                control_id: control,
                                description: description.trim(),
                                remediation: remediation.trim(),
                                file_path: file,
                                line: line
                            });
                        });

                        const dataStr = JSON.stringify(findings, null, 2);
                        const dataBlob = new Blob([dataStr], {type: 'application/json'});
                        const url = URL.createObjectURL(dataBlob);
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = 'security_findings.json';
                        link.click();
                        URL.revokeObjectURL(url);
                    }
                </script>
            </body>
            </html>
            