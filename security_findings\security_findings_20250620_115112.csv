File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
Parameters\LacpBillingExhaust.Parameters-LacpBillingExhaust.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'usageAccountSystemAssignedIdentityPrincipalId' flows across template boundary,Secure parameter 'usageAccountSystemAssignedIdentityPrincipalId' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyVaultSuffix' flows across template boundary,Secure parameter 'keyVaultSuffix' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'regionalCosmosAccountLocation' flows across template boundary,Secure parameter 'regionalCosmosAccountLocation' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'dasStorageAccountKey' flows across template boundary,Secure parameter 'dasStorageAccountKey' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'globalKeyVaultName' flows across template boundary,Secure parameter 'globalKeyVaultName' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'createSharedKeyVault' flows across template boundary,Secure parameter 'createSharedKeyVault' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,40.0,"The parameter 'isBoundariesRestricted' is explicitly set to 'false' (Line 041), indicating that network boundary restrictions are disabled. This configuration can enable public network access to Logic Apps and related resources, increasing the risk of initial access by external attackers, lateral movement, and data exfiltration. Attackers could exploit the lack of network controls to access sensitive workflows or data processed by Logic Apps, expanding the blast radius to all resources integrated with these Logic Apps.","Set 'isBoundariesRestricted' to 'true' to enforce network boundary restrictions. Implement private endpoints and disable public network access for all Logic Apps and associated resources. Review and update network security group (NSG) rules to restrict access to only trusted networks, following Azure Security Benchmark NS-2 guidance.",,,,ai_analysis,,Validated
Templates\LacpGeo.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,225.0,"The CosmosDB resource at line 225 has 'publicNetworkAccess' explicitly set to 'Enabled'. This exposes the CosmosDB account to the public internet, allowing attackers to attempt initial access, brute force, or exploit vulnerabilities directly from outside the trusted network. The blast radius includes potential compromise of all data in the CosmosDB account and lateral movement to other resources if credentials or tokens are obtained.",Set 'publicNetworkAccess' to 'Disabled' in the CosmosDB resource configuration to restrict access to private endpoints only. Implement Azure Private Link and ensure only trusted VNets/subnets can access the database. Example: 'publicNetworkAccess': 'Disabled'.,,,,ai_analysis,,Validated
Templates\LacpGeo.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,228.0,"The CosmosDB resource at line 228 has 'isVirtualNetworkFilterEnabled' set to 'false', which disables VNet-based access controls. This allows connections from any network, increasing the attack surface for initial access and lateral movement. Attackers can exploit this to access the database from untrusted networks, increasing the risk of data exfiltration and privilege escalation.","Set 'isVirtualNetworkFilterEnabled' to 'true' to enforce VNet-based access controls. Define 'virtualNetworkRules' to specify allowed subnets. Example: 'isVirtualNetworkFilterEnabled': true, and configure 'virtualNetworkRules' with only trusted subnets.",,,,ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,105.0,"CosmosDB resource 'publicNetworkAccess' is set to 'Enabled' at line 105. This allows public network access to the CosmosDB account, exposing it to the internet and enabling initial access, lateral movement, and data exfiltration attack vectors. Attackers can attempt brute force, exploit misconfigurations, or leverage stolen credentials to access sensitive data, increasing the blast radius to all data in the CosmosDB account.","Set 'publicNetworkAccess' to 'Disabled' in the CosmosDB resource definition at line 105. Additionally, configure private endpoints and restrict access to trusted networks only. Reference: Azure Security Benchmark NS-2.",,,,ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,108.0,"CosmosDB resource 'isVirtualNetworkFilterEnabled' is set to 'false' at line 108. This disables virtual network filtering, allowing connections from any network, including the public internet. Attackers can exploit this to access the database from untrusted networks, increasing the risk of data compromise and lateral movement.",Set 'isVirtualNetworkFilterEnabled' to 'true' in the CosmosDB resource definition at line 108. Define appropriate 'virtualNetworkRules' to restrict access to trusted subnets. Reference: Azure Security Benchmark NS-2.,,,,ai_analysis,,Validated
Templates\LacpRegion.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,561.0,"The CosmosDB resource at line 558 has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false. This configuration allows the CosmosDB account to be accessed from any public network, exposing the database to the internet and enabling initial access, lateral movement, and data exfiltration attack vectors. The blast radius includes all data stored in this CosmosDB account, and attackers could bypass network controls to access sensitive data.","Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to true. Configure 'virtualNetworkRules' to allow access only from trusted VNets and subnets. Deploy a Private Endpoint for CosmosDB to restrict access to private networks only, in accordance with Azure Security Benchmark NS-2.",,,,ai_analysis,,Validated
Templates\LacpRegion.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,561.0,"The CosmosDB resource at line 561 has 'isVirtualNetworkFilterEnabled' set to false, which disables VNet-based access controls. This allows unrestricted access to the database from any network, increasing the risk of unauthorized access, lateral movement, and data exfiltration. The blast radius includes all data in the CosmosDB account.","Set 'isVirtualNetworkFilterEnabled' to true and define 'virtualNetworkRules' to restrict access to only trusted VNets and subnets. This will enforce network boundaries and reduce the attack surface, as required by Azure Security Benchmark NS-2.",,,,ai_analysis,,Validated
Templates\LacpStamp.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,622.0,"The Key Vault resource at line 622 (Microsoft.KeyVault/vaults) does not explicitly restrict public network access or require private endpoints. Without explicit network ACLs or private endpoint configuration, the Key Vault may be accessible from the public internet, enabling attackers to attempt brute force, credential stuffing, or exploit vulnerabilities for initial access. This increases the blast radius to all secrets and keys stored in the vault, potentially compromising all dependent resources.",Restrict public network access to the Key Vault by setting 'properties.networkAcls' to allow only trusted subnets or configuring a private endpoint. Set 'publicNetworkAccess' to 'Disabled' and use Azure Private Link for secure access. Reference: https://docs.microsoft.com/azure/key-vault/general/private-link-service,,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 14,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 6,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T11:51:12.711356,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
