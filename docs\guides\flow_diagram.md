# IaC Security Guardian Logic Flow

```mermaid
flowchart TD
    A[Start Analysis] --> B[Load ARM Template]
    B --> C[Parse Template Parameters]
    
    subgraph Parameter Processing
        C --> D1{Parameter Type?}
        D1 -->|String/Int| D2[Direct Value Assignment]
        D1 -->|Reference| D3[Lookup Referenced Value]
        D1 -->|Expression| D4[Evaluate ARM Expression]
        D2 --> D5[Validated Parameters]
        D3 --> D5
        D4 --> D5
    end
    
    D5 --> E{Review Type}
    
    subgraph Security Review
        E -->|PR| F1[Load PR Changes]
        E -->|Local| F2[Load Local Files]
        F1 --> F3[Extract Resource Configs]
        F2 --> F3
        F3 --> F4{Valid JSON?}
        F4 -->|No| F5[Report Format Error]
        F4 -->|Yes| H[Load Security Benchmarks]
    end
    
    subgraph Control Validation
        H --> I1{Resource Type?}
        I1 -->|Storage| J1[Check Storage Rules]
        I1 -->|Network| J2[Check Network Rules]
        I1 -->|Compute| J3[Check Compute Rules]
        J1 --> K[Aggregate Results]
        J2 --> K
        J3 --> K
    end
    
    subgraph Result Processing
        K --> L{All Checks Pass?}
        L -->|Yes| M1[Log Success]
        L -->|No| M2[Log Violations]
        M1 --> M3[Generate Success Report]
        M2 --> M4[Generate Detailed Findings]
        M3 --> O1[Allow Deployment]
        M4 --> O2[Block Deployment]
    end
    
    O1 --> R[End]
    O2 --> Q[Update Security Controls]
    Q --> A
