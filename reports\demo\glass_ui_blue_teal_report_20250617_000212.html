<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Assessment Report - IaC Guardian</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            /* 🎨 Primary Color Palette (Blue tone) */
            --hue-primary: 223;
            --primary500: hsl(var(--hue-primary), 90%, 50%);
            --primary600: hsl(var(--hue-primary), 90%, 60%);
            --primary700: hsl(var(--hue-primary), 90%, 70%);

            /* 🟢 Secondary Color Palette (Teal tone) */
            --hue-secondary: 178;
            --secondary800: hsl(var(--hue-secondary), 90%, 80%);

            /* 🌑 Dark Grays (used for dark backgrounds) */
            --dark-gray50: hsl(var(--hue-primary), 90%, 5%);
            --dark-gray100: hsl(var(--hue-primary), 90%, 10%);

            /* ⚪ White Transparency Palette (used for glass effects, overlays) */
            --white0: hsla(0, 0%, 100%, 0);
            --white50: hsla(0, 0%, 100%, 0.05);
            --white100: hsla(0, 0%, 100%, 0.1);
            --white200: hsla(0, 0%, 100%, 0.2);
            --white300: hsla(0, 0%, 100%, 0.3);
            --white400: hsla(0, 0%, 100%, 0.4);
            --white500: hsla(0, 0%, 100%, 0.5);
            --white: hsl(0, 0%, 100%);

            /* 🧮 Base font scaling */
            font-size: clamp(0.75rem, 0.65rem + 0.5vw, 1.25rem);

            /* Glass UI Semantic Colors with New Palette */
            --success-green: hsl(142, 76%, 36%);
            --warning-amber: hsl(38, 92%, 50%);
            --danger-red: hsl(0, 84%, 60%);
            --info-cyan: var(--secondary800);

            /* Glass UI Components using White Transparency */
            --glass-white: var(--white200);
            --glass-white-light: var(--white100);
            --glass-white-strong: var(--white400);
            --glass-border: var(--white200);

            /* 📝 Text Color Palette - Optimized for Glass UI */
            --text-primary: var(--white);
            --text-secondary: hsla(0, 0%, 100%, 0.85);
            --text-muted: hsla(0, 0%, 100%, 0.65);
            --text-accent: hsl(var(--hue-secondary), 90%, 85%);
            --text-on-glass: hsla(0, 0%, 100%, 0.95);
            --text-on-dark: var(--white);
            --text-on-light: hsl(var(--hue-primary), 90%, 15%);
            --text-interactive: hsl(var(--hue-primary), 90%, 85%);
            --text-hover: hsl(var(--hue-secondary), 90%, 90%);

            /* Semantic Colors with Glass Effects */
            --critical-glass: hsla(0, 84%, 60%, 0.2);
            --critical-border: hsla(0, 84%, 60%, 0.3);
            --critical-text: hsl(0, 84%, 80%);
            --high-glass: hsla(38, 92%, 50%, 0.2);
            --high-border: hsla(38, 92%, 50%, 0.3);
            --high-text: hsl(38, 92%, 75%);
            --medium-glass: hsla(45, 93%, 47%, 0.2);
            --medium-border: hsla(45, 93%, 47%, 0.3);
            --medium-text: hsl(45, 93%, 75%);
            --low-glass: hsla(var(--hue-secondary), 90%, 80%, 0.2);
            --low-border: hsla(var(--hue-secondary), 90%, 80%, 0.3);
            --low-text: var(--secondary800);

            /* Glass UI Layout */
            --max-width: 1400px;
            --border-radius: 16px;
            --border-radius-sm: 12px;
            --border-radius-lg: 24px;
            --glass-blur: blur(16px);
            --glass-blur-strong: blur(24px);
            --glass-shadow: 0 8px 32px hsla(var(--hue-primary), 90%, 5%, 0.12);
            --glass-shadow-lg: 0 16px 64px hsla(var(--hue-primary), 90%, 5%, 0.16);
            --glass-shadow-xl: 0 24px 96px hsla(var(--hue-primary), 90%, 5%, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-on-glass);
            background: linear-gradient(135deg,
                var(--dark-gray50) 0%,
                var(--dark-gray100) 30%,
                hsl(var(--hue-primary), 60%, 15%) 70%,
                hsl(var(--hue-secondary), 40%, 20%) 100%);
            background-attachment: fixed;
            min-height: 100vh;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, hsla(var(--hue-primary), 90%, 50%, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, hsla(var(--hue-secondary), 90%, 60%, 0.12) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, var(--white50) 0%, transparent 70%);
            pointer-events: none;
            z-index: -1;
        }

        .main-container {
            max-width: var(--max-width);
            margin: 0 auto;
            padding: 2rem 1rem;
            position: relative;
            z-index: 1;
        }

        /* Glass UI Header Section */
        .report-header {
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-lg);
            padding: 3rem 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--glass-shadow-lg);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .report-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary500), var(--primary600), var(--secondary800));
            border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
        }

        .report-header::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
            pointer-events: none;
            border-radius: inherit;
        }

        .report-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-on-glass);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            text-shadow: 0 2px 8px hsla(var(--hue-primary), 90%, 5%, 0.3);
            position: relative;
            z-index: 2;
        }

        .report-subtitle {
            font-size: 1.125rem;
            color: var(--text-accent);
            font-weight: 400;
            margin-bottom: 1rem;
            position: relative;
            z-index: 2;
            text-shadow: 0 1px 4px hsla(var(--hue-primary), 90%, 5%, 0.2);
        }

        .report-meta {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 1.5rem;
            font-size: 0.875rem;
            color: var(--text-interactive);
            position: relative;
            z-index: 2;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: var(--glass-white-light);
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius-sm);
            border: 1px solid var(--glass-border);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }

        /* Glass UI Controls Section */
        .controls-section {
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--glass-shadow);
        }

        .controls-grid {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 1.5rem;
            align-items: center;
        }

        .search-container {
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-sm);
            font-size: 0.875rem;
            transition: all 0.3s ease;
            background: var(--glass-white-light);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            color: var(--text-on-glass);
        }

        .search-input::placeholder {
            color: var(--text-interactive);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary500);
            background: var(--glass-white-strong);
            box-shadow: 0 0 0 3px hsla(var(--hue-primary), 90%, 50%, 0.2);
            transform: translateY(-1px);
            color: var(--text-on-glass);
        }

        .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-interactive);
        }

        .filter-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 0.5rem 1rem;
            border: 1px solid var(--glass-border);
            border-radius: 2rem;
            background: var(--glass-white-light);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            color: var(--text-interactive);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            white-space: nowrap;
        }

        .filter-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--glass-shadow);
            background: var(--glass-white-strong);
            color: var(--text-hover);
        }

        .filter-btn.active {
            color: var(--text-on-dark);
            transform: translateY(-2px);
            box-shadow: var(--glass-shadow-lg);
            border-color: transparent;
        }

        .filter-btn.all.active {
            background: linear-gradient(135deg, var(--primary500), var(--primary600));
        }
        .filter-btn.critical.active {
            background: linear-gradient(135deg, var(--danger-red), var(--critical-text));
        }
        .filter-btn.high.active {
            background: linear-gradient(135deg, var(--warning-amber), var(--high-text));
        }
        .filter-btn.medium.active {
            background: linear-gradient(135deg, hsl(45, 93%, 47%), var(--medium-text));
        }
        .filter-btn.low.active {
            background: linear-gradient(135deg, var(--info-cyan), var(--secondary800));
        }

        /* Multi-select filter enhancements */
        .filter-btn.active {
            position: relative;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .multi-select-info {
            text-align: center;
            margin-top: 0.5rem;
        }

        .filter-summary {
            text-align: center;
            font-weight: 500;
        }

        /* Animation for filter changes */
        .severity-group {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        .severity-group[style*="display: none"] {
            opacity: 0;
            transform: translateY(-10px);
        }

        .finding-item {
            transition: opacity 0.2s ease;
        }

        .finding-item[style*="display: none"] {
            opacity: 0;
        }

        /* Glass UI Summary Section */
        .summary-section {
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--glass-shadow);
        }

        .summary-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-on-glass);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-shadow: 0 2px 8px hsla(var(--hue-primary), 90%, 5%, 0.3);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--glass-white-light);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--glass-shadow-lg);
            background: var(--glass-white-strong);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary500), var(--secondary800));
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        .stat-card::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 50%;
            height: 100%;
            background: radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.06) 0%, transparent 60%);
            pointer-events: none;
            border-radius: inherit;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-on-glass);
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 8px hsla(var(--hue-primary), 90%, 5%, 0.3);
            position: relative;
            z-index: 2;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-accent);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            position: relative;
            z-index: 2;
        }

        .severity-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .severity-badge {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border: 1px solid var(--glass-border);
            position: relative;
            overflow: hidden;
        }

        .severity-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: inherit;
            opacity: 0.1;
            z-index: -1;
        }

        .severity-badge:hover {
            transform: translateY(-2px);
            box-shadow: var(--glass-shadow);
        }

        .severity-badge.critical {
            background: var(--critical-glass);
            border-color: var(--critical-border);
            color: var(--critical-text);
        }

        .severity-badge.high {
            background: var(--high-glass);
            border-color: var(--high-border);
            color: var(--high-text);
        }

        .severity-badge.medium {
            background: var(--medium-glass);
            border-color: var(--medium-border);
            color: var(--medium-text);
        }

        .severity-badge.low {
            background: var(--low-glass);
            border-color: var(--low-border);
            color: var(--low-text);
        }

        .severity-count {
            font-size: 1.25rem;
            font-weight: 700;
            text-shadow: 0 1px 4px hsla(var(--hue-primary), 90%, 5%, 0.2);
        }

        .severity-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* Glass UI Findings Section */
        .findings-container {
            margin-bottom: 2rem;
        }

        .severity-group {
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
            box-shadow: var(--glass-shadow);
            overflow: hidden;
            border: 1px solid var(--glass-border);
            transition: all 0.3s ease;
        }

        .severity-group:hover {
            transform: translateY(-2px);
            box-shadow: var(--glass-shadow-lg);
        }

        .severity-group:last-child {
            margin-bottom: 0;
        }

        /* Glass UI Domain Section Styles */
        .domain-section {
            margin-bottom: 2rem;
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            box-shadow: var(--glass-shadow);
            transition: all 0.3s ease;
        }

        .domain-section:hover {
            transform: translateY(-2px);
            box-shadow: var(--glass-shadow-lg);
        }

        .domain-header {
            background: linear-gradient(135deg, var(--primary500) 0%, var(--secondary800) 100%);
            color: var(--white);
            padding: 1.5rem 2rem;
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            position: relative;
            overflow: hidden;
        }

        .domain-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            border-radius: inherit;
        }

        .domain-header i {
            font-size: 1.5rem;
            position: relative;
            z-index: 2;
        }

        .domain-section .severity-group {
            margin: 0;
            border-radius: 0;
            border: none;
            border-bottom: 1px solid var(--glass-border);
            box-shadow: none;
            background: var(--glass-white-light);
        }

        .domain-section .severity-group:last-child {
            border-bottom: none;
            border-radius: 0 0 var(--border-radius) var(--border-radius);
        }

        .severity-header {
            padding: 1.25rem 1.5rem;
            font-weight: 600;
            font-size: 1.125rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 1px solid var(--glass-border);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }

        .severity-header:hover {
            background: var(--glass-white-strong);
            transform: translateX(4px);
        }

        .severity-header-left {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .severity-icon {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.875rem;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .severity-header.critical {
            background: var(--critical-glass);
            color: var(--critical-text);
            border-left: 4px solid var(--critical-text);
        }

        .severity-header.critical .severity-icon {
            background: linear-gradient(135deg, var(--danger-red), var(--critical-text));
        }

        .severity-header.high {
            background: var(--high-glass);
            color: var(--high-text);
            border-left: 4px solid var(--high-text);
        }

        .severity-header.high .severity-icon {
            background: linear-gradient(135deg, var(--warning-amber), var(--high-text));
        }

        .severity-header.medium {
            background: var(--medium-glass);
            color: var(--medium-text);
            border-left: 4px solid var(--medium-text);
        }

        .severity-header.medium .severity-icon {
            background: linear-gradient(135deg, hsl(45, 93%, 47%), var(--medium-text));
        }

        .severity-header.low {
            background: var(--low-glass);
            color: var(--low-text);
            border-left: 4px solid var(--low-text);
        }

        .severity-header.low .severity-icon {
            background: linear-gradient(135deg, var(--info-cyan), var(--secondary800));
        }

        .severity-title {
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .severity-count {
            background: rgba(255, 255, 255, 0.9);
            color: inherit;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 700;
            margin-left: 0.5rem;
        }

        .toggle-icon {
            transition: transform 0.2s ease;
            color: var(--gray-500);
        }

        .severity-header.collapsed .toggle-icon {
            transform: rotate(-90deg);
        }

        .findings-list {
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .findings-list.collapsed {
            max-height: 0;
        }

        .finding-item {
            border-bottom: 1px solid var(--glass-border);
            padding: 1.5rem;
            transition: all 0.3s ease;
            background: var(--glass-white-light);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }

        .finding-item:last-child {
            border-bottom: none;
        }

        .finding-item:hover {
            background: var(--glass-white-strong);
            transform: translateX(4px);
        }

        .finding-header {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .finding-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
            flex-shrink: 0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
        }

        .finding-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, transparent 60%);
            border-radius: inherit;
        }

        .finding-icon.critical {
            background: linear-gradient(135deg, var(--danger-red), var(--critical-text));
        }
        .finding-icon.high {
            background: linear-gradient(135deg, var(--warning-amber), var(--high-text));
        }
        .finding-icon.medium {
            background: linear-gradient(135deg, hsl(45, 93%, 47%), var(--medium-text));
        }
        .finding-icon.low {
            background: linear-gradient(135deg, var(--info-cyan), var(--secondary800));
        }

        .finding-content {
            flex: 1;
            min-width: 0;
        }

        .finding-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-on-glass);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            flex-wrap: wrap;
            text-shadow: 0 1px 4px hsla(var(--hue-primary), 90%, 5%, 0.2);
        }

        .control-id {
            background: linear-gradient(135deg, var(--primary500), var(--primary600));
            color: var(--white);
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            box-shadow: 0 2px 4px var(--dark-gray50);
        }

        .finding-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1rem;
            font-size: 0.875rem;
            color: var(--text-interactive);
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.375rem;
            background: var(--glass-white-light);
            padding: 0.25rem 0.5rem;
            border-radius: var(--border-radius-sm);
            border: 1px solid var(--glass-border);
            color: var(--text-accent);
        }

        /* Line Number Highlighting - Base Styles */
        .meta-item.line-number {
            color: var(--text-on-dark);
            font-weight: 600;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .meta-item.line-number::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            border-radius: inherit;
        }

        /* Severity-Specific Line Number Colors */
        .finding-item[data-severity="critical"] .meta-item.line-number {
            background: linear-gradient(135deg, var(--danger-red), var(--critical-text));
            border: 1px solid var(--danger-red);
            box-shadow: 0 2px 8px hsla(0, 84%, 60%, 0.4);
        }

        .finding-item[data-severity="high"] .meta-item.line-number {
            background: linear-gradient(135deg, var(--warning-amber), var(--high-text));
            border: 1px solid var(--warning-amber);
            box-shadow: 0 2px 8px hsla(38, 92%, 50%, 0.4);
        }

        .finding-item[data-severity="medium"] .meta-item.line-number {
            background: linear-gradient(135deg, hsl(45, 93%, 47%), var(--medium-text));
            border: 1px solid hsl(45, 93%, 47%);
            box-shadow: 0 2px 8px hsla(45, 93%, 47%, 0.4);
        }

        .finding-item[data-severity="low"] .meta-item.line-number {
            background: linear-gradient(135deg, var(--info-cyan), var(--secondary800));
            border: 1px solid var(--info-cyan);
            box-shadow: 0 2px 8px hsla(var(--hue-secondary), 90%, 80%, 0.4);
        }

        .meta-item.line-number .meta-icon {
            color: var(--text-on-dark);
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
        }

        /* Severity-Specific Line Number Badges */
        .line-number-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            color: var(--text-on-dark);
            padding: 0.125rem 0.5rem;
            border-radius: 1rem;
            font-size: 0.6875rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            position: relative;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        /* Critical Severity Line Numbers - Red Theme */
        .finding-item[data-severity="critical"] .line-number-badge {
            background: linear-gradient(135deg, hsl(0, 84%, 70%), hsl(0, 84%, 80%));
            box-shadow: 0 2px 6px hsla(0, 84%, 60%, 0.5);
            border: 1px solid hsl(0, 84%, 85%);
        }

        /* High Severity Line Numbers - Orange Theme */
        .finding-item[data-severity="high"] .line-number-badge {
            background: linear-gradient(135deg, hsl(38, 92%, 60%), hsl(38, 92%, 70%));
            box-shadow: 0 2px 6px hsla(38, 92%, 50%, 0.5);
            border: 1px solid hsl(38, 92%, 75%);
        }

        /* Medium Severity Line Numbers - Yellow Theme */
        .finding-item[data-severity="medium"] .line-number-badge {
            background: linear-gradient(135deg, hsl(45, 93%, 57%), hsl(45, 93%, 67%));
            box-shadow: 0 2px 6px hsla(45, 93%, 47%, 0.5);
            border: 1px solid hsl(45, 93%, 72%);
        }

        /* Low Severity Line Numbers - Teal Theme */
        .finding-item[data-severity="low"] .line-number-badge {
            background: linear-gradient(135deg, hsl(var(--hue-secondary), 90%, 75%), hsl(var(--hue-secondary), 90%, 85%));
            box-shadow: 0 2px 6px hsla(var(--hue-secondary), 90%, 70%, 0.5);
            border: 1px solid hsl(var(--hue-secondary), 90%, 90%);
        }

        /* Line Number Tooltip */
        .meta-item.line-number:hover::after {
            content: 'Click to jump to line in code editor';
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--dark-gray100);
            color: var(--text-on-dark);
            padding: 0.5rem 0.75rem;
            border-radius: var(--border-radius-sm);
            font-size: 0.75rem;
            white-space: nowrap;
            box-shadow: var(--glass-shadow-lg);
            border: 1px solid var(--glass-border);
            z-index: 1000;
            margin-bottom: 0.5rem;
            opacity: 0;
            animation: tooltip-fade-in 0.3s ease forwards;
        }

        @keyframes tooltip-fade-in {
            from { opacity: 0; transform: translateX(-50%) translateY(5px); }
            to { opacity: 1; transform: translateX(-50%) translateY(0); }
        }

        /* Subtle Professional Hover Effects */
        .finding-item[data-severity="critical"] .meta-item.line-number:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px hsla(0, 84%, 60%, 0.7);
        }

        .finding-item[data-severity="high"] .meta-item.line-number:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px hsla(38, 92%, 50%, 0.7);
        }

        .finding-item[data-severity="medium"] .meta-item.line-number:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px hsla(45, 93%, 47%, 0.7);
        }

        .finding-item[data-severity="low"] .meta-item.line-number:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px hsla(var(--hue-secondary), 90%, 70%, 0.7);
        }

        /* Line Number Copy Functionality */
        .meta-item.line-number {
            cursor: pointer;
            user-select: none;
        }

        .meta-item.line-number:active {
            transform: scale(0.95);
        }

        /* Professional Static Styling - No Animations */
        .finding-item[data-severity="critical"] .line-number-badge {
            box-shadow: 0 3px 8px hsla(0, 84%, 60%, 0.6);
            border: 2px solid hsl(0, 84%, 85%);
        }

        .finding-item[data-severity="high"] .line-number-badge {
            box-shadow: 0 3px 8px hsla(38, 92%, 50%, 0.6);
            border: 2px solid hsl(38, 92%, 75%);
        }

        .finding-item[data-severity="medium"] .line-number-badge {
            box-shadow: 0 2px 6px hsla(45, 93%, 47%, 0.5);
            border: 2px solid hsl(45, 93%, 72%);
        }

        .finding-item[data-severity="low"] .line-number-badge {
            box-shadow: 0 2px 6px hsla(var(--hue-secondary), 90%, 70%, 0.5);
            border: 2px solid hsl(var(--hue-secondary), 90%, 90%);
        }

        .meta-icon {
            color: var(--text-interactive);
            width: 1rem;
        }

        .finding-description {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .remediation-section {
            background: linear-gradient(135deg, var(--success-green) 0%, #059669 100%);
            color: white;
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            margin-top: 1rem;
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border: 1px solid rgba(16, 185, 129, 0.3);
            box-shadow: var(--glass-shadow);
            position: relative;
            overflow: hidden;
        }

        .remediation-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, transparent 60%);
            border-radius: inherit;
        }

        .remediation-title {
            font-weight: 600;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            position: relative;
            z-index: 2;
            color: var(--text-on-dark);
            text-shadow: 0 1px 2px hsla(var(--hue-primary), 90%, 5%, 0.3);
        }

        .remediation-content {
            font-size: 0.875rem;
            line-height: 1.5;
            opacity: 0.95;
            position: relative;
            z-index: 2;
            color: var(--text-on-dark);
        }

        .code-snippet {
            background: hsla(var(--hue-primary), 90%, 5%, 0.9);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            color: var(--text-accent);
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 0.8125rem;
            line-height: 1.5;
            white-space: pre-wrap;
            margin: 1rem 0;
            overflow-x: auto;
            border: 1px solid var(--glass-border);
            box-shadow: var(--glass-shadow);
        }

        .no-findings {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--text-interactive);
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            box-shadow: var(--glass-shadow);
        }

        .no-findings-icon {
            font-size: 3rem;
            color: var(--text-accent);
            margin-bottom: 1rem;
        }

        .no-findings h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-on-glass);
            margin-bottom: 0.5rem;
        }

        /* Glass UI Footer */
        .report-footer {
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-top: 2rem;
            box-shadow: var(--glass-shadow);
            text-align: center;
        }

        .export-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }

        .export-btn {
            padding: 0.75rem 1.5rem;
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-sm);
            background: linear-gradient(135deg, var(--primary500), var(--primary600));
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            color: var(--white);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.875rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            box-shadow: var(--glass-shadow);
            position: relative;
            overflow: hidden;
        }

        .export-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .export-btn:hover::before {
            left: 100%;
        }

        .export-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--glass-shadow-lg);
        }

        .footer-info {
            color: var(--text-interactive);
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .footer-info strong {
            color: var(--text-on-glass);
        }

        /* Glass UI Responsive Design */

        /* Large Desktop (1200px+) - Enhanced Glass Effects */
        @media (min-width: 1200px) {
            .main-container {
                padding: 3rem 2rem;
            }

            .stats-grid {
                grid-template-columns: repeat(4, 1fr);
            }

            .severity-overview {
                grid-template-columns: repeat(4, 1fr);
            }

            /* Enhanced glass blur for larger screens */
            .report-header,
            .controls-section,
            .summary-section,
            .severity-group,
            .domain-section {
                backdrop-filter: var(--glass-blur-strong);
                -webkit-backdrop-filter: var(--glass-blur-strong);
            }
        }

        /* Desktop (992px - 1199px) */
        @media (min-width: 992px) and (max-width: 1199px) {
            .main-container {
                padding: 2rem 1.5rem;
            }

            .report-title {
                font-size: 2.25rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .severity-overview {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Tablet (768px - 991px) */
        @media (min-width: 768px) and (max-width: 991px) {
            .main-container {
                padding: 1.5rem 1rem;
            }

            .report-header {
                padding: 2rem 1.5rem;
            }

            .report-title {
                font-size: 2rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .report-meta {
                flex-direction: column;
                gap: 0.5rem;
            }

            .controls-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .filter-buttons {
                justify-content: center;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .severity-overview {
                grid-template-columns: repeat(2, 1fr);
            }

            .finding-title {
                font-size: 1rem;
            }

            .finding-meta {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        /* Mobile Large (576px - 767px) */
        @media (min-width: 576px) and (max-width: 767px) {
            .main-container {
                padding: 1rem 0.75rem;
            }

            .report-header {
                padding: 1.5rem 1rem;
            }

            .report-title {
                font-size: 1.75rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .report-subtitle {
                font-size: 1rem;
            }

            .report-meta {
                flex-direction: column;
                gap: 0.25rem;
                font-size: 0.8125rem;
            }

            .controls-section {
                padding: 1rem;
            }

            .controls-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .filter-buttons {
                justify-content: center;
                gap: 0.375rem;
            }

            .filter-btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.6875rem;
            }

            .summary-section {
                padding: 1.5rem 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .severity-overview {
                grid-template-columns: 1fr;
            }

            .finding-item {
                padding: 1rem;
            }

            .finding-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.75rem;
            }

            .finding-title {
                font-size: 0.9375rem;
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .export-actions {
                flex-direction: column;
                align-items: center;
            }
        }

        /* Mobile Small (up to 575px) */
        @media (max-width: 575px) {
            .main-container {
                padding: 0.75rem 0.5rem;
            }

            .report-header {
                padding: 1.25rem 0.75rem;
                margin-bottom: 1rem;
            }

            .report-title {
                font-size: 1.5rem;
                flex-direction: column;
                gap: 0.375rem;
            }

            .report-subtitle {
                font-size: 0.9375rem;
            }

            .report-meta {
                flex-direction: column;
                gap: 0.25rem;
                font-size: 0.75rem;
            }

            .controls-section {
                padding: 0.75rem;
            }

            .search-input {
                font-size: 1rem; /* Prevents zoom on iOS */
                padding: 0.75rem 1rem 0.75rem 2.25rem;
            }

            .filter-buttons {
                gap: 0.25rem;
            }

            .filter-btn {
                padding: 0.375rem 0.625rem;
                font-size: 0.625rem;
                min-width: auto;
            }

            .summary-section {
                padding: 1.25rem 0.75rem;
            }

            .summary-title {
                font-size: 1.25rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            .stat-card {
                padding: 1rem;
            }

            .stat-number {
                font-size: 2rem;
            }

            .severity-overview {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .severity-header {
                padding: 1rem;
                font-size: 1rem;
            }

            .finding-item {
                padding: 0.75rem;
            }

            .finding-icon {
                width: 2rem;
                height: 2rem;
                font-size: 0.875rem;
            }

            .finding-title {
                font-size: 0.875rem;
            }

            .control-id {
                font-size: 0.6875rem;
                padding: 0.1875rem 0.5rem;
            }

            .finding-meta {
                font-size: 0.8125rem;
            }

            .finding-description {
                font-size: 0.875rem;
            }

            .code-snippet {
                font-size: 0.75rem;
                padding: 0.75rem;
            }

            .export-actions {
                flex-direction: column;
                gap: 0.5rem;
            }

            .export-btn {
                width: 100%;
                justify-content: center;
            }
        }

        /* Print Styles */
        @media print {
            body {
                background: white !important;
            }

            .main-container {
                max-width: none;
                padding: 0;
            }

            .report-header,
            .controls-section,
            .summary-section,
            .severity-group,
            .report-footer {
                box-shadow: none !important;
                break-inside: avoid;
            }

            .controls-section,
            .export-actions {
                display: none !important;
            }

            .findings-list {
                max-height: none !important;
            }
        }
    </style>

    <script>
        // Modern JavaScript for enhanced interactivity with multi-select filtering
        let searchTimeout;
        let allFindings = [];
        let activeFilters = new Set(['all']); // Support multiple active filters

        document.addEventListener('DOMContentLoaded', function() {
            initializeReport();
            setupEventListeners();
            loadFindings();
        });

        function initializeReport() {
            // Initialize filter buttons with multi-select support
            const filterButtons = document.querySelectorAll('.filter-btn');
            filterButtons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    // Allow Ctrl/Cmd + click for multi-select
                    const isMultiSelect = e.ctrlKey || e.metaKey;
                    toggleFilter(this.dataset.severity, isMultiSelect);
                });
            });

            // Initialize search
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.addEventListener('input', debounceSearch);
            }

            // Initialize collapsible sections
            const severityHeaders = document.querySelectorAll('.severity-header');
            severityHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    toggleSeverityGroup(this);
                });
            });

            // Add instructions for multi-select
            addMultiSelectInstructions();
        }

        function addMultiSelectInstructions() {
            const controlsSection = document.querySelector('.controls-section');
            if (controlsSection) {
                const instructions = document.createElement('div');
                instructions.className = 'multi-select-info';
                instructions.innerHTML = `
                    <small style="color: var(--gray-500); font-size: 0.75rem; margin-top: 0.5rem; display: block;">
                        <i class="fas fa-info-circle"></i>
                        Tip: Hold Ctrl/Cmd and click to select multiple severity levels
                    </small>
                `;
                controlsSection.appendChild(instructions);
            }
        }

        function setupEventListeners() {
            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    clearSearch();
                    resetFilters();
                }
                if (e.ctrlKey && e.key === 'f') {
                    e.preventDefault();
                    focusSearch();
                }
            });
        }

        function debounceSearch() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch();
            }, 300);
        }

        function performSearch() {
            const searchTerm = document.querySelector('.search-input').value.toLowerCase();
            applyFilters(searchTerm);
        }

        function toggleFilter(severity, isMultiSelect = false) {
            if (severity === 'all') {
                // If "All" is clicked, reset to show all
                activeFilters.clear();
                activeFilters.add('all');
            } else {
                if (isMultiSelect) {
                    // Multi-select mode
                    if (activeFilters.has('all')) {
                        activeFilters.clear();
                    }

                    if (activeFilters.has(severity)) {
                        activeFilters.delete(severity);
                    } else {
                        activeFilters.add(severity);
                    }

                    // If no filters selected, default to "all"
                    if (activeFilters.size === 0) {
                        activeFilters.add('all');
                    }
                } else {
                    // Single select mode (default behavior)
                    activeFilters.clear();
                    activeFilters.add(severity);
                }
            }

            updateFilterButtons();
            applyFilters();
            updateUrlHash();
        }

        function updateFilterButtons() {
            const filterButtons = document.querySelectorAll('.filter-btn');
            filterButtons.forEach(btn => {
                const severity = btn.dataset.severity;
                const isActive = activeFilters.has(severity);
                btn.classList.toggle('active', isActive);

                // Add visual indicator for multi-select
                if (activeFilters.size > 1 && !activeFilters.has('all')) {
                    btn.style.position = 'relative';
                    if (isActive && !btn.querySelector('.multi-indicator')) {
                        const indicator = document.createElement('span');
                        indicator.className = 'multi-indicator';
                        indicator.innerHTML = '✓';
                        indicator.style.cssText = `
                            position: absolute;
                            top: -2px;
                            right: -2px;
                            background: var(--success-green);
                            color: white;
                            border-radius: 50%;
                            width: 16px;
                            height: 16px;
                            font-size: 10px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-weight: bold;
                        `;
                        btn.appendChild(indicator);
                    }
                } else {
                    // Remove multi-select indicators
                    const indicator = btn.querySelector('.multi-indicator');
                    if (indicator) {
                        indicator.remove();
                    }
                }
            });
        }

        function applyFilters(searchTerm = '') {
            if (!searchTerm) {
                searchTerm = document.querySelector('.search-input').value.toLowerCase();
            }

            const severityGroups = document.querySelectorAll('.severity-group');
            let totalVisibleCount = 0;

            severityGroups.forEach(group => {
                const groupSeverity = group.dataset.severity;
                const findings = group.querySelectorAll('.finding-item');
                let groupVisibleCount = 0;

                // Check if this severity group should be visible
                const severityMatches = activeFilters.has('all') || activeFilters.has(groupSeverity);

                findings.forEach(finding => {
                    const text = finding.textContent.toLowerCase();
                    const searchMatches = searchTerm === '' || text.includes(searchTerm);
                    const isVisible = severityMatches && searchMatches;

                    finding.style.display = isVisible ? 'block' : 'none';
                    if (isVisible) {
                        groupVisibleCount++;
                        totalVisibleCount++;
                    }
                });

                // Show/hide the entire severity group
                group.style.display = groupVisibleCount > 0 ? 'block' : 'none';
            });

            updateNoResultsMessage(totalVisibleCount === 0);
            updateFilterSummary();
        }

        function updateFilterSummary() {
            // Update or create filter summary
            let summary = document.querySelector('.filter-summary');
            if (!summary) {
                summary = document.createElement('div');
                summary.className = 'filter-summary';
                summary.style.cssText = `
                    margin-top: 0.5rem;
                    padding: 0.5rem;
                    background: var(--gray-100);
                    border-radius: var(--border-radius-sm);
                    font-size: 0.8125rem;
                    color: var(--gray-600);
                `;
                document.querySelector('.controls-section').appendChild(summary);
            }

            if (activeFilters.has('all')) {
                summary.textContent = 'Showing all severity levels';
            } else {
                const filterList = Array.from(activeFilters).map(f => f.charAt(0).toUpperCase() + f.slice(1)).join(', ');
                summary.textContent = `Showing: ${filterList} severity levels`;
            }
        }

        function resetFilters() {
            activeFilters.clear();
            activeFilters.add('all');
            updateFilterButtons();
            applyFilters();
            updateUrlHash();
        }

        function updateUrlHash() {
            const params = new URLSearchParams();
            if (!activeFilters.has('all')) {
                params.set('filters', Array.from(activeFilters).join(','));
            }
            const searchTerm = document.querySelector('.search-input').value;
            if (searchTerm) {
                params.set('search', searchTerm);
            }

            const hash = params.toString();
            if (hash) {
                window.location.hash = hash;
            } else {
                window.history.replaceState(null, null, window.location.pathname);
            }
        }

        function loadFromUrlHash() {
            const hash = window.location.hash.substring(1);
            if (hash) {
                const params = new URLSearchParams(hash);
                const filters = params.get('filters');
                const search = params.get('search');

                if (filters) {
                    activeFilters.clear();
                    filters.split(',').forEach(filter => activeFilters.add(filter.trim()));
                    updateFilterButtons();
                }

                if (search) {
                    document.querySelector('.search-input').value = search;
                }

                applyFilters();
            }
        }

        function toggleSeverityGroup(header) {
            const group = header.parentElement;
            const findingsList = group.querySelector('.findings-list');
            const isCollapsed = header.classList.contains('collapsed');

            if (isCollapsed) {
                header.classList.remove('collapsed');
                findingsList.classList.remove('collapsed');
                findingsList.style.maxHeight = findingsList.scrollHeight + 'px';
            } else {
                header.classList.add('collapsed');
                findingsList.classList.add('collapsed');
                findingsList.style.maxHeight = '0';
            }
        }

        function clearSearch() {
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.value = '';
                applyFilters();
            }
        }

        function focusSearch() {
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }

        function updateNoResultsMessage(show) {
            const noResults = document.querySelector('.no-findings');
            if (noResults) {
                noResults.style.display = show ? 'block' : 'none';
                if (show) {
                    // Update message based on active filters
                    const message = noResults.querySelector('p');
                    if (activeFilters.has('all')) {
                        message.textContent = 'Try adjusting your search terms';
                    } else {
                        const filterList = Array.from(activeFilters).join(', ');
                        message.textContent = `No ${filterList} severity findings match your search. Try different filters or search terms.`;
                    }
                }
            }
        }

        function exportToJson() {
            // Get currently visible findings for export
            const visibleFindings = [];
            document.querySelectorAll('.finding-item').forEach(finding => {
                if (finding.style.display !== 'none') {
                    const severityGroup = finding.closest('.severity-group');
                    const severity = severityGroup ? severityGroup.dataset.severity.toUpperCase() : 'UNKNOWN';
                    const controlId = finding.querySelector('.control-id')?.textContent || 'UNKNOWN';
                    const description = finding.querySelector('.finding-description')?.textContent || '';
                    const remediation = finding.querySelector('.remediation-content')?.textContent || '';
                    const filePath = finding.querySelector('.meta-item:first-child span')?.textContent || '';
                    const lineText = finding.querySelector('.meta-item:last-child span')?.textContent || '';
                    const line = lineText.replace('Line ', '') || '0';

                    visibleFindings.push({
                        control_id: controlId,
                        severity: severity,
                        file_path: filePath,
                        line: parseInt(line) || 0,
                        description: description.trim(),
                        remediation: remediation.trim()
                    });
                }
            });

            const data = {
                timestamp: new Date().toISOString(),
                filters_applied: Array.from(activeFilters),
                total_findings: visibleFindings.length,
                findings: visibleFindings,
                summary: {
                    critical: visibleFindings.filter(f => f.severity === 'CRITICAL').length,
                    high: visibleFindings.filter(f => f.severity === 'HIGH').length,
                    medium: visibleFindings.filter(f => f.severity === 'MEDIUM').length,
                    low: visibleFindings.filter(f => f.severity === 'LOW').length
                }
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], {
                type: 'application/json'
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `security-findings-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function loadFindings() {
            // Initialize findings data - this would be populated with actual findings
            allFindings = [];
        }

        // Glass UI Enhancement Functions
        function addGlassEffects() {
            // Add parallax scrolling effect to background elements
            window.addEventListener('scroll', function() {
                const scrolled = window.pageYOffset;
                const parallax = document.querySelector('body::before');
                if (parallax) {
                    parallax.style.transform = `translateY(${scrolled * 0.5}px)`;
                }
            });

            // Add hover effects to glass cards
            document.querySelectorAll('.stat-card, .severity-badge, .finding-item').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px) scale(1.02)';
                    this.style.boxShadow = 'var(--glass-shadow-xl)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                    this.style.boxShadow = 'var(--glass-shadow)';
                });
            });

            // Add glass ripple effect to buttons
            document.querySelectorAll('.filter-btn, .export-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple');

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        }

        // Line Number Interaction Functions
        function initLineNumberHighlighting() {
            document.querySelectorAll('.meta-item.line-number').forEach(lineItem => {
                lineItem.addEventListener('click', function() {
                    const lineText = this.querySelector('.line-number-badge').textContent;
                    const lineNumber = lineText.replace(/[^0-9]/g, '');
                    const fileName = this.closest('.finding-item').querySelector('.meta-item:first-child span').textContent;

                    // Copy line reference to clipboard
                    const lineReference = `${fileName}:${lineNumber}`;
                    navigator.clipboard.writeText(lineReference).then(() => {
                        showLineNumberFeedback(this, 'Copied to clipboard!');
                    }).catch(() => {
                        showLineNumberFeedback(this, 'Line: ' + lineNumber);
                    });
                });

                // Add hover effect for line numbers
                lineItem.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05) translateY(-2px)';
                });

                lineItem.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1) translateY(0)';
                });
            });
        }

        function showLineNumberFeedback(element, message) {
            const feedback = document.createElement('div');
            feedback.textContent = message;
            feedback.style.cssText = `
                position: absolute;
                top: -40px;
                left: 50%;
                transform: translateX(-50%);
                background: var(--success-green);
                color: white;
                padding: 0.5rem 1rem;
                border-radius: var(--border-radius-sm);
                font-size: 0.75rem;
                font-weight: 600;
                box-shadow: var(--glass-shadow-lg);
                z-index: 1001;
                animation: feedback-bounce 2s ease forwards;
                pointer-events: none;
            `;

            element.style.position = 'relative';
            element.appendChild(feedback);

            setTimeout(() => {
                if (feedback.parentNode) {
                    feedback.parentNode.removeChild(feedback);
                }
            }, 2000);
        }

        // Initialize Glass UI effects
        function initGlassUI() {
            addGlassEffects();
            initLineNumberHighlighting();

            // Add smooth scrolling
            document.documentElement.style.scrollBehavior = 'smooth';

            // Add intersection observer for fade-in animations
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, { threshold: 0.1 });

            document.querySelectorAll('.severity-group, .stat-card').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(el);
            });
        }

        // Initialize from URL hash on page load
        window.addEventListener('load', function() {
            loadFromUrlHash();
            initGlassUI();
        });

        // Handle browser back/forward
        window.addEventListener('hashchange', function() {
            loadFromUrlHash();
        });
    </script>

    <style>
        /* Glass UI Ripple Effect */
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* Professional Static Styling - No Distracting Animations */

        /* Glass UI Print Styles */
        @media print {
            body::before {
                display: none;
            }

            .report-header,
            .controls-section,
            .summary-section,
            .severity-group,
            .domain-section {
                background: white !important;
                backdrop-filter: none !important;
                -webkit-backdrop-filter: none !important;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
            }

            .export-actions {
                display: none;
            }
        }
    </style>
</head>

<body>
    <div class="main-container">
        <!-- Header Section -->
        <header class="report-header">
            <h1 class="report-title">
                <i class="fas fa-shield-alt"></i>
                Security Assessment Report
            </h1>
            <p class="report-subtitle">Infrastructure as Code Security Analysis</p>
            <div class="report-meta">
                <div class="meta-item">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Generated: June 17, 2025 at 12:02 AM</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-robot"></i>
                    <span>IaC Guardian</span>
                </div>
            </div>
        </header>

        <!-- Controls Section -->
        <section class="controls-section">
            <div class="controls-grid">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search findings..." aria-label="Search findings">
                    <i class="fas fa-search search-icon"></i>
                </div>
                <div class="filter-buttons">
                    <button class="filter-btn all active" data-severity="all">All</button>
                    <button class="filter-btn critical" data-severity="critical">Critical</button>
                    <button class="filter-btn high" data-severity="high">High</button>
                    <button class="filter-btn medium" data-severity="medium">Medium</button>
                    <button class="filter-btn low" data-severity="low">Low</button>
                </div>
            </div>
        </section>

        
        <!-- Summary Section -->
        <section class="summary-section">
            <h2 class="summary-title">
                <i class="fas fa-chart-bar"></i>
                Executive Summary
            </h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">8</div>
                    <div class="stat-label">Total Findings</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">8</div>
                    <div class="stat-label">Files Affected</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">4</div>
                    <div class="stat-label">High Priority Issues</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">8</div>
                    <div class="stat-label">Security Controls</div>
                </div>
            </div>
            <div class="severity-overview">
                <div class="severity-badge critical">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        Critical
                    </div>
                    <div class="severity-count">2</div>
                </div>
                <div class="severity-badge high">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        High
                    </div>
                    <div class="severity-count">2</div>
                </div>
                <div class="severity-badge medium">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        Medium
                    </div>
                    <div class="severity-count">2</div>
                </div>
                <div class="severity-badge low">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        Low
                    </div>
                    <div class="severity-count">2</div>
                </div>
            </div>
        </section>

        <div class="findings-container">
            
            <div class="domain-section">
                <h3 class="domain-header">
                    <i class="fas fa-shield-alt"></i>
                    Unknown (8 findings)
                </h3>
                <section class="severity-group" data-severity="critical" data-domain="unknown">
                    <header class="severity-header critical">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">Critical Severity</div>
                            <div class="severity-count">2</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">IM-1</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>identity.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-crosshairs meta-icon"></i>
                                        <span class="line-number-badge">
                                            <i class="fas fa-code"></i>
                                            Line 45
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">Azure Key Vault access policy allows overly broad permissions. The current configuration grants 'all' permissions to service principals, which violates the principle of least privilege and creates significant security risks.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Restrict Key Vault access policies to specific required permissions only. Use role-based access control (RBAC) instead of access policies where possible. Implement just-in-time access for administrative operations.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-2</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>network-security.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-crosshairs meta-icon"></i>
                                        <span class="line-number-badge">
                                            <i class="fas fa-code"></i>
                                            Line 23
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">Network Security Group allows inbound traffic from any source (0.0.0.0/0) on port 22 (SSH). This creates a critical security vulnerability by exposing SSH access to the entire internet.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Restrict SSH access to specific IP ranges or use Azure Bastion for secure remote access. Implement network segmentation and consider using just-in-time (JIT) access for administrative connections.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    </div>
                </section>
                <section class="severity-group" data-severity="high" data-domain="unknown">
                    <header class="severity-header high">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">High Severity</div>
                            <div class="severity-count">2</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item" data-severity="high">
                        <header class="finding-header">
                            <div class="finding-icon high">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">AM-2</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>access-management.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-crosshairs meta-icon"></i>
                                        <span class="line-number-badge">
                                            <i class="fas fa-code"></i>
                                            Line 12
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">Role assignment grants excessive permissions at subscription scope. The 'Contributor' role is assigned when more specific roles would be appropriate for the intended use case.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Use principle of least privilege by assigning specific roles like 'Storage Blob Data Contributor' or 'Virtual Machine Contributor' instead of broad 'Contributor' role. Review and audit role assignments regularly.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="high">
                        <header class="finding-header">
                            <div class="finding-icon high">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-3</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>data-storage.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-crosshairs meta-icon"></i>
                                        <span class="line-number-badge">
                                            <i class="fas fa-code"></i>
                                            Line 67
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">Storage account is configured without encryption at rest using customer-managed keys. Data is encrypted with Microsoft-managed keys only, which may not meet strict compliance requirements.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Configure customer-managed encryption keys (CMK) using Azure Key Vault. Enable infrastructure encryption for additional security layer. Implement key rotation policies.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    </div>
                </section>
                <section class="severity-group" data-severity="medium" data-domain="unknown">
                    <header class="severity-header medium">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">Medium Severity</div>
                            <div class="severity-count">2</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item" data-severity="medium">
                        <header class="finding-header">
                            <div class="finding-icon medium">
                                <i class="fas fa-exclamation"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">LM-1</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>logging-monitoring.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-crosshairs meta-icon"></i>
                                        <span class="line-number-badge">
                                            <i class="fas fa-code"></i>
                                            Line 89
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">Diagnostic settings are not configured for critical Azure resources. This limits visibility into security events and compliance monitoring capabilities.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Enable diagnostic settings for all critical resources. Configure log forwarding to Azure Monitor, Log Analytics workspace, or SIEM solution. Set up appropriate retention policies.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="medium">
                        <header class="finding-header">
                            <div class="finding-icon medium">
                                <i class="fas fa-exclamation"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-4</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>firewall-config.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-crosshairs meta-icon"></i>
                                        <span class="line-number-badge">
                                            <i class="fas fa-code"></i>
                                            Line 156
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">Azure Firewall is not configured with threat intelligence-based filtering. This reduces the effectiveness of network-level threat detection and prevention.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Enable threat intelligence-based filtering on Azure Firewall. Configure appropriate threat intelligence feeds and alerting mechanisms. Regularly update threat intelligence sources.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    </div>
                </section>
                <section class="severity-group" data-severity="low" data-domain="unknown">
                    <header class="severity-header low">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">Low Severity</div>
                            <div class="severity-count">2</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item" data-severity="low">
                        <header class="finding-header">
                            <div class="finding-icon low">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-1</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>database-security.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-crosshairs meta-icon"></i>
                                        <span class="line-number-badge">
                                            <i class="fas fa-code"></i>
                                            Line 34
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">Azure SQL Database is not configured with Advanced Threat Protection. This limits detection of suspicious database activities and potential security threats.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Enable Advanced Threat Protection for Azure SQL Database. Configure appropriate alerting and response procedures for detected threats. Implement database activity monitoring.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="low">
                        <header class="finding-header">
                            <div class="finding-icon low">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">IM-3</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>identity-governance.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-crosshairs meta-icon"></i>
                                        <span class="line-number-badge">
                                            <i class="fas fa-code"></i>
                                            Line 78
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">Privileged Identity Management (PIM) is not configured for administrative roles. This reduces oversight and control over privileged access operations.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Configure Azure AD Privileged Identity Management for all administrative roles. Implement approval workflows and time-limited access for privileged operations.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    </div>
                </section>
            </div>
            <div class="no-findings" style="display: none;">
                <i class="fas fa-search no-findings-icon"></i>
                <h3>No findings match your search criteria</h3>
                <p>Try adjusting your search terms or filters</p>
            </div>
        </div>

        <!-- Footer Section -->
        <footer class="report-footer">
            <div class="export-actions">
                <button class="export-btn" onclick="window.print()">
                    <i class="fas fa-print"></i>
                    Print Report
                </button>
                <button class="export-btn" onclick="exportToJson()">
                    <i class="fas fa-download"></i>
                    Export JSON
                </button>
            </div>
            <div class="footer-info">
                <p><strong>Generated by IaC Guardian</strong> • June 17, 2025 at 12:02 AM</p>
                <p><strong>Disclaimer:</strong> This report is AI-generated and should be reviewed by security professionals.</p>
            </div>
        </footer>
    </div>
</body>
</html>