File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
app-config.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyValues' flows across template boundary,"Secure parameter 'keyValues' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'app_insights_key' flows across template boundary,"Secure parameter 'app_insights_key' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyvault_name' flows across template boundary,"Secure parameter 'keyvault_name' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
keyvault.bicep,NS-1,Network Security,Protect resources using network security groups,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,"Review template dependencies and ensure secure communication between templates

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'secrets' flows across template boundary,"Secure parameter 'secrets' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
storage-accounts.bicep,NS-1,Network Security,Protect resources using network security groups,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,"Review template dependencies and ensure secure communication between templates

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,DP-6,Data Protection,Secure Data with Customer-Managed Keys (CMK),HIGH,1.0,Parameter flow security risk: Sensitive parameters may be exposed through template dependencies,"Use Key Vault references for sensitive parameters and validate parameter flow security

📚 References: [Customer Managed Keys Overview](https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-overview) | [Customer Managed Keys Configure Key Vault](https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-configure-key-vault) | [Transparent Data Encryption Byok Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-byok-overview)

🔵 Azure Guidance: Use customer-managed keys for critical data.",[Customer Managed Keys Overview](https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-overview) | [Customer Managed Keys Configure Key Vault](https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-configure-key-vault) | [Transparent Data Encryption Byok Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-byok-overview),Use customer-managed keys for critical data.,"Configure Key Vault for key management. Rotate keys regularly.
Customer-managed keys: https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-overview
Key Vault CMK setup: https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-configure-key-vault
SQL CMK configuration: https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-byok-overview

This control is implemented through 3 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,parameter_flow,Validated
app-config.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,8.0,"The App Configuration resource 'appConfig' (Microsoft.AppConfiguration/configurationStores) defined at line 8 does not restrict public network access or implement Private Link/service endpoints. By default, App Configuration endpoints are publicly accessible, enabling attackers to attempt initial access, brute force, or enumeration attacks over the public internet. This increases the blast radius by exposing configuration data and potentially sensitive application settings to unauthorized access.","Restrict public network access to the App Configuration resource by enabling Private Link or service endpoints. In the 'properties' block, set 'publicNetworkAccess' to 'Disabled' and configure a 'privateEndpointConnections' property to enforce private access. Review Azure documentation to ensure only required IPs or networks can access the resource.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
function.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,51.0,"The 'allowedOrigins' property under 'cors' (Line 052) configures CORS for the App Service. If 'cors_origins' includes wildcards ('*') or untrusted domains, this enables attackers to make cross-origin requests to the function app, potentially exfiltrating sensitive data or abusing APIs. This increases the blast radius by exposing the app to the public internet without strict access control.","Restrict 'cors_origins' to only trusted, required domains. Remove any wildcard ('*') entries and avoid allowing broad or untrusted origins. Review and update the 'allowedOrigins' array to minimize public exposure, following Azure guidance for secure CORS configuration.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
function.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,106.0,"The 'excludedPaths' property in 'globalValidation' (Line 109) allows unauthenticated access to '/api/config'. This creates an attack vector for initial access, as attackers can probe or exploit this endpoint without authentication, increasing the risk of data exposure or reconnaissance.","Remove '/api/config' from the 'excludedPaths' array to ensure all API endpoints require authentication. Only exclude paths that are explicitly intended to be public and do not expose sensitive information or configuration.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
hub-network.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,14.0,"The subnet 'hub-subnet' defined at line 14 does not have a networkSecurityGroup property assigned. Without a Network Security Group (NSG), there is no network-level access control, enabling attackers to exploit open network paths for lateral movement, initial access, or data exfiltration. The blast radius includes all resources within this subnet, as unrestricted traffic could reach any deployed service.","Assign a networkSecurityGroup to the 'hub-subnet' by adding the 'networkSecurityGroup' property referencing a properly configured NSG resource. Ensure the NSG enforces a default deny-all inbound rule and only allows explicitly required traffic.

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
hub-network.bicep,NS-3,Network Security,Use Network Security Groups (NSGs),CRITICAL,14.0,"The subnet 'hub-subnet' at line 14 lacks an associated Network Security Group (NSG), violating the requirement for deny-by-default network access control. This omission allows all inbound and outbound traffic by default, increasing the risk of unauthorized access and lateral movement within the virtual network.","Create and associate a Network Security Group (NSG) with the 'hub-subnet' by adding the 'networkSecurityGroup' property. Configure the NSG with a default deny-all inbound rule and only permit necessary traffic for business operations.

📚 References: [Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules)

🔵 Azure Guidance: Implement NSGs with deny-by-default rules.",[Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules),Implement NSGs with deny-by-default rules.,"Configure NSGs to deny all inbound traffic by default. Allow only necessary traffic.
NSG rules documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works
Security rules best practices: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
instance-config.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,24.0,"The 'network_config' object at line 24 defines an 'address_space' of '10.0.0.0/8' and a 'subnet' of '10.0.0.0/16', but there is no evidence of Network Security Groups (NSGs) being applied to segment or restrict network traffic. Without NSGs, all resources in this address space are potentially exposed to lateral movement and network-based attacks, increasing the blast radius if any resource is compromised.","Define and associate Network Security Groups (NSGs) with the subnet and/or network interfaces to enforce a default-deny policy and only allow required traffic. Update the template to include NSG resources and link them to the 'network_config.subnet'.

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,4.0,"The 'corpNetIps' variable on line 004 includes the IP range '*******/8', which is an extremely broad public IP range. Allowing such a wide range as a network rule can expose resources to the entire public internet, enabling initial access, lateral movement, and data exfiltration. This configuration significantly increases the attack surface and blast radius, violating the requirement to restrict public access to required IPs only.","Restrict allowed IP ranges to only those that are absolutely necessary and verifiably under organizational control. Remove or replace '*******/8' with specific, trusted corporate IP ranges. Implement Azure Private Link or service endpoints to eliminate public exposure, and ensure only required, minimal IPs are allowed as per NS-2 guidance.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,5.0,"The 'corpNetIps' variable on line 005 includes the IP range '********/8', which is a very large public IP block. Allowing such a broad range can unintentionally permit access from untrusted sources, enabling attackers to reach protected resources, increasing the risk of initial access and lateral movement.","Replace '********/8' with only the specific, trusted IP addresses or ranges required for business operations. Use Azure Private Link or service endpoints to further restrict public access, and review all allowed IPs to ensure they are necessary and controlled.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,6.0,"The 'corpNetIps' variable on line 006 includes the IP range '20.0.0.0/8', which is a massive public IP range. This configuration can allow unauthorized public access, enabling attackers to exploit exposed endpoints, increasing the risk of data exposure and network compromise.","Limit allowed IPs to only those that are strictly required and under organizational control. Remove '20.0.0.0/8' and use more granular, trusted IP ranges. Implement Azure Private Link or service endpoints to eliminate unnecessary public exposure.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,7.0,"The 'corpNetIps' variable on line 007 includes the IP range '40.0.0.0/8', which is a large public IP block. Allowing such a range can expose resources to a wide range of external threats, enabling initial access and increasing the blast radius of potential attacks.","Remove '40.0.0.0/8' from the allowed IPs and replace it with only the specific, trusted IP addresses required. Use Azure Private Link or service endpoints to restrict access and minimize the attack surface.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,8.0,"The 'corpNetIps' variable on line 008 includes the IP range '********/8', which is a broad public IP range. This configuration can allow attackers from a large portion of the internet to access protected resources, increasing the risk of compromise.","Restrict allowed IPs to only those that are necessary and under strict organizational control. Remove '********/8' and use more specific, trusted IP ranges. Consider implementing Azure Private Link or service endpoints to further reduce exposure.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,9.0,"The 'corpNetIps' variable on line 009 includes the IP range '********/8', which is a very large public IP block. Allowing such a range can expose resources to unauthorized access, enabling attack vectors such as initial access and lateral movement.","Remove '********/8' from the allowed IPs and replace it with only the specific, trusted IP addresses required for business operations. Use Azure Private Link or service endpoints to restrict public access.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,10.0,"The 'corpNetIps' variable on line 010 includes the IP range '********/8', which is a broad public IP range. This configuration can allow attackers from a large portion of the internet to access protected resources, increasing the risk of compromise.","Restrict allowed IPs to only those that are necessary and under strict organizational control. Remove '********/8' and use more specific, trusted IP ranges. Consider implementing Azure Private Link or service endpoints to further reduce exposure.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,11.0,"The 'corpNetIps' variable on line 011 includes the IP range '70.0.0.0/8', which is a very large public IP block. Allowing such a range can expose resources to unauthorized access, enabling attack vectors such as initial access and lateral movement.","Remove '70.0.0.0/8' from the allowed IPs and replace it with only the specific, trusted IP addresses required for business operations. Use Azure Private Link or service endpoints to restrict public access.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
keyvault.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,28.0,"The 'networkAcls.defaultAction' property for the Key Vault resource is set to 'Allow' on line 028. This configuration exposes the Key Vault to public network access by default, enabling attackers to attempt unauthorized access from any IP address. The blast radius includes potential exposure of all secrets and keys stored in the Key Vault, risking data exfiltration and privilege escalation if credentials are compromised.","Set 'networkAcls.defaultAction' to 'Deny' to block all public network access by default. Only allow access from explicitly defined IP rules or virtual network rules. Example: 'defaultAction: ""Deny""'. Review and restrict 'ipRules' and 'virtualNetworkRules' to only trusted sources.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
keyvault.bicep,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,22.0,"The 'accessPolicies' property for the Key Vault resource is configured as an empty array on line 037. With 'enableRbacAuthorization' set to true, access is controlled via Azure RBAC, but if RBAC roles are not properly assigned, this can result in either excessive access or denial of legitimate access. Attackers who gain privileged Azure roles could access all secrets, increasing the risk of data exfiltration and privilege escalation.","Review and assign least-privilege Azure RBAC roles to users and service principals that require access to the Key Vault. Regularly audit RBAC assignments to ensure only authorized identities have access. Consider adding explicit 'accessPolicies' if not using RBAC, or ensure RBAC is tightly controlled.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",[Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references),Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.
Key Vault overview: https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Secret management: https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets
App Service Key Vault integration: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
scaleset-networks.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,66.0,"The 'defaultOutboundAccess' property is set to true on the subnet configuration at line 066. This enables default outbound internet access for all resources in the subnet, bypassing network security group (NSG) controls and exposing the subnet to potential external attack vectors. Attackers who compromise a resource in this subnet could exfiltrate data or establish command and control channels, increasing the blast radius and risk of lateral movement.","Set 'defaultOutboundAccess' to false on line 066 and associate a Network Security Group (NSG) with the subnet to explicitly control outbound and inbound traffic. Ensure the NSG has a default deny rule and only allows required traffic. Example: Add an NSG resource and reference it in the subnet's 'networkSecurityGroup' property.

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
server-farms.bicep,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,168.0,"The 'settingValue' property for 'CERTIFICATE_PASSWORD_GENEVACERT' is set to an empty string on line 168. This creates an attack vector where the App Service may expect a certificate password but none is provided, potentially resulting in the certificate being unprotected or the application failing to securely retrieve the secret. If the password is required and not securely referenced from Key Vault, this could lead to sensitive information disclosure or improper secret management. The blast radius includes potential exposure of certificate material and compromise of secure communications.","Store all sensitive secrets, such as certificate passwords, in Azure Key Vault and reference them securely in your App Service configuration. Update line 168 to use a Key Vault reference for the certificate password, and ensure purge protection is enabled on the Key Vault. Do not leave secret values empty or hardcoded in the template. Reference: ASB DP-3.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",[Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references),Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.
Key Vault overview: https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Secret management: https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets
App Service Key Vault integration: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
signalR.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,4.0,"The Microsoft.SignalRService/signalR resource defined at line 004 does not specify any network access controls such as Private Endpoints or IP ACLs. By default, Azure SignalR Service is deployed with a public endpoint, which exposes the service to the internet. This enables initial access attack vectors, allowing unauthenticated or unauthorized users to attempt connections, increasing the risk of data exfiltration, lateral movement, and service abuse. The blast radius includes potential compromise of all data and operations handled by this SignalR instance.","Restrict public access to the SignalR Service by configuring Private Endpoints or service endpoints. Update the resource definition to include 'privateEndpointConnections' or use the 'publicNetworkAccess' property set to 'Disabled'. Additionally, restrict allowed IPs using network ACLs to only trusted sources. Reference: Azure Security Benchmark NS-2.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,168.0,"The 'defaultAction' property in the 'networkAcls' block for the 'storageAccountFunc' resource is set to 'Allow' (Line 031). This configuration allows public network access to the storage account unless explicitly denied by IP or VNet rules. Attackers can exploit this to gain initial access to storage resources from any network not explicitly blocked, increasing the risk of data exfiltration and lateral movement. The blast radius includes all data and services within this storage account.","Set 'networkAcls.defaultAction' to 'Deny' to block all public network access by default. Only allow access from explicitly defined IP addresses or virtual networks. Example: change 'defaultAction: ""Allow""' to 'defaultAction: ""Deny""' on Line 031. Consider implementing Private Endpoints for further reduction of attack surface.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,29.0,"The 'defaultAction' property in the 'networkAcls' block for the 'fuzzStorageProperties' object is set to 'Allow' (Line 065). This configuration, when applied to storage accounts, allows public network access unless explicitly denied, enabling attackers to access storage resources from untrusted networks. This increases the risk of unauthorized data access and broadens the attack surface for initial access and data exfiltration.","Set 'networkAcls.defaultAction' to 'Deny' in the 'fuzzStorageProperties' object (Line 065). This will ensure that only explicitly allowed IP addresses or virtual networks can access the storage account. Example: change 'defaultAction: ""Allow""' to 'defaultAction: ""Deny""'.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,57.0,"The 'fuzzStorageProperties' object, which sets 'networkAcls.defaultAction' to 'Allow', is used for additional storage accounts in the 'storageAccountsCorpus' resource (Line 119). This means all such storage accounts will be publicly accessible unless specifically restricted, enabling attackers to target these accounts for unauthorized access and data exfiltration.","Update the 'fuzzStorageProperties' object to set 'networkAcls.defaultAction' to 'Deny' (see Line 065), ensuring that all storage accounts using this configuration are protected from public network access by default.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 29,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 7,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-18T00:31:32.407143,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
