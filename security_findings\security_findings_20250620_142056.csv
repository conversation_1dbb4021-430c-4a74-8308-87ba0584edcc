File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
Parameters\LacpBillingExhaust.Parameters-LacpBillingExhaust.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'usageAccountSystemAssignedIdentityPrincipalId' flows across template boundary,Secure parameter 'usageAccountSystemAssignedIdentityPrincipalId' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyVaultSuffix' flows across template boundary,Secure parameter 'keyVaultSuffix' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'regionalCosmosAccountLocation' flows across template boundary,Secure parameter 'regionalCosmosAccountLocation' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'dasStorageAccountKey' flows across template boundary,Secure parameter 'dasStorageAccountKey' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'globalKeyVaultName' flows across template boundary,Secure parameter 'globalKeyVaultName' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'createSharedKeyVault' flows across template boundary,Secure parameter 'createSharedKeyVault' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Templates\IngestionStorageAccount.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,44.0,"The storage account resource at line 044 does not include any configuration for private endpoints or explicit disabling of public network access. By default, Azure Storage accounts allow public network access unless explicitly restricted. This enables an initial access attack vector, as attackers can attempt to access the storage account from the public internet, increasing the blast radius to all data stored within the account.","Restrict public network access by setting the 'publicNetworkAccess' property to 'Disabled' and configure a private endpoint for the storage account. This ensures only resources within your private network can access the storage account, mitigating the risk of unauthorized public access.",,,,ai_analysis,,Validated
Templates\LacpBillingExhaust.Template.json,DP-1,Data Protection,Discover classify and label sensitive data,MEDIUM,155.0,"The 'scriptContent' property at line 143 creates a table 'ClientRaw' with columns such as 'EventId', 'SubjectId', and 'CustomerId', which may contain sensitive or personally identifiable information. There is no evidence of data classification, labeling, or inventory of sensitive data as required by Azure Security Benchmark DP-1. This omission increases the risk of unmonitored sensitive data exposure and impairs the ability to apply appropriate data protection controls.",Implement data discovery and classification for the 'ClientRaw' table and other data assets using Azure Purview or Azure Information Protection. Apply sensitivity labels and maintain an inventory of sensitive data to ensure compliance and enable downstream protection and monitoring.,,,,ai_analysis,,Validated
Templates\LacpGeo.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,225.0,"The CosmosDB resource 'publicNetworkAccess' property is set to 'Enabled' on line 225. This allows the database account to be accessed from the public internet, enabling initial access attack vectors and increasing the blast radius for data exfiltration and lateral movement. Attackers can attempt brute force, credential stuffing, or exploit vulnerabilities over the public endpoint.",Set 'publicNetworkAccess' to 'Disabled' to restrict access to the CosmosDB account to only private endpoints. Implement Azure Private Link and configure virtual network rules to ensure only trusted networks can access the database. Reference: NS-2 (Secure cloud services with network controls).,,,,ai_analysis,,Validated
Templates\LacpGeo.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,228.0,"The CosmosDB resource 'isVirtualNetworkFilterEnabled' property is set to 'false' on line 228. This disables virtual network filtering, allowing connections from any network, including the public internet, which enables lateral movement and increases the risk of unauthorized access to sensitive data.",Set 'isVirtualNetworkFilterEnabled' to 'true' and define 'virtualNetworkRules' to restrict access to only trusted subnets. This enforces network boundaries and reduces the attack surface. Reference: NS-2 (Secure cloud services with network controls).,,,,ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,105.0,"CosmosDB resource 'publicNetworkAccess' is set to 'Enabled' on line 105. This allows public network access to the CosmosDB account, exposing it to the internet and enabling initial access, lateral movement, and data exfiltration by attackers. The blast radius includes all data in the CosmosDB account and any dependent applications.","Set 'publicNetworkAccess' to 'Disabled' in the CosmosDB resource definition to restrict access to private endpoints only. Implement Azure Private Link and ensure only trusted networks can access the database. Example: ""publicNetworkAccess"": ""Disabled"".",,,,ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,108.0,"CosmosDB resource 'isVirtualNetworkFilterEnabled' is set to 'false' on line 108. This disables virtual network filtering, allowing unrestricted access from any network, which increases the risk of unauthorized access, lateral movement, and data exfiltration. The blast radius includes all data in the CosmosDB account.","Set 'isVirtualNetworkFilterEnabled' to 'true' and define appropriate 'virtualNetworkRules' to restrict access to trusted VNets only. Example: ""isVirtualNetworkFilterEnabled"": true.",,,,ai_analysis,,Validated
Templates\LacpRegion.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,561.0,"The CosmosDB account resource at line 558 has 'publicNetworkAccess' explicitly set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false. This exposes the CosmosDB account to the public internet, allowing attackers to attempt direct access, brute force, or exploit vulnerabilities, significantly increasing the risk of data exfiltration and lateral movement. The blast radius includes all data in the CosmosDB account and any downstream services relying on it.","Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to true in the CosmosDB account properties. Configure 'virtualNetworkRules' to allow only trusted subnets. Deploy a Private Endpoint for CosmosDB to restrict access to private networks only, following Azure guidance for NS-2.",,,,ai_analysis,,Validated
Templates\LacpRegion.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,561.0,"The CosmosDB account resource at line 561 has 'isVirtualNetworkFilterEnabled' set to false, which disables all virtual network restrictions. This allows any IP address to access the database if public network access is enabled, enabling initial access and lateral movement for attackers. The blast radius includes all data in the CosmosDB account.","Set 'isVirtualNetworkFilterEnabled' to true and define 'virtualNetworkRules' to restrict access to only trusted subnets. This will ensure that only resources within approved VNets can access the CosmosDB account, in line with Azure Security Benchmark NS-2.",,,,ai_analysis,,Validated
Templates\RoleAssignment.Template.json,IM-2,Identity Management,Protect identity and authentication systems,HIGH,5.0,"The 'roleDefinitionId' property on line 72 assigns the built-in 'Contributor' role to the principal specified by 'Ev2BuildoutServicePrincipalId'. Assigning Contributor at the subscription level to a service principal enables broad permissions, including resource creation, modification, and deletion. If this service principal is compromised, an attacker could escalate privileges, move laterally, and impact all resources within the subscription, significantly increasing the blast radius.","Restrict the 'Contributor' role assignment to the minimum required scope (resource group or specific resources) and use least privilege roles. Require strong authentication (MFA) for the service principal, monitor its activity, and regularly review role assignments. Implement Privileged Identity Management (PIM) for just-in-time access. Reference: ASB IM-2.",,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 15,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 6,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T14:20:56.813945,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
