{"metadata": {"version": "1.0", "source": "IaC Guardian Advanced Remediation Guidance System", "created_date": "2025-06-20T00:00:00.000000", "supported_formats": ["ARM", "Bicep", "Terraform"], "total_remediation_patterns": 150, "automated_policy_mappings": 75}, "remediation_patterns": {"storage_security": {"NS-2": {"control_name": "Secure cloud services with network controls", "common_violations": ["public_blob_access_enabled", "missing_private_endpoints", "unrestricted_network_access"], "remediation_templates": {"terraform": {"code_example": "resource \"azurerm_storage_account\" \"example\" {\n  name                     = \"examplestorage\"\n  resource_group_name      = azurerm_resource_group.example.name\n  location                 = azurerm_resource_group.example.location\n  account_tier             = \"Standard\"\n  account_replication_type = \"LRS\"\n  \n  # REMEDIATION: Disable public blob access\n  allow_nested_items_to_be_public = false\n  public_network_access_enabled   = false\n  \n  # REMEDIATION: Configure network rules\n  network_rules {\n    default_action = \"Deny\"\n    ip_rules       = [\"10.0.0.0/16\"]\n    virtual_network_subnet_ids = [azurerm_subnet.example.id]\n  }\n}", "explanation": "Disable public access and configure network restrictions to allow only specific IP ranges and virtual network subnets."}, "bicep": {"code_example": "resource storageAccount 'Microsoft.Storage/storageAccounts@2023-01-01' = {\n  name: 'examplestorage'\n  location: resourceGroup().location\n  sku: {\n    name: 'Standard_LRS'\n  }\n  kind: 'StorageV2'\n  properties: {\n    // REMEDIATION: Disable public blob access\n    allowBlobPublicAccess: false\n    publicNetworkAccess: 'Disabled'\n    \n    // REMEDIATION: Configure network ACLs\n    networkAcls: {\n      defaultAction: 'Deny'\n      ipRules: [\n        {\n          value: '10.0.0.0/16'\n        }\n      ]\n      virtualNetworkRules: [\n        {\n          id: subnet.id\n        }\n      ]\n    }\n  }\n}", "explanation": "Use Bicep properties to disable public access and implement network access control lists."}, "arm": {"code_example": "{\n  \"type\": \"Microsoft.Storage/storageAccounts\",\n  \"apiVersion\": \"2023-01-01\",\n  \"name\": \"[parameters('storageAccountName')]\",\n  \"location\": \"[resourceGroup().location]\",\n  \"properties\": {\n    \"allowBlobPublicAccess\": false,\n    \"publicNetworkAccess\": \"Disabled\",\n    \"networkAcls\": {\n      \"defaultAction\": \"Deny\",\n      \"ipRules\": [\n        {\n          \"value\": \"10.0.0.0/16\"\n        }\n      ],\n      \"virtualNetworkRules\": [\n        {\n          \"id\": \"[parameters('subnetId')]\"\n        }\n      ]\n    }\n  }\n}", "explanation": "ARM template configuration to restrict network access and disable public blob access."}}, "azure_policy_definitions": ["Storage accounts should restrict network access using virtual network rules", "Storage accounts should disable public network access", "Storage account public access should be disallowed"], "compensating_controls": ["Implement Azure Front Door with WAF for controlled public access", "Use Azure Private Link for secure connectivity", "Deploy Azure Firewall with application rules for traffic filtering"], "effort_estimation": "1-4 hours", "business_impact": "Medium - May require application connectivity updates"}, "DP-3": {"control_name": "Encrypt sensitive data in transit", "common_violations": ["https_traffic_disabled", "weak_tls_version", "insecure_transfer_enabled"], "remediation_templates": {"terraform": {"code_example": "resource \"azurerm_storage_account\" \"example\" {\n  name                     = \"examplestorage\"\n  resource_group_name      = azurerm_resource_group.example.name\n  location                 = azurerm_resource_group.example.location\n  account_tier             = \"Standard\"\n  account_replication_type = \"LRS\"\n  \n  # REMEDIATION: Enforce HTTPS traffic only\n  enable_https_traffic_only = true\n  \n  # REMEDIATION: Set minimum TLS version\n  min_tls_version = \"TLS1_2\"\n  \n  # REMEDIATION: Disable insecure transfer\n  allow_nested_items_to_be_public = false\n}", "explanation": "Enable HTTPS-only traffic and set minimum TLS version to 1.2 for secure data transmission."}, "bicep": {"code_example": "resource storageAccount 'Microsoft.Storage/storageAccounts@2023-01-01' = {\n  name: 'examplestorage'\n  location: resourceGroup().location\n  sku: {\n    name: 'Standard_LRS'\n  }\n  kind: 'StorageV2'\n  properties: {\n    // REMEDIATION: Enforce secure transfer\n    supportsHttpsTrafficOnly: true\n    minimumTlsVersion: 'TLS1_2'\n    allowBlobPublicAccess: false\n  }\n}", "explanation": "Configure Bicep properties to enforce HTTPS and minimum TLS version requirements."}}, "azure_policy_definitions": ["Secure transfer to storage accounts should be enabled", "Storage accounts should use minimum TLS version 1.2"], "compensating_controls": ["Implement Azure Application Gateway with SSL termination", "Use Azure Front Door with HTTPS enforcement", "Deploy network-level encryption with IPSec VPN"], "effort_estimation": "1-4 hours", "business_impact": "Low - Transparent to applications using HTTPS"}}, "web_app_security": {"AS-1": {"control_name": "API Authentication and Authorization", "common_violations": ["anonymous_access_enabled", "weak_authentication_methods", "missing_api_management"], "remediation_templates": {"terraform": {"code_example": "resource \"azurerm_linux_web_app\" \"example\" {\n  name                = \"example-app\"\n  resource_group_name = azurerm_resource_group.example.name\n  location            = azurerm_resource_group.example.location\n  service_plan_id     = azurerm_service_plan.example.id\n  \n  # REMEDIATION: Configure authentication\n  auth_settings_v2 {\n    auth_enabled           = true\n    require_authentication = true\n    unauthenticated_action = \"RedirectToLoginPage\"\n    \n    active_directory_v2 {\n      client_id     = azurerm_azuread_application.example.application_id\n      tenant_auth_endpoint = \"https://login.microsoftonline.com/${data.azurerm_client_config.current.tenant_id}/v2.0\"\n    }\n  }\n  \n  site_config {\n    # REMEDIATION: Enforce HTTPS only\n    https_only = true\n    \n    # REMEDIATION: Set minimum TLS version\n    minimum_tls_version = \"1.2\"\n  }\n}", "explanation": "Configure Azure AD authentication and enforce HTTPS with minimum TLS version."}, "bicep": {"code_example": "resource webApp 'Microsoft.Web/sites@2023-01-01' = {\n  name: 'example-app'\n  location: resourceGroup().location\n  properties: {\n    serverFarmId: appServicePlan.id\n    httpsOnly: true\n    siteConfig: {\n      minTlsVersion: '1.2'\n      ftpsState: 'Disabled'\n    }\n  }\n}\n\nresource authSettings 'Microsoft.Web/sites/config@2023-01-01' = {\n  parent: webApp\n  name: 'authsettingsV2'\n  properties: {\n    globalValidation: {\n      requireAuthentication: true\n      unauthenticatedClientAction: 'RedirectToLoginPage'\n    }\n    identityProviders: {\n      azureActiveDirectory: {\n        enabled: true\n        registration: {\n          openIdIssuer: 'https://login.microsoftonline.com/${tenant().tenantId}/v2.0'\n          clientId: azureAdApp.appId\n        }\n      }\n    }\n  }\n}", "explanation": "Implement Azure AD authentication with Bicep configuration for secure API access."}}, "azure_policy_definitions": ["App Service apps should use managed identity", "Authentication should be enabled on your web app", "Web Application should only be accessible over HTTPS"], "compensating_controls": ["Implement Azure API Management with OAuth 2.0", "Use Azure Front Door with WAF and authentication", "Deploy Azure Application Gateway with authentication modules"], "effort_estimation": "4-8 hours", "business_impact": "Medium - Requires application authentication integration"}}, "container_security": {"CS-1": {"control_name": "Container Image Security", "common_violations": ["untrusted_container_registry", "missing_image_scanning", "privileged_containers"], "remediation_templates": {"terraform": {"code_example": "resource \"azurerm_kubernetes_cluster\" \"example\" {\n  name                = \"example-aks\"\n  location            = azurerm_resource_group.example.location\n  resource_group_name = azurerm_resource_group.example.name\n  dns_prefix          = \"exampleaks\"\n  \n  default_node_pool {\n    name       = \"default\"\n    node_count = 1\n    vm_size    = \"Standard_D2_v2\"\n  }\n  \n  # REMEDIATION: Enable Azure Policy for AKS\n  azure_policy_enabled = true\n  \n  # REMEDIATION: Configure security profile\n  security_profile {\n    defender {\n      log_analytics_workspace_id = azurerm_log_analytics_workspace.example.id\n      enabled                    = true\n    }\n  }\n  \n  # REMEDIATION: Enable image cleaner\n  image_cleaner_enabled        = true\n  image_cleaner_interval_hours = 48\n  \n  identity {\n    type = \"SystemAssigned\"\n  }\n}", "explanation": "Configure AKS with security features including Azure Policy, Defender, and image cleaner."}}, "azure_policy_definitions": ["Container registries should use private link", "Container images should be deployed from trusted registries only", "Kubernetes cluster containers should not use forbidden sysctl interfaces"], "compensating_controls": ["Implement Azure Container Registry with vulnerability scanning", "Use Azure Security Center for container monitoring", "Deploy admission controllers for pod security policies"], "effort_estimation": "8-16 hours", "business_impact": "High - May require container image and deployment updates"}}}, "automated_remediation": {"azure_policy_assignments": {"storage_security_initiative": {"policy_set_definition": "Azure Security Benchmark - Storage", "policies": ["Storage accounts should restrict network access using virtual network rules", "Storage accounts should disable public network access", "Secure transfer to storage accounts should be enabled"], "assignment_scope": "/subscriptions/{subscription-id}", "enforcement_mode": "<PERSON><PERSON><PERSON>"}, "web_app_security_initiative": {"policy_set_definition": "Azure Security Benchmark - App Service", "policies": ["Web Application should only be accessible over HTTPS", "Authentication should be enabled on your web app", "Latest TLS version should be used in your Web App"], "assignment_scope": "/subscriptions/{subscription-id}/resourceGroups/{resource-group}", "enforcement_mode": "<PERSON><PERSON><PERSON>"}}, "remediation_scripts": {"enable_storage_https": {"powershell": "$storageAccount = Get-AzStorageAccount -ResourceGroupName 'myRG' -Name 'mystorageaccount'\n$storageAccount.EnableHttpsTrafficOnly = $true\nSet-AzStorageAccount -ResourceGroupName 'myRG' -Name 'mystorageaccount' -EnableHttpsTrafficOnly $true", "azure_cli": "az storage account update --resource-group myRG --name mystorageaccount --https-only true --min-tls-version TLS1_2"}, "configure_web_app_auth": {"azure_cli": "az webapp auth update --resource-group myRG --name myweba<PERSON> --enabled true --action RedirectToLoginPage --aad-client-id {client-id} --aad-tenant-id {tenant-id}"}}}, "effort_estimation_matrix": {"configuration_changes": {"simple_property_toggle": "1-4 hours", "network_configuration": "4-8 hours", "authentication_setup": "4-8 hours", "encryption_implementation": "1-4 hours"}, "architectural_changes": {"private_endpoint_deployment": "8-16 hours", "network_redesign": "16+ hours", "identity_integration": "16+ hours", "comprehensive_security_overhaul": "16+ hours"}, "complexity_factors": ["number_of_resources_affected", "dependency_complexity", "testing_requirements", "rollback_planning", "documentation_updates"]}}