#!/usr/bin/env python3
"""
Test script to generate HTML report with improved line number visibility
"""

import sys
import os
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from security_opt import SecurityPRReviewer

def create_test_findings():
    """Create test security findings to demonstrate line number visibility"""
    return [
        {
            'file_path': 'demo_files/storage_demo.bicep',
            'line_number': 15,
            'severity': 'critical',
            'title': 'Storage Account Public Access Enabled',
            'description': 'Storage account allows public blob access which poses security risks.',
            'recommendation': 'Set allowBlobPublicAccess to false to prevent unauthorized access.',
            'control_id': 'ST-1',
            'benchmark': 'Azure Security Benchmark v3.0'
        },
        {
            'file_path': 'demo_files/network_demo.tf',
            'line_number': 8,
            'severity': 'high',
            'title': 'Network Security Group Rule Too Permissive',
            'description': 'NSG rule allows traffic from any source (0.0.0.0/0).',
            'recommendation': 'Restrict source IP ranges to specific networks or IP addresses.',
            'control_id': 'NS-2',
            'benchmark': 'Azure Security Benchmark v3.0'
        },
        {
            'file_path': 'demo_files/storage_demo.bicep',
            'line_number': 25,
            'severity': 'medium',
            'title': 'Storage Account Encryption Not Configured',
            'description': 'Storage account does not have customer-managed encryption keys configured.',
            'recommendation': 'Configure customer-managed encryption keys for enhanced security.',
            'control_id': 'DP-5',
            'benchmark': 'Azure Security Benchmark v3.0'
        },
        {
            'file_path': 'demo_files/network_demo.tf',
            'line_number': 35,
            'severity': 'low',
            'title': 'Resource Tags Missing',
            'description': 'Resource does not have required tags for governance and cost management.',
            'recommendation': 'Add required tags including Environment, Owner, and CostCenter.',
            'control_id': 'GV-1',
            'benchmark': 'Azure Security Benchmark v3.0'
        }
    ]

def main():
    """Generate test HTML report with improved line numbers"""
    print("🔧 Generating HTML report with improved line number visibility...")
    
    # Create analyzer instance for local analysis
    analyzer = SecurityPRReviewer(local_folder="demo_files")

    # Create test findings
    findings = create_test_findings()

    # Generate timestamp for filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Generate HTML report
    output_file = f"improved_line_numbers_report_{timestamp}.html"

    try:
        # Use the analyzer's HTML export method
        analyzer._export_findings_to_html(findings, output_file)
        
        print(f"✅ HTML report generated: {output_file}")
        print(f"📁 File size: {os.path.getsize(output_file) / 1024:.1f} KB")
        
        # Open in browser for immediate testing
        import webbrowser
        file_url = f"file:///{os.path.abspath(output_file)}"
        webbrowser.open(file_url)
        print(f"🌐 Opened in browser: {file_url}")
        
        print("\n🔍 Test Instructions:")
        print("1. Click on any 'VIEW CODE' button in the report")
        print("2. Check if line numbers are clearly visible in the code dialog")
        print("3. Verify that highlighted line numbers stand out")
        print("4. Test hover effects on line numbers")
        print("5. Check responsive design on mobile/tablet sizes")
        
        return True
        
    except Exception as e:
        print(f"❌ Error generating report: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
