ASB ID,Control Domain,Recommendation,Security Principle,Azure Guidance,Implementation and additional context,Customer Security Stakeholders,Azure Policy Mapping
IM-1,Identity Management,Use centralized identity and authentication system,Use a centralized identity and authentication system to govern your organization's identities and authentications for cloud and non-cloud resources.,"Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applications to Azure AD when technically feasible.","Enhanced Implementation Context:
• Azure AD tenancy concepts: https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps
• Create and configure Azure AD: https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant
• Define Azure AD tenants: https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/
• External identity providers: https://docs.microsoft.com/azure/active-directory/b2b/identity-providers
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 6.7, 12.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8
• PCI-DSS v3.2.1: 7.2, 8.3

Azure Policy Examples:
• An Azure Active Directory administrator should be provisioned for SQL servers
• Service Fabric clusters should only use Azure Active Directory for client authentication
• Standardize identity provider across all applications and services",Identity and Key Management; Security Architecture; Application Security,RequireAADAuth;StandardizeIdentityProvider
IM-2,Identity Management,Protect identity and authentication systems,Secure your identity and authentication system as a high priority. Restrict privileged roles and accounts require strong authentication for privileged access and monitor high-risk activities.,"Use Azure AD security baseline and Identity Secure Score to evaluate security posture. Implement limited administrative roles user risk policies MFA for administrative roles and block legacy authentication.","Enhanced Implementation Context:
• Azure AD Identity Secure Score: https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score
• Active Directory security best practices: https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory
• Azure AD security baseline: https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline
• Privileged Identity Management: https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 5.4, 6.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8, SI-4
• PCI-DSS v3.2.1: 8.2, 8.3

Azure Policy Examples:
• No applicable built-in policy (requires configuration-based implementation)
• Use Azure AD Identity Secure Score recommendations
• Implement Azure AD security baseline configurations
• Monitor privileged account activities through Azure AD logs",Identity and Key Management; Security Architecture; Posture Management,ImplementSecurityBaseline;MonitorPrivilegedAccounts;UseIdentitySecureScore
IM-3,Identity Management,Manage application identities securely and automatically,Use managed application identities instead of creating human accounts for applications. Managed identities provide benefits such as reducing credential exposure and automating credential rotation.,"Use Azure managed identities for services supporting Azure AD authentication. For services without managed identity support use Azure AD service principals with certificate credentials and restricted permissions.","Enhanced Implementation Context:
• Azure managed identities overview: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview
• Services supporting managed identities: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities
• Azure service principal creation: https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps
• Service principal with certificates: https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: Not specified
• NIST SP800-53 r4: AC-2, AC-3, IA-4, IA-5, IA-9
• PCI-DSS v3.2.1: Not applicable

Azure Policy Examples:
• Managed identity should be used in your Function App
• Managed identity should be used in your Web App
• Service principals should be used to protect your subscriptions instead of management certificates
• Managed identity should be used in your API App
• Virtual machines' Guest Configuration extension should be deployed with system-assigned managed identity",Identity and Key Management; Application Security,EnableManagedIdentity;RequireServicePrincipals;EliminateHardcodedCredentials
IM-4,Identity Management,Authenticate server and services,Authenticate remote servers and services from your client side to ensure you are connecting to trusted servers and services using protocols like TLS.,"Enable TLS authentication for Azure services. Ensure client applications verify server/service identity by validating certificates issued by trusted certificate authorities during handshake.","Enhanced Implementation Context:
• TLS enforcement for storage accounts: https://docs.microsoft.com/azure/storage/common/transport-layer-security-configure-minimum-version?tabs=portal#use-azure-policy-to-enforce-the-minimum-tls-version
• Azure service TLS configuration: https://docs.microsoft.com/azure/security/fundamentals/tls-certificate-changes
• Certificate management best practices: https://docs.microsoft.com/azure/security/fundamentals/certificate-management
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: Not specified
• NIST SP800-53 r4: IA-9
• PCI-DSS v3.2.1: Not specified

Azure Policy Examples:
• No applicable built-in policy (requires service-specific TLS configuration)
• Secure transfer to storage accounts should be enabled
• Latest TLS version should be used in your Web App
• Latest TLS version should be used in your Function App
• Latest TLS version should be used in your API App",Identity and Key Management; Application Security,EnforceTLS;ValidateServerCertificates;RequireSecureTransfer
IM-5,Identity Management,Use single sign-on (SSO) for application access,Use single sign-on to simplify user experience for authenticating to resources including applications and data across cloud and on-premises environments.,"Use Azure AD SSO for workload application access eliminating need for multiple accounts. Support enterprise identities external user identities and public users through Azure AD SSO.","Enhanced Implementation Context:
• Azure AD SSO overview: https://docs.microsoft.com/azure/active-directory/manage-apps/what-is-single-sign-on
• SSO deployment planning: https://docs.microsoft.com/azure/active-directory/manage-apps/plan-sso-deployment
• Application integration with Azure AD: https://docs.microsoft.com/azure/active-directory/manage-apps/add-application-portal
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 12.5
• NIST SP800-53 r4: IA-4, IA-2, IA-8
• PCI-DSS v3.2.1: Not specified

Azure Policy Examples:
• No applicable built-in policy (requires application configuration)
• Implement SSO for all enterprise applications
• Configure Azure AD as identity provider for all applications
• Monitor SSO usage and authentication patterns
• Eliminate standalone application authentication systems",Security Architecture; Identity and Key Management; Application Security,ImplementSSO;EliminateMultipleAccounts;CentralizeAuthentication
IM-6,Identity Management,Use strong authentication controls,Enforce strong authentication controls (passwordless authentication or multi-factor authentication) with centralized identity management for all access to resources.,"Use Azure AD passwordless authentication as default method (Windows Hello Microsoft Authenticator FIDO2 Keys). Enable Azure MFA for all users with conditional access policies. Block legacy authentication.","Enhanced Implementation Context:
• Azure MFA deployment: https://docs.microsoft.com/azure/active-directory/authentication/howto-mfa-getstarted
• Passwordless authentication options: https://docs.microsoft.com/azure/active-directory/authentication/concept-authentication-passwordless
• Azure AD password policies: https://docs.microsoft.com/azure/active-directory/authentication/concept-sspr-policy#password-policies-that-only-apply-to-cloud-user-accounts
• Azure AD Password Protection: https://docs.microsoft.com/azure/active-directory/authentication/concept-password-ban-bad
• Block legacy authentication: https://docs.microsoft.com/azure/active-directory/conditional-access/block-legacy-authentication

Compliance Mappings:
• CIS Controls v8: 6.3, 6.4
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-5, IA-8
• PCI-DSS v3.2.1: 7.2, 8.2, 8.3, 8.4

Azure Policy Examples:
• Authentication to Linux machines should require SSH keys
• MFA should be enabled on accounts with write permissions on your subscription
• MFA should be enabled on accounts with owner permissions on your subscription
• MFA should be enabled on accounts with read permissions on your subscription",Security Architecture; Identity and Key Management; Application Security,RequireMFA;EnablePasswordless;BlockLegacyAuth;EnforceStrongAuth
IM-7,Identity Management,Restrict resource access based on conditions,Explicitly validate trusted signals to allow or deny user access to resources as part of a zero-trust access model including strong authentication behavioral analytics device trustworthiness and locations.,"Use Azure AD Conditional Access for granular access controls based on user-defined conditions. Implement MFA for administrative roles Azure management tasks and risky sign-in behaviors. Require trusted locations and managed devices.","Enhanced Implementation Context:
• Azure Conditional Access overview: https://docs.microsoft.com/azure/active-directory/conditional-access/overview
• Common Conditional Access policies: https://docs.microsoft.com/azure/active-directory/conditional-access/concept-conditional-access-policy-common
• Conditional Access insights and reporting: https://docs.microsoft.com/azure/active-directory/conditional-access/howto-conditional-access-insights-reporting
• Authentication session management: https://docs.microsoft.com/azure/active-directory/conditional-access/howto-conditional-access-session-lifetime
• Zero Trust architecture: https://docs.microsoft.com/security/zero-trust/

Compliance Mappings:
• CIS Controls v8: 3.3, 6.4, 13.5
• NIST SP800-53 r4: AC-2, AC-3, AC-6
• PCI-DSS v3.2.1: 7.2

Azure Policy Examples:
• No applicable built-in policy (requires Conditional Access configuration)
• Implement risk-based Conditional Access policies
• Require MFA for administrative roles and Azure management
• Block access from untrusted locations and devices
• Monitor and audit Conditional Access policy effectiveness",Identity and Key Management; Application Security; Threat Intelligence,EnableConditionalAccess;ImplementZeroTrust;RequireDeviceTrust;ValidateTrustedSignals
IM-8,Identity Management,Restrict the exposure of credential and secrets,Ensure application developers securely handle credentials and secrets by avoiding embedding them in code using key vault services and scanning for credentials in source code.,"Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.","Enhanced Implementation Context:
• Azure DevOps Credential Scanner setup: https://secdevtools.azurewebsites.net/helpcredscan.html
• GitHub secret scanning: https://docs.github.com/github/administering-a-repository/about-secret-scanning
• Azure Key Vault developer guide: https://docs.microsoft.com/azure/key-vault/general/developers-guide
• Secure coding practices: https://docs.microsoft.com/azure/security/develop/secure-dev-overview
• Application security guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops

Compliance Mappings:
• CIS Controls v8: 16.9, 16.12
• NIST SP800-53 r4: IA-5
• PCI-DSS v3.2.1: 3.5, 6.3, 8.2

Azure Policy Examples:
• No applicable built-in policy (requires development process implementation)
• Implement credential scanning in CI/CD pipelines
• Enforce Azure Key Vault usage for secret storage
• Monitor and audit secret access patterns
• Require managed identities for Azure service authentication",Application Security; Posture Management,RequireKeyVault;ImplementCredentialScanning;EliminateHardcodedSecrets;UseSecureSDLC
IM-9,Identity Management,Secure user access to existing applications,In hybrid environments with on-premises or non-native cloud applications using legacy authentication consider solutions like CASB application proxy and SSO to govern access.,"Protect legacy applications using Azure AD Application Proxy for on-premises apps with SSO and Conditional Access. Use Microsoft Cloud App Security (MCAS) as CASB for monitoring and controlling user sessions.","Enhanced Implementation Context:
• Azure AD Application Proxy: https://docs.microsoft.com/azure/active-directory/manage-apps/application-proxy#what-is-application-proxy
• Microsoft Cloud App Security best practices: https://docs.microsoft.com/cloud-app-security/best-practices
• Azure AD secure hybrid access: https://docs.microsoft.com/azure/active-directory/manage-apps/secure-hybrid-access
• Legacy application modernization: https://docs.microsoft.com/azure/active-directory/manage-apps/migration-resources
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 6.7, 12.5
• NIST SP800-53 r4: AC-2, AC-3, SC-11
• PCI-DSS v3.2.1: Not specified

Azure Policy Examples:
• No applicable built-in policy (requires application-specific configuration)
• Implement Azure AD Application Proxy for on-premises applications
• Configure MCAS policies for cloud application monitoring
• Enforce SSO for all legacy applications where possible
• Monitor and audit legacy application access patterns",Security Architecture; Infrastructure and Endpoint Security; Application Security,ImplementAppProxy;EnableCASB;SecureLegacyApps;ModernizeAuthentication
