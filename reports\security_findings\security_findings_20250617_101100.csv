Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P1-Identity-HIGH,Identity Management,HIGH,IM-1,template.json,180,App Service 'onefuzz-daily-ui' does not explicitly integrate with Azure Active Directory for authentication and access management.,"Enable Azure Active Directory authentication for the App Service to ensure secure identity management, in compliance with ASB IM-1.",N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-2,template.json,180,App Service 'onefuzz-daily-ui' does not enforce Multi-Factor Authentication (MFA) for user or administrator access.,"Configure Azure Active Directory authentication with MFA enforcement for all users and administrators accessing the App Service, in compliance with ASB IM-2.",N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,template.json,180,"App Service 'onefuzz-daily-ui' is not protected by network security groups (NSGs) or Azure Firewall, and public access is allowed.","Protect the App Service with NSGs or Azure Firewall and restrict public access, in compliance with ASB NS-1.",N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,54,"App Service 'onefuzz-daily-ui' exposes a public endpoint ('onefuzz-daily-ui.azurewebsites.net') with 'sslState' set to 'Disabled', allowing unencrypted public access.","Enable SSL for all public endpoints by setting 'sslState' to 'SniEnabled' or 'IpBasedEnabled' and ensure 'httpsOnly' is true, in compliance with ASB NS-2.",N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,62,"App Service 'onefuzz-daily-ui' exposes a public SCM endpoint ('onefuzz-daily-ui.scm.azurewebsites.net') with 'sslState' set to 'Disabled', allowing unencrypted public access.","Enable SSL for all public SCM endpoints by setting 'sslState' to 'SniEnabled' or 'IpBasedEnabled' and ensure 'httpsOnly' is true, in compliance with ASB NS-2.",N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,180,"App Service 'onefuzz-daily-ui' allows public network access ('publicNetworkAccess': 'Enabled') in 'siteConfig', exposing the app to the internet.","Set 'publicNetworkAccess' to 'Disabled' in 'siteConfig' to restrict public access and use private endpoints or access restrictions, in compliance with ASB NS-2.",N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,180,"App Service 'onefuzz-daily-ui' has 'ipSecurityRestrictions' allowing 'Any' IP address with 'action': 'Allow', which exposes the app to the public internet.","Restrict 'ipSecurityRestrictions' to only allow trusted IP ranges and remove 'Allow all' rules, in compliance with ASB NS-2.",N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,180,"App Service 'onefuzz-daily-ui' has 'scmIpSecurityRestrictions' allowing 'Any' IP address with 'action': 'Allow', which exposes the SCM endpoint to the public internet.","Restrict 'scmIpSecurityRestrictions' to only allow trusted IP ranges and remove 'Allow all' rules, in compliance with ASB NS-2.",N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-3,template.json,180,App Service 'onefuzz-daily-ui' does not implement Network Security Groups (NSGs) to control inbound and outbound traffic.,"Apply NSGs to the subnet hosting the App Service Environment or use access restrictions to control traffic, in compliance with ASB NS-3.",N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,180,"App Service 'onefuzz-daily-ui' does not use private endpoints ('publicNetworkAccess': 'Enabled'), increasing exposure to public networks.","Implement private endpoints for the App Service and set 'publicNetworkAccess' to 'Disabled' to restrict access, in compliance with ASB NS-5.",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,180,App Service 'onefuzz-daily-ui' does not specify use of customer-managed keys (CMK) or explicit encryption at rest settings.,"Configure App Service to use encryption at rest with customer-managed keys if handling sensitive data, in compliance with ASB DP-1.",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,54,"App Service 'onefuzz-daily-ui' has 'sslState' set to 'Disabled' for hostName 'onefuzz-daily-ui.azurewebsites.net', which means encryption in transit (TLS) is not enforced for this endpoint.","Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates to ensure TLS is enforced for all endpoints, in compliance with ASB DP-2.",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,62,"App Service 'onefuzz-daily-ui' has 'sslState' set to 'Disabled' for hostName 'onefuzz-daily-ui.scm.azurewebsites.net', which means encryption in transit (TLS) is not enforced for the SCM (deployment) endpoint.","Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all SCM hostNameSslStates to ensure TLS is enforced for all endpoints, in compliance with ASB DP-2.",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,180,App Service 'onefuzz-daily-ui' does not reference Azure Key Vault for application secrets or sensitive configuration.,"Store all application secrets and sensitive configuration in Azure Key Vault and reference them securely in the App Service configuration, in compliance with ASB DP-3.",N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-6,template.json,180,App Service 'onefuzz-daily-ui' does not specify use of customer-managed keys (CMK) for encryption.,"Configure App Service to use customer-managed keys for encryption at rest if required by your data protection policy, in compliance with ASB DP-6.",N/A,AI,Generic
