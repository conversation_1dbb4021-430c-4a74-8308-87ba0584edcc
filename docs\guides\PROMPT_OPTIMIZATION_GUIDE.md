# Prompt Optimization Guide for Consistent Security Analysis

## Overview

This guide explains the prompt optimizations implemented in `security_opt.py` to ensure consistent and reproducible security analysis results when running the same Infrastructure-as-Code files multiple times.

## Problem Statement

The original implementation could produce different security findings when analyzing the same file multiple times due to:

1. **Non-deterministic AI responses** - Default temperature settings allowed randomness
2. **Inconsistent control selection** - Controls were not processed in deterministic order
3. **Subjective severity mapping** - Severity levels were left to AI interpretation
4. **Variable prompt structure** - Context preparation was not standardized
5. **Ambiguous analysis instructions** - Lack of explicit consistency requirements

## Optimization Solutions

### 1. Deterministic API Configuration

**Before:**
```python
# temperature=0.0, # Commented out - allowed randomness
# max_tokens=2000, # Commented out (deprecated parameter for O model)
response_format={"type": "json_object"}
```

**After:**
```python
temperature=0.0,  # Ensure deterministic responses
max_completion_tokens=3000,  # Sufficient for detailed analysis (updated for Azure OpenAI O model)
response_format={"type": "json_object"},
seed=42  # Additional consistency measure
```

### 2. Optimized System Prompt

**Key Improvements:**
- **Explicit consistency requirements** - "Your analysis must be consistent and reproducible"
- **Deterministic methodology** - Step-by-step analysis process
- **Strict severity criteria** - Clear rules for CRITICAL/HIGH/MEDIUM/LOW assignment
- **Ordered processing** - "Parse systematically (top-to-bottom, resource-by-resource)"

### 3. Structured User Context

**Before:**
```
You are an Azure Security Expert. Analyze the following...
File Path: example.json
Resource Types: Storage, Network
```

**After:**
```
SECURITY ANALYSIS REQUEST

=== TEMPLATE METADATA ===
File Path: example.json
Resource Types: Network, Storage  # Sorted for consistency
Template Format: JSON

=== TEMPLATE CONTENT TO ANALYZE ===
[content]

=== AZURE SECURITY BENCHMARK CONTROLS TO APPLY ===
Total Controls Provided: 15
Resource Types Covered: Network, Storage
```

### 4. Deterministic Control Processing

**Improvements:**
- Controls sorted by ID for consistent ordering
- Keywords sorted alphabetically
- Critical controls added in deterministic order
- Consistent control presentation format

### 5. Severity Indicators

Added `_get_control_severity_indicators()` method that provides explicit severity guidance:

```python
def _get_control_severity_indicators(self, control: Dict) -> str:
    # Analyzes control characteristics to provide consistent severity mapping
    # Returns indicators like "CRITICAL: Exposes critical security vulnerabilities"
```

## Configuration Options

### Environment Variables

```bash
# Enable/disable optimized prompts (default: true)
USE_OPTIMIZED_PROMPTS=true

# Set analysis seed for reproducibility (default: 42)
ANALYSIS_SEED=42
```

### Usage Examples

```python
# Initialize with optimized prompts (default)
reviewer = SecurityPRReviewer(local_folder="./templates")

# Or disable optimization for backward compatibility
os.environ['USE_OPTIMIZED_PROMPTS'] = 'false'
reviewer = SecurityPRReviewer(local_folder="./templates")
```

## Expected Results

### Before Optimization
Running the same file twice might produce:
- **Run 1:** 5 findings (2 HIGH, 3 MEDIUM)
- **Run 2:** 7 findings (1 CRITICAL, 3 HIGH, 3 MEDIUM)

### After Optimization
Running the same file multiple times produces:
- **Run 1:** 6 findings (1 CRITICAL, 2 HIGH, 3 MEDIUM)
- **Run 2:** 6 findings (1 CRITICAL, 2 HIGH, 3 MEDIUM) ✅ Identical
- **Run 3:** 6 findings (1 CRITICAL, 2 HIGH, 3 MEDIUM) ✅ Identical

## Validation Steps

To verify consistency improvements:

1. **Run the same analysis multiple times:**
   ```bash
   python security_opt.py --local-folder ./test-templates --output-format json
   ```

2. **Compare results:**
   ```bash
   # Should produce identical findings
   diff security_findings_run1.json security_findings_run2.json
   ```

3. **Check deterministic ordering:**
   - Controls should appear in same order
   - Findings should have consistent line numbers
   - Severity assignments should be identical

## Technical Implementation Details

### Control Selection Algorithm
1. Sort all controls by ID for deterministic processing
2. Match controls to resource types using sorted keywords
3. Add critical controls in alphabetical order
4. Supplement with general controls if needed (deterministic selection)

### Prompt Structure
1. **Metadata Section** - Consistent template information
2. **Content Section** - Raw template content
3. **Controls Section** - Sorted and formatted controls with severity indicators
4. **Instructions Section** - Explicit step-by-step analysis requirements

### API Call Optimization
1. **Temperature: 0.0** - Eliminates randomness
2. **Seed Parameter** - Additional consistency measure
3. **Structured JSON Output** - Enforces consistent response format
4. **Increased Token Limit** - Prevents truncation of detailed analysis

## Backward Compatibility

The optimization is controlled by the `USE_OPTIMIZED_PROMPTS` environment variable:
- **Default: true** - Uses optimized prompts for new deployments
- **Set to false** - Maintains original behavior for existing workflows

## Monitoring and Validation

### Logging Enhancements
- Added consistency validation logging
- Control selection process tracking
- Prompt optimization status reporting

### Metrics to Monitor
- **Consistency Rate** - Percentage of identical results across runs
- **Finding Stability** - Variance in finding counts
- **Severity Distribution** - Consistency of severity assignments

## Best Practices

1. **Always use optimized prompts** for production deployments
2. **Set a consistent analysis seed** across environments
3. **Monitor consistency metrics** to detect regressions
4. **Validate findings** with multiple runs during testing
5. **Document any customizations** to prompt structure

## Troubleshooting

### Common Issues

**Issue:** Still getting inconsistent results
**Solution:** Verify `USE_OPTIMIZED_PROMPTS=true` and `temperature=0.0`

**Issue:** Different findings on different environments
**Solution:** Ensure same `ANALYSIS_SEED` value across environments

**Issue:** Missing expected findings
**Solution:** Check control selection logic and resource type mapping

### Debug Mode

Enable detailed logging to troubleshoot consistency issues:
```python
logging.getLogger().setLevel(logging.DEBUG)
```

This will show:
- Control selection process
- Prompt construction details
- API call parameters
- Response parsing steps
