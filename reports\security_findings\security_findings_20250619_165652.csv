File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
Parameters\LacpBillingExhaust.Parameters-LacpBillingExhaust.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'usageAccountSystemAssignedIdentityPrincipalId' flows across template boundary,"Secure parameter 'usageAccountSystemAssignedIdentityPrincipalId' and validate its usage across template boundaries

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applicati...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),"Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applications to Azure AD when technically feasible.","Enhanced Implementation Context:
• Azure AD tenancy concepts: https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps
• Create and configure Azure AD: https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant
• Define Azure AD tenants: https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/
• External identity providers: https://docs.microsoft.com/azure/active-directory/b2b/identity-providers
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 6.7, 12.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8
• PCI-DSS v3.2.1: 7.2, 8.3

Azure Policy Examples:
• An Azure Active Directory administrator should be provisioned for SQL servers
• Service Fabric clusters should only use Azure Active Directory for client authentication
• Standardize identity provider across all applications and services",cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyVaultSuffix' flows across template boundary,"Secure parameter 'keyVaultSuffix' and validate its usage across template boundaries

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applicati...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),"Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applications to Azure AD when technically feasible.","Enhanced Implementation Context:
• Azure AD tenancy concepts: https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps
• Create and configure Azure AD: https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant
• Define Azure AD tenants: https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/
• External identity providers: https://docs.microsoft.com/azure/active-directory/b2b/identity-providers
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 6.7, 12.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8
• PCI-DSS v3.2.1: 7.2, 8.3

Azure Policy Examples:
• An Azure Active Directory administrator should be provisioned for SQL servers
• Service Fabric clusters should only use Azure Active Directory for client authentication
• Standardize identity provider across all applications and services",cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'regionalCosmosAccountLocation' flows across template boundary,"Secure parameter 'regionalCosmosAccountLocation' and validate its usage across template boundaries

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applicati...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),"Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applications to Azure AD when technically feasible.","Enhanced Implementation Context:
• Azure AD tenancy concepts: https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps
• Create and configure Azure AD: https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant
• Define Azure AD tenants: https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/
• External identity providers: https://docs.microsoft.com/azure/active-directory/b2b/identity-providers
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 6.7, 12.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8
• PCI-DSS v3.2.1: 7.2, 8.3

Azure Policy Examples:
• An Azure Active Directory administrator should be provisioned for SQL servers
• Service Fabric clusters should only use Azure Active Directory for client authentication
• Standardize identity provider across all applications and services",cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'dasStorageAccountKey' flows across template boundary,"Secure parameter 'dasStorageAccountKey' and validate its usage across template boundaries

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applicati...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),"Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applications to Azure AD when technically feasible.","Enhanced Implementation Context:
• Azure AD tenancy concepts: https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps
• Create and configure Azure AD: https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant
• Define Azure AD tenants: https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/
• External identity providers: https://docs.microsoft.com/azure/active-directory/b2b/identity-providers
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 6.7, 12.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8
• PCI-DSS v3.2.1: 7.2, 8.3

Azure Policy Examples:
• An Azure Active Directory administrator should be provisioned for SQL servers
• Service Fabric clusters should only use Azure Active Directory for client authentication
• Standardize identity provider across all applications and services",cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'globalKeyVaultName' flows across template boundary,"Secure parameter 'globalKeyVaultName' and validate its usage across template boundaries

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applicati...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),"Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applications to Azure AD when technically feasible.","Enhanced Implementation Context:
• Azure AD tenancy concepts: https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps
• Create and configure Azure AD: https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant
• Define Azure AD tenants: https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/
• External identity providers: https://docs.microsoft.com/azure/active-directory/b2b/identity-providers
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 6.7, 12.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8
• PCI-DSS v3.2.1: 7.2, 8.3

Azure Policy Examples:
• An Azure Active Directory administrator should be provisioned for SQL servers
• Service Fabric clusters should only use Azure Active Directory for client authentication
• Standardize identity provider across all applications and services",cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'createSharedKeyVault' flows across template boundary,"Secure parameter 'createSharedKeyVault' and validate its usage across template boundaries

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applicati...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),"Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applications to Azure AD when technically feasible.","Enhanced Implementation Context:
• Azure AD tenancy concepts: https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps
• Create and configure Azure AD: https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant
• Define Azure AD tenants: https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/
• External identity providers: https://docs.microsoft.com/azure/active-directory/b2b/identity-providers
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 6.7, 12.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8
• PCI-DSS v3.2.1: 7.2, 8.3

Azure Policy Examples:
• An Azure Active Directory administrator should be provisioned for SQL servers
• Service Fabric clusters should only use Azure Active Directory for client authentication
• Standardize identity provider across all applications and services",cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,NS-2,Network Security,Protect public endpoints,CRITICAL,40.0,"The parameter 'isBoundariesRestricted' is set to 'false' on line 41, indicating that network boundaries are not restricted. This configuration can enable public exposure of Logic Apps and related resources, allowing attackers to exploit public endpoints for initial access, lateral movement, and data exfiltration. The blast radius includes all resources accessible via these endpoints, increasing the risk of network compromise.","Set 'isBoundariesRestricted' to 'true' to enforce strict network boundaries. Implement Azure Private Link or service endpoints for all Logic Apps and related resources, and restrict public access to only required IPs. Review and update network security group (NSG) rules to deny all inbound traffic by default except for explicitly allowed sources.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
Templates\IngestionStorageAccount.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,44.0,"The storage account resource at line 044 does not configure network rules to restrict public access. By default, Azure Storage accounts allow public network access unless explicitly restricted. This enables attackers to attempt initial access, brute force, or exploit vulnerabilities over the public internet, potentially exposing all data in the storage account and increasing the blast radius to all resources and data within.","Restrict public network access by adding the 'networkAcls' property with 'defaultAction' set to 'Deny' and specifying only required IPs or subnets in 'ipRules' or 'virtualNetworkRules'. Example: 'networkAcls': { 'defaultAction': 'Deny', 'bypass': 'AzureServices', 'ipRules': [ { 'value': '<trusted-ip>' } ], 'virtualNetworkRules': [ { 'id': '<subnet-resource-id>' } ] }.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
Templates\LacpBillingExhaust.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,61.0,"The 'trustedExternalTenants' property on the Microsoft.Kusto/clusters resource (Line 061) allows access from an external Azure AD tenant (value: '72f988bf-86f1-41af-91ab-2d7cd011db47'). This configuration enables cross-tenant access, which increases the attack surface for initial access and lateral movement by potentially untrusted external identities. If the external tenant is compromised, attackers could gain access to the Kusto cluster, leading to data exfiltration or privilege escalation. The blast radius includes all data and operations within the Kusto cluster.","Restrict 'trustedExternalTenants' to only those external tenants that are strictly required for business operations. Remove unnecessary external tenant IDs from the list. Where possible, use Private Link or service endpoints to further restrict access to the cluster. Review and validate all cross-tenant trust relationships to ensure they are justified and monitored.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
Templates\LacpGeo.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,225.0,"The CosmosDB resource at line 225 has 'publicNetworkAccess' set to 'Enabled', which exposes the database account to the public internet. This enables initial access attack vectors, allowing attackers to attempt brute force, credential stuffing, or exploit vulnerabilities over the public endpoint. The blast radius includes potential compromise of all data in the CosmosDB account and lateral movement to other Azure resources.","Set 'publicNetworkAccess' to 'Disabled' to restrict access to private endpoints only. Implement Azure Private Link or service endpoints to ensure only trusted networks can access the CosmosDB account. Example: 'publicNetworkAccess': 'Disabled'.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
Templates\LacpGeo.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,228.0,"The CosmosDB resource at line 228 has 'isVirtualNetworkFilterEnabled' set to 'false', which disables virtual network filtering. This allows unrestricted access from any network, increasing the risk of unauthorized access and data exfiltration. Attackers can exploit this to access the database from untrusted networks, expanding the blast radius to all data in the account.","Set 'isVirtualNetworkFilterEnabled' to 'true' and define 'virtualNetworkRules' to restrict access to only approved subnets. Example: 'isVirtualNetworkFilterEnabled': true, and specify required 'virtualNetworkRules'.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,105.0,"CosmosDB resource 'publicNetworkAccess' is set to 'Enabled' on line 105. This exposes the CosmosDB account to the public internet, allowing attackers to attempt initial access, brute force, or exploit vulnerabilities. The blast radius includes potential compromise of all data in the CosmosDB account and lateral movement to other resources if credentials are obtained.","Set 'publicNetworkAccess' to 'Disabled' to restrict access to private endpoints only. Implement Azure Private Link or service endpoints to ensure only trusted networks can access the CosmosDB account. Example: ""publicNetworkAccess"": ""Disabled"".

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,108.0,"CosmosDB resource 'isVirtualNetworkFilterEnabled' is set to 'false' on line 108. This disables virtual network filtering, allowing unrestricted access from any network, which enables attackers to connect from untrusted locations. The blast radius includes full data exposure and increased risk of lateral movement.","Set 'isVirtualNetworkFilterEnabled' to 'true' and define 'virtualNetworkRules' to restrict access to trusted subnets. Example: ""isVirtualNetworkFilterEnabled"": true, and provide appropriate 'virtualNetworkRules'.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
Templates\LacpGlobal.Template.json,DP-8,Data Protection,Ensure security of key and certificate repository,HIGH,105.0,"Key Vault resource does not specify 'networkAcls' or 'publicNetworkAccess', which means the Key Vault is accessible from all public networks by default. This increases the attack surface for credential theft and key compromise, enabling attackers to attempt access from anywhere.","Explicitly set 'publicNetworkAccess' to 'Disabled' and configure 'networkAcls' to allow access only from trusted subnets or private endpoints. Example: add ""publicNetworkAccess"": ""Disabled"" and define 'networkAcls' with appropriate 'virtualNetworkRules' and 'ipRules'.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/key-vault/general/overview) | [Key Vault security best practices](https://docs.microsoft.com/azure/key-vault/general/best-practices) | [Managed identity Key Vault access](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad) | [Key Vault Private Link](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Key Vault logging and monitoring](https://docs.microsoft.com/azure/key-vault/general/logging)

🔵 Azure Guidance: Secure Azure Key Vault through access policies or Azure RBAC for least privilege. Use Private Link and Azure Firewall for minimal exposure. Implement separation of duties managed identity access and c...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/key-vault/general/overview) | [Key Vault security best practices](https://docs.microsoft.com/azure/key-vault/general/best-practices) | [Managed identity Key Vault access](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad) | [Key Vault Private Link](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Key Vault logging and monitoring](https://docs.microsoft.com/azure/key-vault/general/logging),Secure Azure Key Vault through access policies or Azure RBAC for least privilege. Use Private Link and Azure Firewall for minimal exposure. Implement separation of duties managed identity access and comprehensive logging.,"Enhanced Implementation Context:
• Azure Key Vault overview: https://docs.microsoft.com/azure/key-vault/general/overview
• Key Vault security best practices: https://docs.microsoft.com/azure/key-vault/general/best-practices
• Managed identity Key Vault access: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad
• Key Vault Private Link: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Key Vault logging and monitoring: https://docs.microsoft.com/azure/key-vault/general/logging

Compliance Mappings:
• CIS Controls v8: Not specified
• NIST SP800-53 r4: IA-5, SC-12, SC-17
• PCI-DSS v3.2.1: 3.6

Azure Policy Examples:
• Key vaults should have purge protection enabled
• Azure Defender for Key Vault should be enabled
• Key vaults should have soft delete enabled
• Azure Key Vault should disable public network access
• Private endpoint should be configured for Key Vault
• Resource logs in Key Vault should be enabled
• Implement separation of duties for key management and data access",ai_analysis,,Validated
Templates\LacpRegion.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,561.0,"The CosmosDB account resource at line 558 has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false. This exposes the CosmosDB account to the public internet, allowing attackers to attempt direct access, brute force, or exploit vulnerabilities. The blast radius includes potential unauthorized access to all data in the CosmosDB account, lateral movement to other resources, and data exfiltration.","Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to true. Configure 'virtualNetworkRules' to allow only trusted subnets. Implement Private Endpoints for CosmosDB to eliminate public exposure. Reference: ASB NS-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
Templates\LacpRegion.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,561.0,"The CosmosDB account resource at line 558 does not configure any Private Endpoints or restrict access to private networks. 'publicNetworkAccess' is 'Enabled' and 'isVirtualNetworkFilterEnabled' is false, meaning all traffic is allowed from the public internet. This increases the attack surface and allows attackers to target the CosmosDB account from anywhere.","Implement Private Endpoints for the CosmosDB account and restrict access to only trusted virtual networks. Set 'publicNetworkAccess' to 'Disabled' and configure 'virtualNetworkRules' appropriately. Reference: ASB NS-5.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/manage-ddos-protection) | [DDoS response strategy](https://docs.microsoft.com/azure/ddos-protection/ddos-response-strategy) | [DDoS monitoring and alerting](https://docs.microsoft.com/azure/ddos-protection/telemetry-monitoring-alerting) | [DDoS best practices](https://docs.microsoft.com/azure/security/fundamentals/ddos-best-practices) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Enable DDoS Standard protection plan on your VNet to protect resources exposed to public networks. Configure DDoS policies and monitoring.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/manage-ddos-protection) | [DDoS response strategy](https://docs.microsoft.com/azure/ddos-protection/ddos-response-strategy) | [DDoS monitoring and alerting](https://docs.microsoft.com/azure/ddos-protection/telemetry-monitoring-alerting) | [DDoS best practices](https://docs.microsoft.com/azure/security/fundamentals/ddos-best-practices) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Enable DDoS Standard protection plan on your VNet to protect resources exposed to public networks. Configure DDoS policies and monitoring.,"Enhanced Implementation Context:
• Azure DDoS Protection Standard: https://docs.microsoft.com/azure/virtual-network/manage-ddos-protection
• DDoS response strategy: https://docs.microsoft.com/azure/ddos-protection/ddos-response-strategy
• DDoS monitoring and alerting: https://docs.microsoft.com/azure/ddos-protection/telemetry-monitoring-alerting
• DDoS best practices: https://docs.microsoft.com/azure/security/fundamentals/ddos-best-practices
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 13.10
• NIST SP800-53 r4: SC-5, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3, 6.6

Azure Policy Examples:
• Azure DDoS Protection Standard should be enabled
• Monitor DDoS attack metrics and configure alerts
• Implement DDoS response procedures",ai_analysis,,Validated
Templates\LacpRegion.Template.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,561.0,"The CosmosDB account at line 561 has 'isVirtualNetworkFilterEnabled' set to false, which means network traffic is not restricted and data in transit may not be protected by network boundaries. Attackers can intercept or manipulate data in transit if they gain access to the public endpoint.","Set 'isVirtualNetworkFilterEnabled' to true and restrict access to only trusted subnets. Ensure all client connections enforce TLS 1.2 or higher. Reference: ASB DP-3.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account)

🔵 Azure Guidance: Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windo...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account),Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windows remote management.,"Enhanced Implementation Context:
• Double encryption for Azure data in transit: https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit
• Encryption in transit overview: https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit
• TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem
• Azure Storage secure transfer: https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account

Compliance Mappings:
• CIS Controls v8: 3.10
• NIST SP800-53 r4: SC-8
• PCI-DSS v3.2.1: 3.5, 3.6, 4.1

Azure Policy Examples:
• Kubernetes clusters should be accessible only over HTTPS
• Only secure connections to your Azure Cache for Redis should be enabled
• FTPS only should be required in your Function App
• Secure transfer to storage accounts should be enabled
• Function App should only be accessible over HTTPS
• Latest TLS version should be used in your API App
• Web Application should only be accessible over HTTPS
• Enforce SSL connection should be enabled for PostgreSQL database servers
• Latest TLS version should be used in your Web App",ai_analysis,,Validated
Templates\LacpStamp.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,164.0,"The 'target' property in the Microsoft.Network/trafficManagerProfiles/externalEndpoints resource (Line 281) exposes an endpoint using a public DNS name ('cloudapp.azure.com'). This enables public access to backend services, increasing the attack surface for initial access, lateral movement, and data exfiltration. Attackers can directly target these endpoints if network restrictions are not enforced.","Restrict public access by configuring Private Link or service endpoints for backend services. Limit access to required IPs only and consider using Azure Front Door or Application Gateway with Web Application Firewall (WAF) to protect public endpoints. Update the 'target' property to reference a private endpoint or ensure NSGs and firewalls restrict access to only trusted sources.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
Templates\LacpStamp.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,164.0,"The 'target' property in the Microsoft.Network/trafficManagerProfiles/externalEndpoints resource (Line 316) exposes an endpoint using a public DNS name ('cloudapp.azure.com'). This configuration allows public access to backend services, creating an attack vector for initial access, lateral movement, and potential data exfiltration. Without network restrictions, attackers can probe and exploit these endpoints.","Restrict public access by implementing Private Link or service endpoints for backend services. Limit access to only required IPs and use Azure Front Door or Application Gateway with WAF for public exposure. Update the 'target' property to use a private endpoint or ensure NSGs/firewalls restrict access to trusted sources only.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
Templates\RoleAssignment.Template.json,IM-2,Identity Management,Protect identity and authentication systems,HIGH,5.0,"The role assignment on line 72 grants 'Contributor' access to the principal specified by 'Ev2BuildoutServicePrincipalId'. Assigning the Contributor role at the subscription or resource group level to a service principal enables broad permissions, including the ability to modify, delete, or escalate privileges on most resources. If this service principal is compromised, an attacker could gain full control over the environment, perform privilege escalation, and move laterally across resources, significantly increasing the blast radius.","Restrict the 'Contributor' role assignment to the minimum scope required (preferably at the resource or resource group level, not subscription-wide). Use more granular, least-privilege custom roles if possible. Regularly review and audit service principal permissions. Enforce strong authentication (MFA) and monitor for anomalous activity. Reference: ASB IM-2 (Implement limited administrative roles and require strong authentication for privileged access).

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score) | [Active Directory security best practices](https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory) | [Azure AD security baseline](https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline) | [Privileged Identity Management](https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Use Azure AD security baseline and Identity Secure Score to evaluate security posture. Implement limited administrative roles user risk policies MFA for administrative roles and block legacy authentic...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score) | [Active Directory security best practices](https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory) | [Azure AD security baseline](https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline) | [Privileged Identity Management](https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),Use Azure AD security baseline and Identity Secure Score to evaluate security posture. Implement limited administrative roles user risk policies MFA for administrative roles and block legacy authentication.,"Enhanced Implementation Context:
• Azure AD Identity Secure Score: https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score
• Active Directory security best practices: https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory
• Azure AD security baseline: https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline
• Privileged Identity Management: https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 5.4, 6.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8, SI-4
• PCI-DSS v3.2.1: 8.2, 8.3

Azure Policy Examples:
• No applicable built-in policy (requires configuration-based implementation)
• Use Azure AD Identity Secure Score recommendations
• Implement Azure AD security baseline configurations
• Monitor privileged account activities through Azure AD logs",ai_analysis,,Validated
Templates\TrafficManagerEndpoints.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,8.0,"The 'target' property on line 53 of the Microsoft.Network/trafficManagerProfiles/externalEndpoints resource is configured with a domain-based endpoint (parameters('clusterNamePrefix')-variables('endpointsDetails')0.index-parameters('cluster').suffix.parameters('region').parameters('cluster').domain), which by default exposes the endpoint publicly. This enables an initial access attack vector, as attackers can directly target the public endpoint, increasing the blast radius to all resources behind the Traffic Manager profile. Without explicit IP restrictions or Private Link, this configuration allows broad network exposure.","Restrict public access to the Traffic Manager endpoint by configuring IP whitelisting, using Azure Front Door or Application Gateway with Web Application Firewall, or implementing Azure Private Link for the endpoint. Update the 'target' property to reference a private endpoint or ensure that only required IPs can access the resource. Review and apply NSG or firewall rules to limit exposure.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 21,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 6,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-19T16:56:52.647828,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
