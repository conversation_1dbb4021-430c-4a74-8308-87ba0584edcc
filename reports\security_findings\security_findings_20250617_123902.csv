Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,153,"App Service 'onefuzz-daily-ui' allows public network access ('publicNetworkAccess': 'Enabled') and has 'ipSecurityRestrictions' with 'ipAddress': 'Any' and 'action': 'Allow', exposing the app to the public internet.",Restrict 'ipSecurityRestrictions' to specific IP ranges or disable public network access by setting 'publicNetworkAccess' to 'Disabled'.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,163,"App Service 'onefuzz-daily-ui' SCM endpoint allows public network access ('publicNetworkAccess': 'Enabled') and has 'scmIpSecurityRestrictions' with 'ipAddress': 'Any' and 'action': 'Allow', exposing the SCM endpoint to the public internet.",Restrict 'scmIpSecurityRestrictions' to specific IP ranges or disable public network access by setting 'publicNetworkAccess' to 'Disabled'.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-3,template.json,153,App Service 'onefuzz-daily-ui' does not implement Network Security Groups (NSGs) and allows all inbound traffic ('ipSecurityRestrictions' with 'ipAddress': 'Any' and 'action': 'Allow').,Implement NSGs or restrict 'ipSecurityRestrictions' to limit inbound traffic to only trusted sources.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-3,template.json,163,App Service 'onefuzz-daily-ui' SCM endpoint does not implement Network Security Groups (NSGs) and allows all inbound traffic ('scmIpSecurityRestrictions' with 'ipAddress': 'Any' and 'action': 'Allow').,Implement NSGs or restrict 'scmIpSecurityRestrictions' to limit inbound traffic to only trusted sources.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,153,App Service 'onefuzz-daily-ui' is accessible via public endpoints and does not use private endpoints for secure access.,Configure a private endpoint for the App Service and disable public network access to restrict access to trusted networks only.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,61,"App Service 'onefuzz-daily-ui' has 'sslState' set to 'Disabled' for the hostname 'onefuzz-daily-ui.azurewebsites.net', which means encryption in transit (TLS) is not enforced for this endpoint.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates to enforce TLS for all endpoints.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,68,"App Service 'onefuzz-daily-ui' has 'sslState' set to 'Disabled' for the hostname 'onefuzz-daily-ui.scm.azurewebsites.net', which means encryption in transit (TLS) is not enforced for the SCM (deployment) endpoint.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all SCM hostNameSslStates to enforce TLS for all endpoints.,N/A,AI,Generic
