<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Assessment Report - IaC Guardian</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            /* 🎨 Primary Color Palette (Blue tone) */
            --hue-primary: 223;
            --primary500: hsl(var(--hue-primary), 90%, 50%);
            --primary600: hsl(var(--hue-primary), 90%, 60%);
            --primary700: hsl(var(--hue-primary), 90%, 70%);

            /* 🟢 Secondary Color Palette (Teal tone) */
            --hue-secondary: 178;
            --secondary800: hsl(var(--hue-secondary), 90%, 80%);

            /* 🌑 Dark Grays (used for dark backgrounds) */
            --dark-gray50: hsl(var(--hue-primary), 90%, 5%);
            --dark-gray100: hsl(var(--hue-primary), 90%, 10%);

            /* ⚪ White Transparency Palette (used for glass effects, overlays) */
            --white0: hsla(0, 0%, 100%, 0);
            --white50: hsla(0, 0%, 100%, 0.05);
            --white100: hsla(0, 0%, 100%, 0.1);
            --white200: hsla(0, 0%, 100%, 0.2);
            --white300: hsla(0, 0%, 100%, 0.3);
            --white400: hsla(0, 0%, 100%, 0.4);
            --white500: hsla(0, 0%, 100%, 0.5);
            --white: hsl(0, 0%, 100%);

            /* 🧮 Base font scaling */
            font-size: clamp(0.75rem, 0.65rem + 0.5vw, 1.25rem);

            /* Glass UI Semantic Colors with New Palette */
            --success-green: hsl(142, 76%, 36%);
            --warning-amber: hsl(38, 92%, 50%);
            --danger-red: hsl(0, 84%, 60%);
            --info-cyan: var(--secondary800);

            /* Glass UI Components using White Transparency */
            --glass-white: var(--white200);
            --glass-white-light: var(--white100);
            --glass-white-strong: var(--white400);
            --glass-border: var(--white200);

            /* 📝 Text Color Palette - Optimized for Glass UI */
            --text-primary: var(--white);
            --text-secondary: hsla(0, 0%, 100%, 0.85);
            --text-muted: hsla(0, 0%, 100%, 0.65);
            --text-accent: hsl(var(--hue-secondary), 90%, 85%);
            --text-on-glass: hsla(0, 0%, 100%, 0.95);
            --text-on-dark: var(--white);
            --text-on-light: hsl(var(--hue-primary), 90%, 15%);
            --text-interactive: hsl(var(--hue-primary), 90%, 85%);
            --text-hover: hsl(var(--hue-secondary), 90%, 90%);

            /* Semantic Colors with Glass Effects */
            --critical-glass: hsla(0, 84%, 60%, 0.2);
            --critical-border: hsla(0, 84%, 60%, 0.3);
            --critical-text: hsl(0, 84%, 80%);
            --high-glass: hsla(38, 92%, 50%, 0.2);
            --high-border: hsla(38, 92%, 50%, 0.3);
            --high-text: hsl(38, 92%, 75%);
            --medium-glass: hsla(45, 93%, 47%, 0.2);
            --medium-border: hsla(45, 93%, 47%, 0.3);
            --medium-text: hsl(45, 93%, 75%);
            --low-glass: hsla(var(--hue-secondary), 90%, 80%, 0.2);
            --low-border: hsla(var(--hue-secondary), 90%, 80%, 0.3);
            --low-text: var(--secondary800);

            /* Glass UI Layout */
            --max-width: 1400px;
            --border-radius: 16px;
            --border-radius-sm: 12px;
            --border-radius-lg: 24px;
            --glass-blur: blur(16px);
            --glass-blur-strong: blur(24px);
            --glass-shadow: 0 8px 32px hsla(var(--hue-primary), 90%, 5%, 0.12);
            --glass-shadow-lg: 0 16px 64px hsla(var(--hue-primary), 90%, 5%, 0.16);
            --glass-shadow-xl: 0 24px 96px hsla(var(--hue-primary), 90%, 5%, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-on-glass);
            background: linear-gradient(135deg,
                var(--dark-gray50) 0%,
                var(--dark-gray100) 30%,
                hsl(var(--hue-primary), 60%, 15%) 70%,
                hsl(var(--hue-secondary), 40%, 20%) 100%);
            background-attachment: fixed;
            min-height: 100vh;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, hsla(var(--hue-primary), 90%, 50%, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, hsla(var(--hue-secondary), 90%, 60%, 0.12) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, var(--white50) 0%, transparent 70%);
            pointer-events: none;
            z-index: -1;
        }

        .main-container {
            max-width: var(--max-width);
            margin: 0 auto;
            padding: 2rem 1rem;
            position: relative;
            z-index: 1;
        }

        /* Glass UI Header Section */
        .report-header {
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-lg);
            padding: 3rem 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--glass-shadow-lg);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .report-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary500), var(--primary600), var(--secondary800));
            border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
        }

        .report-header::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
            pointer-events: none;
            border-radius: inherit;
        }

        .report-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-on-glass);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            text-shadow: 0 2px 8px hsla(var(--hue-primary), 90%, 5%, 0.3);
            position: relative;
            z-index: 2;
        }

        .report-subtitle {
            font-size: 1.125rem;
            color: var(--text-accent);
            font-weight: 400;
            margin-bottom: 1rem;
            position: relative;
            z-index: 2;
            text-shadow: 0 1px 4px hsla(var(--hue-primary), 90%, 5%, 0.2);
        }

        .report-meta {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 1.5rem;
            font-size: 0.875rem;
            color: var(--text-interactive);
            position: relative;
            z-index: 2;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: var(--glass-white-light);
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius-sm);
            border: 1px solid var(--glass-border);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }

        /* Glass UI Controls Section */
        .controls-section {
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--glass-shadow);
        }

        .controls-grid {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 1.5rem;
            align-items: center;
        }

        .search-container {
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-sm);
            font-size: 0.875rem;
            transition: all 0.3s ease;
            background: var(--glass-white-light);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            color: var(--text-on-glass);
        }

        .search-input::placeholder {
            color: var(--text-interactive);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary500);
            background: var(--glass-white-strong);
            box-shadow: 0 0 0 3px hsla(var(--hue-primary), 90%, 50%, 0.2);
            transform: translateY(-1px);
            color: var(--text-on-glass);
        }

        .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-interactive);
        }

        .filter-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 0.5rem 1rem;
            border: 1px solid var(--glass-border);
            border-radius: 2rem;
            background: var(--glass-white-light);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            color: var(--text-interactive);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            white-space: nowrap;
        }

        .filter-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--glass-shadow);
            background: var(--glass-white-strong);
            color: var(--text-hover);
        }

        .filter-btn.active {
            color: var(--text-on-dark);
            transform: translateY(-2px);
            box-shadow: var(--glass-shadow-lg);
            border-color: transparent;
        }

        .filter-btn.all.active {
            background: linear-gradient(135deg, var(--primary500), var(--primary600));
        }
        .filter-btn.critical.active {
            background: linear-gradient(135deg, var(--danger-red), var(--critical-text));
        }
        .filter-btn.high.active {
            background: linear-gradient(135deg, var(--warning-amber), var(--high-text));
        }
        .filter-btn.medium.active {
            background: linear-gradient(135deg, hsl(45, 93%, 47%), var(--medium-text));
        }
        .filter-btn.low.active {
            background: linear-gradient(135deg, var(--info-cyan), var(--secondary800));
        }

        /* Multi-select filter enhancements */
        .filter-btn.active {
            position: relative;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .multi-select-info {
            text-align: center;
            margin-top: 0.5rem;
        }

        .filter-summary {
            text-align: center;
            font-weight: 500;
        }

        /* Animation for filter changes */
        .severity-group {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        .severity-group[style*="display: none"] {
            opacity: 0;
            transform: translateY(-10px);
        }

        .finding-item {
            transition: opacity 0.2s ease;
        }

        .finding-item[style*="display: none"] {
            opacity: 0;
        }

        /* Glass UI Summary Section */
        .summary-section {
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--glass-shadow);
        }

        .summary-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-on-glass);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-shadow: 0 2px 8px hsla(var(--hue-primary), 90%, 5%, 0.3);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--glass-white-light);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--glass-shadow-lg);
            background: var(--glass-white-strong);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary500), var(--secondary800));
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        .stat-card::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 50%;
            height: 100%;
            background: radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.06) 0%, transparent 60%);
            pointer-events: none;
            border-radius: inherit;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-on-glass);
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 8px hsla(var(--hue-primary), 90%, 5%, 0.3);
            position: relative;
            z-index: 2;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-accent);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            position: relative;
            z-index: 2;
        }

        .severity-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .severity-badge {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border: 1px solid var(--glass-border);
            position: relative;
            overflow: hidden;
        }

        .severity-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: inherit;
            opacity: 0.1;
            z-index: -1;
        }

        .severity-badge:hover {
            transform: translateY(-2px);
            box-shadow: var(--glass-shadow);
        }

        .severity-badge.critical {
            background: var(--critical-glass);
            border-color: var(--critical-border);
            color: var(--critical-text);
        }

        .severity-badge.high {
            background: var(--high-glass);
            border-color: var(--high-border);
            color: var(--high-text);
        }

        .severity-badge.medium {
            background: var(--medium-glass);
            border-color: var(--medium-border);
            color: var(--medium-text);
        }

        .severity-badge.low {
            background: var(--low-glass);
            border-color: var(--low-border);
            color: var(--low-text);
        }

        .severity-count {
            font-size: 1.25rem;
            font-weight: 700;
            text-shadow: 0 1px 4px hsla(var(--hue-primary), 90%, 5%, 0.2);
        }

        .severity-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* Glass UI Findings Section */
        .findings-container {
            margin-bottom: 2rem;
        }

        .severity-group {
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
            box-shadow: var(--glass-shadow);
            overflow: hidden;
            border: 1px solid var(--glass-border);
            transition: all 0.3s ease;
        }

        .severity-group:hover {
            transform: translateY(-2px);
            box-shadow: var(--glass-shadow-lg);
        }

        .severity-group:last-child {
            margin-bottom: 0;
        }

        /* Glass UI Domain Section Styles */
        .domain-section {
            margin-bottom: 2rem;
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            box-shadow: var(--glass-shadow);
            transition: all 0.3s ease;
        }

        .domain-section:hover {
            transform: translateY(-2px);
            box-shadow: var(--glass-shadow-lg);
        }

        .domain-header {
            background: linear-gradient(135deg, var(--primary500) 0%, var(--secondary800) 100%);
            color: var(--white);
            padding: 1.5rem 2rem;
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            position: relative;
            overflow: hidden;
        }

        .domain-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            border-radius: inherit;
        }

        .domain-header i {
            font-size: 1.5rem;
            position: relative;
            z-index: 2;
        }

        .domain-section .severity-group {
            margin: 0;
            border-radius: 0;
            border: none;
            border-bottom: 1px solid var(--glass-border);
            box-shadow: none;
            background: var(--glass-white-light);
        }

        .domain-section .severity-group:last-child {
            border-bottom: none;
            border-radius: 0 0 var(--border-radius) var(--border-radius);
        }

        .severity-header {
            padding: 1.25rem 1.5rem;
            font-weight: 600;
            font-size: 1.125rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 1px solid var(--glass-border);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }

        .severity-header:hover {
            background: var(--glass-white-strong);
            transform: translateX(4px);
        }

        .severity-header-left {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .severity-icon {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.875rem;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .severity-header.critical {
            background: var(--critical-glass);
            color: var(--critical-text);
            border-left: 4px solid var(--critical-text);
        }

        .severity-header.critical .severity-icon {
            background: linear-gradient(135deg, var(--danger-red), var(--critical-text));
        }

        .severity-header.high {
            background: var(--high-glass);
            color: var(--high-text);
            border-left: 4px solid var(--high-text);
        }

        .severity-header.high .severity-icon {
            background: linear-gradient(135deg, var(--warning-amber), var(--high-text));
        }

        .severity-header.medium {
            background: var(--medium-glass);
            color: var(--medium-text);
            border-left: 4px solid var(--medium-text);
        }

        .severity-header.medium .severity-icon {
            background: linear-gradient(135deg, hsl(45, 93%, 47%), var(--medium-text));
        }

        .severity-header.low {
            background: var(--low-glass);
            color: var(--low-text);
            border-left: 4px solid var(--low-text);
        }

        .severity-header.low .severity-icon {
            background: linear-gradient(135deg, var(--info-cyan), var(--secondary800));
        }

        .severity-title {
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .severity-count {
            background: rgba(255, 255, 255, 0.9);
            color: inherit;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 700;
            margin-left: 0.5rem;
        }

        .toggle-icon {
            transition: transform 0.2s ease;
            color: var(--gray-500);
        }

        .severity-header.collapsed .toggle-icon {
            transform: rotate(-90deg);
        }

        .findings-list {
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .findings-list.collapsed {
            max-height: 0;
        }

        .finding-item {
            border-bottom: 1px solid var(--glass-border);
            padding: 1.5rem;
            transition: all 0.3s ease;
            background: var(--glass-white-light);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }

        .finding-item:last-child {
            border-bottom: none;
        }

        .finding-item:hover {
            background: var(--glass-white-strong);
            transform: translateX(4px);
        }

        .finding-header {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .finding-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
            flex-shrink: 0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
        }

        .finding-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, transparent 60%);
            border-radius: inherit;
        }

        .finding-icon.critical {
            background: linear-gradient(135deg, var(--danger-red), var(--critical-text));
        }
        .finding-icon.high {
            background: linear-gradient(135deg, var(--warning-amber), var(--high-text));
        }
        .finding-icon.medium {
            background: linear-gradient(135deg, hsl(45, 93%, 47%), var(--medium-text));
        }
        .finding-icon.low {
            background: linear-gradient(135deg, var(--info-cyan), var(--secondary800));
        }

        .finding-content {
            flex: 1;
            min-width: 0;
        }

        .finding-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-on-glass);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            flex-wrap: wrap;
            text-shadow: 0 1px 4px hsla(var(--hue-primary), 90%, 5%, 0.2);
        }

        .control-id {
            background: linear-gradient(135deg, var(--primary500), var(--primary600));
            color: var(--white);
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            box-shadow: 0 2px 4px var(--dark-gray50);
        }

        .finding-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 0.375rem;
            margin-bottom: 1rem;
            font-size: 0.875rem;
            color: var(--text-interactive);
        }

        .meta-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.25rem;
            background: var(--glass-white-light);
            padding: 0.4rem 0.8rem;
            border-radius: 0.5rem;
            border: 1px solid var(--glass-border);
            color: var(--text-accent);
            font-size: 0.8125rem;
            font-weight: 500;
            min-height: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            white-space: nowrap;
            overflow: visible;
        }

        /* Line Number Highlighting - Base Styles */
        .meta-item.line-number {
            color: var(--text-on-dark);
            font-weight: 600;
            position: relative;
            overflow: visible;
            transition: all 0.3s ease;
            cursor: pointer;
            user-select: none;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            white-space: nowrap;
            /* FORCE DYNAMIC SIZING */
            width: auto !important;
            min-width: max-content !important;
            max-width: none !important;
        }

        .meta-item.line-number::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            border-radius: inherit;
        }

        /* Severity-Specific Line Number Colors - Simplified Structure */
        .finding-item[data-severity="critical"] .meta-item.line-number {
            background: linear-gradient(135deg, hsl(0, 84%, 70%), hsl(0, 84%, 80%)) !important;
            border: 1px solid hsl(0, 84%, 85%) !important;
            box-shadow: 0 2px 6px hsla(0, 84%, 60%, 0.5) !important;
            color: var(--text-on-dark) !important;
            font-weight: 700 !important;
        }

        .finding-item[data-severity="high"] .meta-item.line-number {
            background: linear-gradient(135deg, hsl(38, 92%, 60%), hsl(38, 92%, 70%)) !important;
            border: 1px solid hsl(38, 92%, 75%) !important;
            box-shadow: 0 2px 6px hsla(38, 92%, 50%, 0.5) !important;
            color: var(--text-on-dark) !important;
            font-weight: 700 !important;
        }

        .finding-item[data-severity="medium"] .meta-item.line-number {
            background: linear-gradient(135deg, hsl(45, 93%, 57%), hsl(45, 93%, 67%)) !important;
            border: 1px solid hsl(45, 93%, 72%) !important;
            box-shadow: 0 2px 6px hsla(45, 93%, 47%, 0.5) !important;
            color: var(--text-on-dark) !important;
            font-weight: 700 !important;
        }

        .finding-item[data-severity="low"] .meta-item.line-number {
            background: linear-gradient(135deg, hsl(var(--hue-secondary), 90%, 75%), hsl(var(--hue-secondary), 90%, 85%)) !important;
            border: 1px solid hsl(var(--hue-secondary), 90%, 90%) !important;
            box-shadow: 0 2px 6px hsla(var(--hue-secondary), 90%, 70%, 0.5) !important;
            color: var(--text-on-dark) !important;
            font-weight: 700 !important;
        }

        .meta-item.line-number .meta-icon {
            color: var(--text-on-dark);
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
        }

        /* Line number badges removed - now using simplified .meta-item.line-number structure */

        /* Removed old .line-number-badge rules - now using simplified .meta-item.line-number structure */

        /* Code Preview Button Styling */
        .meta-item.code-preview {
            background: linear-gradient(135deg, var(--primary500), var(--primary600));
            color: var(--text-on-dark);
            font-weight: 600;
            border: 2px solid var(--primary500);
            box-shadow: 0 2px 8px hsla(var(--hue-primary), 90%, 50%, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 0.5rem;
        }

        .meta-item.code-preview:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px hsla(var(--hue-primary), 90%, 50%, 0.5);
            background: linear-gradient(135deg, var(--primary600), var(--primary700));
        }

        .meta-item.code-preview .meta-icon {
            color: var(--text-on-dark);
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
        }

        .code-preview-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.4rem;
            color: var(--text-on-dark);
            padding: 0.4rem 0.8rem;
            border-radius: 0.5rem;
            font-size: 0.8125rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.03em;
            background: linear-gradient(135deg, hsl(var(--hue-primary), 90%, 60%), hsl(var(--hue-primary), 90%, 70%));
            box-shadow: 0 2px 6px hsla(var(--hue-primary), 90%, 50%, 0.4);
            border: 1px solid hsl(var(--hue-primary), 90%, 75%);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
            min-height: 2rem;
            width: auto;
            min-width: max-content;
            line-height: 1.2;
            white-space: nowrap;
            overflow: visible;
        }

        /* Line Number Tooltip */
        .meta-item.line-number:hover::after {
            content: 'Click to jump to line in code editor';
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--dark-gray100);
            color: var(--text-on-dark);
            padding: 0.5rem 0.75rem;
            border-radius: var(--border-radius-sm);
            font-size: 0.75rem;
            white-space: nowrap;
            box-shadow: var(--glass-shadow-lg);
            border: 1px solid var(--glass-border);
            z-index: 1000;
            margin-bottom: 0.5rem;
            opacity: 0;
            animation: tooltip-fade-in 0.3s ease forwards;
        }

        @keyframes tooltip-fade-in {
            from { opacity: 0; transform: translateX(-50%) translateY(5px); }
            to { opacity: 1; transform: translateX(-50%) translateY(0); }
        }

        /* Subtle Professional Hover Effects */
        .finding-item[data-severity="critical"] .meta-item.line-number:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px hsla(0, 84%, 60%, 0.7);
        }

        .finding-item[data-severity="high"] .meta-item.line-number:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px hsla(38, 92%, 50%, 0.7);
        }

        .finding-item[data-severity="medium"] .meta-item.line-number:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px hsla(45, 93%, 47%, 0.7);
        }

        .finding-item[data-severity="low"] .meta-item.line-number:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px hsla(var(--hue-secondary), 90%, 70%, 0.7);
        }

        /* Line Number Copy Functionality - Already defined in base styles */

        .meta-item.line-number:active {
            transform: scale(0.95);
        }

        /* Removed duplicate Professional Static Styling - styles now handled in severity-specific sections above */

        .meta-icon {
            color: var(--text-interactive);
            width: 1rem;
            font-size: 1rem;
        }

        .finding-description {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .remediation-section {
            background: linear-gradient(135deg, var(--success-green) 0%, #059669 100%);
            color: white;
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            margin-top: 1rem;
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border: 1px solid rgba(16, 185, 129, 0.3);
            box-shadow: var(--glass-shadow);
            position: relative;
            overflow: hidden;
        }

        .remediation-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, transparent 60%);
            border-radius: inherit;
        }

        .remediation-title {
            font-weight: 600;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            position: relative;
            z-index: 2;
            color: var(--text-on-dark);
            text-shadow: 0 1px 2px hsla(var(--hue-primary), 90%, 5%, 0.3);
        }

        .remediation-content {
            font-size: 0.875rem;
            line-height: 1.5;
            opacity: 0.95;
            position: relative;
            z-index: 2;
            color: var(--text-on-dark);
        }

        .code-snippet {
            background: hsla(var(--hue-primary), 90%, 5%, 0.9);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            color: var(--text-accent);
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 0.8125rem;
            line-height: 1.5;
            white-space: pre-wrap;
            margin: 1rem 0;
            overflow-x: auto;
            border: 1px solid var(--glass-border);
            box-shadow: var(--glass-shadow);
        }

        .no-findings {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--text-interactive);
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            box-shadow: var(--glass-shadow);
        }

        .no-findings-icon {
            font-size: 3rem;
            color: var(--text-accent);
            margin-bottom: 1rem;
        }

        .no-findings h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-on-glass);
            margin-bottom: 0.5rem;
        }

        /* Glass UI Footer */
        .report-footer {
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-top: 2rem;
            box-shadow: var(--glass-shadow);
            text-align: center;
        }

        .export-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }

        .export-btn {
            padding: 0.75rem 1.5rem;
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-sm);
            background: linear-gradient(135deg, var(--primary500), var(--primary600));
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            color: var(--white);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.875rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            box-shadow: var(--glass-shadow);
            position: relative;
            overflow: hidden;
        }

        .export-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .export-btn:hover::before {
            left: 100%;
        }

        .export-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--glass-shadow-lg);
        }

        .footer-info {
            color: var(--text-interactive);
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .footer-info strong {
            color: var(--text-on-glass);
        }

        /* Glass UI Responsive Design */

        /* Large Desktop (1200px+) - Enhanced Glass Effects */
        @media (min-width: 1200px) {
            .main-container {
                padding: 3rem 2rem;
            }

            .stats-grid {
                grid-template-columns: repeat(4, 1fr);
            }

            .severity-overview {
                grid-template-columns: repeat(4, 1fr);
            }

            /* Enhanced glass blur for larger screens */
            .report-header,
            .controls-section,
            .summary-section,
            .severity-group,
            .domain-section {
                backdrop-filter: var(--glass-blur-strong);
                -webkit-backdrop-filter: var(--glass-blur-strong);
            }
        }

        /* Desktop (992px - 1199px) */
        @media (min-width: 992px) and (max-width: 1199px) {
            .main-container {
                padding: 2rem 1.5rem;
            }

            .report-title {
                font-size: 2.25rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .severity-overview {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Tablet (768px - 991px) */
        @media (min-width: 768px) and (max-width: 991px) {
            .main-container {
                padding: 1.5rem 1rem;
            }

            .report-header {
                padding: 2rem 1.5rem;
            }

            .report-title {
                font-size: 2rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .report-meta {
                flex-direction: column;
                gap: 0.5rem;
            }

            .controls-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .filter-buttons {
                justify-content: center;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .severity-overview {
                grid-template-columns: repeat(2, 1fr);
            }

            .finding-title {
                font-size: 1rem;
            }

            .finding-meta {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        /* Mobile Large (576px - 767px) */
        @media (min-width: 576px) and (max-width: 767px) {
            .main-container {
                padding: 1rem 0.75rem;
            }

            .report-header {
                padding: 1.5rem 1rem;
            }

            .report-title {
                font-size: 1.75rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .report-subtitle {
                font-size: 1rem;
            }

            .report-meta {
                flex-direction: column;
                gap: 0.25rem;
                font-size: 0.8125rem;
            }

            .controls-section {
                padding: 1rem;
            }

            .controls-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .filter-buttons {
                justify-content: center;
                gap: 0.375rem;
            }

            .filter-btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.6875rem;
            }

            .summary-section {
                padding: 1.5rem 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .severity-overview {
                grid-template-columns: 1fr;
            }

            .finding-item {
                padding: 1rem;
            }

            .finding-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.75rem;
            }

            .finding-title {
                font-size: 0.9375rem;
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .export-actions {
                flex-direction: column;
                align-items: center;
            }
        }

        /* Mobile Small (up to 575px) */
        @media (max-width: 575px) {
            .main-container {
                padding: 0.75rem 0.5rem;
            }

            .report-header {
                padding: 1.25rem 0.75rem;
                margin-bottom: 1rem;
            }

            .report-title {
                font-size: 1.5rem;
                flex-direction: column;
                gap: 0.375rem;
            }

            .report-subtitle {
                font-size: 0.9375rem;
            }

            .report-meta {
                flex-direction: column;
                gap: 0.25rem;
                font-size: 0.75rem;
            }

            .controls-section {
                padding: 0.75rem;
            }

            .search-input {
                font-size: 1rem; /* Prevents zoom on iOS */
                padding: 0.75rem 1rem 0.75rem 2.25rem;
            }

            .filter-buttons {
                gap: 0.25rem;
            }

            .filter-btn {
                padding: 0.375rem 0.625rem;
                font-size: 0.625rem;
                min-width: auto;
            }

            .summary-section {
                padding: 1.25rem 0.75rem;
            }

            .summary-title {
                font-size: 1.25rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            .stat-card {
                padding: 1rem;
            }

            .stat-number {
                font-size: 2rem;
            }

            .severity-overview {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .severity-header {
                padding: 1rem;
                font-size: 1rem;
            }

            .finding-item {
                padding: 0.75rem;
            }

            .finding-icon {
                width: 2rem;
                height: 2rem;
                font-size: 0.875rem;
            }

            .finding-title {
                font-size: 0.875rem;
            }

            .control-id {
                font-size: 0.6875rem;
                padding: 0.1875rem 0.5rem;
            }

            .finding-meta {
                font-size: 0.8125rem;
            }

            .finding-description {
                font-size: 0.875rem;
            }

            .code-snippet {
                font-size: 0.75rem;
                padding: 0.75rem;
            }

            .export-actions {
                flex-direction: column;
                gap: 0.5rem;
            }

            .export-btn {
                width: 100%;
                justify-content: center;
            }

            /* Mobile Line Number Fixes - Simplified Structure */
            .meta-item.line-number {
                width: auto !important;
                min-width: max-content !important;
                flex-shrink: 0 !important;
                font-size: 0.75rem !important;
                padding: 0.3rem 0.6rem !important;
                gap: 0.25rem !important;
                white-space: nowrap !important;
                overflow: visible !important;
                box-sizing: border-box !important;
            }
        }

        /* Print Styles */
        @media print {
            body {
                background: white !important;
            }

            .main-container {
                max-width: none;
                padding: 0;
            }

            .report-header,
            .controls-section,
            .summary-section,
            .severity-group,
            .report-footer {
                box-shadow: none !important;
                break-inside: avoid;
            }

            .controls-section,
            .export-actions {
                display: none !important;
            }

            .findings-list {
                max-height: none !important;
            }
        }
    </style>

    <script>
        // Modern JavaScript for enhanced interactivity with multi-select filtering
        let searchTimeout;
        let allFindings = [];
        let activeFilters = new Set(['all']); // Support multiple active filters

        document.addEventListener('DOMContentLoaded', function() {
            initializeReport();
            setupEventListeners();
            loadFindings();
        });

        function initializeReport() {
            // Create code dialog on page load
            createCodeDialog();
            console.log('✅ Code dialog created');

            // Initialize filter buttons with multi-select support
            const filterButtons = document.querySelectorAll('.filter-btn');
            filterButtons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    // Allow Ctrl/Cmd + click for multi-select
                    const isMultiSelect = e.ctrlKey || e.metaKey;
                    toggleFilter(this.dataset.severity, isMultiSelect);
                });
            });

            // Initialize search
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.addEventListener('input', debounceSearch);
            }

            // Initialize collapsible sections
            const severityHeaders = document.querySelectorAll('.severity-header');
            severityHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    toggleSeverityGroup(this);
                });
            });

            // Add instructions for multi-select
            addMultiSelectInstructions();
        }

        function addMultiSelectInstructions() {
            const controlsSection = document.querySelector('.controls-section');
            if (controlsSection) {
                const instructions = document.createElement('div');
                instructions.className = 'multi-select-info';
                instructions.innerHTML = `
                    <small style="color: var(--gray-500); font-size: 0.75rem; margin-top: 0.5rem; display: block;">
                        <i class="fas fa-info-circle"></i>
                        Tip: Hold Ctrl/Cmd and click to select multiple severity levels
                    </small>
                `;
                controlsSection.appendChild(instructions);
            }
        }

        function setupEventListeners() {
            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    clearSearch();
                    resetFilters();
                }
                if (e.ctrlKey && e.key === 'f') {
                    e.preventDefault();
                    focusSearch();
                }
            });
        }

        function debounceSearch() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch();
            }, 300);
        }

        function performSearch() {
            const searchTerm = document.querySelector('.search-input').value.toLowerCase();
            applyFilters(searchTerm);
        }

        function toggleFilter(severity, isMultiSelect = false) {
            if (severity === 'all') {
                // If "All" is clicked, reset to show all
                activeFilters.clear();
                activeFilters.add('all');
            } else {
                if (isMultiSelect) {
                    // Multi-select mode
                    if (activeFilters.has('all')) {
                        activeFilters.clear();
                    }

                    if (activeFilters.has(severity)) {
                        activeFilters.delete(severity);
                    } else {
                        activeFilters.add(severity);
                    }

                    // If no filters selected, default to "all"
                    if (activeFilters.size === 0) {
                        activeFilters.add('all');
                    }
                } else {
                    // Single select mode (default behavior)
                    activeFilters.clear();
                    activeFilters.add(severity);
                }
            }

            updateFilterButtons();
            applyFilters();
            updateUrlHash();
        }

        function updateFilterButtons() {
            const filterButtons = document.querySelectorAll('.filter-btn');
            filterButtons.forEach(btn => {
                const severity = btn.dataset.severity;
                const isActive = activeFilters.has(severity);
                btn.classList.toggle('active', isActive);

                // Add visual indicator for multi-select
                if (activeFilters.size > 1 && !activeFilters.has('all')) {
                    btn.style.position = 'relative';
                    if (isActive && !btn.querySelector('.multi-indicator')) {
                        const indicator = document.createElement('span');
                        indicator.className = 'multi-indicator';
                        indicator.innerHTML = '✓';
                        indicator.style.cssText = `
                            position: absolute;
                            top: -2px;
                            right: -2px;
                            background: var(--success-green);
                            color: white;
                            border-radius: 50%;
                            width: 16px;
                            height: 16px;
                            font-size: 10px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-weight: bold;
                        `;
                        btn.appendChild(indicator);
                    }
                } else {
                    // Remove multi-select indicators
                    const indicator = btn.querySelector('.multi-indicator');
                    if (indicator) {
                        indicator.remove();
                    }
                }
            });
        }

        function applyFilters(searchTerm = '') {
            if (!searchTerm) {
                searchTerm = document.querySelector('.search-input').value.toLowerCase();
            }

            const severityGroups = document.querySelectorAll('.severity-group');
            let totalVisibleCount = 0;

            severityGroups.forEach(group => {
                const groupSeverity = group.dataset.severity;
                const findings = group.querySelectorAll('.finding-item');
                let groupVisibleCount = 0;

                // Check if this severity group should be visible
                const severityMatches = activeFilters.has('all') || activeFilters.has(groupSeverity);

                findings.forEach(finding => {
                    const text = finding.textContent.toLowerCase();
                    const searchMatches = searchTerm === '' || text.includes(searchTerm);
                    const isVisible = severityMatches && searchMatches;

                    finding.style.display = isVisible ? 'block' : 'none';
                    if (isVisible) {
                        groupVisibleCount++;
                        totalVisibleCount++;
                    }
                });

                // Show/hide the entire severity group
                group.style.display = groupVisibleCount > 0 ? 'block' : 'none';
            });

            updateNoResultsMessage(totalVisibleCount === 0);
            updateFilterSummary();
        }

        function updateFilterSummary() {
            // Update or create filter summary
            let summary = document.querySelector('.filter-summary');
            if (!summary) {
                summary = document.createElement('div');
                summary.className = 'filter-summary';
                summary.style.cssText = `
                    margin-top: 0.5rem;
                    padding: 0.5rem;
                    background: var(--gray-100);
                    border-radius: var(--border-radius-sm);
                    font-size: 0.8125rem;
                    color: var(--gray-600);
                `;
                document.querySelector('.controls-section').appendChild(summary);
            }

            if (activeFilters.has('all')) {
                summary.textContent = 'Showing all severity levels';
            } else {
                const filterList = Array.from(activeFilters).map(f => f.charAt(0).toUpperCase() + f.slice(1)).join(', ');
                summary.textContent = `Showing: ${filterList} severity levels`;
            }
        }

        function resetFilters() {
            activeFilters.clear();
            activeFilters.add('all');
            updateFilterButtons();
            applyFilters();
            updateUrlHash();
        }

        function updateUrlHash() {
            const params = new URLSearchParams();
            if (!activeFilters.has('all')) {
                params.set('filters', Array.from(activeFilters).join(','));
            }
            const searchTerm = document.querySelector('.search-input').value;
            if (searchTerm) {
                params.set('search', searchTerm);
            }

            const hash = params.toString();
            if (hash) {
                window.location.hash = hash;
            } else {
                window.history.replaceState(null, null, window.location.pathname);
            }
        }

        function loadFromUrlHash() {
            const hash = window.location.hash.substring(1);
            if (hash) {
                const params = new URLSearchParams(hash);
                const filters = params.get('filters');
                const search = params.get('search');

                if (filters) {
                    activeFilters.clear();
                    filters.split(',').forEach(filter => activeFilters.add(filter.trim()));
                    updateFilterButtons();
                }

                if (search) {
                    document.querySelector('.search-input').value = search;
                }

                applyFilters();
            }
        }

        function toggleSeverityGroup(header) {
            const group = header.parentElement;
            const findingsList = group.querySelector('.findings-list');
            const isCollapsed = header.classList.contains('collapsed');

            if (isCollapsed) {
                header.classList.remove('collapsed');
                findingsList.classList.remove('collapsed');
                findingsList.style.maxHeight = findingsList.scrollHeight + 'px';
            } else {
                header.classList.add('collapsed');
                findingsList.classList.add('collapsed');
                findingsList.style.maxHeight = '0';
            }
        }

        function clearSearch() {
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.value = '';
                applyFilters();
            }
        }

        function focusSearch() {
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }

        function updateNoResultsMessage(show) {
            const noResults = document.querySelector('.no-findings');
            if (noResults) {
                noResults.style.display = show ? 'block' : 'none';
                if (show) {
                    // Update message based on active filters
                    const message = noResults.querySelector('p');
                    if (activeFilters.has('all')) {
                        message.textContent = 'Try adjusting your search terms';
                    } else {
                        const filterList = Array.from(activeFilters).join(', ');
                        message.textContent = `No ${filterList} severity findings match your search. Try different filters or search terms.`;
                    }
                }
            }
        }

        function exportToJson() {
            // Get currently visible findings for export
            const visibleFindings = [];
            document.querySelectorAll('.finding-item').forEach(finding => {
                if (finding.style.display !== 'none') {
                    const severityGroup = finding.closest('.severity-group');
                    const severity = severityGroup ? severityGroup.dataset.severity.toUpperCase() : 'UNKNOWN';
                    const controlId = finding.querySelector('.control-id')?.textContent || 'UNKNOWN';
                    const description = finding.querySelector('.finding-description')?.textContent || '';
                    const remediation = finding.querySelector('.remediation-content')?.textContent || '';
                    const filePath = finding.querySelector('.meta-item:first-child span')?.textContent || '';
                    const lineText = finding.querySelector('.meta-item:last-child span')?.textContent || '';
                    const line = lineText.replace('Line ', '') || '0';

                    visibleFindings.push({
                        control_id: controlId,
                        severity: severity,
                        file_path: filePath,
                        line: parseInt(line) || 0,
                        description: description.trim(),
                        remediation: remediation.trim()
                    });
                }
            });

            const data = {
                timestamp: new Date().toISOString(),
                filters_applied: Array.from(activeFilters),
                total_findings: visibleFindings.length,
                findings: visibleFindings,
                summary: {
                    critical: visibleFindings.filter(f => f.severity === 'CRITICAL').length,
                    high: visibleFindings.filter(f => f.severity === 'HIGH').length,
                    medium: visibleFindings.filter(f => f.severity === 'MEDIUM').length,
                    low: visibleFindings.filter(f => f.severity === 'LOW').length
                }
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], {
                type: 'application/json'
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `security-findings-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function loadFindings() {
            // Initialize findings data - this would be populated with actual findings
            allFindings = [];
        }

        // Glass UI Enhancement Functions
        function addGlassEffects() {
            // Add parallax scrolling effect to background elements
            window.addEventListener('scroll', function() {
                const scrolled = window.pageYOffset;
                const parallax = document.querySelector('body::before');
                if (parallax) {
                    parallax.style.transform = `translateY(${scrolled * 0.5}px)`;
                }
            });

            // Add hover effects to glass cards
            document.querySelectorAll('.stat-card, .severity-badge, .finding-item').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px) scale(1.02)';
                    this.style.boxShadow = 'var(--glass-shadow-xl)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                    this.style.boxShadow = 'var(--glass-shadow)';
                });
            });

            // Add glass ripple effect to buttons
            document.querySelectorAll('.filter-btn, .export-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple');

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        }

        // Line Number Interaction Functions
        function initLineNumberHighlighting() {
            document.querySelectorAll('.meta-item.line-number').forEach(lineItem => {
                lineItem.addEventListener('click', function() {
                    const lineText = this.querySelector('span').textContent;
                    const lineNumber = lineText.replace(/[^0-9]/g, '');
                    const fileName = this.closest('.finding-item').querySelector('.meta-item:first-child span').textContent;

                    // Copy line reference to clipboard
                    const lineReference = `${fileName}:${lineNumber}`;
                    navigator.clipboard.writeText(lineReference).then(() => {
                        showLineNumberFeedback(this, 'Copied to clipboard!');
                    }).catch(() => {
                        showLineNumberFeedback(this, 'Line: ' + lineNumber);
                    });
                });

                // Add hover effect for line numbers
                lineItem.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05) translateY(-2px)';
                });

                lineItem.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1) translateY(0)';
                });
            });
        }

        function showLineNumberFeedback(element, message) {
            const feedback = document.createElement('div');
            feedback.textContent = message;
            feedback.style.cssText = `
                position: absolute;
                top: -40px;
                left: 50%;
                transform: translateX(-50%);
                background: var(--success-green);
                color: white;
                padding: 0.5rem 1rem;
                border-radius: var(--border-radius-sm);
                font-size: 0.75rem;
                font-weight: 600;
                box-shadow: var(--glass-shadow-lg);
                z-index: 1001;
                animation: feedback-bounce 2s ease forwards;
                pointer-events: none;
            `;

            element.style.position = 'relative';
            element.appendChild(feedback);

            setTimeout(() => {
                if (feedback.parentNode) {
                    feedback.parentNode.removeChild(feedback);
                }
            }, 2000);
        }

        // Code Dialog Functions
        function showCodeDialog(filePath, lineNumber, controlId, severity) {
            // Create dialog if it doesn't exist
            let dialog = document.getElementById('code-dialog-overlay');
            if (!dialog) {
                createCodeDialog();
                dialog = document.getElementById('code-dialog-overlay');
            }

            // Update dialog content
            updateCodeDialog(filePath, lineNumber, controlId, severity);

            // Show dialog
            dialog.style.display = 'flex';
            document.body.style.overflow = 'hidden';

            // Focus trap
            dialog.querySelector('.code-dialog-close').focus();
        }

        function createCodeDialog() {
            const dialogHTML = `
                <div id="code-dialog-overlay" class="code-dialog-overlay" onclick="closeCodeDialog(event)">
                    <div class="code-dialog" onclick="event.stopPropagation()">
                        <div class="code-dialog-header">
                            <div class="code-dialog-title">
                                <i class="fas fa-file-code"></i>
                                <span id="dialog-title">Code Snippet</span>
                            </div>
                            <button class="code-dialog-close" onclick="closeCodeDialog()" title="Close dialog">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="code-dialog-content">
                            <div class="code-snippet-container">
                                <div class="code-snippet-with-lines">
                                    <div class="line-numbers" id="line-numbers"></div>
                                    <div class="code-content" id="code-content"></div>
                                </div>
                            </div>
                        </div>
                        <div class="code-dialog-footer">
                            <div class="code-dialog-info">
                                <span id="dialog-file-info"></span>
                                <span id="dialog-severity-info"></span>
                            </div>
                            <div class="code-dialog-actions">
                                <button class="code-dialog-btn" onclick="copyCodeSnippet()" title="Copy code to clipboard">
                                    <i class="fas fa-copy"></i>
                                    Copy Code
                                </button>
                                <button class="code-dialog-btn primary" onclick="closeCodeDialog()">
                                    <i class="fas fa-check"></i>
                                    Close
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', dialogHTML);

            // Add keyboard event listener
            document.addEventListener('keydown', handleDialogKeydown);
        }

        // Global variable to track current dialog request
        let currentDialogRequestId = 0;

        function updateCodeDialog(filePath, lineNumber, controlId, severity) {
            // Generate unique request ID to prevent race conditions
            const requestId = ++currentDialogRequestId;
            console.log('🔄 Updating code dialog for:', filePath, 'line:', lineNumber, 'control:', controlId, 'requestId:', requestId);

            // Clear any existing content and warnings first
            clearCodeDialogContent();

            // Show loading state
            showCodeDialogLoading();

            // Ensure dialog exists before updating
            if (!document.getElementById('code-dialog-overlay')) {
                createCodeDialog();
            }

            // Update title and info with safety checks
            const titleEl = document.getElementById('dialog-title');
            const fileInfoEl = document.getElementById('dialog-file-info');
            const severityInfoEl = document.getElementById('dialog-severity-info');

            if (titleEl) {
                titleEl.textContent = `${controlId} - Code Snippet`;
            } else {
                console.error('❌ dialog-title element not found');
            }

            if (fileInfoEl) {
                fileInfoEl.innerHTML = `<i class="fas fa-file"></i> ${filePath}`;
            } else {
                console.error('❌ dialog-file-info element not found');
            }

            if (severityInfoEl) {
                severityInfoEl.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${severity.toUpperCase()} Severity`;
            } else {
                console.error('❌ dialog-severity-info element not found');
            }

            // Fetch real file content with ±100 lines context
            fetchRealFileContentWithContext(filePath, lineNumber).then(content => {
                // Check if this is still the current request (prevent race conditions)
                if (requestId !== currentDialogRequestId) {
                    console.log('🚫 Ignoring outdated request', requestId, 'current is', currentDialogRequestId);
                    return;
                }

                // Hide loading state
                hideCodeDialogLoading();

                if (content.success) {
                    displayRealCodeWithLineNumbers(content, lineNumber);
                    console.log('✅ Successfully displayed real file content for', filePath, 'line', lineNumber, 'requestId:', requestId);
                } else {
                    // Fallback to sample code if real content unavailable
                    const sampleCode = generateSampleCode(filePath, lineNumber, severity);
                    displayCodeWithLineNumbers(sampleCode, lineNumber);

                    // Show detailed warning about fallback content
                    const errorMsg = content.error || 'File content not embedded in report';
                    showContentWarning(`Real file content unavailable: ${errorMsg}. Showing representative example.`);
                    console.warn('⚠️ Using fallback sample code for', filePath, ':', errorMsg);
                }
            }).catch(error => {
                // Check if this is still the current request
                if (requestId !== currentDialogRequestId) {
                    console.log('🚫 Ignoring outdated error for request', requestId);
                    return;
                }

                // Hide loading state
                hideCodeDialogLoading();

                console.error('❌ Failed to fetch real file content for', filePath, ':', error);
                const sampleCode = generateSampleCode(filePath, lineNumber, severity);
                displayCodeWithLineNumbers(sampleCode, lineNumber);
                showContentWarning(`Error loading file content: ${error.message || error}. Showing representative example.`);
            });
        }

        function clearCodeDialogContent() {
            // Ensure dialog exists
            if (!document.getElementById('code-dialog-overlay')) {
                console.log('🧹 Dialog not found, creating it first');
                createCodeDialog();
                return;
            }

            // Clear previous content with safety checks
            const lineNumbersEl = document.getElementById('line-numbers');
            const codeContentEl = document.getElementById('code-content');

            if (lineNumbersEl) {
                lineNumbersEl.innerHTML = '';
            } else {
                console.warn('⚠️ line-numbers element not found');
            }

            if (codeContentEl) {
                codeContentEl.innerHTML = '';
            } else {
                console.warn('⚠️ code-content element not found');
            }

            // Remove any existing warnings
            const dialogContent = document.querySelector('.code-dialog-content');
            if (dialogContent) {
                const existingWarning = dialogContent.querySelector('.content-warning');
                if (existingWarning) {
                    existingWarning.remove();
                }
            } else {
                console.warn('⚠️ code-dialog-content element not found');
            }

            console.log('🧹 Cleared previous code dialog content');
        }

        function showCodeDialogLoading() {
            // Ensure dialog exists
            if (!document.getElementById('code-dialog-overlay')) {
                console.log('📤 Dialog not found, creating it first');
                createCodeDialog();
            }

            const codeContentEl = document.getElementById('code-content');
            if (codeContentEl) {
                codeContentEl.innerHTML = `
                    <div style="
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        padding: 2rem;
                        color: var(--text-accent);
                        font-size: 0.875rem;
                    ">
                        <i class="fas fa-spinner fa-spin" style="margin-right: 0.5rem;"></i>
                        Loading file content...
                    </div>
                `;
            } else {
                console.error('❌ code-content element not found for loading state');
            }
        }

        function hideCodeDialogLoading() {
            // Loading content will be replaced by actual content, so no explicit action needed
            console.log('📤 Loading state will be replaced by content');
        }

        async function fetchRealFileContentWithContext(filePath, lineNumber) {
            try {
                console.log('🔍 Fetching real file content with context for:', filePath, 'line:', lineNumber);

                // Clear any cached results to ensure fresh fetch
                const timestamp = Date.now();
                console.log('🕒 Fetch timestamp:', timestamp);

                // Create finding ID to match embedded context data
                const findingId = `${filePath}:${lineNumber}`;
                console.log('🔧 Original finding ID:', findingId);

                // Try different normalization patterns that might be used during embedding
                const normalizedIds = [
                    // Primary approach - matches Python normalization exactly
                    findingId.replace(/\\/g, '/').replace(/:/g, '_').replace(/\//g, '_'),
                    // Alternative approaches for robustness
                    findingId.split('\\').join('/').split(':').join('_').split('/').join('_'),
                    findingId.replace(/\\\\/g, '/').replace(/:/g, '_').replace(/\//g, '_'),
                    findingId.replace(/\\\\/g, '_').replace(/:/g, '_').replace(/\//g, '_'),
                    // Keep original separators for debugging
                    findingId,
                    findingId.replace(/\\\\/g, '/'),
                    findingId.split('\\').join('/'),
                    // URL-encoded version
                    encodeURIComponent(findingId),
                    // Base64 encoded version
                    btoa(findingId).replace(/[+\/=]/g, '_')
                ];

                console.log('🔧 Trying normalized IDs:', normalizedIds);

                // Debug: List all available embedded context elements
                const allContextElements = document.querySelectorAll('[data-finding-context]');
                console.log('📄 Available embedded context IDs:', Array.from(allContextElements).map(el => el.getAttribute('data-finding-context')));

                // Try to find embedded context data with any of the normalized IDs
                let contextElement = null;
                let usedId = null;

                for (const normalizedId of normalizedIds) {
                    contextElement = document.querySelector(`[data-finding-context="${normalizedId}"]`);
                    if (contextElement) {
                        usedId = normalizedId;
                        console.log('✅ Found context element with ID:', usedId);
                        break;
                    }
                }

                console.log('🎯 Found embedded context element:', !!contextElement, 'using ID:', usedId);

                if (contextElement) {
                    let contextJson = contextElement.textContent || contextElement.innerText;
                    console.log('📝 Context JSON length:', contextJson.length);
                    console.log('📝 First 200 chars of raw JSON:', contextJson.substring(0, 200));

                    // Unescape HTML entities that were escaped during embedding
                    contextJson = contextJson
                        .replace(/&quot;/g, '"')
                        .replace(/&lt;/g, '<')
                        .replace(/&gt;/g, '>')
                        .replace(/&amp;/g, '&');

                    console.log('📝 First 200 chars of unescaped JSON:', contextJson.substring(0, 200));

                    try {
                        const contextData = JSON.parse(contextJson);
                        console.log('✅ Parsed context data successfully:', contextData.success);

                        if (contextData.success) {
                            console.log('📊 Context info:', {
                                file_path: contextData.file_path,
                                total_lines: contextData.total_lines,
                                start_line: contextData.start_line,
                                end_line: contextData.end_line,
                                content_lines_count: contextData.content_lines?.length
                            });
                            return contextData;
                        } else {
                            console.warn('⚠️ Context data indicates failure:', contextData.error);
                        }
                    } catch (parseError) {
                        console.error('❌ Error parsing context JSON:', parseError);
                        console.error('❌ Raw JSON content (first 500 chars):', contextJson.substring(0, 500));
                    }
                }

                console.log('⚠️ No embedded context found, trying fallback approaches...');

                // Enhanced fallback: try multiple path normalization approaches
                const pathVariations = [
                    filePath,
                    filePath.split('\\').join('/'),
                    filePath.replace(/\\\\/g, '/'),
                    filePath.replace(/\//g, '\\\\'),
                    // Try relative paths
                    filePath.split('/').pop(),
                    filePath.split('\\').pop()
                ];

                const allEmbeddedFiles = document.querySelectorAll('[data-file-path]');
                console.log('📄 Available embedded files:', Array.from(allEmbeddedFiles).map(el => el.getAttribute('data-file-path')));

                for (const pathVariation of pathVariations) {
                    for (const element of allEmbeddedFiles) {
                        const embeddedPath = element.getAttribute('data-file-path');
                        if (embeddedPath === pathVariation || embeddedPath.endsWith(pathVariation) || pathVariation.endsWith(embeddedPath)) {
                            const content = element.textContent || element.innerText;
                            if (content && content.trim()) {
                                const result = parseFileContentWithAccurateLines(content, lineNumber, 100);
                                console.log('✅ Used fallback file content with path variation:', pathVariation);
                                return result;
                            }
                        }
                    }
                }

                console.log('❌ No content available from any source');
                console.log('❌ Debug info - Finding ID:', findingId, 'Normalized IDs tried:', normalizedIds);
                return { success: false, error: 'File content not available - no embedded data found' };

            } catch (error) {
                console.error('❌ Error fetching file content with context:', error);
                return { success: false, error: error.message };
            }
        }

        function parseFileContentWithAccurateLines(content, targetLine, contextLines) {
            // Split content into lines (preserving empty lines)
            const lines = content.split('\n');
            const totalLines = lines.length;
            const targetLineNum = parseInt(targetLine);

            // Validate target line
            if (targetLineNum < 1 || targetLineNum > totalLines) {
                return {
                    success: false,
                    error: `Line ${targetLineNum} out of range (1-${totalLines})`
                };
            }

            // Calculate context range with ±100 lines
            const startLine = Math.max(1, targetLineNum - contextLines);
            const endLine = Math.min(totalLines, targetLineNum + contextLines);

            // Build content lines with accurate numbering
            const contentLines = [];
            for (let lineNum = startLine; lineNum <= endLine; lineNum++) {
                const lineIndex = lineNum - 1; // Convert to 0-based index
                contentLines.push({
                    number: lineNum,
                    content: lines[lineIndex] || '',
                    highlighted: lineNum === targetLineNum
                });
            }

            return {
                success: true,
                file_path: filePath,
                total_lines: totalLines,
                highlighted_line: targetLineNum,
                start_line: startLine,
                end_line: endLine,
                context_size: contextLines,
                content_lines: contentLines
            };
        }

        function displayRealCodeWithLineNumbers(fileData, highlightLine) {
            const lineNumbersEl = document.getElementById('line-numbers');
            const codeContentEl = document.getElementById('code-content');

            let lineNumbersHTML = '';
            let codeHTML = '';

            // Add context information at the top
            if (fileData.start_line && fileData.end_line) {
                const contextInfo = `Showing lines ${fileData.start_line}-${fileData.end_line} of ${fileData.total_lines} (±${fileData.context_size || 100} lines around line ${fileData.line_number || highlightLine})`;
                codeHTML += `<div class="context-info">${contextInfo}</div>\n`;
                lineNumbersHTML += `<div class="context-info-spacer"></div>\n`;
            }

            // Handle both old and new data formats
            const contentLines = fileData.content_lines || [];
            const targetLine = fileData.line_number || fileData.highlighted_line || highlightLine;

            contentLines.forEach(lineInfo => {
                const isHighlighted = lineInfo.highlighted || lineInfo.number === targetLine;

                if (isHighlighted) {
                    lineNumbersHTML += `<div class="highlighted-line-number">${lineInfo.number}</div>\n`;
                    codeHTML += `<div class="highlighted-line">${escapeHtml(lineInfo.content)} <span class="security-marker">← Security Issue</span></div>\n`;
                } else {
                    lineNumbersHTML += `<div>${lineInfo.number}</div>\n`;
                    codeHTML += `<div>${escapeHtml(lineInfo.content)}</div>\n`;
                }
            });

            lineNumbersEl.innerHTML = lineNumbersHTML;
            codeContentEl.innerHTML = codeHTML;

            // Scroll to highlighted line with better positioning
            setTimeout(() => {
                const highlightedLine = codeContentEl.querySelector('.highlighted-line');
                if (highlightedLine) {
                    // Scroll to center the highlighted line
                    const container = document.querySelector('.code-snippet-container');
                    if (container) {
                        const containerHeight = container.clientHeight;
                        const lineTop = highlightedLine.offsetTop;
                        const lineHeight = highlightedLine.clientHeight;

                        // Calculate scroll position to center the line
                        const scrollTop = lineTop - (containerHeight / 2) + (lineHeight / 2);
                        container.scrollTop = Math.max(0, scrollTop);
                    }
                }
            }, 100);

            // Update dialog info with comprehensive file stats
            const dialogInfo = document.querySelector('.code-dialog-info');
            if (dialogInfo && fileData.total_lines) {
                const contextSize = fileData.context_size || 100;
                const linesShown = contentLines.length;
                const filePath = fileData.file_path || 'Unknown file';
                dialogInfo.innerHTML = `
                    <span><i class="fas fa-file"></i> ${filePath}</span>
                    <span><i class="fas fa-list-ol"></i> ${fileData.total_lines} total lines</span>
                    <span><i class="fas fa-eye"></i> Showing ${linesShown} lines (±${contextSize})</span>
                    <span><i class="fas fa-crosshairs"></i> Line ${targetLine} highlighted</span>
                `;
            }
        }

        function showContentWarning(message) {
            console.log('⚠️ Showing content warning:', message);
            const dialogContent = document.querySelector('.code-dialog-content');

            // Remove existing warning
            const existingWarning = dialogContent.querySelector('.content-warning');
            if (existingWarning) {
                existingWarning.remove();
            }

            // Add new warning with less prominent styling
            const warning = document.createElement('div');
            warning.className = 'content-warning';
            warning.innerHTML = `
                <div style="
                    background: linear-gradient(135deg, hsl(45, 93%, 47%), hsl(45, 93%, 40%));
                    color: var(--text-on-dark);
                    padding: 0.5rem 1rem;
                    margin: 0;
                    font-size: 0.8125rem;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    border-bottom: 1px solid var(--glass-border);
                    font-weight: 500;
                    opacity: 0.95;
                ">
                    <i class="fas fa-info-circle" style="font-size: 0.875rem;"></i>
                    <span>ℹ️ SAMPLE CONTENT: ${message}</span>
                </div>
            `;

            dialogContent.insertBefore(warning, dialogContent.firstChild);
        }

        function generateSampleCode(filePath, lineNumber, severity) {
            // Fallback sample code generator when real content is unavailable
            const fileExtension = filePath.split('.').pop().toLowerCase();

            if (fileExtension === 'bicep') {
                return `// Azure Bicep Template
param location string = resourceGroup().location
param storageAccountName string

resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {
  name: storageAccountName
  location: location
  kind: 'StorageV2'
  sku: {
    name: 'Standard_LRS'  // ← Security issue on this line
  }
  properties: {
    allowBlobPublicAccess: true  // ← Potential security risk
    minimumTlsVersion: 'TLS1_0'  // ← Outdated TLS version
    supportsHttpsTrafficOnly: false
  }
}

output storageAccountId string = storageAccount.id`;
            } else if (fileExtension === 'tf') {
                return `# Terraform Configuration
resource "azurerm_storage_account" "example" {
  name                     = var.storage_account_name
  resource_group_name      = azurerm_resource_group.example.name
  location                 = azurerm_resource_group.example.location
  account_tier             = "Standard"
  account_replication_type = "LRS"

  # Security Configuration Issues
  allow_blob_public_access = true    # ← Line ${lineNumber}: Public access enabled
  min_tls_version         = "TLS1_0" # ← Outdated TLS version
  https_traffic_only      = false    # ← HTTP traffic allowed

  tags = {
    Environment = "Development"
  }
}`;
            } else {
                return `// Generic code example
function processData(input) {
    // Security issue: No input validation
    const result = eval(input);  // ← Line ${lineNumber}: Dangerous eval usage

    // Additional security concerns
    localStorage.setItem('data', result);  // ← Storing sensitive data
    console.log('Debug info:', result);    // ← Information disclosure

    return result;
}

// Usage example
const userInput = getUrlParameter('code');
const output = processData(userInput);`;
            }
        }

        function displayCodeWithLineNumbers(code, highlightLine) {
            const lines = code.split('\n');
            const lineNumbersEl = document.getElementById('line-numbers');
            const codeContentEl = document.getElementById('code-content');

            // Generate line numbers
            let lineNumbersHTML = '';
            let codeHTML = '';

            lines.forEach((line, index) => {
                const lineNum = index + 1;
                const isHighlighted = lineNum === parseInt(highlightLine);

                if (isHighlighted) {
                    lineNumbersHTML += `<div class="highlighted-line-number">${lineNum}</div>\n`;
                    codeHTML += `<div class="highlighted-line">${escapeHtml(line)}</div>\n`;
                } else {
                    lineNumbersHTML += `<div>${lineNum}</div>\n`;
                    codeHTML += `<div>${escapeHtml(line)}</div>\n`;
                }
            });

            lineNumbersEl.innerHTML = lineNumbersHTML;
            codeContentEl.innerHTML = codeHTML;

            // Scroll to highlighted line
            setTimeout(() => {
                const highlightedLine = codeContentEl.querySelector('.highlighted-line');
                if (highlightedLine) {
                    highlightedLine.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }, 100);
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function copyCodeSnippet() {
            const codeContent = document.getElementById('code-content').textContent;
            navigator.clipboard.writeText(codeContent).then(() => {
                const btn = event.target.closest('.code-dialog-btn');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
                btn.style.background = 'var(--success-green)';

                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.style.background = '';
                }, 2000);
            }).catch(() => {
                alert('Failed to copy code to clipboard');
            });
        }

        function closeCodeDialog(event) {
            if (event && event.target !== event.currentTarget) return;

            const dialog = document.getElementById('code-dialog-overlay');
            if (dialog) {
                // Clear dialog content to ensure fresh state for next open
                clearCodeDialogContent();

                // Hide dialog
                dialog.style.display = 'none';
                document.body.style.overflow = '';

                console.log('🚪 Code dialog closed and reset');
            }
        }

        function handleDialogKeydown(event) {
            if (event.key === 'Escape') {
                closeCodeDialog();
            }
        }

        // Initialize Glass UI effects
        function initGlassUI() {
            addGlassEffects();
            initLineNumberHighlighting();

            // Add smooth scrolling
            document.documentElement.style.scrollBehavior = 'smooth';

            // Add intersection observer for fade-in animations
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, { threshold: 0.1 });

            document.querySelectorAll('.severity-group, .stat-card').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(el);
            });
        }

        // Initialize from URL hash on page load
        window.addEventListener('load', function() {
            loadFromUrlHash();
            initGlassUI();
        });

        // Handle browser back/forward
        window.addEventListener('hashchange', function() {
            loadFromUrlHash();
        });

        // Copy code snippet function
        function copyCodeSnippet(button) {
            const codeSection = button.closest('.code-snippet-section');
            const codeLines = codeSection.querySelectorAll('.code-line, .highlighted-code-line');

            let codeText = '';
            codeLines.forEach(line => {
                const lineNumber = line.querySelector('.line-number').textContent.trim();
                const lineContent = line.querySelector('.line-content').textContent;
                codeText += `${lineNumber.padStart(4, ' ')}: ${lineContent}\n`;
            });

            navigator.clipboard.writeText(codeText).then(() => {
                // Show success feedback
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i> Copied!';
                button.style.background = 'var(--success-green)';

                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.style.background = 'var(--primary500)';
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy code:', err);
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = codeText;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i> Copied!';
                button.style.background = 'var(--success-green)';

                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.style.background = 'var(--primary500)';
                }, 2000);
            });
        }
    </script>

    <!-- Embedded File Content for Code Dialog -->
    
                <script type="application/json" data-finding-context="app-config.bicep_1" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;app-config.bicep&quot;, &quot;line_number&quot;: 1, &quot;total_lines&quot;: 35, &quot;start_line&quot;: 1, &quot;end_line&quot;: 35, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;param location string&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;param keyValues object[]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;var suffix = uniqueString(resourceGroup().id)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;var appConfigName = 'app-config-${suffix}'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;resource appConfig 'Microsoft.AppConfiguration/configurationStores@2023-09-01-preview' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;  name: appConfigName&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;  sku: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;    name: 'standard'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;    disableLocalAuth: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;    // This config is needed to be able to set the feature flags when local auth is disabled&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;    // https://github.com/Azure/AppConfiguration/issues/692&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;    dataPlaneProxy: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;      authenticationMode: 'Pass-through'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;  resource storedFlags 'keyValues' = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;    for item in keyValues: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;      name: item.name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;      properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;        value: item.value&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;        contentType: contains(item, 'contentType') ? item.contentType : ''&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;        tags: contains(item, 'tags') ? item.tags : {}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;  ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;output name string = appConfig.name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;output endpoint string = appConfig.properties.endpoint&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;&gt;&gt;&gt;    1: param location string\n       2: param keyValues object[]\n       3: \n       4: var suffix = uniqueString(resourceGroup().id)\n       5: var appConfigName = 'app-config-${suffix}'\n       6: \n       7: resource appConfig 'Microsoft.AppConfiguration/configurationStores@2023-09-01-preview' = {\n       8:   name: appConfigName\n       9:   location: location\n      10:   sku: {\n      11:     name: 'standard'\n      12:   }\n      13:   properties: {\n      14:     disableLocalAuth: true\n      15:     // This config is needed to be able to set the feature flags when local auth is disabled\n      16:     // https://github.com/Azure/AppConfiguration/issues/692\n      17:     dataPlaneProxy: {\n      18:       authenticationMode: 'Pass-through'\n      19:     }\n      20:   }\n      21: \n      22:   resource storedFlags 'keyValues' = [\n      23:     for item in keyValues: {\n      24:       name: item.name\n      25:       properties: {\n      26:         value: item.value\n      27:         contentType: contains(item, 'contentType') ? item.contentType : ''\n      28:         tags: contains(item, 'tags') ? item.tags : {}\n      29:       }\n      30:     }\n      31:   ]\n      32: }\n      33: \n      34: output name string = appConfig.name\n      35: output endpoint string = appConfig.properties.endpoint&quot;, &quot;highlighted_line_content&quot;: &quot;param location string&quot;}
                </script>
                <script type="application/json" data-finding-context="function-settings.bicep_1" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;function-settings.bicep&quot;, &quot;line_number&quot;: 1, &quot;total_lines&quot;: 117, &quot;start_line&quot;: 1, &quot;end_line&quot;: 101, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;param name string&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;param instance_name string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;param instance_config object&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;param owner string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;param app_insights_app_id string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;param log_analytics_workspace_id string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;param evidence_store_url string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;param liquid_requirement_url string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;@secure()&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;param app_insights_key string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;param cli_app_id string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;param authority string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;param app_config_endpoint string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;param func_storage_resource_id string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;param fuzz_storage_resource_id string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;param keyvault_name string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;param monitor_account_name string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;param functions_worker_runtime string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;param functions_extension_version string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;param enable_profiler bool&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;param signalRName string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;param funcStorageName string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;param liquid_app_tenant_id string = '72f988bf-86f1-41af-91ab-2d7cd011db47'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;param registration_app_id string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;param func_user_assigned_identity_client_id string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;// Settings for ADO access:&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;// Application ID for the ADO access app registration.&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;// This must live in the same tenant as the MSI used by the function app.&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;// - 'onefuzz-ado-access' this is currently shared between daily/canary/prod&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;//   so it is entered directly here to avoid having to pass it in as a parameter&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;param ado_access_client_id string = 'c77cd33b-8c5b-4d5c-a7a7-bdea70b062b1' &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 44, &quot;content&quot;: &quot;var telemetry = 'd7a73cf4-5a1a-4030-85e1-e5b25867e45a'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 45, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 46, &quot;content&quot;: &quot;resource funcStorage 'Microsoft.Storage/storageAccounts@2021-08-01' existing = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 47, &quot;content&quot;: &quot;  name: funcStorageName&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 48, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 49, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 50, &quot;content&quot;: &quot;resource function 'Microsoft.Web/sites@2021-02-01' existing = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 51, &quot;content&quot;: &quot;  name: name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 52, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 53, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 54, &quot;content&quot;: &quot;var enable_profilers = enable_profiler ? {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 55, &quot;content&quot;: &quot;  APPINSIGHTS_PROFILERFEATURE_VERSION: '1.0.0'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 56, &quot;content&quot;: &quot;  DiagnosticServices_EXTENSION_VERSION: '~3'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 57, &quot;content&quot;: &quot;} : {}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 58, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 59, &quot;content&quot;: &quot;resource functionSettings 'Microsoft.Web/sites/config@2021-03-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 60, &quot;content&quot;: &quot;  parent: function&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 61, &quot;content&quot;: &quot;  name: 'appsettings'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 62, &quot;content&quot;: &quot;  properties: union({&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 63, &quot;content&quot;: &quot;      FUNCTIONS_EXTENSION_VERSION: functions_extension_version&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 64, &quot;content&quot;: &quot;      FUNCTIONS_WORKER_RUNTIME: functions_worker_runtime&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 65, &quot;content&quot;: &quot;      FUNCTIONS_WORKER_PROCESS_COUNT: '1'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 66, &quot;content&quot;: &quot;      APPINSIGHTS_INSTRUMENTATIONKEY: app_insights_key&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 67, &quot;content&quot;: &quot;      APPINSIGHTS_APPID: app_insights_app_id&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 68, &quot;content&quot;: &quot;      LOG_ANALYTICS_WORKSPACE_ID: log_analytics_workspace_id&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 69, &quot;content&quot;: &quot;      ONEFUZZ_TELEMETRY: telemetry&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 70, &quot;content&quot;: &quot;      AzureWebJobsStorage__accountName: funcStorage.name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 71, &quot;content&quot;: &quot;      CLI_APP_ID: cli_app_id&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 72, &quot;content&quot;: &quot;      AUTHORITY: authority&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 73, &quot;content&quot;: &quot;      AzureWebJobsDisableHomepage: 'true'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 74, &quot;content&quot;: &quot;      // nable MA agent to send logs to Log Analytics&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 75, &quot;content&quot;: &quot;      WEBSITE_FIRST_PARTY_ID: 'AntMDS'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 76, &quot;content&quot;: &quot;      WEBSITE_RUN_FROM_PACKAGE: '1'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 77, &quot;content&quot;: &quot;      // use managed identity for accessing Azure SignalR: https://learn.microsoft.com/en-us/azure/azure-signalr/signalr-howto-authorize-managed-identity#use-a-system-assigned-identity-1&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 78, &quot;content&quot;: &quot;      AzureSignalRConnectionString__serviceUri: 'https://${signalRName}.service.signalr.net'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 79, &quot;content&quot;: &quot;      AzureSignalRServiceTransportType: 'Transient'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 80, &quot;content&quot;: &quot;      APPCONFIGURATION_ENDPOINT: app_config_endpoint&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 81, &quot;content&quot;: &quot;      ONEFUZZ_INSTANCE_NAME: instance_name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 82, &quot;content&quot;: &quot;      ONEFUZZ_INSTANCE: 'https://${instance_name}.azurewebsites.net'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 83, &quot;content&quot;: &quot;      ONEFUZZ_RESOURCE_GROUP: resourceGroup().id&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 84, &quot;content&quot;: &quot;      ONEFUZZ_DATA_STORAGE: fuzz_storage_resource_id&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 85, &quot;content&quot;: &quot;      ONEFUZZ_FUNC_STORAGE: func_storage_resource_id&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 86, &quot;content&quot;: &quot;      ONEFUZZ_MONITOR: monitor_account_name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 87, &quot;content&quot;: &quot;      ONEFUZZ_KEYVAULT: keyvault_name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 88, &quot;content&quot;: &quot;      ONEFUZZ_OWNER: owner&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 89, &quot;content&quot;: &quot;      ONEFUZZ_EVIDENCE_STORE_URL: evidence_store_url&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 90, &quot;content&quot;: &quot;      ONEFUZZ_LIQUID_REQUIREMENT_URL: liquid_requirement_url&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 91, &quot;content&quot;: &quot;      // we rely on easy-auth for authentication but perform our own authorization&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 92, &quot;content&quot;: &quot;      // see: https://eng.ms/docs/cloud-ai-platform/devdiv/serverless-paas-balam/serverless-paas-benbyrd/app-service-web-apps/antares-trouble-shooting-guides-tsg/partnertsgs/easyauth&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 93, &quot;content&quot;: &quot;      WEBSITE_AUTH_CUSTOM_AUTHORIZATION: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 94, &quot;content&quot;: &quot;      LIQUID_APP_TENANT_ID: liquid_app_tenant_id&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 95, &quot;content&quot;: &quot;      REGISTRATION_APP_ID: registration_app_id&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 96, &quot;content&quot;: &quot;      // this configures EasyAuth to use FIC to access the app registration&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 97, &quot;content&quot;: &quot;      // see: https://eng.ms/docs/cloud-ai-platform/devdiv/serverless-paas-balam/serverless-paas-vikr/app-service-web-apps/antares-trouble-shooting-guides-tsg/partnertsgs/easyauth-usemiasfic&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 98, &quot;content&quot;: &quot;      OVERRIDE_USE_MI_FIC_ASSERTION_CLIENTID: func_user_assigned_identity_client_id&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 99, &quot;content&quot;: &quot;      // enable use of MISE by EasyAuth&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 100, &quot;content&quot;: &quot;      // see: https://eng.ms/docs/cloud-ai-platform/devdiv/serverless-paas-balam/serverless-paas-vikr/app-service-web-apps/antares-trouble-shooting-guides-tsg/partnertsgs/enablemise&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 101, &quot;content&quot;: &quot;      WEBSITE_AAD_ENABLE_MISE: true&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;&gt;&gt;&gt;    1: param name string\n       2: param instance_name string\n       3: param instance_config object\n       4: param owner string\n       5: param app_insights_app_id string\n       6: param log_analytics_workspace_id string\n       7: param evidence_store_url string\n       8: param liquid_requirement_url string\n       9: @secure()\n      10: param app_insights_key string\n      11: \n      12: param cli_app_id string\n      13: param authority string\n      14: \n      15: param app_config_endpoint string\n      16: \n      17: param func_storage_resource_id string\n      18: param fuzz_storage_resource_id string\n      19: \n      20: param keyvault_name string\n      21: \n      22: param monitor_account_name string\n      23: \n      24: param functions_worker_runtime string\n      25: param functions_extension_version string\n      26: \n      27: param enable_profiler bool\n      28: \n      29: param signalRName string\n      30: param funcStorageName string\n      31: \n      32: param liquid_app_tenant_id string = '72f988bf-86f1-41af-91ab-2d7cd011db47'\n      33: param registration_app_id string\n      34: \n      35: param func_user_assigned_identity_client_id string\n      36: \n      37: // Settings for ADO access:\n      38: // Application ID for the ADO access app registration.\n      39: // This must live in the same tenant as the MSI used by the function app.\n      40: // - 'onefuzz-ado-access' this is currently shared between daily/canary/prod\n      41: //   so it is entered directly here to avoid having to pass it in as a parameter\n      42: param ado_access_client_id string = 'c77cd33b-8c5b-4d5c-a7a7-bdea70b062b1' \n      43: \n      44: var telemetry = 'd7a73cf4-5a1a-4030-85e1-e5b25867e45a'\n      45: \n      46: resource funcStorage 'Microsoft.Storage/storageAccounts@2021-08-01' existing = {\n      47:   name: funcStorageName\n      48: }\n      49: \n      50: resource function 'Microsoft.Web/sites@2021-02-01' existing = {\n      51:   name: name\n      52: }\n      53: \n      54: var enable_profilers = enable_profiler ? {\n      55:   APPINSIGHTS_PROFILERFEATURE_VERSION: '1.0.0'\n      56:   DiagnosticServices_EXTENSION_VERSION: '~3'\n      57: } : {}\n      58: \n      59: resource functionSettings 'Microsoft.Web/sites/config@2021-03-01' = {\n      60:   parent: function\n      61:   name: 'appsettings'\n      62:   properties: union({\n      63:       FUNCTIONS_EXTENSION_VERSION: functions_extension_version\n      64:       FUNCTIONS_WORKER_RUNTIME: functions_worker_runtime\n      65:       FUNCTIONS_WORKER_PROCESS_COUNT: '1'\n      66:       APPINSIGHTS_INSTRUMENTATIONKEY: app_insights_key\n      67:       APPINSIGHTS_APPID: app_insights_app_id\n      68:       LOG_ANALYTICS_WORKSPACE_ID: log_analytics_workspace_id\n      69:       ONEFUZZ_TELEMETRY: telemetry\n      70:       AzureWebJobsStorage__accountName: funcStorage.name\n      71:       CLI_APP_ID: cli_app_id\n      72:       AUTHORITY: authority\n      73:       AzureWebJobsDisableHomepage: 'true'\n      74:       // nable MA agent to send logs to Log Analytics\n      75:       WEBSITE_FIRST_PARTY_ID: 'AntMDS'\n      76:       WEBSITE_RUN_FROM_PACKAGE: '1'\n      77:       // use managed identity for accessing Azure SignalR: https://learn.microsoft.com/en-us/azure/azure-signalr/signalr-howto-authorize-managed-identity#use-a-system-assigned-identity-1\n      78:       AzureSignalRConnectionString__serviceUri: 'https://${signalRName}.service.signalr.net'\n      79:       AzureSignalRServiceTransportType: 'Transient'\n      80:       APPCONFIGURATION_ENDPOINT: app_config_endpoint\n      81:       ONEFUZZ_INSTANCE_NAME: instance_name\n      82:       ONEFUZZ_INSTANCE: 'https://${instance_name}.azurewebsites.net'\n      83:       ONEFUZZ_RESOURCE_GROUP: resourceGroup().id\n      84:       ONEFUZZ_DATA_STORAGE: fuzz_storage_resource_id\n      85:       ONEFUZZ_FUNC_STORAGE: func_storage_resource_id\n      86:       ONEFUZZ_MONITOR: monitor_account_name\n      87:       ONEFUZZ_KEYVAULT: keyvault_name\n      88:       ONEFUZZ_OWNER: owner\n      89:       ONEFUZZ_EVIDENCE_STORE_URL: evidence_store_url\n      90:       ONEFUZZ_LIQUID_REQUIREMENT_URL: liquid_requirement_url\n      91:       // we rely on easy-auth for authentication but perform our own authorization\n      92:       // see: https://eng.ms/docs/cloud-ai-platform/devdiv/serverless-paas-balam/serverless-paas-benbyrd/app-service-web-apps/antares-trouble-shooting-guides-tsg/partnertsgs/easyauth\n      93:       WEBSITE_AUTH_CUSTOM_AUTHORIZATION: true\n      94:       LIQUID_APP_TENANT_ID: liquid_app_tenant_id\n      95:       REGISTRATION_APP_ID: registration_app_id\n      96:       // this configures EasyAuth to use FIC to access the app registration\n      97:       // see: https://eng.ms/docs/cloud-ai-platform/devdiv/serverless-paas-balam/serverless-paas-vikr/app-service-web-apps/antares-trouble-shooting-guides-tsg/partnertsgs/easyauth-usemiasfic\n      98:       OVERRIDE_USE_MI_FIC_ASSERTION_CLIENTID: func_user_assigned_identity_client_id\n      99:       // enable use of MISE by EasyAuth\n     100:       // see: https://eng.ms/docs/cloud-ai-platform/devdiv/serverless-paas-balam/serverless-paas-vikr/app-service-web-apps/antares-trouble-shooting-guides-tsg/partnertsgs/enablemise\n     101:       WEBSITE_AAD_ENABLE_MISE: true&quot;, &quot;highlighted_line_content&quot;: &quot;param name string&quot;}
                </script>
                <script type="application/json" data-finding-context="keyvault.bicep_1" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;keyvault.bicep&quot;, &quot;line_number&quot;: 1, &quot;total_lines&quot;: 59, &quot;start_line&quot;: 1, &quot;end_line&quot;: 59, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;param location string&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;param tenantId string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;@secure()&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;param secrets object = {}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;param hubSubnetId string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;module ipRules './ip-rules.bicep' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;  name: 'ipRules'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;var sawIpRules = ipRules.outputs.sawVnetRules&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;var keyVaultName = 'of-kv-${uniqueString(resourceGroup().id)}'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;var secretNames = objectKeys(secrets)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;resource keyVault 'Microsoft.KeyVault/vaults@2021-10-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;  name: keyVaultName&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;    enabledForDiskEncryption: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;    enabledForDeployment: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;    enabledForTemplateDeployment: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;    enableRbacAuthorization: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;    sku: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;      family: 'A'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;      name: 'standard'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;    networkAcls: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;      defaultAction: 'Allow'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;      bypass: 'AzureServices'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;      ipRules: sawIpRules&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;      virtualNetworkRules: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;        {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;          id: hubSubnetId&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;      ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;    accessPolicies: []&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;    tenantId: tenantId&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;  &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;  resource storedSecrets 'secrets' = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;    for name in secretNames: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;      name: name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 44, &quot;content&quot;: &quot;      properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 45, &quot;content&quot;: &quot;        value: string(secrets[name])&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 46, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 47, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 48, &quot;content&quot;: &quot;  ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 49, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 50, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 51, &quot;content&quot;: &quot;var numSecrets = length(secretNames)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 52, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 53, &quot;content&quot;: &quot;output keyVaultName string = keyVault.name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 54, &quot;content&quot;: &quot;output keyVaultSecretUris object[] = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 55, &quot;content&quot;: &quot;  for i in range(0, numSecrets): {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 56, &quot;content&quot;: &quot;    name: keyVault::storedSecrets[i].name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 57, &quot;content&quot;: &quot;    uri: keyVault::storedSecrets[i].properties.secretUriWithVersion&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 58, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 59, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;&gt;&gt;&gt;    1: param location string\n       2: param tenantId string\n       3: @secure()\n       4: param secrets object = {}\n       5: param hubSubnetId string\n       6: \n       7: module ipRules './ip-rules.bicep' = {\n       8:   name: 'ipRules'\n       9: }\n      10: var sawIpRules = ipRules.outputs.sawVnetRules\n      11: \n      12: var keyVaultName = 'of-kv-${uniqueString(resourceGroup().id)}'\n      13: var secretNames = objectKeys(secrets)\n      14: \n      15: resource keyVault 'Microsoft.KeyVault/vaults@2021-10-01' = {\n      16:   name: keyVaultName\n      17:   location: location\n      18:   properties: {\n      19:     enabledForDiskEncryption: false\n      20:     enabledForDeployment: true\n      21:     enabledForTemplateDeployment: true\n      22:     enableRbacAuthorization: true\n      23:     sku: {\n      24:       family: 'A'\n      25:       name: 'standard'\n      26:     }\n      27:     networkAcls: {\n      28:       defaultAction: 'Allow'\n      29:       bypass: 'AzureServices'\n      30:       ipRules: sawIpRules\n      31:       virtualNetworkRules: [\n      32:         {\n      33:           id: hubSubnetId\n      34:         }\n      35:       ]\n      36:     }\n      37:     accessPolicies: []\n      38:     tenantId: tenantId\n      39:   }\n      40:   \n      41:   resource storedSecrets 'secrets' = [\n      42:     for name in secretNames: {\n      43:       name: name\n      44:       properties: {\n      45:         value: string(secrets[name])\n      46:       }\n      47:     }\n      48:   ]\n      49: }\n      50: \n      51: var numSecrets = length(secretNames)\n      52: \n      53: output keyVaultName string = keyVault.name\n      54: output keyVaultSecretUris object[] = [\n      55:   for i in range(0, numSecrets): {\n      56:     name: keyVault::storedSecrets[i].name\n      57:     uri: keyVault::storedSecrets[i].properties.secretUriWithVersion\n      58:   }\n      59: ]&quot;, &quot;highlighted_line_content&quot;: &quot;param location string&quot;}
                </script>
                <script type="application/json" data-finding-context="storage-accounts.bicep_1" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;storage-accounts.bicep&quot;, &quot;line_number&quot;: 1, &quot;total_lines&quot;: 204, &quot;start_line&quot;: 1, &quot;end_line&quot;: 101, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;param owner string&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;param location string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;param cors_origins array&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;param hubSubnetId string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;param storageAccountNameFunc string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;param storageAccountNameFuzz string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;param storageAccountsConfigCorpus object[]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;module ipRules './ip-rules.bicep' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;  name: 'ipRules'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;var corpNetAndSawIpRules = ipRules.outputs.combinedRules&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;// Func storage&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;resource storageAccountFunc 'Microsoft.Storage/storageAccounts@2021-08-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;  name: storageAccountNameFunc&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;  sku: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;    name: 'Standard_LRS'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;  kind: 'StorageV2'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;    supportsHttpsTrafficOnly: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;    accessTier: 'Hot'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;    allowBlobPublicAccess: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;    isLocalUserEnabled: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;    allowSharedKeyAccess: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;    networkAcls: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;      bypass: 'AzureServices, Logging, Metrics'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;      defaultAction: 'Allow'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;      ipRules: corpNetAndSawIpRules&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;      virtualNetworkRules: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;        {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;          id: hubSubnetId&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;          action: 'Allow'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;      ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;  tags: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;    OWNER: owner&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 44, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 45, &quot;content&quot;: &quot;  resource blobServicesFunc 'blobServices' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 46, &quot;content&quot;: &quot;    name: 'default'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 47, &quot;content&quot;: &quot;    properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 48, &quot;content&quot;: &quot;      deleteRetentionPolicy: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 49, &quot;content&quot;: &quot;        enabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 50, &quot;content&quot;: &quot;        days: 30&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 51, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 52, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 53, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 54, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 55, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 56, &quot;content&quot;: &quot;// Fuzz storage (default)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 57, &quot;content&quot;: &quot;var fuzzStorageProperties = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 58, &quot;content&quot;: &quot;  supportsHttpsTrafficOnly: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 59, &quot;content&quot;: &quot;  accessTier: 'Hot'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 60, &quot;content&quot;: &quot;  allowBlobPublicAccess: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 61, &quot;content&quot;: &quot;  isLocalUserEnabled: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 62, &quot;content&quot;: &quot;  allowSharedKeyAccess: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 63, &quot;content&quot;: &quot;  networkAcls: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 64, &quot;content&quot;: &quot;    bypass: 'AzureServices, Logging, Metrics'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 65, &quot;content&quot;: &quot;    defaultAction: 'Allow'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 66, &quot;content&quot;: &quot;    ipRules: corpNetAndSawIpRules&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 67, &quot;content&quot;: &quot;    virtualNetworkRules: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 68, &quot;content&quot;: &quot;      {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 69, &quot;content&quot;: &quot;        id: hubSubnetId&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 70, &quot;content&quot;: &quot;        action: 'Allow'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 71, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 72, &quot;content&quot;: &quot;    ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 73, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 74, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 75, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 76, &quot;content&quot;: &quot;resource storageAccountFuzz 'Microsoft.Storage/storageAccounts@2021-08-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 77, &quot;content&quot;: &quot;  name: storageAccountNameFuzz&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 78, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 79, &quot;content&quot;: &quot;  sku: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 80, &quot;content&quot;: &quot;    name: 'Standard_LRS'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 81, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 82, &quot;content&quot;: &quot;  kind: 'StorageV2'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 83, &quot;content&quot;: &quot;  properties: fuzzStorageProperties&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 84, &quot;content&quot;: &quot;  tags: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 85, &quot;content&quot;: &quot;    OWNER: owner&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 86, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 87, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 88, &quot;content&quot;: &quot;  resource blobServicesFuzz 'blobServices' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 89, &quot;content&quot;: &quot;    name: 'default'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 90, &quot;content&quot;: &quot;    properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 91, &quot;content&quot;: &quot;      deleteRetentionPolicy: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 92, &quot;content&quot;: &quot;        enabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 93, &quot;content&quot;: &quot;        days: 30&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 94, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 95, &quot;content&quot;: &quot;      cors: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 96, &quot;content&quot;: &quot;        corsRules: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 97, &quot;content&quot;: &quot;          {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 98, &quot;content&quot;: &quot;            allowedOrigins: cors_origins&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 99, &quot;content&quot;: &quot;            allowedMethods: ['GET', 'OPTIONS', 'HEAD']&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 100, &quot;content&quot;: &quot;            maxAgeInSeconds: 300&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 101, &quot;content&quot;: &quot;            allowedHeaders: ['*']&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;&gt;&gt;&gt;    1: param owner string\n       2: param location string\n       3: param cors_origins array\n       4: param hubSubnetId string\n       5: \n       6: param storageAccountNameFunc string\n       7: param storageAccountNameFuzz string\n       8: param storageAccountsConfigCorpus object[]\n       9: \n      10: module ipRules './ip-rules.bicep' = {\n      11:   name: 'ipRules'\n      12: }\n      13: var corpNetAndSawIpRules = ipRules.outputs.combinedRules\n      14: \n      15: // Func storage\n      16: resource storageAccountFunc 'Microsoft.Storage/storageAccounts@2021-08-01' = {\n      17:   name: storageAccountNameFunc\n      18:   location: location\n      19:   sku: {\n      20:     name: 'Standard_LRS'\n      21:   }\n      22:   kind: 'StorageV2'\n      23:   properties: {\n      24:     supportsHttpsTrafficOnly: true\n      25:     accessTier: 'Hot'\n      26:     allowBlobPublicAccess: false\n      27:     isLocalUserEnabled: false\n      28:     allowSharedKeyAccess: false\n      29:     networkAcls: {\n      30:       bypass: 'AzureServices, Logging, Metrics'\n      31:       defaultAction: 'Allow'\n      32:       ipRules: corpNetAndSawIpRules\n      33:       virtualNetworkRules: [\n      34:         {\n      35:           id: hubSubnetId\n      36:           action: 'Allow'\n      37:         }\n      38:       ]\n      39:     }\n      40:   }\n      41:   tags: {\n      42:     OWNER: owner\n      43:   }\n      44: \n      45:   resource blobServicesFunc 'blobServices' = {\n      46:     name: 'default'\n      47:     properties: {\n      48:       deleteRetentionPolicy: {\n      49:         enabled: true\n      50:         days: 30\n      51:       }\n      52:     }\n      53:   }\n      54: }\n      55: \n      56: // Fuzz storage (default)\n      57: var fuzzStorageProperties = {\n      58:   supportsHttpsTrafficOnly: true\n      59:   accessTier: 'Hot'\n      60:   allowBlobPublicAccess: false\n      61:   isLocalUserEnabled: false\n      62:   allowSharedKeyAccess: false\n      63:   networkAcls: {\n      64:     bypass: 'AzureServices, Logging, Metrics'\n      65:     defaultAction: 'Allow'\n      66:     ipRules: corpNetAndSawIpRules\n      67:     virtualNetworkRules: [\n      68:       {\n      69:         id: hubSubnetId\n      70:         action: 'Allow'\n      71:       }\n      72:     ]\n      73:   }\n      74: }\n      75: \n      76: resource storageAccountFuzz 'Microsoft.Storage/storageAccounts@2021-08-01' = {\n      77:   name: storageAccountNameFuzz\n      78:   location: location\n      79:   sku: {\n      80:     name: 'Standard_LRS'\n      81:   }\n      82:   kind: 'StorageV2'\n      83:   properties: fuzzStorageProperties\n      84:   tags: {\n      85:     OWNER: owner\n      86:   }\n      87: \n      88:   resource blobServicesFuzz 'blobServices' = {\n      89:     name: 'default'\n      90:     properties: {\n      91:       deleteRetentionPolicy: {\n      92:         enabled: true\n      93:         days: 30\n      94:       }\n      95:       cors: {\n      96:         corsRules: [\n      97:           {\n      98:             allowedOrigins: cors_origins\n      99:             allowedMethods: ['GET', 'OPTIONS', 'HEAD']\n     100:             maxAgeInSeconds: 300\n     101:             allowedHeaders: ['*']&quot;, &quot;highlighted_line_content&quot;: &quot;param owner string&quot;}
                </script>
                <script type="application/json" data-finding-context="function.bicep_4" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;function.bicep&quot;, &quot;line_number&quot;: 4, &quot;total_lines&quot;: 150, &quot;start_line&quot;: 1, &quot;end_line&quot;: 104, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;param name string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;param location string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;param owner string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;param cors_origins array&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;param server_farm_id string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;param client_id string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;param app_func_issuer string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;param app_func_audiences array&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;param use_windows bool&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;param enable_remote_debugging bool&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;param logs_storage string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;param signedExpiry string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;param hubSubnetId string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;@description('The degree of severity for diagnostics logs.')&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;@allowed([&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;  'Verbose'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;  'Information'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;  'Warning'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;  'Error'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;])&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;param diagnostics_log_level string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;param log_retention int&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;param linux_fx_version string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;var siteconfig = (use_windows) ? {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;} : {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;  linuxFxVersion: linux_fx_version&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;var storage_account_sas = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;  signedExpiry: signedExpiry&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;  signedPermission: 'rwdlacup'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;  signedResourceTypes: 'sco'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;  signedServices: 'bfqt'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;var commonSiteConfig = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;  alwaysOn: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;  defaultDocuments: []&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 44, &quot;content&quot;: &quot;  httpLoggingEnabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 45, &quot;content&quot;: &quot;  logsDirectorySizeLimit: 100&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 46, &quot;content&quot;: &quot;  detailedErrorLoggingEnabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 47, &quot;content&quot;: &quot;  http20Enabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 48, &quot;content&quot;: &quot;  ftpsState: 'Disabled'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 49, &quot;content&quot;: &quot;  use32BitWorkerProcess: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 50, &quot;content&quot;: &quot;  healthCheckPath: '/api/config'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 51, &quot;content&quot;: &quot;  cors: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 52, &quot;content&quot;: &quot;    allowedOrigins: cors_origins&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 53, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 54, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 55, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 56, &quot;content&quot;: &quot;var extraProperties = (use_windows &amp;&amp; enable_remote_debugging) ? {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 57, &quot;content&quot;: &quot;  netFrameworkVersion: 'v8.0'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 58, &quot;content&quot;: &quot;  remoteDebuggingEnabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 59, &quot;content&quot;: &quot;  remoteDebuggingVersion: 'VS2022'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 60, &quot;content&quot;: &quot;} : {}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 61, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 62, &quot;content&quot;: &quot;resource funcStorage 'Microsoft.Storage/storageAccounts@2021-08-01' existing = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 63, &quot;content&quot;: &quot;  name: logs_storage&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 64, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 65, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 66, &quot;content&quot;: &quot;resource functionUserAssignedMSI 'Microsoft.ManagedIdentity/userAssignedIdentities@2023-01-31' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 67, &quot;content&quot;: &quot;  name: '${name}-user-assigned-msi'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 68, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 69, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 70, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 71, &quot;content&quot;: &quot;resource function 'Microsoft.Web/sites@2021-03-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 72, &quot;content&quot;: &quot;  name: name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 73, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 74, &quot;content&quot;: &quot;  kind: (use_windows) ? 'functionapp' : 'functionapp,linux'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 75, &quot;content&quot;: &quot;  tags: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 76, &quot;content&quot;: &quot;    OWNER: owner&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 77, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 78, &quot;content&quot;: &quot;  identity: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 79, &quot;content&quot;: &quot;    type: 'SystemAssigned, UserAssigned'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 80, &quot;content&quot;: &quot;    userAssignedIdentities: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 81, &quot;content&quot;: &quot;      '${functionUserAssignedMSI.id}': {}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 82, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 83, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 84, &quot;content&quot;: &quot;  properties: union(&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 85, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 86, &quot;content&quot;: &quot;      siteConfig: union(siteconfig, commonSiteConfig)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 87, &quot;content&quot;: &quot;      httpsOnly: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 88, &quot;content&quot;: &quot;      serverFarmId: server_farm_id&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 89, &quot;content&quot;: &quot;      clientAffinityEnabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 90, &quot;content&quot;: &quot;      virtualNetworkSubnetId: hubSubnetId&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 91, &quot;content&quot;: &quot;      vnetContentShareEnabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 92, &quot;content&quot;: &quot;      vnetImagePullEnabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 93, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 94, &quot;content&quot;: &quot;    extraProperties&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 95, &quot;content&quot;: &quot;  )&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 96, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 97, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 98, &quot;content&quot;: &quot;resource funcAuthSettings 'Microsoft.Web/sites/config@2021-03-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 99, &quot;content&quot;: &quot;  name: 'authsettingsV2'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 100, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 101, &quot;content&quot;: &quot;    login: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 102, &quot;content&quot;: &quot;      tokenStore: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 103, &quot;content&quot;: &quot;        enabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 104, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: param name string\n       2: param location string\n       3: param owner string\n&gt;&gt;&gt;    4: param cors_origins array\n       5: \n       6: param server_farm_id string\n       7: param client_id string\n       8: param app_func_issuer string\n       9: param app_func_audiences array\n      10: param use_windows bool\n      11: param enable_remote_debugging bool\n      12: \n      13: param logs_storage string\n      14: param signedExpiry string\n      15: param hubSubnetId string\n      16: \n      17: @description('The degree of severity for diagnostics logs.')\n      18: @allowed([\n      19:   'Verbose'\n      20:   'Information'\n      21:   'Warning'\n      22:   'Error'\n      23: ])\n      24: param diagnostics_log_level string\n      25: param log_retention int\n      26: param linux_fx_version string\n      27: \n      28: var siteconfig = (use_windows) ? {\n      29: } : {\n      30:   linuxFxVersion: linux_fx_version\n      31: }\n      32: \n      33: var storage_account_sas = {\n      34:   signedExpiry: signedExpiry\n      35:   signedPermission: 'rwdlacup'\n      36:   signedResourceTypes: 'sco'\n      37:   signedServices: 'bfqt'\n      38: }\n      39: \n      40: \n      41: var commonSiteConfig = {\n      42:   alwaysOn: true\n      43:   defaultDocuments: []\n      44:   httpLoggingEnabled: true\n      45:   logsDirectorySizeLimit: 100\n      46:   detailedErrorLoggingEnabled: true\n      47:   http20Enabled: true\n      48:   ftpsState: 'Disabled'\n      49:   use32BitWorkerProcess: false\n      50:   healthCheckPath: '/api/config'\n      51:   cors: {\n      52:     allowedOrigins: cors_origins\n      53:   }\n      54: }\n      55: \n      56: var extraProperties = (use_windows &amp;&amp; enable_remote_debugging) ? {\n      57:   netFrameworkVersion: 'v8.0'\n      58:   remoteDebuggingEnabled: true\n      59:   remoteDebuggingVersion: 'VS2022'\n      60: } : {}\n      61: \n      62: resource funcStorage 'Microsoft.Storage/storageAccounts@2021-08-01' existing = {\n      63:   name: logs_storage\n      64: }\n      65: \n      66: resource functionUserAssignedMSI 'Microsoft.ManagedIdentity/userAssignedIdentities@2023-01-31' = {\n      67:   name: '${name}-user-assigned-msi'\n      68:   location: location\n      69: }\n      70: \n      71: resource function 'Microsoft.Web/sites@2021-03-01' = {\n      72:   name: name\n      73:   location: location\n      74:   kind: (use_windows) ? 'functionapp' : 'functionapp,linux'\n      75:   tags: {\n      76:     OWNER: owner\n      77:   }\n      78:   identity: {\n      79:     type: 'SystemAssigned, UserAssigned'\n      80:     userAssignedIdentities: {\n      81:       '${functionUserAssignedMSI.id}': {}\n      82:     }\n      83:   }\n      84:   properties: union(\n      85:     {\n      86:       siteConfig: union(siteconfig, commonSiteConfig)\n      87:       httpsOnly: true\n      88:       serverFarmId: server_farm_id\n      89:       clientAffinityEnabled: true\n      90:       virtualNetworkSubnetId: hubSubnetId\n      91:       vnetContentShareEnabled: true\n      92:       vnetImagePullEnabled: true\n      93:     },\n      94:     extraProperties\n      95:   )\n      96: }\n      97: \n      98: resource funcAuthSettings 'Microsoft.Web/sites/config@2021-03-01' = {\n      99:   name: 'authsettingsV2'\n     100:   properties: {\n     101:     login: {\n     102:       tokenStore: {\n     103:         enabled: true\n     104:       }&quot;, &quot;highlighted_line_content&quot;: &quot;param cors_origins array&quot;}
                </script>
                <script type="application/json" data-finding-context="function.bicep_48" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;function.bicep&quot;, &quot;line_number&quot;: 48, &quot;total_lines&quot;: 150, &quot;start_line&quot;: 1, &quot;end_line&quot;: 148, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;param name string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;param location string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;param owner string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;param cors_origins array&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;param server_farm_id string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;param client_id string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;param app_func_issuer string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;param app_func_audiences array&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;param use_windows bool&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;param enable_remote_debugging bool&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;param logs_storage string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;param signedExpiry string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;param hubSubnetId string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;@description('The degree of severity for diagnostics logs.')&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;@allowed([&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;  'Verbose'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;  'Information'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;  'Warning'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;  'Error'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;])&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;param diagnostics_log_level string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;param log_retention int&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;param linux_fx_version string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;var siteconfig = (use_windows) ? {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;} : {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;  linuxFxVersion: linux_fx_version&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;var storage_account_sas = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;  signedExpiry: signedExpiry&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;  signedPermission: 'rwdlacup'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;  signedResourceTypes: 'sco'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;  signedServices: 'bfqt'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;var commonSiteConfig = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;  alwaysOn: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;  defaultDocuments: []&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 44, &quot;content&quot;: &quot;  httpLoggingEnabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 45, &quot;content&quot;: &quot;  logsDirectorySizeLimit: 100&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 46, &quot;content&quot;: &quot;  detailedErrorLoggingEnabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 47, &quot;content&quot;: &quot;  http20Enabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 48, &quot;content&quot;: &quot;  ftpsState: 'Disabled'&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 49, &quot;content&quot;: &quot;  use32BitWorkerProcess: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 50, &quot;content&quot;: &quot;  healthCheckPath: '/api/config'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 51, &quot;content&quot;: &quot;  cors: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 52, &quot;content&quot;: &quot;    allowedOrigins: cors_origins&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 53, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 54, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 55, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 56, &quot;content&quot;: &quot;var extraProperties = (use_windows &amp;&amp; enable_remote_debugging) ? {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 57, &quot;content&quot;: &quot;  netFrameworkVersion: 'v8.0'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 58, &quot;content&quot;: &quot;  remoteDebuggingEnabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 59, &quot;content&quot;: &quot;  remoteDebuggingVersion: 'VS2022'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 60, &quot;content&quot;: &quot;} : {}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 61, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 62, &quot;content&quot;: &quot;resource funcStorage 'Microsoft.Storage/storageAccounts@2021-08-01' existing = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 63, &quot;content&quot;: &quot;  name: logs_storage&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 64, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 65, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 66, &quot;content&quot;: &quot;resource functionUserAssignedMSI 'Microsoft.ManagedIdentity/userAssignedIdentities@2023-01-31' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 67, &quot;content&quot;: &quot;  name: '${name}-user-assigned-msi'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 68, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 69, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 70, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 71, &quot;content&quot;: &quot;resource function 'Microsoft.Web/sites@2021-03-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 72, &quot;content&quot;: &quot;  name: name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 73, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 74, &quot;content&quot;: &quot;  kind: (use_windows) ? 'functionapp' : 'functionapp,linux'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 75, &quot;content&quot;: &quot;  tags: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 76, &quot;content&quot;: &quot;    OWNER: owner&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 77, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 78, &quot;content&quot;: &quot;  identity: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 79, &quot;content&quot;: &quot;    type: 'SystemAssigned, UserAssigned'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 80, &quot;content&quot;: &quot;    userAssignedIdentities: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 81, &quot;content&quot;: &quot;      '${functionUserAssignedMSI.id}': {}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 82, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 83, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 84, &quot;content&quot;: &quot;  properties: union(&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 85, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 86, &quot;content&quot;: &quot;      siteConfig: union(siteconfig, commonSiteConfig)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 87, &quot;content&quot;: &quot;      httpsOnly: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 88, &quot;content&quot;: &quot;      serverFarmId: server_farm_id&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 89, &quot;content&quot;: &quot;      clientAffinityEnabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 90, &quot;content&quot;: &quot;      virtualNetworkSubnetId: hubSubnetId&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 91, &quot;content&quot;: &quot;      vnetContentShareEnabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 92, &quot;content&quot;: &quot;      vnetImagePullEnabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 93, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 94, &quot;content&quot;: &quot;    extraProperties&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 95, &quot;content&quot;: &quot;  )&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 96, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 97, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 98, &quot;content&quot;: &quot;resource funcAuthSettings 'Microsoft.Web/sites/config@2021-03-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 99, &quot;content&quot;: &quot;  name: 'authsettingsV2'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 100, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 101, &quot;content&quot;: &quot;    login: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 102, &quot;content&quot;: &quot;      tokenStore: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 103, &quot;content&quot;: &quot;        enabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 104, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 105, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 106, &quot;content&quot;: &quot;    globalValidation: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 107, &quot;content&quot;: &quot;      unauthenticatedClientAction: 'RedirectToLoginPage'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 108, &quot;content&quot;: &quot;      requireAuthentication: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 109, &quot;content&quot;: &quot;      excludedPaths: [ '/api/config' ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 110, &quot;content&quot;: &quot;      redirectToProvider: 'azureActiveDirectory'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 111, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 112, &quot;content&quot;: &quot;    httpSettings: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 113, &quot;content&quot;: &quot;      requireHttps: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 114, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 115, &quot;content&quot;: &quot;    identityProviders: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 116, &quot;content&quot;: &quot;      azureActiveDirectory: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 117, &quot;content&quot;: &quot;        enabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 118, &quot;content&quot;: &quot;        isAutoProvisioned: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 119, &quot;content&quot;: &quot;        registration: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 120, &quot;content&quot;: &quot;          clientId: client_id&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 121, &quot;content&quot;: &quot;          openIdIssuer: app_func_issuer&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 122, &quot;content&quot;: &quot;          clientSecretSettingName: 'OVERRIDE_USE_MI_FIC_ASSERTION_CLIENTID'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 123, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 124, &quot;content&quot;: &quot;        validation: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 125, &quot;content&quot;: &quot;          allowedAudiences: app_func_audiences&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 126, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 127, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 128, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 129, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 130, &quot;content&quot;: &quot;  parent: function&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 131, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 132, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 133, &quot;content&quot;: &quot;var sas = funcStorage.listAccountSas('2021-08-01', storage_account_sas)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 134, &quot;content&quot;: &quot;resource funcLogs 'Microsoft.Web/sites/config@2021-03-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 135, &quot;content&quot;: &quot;  name: 'logs'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 136, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 137, &quot;content&quot;: &quot;    applicationLogs: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 138, &quot;content&quot;: &quot;      azureBlobStorage: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 139, &quot;content&quot;: &quot;        level: diagnostics_log_level&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 140, &quot;content&quot;: &quot;        retentionInDays: log_retention&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 141, &quot;content&quot;: &quot;        sasUrl: '${funcStorage.properties.primaryEndpoints.blob}app-logs?${sas.accountSasToken}'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 142, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 143, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 144, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 145, &quot;content&quot;: &quot;  parent: function&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 146, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 147, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 148, &quot;content&quot;: &quot;output principalId string = function.identity.principalId&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: param name string\n       2: param location string\n       3: param owner string\n       4: param cors_origins array\n       5: \n       6: param server_farm_id string\n       7: param client_id string\n       8: param app_func_issuer string\n       9: param app_func_audiences array\n      10: param use_windows bool\n      11: param enable_remote_debugging bool\n      12: \n      13: param logs_storage string\n      14: param signedExpiry string\n      15: param hubSubnetId string\n      16: \n      17: @description('The degree of severity for diagnostics logs.')\n      18: @allowed([\n      19:   'Verbose'\n      20:   'Information'\n      21:   'Warning'\n      22:   'Error'\n      23: ])\n      24: param diagnostics_log_level string\n      25: param log_retention int\n      26: param linux_fx_version string\n      27: \n      28: var siteconfig = (use_windows) ? {\n      29: } : {\n      30:   linuxFxVersion: linux_fx_version\n      31: }\n      32: \n      33: var storage_account_sas = {\n      34:   signedExpiry: signedExpiry\n      35:   signedPermission: 'rwdlacup'\n      36:   signedResourceTypes: 'sco'\n      37:   signedServices: 'bfqt'\n      38: }\n      39: \n      40: \n      41: var commonSiteConfig = {\n      42:   alwaysOn: true\n      43:   defaultDocuments: []\n      44:   httpLoggingEnabled: true\n      45:   logsDirectorySizeLimit: 100\n      46:   detailedErrorLoggingEnabled: true\n      47:   http20Enabled: true\n&gt;&gt;&gt;   48:   ftpsState: 'Disabled'\n      49:   use32BitWorkerProcess: false\n      50:   healthCheckPath: '/api/config'\n      51:   cors: {\n      52:     allowedOrigins: cors_origins\n      53:   }\n      54: }\n      55: \n      56: var extraProperties = (use_windows &amp;&amp; enable_remote_debugging) ? {\n      57:   netFrameworkVersion: 'v8.0'\n      58:   remoteDebuggingEnabled: true\n      59:   remoteDebuggingVersion: 'VS2022'\n      60: } : {}\n      61: \n      62: resource funcStorage 'Microsoft.Storage/storageAccounts@2021-08-01' existing = {\n      63:   name: logs_storage\n      64: }\n      65: \n      66: resource functionUserAssignedMSI 'Microsoft.ManagedIdentity/userAssignedIdentities@2023-01-31' = {\n      67:   name: '${name}-user-assigned-msi'\n      68:   location: location\n      69: }\n      70: \n      71: resource function 'Microsoft.Web/sites@2021-03-01' = {\n      72:   name: name\n      73:   location: location\n      74:   kind: (use_windows) ? 'functionapp' : 'functionapp,linux'\n      75:   tags: {\n      76:     OWNER: owner\n      77:   }\n      78:   identity: {\n      79:     type: 'SystemAssigned, UserAssigned'\n      80:     userAssignedIdentities: {\n      81:       '${functionUserAssignedMSI.id}': {}\n      82:     }\n      83:   }\n      84:   properties: union(\n      85:     {\n      86:       siteConfig: union(siteconfig, commonSiteConfig)\n      87:       httpsOnly: true\n      88:       serverFarmId: server_farm_id\n      89:       clientAffinityEnabled: true\n      90:       virtualNetworkSubnetId: hubSubnetId\n      91:       vnetContentShareEnabled: true\n      92:       vnetImagePullEnabled: true\n      93:     },\n      94:     extraProperties\n      95:   )\n      96: }\n      97: \n      98: resource funcAuthSettings 'Microsoft.Web/sites/config@2021-03-01' = {\n      99:   name: 'authsettingsV2'\n     100:   properties: {\n     101:     login: {\n     102:       tokenStore: {\n     103:         enabled: true\n     104:       }\n     105:     }\n     106:     globalValidation: {\n     107:       unauthenticatedClientAction: 'RedirectToLoginPage'\n     108:       requireAuthentication: true\n     109:       excludedPaths: [ '/api/config' ]\n     110:       redirectToProvider: 'azureActiveDirectory'\n     111:     }\n     112:     httpSettings: {\n     113:       requireHttps: true\n     114:     }\n     115:     identityProviders: {\n     116:       azureActiveDirectory: {\n     117:         enabled: true\n     118:         isAutoProvisioned: false\n     119:         registration: {\n     120:           clientId: client_id\n     121:           openIdIssuer: app_func_issuer\n     122:           clientSecretSettingName: 'OVERRIDE_USE_MI_FIC_ASSERTION_CLIENTID'\n     123:         }\n     124:         validation: {\n     125:           allowedAudiences: app_func_audiences\n     126:         }\n     127:       }\n     128:     }\n     129:   }\n     130:   parent: function\n     131: }\n     132: \n     133: var sas = funcStorage.listAccountSas('2021-08-01', storage_account_sas)\n     134: resource funcLogs 'Microsoft.Web/sites/config@2021-03-01' = {\n     135:   name: 'logs'\n     136:   properties: {\n     137:     applicationLogs: {\n     138:       azureBlobStorage: {\n     139:         level: diagnostics_log_level\n     140:         retentionInDays: log_retention\n     141:         sasUrl: '${funcStorage.properties.primaryEndpoints.blob}app-logs?${sas.accountSasToken}'\n     142:       }\n     143:     }\n     144:   }\n     145:   parent: function\n     146: }\n     147: \n     148: output principalId string = function.identity.principalId&quot;, &quot;highlighted_line_content&quot;: &quot;ftpsState: 'Disabled'&quot;}
                </script>
                <script type="application/json" data-finding-context="function.bicep_58" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;function.bicep&quot;, &quot;line_number&quot;: 58, &quot;total_lines&quot;: 150, &quot;start_line&quot;: 1, &quot;end_line&quot;: 150, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;param name string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;param location string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;param owner string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;param cors_origins array&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;param server_farm_id string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;param client_id string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;param app_func_issuer string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;param app_func_audiences array&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;param use_windows bool&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;param enable_remote_debugging bool&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;param logs_storage string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;param signedExpiry string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;param hubSubnetId string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;@description('The degree of severity for diagnostics logs.')&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;@allowed([&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;  'Verbose'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;  'Information'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;  'Warning'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;  'Error'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;])&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;param diagnostics_log_level string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;param log_retention int&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;param linux_fx_version string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;var siteconfig = (use_windows) ? {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;} : {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;  linuxFxVersion: linux_fx_version&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;var storage_account_sas = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;  signedExpiry: signedExpiry&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;  signedPermission: 'rwdlacup'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;  signedResourceTypes: 'sco'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;  signedServices: 'bfqt'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;var commonSiteConfig = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;  alwaysOn: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;  defaultDocuments: []&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 44, &quot;content&quot;: &quot;  httpLoggingEnabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 45, &quot;content&quot;: &quot;  logsDirectorySizeLimit: 100&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 46, &quot;content&quot;: &quot;  detailedErrorLoggingEnabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 47, &quot;content&quot;: &quot;  http20Enabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 48, &quot;content&quot;: &quot;  ftpsState: 'Disabled'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 49, &quot;content&quot;: &quot;  use32BitWorkerProcess: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 50, &quot;content&quot;: &quot;  healthCheckPath: '/api/config'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 51, &quot;content&quot;: &quot;  cors: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 52, &quot;content&quot;: &quot;    allowedOrigins: cors_origins&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 53, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 54, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 55, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 56, &quot;content&quot;: &quot;var extraProperties = (use_windows &amp;&amp; enable_remote_debugging) ? {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 57, &quot;content&quot;: &quot;  netFrameworkVersion: 'v8.0'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 58, &quot;content&quot;: &quot;  remoteDebuggingEnabled: true&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 59, &quot;content&quot;: &quot;  remoteDebuggingVersion: 'VS2022'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 60, &quot;content&quot;: &quot;} : {}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 61, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 62, &quot;content&quot;: &quot;resource funcStorage 'Microsoft.Storage/storageAccounts@2021-08-01' existing = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 63, &quot;content&quot;: &quot;  name: logs_storage&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 64, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 65, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 66, &quot;content&quot;: &quot;resource functionUserAssignedMSI 'Microsoft.ManagedIdentity/userAssignedIdentities@2023-01-31' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 67, &quot;content&quot;: &quot;  name: '${name}-user-assigned-msi'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 68, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 69, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 70, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 71, &quot;content&quot;: &quot;resource function 'Microsoft.Web/sites@2021-03-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 72, &quot;content&quot;: &quot;  name: name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 73, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 74, &quot;content&quot;: &quot;  kind: (use_windows) ? 'functionapp' : 'functionapp,linux'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 75, &quot;content&quot;: &quot;  tags: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 76, &quot;content&quot;: &quot;    OWNER: owner&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 77, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 78, &quot;content&quot;: &quot;  identity: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 79, &quot;content&quot;: &quot;    type: 'SystemAssigned, UserAssigned'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 80, &quot;content&quot;: &quot;    userAssignedIdentities: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 81, &quot;content&quot;: &quot;      '${functionUserAssignedMSI.id}': {}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 82, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 83, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 84, &quot;content&quot;: &quot;  properties: union(&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 85, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 86, &quot;content&quot;: &quot;      siteConfig: union(siteconfig, commonSiteConfig)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 87, &quot;content&quot;: &quot;      httpsOnly: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 88, &quot;content&quot;: &quot;      serverFarmId: server_farm_id&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 89, &quot;content&quot;: &quot;      clientAffinityEnabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 90, &quot;content&quot;: &quot;      virtualNetworkSubnetId: hubSubnetId&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 91, &quot;content&quot;: &quot;      vnetContentShareEnabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 92, &quot;content&quot;: &quot;      vnetImagePullEnabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 93, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 94, &quot;content&quot;: &quot;    extraProperties&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 95, &quot;content&quot;: &quot;  )&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 96, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 97, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 98, &quot;content&quot;: &quot;resource funcAuthSettings 'Microsoft.Web/sites/config@2021-03-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 99, &quot;content&quot;: &quot;  name: 'authsettingsV2'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 100, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 101, &quot;content&quot;: &quot;    login: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 102, &quot;content&quot;: &quot;      tokenStore: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 103, &quot;content&quot;: &quot;        enabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 104, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 105, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 106, &quot;content&quot;: &quot;    globalValidation: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 107, &quot;content&quot;: &quot;      unauthenticatedClientAction: 'RedirectToLoginPage'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 108, &quot;content&quot;: &quot;      requireAuthentication: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 109, &quot;content&quot;: &quot;      excludedPaths: [ '/api/config' ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 110, &quot;content&quot;: &quot;      redirectToProvider: 'azureActiveDirectory'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 111, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 112, &quot;content&quot;: &quot;    httpSettings: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 113, &quot;content&quot;: &quot;      requireHttps: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 114, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 115, &quot;content&quot;: &quot;    identityProviders: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 116, &quot;content&quot;: &quot;      azureActiveDirectory: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 117, &quot;content&quot;: &quot;        enabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 118, &quot;content&quot;: &quot;        isAutoProvisioned: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 119, &quot;content&quot;: &quot;        registration: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 120, &quot;content&quot;: &quot;          clientId: client_id&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 121, &quot;content&quot;: &quot;          openIdIssuer: app_func_issuer&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 122, &quot;content&quot;: &quot;          clientSecretSettingName: 'OVERRIDE_USE_MI_FIC_ASSERTION_CLIENTID'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 123, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 124, &quot;content&quot;: &quot;        validation: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 125, &quot;content&quot;: &quot;          allowedAudiences: app_func_audiences&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 126, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 127, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 128, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 129, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 130, &quot;content&quot;: &quot;  parent: function&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 131, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 132, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 133, &quot;content&quot;: &quot;var sas = funcStorage.listAccountSas('2021-08-01', storage_account_sas)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 134, &quot;content&quot;: &quot;resource funcLogs 'Microsoft.Web/sites/config@2021-03-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 135, &quot;content&quot;: &quot;  name: 'logs'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 136, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 137, &quot;content&quot;: &quot;    applicationLogs: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 138, &quot;content&quot;: &quot;      azureBlobStorage: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 139, &quot;content&quot;: &quot;        level: diagnostics_log_level&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 140, &quot;content&quot;: &quot;        retentionInDays: log_retention&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 141, &quot;content&quot;: &quot;        sasUrl: '${funcStorage.properties.primaryEndpoints.blob}app-logs?${sas.accountSasToken}'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 142, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 143, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 144, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 145, &quot;content&quot;: &quot;  parent: function&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 146, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 147, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 148, &quot;content&quot;: &quot;output principalId string = function.identity.principalId&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 149, &quot;content&quot;: &quot;output userAssignedMsiClientId string = functionUserAssignedMSI.properties.clientId&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 150, &quot;content&quot;: &quot;output userAssignedMsiObjectId string = functionUserAssignedMSI.properties.principalId&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: param name string\n       2: param location string\n       3: param owner string\n       4: param cors_origins array\n       5: \n       6: param server_farm_id string\n       7: param client_id string\n       8: param app_func_issuer string\n       9: param app_func_audiences array\n      10: param use_windows bool\n      11: param enable_remote_debugging bool\n      12: \n      13: param logs_storage string\n      14: param signedExpiry string\n      15: param hubSubnetId string\n      16: \n      17: @description('The degree of severity for diagnostics logs.')\n      18: @allowed([\n      19:   'Verbose'\n      20:   'Information'\n      21:   'Warning'\n      22:   'Error'\n      23: ])\n      24: param diagnostics_log_level string\n      25: param log_retention int\n      26: param linux_fx_version string\n      27: \n      28: var siteconfig = (use_windows) ? {\n      29: } : {\n      30:   linuxFxVersion: linux_fx_version\n      31: }\n      32: \n      33: var storage_account_sas = {\n      34:   signedExpiry: signedExpiry\n      35:   signedPermission: 'rwdlacup'\n      36:   signedResourceTypes: 'sco'\n      37:   signedServices: 'bfqt'\n      38: }\n      39: \n      40: \n      41: var commonSiteConfig = {\n      42:   alwaysOn: true\n      43:   defaultDocuments: []\n      44:   httpLoggingEnabled: true\n      45:   logsDirectorySizeLimit: 100\n      46:   detailedErrorLoggingEnabled: true\n      47:   http20Enabled: true\n      48:   ftpsState: 'Disabled'\n      49:   use32BitWorkerProcess: false\n      50:   healthCheckPath: '/api/config'\n      51:   cors: {\n      52:     allowedOrigins: cors_origins\n      53:   }\n      54: }\n      55: \n      56: var extraProperties = (use_windows &amp;&amp; enable_remote_debugging) ? {\n      57:   netFrameworkVersion: 'v8.0'\n&gt;&gt;&gt;   58:   remoteDebuggingEnabled: true\n      59:   remoteDebuggingVersion: 'VS2022'\n      60: } : {}\n      61: \n      62: resource funcStorage 'Microsoft.Storage/storageAccounts@2021-08-01' existing = {\n      63:   name: logs_storage\n      64: }\n      65: \n      66: resource functionUserAssignedMSI 'Microsoft.ManagedIdentity/userAssignedIdentities@2023-01-31' = {\n      67:   name: '${name}-user-assigned-msi'\n      68:   location: location\n      69: }\n      70: \n      71: resource function 'Microsoft.Web/sites@2021-03-01' = {\n      72:   name: name\n      73:   location: location\n      74:   kind: (use_windows) ? 'functionapp' : 'functionapp,linux'\n      75:   tags: {\n      76:     OWNER: owner\n      77:   }\n      78:   identity: {\n      79:     type: 'SystemAssigned, UserAssigned'\n      80:     userAssignedIdentities: {\n      81:       '${functionUserAssignedMSI.id}': {}\n      82:     }\n      83:   }\n      84:   properties: union(\n      85:     {\n      86:       siteConfig: union(siteconfig, commonSiteConfig)\n      87:       httpsOnly: true\n      88:       serverFarmId: server_farm_id\n      89:       clientAffinityEnabled: true\n      90:       virtualNetworkSubnetId: hubSubnetId\n      91:       vnetContentShareEnabled: true\n      92:       vnetImagePullEnabled: true\n      93:     },\n      94:     extraProperties\n      95:   )\n      96: }\n      97: \n      98: resource funcAuthSettings 'Microsoft.Web/sites/config@2021-03-01' = {\n      99:   name: 'authsettingsV2'\n     100:   properties: {\n     101:     login: {\n     102:       tokenStore: {\n     103:         enabled: true\n     104:       }\n     105:     }\n     106:     globalValidation: {\n     107:       unauthenticatedClientAction: 'RedirectToLoginPage'\n     108:       requireAuthentication: true\n     109:       excludedPaths: [ '/api/config' ]\n     110:       redirectToProvider: 'azureActiveDirectory'\n     111:     }\n     112:     httpSettings: {\n     113:       requireHttps: true\n     114:     }\n     115:     identityProviders: {\n     116:       azureActiveDirectory: {\n     117:         enabled: true\n     118:         isAutoProvisioned: false\n     119:         registration: {\n     120:           clientId: client_id\n     121:           openIdIssuer: app_func_issuer\n     122:           clientSecretSettingName: 'OVERRIDE_USE_MI_FIC_ASSERTION_CLIENTID'\n     123:         }\n     124:         validation: {\n     125:           allowedAudiences: app_func_audiences\n     126:         }\n     127:       }\n     128:     }\n     129:   }\n     130:   parent: function\n     131: }\n     132: \n     133: var sas = funcStorage.listAccountSas('2021-08-01', storage_account_sas)\n     134: resource funcLogs 'Microsoft.Web/sites/config@2021-03-01' = {\n     135:   name: 'logs'\n     136:   properties: {\n     137:     applicationLogs: {\n     138:       azureBlobStorage: {\n     139:         level: diagnostics_log_level\n     140:         retentionInDays: log_retention\n     141:         sasUrl: '${funcStorage.properties.primaryEndpoints.blob}app-logs?${sas.accountSasToken}'\n     142:       }\n     143:     }\n     144:   }\n     145:   parent: function\n     146: }\n     147: \n     148: output principalId string = function.identity.principalId\n     149: output userAssignedMsiClientId string = functionUserAssignedMSI.properties.clientId\n     150: output userAssignedMsiObjectId string = functionUserAssignedMSI.properties.principalId&quot;, &quot;highlighted_line_content&quot;: &quot;remoteDebuggingEnabled: true&quot;}
                </script>
                <script type="application/json" data-finding-context="hub-network.bicep_14" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;hub-network.bicep&quot;, &quot;line_number&quot;: 14, &quot;total_lines&quot;: 41, &quot;start_line&quot;: 1, &quot;end_line&quot;: 41, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;param location string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;resource vnet 'Microsoft.Network/virtualNetworks@2023-11-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;  name: 'hub-vnet'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;    addressSpace: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;      addressPrefixes: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;        '**********/20'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;      ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;    subnets: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;      {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;        name: 'hub-subnet'&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;        properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;          addressPrefix: '**********/20'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;          serviceEndpoints: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;            {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;              // https://learn.microsoft.com/en-us/azure/storage/common/storage-network-security?tabs=azure-portal#azure-storage-cross-region-service-endpoints&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;              service: 'Microsoft.Storage.Global'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;            }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;            {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;              service: 'Microsoft.Keyvault'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;            }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;          ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;          delegations: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;            {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;              name: 'func-delegation'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;              properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;                serviceName: 'Microsoft.Web/serverFarms'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;              }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;            }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;          ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;    ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;output hubSubnetId string = vnet.properties.subnets[0].id&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;output hubNetworkName string = vnet.name&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: param location string\n       2: \n       3: resource vnet 'Microsoft.Network/virtualNetworks@2023-11-01' = {\n       4:   name: 'hub-vnet'\n       5:   location: location\n       6:   properties: {\n       7:     addressSpace: {\n       8:       addressPrefixes: [\n       9:         '**********/20'\n      10:       ]\n      11:     }\n      12:     subnets: [\n      13:       {\n&gt;&gt;&gt;   14:         name: 'hub-subnet'\n      15:         properties: {\n      16:           addressPrefix: '**********/20'\n      17:           serviceEndpoints: [\n      18:             {\n      19:               // https://learn.microsoft.com/en-us/azure/storage/common/storage-network-security?tabs=azure-portal#azure-storage-cross-region-service-endpoints\n      20:               service: 'Microsoft.Storage.Global'\n      21:             }\n      22:             {\n      23:               service: 'Microsoft.Keyvault'\n      24:             }\n      25:           ]\n      26:           delegations: [\n      27:             {\n      28:               name: 'func-delegation'\n      29:               properties: {\n      30:                 serviceName: 'Microsoft.Web/serverFarms'\n      31:               }\n      32:             }\n      33:           ]\n      34:         }\n      35:       }\n      36:     ]\n      37:   }\n      38: }\n      39: \n      40: output hubSubnetId string = vnet.properties.subnets[0].id\n      41: output hubNetworkName string = vnet.name&quot;, &quot;highlighted_line_content&quot;: &quot;name: 'hub-subnet'&quot;}
                </script>
                <script type="application/json" data-finding-context="ip-rules.bicep_5" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;ip-rules.bicep&quot;, &quot;line_number&quot;: 5, &quot;total_lines&quot;: 54, &quot;start_line&quot;: 1, &quot;end_line&quot;: 54, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;// https://eng.ms/docs/products/azure-storage/security/standards/network-isolation#recommended-ip-ranges&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;// (includes MSFTVPN)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;var corpNetIps = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;  '*******/8'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;  '********/8'&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;  '20.0.0.0/8'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;  '40.0.0.0/8'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;  '********/8'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;  '********/8'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;  '********/8'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;  '70.0.0.0/8'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;  '**********/16'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;  '*************'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;  '*************'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;  '**********/16'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;  '*********/16'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;  '***********/16'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;  '*************/29'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;  '**********/16'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;  '*************'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;  '***********/12'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;  '***********/16'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;  '***********/16'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;  '**********/16'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;  '***********/16'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;  '***********/13'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;  '************/26'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;  '**********/16'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;  '**********/16'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;var corpNetRules = [ for ip in corpNetIps: { action: 'Allow', value: ip }]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;var sawVnetIps = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;  '***********/27'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;  '************/27'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;  '*************/27'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;  '**************/26'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;  '*************/26'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;  '***************/26'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;  '*************/26'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;  '*************/26'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;  '*************/27'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 44, &quot;content&quot;: &quot;var sawVnetRules = [ for ip in sawVnetIps: { action: 'Allow', value: ip }]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 45, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 46, &quot;content&quot;: &quot;var ingestionServiceIps = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 47, &quot;content&quot;: &quot;  // TODO: get these from Teo&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 48, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 49, &quot;content&quot;: &quot;var ingestionServiceRules = [ for ip in ingestionServiceIps: { action: 'Allow', value: ip }]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 50, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 51, &quot;content&quot;: &quot;output combinedRules object[] = concat(corpNetRules, sawVnetRules, ingestionServiceRules)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 52, &quot;content&quot;: &quot;output corpNetRules object[] = corpNetRules&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 53, &quot;content&quot;: &quot;output sawVnetRules object[] = sawVnetRules&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 54, &quot;content&quot;: &quot;output ingestionServiceRules object[] = ingestionServiceRules&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: // https://eng.ms/docs/products/azure-storage/security/standards/network-isolation#recommended-ip-ranges\n       2: // (includes MSFTVPN)\n       3: var corpNetIps = [\n       4:   '*******/8'\n&gt;&gt;&gt;    5:   '********/8'\n       6:   '20.0.0.0/8'\n       7:   '40.0.0.0/8'\n       8:   '********/8'\n       9:   '********/8'\n      10:   '********/8'\n      11:   '70.0.0.0/8'\n      12:   '**********/16'\n      13:   '*************'\n      14:   '*************'\n      15:   '**********/16'\n      16:   '*********/16'\n      17:   '***********/16'\n      18:   '*************/29'\n      19:   '**********/16'\n      20:   '*************'\n      21:   '***********/12'\n      22:   '***********/16'\n      23:   '***********/16'\n      24:   '**********/16'\n      25:   '***********/16'\n      26:   '***********/13'\n      27:   '************/26'\n      28:   '**********/16'\n      29:   '**********/16'\n      30: ]\n      31: var corpNetRules = [ for ip in corpNetIps: { action: 'Allow', value: ip }]\n      32: \n      33: var sawVnetIps = [\n      34:   '***********/27'\n      35:   '************/27'\n      36:   '*************/27'\n      37:   '**************/26'\n      38:   '*************/26'\n      39:   '***************/26'\n      40:   '*************/26'\n      41:   '*************/26'\n      42:   '*************/27'\n      43: ]\n      44: var sawVnetRules = [ for ip in sawVnetIps: { action: 'Allow', value: ip }]\n      45: \n      46: var ingestionServiceIps = [\n      47:   // TODO: get these from Teo\n      48: ]\n      49: var ingestionServiceRules = [ for ip in ingestionServiceIps: { action: 'Allow', value: ip }]\n      50: \n      51: output combinedRules object[] = concat(corpNetRules, sawVnetRules, ingestionServiceRules)\n      52: output corpNetRules object[] = corpNetRules\n      53: output sawVnetRules object[] = sawVnetRules\n      54: output ingestionServiceRules object[] = ingestionServiceRules&quot;, &quot;highlighted_line_content&quot;: &quot;'********/8'&quot;}
                </script>
                <script type="application/json" data-finding-context="ip-rules.bicep_3" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;ip-rules.bicep&quot;, &quot;line_number&quot;: 3, &quot;total_lines&quot;: 54, &quot;start_line&quot;: 1, &quot;end_line&quot;: 54, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;// https://eng.ms/docs/products/azure-storage/security/standards/network-isolation#recommended-ip-ranges&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;// (includes MSFTVPN)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;var corpNetIps = [&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;  '*******/8'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;  '********/8'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;  '20.0.0.0/8'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;  '40.0.0.0/8'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;  '********/8'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;  '********/8'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;  '********/8'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;  '70.0.0.0/8'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;  '**********/16'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;  '*************'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;  '*************'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;  '**********/16'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;  '*********/16'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;  '***********/16'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;  '*************/29'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;  '**********/16'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;  '*************'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;  '***********/12'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;  '***********/16'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;  '***********/16'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;  '**********/16'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;  '***********/16'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;  '***********/13'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;  '************/26'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;  '**********/16'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;  '**********/16'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;var corpNetRules = [ for ip in corpNetIps: { action: 'Allow', value: ip }]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;var sawVnetIps = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;  '***********/27'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;  '************/27'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;  '*************/27'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;  '**************/26'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;  '*************/26'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;  '***************/26'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;  '*************/26'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;  '*************/26'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;  '*************/27'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 44, &quot;content&quot;: &quot;var sawVnetRules = [ for ip in sawVnetIps: { action: 'Allow', value: ip }]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 45, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 46, &quot;content&quot;: &quot;var ingestionServiceIps = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 47, &quot;content&quot;: &quot;  // TODO: get these from Teo&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 48, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 49, &quot;content&quot;: &quot;var ingestionServiceRules = [ for ip in ingestionServiceIps: { action: 'Allow', value: ip }]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 50, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 51, &quot;content&quot;: &quot;output combinedRules object[] = concat(corpNetRules, sawVnetRules, ingestionServiceRules)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 52, &quot;content&quot;: &quot;output corpNetRules object[] = corpNetRules&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 53, &quot;content&quot;: &quot;output sawVnetRules object[] = sawVnetRules&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 54, &quot;content&quot;: &quot;output ingestionServiceRules object[] = ingestionServiceRules&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: // https://eng.ms/docs/products/azure-storage/security/standards/network-isolation#recommended-ip-ranges\n       2: // (includes MSFTVPN)\n&gt;&gt;&gt;    3: var corpNetIps = [\n       4:   '*******/8'\n       5:   '********/8'\n       6:   '20.0.0.0/8'\n       7:   '40.0.0.0/8'\n       8:   '********/8'\n       9:   '********/8'\n      10:   '********/8'\n      11:   '70.0.0.0/8'\n      12:   '**********/16'\n      13:   '*************'\n      14:   '*************'\n      15:   '**********/16'\n      16:   '*********/16'\n      17:   '***********/16'\n      18:   '*************/29'\n      19:   '**********/16'\n      20:   '*************'\n      21:   '***********/12'\n      22:   '***********/16'\n      23:   '***********/16'\n      24:   '**********/16'\n      25:   '***********/16'\n      26:   '***********/13'\n      27:   '************/26'\n      28:   '**********/16'\n      29:   '**********/16'\n      30: ]\n      31: var corpNetRules = [ for ip in corpNetIps: { action: 'Allow', value: ip }]\n      32: \n      33: var sawVnetIps = [\n      34:   '***********/27'\n      35:   '************/27'\n      36:   '*************/27'\n      37:   '**************/26'\n      38:   '*************/26'\n      39:   '***************/26'\n      40:   '*************/26'\n      41:   '*************/26'\n      42:   '*************/27'\n      43: ]\n      44: var sawVnetRules = [ for ip in sawVnetIps: { action: 'Allow', value: ip }]\n      45: \n      46: var ingestionServiceIps = [\n      47:   // TODO: get these from Teo\n      48: ]\n      49: var ingestionServiceRules = [ for ip in ingestionServiceIps: { action: 'Allow', value: ip }]\n      50: \n      51: output combinedRules object[] = concat(corpNetRules, sawVnetRules, ingestionServiceRules)\n      52: output corpNetRules object[] = corpNetRules\n      53: output sawVnetRules object[] = sawVnetRules\n      54: output ingestionServiceRules object[] = ingestionServiceRules&quot;, &quot;highlighted_line_content&quot;: &quot;var corpNetIps = [&quot;}
                </script>
                <script type="application/json" data-finding-context="keyvault.bicep_28" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;keyvault.bicep&quot;, &quot;line_number&quot;: 28, &quot;total_lines&quot;: 59, &quot;start_line&quot;: 1, &quot;end_line&quot;: 59, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;param location string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;param tenantId string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;@secure()&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;param secrets object = {}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;param hubSubnetId string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;module ipRules './ip-rules.bicep' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;  name: 'ipRules'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;var sawIpRules = ipRules.outputs.sawVnetRules&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;var keyVaultName = 'of-kv-${uniqueString(resourceGroup().id)}'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;var secretNames = objectKeys(secrets)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;resource keyVault 'Microsoft.KeyVault/vaults@2021-10-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;  name: keyVaultName&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;    enabledForDiskEncryption: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;    enabledForDeployment: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;    enabledForTemplateDeployment: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;    enableRbacAuthorization: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;    sku: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;      family: 'A'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;      name: 'standard'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;    networkAcls: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;      defaultAction: 'Allow'&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;      bypass: 'AzureServices'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;      ipRules: sawIpRules&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;      virtualNetworkRules: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;        {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;          id: hubSubnetId&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;      ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;    accessPolicies: []&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;    tenantId: tenantId&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;  &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;  resource storedSecrets 'secrets' = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;    for name in secretNames: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;      name: name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 44, &quot;content&quot;: &quot;      properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 45, &quot;content&quot;: &quot;        value: string(secrets[name])&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 46, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 47, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 48, &quot;content&quot;: &quot;  ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 49, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 50, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 51, &quot;content&quot;: &quot;var numSecrets = length(secretNames)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 52, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 53, &quot;content&quot;: &quot;output keyVaultName string = keyVault.name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 54, &quot;content&quot;: &quot;output keyVaultSecretUris object[] = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 55, &quot;content&quot;: &quot;  for i in range(0, numSecrets): {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 56, &quot;content&quot;: &quot;    name: keyVault::storedSecrets[i].name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 57, &quot;content&quot;: &quot;    uri: keyVault::storedSecrets[i].properties.secretUriWithVersion&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 58, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 59, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: param location string\n       2: param tenantId string\n       3: @secure()\n       4: param secrets object = {}\n       5: param hubSubnetId string\n       6: \n       7: module ipRules './ip-rules.bicep' = {\n       8:   name: 'ipRules'\n       9: }\n      10: var sawIpRules = ipRules.outputs.sawVnetRules\n      11: \n      12: var keyVaultName = 'of-kv-${uniqueString(resourceGroup().id)}'\n      13: var secretNames = objectKeys(secrets)\n      14: \n      15: resource keyVault 'Microsoft.KeyVault/vaults@2021-10-01' = {\n      16:   name: keyVaultName\n      17:   location: location\n      18:   properties: {\n      19:     enabledForDiskEncryption: false\n      20:     enabledForDeployment: true\n      21:     enabledForTemplateDeployment: true\n      22:     enableRbacAuthorization: true\n      23:     sku: {\n      24:       family: 'A'\n      25:       name: 'standard'\n      26:     }\n      27:     networkAcls: {\n&gt;&gt;&gt;   28:       defaultAction: 'Allow'\n      29:       bypass: 'AzureServices'\n      30:       ipRules: sawIpRules\n      31:       virtualNetworkRules: [\n      32:         {\n      33:           id: hubSubnetId\n      34:         }\n      35:       ]\n      36:     }\n      37:     accessPolicies: []\n      38:     tenantId: tenantId\n      39:   }\n      40:   \n      41:   resource storedSecrets 'secrets' = [\n      42:     for name in secretNames: {\n      43:       name: name\n      44:       properties: {\n      45:         value: string(secrets[name])\n      46:       }\n      47:     }\n      48:   ]\n      49: }\n      50: \n      51: var numSecrets = length(secretNames)\n      52: \n      53: output keyVaultName string = keyVault.name\n      54: output keyVaultSecretUris object[] = [\n      55:   for i in range(0, numSecrets): {\n      56:     name: keyVault::storedSecrets[i].name\n      57:     uri: keyVault::storedSecrets[i].properties.secretUriWithVersion\n      58:   }\n      59: ]&quot;, &quot;highlighted_line_content&quot;: &quot;defaultAction: 'Allow'&quot;}
                </script>
                <script type="application/json" data-finding-context="keyvault.bicep_29" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;keyvault.bicep&quot;, &quot;line_number&quot;: 29, &quot;total_lines&quot;: 59, &quot;start_line&quot;: 1, &quot;end_line&quot;: 59, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;param location string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;param tenantId string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;@secure()&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;param secrets object = {}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;param hubSubnetId string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;module ipRules './ip-rules.bicep' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;  name: 'ipRules'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;var sawIpRules = ipRules.outputs.sawVnetRules&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;var keyVaultName = 'of-kv-${uniqueString(resourceGroup().id)}'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;var secretNames = objectKeys(secrets)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;resource keyVault 'Microsoft.KeyVault/vaults@2021-10-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;  name: keyVaultName&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;    enabledForDiskEncryption: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;    enabledForDeployment: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;    enabledForTemplateDeployment: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;    enableRbacAuthorization: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;    sku: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;      family: 'A'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;      name: 'standard'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;    networkAcls: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;      defaultAction: 'Allow'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;      bypass: 'AzureServices'&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;      ipRules: sawIpRules&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;      virtualNetworkRules: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;        {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;          id: hubSubnetId&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;      ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;    accessPolicies: []&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;    tenantId: tenantId&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;  &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;  resource storedSecrets 'secrets' = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;    for name in secretNames: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;      name: name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 44, &quot;content&quot;: &quot;      properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 45, &quot;content&quot;: &quot;        value: string(secrets[name])&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 46, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 47, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 48, &quot;content&quot;: &quot;  ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 49, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 50, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 51, &quot;content&quot;: &quot;var numSecrets = length(secretNames)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 52, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 53, &quot;content&quot;: &quot;output keyVaultName string = keyVault.name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 54, &quot;content&quot;: &quot;output keyVaultSecretUris object[] = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 55, &quot;content&quot;: &quot;  for i in range(0, numSecrets): {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 56, &quot;content&quot;: &quot;    name: keyVault::storedSecrets[i].name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 57, &quot;content&quot;: &quot;    uri: keyVault::storedSecrets[i].properties.secretUriWithVersion&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 58, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 59, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: param location string\n       2: param tenantId string\n       3: @secure()\n       4: param secrets object = {}\n       5: param hubSubnetId string\n       6: \n       7: module ipRules './ip-rules.bicep' = {\n       8:   name: 'ipRules'\n       9: }\n      10: var sawIpRules = ipRules.outputs.sawVnetRules\n      11: \n      12: var keyVaultName = 'of-kv-${uniqueString(resourceGroup().id)}'\n      13: var secretNames = objectKeys(secrets)\n      14: \n      15: resource keyVault 'Microsoft.KeyVault/vaults@2021-10-01' = {\n      16:   name: keyVaultName\n      17:   location: location\n      18:   properties: {\n      19:     enabledForDiskEncryption: false\n      20:     enabledForDeployment: true\n      21:     enabledForTemplateDeployment: true\n      22:     enableRbacAuthorization: true\n      23:     sku: {\n      24:       family: 'A'\n      25:       name: 'standard'\n      26:     }\n      27:     networkAcls: {\n      28:       defaultAction: 'Allow'\n&gt;&gt;&gt;   29:       bypass: 'AzureServices'\n      30:       ipRules: sawIpRules\n      31:       virtualNetworkRules: [\n      32:         {\n      33:           id: hubSubnetId\n      34:         }\n      35:       ]\n      36:     }\n      37:     accessPolicies: []\n      38:     tenantId: tenantId\n      39:   }\n      40:   \n      41:   resource storedSecrets 'secrets' = [\n      42:     for name in secretNames: {\n      43:       name: name\n      44:       properties: {\n      45:         value: string(secrets[name])\n      46:       }\n      47:     }\n      48:   ]\n      49: }\n      50: \n      51: var numSecrets = length(secretNames)\n      52: \n      53: output keyVaultName string = keyVault.name\n      54: output keyVaultSecretUris object[] = [\n      55:   for i in range(0, numSecrets): {\n      56:     name: keyVault::storedSecrets[i].name\n      57:     uri: keyVault::storedSecrets[i].properties.secretUriWithVersion\n      58:   }\n      59: ]&quot;, &quot;highlighted_line_content&quot;: &quot;bypass: 'AzureServices'&quot;}
                </script>
                <script type="application/json" data-finding-context="keyvault.bicep_22" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;keyvault.bicep&quot;, &quot;line_number&quot;: 22, &quot;total_lines&quot;: 59, &quot;start_line&quot;: 1, &quot;end_line&quot;: 59, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;param location string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;param tenantId string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;@secure()&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;param secrets object = {}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;param hubSubnetId string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;module ipRules './ip-rules.bicep' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;  name: 'ipRules'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;var sawIpRules = ipRules.outputs.sawVnetRules&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;var keyVaultName = 'of-kv-${uniqueString(resourceGroup().id)}'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;var secretNames = objectKeys(secrets)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;resource keyVault 'Microsoft.KeyVault/vaults@2021-10-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;  name: keyVaultName&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;    enabledForDiskEncryption: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;    enabledForDeployment: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;    enabledForTemplateDeployment: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;    enableRbacAuthorization: true&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;    sku: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;      family: 'A'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;      name: 'standard'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;    networkAcls: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;      defaultAction: 'Allow'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;      bypass: 'AzureServices'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;      ipRules: sawIpRules&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;      virtualNetworkRules: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;        {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;          id: hubSubnetId&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;      ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;    accessPolicies: []&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;    tenantId: tenantId&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;  &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;  resource storedSecrets 'secrets' = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;    for name in secretNames: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;      name: name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 44, &quot;content&quot;: &quot;      properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 45, &quot;content&quot;: &quot;        value: string(secrets[name])&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 46, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 47, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 48, &quot;content&quot;: &quot;  ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 49, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 50, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 51, &quot;content&quot;: &quot;var numSecrets = length(secretNames)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 52, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 53, &quot;content&quot;: &quot;output keyVaultName string = keyVault.name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 54, &quot;content&quot;: &quot;output keyVaultSecretUris object[] = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 55, &quot;content&quot;: &quot;  for i in range(0, numSecrets): {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 56, &quot;content&quot;: &quot;    name: keyVault::storedSecrets[i].name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 57, &quot;content&quot;: &quot;    uri: keyVault::storedSecrets[i].properties.secretUriWithVersion&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 58, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 59, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: param location string\n       2: param tenantId string\n       3: @secure()\n       4: param secrets object = {}\n       5: param hubSubnetId string\n       6: \n       7: module ipRules './ip-rules.bicep' = {\n       8:   name: 'ipRules'\n       9: }\n      10: var sawIpRules = ipRules.outputs.sawVnetRules\n      11: \n      12: var keyVaultName = 'of-kv-${uniqueString(resourceGroup().id)}'\n      13: var secretNames = objectKeys(secrets)\n      14: \n      15: resource keyVault 'Microsoft.KeyVault/vaults@2021-10-01' = {\n      16:   name: keyVaultName\n      17:   location: location\n      18:   properties: {\n      19:     enabledForDiskEncryption: false\n      20:     enabledForDeployment: true\n      21:     enabledForTemplateDeployment: true\n&gt;&gt;&gt;   22:     enableRbacAuthorization: true\n      23:     sku: {\n      24:       family: 'A'\n      25:       name: 'standard'\n      26:     }\n      27:     networkAcls: {\n      28:       defaultAction: 'Allow'\n      29:       bypass: 'AzureServices'\n      30:       ipRules: sawIpRules\n      31:       virtualNetworkRules: [\n      32:         {\n      33:           id: hubSubnetId\n      34:         }\n      35:       ]\n      36:     }\n      37:     accessPolicies: []\n      38:     tenantId: tenantId\n      39:   }\n      40:   \n      41:   resource storedSecrets 'secrets' = [\n      42:     for name in secretNames: {\n      43:       name: name\n      44:       properties: {\n      45:         value: string(secrets[name])\n      46:       }\n      47:     }\n      48:   ]\n      49: }\n      50: \n      51: var numSecrets = length(secretNames)\n      52: \n      53: output keyVaultName string = keyVault.name\n      54: output keyVaultSecretUris object[] = [\n      55:   for i in range(0, numSecrets): {\n      56:     name: keyVault::storedSecrets[i].name\n      57:     uri: keyVault::storedSecrets[i].properties.secretUriWithVersion\n      58:   }\n      59: ]&quot;, &quot;highlighted_line_content&quot;: &quot;enableRbacAuthorization: true&quot;}
                </script>
                <script type="application/json" data-finding-context="keyvault.bicep_15" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;keyvault.bicep&quot;, &quot;line_number&quot;: 15, &quot;total_lines&quot;: 59, &quot;start_line&quot;: 1, &quot;end_line&quot;: 59, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;param location string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;param tenantId string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;@secure()&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;param secrets object = {}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;param hubSubnetId string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;module ipRules './ip-rules.bicep' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;  name: 'ipRules'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;var sawIpRules = ipRules.outputs.sawVnetRules&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;var keyVaultName = 'of-kv-${uniqueString(resourceGroup().id)}'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;var secretNames = objectKeys(secrets)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;resource keyVault 'Microsoft.KeyVault/vaults@2021-10-01' = {&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;  name: keyVaultName&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;    enabledForDiskEncryption: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;    enabledForDeployment: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;    enabledForTemplateDeployment: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;    enableRbacAuthorization: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;    sku: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;      family: 'A'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;      name: 'standard'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;    networkAcls: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;      defaultAction: 'Allow'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;      bypass: 'AzureServices'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;      ipRules: sawIpRules&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;      virtualNetworkRules: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;        {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;          id: hubSubnetId&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;      ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;    accessPolicies: []&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;    tenantId: tenantId&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;  &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;  resource storedSecrets 'secrets' = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;    for name in secretNames: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;      name: name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 44, &quot;content&quot;: &quot;      properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 45, &quot;content&quot;: &quot;        value: string(secrets[name])&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 46, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 47, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 48, &quot;content&quot;: &quot;  ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 49, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 50, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 51, &quot;content&quot;: &quot;var numSecrets = length(secretNames)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 52, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 53, &quot;content&quot;: &quot;output keyVaultName string = keyVault.name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 54, &quot;content&quot;: &quot;output keyVaultSecretUris object[] = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 55, &quot;content&quot;: &quot;  for i in range(0, numSecrets): {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 56, &quot;content&quot;: &quot;    name: keyVault::storedSecrets[i].name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 57, &quot;content&quot;: &quot;    uri: keyVault::storedSecrets[i].properties.secretUriWithVersion&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 58, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 59, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: param location string\n       2: param tenantId string\n       3: @secure()\n       4: param secrets object = {}\n       5: param hubSubnetId string\n       6: \n       7: module ipRules './ip-rules.bicep' = {\n       8:   name: 'ipRules'\n       9: }\n      10: var sawIpRules = ipRules.outputs.sawVnetRules\n      11: \n      12: var keyVaultName = 'of-kv-${uniqueString(resourceGroup().id)}'\n      13: var secretNames = objectKeys(secrets)\n      14: \n&gt;&gt;&gt;   15: resource keyVault 'Microsoft.KeyVault/vaults@2021-10-01' = {\n      16:   name: keyVaultName\n      17:   location: location\n      18:   properties: {\n      19:     enabledForDiskEncryption: false\n      20:     enabledForDeployment: true\n      21:     enabledForTemplateDeployment: true\n      22:     enableRbacAuthorization: true\n      23:     sku: {\n      24:       family: 'A'\n      25:       name: 'standard'\n      26:     }\n      27:     networkAcls: {\n      28:       defaultAction: 'Allow'\n      29:       bypass: 'AzureServices'\n      30:       ipRules: sawIpRules\n      31:       virtualNetworkRules: [\n      32:         {\n      33:           id: hubSubnetId\n      34:         }\n      35:       ]\n      36:     }\n      37:     accessPolicies: []\n      38:     tenantId: tenantId\n      39:   }\n      40:   \n      41:   resource storedSecrets 'secrets' = [\n      42:     for name in secretNames: {\n      43:       name: name\n      44:       properties: {\n      45:         value: string(secrets[name])\n      46:       }\n      47:     }\n      48:   ]\n      49: }\n      50: \n      51: var numSecrets = length(secretNames)\n      52: \n      53: output keyVaultName string = keyVault.name\n      54: output keyVaultSecretUris object[] = [\n      55:   for i in range(0, numSecrets): {\n      56:     name: keyVault::storedSecrets[i].name\n      57:     uri: keyVault::storedSecrets[i].properties.secretUriWithVersion\n      58:   }\n      59: ]&quot;, &quot;highlighted_line_content&quot;: &quot;resource keyVault 'Microsoft.KeyVault/vaults@2021-10-01' = {&quot;}
                </script>
                <script type="application/json" data-finding-context="keyvault.bicep_4" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;keyvault.bicep&quot;, &quot;line_number&quot;: 4, &quot;total_lines&quot;: 59, &quot;start_line&quot;: 1, &quot;end_line&quot;: 59, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;param location string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;param tenantId string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;@secure()&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;param secrets object = {}&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;param hubSubnetId string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;module ipRules './ip-rules.bicep' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;  name: 'ipRules'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;var sawIpRules = ipRules.outputs.sawVnetRules&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;var keyVaultName = 'of-kv-${uniqueString(resourceGroup().id)}'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;var secretNames = objectKeys(secrets)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;resource keyVault 'Microsoft.KeyVault/vaults@2021-10-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;  name: keyVaultName&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;    enabledForDiskEncryption: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;    enabledForDeployment: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;    enabledForTemplateDeployment: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;    enableRbacAuthorization: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;    sku: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;      family: 'A'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;      name: 'standard'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;    networkAcls: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;      defaultAction: 'Allow'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;      bypass: 'AzureServices'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;      ipRules: sawIpRules&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;      virtualNetworkRules: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;        {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;          id: hubSubnetId&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;      ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;    accessPolicies: []&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;    tenantId: tenantId&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;  &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;  resource storedSecrets 'secrets' = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;    for name in secretNames: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;      name: name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 44, &quot;content&quot;: &quot;      properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 45, &quot;content&quot;: &quot;        value: string(secrets[name])&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 46, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 47, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 48, &quot;content&quot;: &quot;  ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 49, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 50, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 51, &quot;content&quot;: &quot;var numSecrets = length(secretNames)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 52, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 53, &quot;content&quot;: &quot;output keyVaultName string = keyVault.name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 54, &quot;content&quot;: &quot;output keyVaultSecretUris object[] = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 55, &quot;content&quot;: &quot;  for i in range(0, numSecrets): {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 56, &quot;content&quot;: &quot;    name: keyVault::storedSecrets[i].name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 57, &quot;content&quot;: &quot;    uri: keyVault::storedSecrets[i].properties.secretUriWithVersion&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 58, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 59, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: param location string\n       2: param tenantId string\n       3: @secure()\n&gt;&gt;&gt;    4: param secrets object = {}\n       5: param hubSubnetId string\n       6: \n       7: module ipRules './ip-rules.bicep' = {\n       8:   name: 'ipRules'\n       9: }\n      10: var sawIpRules = ipRules.outputs.sawVnetRules\n      11: \n      12: var keyVaultName = 'of-kv-${uniqueString(resourceGroup().id)}'\n      13: var secretNames = objectKeys(secrets)\n      14: \n      15: resource keyVault 'Microsoft.KeyVault/vaults@2021-10-01' = {\n      16:   name: keyVaultName\n      17:   location: location\n      18:   properties: {\n      19:     enabledForDiskEncryption: false\n      20:     enabledForDeployment: true\n      21:     enabledForTemplateDeployment: true\n      22:     enableRbacAuthorization: true\n      23:     sku: {\n      24:       family: 'A'\n      25:       name: 'standard'\n      26:     }\n      27:     networkAcls: {\n      28:       defaultAction: 'Allow'\n      29:       bypass: 'AzureServices'\n      30:       ipRules: sawIpRules\n      31:       virtualNetworkRules: [\n      32:         {\n      33:           id: hubSubnetId\n      34:         }\n      35:       ]\n      36:     }\n      37:     accessPolicies: []\n      38:     tenantId: tenantId\n      39:   }\n      40:   \n      41:   resource storedSecrets 'secrets' = [\n      42:     for name in secretNames: {\n      43:       name: name\n      44:       properties: {\n      45:         value: string(secrets[name])\n      46:       }\n      47:     }\n      48:   ]\n      49: }\n      50: \n      51: var numSecrets = length(secretNames)\n      52: \n      53: output keyVaultName string = keyVault.name\n      54: output keyVaultSecretUris object[] = [\n      55:   for i in range(0, numSecrets): {\n      56:     name: keyVault::storedSecrets[i].name\n      57:     uri: keyVault::storedSecrets[i].properties.secretUriWithVersion\n      58:   }\n      59: ]&quot;, &quot;highlighted_line_content&quot;: &quot;param secrets object = {}&quot;}
                </script>
                <script type="application/json" data-finding-context="scaleset-networks.bicep_66" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;scaleset-networks.bicep&quot;, &quot;line_number&quot;: 66, &quot;total_lines&quot;: 106, &quot;start_line&quot;: 1, &quot;end_line&quot;: 106, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;param location string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;param ipServiceTag string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;param hubNetworkName string&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;param index int&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;var scalesetSubnetName = 'scaleset'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;var scalesetOutboundIpName = 'scaleset-outbound-ip'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;var scalesetOutboundNATGatewayName = 'scaleset-outbound-nat-gateway'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;var vnetName = 'vnet'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;resource scalesetOutboundIp 'Microsoft.Network/publicIPAddresses@2021-05-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;  name: '${scalesetOutboundIpName}-${location}-v2'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;  sku: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;    name: 'Standard'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;    publicIPAddressVersion: 'IPv4'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;    publicIPAllocationMethod: 'Static' // Needs to be static for the NAT gateway&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;    idleTimeoutInMinutes: 4&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;    // We can't include this until we're onboarded to the service tags&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;    // ipTags: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;    //   {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;    //     ipTagType: 'FirstPartyUsage'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;    //     // https://fuzzfest.visualstudio.com/Onefuzz/_wiki/wikis/Onefuzz.wiki/2506/Service-Tags&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;    //     tag: ip_service_tag&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;    //   }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;    // ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;resource scalesetOutboundNatGateway 'Microsoft.Network/natGateways@2021-05-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;  name: '${scalesetOutboundNATGatewayName}-${location}-v2'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;  sku: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;    name: 'Standard'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;    idleTimeoutInMinutes: 4&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;    publicIpAddresses: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;      {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;        id: scalesetOutboundIp.id&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 44, &quot;content&quot;: &quot;    ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 45, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 46, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 47, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 48, &quot;content&quot;: &quot;resource vnet 'Microsoft.Network/virtualNetworks@2023-11-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 49, &quot;content&quot;: &quot;  name: '${vnetName}-${location}-v2'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 50, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 51, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 52, &quot;content&quot;: &quot;    addressSpace: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 53, &quot;content&quot;: &quot;      addressPrefixes: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 54, &quot;content&quot;: &quot;        '10.${254-index}.0.0/16'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 55, &quot;content&quot;: &quot;      ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 56, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 57, &quot;content&quot;: &quot;    subnets: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 58, &quot;content&quot;: &quot;      {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 59, &quot;content&quot;: &quot;        name: scalesetSubnetName&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 60, &quot;content&quot;: &quot;        properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 61, &quot;content&quot;: &quot;          addressPrefix: '10.${254-index}.0.0/16'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 62, &quot;content&quot;: &quot;          privateEndpointNetworkPolicies: 'Enabled'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 63, &quot;content&quot;: &quot;          privateLinkServiceNetworkPolicies: 'Enabled'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 64, &quot;content&quot;: &quot;          serviceEndpoints: []&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 65, &quot;content&quot;: &quot;          delegations: []&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 66, &quot;content&quot;: &quot;          defaultOutboundAccess: true&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 67, &quot;content&quot;: &quot;          natGateway: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 68, &quot;content&quot;: &quot;            id: scalesetOutboundNatGateway.id&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 69, &quot;content&quot;: &quot;          }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 70, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 71, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 72, &quot;content&quot;: &quot;    ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 73, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 74, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 75, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 76, &quot;content&quot;: &quot;resource hubNetwork 'Microsoft.Network/virtualNetworks@2023-11-01' existing = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 77, &quot;content&quot;: &quot;  name: hubNetworkName&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 78, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 79, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 80, &quot;content&quot;: &quot;resource scalesetToHubPeering 'Microsoft.Network/virtualNetworks/virtualNetworkPeerings@2022-07-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 81, &quot;content&quot;: &quot;  name: '${vnet.name}-to-hub'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 82, &quot;content&quot;: &quot;  parent: vnet&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 83, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 84, &quot;content&quot;: &quot;    allowForwardedTraffic: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 85, &quot;content&quot;: &quot;    allowGatewayTransit: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 86, &quot;content&quot;: &quot;    allowVirtualNetworkAccess: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 87, &quot;content&quot;: &quot;    remoteVirtualNetwork: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 88, &quot;content&quot;: &quot;      id: hubNetwork.id&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 89, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 90, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 91, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 92, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 93, &quot;content&quot;: &quot;resource destinationToSourcePeering 'Microsoft.Network/virtualNetworks/virtualNetworkPeerings@2022-07-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 94, &quot;content&quot;: &quot;  name: 'hub-to-${vnet.name}'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 95, &quot;content&quot;: &quot;  parent: hubNetwork&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 96, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 97, &quot;content&quot;: &quot;    allowForwardedTraffic: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 98, &quot;content&quot;: &quot;    allowGatewayTransit: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 99, &quot;content&quot;: &quot;    allowVirtualNetworkAccess: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 100, &quot;content&quot;: &quot;    remoteVirtualNetwork: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 101, &quot;content&quot;: &quot;      id: vnet.id&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 102, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 103, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 104, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 105, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 106, &quot;content&quot;: &quot;output vnet object = vnet&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: param location string\n       2: param ipServiceTag string\n       3: param hubNetworkName string\n       4: param index int\n       5: \n       6: var scalesetSubnetName = 'scaleset'\n       7: var scalesetOutboundIpName = 'scaleset-outbound-ip'\n       8: var scalesetOutboundNATGatewayName = 'scaleset-outbound-nat-gateway'\n       9: var vnetName = 'vnet'\n      10: \n      11: resource scalesetOutboundIp 'Microsoft.Network/publicIPAddresses@2021-05-01' = {\n      12:   name: '${scalesetOutboundIpName}-${location}-v2'\n      13:   location: location\n      14:   sku: {\n      15:     name: 'Standard'\n      16:   }\n      17:   properties: {\n      18:     publicIPAddressVersion: 'IPv4'\n      19:     publicIPAllocationMethod: 'Static' // Needs to be static for the NAT gateway\n      20:     idleTimeoutInMinutes: 4\n      21:     // We can't include this until we're onboarded to the service tags\n      22:     // ipTags: [\n      23:     //   {\n      24:     //     ipTagType: 'FirstPartyUsage'\n      25:     //     // https://fuzzfest.visualstudio.com/Onefuzz/_wiki/wikis/Onefuzz.wiki/2506/Service-Tags\n      26:     //     tag: ip_service_tag\n      27:     //   }\n      28:     // ]\n      29:   }\n      30: }\n      31: \n      32: resource scalesetOutboundNatGateway 'Microsoft.Network/natGateways@2021-05-01' = {\n      33:   name: '${scalesetOutboundNATGatewayName}-${location}-v2'\n      34:   location: location\n      35:   sku: {\n      36:     name: 'Standard'\n      37:   }\n      38:   properties: {\n      39:     idleTimeoutInMinutes: 4\n      40:     publicIpAddresses: [\n      41:       {\n      42:         id: scalesetOutboundIp.id\n      43:       }\n      44:     ]\n      45:   }\n      46: }\n      47: \n      48: resource vnet 'Microsoft.Network/virtualNetworks@2023-11-01' = {\n      49:   name: '${vnetName}-${location}-v2'\n      50:   location: location\n      51:   properties: {\n      52:     addressSpace: {\n      53:       addressPrefixes: [\n      54:         '10.${254-index}.0.0/16'\n      55:       ]\n      56:     }\n      57:     subnets: [\n      58:       {\n      59:         name: scalesetSubnetName\n      60:         properties: {\n      61:           addressPrefix: '10.${254-index}.0.0/16'\n      62:           privateEndpointNetworkPolicies: 'Enabled'\n      63:           privateLinkServiceNetworkPolicies: 'Enabled'\n      64:           serviceEndpoints: []\n      65:           delegations: []\n&gt;&gt;&gt;   66:           defaultOutboundAccess: true\n      67:           natGateway: {\n      68:             id: scalesetOutboundNatGateway.id\n      69:           }\n      70:         }\n      71:       }\n      72:     ]\n      73:   }\n      74: }\n      75: \n      76: resource hubNetwork 'Microsoft.Network/virtualNetworks@2023-11-01' existing = {\n      77:   name: hubNetworkName\n      78: }\n      79: \n      80: resource scalesetToHubPeering 'Microsoft.Network/virtualNetworks/virtualNetworkPeerings@2022-07-01' = {\n      81:   name: '${vnet.name}-to-hub'\n      82:   parent: vnet\n      83:   properties: {\n      84:     allowForwardedTraffic: true\n      85:     allowGatewayTransit: true\n      86:     allowVirtualNetworkAccess: true\n      87:     remoteVirtualNetwork: {\n      88:       id: hubNetwork.id\n      89:     }\n      90:   }\n      91: }\n      92: \n      93: resource destinationToSourcePeering 'Microsoft.Network/virtualNetworks/virtualNetworkPeerings@2022-07-01' = {\n      94:   name: 'hub-to-${vnet.name}'\n      95:   parent: hubNetwork\n      96:   properties: {\n      97:     allowForwardedTraffic: true\n      98:     allowGatewayTransit: true\n      99:     allowVirtualNetworkAccess: true\n     100:     remoteVirtualNetwork: {\n     101:       id: vnet.id\n     102:     }\n     103:   }\n     104: }\n     105: \n     106: output vnet object = vnet&quot;, &quot;highlighted_line_content&quot;: &quot;defaultOutboundAccess: true&quot;}
                </script>
                <script type="application/json" data-finding-context="server-farms.bicep_168" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;server-farms.bicep&quot;, &quot;line_number&quot;: 168, &quot;total_lines&quot;: 174, &quot;start_line&quot;: 68, &quot;end_line&quot;: 174, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 68, &quot;content&quot;: &quot;      Key: 'DATACENTER'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 69, &quot;content&quot;: &quot;      Value: siteLocation&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 70, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 71, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 72, &quot;content&quot;: &quot;      Key: 'MONITORING_GCS_ENVIRONMENT'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 73, &quot;content&quot;: &quot;      Value: defaultMonitoringGcsEnvironment&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 74, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 75, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 76, &quot;content&quot;: &quot;      Key: 'MONITORING_GCS_ACCOUNT'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 77, &quot;content&quot;: &quot;      Value: defaultMonitoringGcsAccount&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 78, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 79, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 80, &quot;content&quot;: &quot;      Key: 'MONITORING_GCS_NAMESPACE'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 81, &quot;content&quot;: &quot;      Value: defaultMonitoringGcsNamespace&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 82, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 83, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 84, &quot;content&quot;: &quot;      Key: 'MONITORING_GCS_REGION'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 85, &quot;content&quot;: &quot;      Value: siteLocation&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 86, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 87, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 88, &quot;content&quot;: &quot;      Key: 'MONITORING_GCS_AUTH_ID'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 89, &quot;content&quot;: &quot;      Value: defaultMonitoringGcsAuthId&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 90, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 91, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 92, &quot;content&quot;: &quot;      Key: 'MONITORING_GCS_AUTH_ID_TYPE'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 93, &quot;content&quot;: &quot;      Value: 'AuthKeyVault'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 94, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 95, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 96, &quot;content&quot;: &quot;      Key: 'MONITORING_CONFIG_VERSION'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 97, &quot;content&quot;: &quot;      Value: defaultMonitoringConfigVersion&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 98, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 99, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 100, &quot;content&quot;: &quot;      Key: 'MONITORING_USE_GENEVA_CONFIG_SERVICE'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 101, &quot;content&quot;: &quot;      Value: 'true'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 102, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 103, &quot;content&quot;: &quot;  ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 104, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 105, &quot;content&quot;: &quot;var configXml = '&lt;MonitoringManagement eventVersion=\&quot;1\&quot; version=\&quot;1.0\&quot; timestamp=\&quot;2017-12-29T00:00:00Z\&quot; namespace=\&quot;PlaceHolder\&quot;&gt;&lt;/MonitoringManagement&gt;'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 106, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 107, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 108, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 109, &quot;content&quot;: &quot;var kind = (use_windows) ? 'app' : 'linux'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 110, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 111, &quot;content&quot;: &quot;resource serverFarms 'Microsoft.Web/serverfarms@2022-03-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 112, &quot;content&quot;: &quot;  name: server_farm_name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 113, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 114, &quot;content&quot;: &quot;  kind: kind&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 115, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 116, &quot;content&quot;: &quot;    // reserved must be set to true for Linux server farm, otherwise it is false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 117, &quot;content&quot;: &quot;    reserved: !use_windows&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 118, &quot;content&quot;: &quot;    name: server_farm_name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 119, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 120, &quot;content&quot;: &quot;  sku: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 121, &quot;content&quot;: &quot;    name: 'P2v2'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 122, &quot;content&quot;: &quot;    tier: 'PremiumV2'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 123, &quot;content&quot;: &quot;    family: 'Pv2'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 124, &quot;content&quot;: &quot;    capacity: 1&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 125, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 126, &quot;content&quot;: &quot;  tags: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 127, &quot;content&quot;: &quot;    OWNER: owner&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 128, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 129, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 130, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 131, &quot;content&quot;: &quot;resource serverFarms_AntMDS_ConfigJson 'Microsoft.Web/serverfarms/firstPartyApps/settings@2015-08-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 132, &quot;content&quot;: &quot;  name: '${serverFarms.name}/AntMDS/ConfigJson'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 133, &quot;content&quot;: &quot;  location: siteLocation&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 134, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 135, &quot;content&quot;: &quot;    firstPartyId: 'AntMDS'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 136, &quot;content&quot;: &quot;    settingName: 'ConfigJson'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 137, &quot;content&quot;: &quot;    settingValue: string(configJson)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 138, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 139, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 140, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 141, &quot;content&quot;: &quot;resource serverFarms_AntMDS_MdsConfigXml 'Microsoft.Web/serverfarms/firstPartyApps/settings@2015-08-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 142, &quot;content&quot;: &quot;  name: '${serverFarms.name}/AntMDS/MdsConfigXml'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 143, &quot;content&quot;: &quot;  location: siteLocation&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 144, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 145, &quot;content&quot;: &quot;    firstPartyId: 'AntMDS'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 146, &quot;content&quot;: &quot;    settingName: 'MdsConfigXml'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 147, &quot;content&quot;: &quot;    settingValue: configXml&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 148, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 149, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 150, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 151, &quot;content&quot;: &quot;resource serverFarms_AntMDS_CERTIFICATE_PFX_GENEVACERT 'Microsoft.Web/serverfarms/firstPartyApps/keyVaultSettings@2015-08-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 152, &quot;content&quot;: &quot;  name: '${serverFarms.name}/AntMDS/CERTIFICATE_PFX_GENEVACERT'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 153, &quot;content&quot;: &quot;  location: siteLocation&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 154, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 155, &quot;content&quot;: &quot;    firstPartyId: 'AntMDS'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 156, &quot;content&quot;: &quot;    settingName: 'CERTIFICATE_PFX_GENEVACERT'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 157, &quot;content&quot;: &quot;    vaultId: defaultGenevaCertVaultId&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 158, &quot;content&quot;: &quot;    secretName: defaultGenevaCertSecretName&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 159, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 160, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 161, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 162, &quot;content&quot;: &quot;resource serverFarms_AntMDS_CERTIFICATE_PASSWORD_GENEVACERT 'Microsoft.Web/serverfarms/firstPartyApps/settings@2015-08-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 163, &quot;content&quot;: &quot;  name: '${serverFarms.name}/AntMDS/CERTIFICATE_PASSWORD_GENEVACERT'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 164, &quot;content&quot;: &quot;  location: siteLocation&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 165, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 166, &quot;content&quot;: &quot;    firstPartyId: 'AntMDS'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 167, &quot;content&quot;: &quot;    settingName: 'CERTIFICATE_PASSWORD_GENEVACERT'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 168, &quot;content&quot;: &quot;    settingValue: ''&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 169, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 170, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 171, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 172, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 173, &quot;content&quot;: &quot;output id string = serverFarms.id&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 174, &quot;content&quot;: &quot;output kind string = kind&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;      68:       Key: 'DATACENTER'\n      69:       Value: siteLocation\n      70:     }\n      71:     {\n      72:       Key: 'MONITORING_GCS_ENVIRONMENT'\n      73:       Value: defaultMonitoringGcsEnvironment\n      74:     }\n      75:     {\n      76:       Key: 'MONITORING_GCS_ACCOUNT'\n      77:       Value: defaultMonitoringGcsAccount\n      78:     }\n      79:     {\n      80:       Key: 'MONITORING_GCS_NAMESPACE'\n      81:       Value: defaultMonitoringGcsNamespace\n      82:     }\n      83:     {\n      84:       Key: 'MONITORING_GCS_REGION'\n      85:       Value: siteLocation\n      86:     }\n      87:     {\n      88:       Key: 'MONITORING_GCS_AUTH_ID'\n      89:       Value: defaultMonitoringGcsAuthId\n      90:     }\n      91:     {\n      92:       Key: 'MONITORING_GCS_AUTH_ID_TYPE'\n      93:       Value: 'AuthKeyVault'\n      94:     }\n      95:     {\n      96:       Key: 'MONITORING_CONFIG_VERSION'\n      97:       Value: defaultMonitoringConfigVersion\n      98:     }\n      99:     {\n     100:       Key: 'MONITORING_USE_GENEVA_CONFIG_SERVICE'\n     101:       Value: 'true'\n     102:     }\n     103:   ]\n     104: }\n     105: var configXml = '&lt;MonitoringManagement eventVersion=\&quot;1\&quot; version=\&quot;1.0\&quot; timestamp=\&quot;2017-12-29T00:00:00Z\&quot; namespace=\&quot;PlaceHolder\&quot;&gt;&lt;/MonitoringManagement&gt;'\n     106: \n     107: \n     108: \n     109: var kind = (use_windows) ? 'app' : 'linux'\n     110: \n     111: resource serverFarms 'Microsoft.Web/serverfarms@2022-03-01' = {\n     112:   name: server_farm_name\n     113:   location: location\n     114:   kind: kind\n     115:   properties: {\n     116:     // reserved must be set to true for Linux server farm, otherwise it is false\n     117:     reserved: !use_windows\n     118:     name: server_farm_name\n     119:   }\n     120:   sku: {\n     121:     name: 'P2v2'\n     122:     tier: 'PremiumV2'\n     123:     family: 'Pv2'\n     124:     capacity: 1\n     125:   }\n     126:   tags: {\n     127:     OWNER: owner\n     128:   }\n     129: }\n     130: \n     131: resource serverFarms_AntMDS_ConfigJson 'Microsoft.Web/serverfarms/firstPartyApps/settings@2015-08-01' = {\n     132:   name: '${serverFarms.name}/AntMDS/ConfigJson'\n     133:   location: siteLocation\n     134:   properties: {\n     135:     firstPartyId: 'AntMDS'\n     136:     settingName: 'ConfigJson'\n     137:     settingValue: string(configJson)\n     138:   }\n     139: }\n     140: \n     141: resource serverFarms_AntMDS_MdsConfigXml 'Microsoft.Web/serverfarms/firstPartyApps/settings@2015-08-01' = {\n     142:   name: '${serverFarms.name}/AntMDS/MdsConfigXml'\n     143:   location: siteLocation\n     144:   properties: {\n     145:     firstPartyId: 'AntMDS'\n     146:     settingName: 'MdsConfigXml'\n     147:     settingValue: configXml\n     148:   }\n     149: }\n     150: \n     151: resource serverFarms_AntMDS_CERTIFICATE_PFX_GENEVACERT 'Microsoft.Web/serverfarms/firstPartyApps/keyVaultSettings@2015-08-01' = {\n     152:   name: '${serverFarms.name}/AntMDS/CERTIFICATE_PFX_GENEVACERT'\n     153:   location: siteLocation\n     154:   properties: {\n     155:     firstPartyId: 'AntMDS'\n     156:     settingName: 'CERTIFICATE_PFX_GENEVACERT'\n     157:     vaultId: defaultGenevaCertVaultId\n     158:     secretName: defaultGenevaCertSecretName\n     159:   }\n     160: }\n     161: \n     162: resource serverFarms_AntMDS_CERTIFICATE_PASSWORD_GENEVACERT 'Microsoft.Web/serverfarms/firstPartyApps/settings@2015-08-01' = {\n     163:   name: '${serverFarms.name}/AntMDS/CERTIFICATE_PASSWORD_GENEVACERT'\n     164:   location: siteLocation\n     165:   properties: {\n     166:     firstPartyId: 'AntMDS'\n     167:     settingName: 'CERTIFICATE_PASSWORD_GENEVACERT'\n&gt;&gt;&gt;  168:     settingValue: ''\n     169:   }\n     170: }\n     171: \n     172: \n     173: output id string = serverFarms.id\n     174: output kind string = kind&quot;, &quot;highlighted_line_content&quot;: &quot;settingValue: ''&quot;}
                </script>
                <script type="application/json" data-finding-context="storage-accounts.bicep_168" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;storage-accounts.bicep&quot;, &quot;line_number&quot;: 168, &quot;total_lines&quot;: 204, &quot;start_line&quot;: 68, &quot;end_line&quot;: 204, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 68, &quot;content&quot;: &quot;      {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 69, &quot;content&quot;: &quot;        id: hubSubnetId&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 70, &quot;content&quot;: &quot;        action: 'Allow'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 71, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 72, &quot;content&quot;: &quot;    ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 73, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 74, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 75, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 76, &quot;content&quot;: &quot;resource storageAccountFuzz 'Microsoft.Storage/storageAccounts@2021-08-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 77, &quot;content&quot;: &quot;  name: storageAccountNameFuzz&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 78, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 79, &quot;content&quot;: &quot;  sku: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 80, &quot;content&quot;: &quot;    name: 'Standard_LRS'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 81, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 82, &quot;content&quot;: &quot;  kind: 'StorageV2'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 83, &quot;content&quot;: &quot;  properties: fuzzStorageProperties&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 84, &quot;content&quot;: &quot;  tags: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 85, &quot;content&quot;: &quot;    OWNER: owner&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 86, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 87, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 88, &quot;content&quot;: &quot;  resource blobServicesFuzz 'blobServices' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 89, &quot;content&quot;: &quot;    name: 'default'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 90, &quot;content&quot;: &quot;    properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 91, &quot;content&quot;: &quot;      deleteRetentionPolicy: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 92, &quot;content&quot;: &quot;        enabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 93, &quot;content&quot;: &quot;        days: 30&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 94, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 95, &quot;content&quot;: &quot;      cors: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 96, &quot;content&quot;: &quot;        corsRules: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 97, &quot;content&quot;: &quot;          {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 98, &quot;content&quot;: &quot;            allowedOrigins: cors_origins&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 99, &quot;content&quot;: &quot;            allowedMethods: ['GET', 'OPTIONS', 'HEAD']&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 100, &quot;content&quot;: &quot;            maxAgeInSeconds: 300&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 101, &quot;content&quot;: &quot;            allowedHeaders: ['*']&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 102, &quot;content&quot;: &quot;            exposedHeaders: ['*']&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 103, &quot;content&quot;: &quot;          }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 104, &quot;content&quot;: &quot;        ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 105, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 106, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 107, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 108, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 109, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 110, &quot;content&quot;: &quot;// Additional fuzz storage (per deployment)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 111, &quot;content&quot;: &quot;resource storageAccountsCorpus 'Microsoft.Storage/storageAccounts@2023-05-01' = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 112, &quot;content&quot;: &quot;  for storage in storageAccountsConfigCorpus: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 113, &quot;content&quot;: &quot;    name: storage.name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 114, &quot;content&quot;: &quot;    location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 115, &quot;content&quot;: &quot;    sku: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 116, &quot;content&quot;: &quot;      name: storage.skuName&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 117, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 118, &quot;content&quot;: &quot;    kind: storage.kind&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 119, &quot;content&quot;: &quot;    properties: fuzzStorageProperties&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 120, &quot;content&quot;: &quot;    tags: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 121, &quot;content&quot;: &quot;      storage_type: 'corpus'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 122, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 123, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 124, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 125, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 126, &quot;content&quot;: &quot;resource blobServicesCorpus 'Microsoft.Storage/storageAccounts/blobServices@2023-05-01' = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 127, &quot;content&quot;: &quot;  for i in range(0, length(storageAccountsConfigCorpus)): {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 128, &quot;content&quot;: &quot;    name: 'default'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 129, &quot;content&quot;: &quot;    parent: storageAccountsCorpus[i]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 130, &quot;content&quot;: &quot;    properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 131, &quot;content&quot;: &quot;      deleteRetentionPolicy: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 132, &quot;content&quot;: &quot;        enabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 133, &quot;content&quot;: &quot;        days: 30&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 134, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 135, &quot;content&quot;: &quot;      cors: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 136, &quot;content&quot;: &quot;        corsRules: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 137, &quot;content&quot;: &quot;          {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 138, &quot;content&quot;: &quot;            allowedOrigins: cors_origins&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 139, &quot;content&quot;: &quot;            allowedMethods: ['GET', 'OPTIONS', 'HEAD']&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 140, &quot;content&quot;: &quot;            maxAgeInSeconds: 300&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 141, &quot;content&quot;: &quot;            allowedHeaders: ['*']&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 142, &quot;content&quot;: &quot;            exposedHeaders: ['*']&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 143, &quot;content&quot;: &quot;          }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 144, &quot;content&quot;: &quot;        ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 145, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 146, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 147, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 148, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 149, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 150, &quot;content&quot;: &quot;// Queues and containers&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 151, &quot;content&quot;: &quot;var fileChangesQueueName = 'file-changes'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 152, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 153, &quot;content&quot;: &quot;var storageAccountFuncQueuesParams = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 154, &quot;content&quot;: &quot;  fileChangesQueueName&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 155, &quot;content&quot;: &quot;  'task-heartbeat'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 156, &quot;content&quot;: &quot;  'node-heartbeat'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 157, &quot;content&quot;: &quot;  'proxy'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 158, &quot;content&quot;: &quot;  'update-queue'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 159, &quot;content&quot;: &quot;  'webhooks'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 160, &quot;content&quot;: &quot;  'signalr-events'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 161, &quot;content&quot;: &quot;  'job-result'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 162, &quot;content&quot;: &quot;  'job-stopped'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 163, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 164, &quot;content&quot;: &quot;resource storageAccountFuncQueues 'Microsoft.Storage/storageAccounts/queueServices/queues@2021-08-01' = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 165, &quot;content&quot;: &quot;  for q in storageAccountFuncQueuesParams: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 166, &quot;content&quot;: &quot;    name: '${storageAccountNameFunc}/default/${q}'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 167, &quot;content&quot;: &quot;    dependsOn: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 168, &quot;content&quot;: &quot;      storageAccountFunc&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 169, &quot;content&quot;: &quot;    ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 170, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 171, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 172, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 173, &quot;content&quot;: &quot;var storageAccountFuncContainersParams = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 174, &quot;content&quot;: &quot;  'vm-scripts'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 175, &quot;content&quot;: &quot;  'repro-scripts'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 176, &quot;content&quot;: &quot;  'proxy-configs'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 177, &quot;content&quot;: &quot;  'task-configs'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 178, &quot;content&quot;: &quot;  'app-logs'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 179, &quot;content&quot;: &quot;  'events'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 180, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 181, &quot;content&quot;: &quot;resource storageAccountFuncBlobContainers 'Microsoft.Storage/storageAccounts/blobServices/containers@2021-08-01' = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 182, &quot;content&quot;: &quot;  for c in storageAccountFuncContainersParams: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 183, &quot;content&quot;: &quot;    name: '${storageAccountNameFunc}/default/${c}'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 184, &quot;content&quot;: &quot;    dependsOn: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 185, &quot;content&quot;: &quot;      storageAccountFunc&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 186, &quot;content&quot;: &quot;    ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 187, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 188, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 189, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 190, &quot;content&quot;: &quot;var storageAccountFuzzContainersParams = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 191, &quot;content&quot;: &quot;  // TODO: Delete this on, or after, March 1st 2024&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 192, &quot;content&quot;: &quot;  // See Work Item #178573 for more details&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 193, &quot;content&quot;: &quot;  'events'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 194, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 195, &quot;content&quot;: &quot;resource storageAccountFuzzBlobContainers 'Microsoft.Storage/storageAccounts/blobServices/containers@2021-08-01' = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 196, &quot;content&quot;: &quot;  for c in storageAccountFuzzContainersParams: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 197, &quot;content&quot;: &quot;    name: '${storageAccountNameFuzz}/default/${c}'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 198, &quot;content&quot;: &quot;    dependsOn: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 199, &quot;content&quot;: &quot;      storageAccountFuzz&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 200, &quot;content&quot;: &quot;    ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 201, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 202, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 203, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 204, &quot;content&quot;: &quot;output queueNameFileChanges string = fileChangesQueueName&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;      68:       {\n      69:         id: hubSubnetId\n      70:         action: 'Allow'\n      71:       }\n      72:     ]\n      73:   }\n      74: }\n      75: \n      76: resource storageAccountFuzz 'Microsoft.Storage/storageAccounts@2021-08-01' = {\n      77:   name: storageAccountNameFuzz\n      78:   location: location\n      79:   sku: {\n      80:     name: 'Standard_LRS'\n      81:   }\n      82:   kind: 'StorageV2'\n      83:   properties: fuzzStorageProperties\n      84:   tags: {\n      85:     OWNER: owner\n      86:   }\n      87: \n      88:   resource blobServicesFuzz 'blobServices' = {\n      89:     name: 'default'\n      90:     properties: {\n      91:       deleteRetentionPolicy: {\n      92:         enabled: true\n      93:         days: 30\n      94:       }\n      95:       cors: {\n      96:         corsRules: [\n      97:           {\n      98:             allowedOrigins: cors_origins\n      99:             allowedMethods: ['GET', 'OPTIONS', 'HEAD']\n     100:             maxAgeInSeconds: 300\n     101:             allowedHeaders: ['*']\n     102:             exposedHeaders: ['*']\n     103:           }\n     104:         ]\n     105:       }\n     106:     }\n     107:   }\n     108: }\n     109: \n     110: // Additional fuzz storage (per deployment)\n     111: resource storageAccountsCorpus 'Microsoft.Storage/storageAccounts@2023-05-01' = [\n     112:   for storage in storageAccountsConfigCorpus: {\n     113:     name: storage.name\n     114:     location: location\n     115:     sku: {\n     116:       name: storage.skuName\n     117:     }\n     118:     kind: storage.kind\n     119:     properties: fuzzStorageProperties\n     120:     tags: {\n     121:       storage_type: 'corpus'\n     122:     }\n     123:   }\n     124: ]\n     125: \n     126: resource blobServicesCorpus 'Microsoft.Storage/storageAccounts/blobServices@2023-05-01' = [\n     127:   for i in range(0, length(storageAccountsConfigCorpus)): {\n     128:     name: 'default'\n     129:     parent: storageAccountsCorpus[i]\n     130:     properties: {\n     131:       deleteRetentionPolicy: {\n     132:         enabled: true\n     133:         days: 30\n     134:       }\n     135:       cors: {\n     136:         corsRules: [\n     137:           {\n     138:             allowedOrigins: cors_origins\n     139:             allowedMethods: ['GET', 'OPTIONS', 'HEAD']\n     140:             maxAgeInSeconds: 300\n     141:             allowedHeaders: ['*']\n     142:             exposedHeaders: ['*']\n     143:           }\n     144:         ]\n     145:       }\n     146:     }\n     147:   }\n     148: ]\n     149: \n     150: // Queues and containers\n     151: var fileChangesQueueName = 'file-changes'\n     152: \n     153: var storageAccountFuncQueuesParams = [\n     154:   fileChangesQueueName\n     155:   'task-heartbeat'\n     156:   'node-heartbeat'\n     157:   'proxy'\n     158:   'update-queue'\n     159:   'webhooks'\n     160:   'signalr-events'\n     161:   'job-result'\n     162:   'job-stopped'\n     163: ]\n     164: resource storageAccountFuncQueues 'Microsoft.Storage/storageAccounts/queueServices/queues@2021-08-01' = [\n     165:   for q in storageAccountFuncQueuesParams: {\n     166:     name: '${storageAccountNameFunc}/default/${q}'\n     167:     dependsOn: [\n&gt;&gt;&gt;  168:       storageAccountFunc\n     169:     ]\n     170:   }\n     171: ]\n     172: \n     173: var storageAccountFuncContainersParams = [\n     174:   'vm-scripts'\n     175:   'repro-scripts'\n     176:   'proxy-configs'\n     177:   'task-configs'\n     178:   'app-logs'\n     179:   'events'\n     180: ]\n     181: resource storageAccountFuncBlobContainers 'Microsoft.Storage/storageAccounts/blobServices/containers@2021-08-01' = [\n     182:   for c in storageAccountFuncContainersParams: {\n     183:     name: '${storageAccountNameFunc}/default/${c}'\n     184:     dependsOn: [\n     185:       storageAccountFunc\n     186:     ]\n     187:   }\n     188: ]\n     189: \n     190: var storageAccountFuzzContainersParams = [\n     191:   // TODO: Delete this on, or after, March 1st 2024\n     192:   // See Work Item #178573 for more details\n     193:   'events'\n     194: ]\n     195: resource storageAccountFuzzBlobContainers 'Microsoft.Storage/storageAccounts/blobServices/containers@2021-08-01' = [\n     196:   for c in storageAccountFuzzContainersParams: {\n     197:     name: '${storageAccountNameFuzz}/default/${c}'\n     198:     dependsOn: [\n     199:       storageAccountFuzz\n     200:     ]\n     201:   }\n     202: ]\n     203: \n     204: output queueNameFileChanges string = fileChangesQueueName&quot;, &quot;highlighted_line_content&quot;: &quot;storageAccountFunc&quot;}
                </script>
                <script type="application/json" data-finding-context="storage-accounts.bicep_199" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;storage-accounts.bicep&quot;, &quot;line_number&quot;: 199, &quot;total_lines&quot;: 204, &quot;start_line&quot;: 99, &quot;end_line&quot;: 204, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 99, &quot;content&quot;: &quot;            allowedMethods: ['GET', 'OPTIONS', 'HEAD']&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 100, &quot;content&quot;: &quot;            maxAgeInSeconds: 300&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 101, &quot;content&quot;: &quot;            allowedHeaders: ['*']&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 102, &quot;content&quot;: &quot;            exposedHeaders: ['*']&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 103, &quot;content&quot;: &quot;          }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 104, &quot;content&quot;: &quot;        ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 105, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 106, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 107, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 108, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 109, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 110, &quot;content&quot;: &quot;// Additional fuzz storage (per deployment)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 111, &quot;content&quot;: &quot;resource storageAccountsCorpus 'Microsoft.Storage/storageAccounts@2023-05-01' = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 112, &quot;content&quot;: &quot;  for storage in storageAccountsConfigCorpus: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 113, &quot;content&quot;: &quot;    name: storage.name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 114, &quot;content&quot;: &quot;    location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 115, &quot;content&quot;: &quot;    sku: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 116, &quot;content&quot;: &quot;      name: storage.skuName&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 117, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 118, &quot;content&quot;: &quot;    kind: storage.kind&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 119, &quot;content&quot;: &quot;    properties: fuzzStorageProperties&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 120, &quot;content&quot;: &quot;    tags: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 121, &quot;content&quot;: &quot;      storage_type: 'corpus'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 122, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 123, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 124, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 125, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 126, &quot;content&quot;: &quot;resource blobServicesCorpus 'Microsoft.Storage/storageAccounts/blobServices@2023-05-01' = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 127, &quot;content&quot;: &quot;  for i in range(0, length(storageAccountsConfigCorpus)): {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 128, &quot;content&quot;: &quot;    name: 'default'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 129, &quot;content&quot;: &quot;    parent: storageAccountsCorpus[i]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 130, &quot;content&quot;: &quot;    properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 131, &quot;content&quot;: &quot;      deleteRetentionPolicy: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 132, &quot;content&quot;: &quot;        enabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 133, &quot;content&quot;: &quot;        days: 30&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 134, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 135, &quot;content&quot;: &quot;      cors: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 136, &quot;content&quot;: &quot;        corsRules: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 137, &quot;content&quot;: &quot;          {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 138, &quot;content&quot;: &quot;            allowedOrigins: cors_origins&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 139, &quot;content&quot;: &quot;            allowedMethods: ['GET', 'OPTIONS', 'HEAD']&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 140, &quot;content&quot;: &quot;            maxAgeInSeconds: 300&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 141, &quot;content&quot;: &quot;            allowedHeaders: ['*']&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 142, &quot;content&quot;: &quot;            exposedHeaders: ['*']&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 143, &quot;content&quot;: &quot;          }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 144, &quot;content&quot;: &quot;        ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 145, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 146, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 147, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 148, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 149, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 150, &quot;content&quot;: &quot;// Queues and containers&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 151, &quot;content&quot;: &quot;var fileChangesQueueName = 'file-changes'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 152, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 153, &quot;content&quot;: &quot;var storageAccountFuncQueuesParams = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 154, &quot;content&quot;: &quot;  fileChangesQueueName&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 155, &quot;content&quot;: &quot;  'task-heartbeat'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 156, &quot;content&quot;: &quot;  'node-heartbeat'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 157, &quot;content&quot;: &quot;  'proxy'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 158, &quot;content&quot;: &quot;  'update-queue'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 159, &quot;content&quot;: &quot;  'webhooks'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 160, &quot;content&quot;: &quot;  'signalr-events'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 161, &quot;content&quot;: &quot;  'job-result'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 162, &quot;content&quot;: &quot;  'job-stopped'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 163, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 164, &quot;content&quot;: &quot;resource storageAccountFuncQueues 'Microsoft.Storage/storageAccounts/queueServices/queues@2021-08-01' = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 165, &quot;content&quot;: &quot;  for q in storageAccountFuncQueuesParams: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 166, &quot;content&quot;: &quot;    name: '${storageAccountNameFunc}/default/${q}'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 167, &quot;content&quot;: &quot;    dependsOn: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 168, &quot;content&quot;: &quot;      storageAccountFunc&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 169, &quot;content&quot;: &quot;    ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 170, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 171, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 172, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 173, &quot;content&quot;: &quot;var storageAccountFuncContainersParams = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 174, &quot;content&quot;: &quot;  'vm-scripts'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 175, &quot;content&quot;: &quot;  'repro-scripts'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 176, &quot;content&quot;: &quot;  'proxy-configs'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 177, &quot;content&quot;: &quot;  'task-configs'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 178, &quot;content&quot;: &quot;  'app-logs'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 179, &quot;content&quot;: &quot;  'events'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 180, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 181, &quot;content&quot;: &quot;resource storageAccountFuncBlobContainers 'Microsoft.Storage/storageAccounts/blobServices/containers@2021-08-01' = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 182, &quot;content&quot;: &quot;  for c in storageAccountFuncContainersParams: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 183, &quot;content&quot;: &quot;    name: '${storageAccountNameFunc}/default/${c}'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 184, &quot;content&quot;: &quot;    dependsOn: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 185, &quot;content&quot;: &quot;      storageAccountFunc&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 186, &quot;content&quot;: &quot;    ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 187, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 188, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 189, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 190, &quot;content&quot;: &quot;var storageAccountFuzzContainersParams = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 191, &quot;content&quot;: &quot;  // TODO: Delete this on, or after, March 1st 2024&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 192, &quot;content&quot;: &quot;  // See Work Item #178573 for more details&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 193, &quot;content&quot;: &quot;  'events'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 194, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 195, &quot;content&quot;: &quot;resource storageAccountFuzzBlobContainers 'Microsoft.Storage/storageAccounts/blobServices/containers@2021-08-01' = [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 196, &quot;content&quot;: &quot;  for c in storageAccountFuzzContainersParams: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 197, &quot;content&quot;: &quot;    name: '${storageAccountNameFuzz}/default/${c}'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 198, &quot;content&quot;: &quot;    dependsOn: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 199, &quot;content&quot;: &quot;      storageAccountFuzz&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 200, &quot;content&quot;: &quot;    ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 201, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 202, &quot;content&quot;: &quot;]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 203, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 204, &quot;content&quot;: &quot;output queueNameFileChanges string = fileChangesQueueName&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;      99:             allowedMethods: ['GET', 'OPTIONS', 'HEAD']\n     100:             maxAgeInSeconds: 300\n     101:             allowedHeaders: ['*']\n     102:             exposedHeaders: ['*']\n     103:           }\n     104:         ]\n     105:       }\n     106:     }\n     107:   }\n     108: }\n     109: \n     110: // Additional fuzz storage (per deployment)\n     111: resource storageAccountsCorpus 'Microsoft.Storage/storageAccounts@2023-05-01' = [\n     112:   for storage in storageAccountsConfigCorpus: {\n     113:     name: storage.name\n     114:     location: location\n     115:     sku: {\n     116:       name: storage.skuName\n     117:     }\n     118:     kind: storage.kind\n     119:     properties: fuzzStorageProperties\n     120:     tags: {\n     121:       storage_type: 'corpus'\n     122:     }\n     123:   }\n     124: ]\n     125: \n     126: resource blobServicesCorpus 'Microsoft.Storage/storageAccounts/blobServices@2023-05-01' = [\n     127:   for i in range(0, length(storageAccountsConfigCorpus)): {\n     128:     name: 'default'\n     129:     parent: storageAccountsCorpus[i]\n     130:     properties: {\n     131:       deleteRetentionPolicy: {\n     132:         enabled: true\n     133:         days: 30\n     134:       }\n     135:       cors: {\n     136:         corsRules: [\n     137:           {\n     138:             allowedOrigins: cors_origins\n     139:             allowedMethods: ['GET', 'OPTIONS', 'HEAD']\n     140:             maxAgeInSeconds: 300\n     141:             allowedHeaders: ['*']\n     142:             exposedHeaders: ['*']\n     143:           }\n     144:         ]\n     145:       }\n     146:     }\n     147:   }\n     148: ]\n     149: \n     150: // Queues and containers\n     151: var fileChangesQueueName = 'file-changes'\n     152: \n     153: var storageAccountFuncQueuesParams = [\n     154:   fileChangesQueueName\n     155:   'task-heartbeat'\n     156:   'node-heartbeat'\n     157:   'proxy'\n     158:   'update-queue'\n     159:   'webhooks'\n     160:   'signalr-events'\n     161:   'job-result'\n     162:   'job-stopped'\n     163: ]\n     164: resource storageAccountFuncQueues 'Microsoft.Storage/storageAccounts/queueServices/queues@2021-08-01' = [\n     165:   for q in storageAccountFuncQueuesParams: {\n     166:     name: '${storageAccountNameFunc}/default/${q}'\n     167:     dependsOn: [\n     168:       storageAccountFunc\n     169:     ]\n     170:   }\n     171: ]\n     172: \n     173: var storageAccountFuncContainersParams = [\n     174:   'vm-scripts'\n     175:   'repro-scripts'\n     176:   'proxy-configs'\n     177:   'task-configs'\n     178:   'app-logs'\n     179:   'events'\n     180: ]\n     181: resource storageAccountFuncBlobContainers 'Microsoft.Storage/storageAccounts/blobServices/containers@2021-08-01' = [\n     182:   for c in storageAccountFuncContainersParams: {\n     183:     name: '${storageAccountNameFunc}/default/${c}'\n     184:     dependsOn: [\n     185:       storageAccountFunc\n     186:     ]\n     187:   }\n     188: ]\n     189: \n     190: var storageAccountFuzzContainersParams = [\n     191:   // TODO: Delete this on, or after, March 1st 2024\n     192:   // See Work Item #178573 for more details\n     193:   'events'\n     194: ]\n     195: resource storageAccountFuzzBlobContainers 'Microsoft.Storage/storageAccounts/blobServices/containers@2021-08-01' = [\n     196:   for c in storageAccountFuzzContainersParams: {\n     197:     name: '${storageAccountNameFuzz}/default/${c}'\n     198:     dependsOn: [\n&gt;&gt;&gt;  199:       storageAccountFuzz\n     200:     ]\n     201:   }\n     202: ]\n     203: \n     204: output queueNameFileChanges string = fileChangesQueueName&quot;, &quot;highlighted_line_content&quot;: &quot;storageAccountFuzz&quot;}
                </script>


    <style>
        /* Glass UI Ripple Effect */
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* Code Dialog Modal Styling */
        .code-dialog-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: hsla(var(--hue-primary), 90%, 5%, 0.8);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 10000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .code-dialog {
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur-strong);
            -webkit-backdrop-filter: var(--glass-blur-strong);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--glass-shadow-xl);
            max-width: 90vw;
            max-height: 80vh;
            width: 1000px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .code-dialog-header {
            background: linear-gradient(135deg, var(--primary500), var(--primary600));
            color: var(--text-on-dark);
            padding: 1.5rem 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
        }

        .code-dialog-title {
            font-size: 1.25rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .code-dialog-close {
            background: none;
            border: none;
            color: var(--text-on-dark);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 2.5rem;
            height: 2.5rem;
        }

        .code-dialog-close:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        .code-dialog-content {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .code-snippet-container {
            flex: 1;
            overflow: auto;
            background: var(--dark-gray100);
            margin: 0;
            position: relative;
        }

        .code-snippet-with-lines {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 0.875rem;
            line-height: 1.6;
            color: var(--text-accent);
            padding: 0;
            margin: 0;
            display: flex;
            min-height: 100%;
        }

        .line-numbers {
            background: rgba(30, 30, 60, 0.95);
            color: #ffffff;
            padding: 1rem 0.75rem;
            text-align: right;
            user-select: none;
            border-right: 3px solid #4a90e2;
            min-width: 4rem;
            font-weight: 700;
            font-size: 1rem;
            font-family: 'Courier New', monospace;
            line-height: 1.6;
        }

        .code-content {
            flex: 1;
            padding: 1rem;
            white-space: pre;
            overflow-x: auto;
        }

        .highlighted-line {
            background: hsla(var(--hue-primary), 90%, 50%, 0.2);
            border-left: 3px solid var(--primary500);
            margin-left: -1rem;
            padding-left: calc(1rem - 3px);
            position: relative;
        }

        .highlighted-line::before {
            content: '← Security Issue';
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: var(--danger-red);
            color: var(--text-on-dark);
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
            opacity: 0.9;
        }

        .line-numbers .highlighted-line-number {
            background: #ff4444 !important;
            color: #ffffff !important;
            padding: 0.4rem 0.8rem !important;
            border-radius: 0.5rem !important;
            font-weight: 900 !important;
            font-size: 1rem !important;
            box-shadow: 0 4px 12px rgba(255, 68, 68, 0.6) !important;
            position: relative !important;
            border: 2px solid #ff6666 !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
            animation: pulse 2s infinite !important;
        }

        @keyframes pulse {
            0% { box-shadow: 0 4px 12px rgba(255, 68, 68, 0.6); }
            50% { box-shadow: 0 6px 20px rgba(255, 68, 68, 0.8); }
            100% { box-shadow: 0 4px 12px rgba(255, 68, 68, 0.6); }
        }

        .line-numbers .highlighted-line-number::after {
            content: '●';
            position: absolute;
            right: -8px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--danger-red);
            font-size: 0.75rem;
        }

        /* Enhanced line number visibility - Force visible styling */
        .line-numbers div {
            color: #ffffff !important;
            background: rgba(255, 255, 255, 0.1) !important;
            padding: 0.25rem 0.5rem !important;
            margin: 0.125rem 0 !important;
            border-radius: 0.25rem !important;
            transition: all 0.2s ease !important;
            font-weight: 700 !important;
            font-size: 0.95rem !important;
            line-height: 1.6 !important;
            display: block !important;
            text-align: right !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
        }

        .line-numbers div:hover {
            background: rgba(74, 144, 226, 0.5) !important;
            color: #ffffff !important;
            transform: scale(1.05) !important;
            border-color: #4a90e2 !important;
        }

        .context-info {
            background: var(--glass-white-strong);
            color: var(--text-interactive);
            padding: 0.75rem 1rem;
            margin: 0 -1rem 1rem -1rem;
            border-bottom: 1px solid var(--glass-border);
            font-size: 0.875rem;
            font-weight: 500;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .context-info-spacer {
            height: 2.5rem;
            background: var(--glass-white-strong);
            border-bottom: 1px solid var(--glass-border);
        }

        /* Code Snippet Section Styles */
        .code-snippet-section {
            margin-top: 1.5rem;
            background: var(--glass-white-light);
            border: 1px solid var(--glass-border);
            border-radius: 0.75rem;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .code-snippet-section.fallback {
            background: var(--glass-white-medium);
            border-color: var(--warning-amber);
        }

        .code-snippet-header {
            background: var(--glass-white-strong);
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--glass-border);
        }

        .code-snippet-header h4 {
            margin: 0 0 0.5rem 0;
            color: var(--text-primary);
            font-size: 1rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .code-info {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .code-info span {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .code-snippet-content {
            max-height: 600px;
            overflow-y: auto;
            background: var(--glass-dark);
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .code-line, .highlighted-code-line {
            display: flex;
            align-items: flex-start;
            padding: 0.25rem 0;
            border-bottom: 1px solid hsla(var(--hue-primary), 20%, 80%, 0.1);
        }

        .highlighted-code-line {
            background: hsla(var(--hue-primary), 90%, 50%, 0.15);
            border-left: 3px solid var(--danger-red);
            position: relative;
        }

        .line-number {
            display: inline-block;
            width: 4rem;
            text-align: right;
            color: var(--text-primary);
            background: var(--glass-white-light);
            padding: 0.25rem 0.75rem;
            border-right: 2px solid var(--glass-border);
            user-select: none;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .highlighted-code-line .line-number {
            background: var(--danger-red);
            color: var(--text-on-dark);
            font-weight: 700;
        }

        .line-content {
            flex: 1;
            padding: 0.25rem 1rem;
            color: var(--text-primary);
            white-space: pre-wrap;
            word-break: break-word;
        }

        .security-marker {
            color: var(--danger-red);
            font-weight: 600;
            font-size: 0.75rem;
            margin-left: 1rem;
            opacity: 0.9;
        }

        .code-snippet-footer {
            background: var(--glass-white-strong);
            padding: 1rem 1.5rem;
            border-top: 1px solid var(--glass-border);
            display: flex;
            justify-content: flex-end;
        }

        .copy-code-btn {
            background: var(--primary500);
            color: var(--text-on-dark);
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;
        }

        .copy-code-btn:hover {
            background: var(--primary600);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px hsla(var(--hue-primary), 90%, 50%, 0.3);
        }

        .fallback-content {
            padding: 1.5rem;
            color: var(--text-secondary);
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            background: var(--glass-white-medium);
        }

        .fallback-content pre {
            margin: 0;
            white-space: pre-wrap;
            word-break: break-word;
        }

        .code-dialog-footer {
            background: var(--glass-white-light);
            padding: 1rem 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-top: 1px solid var(--glass-border);
            border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
        }

        .code-dialog-info {
            color: var(--text-secondary);
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .code-dialog-actions {
            display: flex;
            gap: 0.75rem;
        }

        .code-dialog-btn {
            padding: 0.375rem 0.75rem;
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-sm);
            background: var(--glass-white-light);
            color: var(--text-interactive);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.8125rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.375rem;
        }

        .code-dialog-btn:hover {
            background: var(--glass-white-strong);
            transform: translateY(-1px);
            box-shadow: var(--glass-shadow);
        }

        .code-dialog-btn.primary {
            background: linear-gradient(135deg, var(--primary500), var(--primary600));
            color: var(--text-on-dark);
            border-color: var(--primary500);
        }

        .code-dialog-btn.primary:hover {
            box-shadow: 0 4px 12px hsla(var(--hue-primary), 90%, 50%, 0.4);
        }

        /* Professional Static Styling - No Distracting Animations */

        /* Code Dialog Responsive Design */
        @media (max-width: 768px) {
            .code-dialog-overlay {
                padding: 1rem;
            }

            .code-dialog {
                width: 100%;
                max-height: 90vh;
            }

            .code-dialog-header {
                padding: 1rem 1.5rem;
            }

            .code-dialog-title {
                font-size: 1.125rem;
            }

            .code-snippet-with-lines {
                font-size: 0.75rem;
            }

            .line-numbers {
                min-width: 3rem;
                padding: 1rem 0.5rem;
                font-size: 0.85rem;
            }

            .code-dialog-footer {
                padding: 1rem 1.5rem;
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }

            .code-dialog-actions {
                justify-content: stretch;
            }

            .code-dialog-btn {
                flex: 1;
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .code-dialog-overlay {
                padding: 0.5rem;
            }

            .code-dialog {
                max-height: 95vh;
            }

            .code-dialog-header {
                padding: 0.75rem 1rem;
            }

            .code-dialog-title {
                font-size: 1rem;
            }

            .code-snippet-with-lines {
                font-size: 0.6875rem;
            }

            .code-dialog-footer {
                padding: 0.75rem 1rem;
            }
        }

        /* Glass UI Print Styles */
        @media print {
            body::before {
                display: none;
            }

            .report-header,
            .controls-section,
            .summary-section,
            .severity-group,
            .domain-section {
                background: white !important;
                backdrop-filter: none !important;
                -webkit-backdrop-filter: none !important;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
            }

            .export-actions,
            .code-dialog-overlay {
                display: none !important;
            }
        }
    </style>
</head>

<body>
    <div class="main-container">
        <!-- Header Section -->
        <header class="report-header">
            <h1 class="report-title">
                <i class="fas fa-shield-alt"></i>
                Security Assessment Report
            </h1>
            <p class="report-subtitle">Infrastructure as Code Security Analysis</p>
            <div class="report-meta">
                <div class="meta-item">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Generated: June 19, 2025 at 12:38 PM</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-robot"></i>
                    <span>IaC Guardian</span>
                </div>
            </div>
        </header>

        <!-- Controls Section -->
        <section class="controls-section">
            <div class="controls-grid">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search findings..." aria-label="Search findings">
                    <i class="fas fa-search search-icon"></i>
                </div>
                <div class="filter-buttons">
                    <button class="filter-btn all active" data-severity="all">All</button>
                    <button class="filter-btn critical" data-severity="critical">Critical</button>
                    <button class="filter-btn high" data-severity="high">High</button>
                    <button class="filter-btn medium" data-severity="medium">Medium</button>
                    <button class="filter-btn low" data-severity="low">Low</button>
                </div>
            </div>
        </section>

        
        <!-- Summary Section -->
        <section class="summary-section">
            <h2 class="summary-title">
                <i class="fas fa-chart-bar"></i>
                Executive Summary
            </h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">26</div>
                    <div class="stat-label">Total Findings</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">9</div>
                    <div class="stat-label">Files Affected</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">24</div>
                    <div class="stat-label">High Priority Issues</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">9</div>
                    <div class="stat-label">Security Controls</div>
                </div>
            </div>
            <div class="severity-overview">
                <div class="severity-badge critical">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        Critical
                    </div>
                    <div class="severity-count">13</div>
                </div>
                <div class="severity-badge high">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        High
                    </div>
                    <div class="severity-count">11</div>
                </div>
                <div class="severity-badge medium">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        Medium
                    </div>
                    <div class="severity-count">2</div>
                </div>
            </div>
        </section>

        <div class="findings-container">
            
            <div class="domain-section">
                <h3 class="domain-header">
                    <i class="fas fa-shield-alt"></i>
                    Identity Management (6 findings)
                </h3>
                <section class="severity-group" data-severity="critical" data-domain="identity-management">
                    <header class="severity-header critical">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">Critical Severity</div>
                            <div class="severity-count">1</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">IM-8</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>server-farms.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 168</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('server-farms.bicep', 168, 'IM-8', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The 'settingValue' property for 'CERTIFICATE_PASSWORD_GENEVACERT' is set to an empty string on line 168. This creates an attack vector where the password for a sensitive certificate is left unset, potentially allowing unauthorized access to the certificate if the Key Vault or application logic does not enforce additional controls. An attacker who gains access to the Key Vault or the application could retrieve and use the certificate without needing a password, increasing the blast radius to all resources protected by this certificate.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Store certificate passwords securely in Azure Key Vault and reference them using secure access patterns. Do not leave 'settingValue' empty for sensitive secrets. Update line 168 to retrieve the password from a Key Vault secret, and ensure managed identities are used for access. Example: settingValue: reference(resourceId('Microsoft.KeyVault/vaults/secrets', '<vaultName>', '<secretName>'), '2015-06-01').value</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    </div>
                </section>
                <section class="severity-group" data-severity="high" data-domain="identity-management">
                    <header class="severity-header high">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">High Severity</div>
                            <div class="severity-count">5</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item" data-severity="high">
                        <header class="finding-header">
                            <div class="finding-icon high">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">IM-1</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>app-config.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 1</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('app-config.bicep', 1, 'IM-1', 'high')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">Privilege escalation risk: Security-sensitive parameter 'keyValues' flows across template boundary</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Secure parameter 'keyValues' and validate its usage across template boundaries</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="high">
                        <header class="finding-header">
                            <div class="finding-icon high">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">IM-1</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>function-settings.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 1</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('function-settings.bicep', 1, 'IM-1', 'high')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">Privilege escalation risk: Security-sensitive parameter 'app_insights_key' flows across template boundary</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Secure parameter 'app_insights_key' and validate its usage across template boundaries</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="high">
                        <header class="finding-header">
                            <div class="finding-icon high">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">IM-1</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>function-settings.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 1</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('function-settings.bicep', 1, 'IM-1', 'high')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">Privilege escalation risk: Security-sensitive parameter 'keyvault_name' flows across template boundary</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Secure parameter 'keyvault_name' and validate its usage across template boundaries</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="high">
                        <header class="finding-header">
                            <div class="finding-icon high">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">IM-1</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>keyvault.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 1</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('keyvault.bicep', 1, 'IM-1', 'high')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">Privilege escalation risk: Security-sensitive parameter 'secrets' flows across template boundary</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Secure parameter 'secrets' and validate its usage across template boundaries</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="high">
                        <header class="finding-header">
                            <div class="finding-icon high">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">IM-2</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>function.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 58</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('function.bicep', 58, 'IM-2', 'high')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The 'remoteDebuggingEnabled' property in the extraProperties configuration (Line 058) is set to true when 'enable_remote_debugging' is enabled. Remote debugging exposes a privileged interface that can be targeted by attackers for initial access or privilege escalation, especially if not properly restricted or monitored. This increases the attack surface and can allow attackers to execute arbitrary code or extract sensitive information from the running application.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Disable remote debugging in production environments by setting 'remoteDebuggingEnabled' to false. Only enable remote debugging temporarily for troubleshooting, and restrict access to trusted IPs. Monitor and audit all remote debugging sessions. Update the configuration to: remoteDebuggingEnabled: false. Follow Azure Security Benchmark IM-2 for securing privileged access.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    </div>
                </section>
            </div>
            <div class="domain-section">
                <h3 class="domain-header">
                    <i class="fas fa-shield-alt"></i>
                    Network Security (12 findings)
                </h3>
                <section class="severity-group" data-severity="critical" data-domain="network-security">
                    <header class="severity-header critical">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">Critical Severity</div>
                            <div class="severity-count">10</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-1</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>hub-network.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 14</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('hub-network.bicep', 14, 'NS-1', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The virtual network 'vnet' defined at Line 003 does not include any Network Security Group (NSG) association for its subnet 'hub-subnet'. Without an NSG, there is no network-level access control, enabling attackers to exploit open network paths for initial access, lateral movement, and data exfiltration. The blast radius includes all resources deployed in this subnet, as unrestricted traffic could reach any attached service.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Associate a Network Security Group (NSG) with the 'hub-subnet' by adding an 'networkSecurityGroup' property referencing a properly configured NSG resource. Ensure the NSG has a default deny-all inbound rule and only allows explicitly required traffic.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-1</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>ip-rules.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 5</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('ip-rules.bicep', 5, 'NS-1', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The 'corpNetIps' variable on line 4 includes extremely broad IP ranges such as '*******/8', '********/8', and others. Allowing /8 and /16 ranges in network access rules can expose resources to a vast number of external IPs, significantly increasing the attack surface for initial access, lateral movement, and data exfiltration. This violates Azure Security Benchmark NS-1, which requires network segmentation and default deny-all posture to minimize exposure.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Restrict allowed IP ranges to the smallest possible subnets that are strictly necessary for business operations. Replace broad /8 and /16 ranges with specific, minimal subnets or individual IP addresses. Implement NSGs with default deny rules and only allow traffic from trusted, well-defined sources.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-1</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>scaleset-networks.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 66</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('scaleset-networks.bicep', 66, 'NS-1', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The 'defaultOutboundAccess' property is set to true on the subnet configuration. This enables unrestricted outbound internet access from the 'scaleset' subnet, violating network segmentation and defense-in-depth principles. Attackers who compromise a resource in this subnet can exfiltrate data or establish command and control channels, increasing the blast radius and enabling lateral movement.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Set 'defaultOutboundAccess' to false on line 66 to disable unrestricted outbound internet access. Instead, restrict outbound traffic using Network Security Groups (NSGs) with deny-by-default rules and only allow required destinations. Implement NSGs at the subnet and NIC levels as per Azure Security Benchmark NS-1.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-2</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>function.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 4</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('function.bicep', 4, 'NS-2', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The 'allowedOrigins' property in the CORS configuration (Line 052) is set to the 'cors_origins' parameter, which may allow broad or unvalidated origins. If this array is not strictly limited to trusted domains, it enables attackers to perform cross-origin attacks, potentially exfiltrating sensitive data or hijacking authenticated sessions from the App Service. The blast radius includes exposure of all APIs and data accessible via the function app to any origin specified, increasing the risk of data exfiltration and unauthorized access.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Restrict the 'cors_origins' parameter to only trusted, specific domains. Avoid using wildcards or overly broad origins. Implement validation to ensure only approved domains are allowed. Example: ['https://trusted.example.com']. Review and update the CORS configuration to minimize the attack surface as per Azure Security Benchmark NS-2.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-2</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>ip-rules.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 5</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('ip-rules.bicep', 5, 'NS-2', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The 'corpNetIps' variable on line 4 contains large public IP ranges (e.g., '*******/8', '********/8'), which, if used for public endpoint access, can allow unauthorized external access to resources. This configuration increases the risk of unauthorized access and data exposure, violating Azure Security Benchmark NS-2, which mandates strict access control for public endpoints and recommends using Private Link or service endpoints.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Limit public endpoint access to only specific, trusted IP addresses. Where possible, use Azure Private Link or service endpoints to eliminate public exposure. Remove broad public IP ranges from access rules and enforce strict allowlists.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-2</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>storage-accounts.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 168</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('storage-accounts.bicep', 168, 'NS-2', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The 'defaultAction' property in the 'networkAcls' block for the 'storageAccountFunc' resource is set to 'Allow' (Line 031). This configuration allows public network access to the storage account unless explicitly denied by IP or VNet rules. Attackers could exploit this to gain initial access to storage resources from any IP not explicitly blocked, increasing the risk of data exfiltration and lateral movement. The blast radius includes all data and services within this storage account.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Set 'networkAcls.defaultAction' to 'Deny' on Line 031 to block all public network access by default. Only allow access from explicitly defined IP addresses or virtual networks. Consider implementing Private Endpoints for the storage account to eliminate public exposure.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-2</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>storage-accounts.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 199</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('storage-accounts.bicep', 199, 'NS-2', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The 'defaultAction' property in the 'networkAcls' block for the 'fuzzStorageProperties' object (used by 'storageAccountFuzz' and 'storageAccountsCorpus') is set to 'Allow' (Line 065). This exposes the storage accounts to public network access unless specifically restricted, enabling attackers to attempt unauthorized access from the internet. The blast radius includes all fuzz and corpus storage accounts using this configuration.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Set 'networkAcls.defaultAction' to 'Deny' on Line 065 to ensure public network access is blocked by default. Only permit access from trusted IPs or VNets. For maximum security, use Private Endpoints to restrict access to internal networks only.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-3</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>hub-network.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 14</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('hub-network.bicep', 14, 'NS-3', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The virtual network 'vnet' at Line 003 and its subnet 'hub-subnet' lack an associated Network Security Group (NSG) with deny-by-default rules. This omission allows all inbound traffic by default, exposing the subnet to unauthorized access and enabling attackers to move laterally or escalate privileges within the network.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Create and associate an NSG with the 'hub-subnet' that implements a deny-all inbound rule by default. Only allow necessary traffic by adding explicit allow rules for required ports and protocols.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-3</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>ip-rules.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 3</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('ip-rules.bicep', 3, 'NS-3', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The 'corpNetRules' array on line 31 is constructed to allow all IPs in 'corpNetIps', which includes very broad ranges. Without explicit deny-by-default rules, this can result in excessive network exposure, enabling attackers to access resources from a wide range of IPs. This violates Azure Security Benchmark NS-3, which requires NSGs to deny all inbound traffic by default and only allow necessary traffic.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Implement NSGs with a default deny-all inbound rule. Only allow specific, minimal IP addresses or subnets that are required for business operations. Review and reduce the allowed IP ranges in 'corpNetRules' to enforce least privilege network access.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-3</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>scaleset-networks.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 66</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('scaleset-networks.bicep', 66, 'NS-3', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The subnet configuration on line 66 enables 'defaultOutboundAccess', which bypasses deny-by-default network security group (NSG) rules. Without explicit NSG rules, all outbound traffic is allowed, creating a significant attack vector for data exfiltration and lateral movement. This configuration undermines network-level access control and exposes the environment to external threats.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Remove or set 'defaultOutboundAccess' to false on line 66. Deploy NSGs with deny-by-default rules to the subnet and explicitly allow only necessary outbound traffic. Ensure that all subnets are protected by NSGs as required by Azure Security Benchmark NS-3.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    </div>
                </section>
                <section class="severity-group" data-severity="medium" data-domain="network-security">
                    <header class="severity-header medium">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">Medium Severity</div>
                            <div class="severity-count">2</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item" data-severity="medium">
                        <header class="finding-header">
                            <div class="finding-icon medium">
                                <i class="fas fa-exclamation"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-1</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>keyvault.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 1</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('keyvault.bicep', 1, 'NS-1', 'medium')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">Cross-template trust boundary: Template references external templates/modules</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Review template dependencies and ensure secure communication between templates</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="medium">
                        <header class="finding-header">
                            <div class="finding-icon medium">
                                <i class="fas fa-exclamation"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-1</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>storage-accounts.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 1</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('storage-accounts.bicep', 1, 'NS-1', 'medium')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">Cross-template trust boundary: Template references external templates/modules</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Review template dependencies and ensure secure communication between templates</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    </div>
                </section>
            </div>
            <div class="domain-section">
                <h3 class="domain-header">
                    <i class="fas fa-shield-alt"></i>
                    Data Protection (8 findings)
                </h3>
                <section class="severity-group" data-severity="critical" data-domain="data-protection">
                    <header class="severity-header critical">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">Critical Severity</div>
                            <div class="severity-count">2</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-3</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>function.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 48</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('function.bicep', 48, 'DP-3', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The 'ftpsState' property in the App Service site configuration (Line 048) is set to 'Disabled', which means FTPS (FTP over TLS/SSL) is not enabled. This allows for the possibility of unencrypted FTP connections if FTP is enabled elsewhere, exposing credentials and data in transit to interception and man-in-the-middle attacks. Attackers could capture sensitive information or inject malicious content, increasing the risk of data compromise.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Set 'ftpsState' to 'FtpsOnly' to enforce encrypted FTPS connections and prevent unencrypted FTP access. This ensures all data transfers are protected in transit. Update the configuration as follows: ftpsState: 'FtpsOnly'. Refer to Azure Security Benchmark DP-3 for secure data-in-transit requirements.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-8</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>keyvault.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 28</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('keyvault.bicep', 28, 'DP-8', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The 'networkAcls.defaultAction' property is set to 'Allow' for the Key Vault resource. This configuration allows public network access to the Key Vault, enabling attackers to attempt unauthorized access from any IP address. The blast radius includes potential exposure of all secrets, keys, and certificates stored in the vault, and increases the risk of data exfiltration and lateral movement if credentials are compromised.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Set 'networkAcls.defaultAction' to 'Deny' to restrict public network access. Only allow access from explicitly defined IP addresses or virtual networks using 'ipRules' and 'virtualNetworkRules'. Example: 'defaultAction: "Deny"'. Additionally, consider enabling Private Endpoint for the Key Vault to further reduce exposure.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    </div>
                </section>
                <section class="severity-group" data-severity="high" data-domain="data-protection">
                    <header class="severity-header high">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">High Severity</div>
                            <div class="severity-count">6</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item" data-severity="high">
                        <header class="finding-header">
                            <div class="finding-icon high">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-3</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>keyvault.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 4</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('keyvault.bicep', 4, 'DP-3', 'high')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The Key Vault's 'networkAcls.defaultAction' is set to 'Allow', which does not enforce secure network boundaries for data in transit. This allows untrusted sources to attempt connections, increasing the risk of interception or man-in-the-middle attacks on data in transit.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Set 'networkAcls.defaultAction' to 'Deny' to enforce secure network boundaries. Only allow access from trusted IP addresses or virtual networks. Ensure all connections to the Key Vault use HTTPS and TLS 1.2 or higher.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="high">
                        <header class="finding-header">
                            <div class="finding-icon high">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-6</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>keyvault.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 1</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('keyvault.bicep', 1, 'DP-6', 'high')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">Parameter flow security risk: Sensitive parameters may be exposed through template dependencies</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Use Key Vault references for sensitive parameters and validate parameter flow security</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="high">
                        <header class="finding-header">
                            <div class="finding-icon high">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-8</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>keyvault.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 15</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('keyvault.bicep', 15, 'DP-8', 'high')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The Key Vault resource does not have any configuration for Private Endpoint or public network access restriction. Without a Private Endpoint, the Key Vault remains accessible over the public internet, increasing the risk of unauthorized access and data exfiltration if network ACLs are misconfigured.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Configure a Private Endpoint for the Key Vault to restrict access to only trusted virtual networks. Update the template to include a 'privateEndpointConnections' property and ensure 'networkAcls.defaultAction' is set to 'Deny'.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="high">
                        <header class="finding-header">
                            <div class="finding-icon high">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-8</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>keyvault.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 22</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('keyvault.bicep', 22, 'DP-8', 'high')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The 'accessPolicies' property is set to an empty array while 'enableRbacAuthorization' is true. If RBAC is not properly configured, this can result in either overly permissive or insufficient access controls, potentially allowing unauthorized users to access or manage secrets, keys, and certificates. Attackers who gain access to a privileged Azure AD role could escalate privileges or exfiltrate sensitive data.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Ensure that Azure RBAC is properly configured for the Key Vault with least privilege principles. Assign only necessary roles (such as Key Vault Reader, Key Vault Secrets User) to trusted identities. Regularly audit RBAC assignments to prevent privilege escalation and unauthorized access.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="high">
                        <header class="finding-header">
                            <div class="finding-icon high">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-8</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>keyvault.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 22</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('keyvault.bicep', 22, 'DP-8', 'high')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The 'enableRbacAuthorization' property is set to 'true', but there is no evidence of managed identities or explicit RBAC role assignments in this template. Without proper RBAC configuration, this can lead to misconfigured access controls, enabling attackers with broad Azure AD privileges to access or manage Key Vault contents.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Explicitly define and assign Azure RBAC roles for the Key Vault to trusted identities or managed identities. Ensure that only required users and services have access, and follow the principle of least privilege. Document and regularly review RBAC assignments.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="high">
                        <header class="finding-header">
                            <div class="finding-icon high">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-8</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>keyvault.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 29</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('keyvault.bicep', 29, 'DP-8', 'high')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The 'networkAcls.bypass' property is set to 'AzureServices', which allows trusted Azure services to bypass network restrictions and access the Key Vault. This increases the attack surface, as any Azure service with the appropriate identity could potentially access the vault if compromised, expanding the blast radius beyond intended boundaries.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Set 'networkAcls.bypass' to 'None' to prevent all bypasses, or restrict to only those services that are strictly necessary. Example: 'bypass: "None"'. Review and limit the list of services that require access to the Key Vault.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    </div>
                </section>
            </div>
            <div class="no-findings" style="display: none;">
                <i class="fas fa-search no-findings-icon"></i>
                <h3>No findings match your search criteria</h3>
                <p>Try adjusting your search terms or filters</p>
            </div>
        </div>

        <!-- Footer Section -->
        <footer class="report-footer">
            <div class="export-actions">
                <button class="export-btn" onclick="window.print()">
                    <i class="fas fa-print"></i>
                    Print Report
                </button>
                <button class="export-btn" onclick="exportToJson()">
                    <i class="fas fa-download"></i>
                    Export JSON
                </button>
            </div>
            <div class="footer-info">
                <p><strong>Generated by IaC Guardian</strong> • June 19, 2025 at 12:38 PM</p>
                <p><strong>Disclaimer:</strong> This report is AI-generated and should be reviewed by security professionals.</p>
            </div>
        </footer>
    </div>

            <script>
            // Enhanced Validation Metadata (for internal use only)
            window.validationMetadata = {
  "generation_timestamp": "2025-06-19T12:38:19.949655",
  "total_findings": 26,
  "validation_statistics": {
    "total_validations": 0,
    "successful_validations": 0,
    "corrections_made": 0,
    "fictional_ids_prevented": 0,
    "domain_corrections": 0
  },
  "analysis_statistics": {},
  "control_id_validation": {
    "total_validations": 0,
    "success_rate": 100.0,
    "corrections_made": 0,
    "fictional_ids_prevented": 0
  },
  "cross_reference_analysis": {
    "cross_ref_findings": 7,
    "template_relationships": 9,
    "security_boundaries_analyzed": 2
  },
  "domain_distribution": {
    "Identity Management": 6,
    "Network Security": 12,
    "Data Protection": 8
  },
  "severity_distribution": {
    "HIGH": 11,
    "MEDIUM": 2,
    "CRITICAL": 13
  },
  "resource_type_coverage": {
    "unique_resource_types": 0,
    "analyzed_files": 9,
    "resource_types": []
  },
  "benchmark_version": "Azure Security Benchmark v3.0",
  "analysis_configuration": {
    "domain_priority_enabled": true,
    "optimized_prompts_enabled": true,
    "control_id_validation_enabled": true,
    "cross_reference_analysis_enabled": true
  }
};

            // Tooltip Links Data
            window.tooltipLinks = {
  "IM-1": {
    "formatted_links": "[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)",
    "azure_guidance": "Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applications to Azure AD when technically feasible.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Azure AD tenancy concepts: https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps\n\u2022 Create and configure Azure AD: https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant\n\u2022 Define Azure AD tenants: https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/\n\u2022 External identity providers: https://docs.microsoft.com/azure/active-directory/b2b/identity-providers\n\u2022 Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys\n\nCompliance Mappings:\n\u2022 CIS Controls v8: 6.7, 12.5\n\u2022 NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8\n\u2022 PCI-DSS v3.2.1: 7.2, 8.3\n\nAzure Policy Examples:\n\u2022 An Azure Active Directory administrator should be provisioned for SQL servers\n\u2022 Service Fabric clusters should only use Azure Active Directory for client authentication\n\u2022 Standardize identity provider across all applications and services",
    "raw_links": [
      "https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps",
      "https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant",
      "https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/",
      "https://docs.microsoft.com/azure/active-directory/b2b/identity-providers",
      "https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys"
    ]
  },
  "NS-1": {
    "formatted_links": "[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)",
    "azure_guidance": "Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices\n\u2022 Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet\n\u2022 NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic\n\u2022 Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups\n\u2022 Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nCompliance Mappings:\n\u2022 CIS Controls v8: 3.12, 13.4, 4.4\n\u2022 NIST SP800-53 r4: AC-4, SC-2, SC-7\n\u2022 PCI-DSS v3.2.1: 1.1, 1.2, 1.3\n\nAzure Policy Examples:\n\u2022 Adaptive network hardening recommendations should be applied on internet facing virtual machines\n\u2022 All network ports should be restricted on network security groups associated to your virtual machine\n\u2022 Subnets should be associated with a Network Security Group",
    "raw_links": [
      "https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices",
      "https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet",
      "https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic",
      "https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups",
      "https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture"
    ]
  },
  "DP-6": {
    "formatted_links": "[Enhanced Implementation Context](https://docs.microsoft.com/azure/key-vault/general/overview) | [Azure data encryption key hierarchy](https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#key-hierarchy) | [BYOK specification](https://docs.microsoft.com/azure/key-vault/keys/byok-specification) | [Key Vault security best practices](https://docs.microsoft.com/azure/key-vault/general/best-practices)",
    "azure_guidance": "Use Azure Key Vault to create and control encryption key lifecycle including generation distribution storage rotation and revocation. Follow best practices for key hierarchy and BYOK scenarios. Ensure FIPS compliance levels meet requirements.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Azure Key Vault overview: https://docs.microsoft.com/azure/key-vault/general/overview\n\u2022 Azure data encryption key hierarchy: https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#key-hierarchy\n\u2022 BYOK specification: https://docs.microsoft.com/azure/key-vault/keys/byok-specification\n\u2022 Key Vault security best practices: https://docs.microsoft.com/azure/key-vault/general/best-practices\n\u2022 FIPS 140-2 compliance levels: Software-protected keys (Level 1) HSM-protected keys in vaults (Level 2) HSM-protected keys in Managed HSM (Level 3)\n\nCompliance Mappings:\n\u2022 CIS Controls v8: Not specified\n\u2022 NIST SP800-53 r4: IA-5, SC-12, SC-28\n\u2022 PCI-DSS v3.2.1: 3.6\n\nAzure Policy Examples:\n\u2022 Key Vault keys should have an expiration date\n\u2022 Key Vault secrets should have an expiration date\n\u2022 Implement key rotation policies\n\u2022 Use separate data encryption keys (DEK) with key encryption keys (KEK)\n\u2022 Register keys with Azure Key Vault using key IDs",
    "raw_links": [
      "https://docs.microsoft.com/azure/key-vault/general/overview",
      "https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#key-hierarchy",
      "https://docs.microsoft.com/azure/key-vault/keys/byok-specification",
      "https://docs.microsoft.com/azure/key-vault/general/best-practices"
    ]
  },
  "NS-2": {
    "formatted_links": "[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)",
    "azure_guidance": "Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview\n\u2022 Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints\n\u2022 SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview\n\u2022 Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service\n\u2022 Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nCompliance Mappings:\n\u2022 CIS Controls v8: 3.12, 4.4\n\u2022 NIST SP800-53 r4: AC-4, SC-2, SC-7\n\u2022 PCI-DSS v3.2.1: 1.1, 1.2, 1.3\n\nAzure Policy Examples:\n\u2022 Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers\n\u2022 Storage accounts should use private link\n\u2022 Azure SQL Database should disable public network access\n\u2022 Cognitive Services accounts should restrict network access\n\u2022 Container registries should use private link",
    "raw_links": [
      "https://docs.microsoft.com/azure/private-link/private-link-overview",
      "https://docs.microsoft.com/azure/storage/common/storage-private-endpoints",
      "https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview",
      "https://docs.microsoft.com/azure/key-vault/general/private-link-service",
      "https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture"
    ]
  },
  "DP-3": {
    "formatted_links": "[Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account)",
    "azure_guidance": "Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windows remote management.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Double encryption for Azure data in transit: https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit\n\u2022 Encryption in transit overview: https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit\n\u2022 TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem\n\u2022 Azure Storage secure transfer: https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account\n\nCompliance Mappings:\n\u2022 CIS Controls v8: 3.10\n\u2022 NIST SP800-53 r4: SC-8\n\u2022 PCI-DSS v3.2.1: 3.5, 3.6, 4.1\n\nAzure Policy Examples:\n\u2022 Kubernetes clusters should be accessible only over HTTPS\n\u2022 Only secure connections to your Azure Cache for Redis should be enabled\n\u2022 FTPS only should be required in your Function App\n\u2022 Secure transfer to storage accounts should be enabled\n\u2022 Function App should only be accessible over HTTPS\n\u2022 Latest TLS version should be used in your API App\n\u2022 Web Application should only be accessible over HTTPS\n\u2022 Enforce SSL connection should be enabled for PostgreSQL database servers\n\u2022 Latest TLS version should be used in your Web App",
    "raw_links": [
      "https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit",
      "https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit",
      "https://docs.microsoft.com/security/engineering/solving-tls1-problem",
      "https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account"
    ]
  },
  "IM-2": {
    "formatted_links": "[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score) | [Active Directory security best practices](https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory) | [Azure AD security baseline](https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline) | [Privileged Identity Management](https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)",
    "azure_guidance": "Use Azure AD security baseline and Identity Secure Score to evaluate security posture. Implement limited administrative roles user risk policies MFA for administrative roles and block legacy authentication.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Azure AD Identity Secure Score: https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score\n\u2022 Active Directory security best practices: https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory\n\u2022 Azure AD security baseline: https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline\n\u2022 Privileged Identity Management: https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure\n\u2022 Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys\n\nCompliance Mappings:\n\u2022 CIS Controls v8: 5.4, 6.5\n\u2022 NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8, SI-4\n\u2022 PCI-DSS v3.2.1: 8.2, 8.3\n\nAzure Policy Examples:\n\u2022 No applicable built-in policy (requires configuration-based implementation)\n\u2022 Use Azure AD Identity Secure Score recommendations\n\u2022 Implement Azure AD security baseline configurations\n\u2022 Monitor privileged account activities through Azure AD logs",
    "raw_links": [
      "https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score",
      "https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory",
      "https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline",
      "https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure",
      "https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys"
    ]
  },
  "NS-3": {
    "formatted_links": "[Enhanced Implementation Context](https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal) | [Virtual network traffic routing](https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview) | [Azure Firewall Manager](https://docs.microsoft.com/azure/firewall-manager/overview) | [Hub-spoke topology](https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)",
    "azure_guidance": "Use Azure Firewall for stateful application layer traffic restriction and central management. Create user-defined routes (UDR) for complex topologies. Block known bad IPs and high-risk protocols.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Azure Firewall deployment: https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal\n\u2022 Virtual network traffic routing: https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview\n\u2022 Azure Firewall Manager: https://docs.microsoft.com/azure/firewall-manager/overview\n\u2022 Hub-spoke topology: https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke\n\u2022 Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nCompliance Mappings:\n\u2022 CIS Controls v8: 4.4, 4.8, 13.10\n\u2022 NIST SP800-53 r4: AC-4, SC-7, CM-7\n\u2022 PCI-DSS v3.2.1: 1.1, 1.2, 1.3\n\nAzure Policy Examples:\n\u2022 Management ports should be closed on your virtual machines\n\u2022 Management ports of virtual machines should be protected with just-in-time network access control\n\u2022 IP Forwarding on your virtual machine should be disabled\n\u2022 All Internet traffic should be routed via your deployed Azure Firewall",
    "raw_links": [
      "https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal",
      "https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview",
      "https://docs.microsoft.com/azure/firewall-manager/overview",
      "https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke",
      "https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture"
    ]
  },
  "DP-8": {
    "formatted_links": "[Enhanced Implementation Context](https://docs.microsoft.com/azure/key-vault/general/overview) | [Key Vault security best practices](https://docs.microsoft.com/azure/key-vault/general/best-practices) | [Managed identity Key Vault access](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad) | [Key Vault Private Link](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Key Vault logging and monitoring](https://docs.microsoft.com/azure/key-vault/general/logging)",
    "azure_guidance": "Secure Azure Key Vault through access policies or Azure RBAC for least privilege. Use Private Link and Azure Firewall for minimal exposure. Implement separation of duties managed identity access and comprehensive logging.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Azure Key Vault overview: https://docs.microsoft.com/azure/key-vault/general/overview\n\u2022 Key Vault security best practices: https://docs.microsoft.com/azure/key-vault/general/best-practices\n\u2022 Managed identity Key Vault access: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad\n\u2022 Key Vault Private Link: https://docs.microsoft.com/azure/key-vault/general/private-link-service\n\u2022 Key Vault logging and monitoring: https://docs.microsoft.com/azure/key-vault/general/logging\n\nCompliance Mappings:\n\u2022 CIS Controls v8: Not specified\n\u2022 NIST SP800-53 r4: IA-5, SC-12, SC-17\n\u2022 PCI-DSS v3.2.1: 3.6\n\nAzure Policy Examples:\n\u2022 Key vaults should have purge protection enabled\n\u2022 Azure Defender for Key Vault should be enabled\n\u2022 Key vaults should have soft delete enabled\n\u2022 Azure Key Vault should disable public network access\n\u2022 Private endpoint should be configured for Key Vault\n\u2022 Resource logs in Key Vault should be enabled\n\u2022 Implement separation of duties for key management and data access",
    "raw_links": [
      "https://docs.microsoft.com/azure/key-vault/general/overview",
      "https://docs.microsoft.com/azure/key-vault/general/best-practices",
      "https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad",
      "https://docs.microsoft.com/azure/key-vault/general/private-link-service",
      "https://docs.microsoft.com/azure/key-vault/general/logging"
    ]
  },
  "IM-8": {
    "formatted_links": "[Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops)",
    "azure_guidance": "Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Azure DevOps Credential Scanner setup: https://secdevtools.azurewebsites.net/helpcredscan.html\n\u2022 GitHub secret scanning: https://docs.github.com/github/administering-a-repository/about-secret-scanning\n\u2022 Azure Key Vault developer guide: https://docs.microsoft.com/azure/key-vault/general/developers-guide\n\u2022 Secure coding practices: https://docs.microsoft.com/azure/security/develop/secure-dev-overview\n\u2022 Application security guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nCompliance Mappings:\n\u2022 CIS Controls v8: 16.9, 16.12\n\u2022 NIST SP800-53 r4: IA-5\n\u2022 PCI-DSS v3.2.1: 3.5, 6.3, 8.2\n\nAzure Policy Examples:\n\u2022 No applicable built-in policy (requires development process implementation)\n\u2022 Implement credential scanning in CI/CD pipelines\n\u2022 Enforce Azure Key Vault usage for secret storage\n\u2022 Monitor and audit secret access patterns\n\u2022 Require managed identities for Azure service authentication",
    "raw_links": [
      "https://secdevtools.azurewebsites.net/helpcredscan.html",
      "https://docs.github.com/github/administering-a-repository/about-secret-scanning",
      "https://docs.microsoft.com/azure/key-vault/general/developers-guide",
      "https://docs.microsoft.com/azure/security/develop/secure-dev-overview",
      "https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops"
    ]
  }
};

            // Initialize tooltip functionality
            document.addEventListener('DOMContentLoaded', function() {
                // Add tooltip functionality to control IDs
                addTooltipFunctionality();
            });

            function addTooltipFunctionality() {
                // Find all control ID elements and add tooltips
                const controlElements = document.querySelectorAll('.control-id, .finding-control');

                controlElements.forEach(element => {
                    const controlId = element.textContent.trim();
                    if (window.tooltipLinks[controlId]) {
                        const tooltipData = window.tooltipLinks[controlId];

                        // Add tooltip icon
                        const tooltipIcon = document.createElement('span');
                        tooltipIcon.className = 'tooltip-icon';
                        tooltipIcon.innerHTML = ' 📚';
                        tooltipIcon.style.cursor = 'pointer';
                        tooltipIcon.style.marginLeft = '5px';
                        tooltipIcon.style.fontSize = '0.8em';

                        // Create tooltip content
                        let tooltipContent = '<div class="tooltip-content">';

                        if (tooltipData.azure_guidance) {
                            tooltipContent += `<div class="tooltip-section">
                                <strong>🔵 Azure Guidance:</strong><br>
                                ${tooltipData.azure_guidance.substring(0, 200)}${tooltipData.azure_guidance.length > 200 ? '...' : ''}
                            </div>`;
                        }

                        if (tooltipData.raw_links && tooltipData.raw_links.length > 0) {
                            tooltipContent += '<div class="tooltip-section"><strong>📚 Reference Links:</strong><br>';
                            tooltipData.raw_links.forEach((link, index) => {
                                const linkText = link.length > 50 ? link.substring(0, 50) + '...' : link;
                                tooltipContent += `<a href="${link}" target="_blank" class="tooltip-link">${linkText}</a><br>`;
                            });
                            tooltipContent += '</div>';
                        }

                        tooltipContent += '</div>';

                        // Add tooltip functionality
                        tooltipIcon.setAttribute('data-tooltip', tooltipContent);
                        element.appendChild(tooltipIcon);

                        // Add click handler to show tooltip
                        tooltipIcon.addEventListener('click', function(e) {
                            e.preventDefault();
                            showTooltip(e.target, tooltipContent);
                        });
                    }
                });
            }

            function showTooltip(element, content) {
                // Remove existing tooltips
                const existingTooltips = document.querySelectorAll('.custom-tooltip');
                existingTooltips.forEach(tooltip => tooltip.remove());

                // Create new tooltip
                const tooltip = document.createElement('div');
                tooltip.className = 'custom-tooltip';
                tooltip.innerHTML = content;

                // Position tooltip
                document.body.appendChild(tooltip);
                const rect = element.getBoundingClientRect();
                tooltip.style.position = 'fixed';
                tooltip.style.top = (rect.bottom + 10) + 'px';
                tooltip.style.left = rect.left + 'px';
                tooltip.style.zIndex = '10000';

                // Auto-hide after 10 seconds
                setTimeout(() => {
                    if (tooltip.parentNode) {
                        tooltip.remove();
                    }
                }, 10000);

                // Hide on click outside
                document.addEventListener('click', function hideTooltip(e) {
                    if (!tooltip.contains(e.target) && e.target !== element) {
                        tooltip.remove();
                        document.removeEventListener('click', hideTooltip);
                    }
                });
            }
            </script>

            <style>
            /* Removed validation statistics CSS styles - not relevant for end users */

            .tooltip-icon {
                transition: all 0.2s ease;
            }

            .tooltip-icon:hover {
                transform: scale(1.2);
                filter: brightness(1.3);
            }

            .custom-tooltip {
                background: rgba(0, 0, 0, 0.9);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 15px;
                max-width: 400px;
                color: white;
                font-size: 0.9em;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                animation: tooltipFadeIn 0.2s ease;
            }

            @keyframes tooltipFadeIn {
                from { opacity: 0; transform: translateY(-10px); }
                to { opacity: 1; transform: translateY(0); }
            }

            .tooltip-section {
                margin-bottom: 10px;
            }

            .tooltip-section:last-child {
                margin-bottom: 0;
            }

            .tooltip-link {
                color: #4ade80;
                text-decoration: none;
                word-break: break-all;
            }

            .tooltip-link:hover {
                color: #6ee7b7;
                text-decoration: underline;
            }
            </style>
            </body>
</html>