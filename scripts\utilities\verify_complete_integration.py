#!/usr/bin/env python3
"""
Complete verification script for enhanced resource-control mapping integration
"""

import logging
import sys
import json
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_enhanced_mapper_standalone():
    """Test the enhanced mapper as a standalone component."""
    print("🧪 Testing Enhanced Mapper (Standalone)...")
    
    try:
        from enhanced_resource_control_mappings import EnhancedResourceControlMapper
        
        mapper = EnhancedResourceControlMapper()
        
        # Test basic functionality
        control_count = len(mapper.control_database)
        links_count = len(mapper.url_links_database)
        resource_count = len(mapper.resource_mappings)
        
        print(f"   ✅ Controls loaded: {control_count}")
        print(f"   ✅ Controls with URLs: {links_count}")
        print(f"   ✅ Resource categories: {resource_count}")
        
        # Test specific control lookup
        test_control = "IM-1"
        if test_control in mapper.control_database:
            control = mapper.control_database[test_control]
            links = mapper.get_control_links(test_control)
            print(f"   ✅ {test_control} test: {len(links.get('raw_links', []))} URLs")
        
        # Test resource mapping
        test_resource = "Microsoft.Storage/storageAccounts"
        controls = mapper.get_controls_for_resource_type(test_resource)
        print(f"   ✅ {test_resource}: {len(controls)} controls")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_security_opt_integration():
    """Test the integration with security_opt.py."""
    print("\n🔧 Testing Security_opt.py Integration...")
    
    try:
        # Check if security_opt.py has the enhanced mapper
        security_opt_path = Path("security_opt.py")
        if not security_opt_path.exists():
            print("   ❌ security_opt.py not found")
            return False
        
        with open(security_opt_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for required components
        checks = [
            ('Enhanced mapper import', 'from enhanced_resource_control_mappings import EnhancedResourceControlMapper'),
            ('Enhanced mapper initialization', 'self.enhanced_mapper = EnhancedResourceControlMapper()'),
            ('Enhanced mapper usage', 'if hasattr(self, \'enhanced_mapper\') and self.enhanced_mapper:')
        ]
        
        all_passed = True
        for check_name, check_pattern in checks:
            if check_pattern in content:
                print(f"   ✅ {check_name}")
            else:
                print(f"   ❌ {check_name}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_csv_data_quality():
    """Test the quality and completeness of CSV data."""
    print("\n📊 Testing CSV Data Quality...")
    
    csv_files = {
        "Identity Management": "SecurityBenchmarks/identity_management.csv",
        "Network Security": "SecurityBenchmarks/network_security_with_urls.csv", 
        "Data Protection": "SecurityBenchmarks/data_protection.csv"
    }
    
    total_controls = 0
    total_urls = 0
    
    for domain, csv_path in csv_files.items():
        try:
            csv_file_path = Path(csv_path)
            if not csv_file_path.exists():
                print(f"   ❌ {domain}: File not found")
                continue
            
            import csv
            with open(csv_file_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                controls = list(reader)
            
            # Count URLs in implementation context
            url_count = 0
            for control in controls:
                implementation = control.get('Implementation and additional context', '')
                import re
                urls = re.findall(r'https?://[^\s\n\r]+', implementation)
                url_count += len(urls)
            
            total_controls += len(controls)
            total_urls += url_count
            
            print(f"   ✅ {domain}: {len(controls)} controls, {url_count} URLs")
            
        except Exception as e:
            print(f"   ❌ {domain}: Error - {e}")
    
    print(f"   📊 Total: {total_controls} controls, {total_urls} URLs")
    return total_controls > 0 and total_urls > 0

def test_url_link_extraction():
    """Test URL link extraction and formatting."""
    print("\n🔗 Testing URL Link Extraction...")
    
    try:
        from enhanced_resource_control_mappings import EnhancedResourceControlMapper
        
        mapper = EnhancedResourceControlMapper()
        
        # Test specific controls
        test_controls = ["IM-1", "NS-1", "DP-1", "IM-6", "NS-5", "DP-4"]
        
        total_links = 0
        for control_id in test_controls:
            links = mapper.get_control_links(control_id)
            raw_links = links.get('raw_links', [])
            formatted_links = links.get('formatted_links', '')
            
            total_links += len(raw_links)
            
            if raw_links:
                print(f"   ✅ {control_id}: {len(raw_links)} URLs")
                # Show first URL as example
                print(f"      Example: {raw_links[0][:60]}...")
            else:
                print(f"   ⚠️ {control_id}: No URLs found")
        
        print(f"   📊 Total links tested: {total_links}")
        return total_links > 0
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_resource_coverage():
    """Test resource type coverage and control mapping."""
    print("\n🏗️ Testing Resource Coverage...")
    
    try:
        from enhanced_resource_control_mappings import EnhancedResourceControlMapper
        
        mapper = EnhancedResourceControlMapper()
        
        # Test various Azure resource types
        test_resources = [
            "Microsoft.Storage/storageAccounts",
            "Microsoft.KeyVault/vaults",
            "Microsoft.Network/networkSecurityGroups",
            "Microsoft.Sql/servers",
            "Microsoft.Compute/virtualMachines",
            "Microsoft.Web/sites",
            "Microsoft.ContainerService/managedClusters",
            "Microsoft.DocumentDB/databaseAccounts"
        ]
        
        total_mappings = 0
        for resource_type in test_resources:
            controls = mapper.get_controls_for_resource_type(resource_type)
            total_mappings += len(controls)
            
            # Count controls by domain
            domain_counts = {}
            for control in controls:
                domain = control.get('domain', 'Unknown')
                domain_counts[domain] = domain_counts.get(domain, 0) + 1
            
            domain_summary = ', '.join([f"{domain}: {count}" for domain, count in domain_counts.items()])
            print(f"   ✅ {resource_type}: {len(controls)} controls ({domain_summary})")
        
        print(f"   📊 Total control mappings: {total_mappings}")
        return total_mappings > 0
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def generate_integration_report():
    """Generate a comprehensive integration report."""
    print("\n📋 Generating Integration Report...")
    
    try:
        from enhanced_resource_control_mappings import EnhancedResourceControlMapper
        
        mapper = EnhancedResourceControlMapper()
        
        report = {
            "integration_status": "SUCCESS",
            "timestamp": "2025-06-19",
            "summary": {
                "total_controls": len(mapper.control_database),
                "controls_with_urls": len(mapper.url_links_database),
                "resource_categories": len(mapper.resource_mappings),
                "csv_files_processed": len(mapper.csv_files)
            },
            "domain_breakdown": {},
            "resource_coverage": {},
            "url_statistics": {
                "total_urls": 0,
                "controls_with_urls": 0
            }
        }
        
        # Domain breakdown
        for control_id, control in mapper.control_database.items():
            domain = control['domain']
            if domain not in report["domain_breakdown"]:
                report["domain_breakdown"][domain] = 0
            report["domain_breakdown"][domain] += 1
        
        # Resource coverage
        for category, mapping in mapper.resource_mappings.items():
            report["resource_coverage"][category] = {
                "applicable_controls": len(mapping['applicable_controls']),
                "arm_types": len(mapping.get('arm_types', [])),
                "primary_domains": mapping['primary_domains']
            }
        
        # URL statistics
        for control_id, links in mapper.url_links_database.items():
            urls = links.get('raw_links', [])
            if urls:
                report["url_statistics"]["controls_with_urls"] += 1
                report["url_statistics"]["total_urls"] += len(urls)
        
        # Save report
        with open("integration_report.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print("   ✅ Integration report saved to integration_report.json")
        return True
        
    except Exception as e:
        print(f"   ❌ Error generating report: {e}")
        return False

def main():
    """Main verification function."""
    print("🔍 Complete Integration Verification")
    print("=" * 60)
    
    tests = [
        ("Enhanced Mapper Standalone", test_enhanced_mapper_standalone),
        ("Security_opt.py Integration", test_security_opt_integration),
        ("CSV Data Quality", test_csv_data_quality),
        ("URL Link Extraction", test_url_link_extraction),
        ("Resource Coverage", test_resource_coverage),
        ("Integration Report", generate_integration_report)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("Enhanced resource-control mapping system is fully integrated and working.")
        print("\nSystem is ready for:")
        print("- Comprehensive security analysis with all 27 controls")
        print("- Rich URL links in CSV and HTML reports")
        print("- Interactive tooltips with documentation links")
        print("- Professional-quality reports for leadership")
    else:
        print(f"\n⚠️ {total - passed} tests failed. Please review and fix issues.")
        sys.exit(1)

if __name__ == "__main__":
    main()
