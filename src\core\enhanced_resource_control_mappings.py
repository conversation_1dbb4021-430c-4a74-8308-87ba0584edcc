#!/usr/bin/env python3
"""
Enhanced Resource-Control Mapping System for IaC Guardian
Integrates all control IDs from enhanced CSV files with comprehensive URL links
"""

import json
import csv
import logging
from pathlib import Path
from typing import Dict, List, Set, Optional
import re

logger = logging.getLogger(__name__)

class EnhancedResourceControlMapper:
    """
    Enhanced system to map Azure resources to all relevant control IDs from CSV files
    and extract URL links for comprehensive reporting.
    """
    
    def __init__(self):
        self.csv_files = {
            "Identity Management": "data/benchmarks/identity_management.csv",
            "Network Security": "data/benchmarks/network_security_with_urls.csv",
            "Data Protection": "data/benchmarks/data_protection.csv"
        }
        
        self.control_database = {}
        self.resource_mappings = {}
        self.url_links_database = {}
        
        # Load existing resource mappings
        self.azure_resource_mappings = self._load_azure_resource_mappings()
        
        # Load and process CSV files
        self._load_csv_controls()
        
        # Generate comprehensive resource-control mappings
        self._generate_comprehensive_mappings()
    
    def _load_azure_resource_mappings(self) -> Dict:
        """Load existing Azure resource mappings from JSON file."""
        try:
            mappings_path = Path("data/mappings/azure_resource_mappings.json")
            if mappings_path.exists():
                with open(mappings_path, 'r') as f:
                    return json.load(f)
            else:
                logger.warning("Azure resource mappings file not found")
                return {}
        except Exception as e:
            logger.error(f"Error loading Azure resource mappings: {e}")
            return {}
    
    def _load_csv_controls(self):
        """Load all controls from enhanced CSV files with URL extraction."""
        logger.info("Loading enhanced CSV control data...")
        
        for domain_name, csv_path in self.csv_files.items():
            try:
                csv_file_path = Path(csv_path)
                if not csv_file_path.exists():
                    logger.warning(f"CSV file not found: {csv_path}")
                    continue
                
                with open(csv_file_path, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    domain_controls = []
                    
                    for row in reader:
                        control_id = row.get('ASB ID', '').strip()
                        if not control_id:
                            continue
                        
                        # Extract control information
                        control_data = {
                            'id': control_id,
                            'domain': domain_name,
                            'name': row.get('Name', '').strip(),
                            'security_principle': row.get('Security Principle', '').strip(),
                            'azure_guidance': row.get('Azure Guidance', '').strip(),
                            'implementation_context': row.get('Implementation and additional context', '').strip(),
                            'stakeholders': row.get('Stakeholders', '').strip(),
                            'tags': row.get('Tags', '').strip()
                        }
                        
                        # Extract URLs from implementation context
                        urls = self._extract_urls_from_text(control_data['implementation_context'])
                        control_data['urls'] = urls
                        
                        # Store in control database
                        self.control_database[control_id] = control_data
                        domain_controls.append(control_data)
                        
                        # Store URL links for reporting
                        if urls:
                            self.url_links_database[control_id] = {
                                'raw_links': urls,
                                'formatted_links': self._format_links_for_display(urls, control_data['implementation_context']),
                                'azure_guidance': control_data['azure_guidance'],
                                'implementation_context': control_data['implementation_context']
                            }
                    
                    logger.info(f"Loaded {len(domain_controls)} controls from {domain_name}")
                    
            except Exception as e:
                logger.error(f"Error loading CSV file {csv_path}: {e}")
    
    def _extract_urls_from_text(self, text: str) -> List[str]:
        """Extract all URLs from text using comprehensive regex patterns."""
        if not text:
            return []
        
        # Enhanced URL pattern to catch various formats
        url_patterns = [
            r'https?://[^\s\n\r\t,;]+',  # Standard HTTP/HTTPS URLs
            r'www\.[^\s\n\r\t,;]+',      # www URLs without protocol
        ]
        
        urls = []
        for pattern in url_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                # Clean up URL (remove trailing punctuation)
                cleaned_url = re.sub(r'[.,;:!?)\]}>"\'\s]+$', '', match)
                if cleaned_url and cleaned_url not in urls:
                    # Add protocol if missing
                    if cleaned_url.startswith('www.'):
                        cleaned_url = 'https://' + cleaned_url
                    urls.append(cleaned_url)
        
        return urls
    
    def _format_links_for_display(self, urls: List[str], context: str) -> str:
        """Format URLs for display in reports with meaningful descriptions."""
        if not urls:
            return ""
        
        formatted_links = []
        for i, url in enumerate(urls, 1):
            description = self._extract_link_description(url, context)
            if description:
                formatted_links.append(f"[{description}]({url})")
            else:
                formatted_links.append(f"[Reference {i}]({url})")
        
        return " | ".join(formatted_links)
    
    def _extract_link_description(self, url: str, context: str) -> str:
        """Extract meaningful description for URL from context."""
        # Try to find text before the URL that describes it
        patterns = [
            rf'([^:\n•]+):\s*[^\n]*{re.escape(url)}',  # "Description: URL"
            rf'([^:\n•]+)\s*\n[^\n]*{re.escape(url)}',  # "Description \n URL"
            rf'•\s*([^:\n]+):\s*[^\n]*{re.escape(url)}',  # "• Description: URL"
        ]
        
        for pattern in patterns:
            match = re.search(pattern, context, re.IGNORECASE)
            if match:
                description = match.group(1).strip()
                # Clean up description
                description = re.sub(r'^[^\w]*', '', description)
                description = re.sub(r'[^\w\s]*$', '', description)
                if 3 < len(description) < 60:
                    return description
        
        # Fallback to URL-based description
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            
            if 'docs.microsoft.com' in parsed.netloc:
                # Extract meaningful part from Microsoft docs URL
                path_parts = parsed.path.strip('/').split('/')
                if len(path_parts) >= 2:
                    service = path_parts[0] if path_parts[0] != 'en-us' else path_parts[1] if len(path_parts) > 1 else 'azure'
                    return f"Microsoft {service.replace('-', ' ').title()} Documentation"
                return "Microsoft Documentation"
            elif 'azure.microsoft.com' in parsed.netloc:
                return "Azure Portal Documentation"
            elif 'github.com' in parsed.netloc:
                return "GitHub Repository"
            else:
                return parsed.netloc.replace('www.', '').replace('.com', '').title()
        except:
            return "Reference"

    def _generate_comprehensive_mappings(self):
        """Generate comprehensive resource-to-control mappings with ALL controls for every resource."""
        logger.info("Generating comprehensive resource-control mappings...")
        logger.info("🔒 APPLYING ALL 27 CONTROLS TO EVERY RESOURCE TYPE FOR MAXIMUM SECURITY COVERAGE")
        logger.info("Rationale: Any resource can be compromised through unexpected attack vectors")

        # Get ALL controls from all domains for comprehensive coverage
        all_controls = list(self.control_database.values())
        logger.info(f"📊 Applying {len(all_controls)} controls to every resource type")

        # Define comprehensive resource-to-domain mappings - ALL GET ALL CONTROLS
        resource_domain_mappings = {
            "Storage": {
                "primary_domains": ["Data Protection", "Network Security", "Identity Management"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["encryption", "access_control", "network_isolation", "data_classification"]
            },
            "KeyVault": {
                "primary_domains": ["Identity Management", "Data Protection", "Network Security"],  # Added NS
                "applicable_controls": all_controls,  # ALL CONTROLS - KeyVault can be compromised via network too
                "focus_areas": ["key_management", "secret_management", "access_policies", "certificate_management", "network_access"]
            },
            "SQL": {
                "primary_domains": ["Data Protection", "Identity Management", "Network Security"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["data_encryption", "authentication", "network_access", "audit_logging"]
            },
            "Network": {
                "primary_domains": ["Network Security", "Identity Management", "Data Protection"],  # Added DP
                "applicable_controls": all_controls,  # ALL CONTROLS - Network resources can expose data too
                "focus_areas": ["network_segmentation", "access_control", "monitoring", "firewall_rules", "data_in_transit"]
            },
            "Compute": {
                "primary_domains": ["Identity Management", "Network Security", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["vm_security", "access_management", "data_protection", "endpoint_security"]
            },
            "AppService": {
                "primary_domains": ["Identity Management", "Network Security", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["authentication", "network_isolation", "data_security", "application_security"]
            },
            "Container": {
                "primary_domains": ["Identity Management", "Network Security", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["container_security", "network_policies", "secrets_management", "image_security"]
            },
            "CosmosDB": {
                "primary_domains": ["Data Protection", "Network Security", "Identity Management"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["data_encryption", "network_access", "authentication", "backup_security"]
            },
            "FunctionApp": {
                "primary_domains": ["Identity Management", "Network Security", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["authentication", "network_isolation", "data_security", "application_security"]
            },
            "LogicApp": {
                "primary_domains": ["Identity Management", "Network Security", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["authentication", "network_isolation", "data_security", "workflow_security"]
            },
            "APIManagement": {
                "primary_domains": ["Identity Management", "Network Security", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["api_security", "access_control", "data_protection", "network_isolation"]
            },
            "ServiceBus": {
                "primary_domains": ["Identity Management", "Network Security", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["message_security", "access_control", "network_isolation", "data_encryption"]
            },
            "EventHub": {
                "primary_domains": ["Identity Management", "Network Security", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["event_security", "access_control", "network_isolation", "data_encryption"]
            },
            "RedisCache": {
                "primary_domains": ["Identity Management", "Network Security", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["cache_security", "access_control", "network_isolation", "data_encryption"]
            },
            "SearchService": {
                "primary_domains": ["Identity Management", "Network Security", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["search_security", "access_control", "network_isolation", "data_encryption"]
            },
            "Batch": {
                "primary_domains": ["Identity Management", "Network Security", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["batch_security", "access_control", "network_isolation", "data_encryption"]
            },
            "ServiceFabric": {
                "primary_domains": ["Identity Management", "Network Security", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["fabric_security", "access_control", "network_isolation", "data_encryption"]
            },
            "LogicApps": {
                "primary_domains": ["Identity Management", "Network Security", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["workflow_security", "access_control", "network_isolation", "data_encryption"]
            },
            "APIApps": {
                "primary_domains": ["Identity Management", "Network Security", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["api_security", "access_control", "network_isolation", "data_encryption"]
            },
            "EventGrid": {
                "primary_domains": ["Identity Management", "Network Security", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["event_security", "access_control", "network_isolation", "data_encryption"]
            },
            "NotificationHub": {
                "primary_domains": ["Identity Management", "Network Security", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["notification_security", "access_control", "network_isolation", "data_encryption"]
            },
            "TrafficManager": {
                "primary_domains": ["Network Security", "Identity Management", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["traffic_security", "access_control", "network_isolation", "data_encryption"]
            },
            "FrontDoor": {
                "primary_domains": ["Network Security", "Identity Management", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["frontdoor_security", "access_control", "network_isolation", "data_encryption"]
            },
            "ApplicationGateway": {
                "primary_domains": ["Network Security", "Identity Management", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["gateway_security", "access_control", "network_isolation", "data_encryption"]
            },
            "LoadBalancer": {
                "primary_domains": ["Network Security", "Identity Management", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["loadbalancer_security", "access_control", "network_isolation", "data_encryption"]
            },
            "VirtualNetwork": {
                "primary_domains": ["Network Security", "Identity Management", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["vnet_security", "access_control", "network_isolation", "data_encryption"]
            },
            "NetworkInterface": {
                "primary_domains": ["Network Security", "Identity Management", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["nic_security", "access_control", "network_isolation", "data_encryption"]
            },
            "NetworkSecurityGroup": {
                "primary_domains": ["Network Security", "Identity Management", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["nsg_security", "access_control", "network_isolation", "data_encryption"]
            },
            "ApplicationSecurityGroup": {
                "primary_domains": ["Network Security", "Identity Management", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["asg_security", "access_control", "network_isolation", "data_encryption"]
            },
            "Firewall": {
                "primary_domains": ["Network Security", "Identity Management", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["firewall_security", "access_control", "network_isolation", "data_encryption"]
            },
            "VPNGateway": {
                "primary_domains": ["Network Security", "Identity Management", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["vpn_security", "access_control", "network_isolation", "data_encryption"]
            },
            "ExpressRoute": {
                "primary_domains": ["Network Security", "Identity Management", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["expressroute_security", "access_control", "network_isolation", "data_encryption"]
            },
            "PrivateEndpoint": {
                "primary_domains": ["Network Security", "Identity Management", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["private_endpoint_security", "access_control", "network_isolation", "data_encryption"]
            },
            "PrivateLink": {
                "primary_domains": ["Network Security", "Identity Management", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["private_link_security", "access_control", "network_isolation", "data_encryption"]
            },
            "ServiceEndpoint": {
                "primary_domains": ["Network Security", "Identity Management", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["service_endpoint_security", "access_control", "network_isolation", "data_encryption"]
            },
            "AzureAD": {
                "primary_domains": ["Identity Management", "Network Security", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["identity_protection", "access_management", "network_security", "data_protection"]
            },
            "AzurePolicy": {
                "primary_domains": ["Identity Management", "Network Security", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["policy_management", "compliance", "security_baselines", "data_protection"]
            },
            "AzureMonitor": {
                "primary_domains": ["Identity Management", "Network Security", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["monitoring", "logging", "alerting", "incident_response"]
            },
            "AzureSecurityCenter": {
                "primary_domains": ["Identity Management", "Network Security", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["security_management", "threat_detection", "vulnerability_assessment", "incident_response"]
            },
            "AzureSentinel": {
                "primary_domains": ["Identity Management", "Network Security", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["security_information", "incident_management", "threat_hunting", "compliance"]
            },
            "IdentityProtection": {
                "primary_domains": ["Identity Management", "Network Security", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["identity_security", "risk_management", "access_control", "data_protection"]
            },
            "Identity": {
                "primary_domains": ["Identity Management", "Network Security", "Data Protection"],
                "applicable_controls": all_controls,  # ALL CONTROLS for comprehensive coverage
                "focus_areas": ["identity_management", "access_control", "network_security", "data_protection"]
            }
        }

        # Generate mappings for each resource category
        for resource_category, config in resource_domain_mappings.items():
            self.resource_mappings[resource_category] = {
                "primary_domains": config["primary_domains"],
                "applicable_controls": config["applicable_controls"],
                "control_ids": [ctrl['id'] for ctrl in config["applicable_controls"]],
                "focus_areas": config["focus_areas"],
                "arm_types": self.azure_resource_mappings.get(resource_category, {}).get("arm_types", []),
                "terraform_types": self.azure_resource_mappings.get(resource_category, {}).get("terraform_types", []),
                "bicep_types": self.azure_resource_mappings.get(resource_category, {}).get("bicep_types", []),
                "keywords": self.azure_resource_mappings.get(resource_category, {}).get("keywords", [])
            }

        logger.info(f"Generated comprehensive mappings for {len(self.resource_mappings)} resource categories")

    def _get_controls_for_domains(self, domains: List[str]) -> List[Dict]:
        """Get all controls for specified domains."""
        controls = []
        for control_id, control_data in self.control_database.items():
            if control_data['domain'] in domains:
                controls.append(control_data)

        # Sort by domain priority and control ID
        domain_priority = {"Identity Management": 1, "Network Security": 2, "Data Protection": 3}
        controls.sort(key=lambda x: (domain_priority.get(x['domain'], 99), x['id']))

        return controls

    def get_controls_for_resource_type(self, resource_type: str) -> List[Dict]:
        """Get ALL applicable controls for a specific Azure resource type - comprehensive security coverage."""
        # Determine resource category from ARM type
        resource_category = self._determine_resource_category(resource_type)

        if resource_category and resource_category in self.resource_mappings:
            controls = self.resource_mappings[resource_category]["applicable_controls"]
            logger.debug(f"🔒 Returning {len(controls)} controls for {resource_type} (category: {resource_category})")
            return controls

        # Fallback: return all controls if category not found - ensures comprehensive coverage
        logger.info(f"🔒 Resource category not found for {resource_type}, applying ALL {len(self.control_database)} controls for maximum security coverage")
        return list(self.control_database.values())

    def _determine_resource_category(self, resource_type: str) -> Optional[str]:
        """Determine resource category from ARM resource type."""
        resource_type_lower = resource_type.lower()

        # Check each category's ARM types
        for category, mapping in self.resource_mappings.items():
            arm_types = mapping.get("arm_types", [])
            for arm_type in arm_types:
                if arm_type.lower() == resource_type_lower:
                    return category

        # Fallback pattern matching
        if "storage" in resource_type_lower:
            return "Storage"
        elif "keyvault" in resource_type_lower or "vault" in resource_type_lower:
            return "KeyVault"
        elif "sql" in resource_type_lower or "database" in resource_type_lower:
            return "SQL"
        elif "network" in resource_type_lower or "vnet" in resource_type_lower:
            return "Network"
        elif "compute" in resource_type_lower or "virtualmachine" in resource_type_lower:
            return "Compute"
        elif "web" in resource_type_lower or "app" in resource_type_lower:
            return "AppService"
        elif "container" in resource_type_lower or "kubernetes" in resource_type_lower:
            return "Container"
        elif "cosmosdb" in resource_type_lower or "documentdb" in resource_type_lower:
            return "CosmosDB"

        return None

    def get_control_links(self, control_id: str) -> Dict:
        """Get URL links and context for a specific control ID."""
        return self.url_links_database.get(control_id, {
            'raw_links': [],
            'formatted_links': '',
            'azure_guidance': '',
            'implementation_context': ''
        })

    def export_enhanced_mappings(self, output_path: str = "data/mappings/enhanced_resource_mappings.json"):
        """Export the enhanced resource mappings to JSON file."""
        try:
            export_data = {
                "metadata": {
                    "version": "1.0",
                    "description": "Enhanced Azure Resource to Security Control Mappings",
                    "total_controls": len(self.control_database),
                    "total_resource_categories": len(self.resource_mappings),
                    "domains_covered": list(self.csv_files.keys())
                },
                "control_database": self.control_database,
                "resource_mappings": self.resource_mappings,
                "url_links_database": self.url_links_database
            }

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)

            logger.info(f"Enhanced resource mappings exported to {output_path}")
            return True

        except Exception as e:
            logger.error(f"Error exporting enhanced mappings: {e}")
            return False

    def generate_resource_summary_report(self) -> str:
        """Generate a summary report of resource-control mappings."""
        report = []
        report.append("# Enhanced Resource-Control Mapping Summary")
        report.append(f"Generated: {len(self.resource_mappings)} resource categories")
        report.append(f"Total Controls: {len(self.control_database)}")
        report.append(f"Controls with URLs: {len(self.url_links_database)}")
        report.append("")

        # Domain breakdown
        domain_counts = {}
        for control in self.control_database.values():
            domain = control['domain']
            domain_counts[domain] = domain_counts.get(domain, 0) + 1

        report.append("## Controls by Domain:")
        for domain, count in sorted(domain_counts.items()):
            report.append(f"- **{domain}**: {count} controls")
        report.append("")

        # Resource category breakdown
        report.append("## Resource Categories:")
        for category, mapping in self.resource_mappings.items():
            control_count = len(mapping['applicable_controls'])
            arm_types_count = len(mapping.get('arm_types', []))
            report.append(f"- **{category}**: {control_count} controls, {arm_types_count} ARM types")

        return "\n".join(report)


def main():
    """Main function to demonstrate the enhanced resource mapping system."""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    print("🚀 Enhanced Resource-Control Mapping System")
    print("=" * 60)

    try:
        # Initialize the mapper
        mapper = EnhancedResourceControlMapper()

        # Generate summary report
        print("\n📊 Summary Report:")
        print(mapper.generate_resource_summary_report())

        # Test specific resource type mapping
        print("\n🔍 Testing Resource Type Mappings:")
        test_resources = [
            "Microsoft.Storage/storageAccounts",
            "Microsoft.KeyVault/vaults",
            "Microsoft.Network/networkSecurityGroups",
            "Microsoft.Sql/servers"
        ]

        for resource_type in test_resources:
            controls = mapper.get_controls_for_resource_type(resource_type)
            print(f"\n{resource_type}:")
            print(f"  📋 Applicable Controls: {len(controls)}")

            # Show first 5 controls with URLs
            for i, control in enumerate(controls[:5]):
                control_id = control['id']
                links = mapper.get_control_links(control_id)
                url_count = len(links.get('raw_links', []))
                print(f"    {control_id} ({control['domain']}) - {url_count} URLs")

        # Export enhanced mappings
        print("\n💾 Exporting Enhanced Mappings...")
        if mapper.export_enhanced_mappings():
            print("✅ Enhanced mappings exported successfully!")
        else:
            print("❌ Failed to export enhanced mappings")

        # Test URL link extraction for specific controls
        print("\n🔗 Testing URL Link Extraction:")
        test_controls = ["IM-1", "NS-1", "DP-1"]

        for control_id in test_controls:
            links = mapper.get_control_links(control_id)
            raw_links = links.get('raw_links', [])
            formatted_links = links.get('formatted_links', '')

            print(f"\n{control_id}:")
            print(f"  📚 Raw Links: {len(raw_links)}")
            if raw_links:
                print(f"    First URL: {raw_links[0]}")
            print(f"  🔗 Formatted: {formatted_links[:100]}{'...' if len(formatted_links) > 100 else ''}")

        print("\n🎉 Enhanced Resource-Control Mapping System completed successfully!")

    except Exception as e:
        print(f"❌ Error: {e}")
        logging.exception("Detailed error information:")


if __name__ == "__main__":
    main()
