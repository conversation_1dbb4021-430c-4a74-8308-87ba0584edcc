Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,54,"App Service configuration allows public network access ('publicNetworkAccess': 'Enabled') and has 'ipSecurityRestrictions' with 'ipAddress': 'Any' and 'action': 'Allow', which exposes the application to the public internet without restriction.",Restrict 'ipSecurityRestrictions' to only allow trusted IP ranges and set 'publicNetworkAccess' to 'Disabled' or configure access restrictions to limit public exposure.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,54,"App Service SCM site configuration allows public network access ('scmIpSecurityRestrictions' with 'ipAddress': 'Any' and 'action': 'Allow'), exposing the SCM endpoint to the public internet.",Restrict 'scmIpSecurityRestrictions' to only allow trusted IP ranges or set 'scmIpSecurityRestrictionsUseMain' to true and ensure main restrictions are secure.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,54,"App Service does not use private endpoints; 'publicNetworkAccess' is 'Enabled', increasing exposure to the public internet.",Configure a private endpoint for the App Service and set 'publicNetworkAccess' to 'Disabled' to restrict access to private networks only.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,54,"App Service 'siteConfig' uses 'phpVersion': '5.6', which is deprecated and may not support modern encryption standards for data at rest.","Upgrade 'phpVersion' to a supported and secure version (e.g., 8.0 or later) to ensure strong encryption and security support.",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,54,"App Service hostNameSslStates for 'onefuzz-daily-ui.azurewebsites.net' and 'onefuzz-daily-ui.scm.azurewebsites.net' have 'sslState' set to 'Disabled', which allows unencrypted HTTP connections. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates to enforce HTTPS and ensure TLS 1.2+ is used for all endpoints.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,54,"App Service 'siteConfig' has 'http20Enabled': false, which disables HTTP/2. This may prevent the use of the latest secure protocols for data in transit.",Set 'http20Enabled' to true in 'siteConfig' to enable HTTP/2 and improve security for data in transit.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,54,"App Service 'publishingUsername' is specified in plain text in the configuration, which risks sensitive information disclosure.",Remove 'publishingUsername' from the template and use Azure Key Vault references or secure parameterization for sensitive credentials.,N/A,AI,Generic
