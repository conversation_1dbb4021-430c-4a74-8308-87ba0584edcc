#!/usr/bin/env python
"""
Test script to validate Azure Security Center policy integration.
"""

from security_opt import SecurityPRReviewer
import logging
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)],
)
logger = logging.getLogger(__name__)

def main():
    """Test the integration of Azure Security Center policies."""
    logger.info("Creating SecurityPRReviewer instance with local folder mode")
    
    # Initialize with local folder mode
    reviewer = SecurityPRReviewer(local_folder=".")
    
    # Load benchmark data
    benchmark = reviewer.prepare_benchmark()
    
    # Check if controls have policy information
    controls = benchmark.get("controls", [])
    controls_with_policies = [c for c in controls if c.get("azure_policies")]
    
    logger.info(f"Found {len(controls)} total controls")
    logger.info(f"Found {len(controls_with_policies)} controls with Azure Security Center policies")
    
    if controls_with_policies:
        # Show a sample of the first control with policies
        sample_control = controls_with_policies[0]
        logger.info(f"Sample control with policies: {sample_control['id']} - {sample_control['name']}")
        logger.info(f"Policy count: {len(sample_control.get('azure_policies', []))}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
