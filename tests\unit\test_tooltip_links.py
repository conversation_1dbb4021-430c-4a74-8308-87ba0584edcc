#!/usr/bin/env python3
"""
Test script for tooltip links functionality in CSV and HTML exports.
Validates link extraction, description generation, and tooltip formatting.
"""

import sys
import json
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from security_opt import SecurityPRReviewer
    print("✅ Successfully imported SecurityPRReviewer")
except ImportError as e:
    print(f"❌ Failed to import SecurityPRReviewer: {e}")
    sys.exit(1)

def test_link_extraction():
    """Test the link extraction functionality."""
    print("\n🔍 Testing Link Extraction Functionality")
    print("=" * 50)
    
    try:
        # Initialize the reviewer
        reviewer = SecurityPRReviewer(local_folder=".")
        
        # Test if the enhanced methods exist
        if hasattr(reviewer, '_extract_control_links'):
            print("✅ _extract_control_links method exists")
        else:
            print("❌ _extract_control_links method missing")
            return False
        
        if hasattr(reviewer, '_extract_link_description'):
            print("✅ _extract_link_description method exists")
        else:
            print("❌ _extract_link_description method missing")
            return False
        
        if hasattr(reviewer, '_enhance_remediation_with_tooltip'):
            print("✅ _enhance_remediation_with_tooltip method exists")
        else:
            print("❌ _enhance_remediation_with_tooltip method missing")
            return False
        
        # Test link extraction with a sample control ID
        print("\n📋 Testing link extraction for control NS-1...")
        links_info = reviewer._extract_control_links("NS-1")
        
        print(f"Raw links found: {len(links_info.get('raw_links', []))}")
        print(f"Formatted links: {links_info.get('formatted_links', 'None')}")
        print(f"Azure guidance available: {'Yes' if links_info.get('azure_guidance') else 'No'}")
        print(f"Implementation context available: {'Yes' if links_info.get('implementation_context') else 'No'}")
        
        # Test description extraction
        print("\n🏷️ Testing description extraction...")
        test_url = "https://docs.microsoft.com/azure/azure-monitor/platform/diagnostic-settings"
        test_context = "How to collect platform logs and metrics with Azure Monitor:\nhttps://docs.microsoft.com/azure/azure-monitor/platform/diagnostic-settings"
        
        description = reviewer._extract_link_description(test_url, test_context)
        print(f"URL: {test_url}")
        print(f"Generated description: {description}")
        
        # Test remediation enhancement
        print("\n💡 Testing remediation enhancement...")
        sample_remediation = "Configure network security groups to restrict access."
        enhanced_remediation = reviewer._enhance_remediation_with_tooltip(sample_remediation, links_info)
        
        print(f"Original: {sample_remediation}")
        print(f"Enhanced length: {len(enhanced_remediation)} characters")
        print(f"Has references: {'📚 References:' in enhanced_remediation}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during link extraction test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_csv_export_enhancement():
    """Test the enhanced CSV export functionality."""
    print("\n📊 Testing Enhanced CSV Export")
    print("=" * 50)
    
    try:
        # Create sample findings
        sample_findings = [
            {
                "file_path": "test.bicep",
                "control_id": "NS-1",
                "severity": "HIGH",
                "line": 10,
                "description": "Public network access enabled",
                "remediation": "Disable public network access and use private endpoints",
                "source": "ai_analysis"
            },
            {
                "file_path": "test.bicep",
                "control_id": "DP-1",
                "severity": "MEDIUM",
                "line": 25,
                "description": "Data classification not enabled",
                "remediation": "Enable data classification for sensitive data",
                "source": "ai_analysis"
            }
        ]
        
        # Initialize reviewer
        reviewer = SecurityPRReviewer(local_folder=".")
        
        # Test if enhanced CSV export method exists
        if hasattr(reviewer, '_export_findings_to_csv_enhanced'):
            print("✅ _export_findings_to_csv_enhanced method exists")
        else:
            print("❌ _export_findings_to_csv_enhanced method missing")
            return False
        
        # Create sample metadata
        sample_metadata = {
            "generation_timestamp": "2025-01-01T00:00:00",
            "total_findings": len(sample_findings),
            "control_id_validation": {
                "success_rate": 95.0,
                "corrections_made": 1,
                "fictional_ids_prevented": 2
            },
            "cross_reference_analysis": {
                "cross_ref_findings": 0
            }
        }
        
        # Test CSV export (dry run - don't actually write file)
        print("✅ Enhanced CSV export method can be called")
        print(f"Sample findings: {len(sample_findings)}")
        print(f"Sample metadata keys: {list(sample_metadata.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during CSV export test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_html_export_enhancement():
    """Test the enhanced HTML export functionality."""
    print("\n🌐 Testing Enhanced HTML Export")
    print("=" * 50)
    
    try:
        # Initialize reviewer
        reviewer = SecurityPRReviewer(local_folder=".")
        
        # Test if enhanced HTML export method exists
        if hasattr(reviewer, '_export_findings_to_html_enhanced'):
            print("✅ _export_findings_to_html_enhanced method exists")
        else:
            print("❌ _export_findings_to_html_enhanced method missing")
            return False
        
        print("✅ Enhanced HTML export method available")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during HTML export test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tooltip links tests."""
    print("🧪 TOOLTIP LINKS FUNCTIONALITY TEST")
    print("=" * 60)
    
    tests = [
        ("Link Extraction", test_link_extraction),
        ("CSV Export Enhancement", test_csv_export_enhancement),
        ("HTML Export Enhancement", test_html_export_enhancement)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running {test_name} Test...")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"❌ {test_name} test ERROR: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tooltip links functionality tests PASSED!")
        return True
    else:
        print("⚠️ Some tests failed. Check implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
