"""
Refactored pattern-based security detection with improved architecture.
Demonstrates better practices for the _detect_common_issues method.
"""

import re
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Pattern, Union
from enum import Enum
from pathlib import Path

logger = logging.getLogger(__name__)

class Severity(Enum):
    """Security issue severity levels."""
    CRITICAL = "CRITICAL"
    HIGH = "HIGH"
    MEDIUM = "MEDIUM"
    LOW = "LOW"

@dataclass
class SecurityPattern:
    """Represents a security pattern to detect."""
    pattern: Pattern[str]
    severity: Severity
    control_id: str
    description: str
    remediation: str
    resource_types: List[str] = field(default_factory=list)
    file_extensions: List[str] = field(default_factory=list)
    
    def applies_to(self, resource_type: str, file_extension: str) -> bool:
        """Check if pattern applies to given resource type and file."""
        # If no restrictions, pattern applies to all
        if not self.resource_types and not self.file_extensions:
            return True
        
        # Check resource type match
        resource_match = not self.resource_types or resource_type in self.resource_types
        
        # Check file extension match
        extension_match = not self.file_extensions or file_extension in self.file_extensions
        
        return resource_match and extension_match

@dataclass
class SecurityFinding:
    """Represents a security finding in code."""
    file_path: str
    line: int
    severity: Severity
    control_id: str
    description: str
    remediation: str
    matching_content: str
    pattern_name: Optional[str] = None
    confidence: float = 1.0

class SecurityPatternDetector(ABC):
    """Abstract base class for security pattern detection."""
    
    @abstractmethod
    def get_patterns(self) -> List[SecurityPattern]:
        """Get security patterns for this detector."""
        pass
    
    def detect(self, file_path: str, content: str, resource_type: str) -> List[SecurityFinding]:
        """Detect security issues in file content."""
        findings = []
        file_extension = Path(file_path).suffix.lower()
        
        # Normalize line endings for consistent line counting
        content = content.replace('\r\n', '\n')
        lines = content.split('\n')
        
        for pattern in self.get_patterns():
            # Check if pattern applies to this file
            if not pattern.applies_to(resource_type, file_extension):
                continue
            
            # Find all matches
            for match in pattern.pattern.finditer(content):
                line_number = content[:match.start()].count('\n') + 1
                
                # Get the actual line content
                if 0 <= line_number - 1 < len(lines):
                    matching_line = lines[line_number - 1].strip()
                    
                    finding = SecurityFinding(
                        file_path=file_path,
                        line=line_number,
                        severity=pattern.severity,
                        control_id=pattern.control_id,
                        description=pattern.description,
                        remediation=pattern.remediation,
                        matching_content=matching_line,
                        pattern_name=self.__class__.__name__
                    )
                    findings.append(finding)
                    
                    logger.debug(
                        f"Found {pattern.severity.value} issue in {file_path}:{line_number} - {pattern.control_id}"
                    )
        
        return findings

class StorageSecurityPatterns(SecurityPatternDetector):
    """Security patterns specific to Azure Storage resources."""
    
    def get_patterns(self) -> List[SecurityPattern]:
        return [
            # Terraform patterns
            SecurityPattern(
                pattern=re.compile(r"enable_https_traffic_only\s*=\s*false", re.IGNORECASE),
                severity=Severity.HIGH,
                control_id="DP-3",
                description="Storage account allows HTTP traffic, which is insecure. HTTPS should be enforced.",
                remediation="Set enable_https_traffic_only = true to enforce HTTPS.",
                resource_types=["Storage"],
                file_extensions=[".tf"]
            ),
            SecurityPattern(
                pattern=re.compile(r"min_tls_version\s*=\s*[\"']TLS1_0[\"']", re.IGNORECASE),
                severity=Severity.MEDIUM,
                control_id="DP-4",
                description="Storage account uses an outdated TLS version which has known vulnerabilities.",
                remediation="Set min_tls_version = \"TLS1_2\" or higher to enforce stronger encryption.",
                resource_types=["Storage"],
                file_extensions=[".tf"]
            ),
            SecurityPattern(
                pattern=re.compile(
                    r"network_rules\s*{[^}]*default_action\s*=\s*[\"']allow[\"']", 
                    re.IGNORECASE | re.DOTALL
                ),
                severity=Severity.HIGH,
                control_id="NS-1",
                description="Storage account allows access from all networks. This creates unnecessary security exposure.",
                remediation="Set default_action = \"Deny\" and explicitly allow only necessary networks.",
                resource_types=["Storage"],
                file_extensions=[".tf"]
            ),
            SecurityPattern(
                pattern=re.compile(r"public_network_access_enabled\s*=\s*true", re.IGNORECASE),
                severity=Severity.MEDIUM,
                control_id="NS-2",
                description="Storage account allows public network access, increasing attack surface.",
                remediation="Consider setting public_network_access_enabled = false and use private endpoints.",
                resource_types=["Storage"],
                file_extensions=[".tf"]
            ),
            
            # Bicep patterns
            SecurityPattern(
                pattern=re.compile(r"enableHttpsTrafficOnly:\s*false", re.IGNORECASE),
                severity=Severity.HIGH,
                control_id="DP-3",
                description="Storage account allows HTTP traffic, which is insecure. HTTPS should be enforced.",
                remediation="Set enableHttpsTrafficOnly: true to enforce HTTPS.",
                resource_types=["Storage"],
                file_extensions=[".bicep"]
            ),
            SecurityPattern(
                pattern=re.compile(r"minimumTlsVersion:\s*[\"']TLS1_0[\"']", re.IGNORECASE),
                severity=Severity.MEDIUM,
                control_id="DP-4",
                description="Storage account uses an outdated TLS version which has known vulnerabilities.",
                remediation="Set minimumTlsVersion: 'TLS1_2' or higher to enforce stronger encryption.",
                resource_types=["Storage"],
                file_extensions=[".bicep"]
            ),
            SecurityPattern(
                pattern=re.compile(
                    r"networkAcls:[^}]*defaultAction:\s*[\"']Allow[\"']", 
                    re.IGNORECASE | re.DOTALL
                ),
                severity=Severity.HIGH,
                control_id="NS-1",
                description="Storage account allows access from all networks. This creates unnecessary security exposure.",
                remediation="Set defaultAction: 'Deny' and explicitly allow only necessary networks.",
                resource_types=["Storage"],
                file_extensions=[".bicep"]
            ),
            
            # ARM/JSON patterns
            SecurityPattern(
                pattern=re.compile(r"\"supportsHttpsTrafficOnly\":\s*false", re.IGNORECASE),
                severity=Severity.HIGH,
                control_id="DP-3",
                description="Storage account allows HTTP traffic, which is insecure. HTTPS should be enforced.",
                remediation="Set \"supportsHttpsTrafficOnly\": true to enforce HTTPS.",
                resource_types=["Storage"],
                file_extensions=[".json", ".arm"]
            ),
        ]

class NetworkSecurityPatterns(SecurityPatternDetector):
    """Security patterns for network resources."""
    
    def get_patterns(self) -> List[SecurityPattern]:
        return [
            SecurityPattern(
                pattern=re.compile(r"source_address_prefix\s*=\s*[\"']\*[\"']", re.IGNORECASE),
                severity=Severity.HIGH,
                control_id="NS-1",
                description="Network Security Group rule allows traffic from any source (*), which is overly permissive.",
                remediation="Restrict source_address_prefix to specific IP ranges or service tags.",
                resource_types=["Network"],
                file_extensions=[".tf"]
            ),
            SecurityPattern(
                pattern=re.compile(r"destination_port_range\s*=\s*[\"']\*[\"']", re.IGNORECASE),
                severity=Severity.MEDIUM,
                control_id="NS-1",
                description="Network Security Group rule allows traffic to all ports (*), which increases attack surface.",
                remediation="Specify only the required destination ports.",
                resource_types=["Network"],
                file_extensions=[".tf"]
            ),
        ]

class KeyVaultSecurityPatterns(SecurityPatternDetector):
    """Security patterns for Azure Key Vault."""
    
    def get_patterns(self) -> List[SecurityPattern]:
        return [
            SecurityPattern(
                pattern=re.compile(r"soft_delete_enabled\s*=\s*false", re.IGNORECASE),
                severity=Severity.HIGH,
                control_id="DP-8",
                description="Key Vault does not have soft delete enabled, risking permanent data loss.",
                remediation="Set soft_delete_enabled = true to enable recovery of deleted keys/secrets.",
                resource_types=["KeyVault"],
                file_extensions=[".tf"]
            ),
            SecurityPattern(
                pattern=re.compile(r"purge_protection_enabled\s*=\s*false", re.IGNORECASE),
                severity=Severity.MEDIUM,
                control_id="DP-8",
                description="Key Vault does not have purge protection enabled, allowing permanent deletion during retention period.",
                remediation="Set purge_protection_enabled = true for critical vaults.",
                resource_types=["KeyVault"],
                file_extensions=[".tf"]
            ),
        ]

class GenericSecurityPatterns(SecurityPatternDetector):
    """Generic security patterns that apply to multiple resource types."""
    
    def get_patterns(self) -> List[SecurityPattern]:
        return [
            SecurityPattern(
                pattern=re.compile(r"password\s*=\s*[\"'][^\"']+[\"']", re.IGNORECASE),
                severity=Severity.CRITICAL,
                control_id="DP-5",
                description="Hardcoded password detected in configuration file.",
                remediation="Store passwords in Azure Key Vault and reference them securely.",
                resource_types=[],  # Applies to all
                file_extensions=[]  # Applies to all
            ),
            SecurityPattern(
                pattern=re.compile(r"(api_key|apikey)\s*=\s*[\"'][^\"']+[\"']", re.IGNORECASE),
                severity=Severity.HIGH,
                control_id="DP-5",
                description="API key hardcoded in configuration file.",
                remediation="Store API keys in Azure Key Vault and reference them securely.",
                resource_types=[],
                file_extensions=[]
            ),
        ]

class CompositeSecurityPatternDetector:
    """Combines multiple security pattern detectors."""
    
    def __init__(self, detectors: Optional[List[SecurityPatternDetector]] = None):
        if detectors is None:
            # Initialize with default detectors
            self.detectors = [
                StorageSecurityPatterns(),
                NetworkSecurityPatterns(),
                KeyVaultSecurityPatterns(),
                GenericSecurityPatterns(),
            ]
        else:
            self.detectors = detectors
        
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def detect_issues(self, file_path: str, content: str, resource_type: str) -> List[SecurityFinding]:
        """Detect security issues using all configured detectors."""
        all_findings = []
        
        for detector in self.detectors:
            try:
                findings = detector.detect(file_path, content, resource_type)
                all_findings.extend(findings)
                
                if findings:
                    self.logger.info(
                        f"{detector.__class__.__name__} found {len(findings)} issues in {file_path}"
                    )
            except Exception as e:
                self.logger.error(
                    f"Error in {detector.__class__.__name__} for {file_path}: {str(e)}"
                )
        
        # Sort findings by severity and line number
        severity_order = {
            Severity.CRITICAL: 0,
            Severity.HIGH: 1,
            Severity.MEDIUM: 2,
            Severity.LOW: 3
        }
        
        all_findings.sort(key=lambda f: (severity_order[f.severity], f.line))
        
        return all_findings

class PatternManager:
    """Manages security patterns with caching and configuration."""
    
    _instance = None
    _patterns_cache = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(PatternManager, cls).__new__(cls)
        return cls._instance
    
    def get_detector(self) -> CompositeSecurityPatternDetector:
        """Get the composite detector with all patterns."""
        if self._patterns_cache is None:
            self._patterns_cache = CompositeSecurityPatternDetector()
        return self._patterns_cache
    
    def reload_patterns(self, config_path: Optional[str] = None):
        """Reload patterns from configuration."""
        # This could load patterns from YAML/JSON configuration
        self._patterns_cache = None

# Example usage and testing
if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(level=logging.DEBUG)
    
    # Example Terraform file content
    terraform_content = """
    resource "azurerm_storage_account" "example" {
        name                     = "examplestorage"
        resource_group_name      = azurerm_resource_group.example.name
        location                 = azurerm_resource_group.example.location
        account_tier             = "Standard"
        account_replication_type = "LRS"
        
        enable_https_traffic_only = false
        min_tls_version          = "TLS1_0"
        
        network_rules {
            default_action = "Allow"
        }
    }
    """
    
    # Create pattern manager and detect issues
    manager = PatternManager()
    detector = manager.get_detector()
    
    findings = detector.detect_issues(
        file_path="example.tf",
        content=terraform_content,
        resource_type="Storage"
    )
    
    # Print findings
    for finding in findings:
        print(f"\n{finding.severity.value} - {finding.control_id}")
        print(f"  File: {finding.file_path}:{finding.line}")
        print(f"  Issue: {finding.description}")
        print(f"  Code: {finding.matching_content}")
        print(f"  Fix: {finding.remediation}")
