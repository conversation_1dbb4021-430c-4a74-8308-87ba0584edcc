#!/usr/bin/env python3
"""
Example usage of Enhanced Resource Control Mappings V2
Demonstrates key features and capabilities
"""

from src.core.enhanced_resource_contarol_mappings_v2 import EnhancedResourceControlMapperV2
import json

def main():
    print("🔍 Enhanced Resource Control Mappings V2 - Example Usage")
    print("=" * 60)
    
    # Initialize the mapper
    print("\n📊 Initializing Enhanced Mapper V2...")
    mapper = EnhancedResourceControlMapperV2()
    
    # Example 1: Basic resource analysis
    print("\n1️⃣ Example: Analyzing a Storage Account")
    resource_type = "Microsoft.Storage/storageAccounts"
    controls = mapper.get_controls_for_resource_type(resource_type)
    category = mapper._determine_resource_category(resource_type)
    
    if category:
        mapping = mapper.resource_mappings[category]
        print(f"\n  Resource: {resource_type}")
        print(f"  Category: {category}")
        print(f"  Risk Level: {mapping['risk_level']}")
        print(f"  Total Controls: {len(controls)}")
        print(f"  Common Vulnerabilities: {', '.join(mapping['common_vulnerabilities'][:3])}")
        print(f"  Focus Areas: {', '.join(mapping['focus_areas'][:3])}")
    
    # Example 2: High-priority controls
    print("\n2️⃣ Example: Finding High-Priority Controls")
    high_priority_controls = [c for c in controls if c.get('priority') == 'HIGH']
    print(f"\n  High Priority Controls: {len(high_priority_controls)}")
    
    for control in high_priority_controls[:3]:
        severity = mapper.get_control_severity(control['id'])
        print(f"\n  Control: {control['id']} - {control['name']}")
        print(f"    Domain: {control['domain']}")
        print(f"    Severity: {severity['severity']}")
        print(f"    Impact Score: {severity['impact_score']}")
        print(f"    Can Automate: {'Yes' if control.get('automation_possible') else 'No'}")
    
    # Example 3: Compliance framework coverage
    print("\n3️⃣ Example: Compliance Framework Analysis")
    compliance_summary = {}
    
    for control in controls:
        frameworks = mapper.get_compliance_frameworks(control['id'])
        for framework in frameworks:
            if framework not in compliance_summary:
                compliance_summary[framework] = 0
            compliance_summary[framework] += 1
    
    print(f"\n  Compliance Coverage for {resource_type}:")
    for framework, count in sorted(compliance_summary.items(), key=lambda x: x[1], reverse=True):
        percentage = (count / len(controls)) * 100
        print(f"    {framework}: {count} controls ({percentage:.1f}%)")
    
    # Example 4: Security posture for multiple resources
    print("\n4️⃣ Example: Multi-Resource Security Posture")
    critical_resources = [
        "Microsoft.KeyVault/vaults",
        "Microsoft.Sql/servers",
        "Microsoft.Storage/storageAccounts",
        "Microsoft.Network/networkSecurityGroups"
    ]
    
    posture_report = mapper.generate_security_posture_report(critical_resources)
    
    print(f"\n  Security Posture Summary:")
    print(f"    Resources Analyzed: {posture_report['total_resources']}")
    print(f"    Total Controls Applied: {posture_report['total_controls']}")
    
    print(f"\n  Risk Distribution:")
    for risk, count in posture_report['risk_summary'].items():
        print(f"    {risk}: {count} resources")
    
    if posture_report['priority_actions']:
        print(f"\n  🚨 Priority Actions Required:")
        for action in posture_report['priority_actions']:
            print(f"    - {action['resource_type']} ({action['risk_level']} risk)")
            print(f"      High Priority Controls: {action['high_priority_controls']}")
    
    # Example 5: Control relationships
    print("\n5️⃣ Example: Exploring Control Relationships")
    test_control = "IM-1"
    
    if test_control in mapper.control_database:
        control_info = mapper.control_database[test_control]
        related = mapper.get_related_controls(test_control)
        
        print(f"\n  Control: {test_control} - {control_info['name']}")
        print(f"  Related Controls: {len(related)}")
        
        # Group by relationship type
        relationship_types = {}
        for rel in related:
            rel_type = rel['relationship_type']
            if rel_type not in relationship_types:
                relationship_types[rel_type] = []
            relationship_types[rel_type].append(rel['control_id'])
        
        for rel_type, control_ids in relationship_types.items():
            print(f"\n  {rel_type.replace('_', ' ').title()}:")
            for control_id in control_ids[:3]:
                if control_id in mapper.control_database:
                    print(f"    - {control_id}: {mapper.control_database[control_id]['name']}")
    
    # Example 6: URL resources
    print("\n6️⃣ Example: Control Documentation Links")
    controls_with_docs = []
    
    for control in controls[:10]:  # Check first 10 controls
        links = mapper.get_control_links(control['id'])
        if links.get('raw_links'):
            controls_with_docs.append({
                'id': control['id'],
                'name': control['name'],
                'links': links
            })
    
    print(f"\n  Controls with documentation links: {len(controls_with_docs)}")
    for doc_control in controls_with_docs[:3]:
        print(f"\n  {doc_control['id']}: {doc_control['name']}")
        print(f"    URLs: {len(doc_control['links']['raw_links'])}")
        print(f"    Formatted: {doc_control['links']['formatted_links'][:100]}...")
    
    # Example 7: Automation opportunities
    print("\n7️⃣ Example: Automation Opportunities")
    automatable = [c for c in controls if c.get('automation_possible')]
    monitoring = [c for c in controls if c.get('monitoring_required')]
    
    print(f"\n  Automation Analysis:")
    print(f"    Automatable Controls: {len(automatable)} ({(len(automatable)/len(controls)*100):.1f}%)")
    print(f"    Monitoring Required: {len(monitoring)} ({(len(monitoring)/len(controls)*100):.1f}%)")
    
    # Show some automatable high-priority controls
    auto_high_priority = [c for c in automatable if c.get('priority') == 'HIGH']
    if auto_high_priority:
        print(f"\n  High-Priority Automatable Controls: {len(auto_high_priority)}")
        for control in auto_high_priority[:3]:
            print(f"    - {control['id']}: {control['name']}")
    
    # Example 8: Export subset of data
    print("\n8️⃣ Example: Exporting Analysis Results")
    export_data = {
        "analysis_summary": {
            "resource": resource_type,
            "category": category,
            "risk_level": mapping['risk_level'] if category else "UNKNOWN",
            "total_controls": len(controls),
            "high_priority_controls": len(high_priority_controls),
            "automatable_controls": len(automatable)
        },
        "compliance_coverage": compliance_summary,
        "security_posture": {
            "resources_analyzed": posture_report['total_resources'],
            "risk_summary": dict(posture_report['risk_summary']),
            "priority_actions": len(posture_report['priority_actions'])
        },
        "sample_controls": [
            {
                "id": c['id'],
                "name": c['name'],
                "priority": c.get('priority'),
                "severity": mapper.get_control_severity(c['id'])['severity']
            }
            for c in high_priority_controls[:5]
        ]
    }
    
    # Save example results
    with open("example_analysis_results.json", "w") as f:
        json.dump(export_data, f, indent=2)
    
    print(f"\n  ✅ Analysis results exported to example_analysis_results.json")
    
    print("\n🎉 Example usage completed successfully!")
    print("\nKey Takeaways:")
    print("- Enhanced mapper provides comprehensive security analysis")
    print("- All resources get ALL controls for maximum coverage")
    print("- Priority, severity, and compliance tracking help focus efforts")
    print("- Automation opportunities can streamline implementation")
    print("- Risk assessment guides resource protection strategies")

if __name__ == "__main__":
    main()
