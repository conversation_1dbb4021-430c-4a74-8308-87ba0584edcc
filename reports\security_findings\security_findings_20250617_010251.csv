Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,template.json,143,No network security groups (NSGs) or Azure Firewall are configured to protect the App Service. 'ipSecurityRestrictions' allows all traffic.,Implement NSGs or Azure Firewall to restrict and monitor network traffic to the App Service.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,38,"App Service 'publicNetworkAccess' is set to 'Enabled' and 'ipSecurityRestrictions' allows 'Any' IP address, exposing the application to the public internet without restriction.",Restrict 'ipSecurityRestrictions' to only allow trusted IP ranges and set 'publicNetworkAccess' to 'Disabled' if public access is not required.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,143,"App Service configuration 'ipSecurityRestrictions' allows 'Any' IP address with action 'Allow', which exposes the application to the public internet.",Remove the 'Allow all' rule or restrict 'ipAddress' to specific trusted IP ranges to minimize public exposure.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,153,"App Service configuration 'scmIpSecurityRestrictions' allows 'Any' IP address with action 'Allow', exposing the SCM (deployment) endpoint to the public internet.",Restrict 'scmIpSecurityRestrictions' to only allow trusted IP ranges for the SCM endpoint.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-3,template.json,143,No Network Security Groups (NSGs) are applied to control inbound and outbound traffic for the App Service.,Apply NSGs to the subnet hosting the App Service Environment or use App Service access restrictions to control traffic.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,38,"App Service 'publicNetworkAccess' is set to 'Enabled' and no private endpoints are configured, increasing exposure to the public internet.",Implement Azure Private Endpoints for the App Service and set 'publicNetworkAccess' to 'Disabled' to restrict access to private networks only.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,38,"No explicit configuration for encryption at rest is present for App Service. While Azure provides encryption at rest by default, customer-managed keys (CMK) are not configured.",Configure App Service to use customer-managed keys (CMK) for encryption at rest if required for compliance.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,38,"App Service hostNameSslStates for 'onefuzz-daily-ui.azurewebsites.net' and 'onefuzz-daily-ui.scm.azurewebsites.net' have 'sslState' set to 'Disabled', which means HTTP is allowed and encryption in transit is not enforced for these endpoints.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates to enforce HTTPS and ensure encryption in transit for all endpoints.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,38,"No explicit reference to Azure Key Vault for storing sensitive information such as secrets, connection strings, or certificates in the App Service configuration.",Integrate Azure Key Vault references in App Service application settings to securely store and access sensitive information.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-6,template.json,38,No customer-managed keys (CMK) are configured for App Service encryption at rest.,Configure App Service to use customer-managed keys (CMK) for enhanced control over encryption at rest.,N/A,AI,Generic
