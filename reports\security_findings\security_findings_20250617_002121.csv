Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P1-Identity-HIGH,Identity Management,HIGH,IM-1,template.json,41,App Service 'onefuzz-daily-ui' does not explicitly integrate with Azure Active Directory for identity management. This weakens secure identity and access management.,Integrate the App Service with Azure Active Directory for authentication and access control.,N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-2,template.json,41,App Service 'onefuzz-daily-ui' does not enforce Multi-Factor Authentication (MFA) for users or administrators. This weakens secure access.,Configure Azure Active Directory authentication for the App Service and enforce MFA for all users and administrators.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,120,"App Service 'onefuzz-daily-ui' allows public network access ('publicNetworkAccess': 'Enabled') and has 'ipSecurityRestrictions' with 'ipAddress': 'Any' and 'action': 'Allow', exposing the app to the public internet. This violates the requirement to secure all public endpoints.",Restrict 'ipSecurityRestrictions' to only allow trusted IP ranges. Set 'publicNetworkAccess' to 'Disabled' or configure access restrictions to limit exposure.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,130,"App Service 'onefuzz-daily-ui' allows public network access to the SCM endpoint ('scmIpSecurityRestrictions' with 'ipAddress': 'Any' and 'action': 'Allow'), exposing the SCM endpoint to the public internet. This violates the requirement to secure all public endpoints.",Restrict 'scmIpSecurityRestrictions' to only allow trusted IP ranges. Limit SCM endpoint access to internal or management networks.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,120,"App Service 'onefuzz-daily-ui' does not use private endpoints and has 'publicNetworkAccess' set to 'Enabled', increasing exposure to the public internet. This violates the recommendation to use private endpoints for secure access.",Configure a private endpoint for the App Service and set 'publicNetworkAccess' to 'Disabled' to restrict access to private networks only.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,120,App Service 'onefuzz-daily-ui' does not specify the use of customer-managed keys (CMK) or explicit encryption at rest settings. This may violate the requirement to enable encryption at rest for all data storage.,Configure encryption at rest using customer-managed keys (CMK) or ensure platform-managed encryption is enabled for all data storage associated with the App Service.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,56,"App Service 'onefuzz-daily-ui' has 'sslState' set to 'Disabled' for hostName 'onefuzz-daily-ui.azurewebsites.net', which allows unencrypted HTTP traffic. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates entries to enforce HTTPS. Ensure all endpoints require TLS 1.2 or higher.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,62,"App Service 'onefuzz-daily-ui' has 'sslState' set to 'Disabled' for hostName 'onefuzz-daily-ui.scm.azurewebsites.net', which allows unencrypted HTTP traffic. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates entries to enforce HTTPS. Ensure all endpoints require TLS 1.2 or higher.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,120,"App Service 'onefuzz-daily-ui' configuration includes a 'publishingUsername' property, which may indicate sensitive information is stored in the template. This violates the requirement to store sensitive data like credentials in Azure Key Vault.",Remove sensitive information such as 'publishingUsername' from the template and reference credentials securely from Azure Key Vault.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-6,template.json,120,App Service 'onefuzz-daily-ui' does not specify the use of customer-managed keys (CMK) for encryption. Using platform-managed keys only may not meet all compliance requirements.,Configure the App Service to use customer-managed keys (CMK) for encryption if required by your compliance standards.,N/A,AI,Generic
