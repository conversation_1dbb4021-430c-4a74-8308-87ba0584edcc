#!/usr/bin/env python3
"""
Simple test for graph functionality.
"""

import sys
import os
import json
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src" / "core"))

def test_graph_building():
    print("🔍 Testing graph building directly...")
    
    # Read the test file directly
    test_file_path = project_root / "test_graph" / "simple_test.json"
    
    with open(test_file_path, 'r') as f:
        content = f.read()
    
    print(f"📁 Read file: {test_file_path}")
    print(f"📄 Content length: {len(content)} characters")
    
    # Test JSON parsing
    try:
        template = json.loads(content)
        resources = template.get("resources", [])
        print(f"✅ JSON parsing successful: {len(resources)} resources found")
        
        for i, resource in enumerate(resources):
            resource_type = resource.get("type", "Unknown")
            resource_name = resource.get("name", "Unknown")
            depends_on = resource.get("dependsOn", [])
            print(f"  Resource {i+1}: {resource_type}")
            print(f"    Name: {resource_name}")
            print(f"    Dependencies: {len(depends_on)}")
            if depends_on:
                for dep in depends_on:
                    print(f"      - {dep}")
    
    except Exception as e:
        print(f"❌ JSON parsing failed: {e}")
        return
    
    # Now test with the actual graph builder
    try:
        os.chdir(project_root / "src" / "core")
        from security_opt import SecurityPRReviewer
        
        # Create a minimal file info structure
        file_info = {
            "path": "simple_test.json",
            "content": content,
            "original_content": content,
            "is_arm_file": True
        }
        
        reviewer = SecurityPRReviewer(local_folder=str(project_root / "test_graph"))
        
        print("\n🔗 Testing graph builder...")
        graph = reviewer.build_resource_dependency_graph([file_info])
        
        print(f"📊 Graph result: {len(graph['nodes'])} nodes, {len(graph['edges'])} edges")
        
        if len(graph['nodes']) > 0:
            print("✅ Graph building successful!")
            for i, node in enumerate(graph['nodes']):
                print(f"  Node {i}: {node['type']} - {node['name']}")
                print(f"    Security relevant: {node.get('security_relevant', False)}")
            
            for i, edge in enumerate(graph['edges']):
                print(f"  Edge {i}: {edge['source']} -> {edge['target']} ({edge['type']})")

            # Test Mermaid diagram generation
            print("\n📊 Testing Mermaid diagram generation...")
            mermaid = reviewer.generate_mermaid_diagram(graph)
            print(f"📄 Generated Mermaid diagram ({len(mermaid)} characters)")
            print("Mermaid diagram preview:")
            print(mermaid[:500] + "..." if len(mermaid) > 500 else mermaid)

            # Test graph-enhanced analysis
            print("\n🔗 Testing graph-enhanced analysis...")
            findings = reviewer.analyze_files_with_graph([file_info])
            print(f"📊 Found {len(findings)} findings with graph context")

            for i, finding in enumerate(findings):
                cascade_risk = finding.get('cascade_risk', 'UNKNOWN')
                connected_count = len(finding.get('connected_resources', []))
                severity = finding.get('severity', 'UNKNOWN')
                control_id = finding.get('control_id', 'Unknown')
                print(f"  Finding {i+1}: {severity} - {control_id} - Cascade Risk: {cascade_risk} - Connected: {connected_count}")

                # Show cascade details for high-risk findings
                if cascade_risk in ['HIGH', 'CRITICAL']:
                    cascade_details = finding.get('cascade_details', {})
                    print(f"    Impact: {cascade_details.get('impact_description', 'Unknown')}")
                    recommendations = cascade_details.get('recommendations', [])
                    if recommendations:
                        print(f"    Recommendations: {len(recommendations)} cascade mitigation steps")
        else:
            print("❌ Graph building failed - no nodes created")

    except Exception as e:
        print(f"❌ Graph building error: {e}")
        import traceback
        traceback.print_exc()

    print("\n✅ All graph functionality tests completed successfully!")

if __name__ == "__main__":
    test_graph_building()
