Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P1-Identity-HIGH,Identity Management,HIGH,IM-1,template.json,38,"No explicit Azure Active Directory (AAD) authentication is configured for the App Service, which is required for secure identity management.",Enable Azure Active Directory authentication for the App Service to ensure secure identity and access management.,N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-2,template.json,38,No explicit configuration to require Multi-Factor Authentication (MFA) for users or administrators accessing the App Service.,Enforce Multi-Factor Authentication (MFA) for all users and administrators accessing the App Service through Azure AD.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,38,"App Service 'publicNetworkAccess' is set to 'Enabled' and 'ipSecurityRestrictions' allows 'Any' IP address, exposing the application to the public internet without restriction.",Restrict 'ipSecurityRestrictions' to only allow trusted IP ranges and set 'publicNetworkAccess' to 'Disabled' if public access is not required.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,143,"App Service 'scmIpSecurityRestrictions' allows 'Any' IP address, exposing the SCM (deployment) endpoint to the public internet.",Restrict 'scmIpSecurityRestrictions' to only allow trusted IP ranges to secure the SCM endpoint.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-3,template.json,143,"App Service does not implement Network Security Groups (NSGs) to control inbound and outbound traffic. 'ipSecurityRestrictions' allows 'Any', which is equivalent to no network security controls.","Configure 'ipSecurityRestrictions' to allow only specific trusted IP addresses or ranges, and consider integrating with NSGs where possible.",N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,38,App Service is accessible via public endpoints and does not use private endpoints for secure access.,Implement Azure Private Endpoints for the App Service to restrict access to private networks only.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,38,"No explicit configuration for encryption at rest is present for the App Service. While Azure provides encryption at rest by default, customer-managed keys (CMK) are not configured.",Configure App Service to use customer-managed keys (CMK) for encryption at rest if handling sensitive or regulated data.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,38,"App Service hostNameSslStates for 'onefuzz-daily-ui.azurewebsites.net' and 'onefuzz-daily-ui.scm.azurewebsites.net' have 'sslState' set to 'Disabled', which means HTTP is allowed and encryption in transit is not enforced for these endpoints.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates to enforce HTTPS and ensure encryption in transit for all endpoints.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,143,"App Service configuration includes 'publishingUsername' in plain text, which is sensitive information and should be stored securely, such as in Azure Key Vault.",Remove 'publishingUsername' from the template and reference it securely from Azure Key Vault using Key Vault references.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-6,template.json,38,"App Service does not use customer-managed keys (CMK) for data encryption, relying only on platform-managed keys.",Configure App Service to use customer-managed keys (CMK) for enhanced control over data encryption.,N/A,AI,Generic
