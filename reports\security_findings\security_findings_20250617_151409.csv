File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
app-config.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyValues' flows across template boundary,"Secure parameter 'keyValues' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'app_insights_key' flows across template boundary,"Secure parameter 'app_insights_key' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyvault_name' flows across template boundary,"Secure parameter 'keyvault_name' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
keyvault.bicep,NS-1,Network Security,Protect resources using network security groups,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,"Review template dependencies and ensure secure communication between templates

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'secrets' flows across template boundary,"Secure parameter 'secrets' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
storage-accounts.bicep,NS-1,Network Security,Protect resources using network security groups,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,"Review template dependencies and ensure secure communication between templates

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,DP-6,Data Protection,Secure Data with Customer-Managed Keys (CMK),HIGH,1.0,Parameter flow security risk: Sensitive parameters may be exposed through template dependencies,"Use Key Vault references for sensitive parameters and validate parameter flow security

📚 References: [Customer Managed Keys Overview](https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-overview) | [Customer Managed Keys Configure Key Vault](https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-configure-key-vault) | [Transparent Data Encryption Byok Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-byok-overview)

🔵 Azure Guidance: Use customer-managed keys for critical data.",[Customer Managed Keys Overview](https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-overview) | [Customer Managed Keys Configure Key Vault](https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-configure-key-vault) | [Transparent Data Encryption Byok Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-byok-overview),Use customer-managed keys for critical data.,"Configure Key Vault for key management. Rotate keys regularly.
Customer-managed keys: https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-overview
Key Vault CMK setup: https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-configure-key-vault
SQL CMK configuration: https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-byok-overview

This control is implemented through 3 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,parameter_flow,Validated
app-config.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,7.0,"The App Configuration resource 'Microsoft.AppConfiguration/configurationStores' at line 7 does not restrict public network access or define access controls for its public endpoint. By default, App Configuration endpoints are publicly accessible unless network rules or private endpoints are explicitly configured.","Restrict public network access by configuring the 'publicNetworkAccess' property to 'Disabled' and/or define network ACLs to allow only required IPs. Alternatively, use Private Endpoints to limit access to trusted networks. See: https://docs.microsoft.com/en-us/azure/azure-app-configuration/concept-private-endpoint

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
app-config.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,7.0,"The App Configuration resource 'Microsoft.AppConfiguration/configurationStores' at line 7 does not use a Private Endpoint. Without a Private Endpoint, the service is accessible over the public internet, increasing the attack surface.","Configure a Private Endpoint for the App Configuration resource to ensure access is only available from within your private network. Refer to: https://docs.microsoft.com/en-us/azure/azure-app-configuration/concept-private-endpoint

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
event-grid.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,27.0,"The Event Grid system topic resource (line 27) does not restrict public endpoints or specify access controls, potentially exposing the resource to the public internet. ASB NS-2 requires strict access control for public endpoints.","Restrict public access to the Event Grid system topic by configuring access controls, such as using Private Endpoints or IP restrictions. Refer to: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
event-grid.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,27.0,"The Event Grid system topic resource (line 27) does not use Private Endpoints, increasing the attack surface. ASB NS-5 recommends configuring Private Endpoints for PaaS services.","Configure Private Endpoints for the Event Grid system topic to ensure private access and reduce exposure. Refer to: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
function-settings.bicep,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,38.0,The parameter 'ado_access_client_id' is assigned a hardcoded client ID value directly in the template. Storing sensitive information such as client IDs or secrets in code violates DP-3: Manage sensitive information disclosure.,"Remove the hardcoded value from the 'ado_access_client_id' parameter and retrieve it securely from Azure Key Vault using a Key Vault reference. Update the template to reference the secret instead of embedding it directly.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",[Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references),Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.
Key Vault overview: https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Secret management: https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets
App Service Key Vault integration: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
function.bicep,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,38.0,"The parameter 'clientSecretSettingName' in the 'funcAuthSettings' resource is set to a static string 'OVERRIDE_USE_MI_FIC_ASSERTION_CLIENTID' (line 38), which may indicate a secret or credential is being referenced directly in code rather than using a secure Key Vault reference.","Replace the static client secret setting with a Key Vault reference or ensure that the application uses managed identities for authentication, following the guidance in ASB DP-3. Do not store secrets in code or parameters.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",[Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references),Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.
Key Vault overview: https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Secret management: https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets
App Service Key Vault integration: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
function.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,31.0,"The App Service resource 'function' (line 31) does not restrict public access or IP ranges, potentially exposing the function app to the public internet, which violates ASB NS-2 requirements for protecting public endpoints.","Restrict public access to the App Service by configuring access restrictions, using service endpoints, or integrating with Azure Front Door or Application Gateway. Limit allowed IP addresses to only those required.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
function.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,19.0,"The storage account 'funcStorage' (line 19) is referenced as an existing resource, but there is no evidence in the template that a Private Endpoint is configured for this storage account, which is required by ASB NS-5 for reducing attack surface.","Configure a Private Endpoint for the storage account to ensure access is only available from within the virtual network. Update the template to include a 'Microsoft.Network/privateEndpoints' resource for the storage account.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
hub-network.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,7.0,The subnet 'hub-subnet' defined at line 7 does not have a Network Security Group (NSG) associated. NSGs are required to segment and protect network resources.,"Associate a Network Security Group (NSG) with the 'hub-subnet' by adding the 'networkSecurityGroup' property referencing an NSG resource. Define and attach an NSG to restrict traffic as per security best practices.

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
hub-network.bicep,NS-3,Network Security,Use Network Security Groups (NSGs),CRITICAL,7.0,No Network Security Group (NSG) is configured for the 'hub-subnet' at line 7. NSGs are required to enforce network-level access controls and deny all inbound traffic by default.,"Create an NSG resource with appropriate security rules and associate it with the 'hub-subnet' to restrict inbound and outbound traffic to only what is necessary.

📚 References: [Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules)

🔵 Azure Guidance: Implement NSGs with deny-by-default rules.",[Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules),Implement NSGs with deny-by-default rules.,"Configure NSGs to deny all inbound traffic by default. Allow only necessary traffic.
NSG rules documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works
Security rules best practices: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
hub-network.bicep,NS-4,Network Security,Use Azure Firewall or third-party firewall,HIGH,1.0,No Azure Firewall or third-party firewall is defined in the template. Advanced network protection is required for critical network segments.,"Deploy an Azure Firewall or a supported third-party firewall in the hub network and configure firewall policies to monitor and control network traffic.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/firewall/overview) | [docs.microsoft.com/en-us/azure/firewall/overview](https://docs.microsoft.com/en-us/azure/firewall/policy-overview) | [Threat Intel](https://docs.microsoft.com/en-us/azure/firewall/threat-intel)

🔵 Azure Guidance: Deploy Azure Firewall or partner solution for enhanced security.",[Overview](https://docs.microsoft.com/en-us/azure/firewall/overview) | [docs.microsoft.com/en-us/azure/firewall/overview](https://docs.microsoft.com/en-us/azure/firewall/policy-overview) | [Threat Intel](https://docs.microsoft.com/en-us/azure/firewall/threat-intel),Deploy Azure Firewall or partner solution for enhanced security.,"Configure Azure Firewall policies. Enable threat intelligence. Monitor traffic.
Azure Firewall documentation: https://docs.microsoft.com/en-us/azure/firewall/overview
Firewall policies guide: https://docs.microsoft.com/en-us/azure/firewall/policy-overview
Threat intelligence setup: https://docs.microsoft.com/en-us/azure/firewall/threat-intel",ai_analysis,,Validated
hub-network.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,18.0,"Service endpoints for 'Microsoft.Storage.Global' and 'Microsoft.Keyvault' are enabled on 'hub-subnet' (line 18), but Private Endpoints are not configured. Private Endpoints are recommended for secure access to PaaS services.","Replace or supplement service endpoints with Private Endpoints for Azure Storage and Key Vault to ensure traffic remains on the Microsoft backbone and is not exposed to the public internet.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
instance-config.bicep,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,32.0,"The configuration at line 32 hardcodes 'monitoringGCSAuthId' with a value resembling a Key Vault or sensitive identifier ('airseccosinetest.geneva.keyvault.airaspcerts.cloudapp.net'), which may expose sensitive information in code. This violates DP-3: Manage sensitive information disclosure.","Remove hardcoded secrets or sensitive identifiers from the template. Reference secrets using Azure Key Vault and Key Vault references instead. Ensure no sensitive values are stored directly in code as per DP-3.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",[Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references),Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.
Key Vault overview: https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Secret management: https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets
App Service Key Vault integration: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,3.0,"The template defines broad IP ranges (e.g., '*******/8', '********/8', etc.) in 'corpNetIps' at line 3, which may allow excessive public access to resources. This violates NS-2, which requires strict access control for public endpoints.","Restrict allowed IP ranges to only those necessary for business operations. Replace broad ranges with specific, trusted IP addresses or smaller CIDR blocks. Consider using Private Link or Application Gateway to further restrict public access.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
ip-rules.bicep,NS-3,Network Security,Use Network Security Groups (NSGs),CRITICAL,3.0,The template at line 3 creates allow rules for large IP ranges without evidence of default deny rules or NSG enforcement. NS-3 requires NSGs to deny all inbound traffic by default and only allow necessary traffic.,"Implement Network Security Groups (NSGs) with a default deny inbound rule. Only allow specific, required traffic from trusted IPs. Review and update the template to ensure NSGs are configured accordingly.

📚 References: [Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules)

🔵 Azure Guidance: Implement NSGs with deny-by-default rules.",[Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules),Implement NSGs with deny-by-default rules.,"Configure NSGs to deny all inbound traffic by default. Allow only necessary traffic.
NSG rules documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works
Security rules best practices: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
keyvault.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,19.0,"Key Vault 'networkAcls.defaultAction' is set to 'Allow', which exposes the Key Vault to the public internet. This violates the requirement to restrict public endpoints.","Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only required IPs or virtual networks. Example: change 'defaultAction' to 'Deny' in the Key Vault resource configuration.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
scaleset-networks.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,38.0,The subnet 'scaleset' defined at line 38 does not have a Network Security Group (NSG) associated. NSGs are required to segment and protect network resources.,"Associate a Network Security Group (NSG) with the 'scaleset' subnet to control inbound and outbound traffic. Define an NSG resource and reference its id in the subnet's 'networkSecurityGroup' property.

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
scaleset-networks.bicep,NS-3,Network Security,Use Network Security Groups (NSGs),CRITICAL,38.0,"The subnet 'scaleset' at line 38 lacks an NSG, which is required to enforce network-level access controls and deny all inbound traffic by default.","Create and associate a Network Security Group (NSG) with the 'scaleset' subnet. Ensure the NSG denies all inbound traffic by default and only allows necessary traffic.

📚 References: [Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules)

🔵 Azure Guidance: Implement NSGs with deny-by-default rules.",[Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules),Implement NSGs with deny-by-default rules.,"Configure NSGs to deny all inbound traffic by default. Allow only necessary traffic.
NSG rules documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works
Security rules best practices: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
server-farms.bicep,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,109.0,"The 'settingValue' property for 'CERTIFICATE_PASSWORD_GENEVACERT' is set to an empty string, which may indicate a placeholder for a sensitive value directly in the template. Storing secrets or passwords in code or templates violates ASB DP-3.","Remove any hardcoded or placeholder secrets from the template. Use Azure Key Vault references for secret values and ensure the App Service retrieves secrets securely at runtime. Reference: ASB DP-3.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",[Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references),Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.
Key Vault overview: https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Secret management: https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets
App Service Key Vault integration: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
signalR.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,4.0,"The Microsoft.SignalRService/signalR resource does not restrict public network access or define allowed IPs, exposing a public endpoint by default. This violates NS-2, which requires strict access control for public endpoints.","Restrict public network access by configuring the 'publicNetworkAccess' property to 'Disabled' or by specifying allowed IP ranges using the 'networkACLs' property. Alternatively, use Private Link to eliminate public exposure.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
signalR.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,4.0,"The Microsoft.SignalRService/signalR resource does not configure a Private Endpoint, leaving the service accessible over the public internet. This violates NS-5, which recommends using Private Endpoints to reduce the attack surface.","Configure a Private Endpoint for the SignalR resource by creating a 'Microsoft.Network/privateEndpoints' resource and associating it with the SignalR instance. This will ensure traffic flows privately within your virtual network.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,20.0,"The 'networkAcls' configuration for 'storageAccountFunc' sets 'defaultAction' to 'Allow', which permits public network access to the storage account. This exposes the storage account to the public internet, violating ASB NS-2 (Protect public endpoints).","Set 'networkAcls.defaultAction' to 'Deny' for 'storageAccountFunc' to restrict public access. Only allow required IPs or virtual networks via 'ipRules' and 'virtualNetworkRules'.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,54.0,"The 'networkAcls' configuration in 'fuzzStorageProperties' (used by 'storageAccountFuzz' and 'storageAccountsCorpus') sets 'defaultAction' to 'Allow', which permits public network access to the storage accounts. This exposes the storage accounts to the public internet, violating ASB NS-2 (Protect public endpoints).","Set 'networkAcls.defaultAction' to 'Deny' in 'fuzzStorageProperties' to restrict public access. Only allow required IPs or virtual networks via 'ipRules' and 'virtualNetworkRules'.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 30,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 7,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-17T15:14:09.109108,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
