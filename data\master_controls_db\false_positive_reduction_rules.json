{"metadata": {"version": "1.0", "source": "IaC Guardian AI-Enhanced False Positive Reduction System", "created_date": "2025-06-20T00:00:00.000000", "ml_model_version": "v2.1", "training_data_size": 10000, "accuracy_rate": 94.5}, "semantic_analysis_rules": {"variable_naming_patterns": {"legitimate_security_keywords": [{"pattern": ".*secret.*", "context_indicators": ["boolean_value", "display_name", "configuration_flag", "ui_setting"], "false_positive_probability": 0.7, "validation_rules": ["check_variable_type", "analyze_usage_context", "verify_actual_value"]}, {"pattern": ".*password.*", "context_indicators": ["policy_name", "complexity_setting", "display_label", "validation_rule"], "false_positive_probability": 0.6, "validation_rules": ["distinguish_policy_vs_credential", "check_reference_pattern", "analyze_data_type"]}, {"pattern": ".*key.*", "context_indicators": ["encryption_key_name", "api_key_reference", "primary_key_flag", "key_vault_reference"], "false_positive_probability": 0.5, "validation_rules": ["verify_key_vault_integration", "check_hardcoded_values", "analyze_reference_pattern"]}]}, "configuration_vs_secret_patterns": {"configuration_indicators": ["boolean_values", "enum_selections", "display_names", "ui_labels", "feature_flags", "environment_names", "resource_names", "policy_names"], "secret_indicators": ["base64_encoded_strings", "long_random_strings", "credential_patterns", "token_formats", "certificate_content", "private_key_content"]}}, "contextual_analysis_rules": {"environment_context": {"development_environment": {"relaxed_security_acceptable": ["public_access_for_testing", "simplified_authentication", "debug_logging_enabled", "development_certificates"], "confidence_adjustment": -0.2, "deployment_worthiness_threshold": 60}, "production_environment": {"strict_security_required": ["private_endpoints_mandatory", "strong_authentication_required", "encryption_at_rest_required", "comprehensive_logging_required"], "confidence_adjustment": 0.1, "deployment_worthiness_threshold": 85}}, "resource_type_context": {"Microsoft.Storage/storageAccounts": {"common_false_positives": ["account_name_containing_secret", "display_name_with_key", "tier_configuration_flags"], "high_risk_configurations": ["public_blob_access", "https_traffic_disabled", "encryption_disabled"]}, "Microsoft.Web/sites": {"common_false_positives": ["app_setting_display_names", "connection_string_names", "feature_flag_names"], "high_risk_configurations": ["https_only_disabled", "client_cert_disabled", "public_network_access"]}}}, "machine_learning_patterns": {"historical_correlation": {"confirmed_vulnerabilities": [{"pattern": "public_blob_access_enabled", "confidence_boost": 0.3, "severity_impact": "HIGH", "frequency": 0.85}, {"pattern": "https_traffic_disabled", "confidence_boost": 0.25, "severity_impact": "MEDIUM", "frequency": 0.72}, {"pattern": "weak_tls_version", "confidence_boost": 0.2, "severity_impact": "MEDIUM", "frequency": 0.68}], "confirmed_false_positives": [{"pattern": "secret_display_name_variable", "confidence_reduction": 0.4, "frequency": 0.91}, {"pattern": "password_policy_configuration", "confidence_reduction": 0.35, "frequency": 0.88}, {"pattern": "key_name_reference_variable", "confidence_reduction": 0.3, "frequency": 0.82}]}, "pattern_recognition": {"legitimate_configuration_patterns": ["reference_to_key_vault", "parameter_with_secure_string", "managed_identity_usage", "azure_policy_compliance"], "suspicious_security_patterns": ["hardcoded_credentials", "public_access_enabled", "encryption_disabled", "logging_disabled"]}}, "confidence_scoring_algorithm": {"base_confidence": 50, "scoring_factors": [{"factor": "explicit_security_violation", "weight": 0.4, "max_boost": 40}, {"factor": "historical_pattern_match", "weight": 0.25, "max_boost": 25}, {"factor": "contextual_validation", "weight": 0.2, "max_boost": 20}, {"factor": "semantic_analysis_result", "weight": 0.15, "max_boost": 15}], "confidence_thresholds": {"high_confidence": 85, "medium_confidence": 70, "low_confidence": 55, "insufficient_confidence": 40}}, "deployment_worthiness_scoring": {"scoring_criteria": [{"criterion": "security_impact_severity", "weight": 0.3, "scale": {"CRITICAL": 100, "HIGH": 80, "MEDIUM": 60, "LOW": 40}}, {"criterion": "exploitation_complexity", "weight": 0.25, "scale": {"Low": 100, "Medium": 75, "High": 50, "Very High": 25}}, {"criterion": "confidence_score", "weight": 0.2, "scale": "linear_0_to_100"}, {"criterion": "business_impact", "weight": 0.15, "scale": {"Critical_Business_Function": 100, "Important_Business_Function": 75, "Supporting_Function": 50, "Development_Only": 25}}, {"criterion": "compliance_requirement", "weight": 0.1, "scale": {"Regulatory_Mandate": 100, "Industry_Standard": 75, "Best_Practice": 50, "Recommendation": 25}}], "minimum_deployment_threshold": 70, "critical_override_threshold": 90, "environment_adjustments": {"production": 1.0, "staging": 0.9, "development": 0.7, "testing": 0.6}}, "remediation_effort_estimation": {"effort_categories": {"1-4_hours": {"description": "Simple configuration changes", "examples": ["Enable HTTPS only", "Disable public blob access", "Enable diagnostic logging"], "complexity_indicators": ["single_property_change", "boolean_flag_modification", "simple_policy_application"]}, "4-8_hours": {"description": "Moderate configuration changes", "examples": ["Implement private endpoints", "Configure network security groups", "Set up managed identities"], "complexity_indicators": ["multiple_property_changes", "resource_relationship_modifications", "security_policy_implementations"]}, "8-16_hours": {"description": "Complex architectural changes", "examples": ["Implement comprehensive RBAC", "Set up Key Vault integration", "Configure advanced threat protection"], "complexity_indicators": ["architectural_modifications", "multiple_resource_coordination", "advanced_security_implementations"]}, "16+_hours": {"description": "Major architectural overhaul", "examples": ["Complete network redesign", "Enterprise identity integration", "Comprehensive security framework implementation"], "complexity_indicators": ["fundamental_architecture_changes", "enterprise_integration_requirements", "comprehensive_security_overhaul"]}}}}