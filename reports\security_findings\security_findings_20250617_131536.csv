Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P1-Identity-CRITICAL,Identity Management,CRITICAL,IM-5,storage-accounts.bicep,19,"storageAccountFunc: 'networkAcls.defaultAction' is set to 'Allow', which permits public network access to the storage account. This exposes the storage account to the public internet, violating ASB NS-2 (Protect public endpoints).",Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only required IPs or virtual networks. Example: properties.networkAcls.defaultAction = 'Deny'.,N/A,AI,Generic
P1-Identity-CRITICAL,Identity Management,CRITICAL,IM-5,storage-accounts.bicep,49,"storageAccountFuzz: 'networkAcls.defaultAction' is set to 'Allow', which permits public network access to the storage account. This exposes the storage account to the public internet, violating ASB NS-2 (Protect public endpoints).",Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only required IPs or virtual networks. Example: properties.networkAcls.defaultAction = 'Deny'.,N/A,AI,Generic
P1-Identity-CRITICAL,Identity Management,CRITICAL,IM-5,storage-accounts.bicep,74,"storageAccountsCorpus: 'networkAcls.defaultAction' is set to 'Allow' via 'fuzzStorageProperties', which permits public network access to the storage accounts. This exposes the storage accounts to the public internet, violating ASB NS-2 (Protect public endpoints).",Set 'networkAcls.defaultAction' to 'Deny' in 'fuzzStorageProperties' and explicitly allow only required IPs or virtual networks. Example: fuzzStorageProperties.networkAcls.defaultAction = 'Deny'.,N/A,AI,Generic
P1-Identity-CRITICAL,Identity Management,CRITICAL,IM-6,keyvault.bicep,17,"Key Vault 'networkAcls.defaultAction' is set to 'Allow', which permits public network access to the Key Vault. This exposes the Key Vault endpoint to the public internet, violating ASB NS-2 (Protect public endpoints).","Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only required IPs or virtual networks. Example: 'defaultAction: ""Deny""'.",N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-1,app-config.bicep,7,"The Microsoft.AppConfiguration/configurationStores resource does not configure a private endpoint, exposing the service to potential public network access. This violates the requirement to use Private Endpoints to reduce attack surface (NS-5).",Configure a private endpoint for the App Configuration resource by adding a Microsoft.Network/privateEndpoints resource and associating it with the App Configuration instance.,N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-1,signalR.bicep,5,"The Microsoft.SignalRService/signalR resource does not configure a private endpoint. Without a private endpoint, the service is accessible over the public internet, increasing the attack surface.",Configure a private endpoint for the SignalR Service by adding a Microsoft.Network/privateEndpoints resource and associating it with the SignalR resource. This will restrict access to the service via the Azure backbone network only.,N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-5,function.bicep,61,The App Service resource 'function' (line 61) is deployed without explicit reference to Azure Firewall or a third-party firewall for advanced network protection. This weakens the network security posture.,Integrate the App Service with Azure Firewall or a third-party firewall. Configure firewall policies to monitor and control inbound and outbound traffic to the App Service.,N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-5,function.bicep,61,"The App Service resource 'function' (line 61) does not use Private Endpoints for secure, private connectivity. Public endpoints increase the attack surface.",Configure a Private Endpoint for the App Service to restrict access to resources within your virtual network and eliminate public exposure.,N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-6,keyvault.bicep,17,"Key Vault does not use a private endpoint. The template only configures network ACLs and does not provision a 'Microsoft.Network/privateEndpoints' resource for the Key Vault, violating ASB NS-5 (Use Private Endpoints).",Deploy a 'Microsoft.Network/privateEndpoints' resource and associate it with the Key Vault to restrict access to private network traffic only.,N/A,AI,Generic
P1-Identity-MEDIUM,Identity Management,MEDIUM,IM-1,autoscale-settings.bicep,1,No explicit Role-Based Access Control (RBAC) configuration is present in the template. CONTROL-08 (IM-6) requires RBAC to enforce least privilege access.,Define role assignments for resources using the 'Microsoft.Authorization/roleAssignments' resource to enforce RBAC and least privilege access.,N/A,AI,Generic
P1-Identity-MEDIUM,Identity Management,MEDIUM,IM-1,autoscale-settings.bicep,1,No managed identity is configured for resources in the template. CONTROL-09 (IM-8) recommends using managed identities to eliminate credential storage.,"Add a managed identity to resources that require Azure service access, and use it for authentication to other Azure resources such as Key Vault or Storage.",N/A,AI,Generic
P1-Identity-MEDIUM,Identity Management,MEDIUM,IM-1,signalR.bicep,5,"The Microsoft.SignalRService/signalR resource does not restrict public network access or configure IP allowlists. By default, SignalR Service endpoints are publicly accessible unless network ACLs or private endpoints are explicitly configured.","Restrict public access to the SignalR Service by configuring network ACLs to allow only required IP addresses or subnets, or by enabling private endpoints. Add the 'networkACLs' property to the resource definition to specify allowed IPs or configure a private endpoint.",N/A,AI,Generic
P1-Identity-MEDIUM,Identity Management,MEDIUM,IM-5,function.bicep,25,"The 'cors' configuration in 'commonSiteConfig' (line 25) allows arbitrary origins via the 'cors_origins' parameter, which may permit broad or unrestricted cross-origin access to the App Service. This can expose the application to unnecessary risk if not tightly controlled.",Restrict 'cors_origins' to only trusted domains. Avoid using wildcards or allowing all origins. Review and update the 'cors_origins' parameter to include only necessary and secure origins.,N/A,AI,Generic
P1-Identity-MEDIUM,Identity Management,MEDIUM,IM-5,function.bicep,61,The App Service resource 'function' (line 61) does not have Virtual Network Service Endpoints enabled for enhanced security when accessing PaaS services like Storage.,Enable Virtual Network Service Endpoints for the App Service and associated resources such as Storage to ensure secure traffic within the Azure backbone network.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,hub-network.bicep,4,"Virtual network 'hub-vnet' (line 4) defines a subnet 'hub-subnet' without any associated Network Security Group (NSG), violating the requirement to protect resources using NSGs for network segmentation.",Associate a Network Security Group (NSG) with the 'hub-subnet' by adding the 'networkSecurityGroup' property referencing an NSG resource to the subnet definition.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,scaleset-networks.bicep,7,"Resource 'scalesetOutboundIp' creates a public IP address, exposing resources to the public internet without explicit access restrictions. This violates the requirement to restrict public endpoints.","Restrict the public IP address to only required source IPs using NSGs or Azure Firewall, or remove the public IP if not strictly necessary.",N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,scaleset-networks.bicep,36,Subnet 'scaleset' in virtual network does not have a Network Security Group (NSG) associated. This violates the requirement to protect resources using NSGs for network segmentation and access control.,Associate a Network Security Group (NSG) with the 'scaleset' subnet to restrict inbound and outbound traffic according to least privilege principles.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,scaleset-networks.bicep,36,"Subnet 'scaleset' in virtual network is missing a Network Security Group (NSG), which is required to deny all inbound traffic by default and allow only necessary traffic.","Create and associate a Network Security Group (NSG) with the 'scaleset' subnet, configuring rules to deny all inbound traffic except for explicitly required ports and protocols.",N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-3,hub-network.bicep,4,"Virtual network 'hub-vnet' (line 4) does not configure a Network Security Group (NSG) for 'hub-subnet', failing to provide network-level access control as required.",Create an NSG resource and associate it with the 'hub-subnet' to enforce network-level access controls and restrict unnecessary inbound and outbound traffic.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-4,hub-network.bicep,4,Virtual network 'hub-vnet' (line 4) does not include Azure Firewall or a third-party firewall for advanced network protection.,Deploy Azure Firewall or a supported third-party firewall in the virtual network and configure appropriate firewall policies to monitor and control network traffic.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,hub-network.bicep,4,"Virtual network 'hub-vnet' (line 4) does not configure Private Endpoints for PaaS services, increasing the attack surface.",Configure Private Endpoints for services such as Storage and Key Vault to ensure private connectivity and reduce exposure to public networks.,N/A,AI,Generic
P2-Network-MEDIUM,Network Security,MEDIUM,NS-1,hub-network.bicep,4,Virtual network 'hub-vnet' (line 4) does not enable network traffic monitoring such as NSG flow logs or Network Watcher.,Enable NSG flow logs and configure Network Watcher for the virtual network to monitor and analyze network traffic.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,56,The 'APPINSIGHTS_INSTRUMENTATIONKEY' application setting is set directly from the 'app_insights_key' parameter. Storing secrets such as instrumentation keys in application settings or parameters violates sensitive information disclosure requirements.,Store the Application Insights instrumentation key in Azure Key Vault and reference it using a Key Vault reference in the app settings. Remove direct assignment of secrets in the template.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,57,The 'APPINSIGHTS_APPID' application setting is set directly from the 'app_insights_app_id' parameter. Storing sensitive identifiers in application settings or parameters can lead to information disclosure.,Store sensitive identifiers such as Application Insights App ID in Azure Key Vault and reference them using Key Vault references in the app settings. Remove direct assignment of sensitive values in the template.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,58,The 'LOG_ANALYTICS_WORKSPACE_ID' application setting is set directly from the 'log_analytics_workspace_id' parameter. Storing sensitive workspace IDs in application settings or parameters can lead to information disclosure.,Store sensitive workspace IDs in Azure Key Vault and reference them using Key Vault references in the app settings. Remove direct assignment of sensitive values in the template.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,70,The 'APPCONFIGURATION_ENDPOINT' application setting is set directly from the 'app_config_endpoint' parameter. Storing sensitive endpoints in application settings or parameters can lead to information disclosure.,Store sensitive endpoints in Azure Key Vault and reference them using Key Vault references in the app settings. Remove direct assignment of sensitive values in the template.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,75,The 'ONEFUZZ_DATA_STORAGE' application setting is set directly from the 'fuzz_storage_resource_id' parameter. Storing storage resource IDs in application settings or parameters can lead to information disclosure.,Store storage resource IDs in Azure Key Vault and reference them using Key Vault references in the app settings. Remove direct assignment of sensitive values in the template.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,76,The 'ONEFUZZ_FUNC_STORAGE' application setting is set directly from the 'func_storage_resource_id' parameter. Storing storage resource IDs in application settings or parameters can lead to information disclosure.,Store storage resource IDs in Azure Key Vault and reference them using Key Vault references in the app settings. Remove direct assignment of sensitive values in the template.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,77,The 'ONEFUZZ_MONITOR' application setting is set directly from the 'monitor_account_name' parameter. Storing monitor account names in application settings or parameters can lead to information disclosure.,Store monitor account names in Azure Key Vault and reference them using Key Vault references in the app settings. Remove direct assignment of sensitive values in the template.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,78,The 'ONEFUZZ_KEYVAULT' application setting is set directly from the 'keyvault_name' parameter. Storing Key Vault names in application settings or parameters can lead to information disclosure.,Store Key Vault names in Azure Key Vault and reference them using Key Vault references in the app settings. Remove direct assignment of sensitive values in the template.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,80,The 'ONEFUZZ_EVIDENCE_STORE_URL' application setting is set directly from the 'evidence_store_url' parameter. Storing evidence store URLs in application settings or parameters can lead to information disclosure.,Store evidence store URLs in Azure Key Vault and reference them using Key Vault references in the app settings. Remove direct assignment of sensitive values in the template.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,81,The 'ONEFUZZ_LIQUID_REQUIREMENT_URL' application setting is set directly from the 'liquid_requirement_url' parameter. Storing sensitive URLs in application settings or parameters can lead to information disclosure.,Store sensitive URLs in Azure Key Vault and reference them using Key Vault references in the app settings. Remove direct assignment of sensitive values in the template.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,89,The 'REGISTRATION_APP_ID' application setting is set directly from the 'registration_app_id' parameter. Storing sensitive application IDs in application settings or parameters can lead to information disclosure.,Store sensitive application IDs in Azure Key Vault and reference them using Key Vault references in the app settings. Remove direct assignment of sensitive values in the template.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,93,"The 'ADO_ACCESS_CLIENT_ID' application setting is set directly from the 'ado_access_client_id' parameter, which is hardcoded in the template. Storing sensitive client IDs in application settings or parameters can lead to information disclosure.",Store sensitive client IDs in Azure Key Vault and reference them using Key Vault references in the app settings. Remove direct assignment of sensitive values in the template.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,instance-config.bicep,7,"The 'specificConfig' parameter is an object that may contain sensitive fields (e.g., tenant_id, cli_client_id, Admins) and is passed directly into configuration variables. There is no evidence of Key Vault integration or secret reference, which risks disclosure of sensitive information.","Store all sensitive values (such as tenant_id, cli_client_id, Admins) in Azure Key Vault and reference them securely in the template using Key Vault references. Do not pass secrets directly via parameters or code.",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,operational-insights.bicep,92,"Sensitive information (InstrumentationKey) is output directly in appInsightsInstrumentationKey, which may expose secrets in deployment outputs. This violates DP-3: Manage sensitive information disclosure.",Remove the output of appInsightsInstrumentationKey or use Azure Key Vault references to securely handle and retrieve sensitive values. Do not expose secrets in outputs.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,server-farms.bicep,109,"The resource 'serverFarms_AntMDS_CERTIFICATE_PASSWORD_GENEVACERT' sets 'settingValue' to an empty string, which may indicate a placeholder for a certificate password. Storing secrets or sensitive values directly in template code violates ASB DP-3.",Remove any hardcoded or placeholder secrets from the template. Use Azure Key Vault references to securely retrieve sensitive values such as certificate passwords at deployment/runtime.,N/A,AI,Generic
