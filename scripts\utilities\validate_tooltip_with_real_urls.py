#!/usr/bin/env python3
"""
Validate HTML tooltip functionality with real URLs from enhanced CSV data.
This test uses the network_security_with_urls.csv file that contains actual URLs.
"""

import sys
import json
import tempfile
import shutil
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from security_opt import SecurityPRReviewer
    print("✅ Successfully imported SecurityPRReviewer")
except ImportError as e:
    print(f"❌ Failed to import SecurityPRReviewer: {e}")
    sys.exit(1)

def setup_test_environment():
    """Set up test environment with CSV files containing URLs."""
    print("🔧 Setting up test environment with URL-enhanced CSV files...")
    
    # Create temporary SecurityBenchmarks directory
    temp_benchmarks_dir = Path("temp_SecurityBenchmarks")
    temp_benchmarks_dir.mkdir(exist_ok=True)
    
    # Copy the enhanced network security CSV
    enhanced_csv = Path("SecurityBenchmarks/network_security_with_urls.csv")
    if enhanced_csv.exists():
        shutil.copy(enhanced_csv, temp_benchmarks_dir / "network_security.csv")
        print(f"✅ Copied enhanced network security CSV")
    else:
        print(f"❌ Enhanced CSV not found: {enhanced_csv}")
        return None
    
    # Copy other CSV files (without URLs for now)
    original_benchmarks = Path("SecurityBenchmarks")
    for csv_file in ["identity_management.csv", "data_protection.csv"]:
        src = original_benchmarks / csv_file
        dst = temp_benchmarks_dir / csv_file
        if src.exists():
            shutil.copy(src, dst)
            print(f"✅ Copied {csv_file}")
    
    return temp_benchmarks_dir

def test_url_extraction_from_csv():
    """Test URL extraction from CSV files with actual URLs."""
    print("\n🔗 Testing URL Extraction from Enhanced CSV")
    print("=" * 60)
    
    try:
        # Set up test environment
        temp_benchmarks_dir = setup_test_environment()
        if not temp_benchmarks_dir:
            return False
        
        # Temporarily rename original SecurityBenchmarks directory
        original_dir = Path("SecurityBenchmarks")
        backup_dir = Path("SecurityBenchmarks_backup")
        
        if original_dir.exists():
            original_dir.rename(backup_dir)
        
        # Rename temp directory to SecurityBenchmarks
        temp_benchmarks_dir.rename(original_dir)
        
        try:
            # Create reviewer instance (this will load the enhanced CSV)
            reviewer = SecurityPRReviewer(local_folder=".")
            
            # Test link extraction for controls that should have URLs
            test_controls = ["NS-1", "NS-2", "NS-3", "NS-4", "NS-5"]
            
            for control_id in test_controls:
                print(f"\n📋 Testing control {control_id}:")
                
                # Extract links
                links_info = reviewer._extract_control_links(control_id)
                
                print(f"   📚 Raw links found: {len(links_info.get('raw_links', []))}")
                for i, link in enumerate(links_info.get('raw_links', []), 1):
                    print(f"      {i}. {link}")
                
                print(f"   🔗 Formatted links: {links_info.get('formatted_links', 'None')}")
                print(f"   🔵 Azure guidance: {'Yes' if links_info.get('azure_guidance') else 'No'}")
                print(f"   📖 Implementation context: {'Yes' if links_info.get('implementation_context') else 'No'}")
                
                # Test description extraction for first URL
                if links_info.get('raw_links'):
                    first_url = links_info['raw_links'][0]
                    context = links_info.get('implementation_context', '')
                    description = reviewer._extract_link_description(first_url, context)
                    print(f"   🏷️ Generated description: '{description}'")
            
            return True
            
        finally:
            # Restore original directory structure
            if original_dir.exists():
                shutil.rmtree(original_dir)
            if backup_dir.exists():
                backup_dir.rename(original_dir)
            if temp_benchmarks_dir.exists():
                shutil.rmtree(temp_benchmarks_dir)
        
    except Exception as e:
        print(f"❌ Error during URL extraction test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_html_with_working_tooltips():
    """Test HTML generation with working tooltips."""
    print("\n🌐 Testing HTML Generation with Working Tooltips")
    print("=" * 60)
    
    try:
        # Set up test environment
        temp_benchmarks_dir = setup_test_environment()
        if not temp_benchmarks_dir:
            return False
        
        # Temporarily rename original SecurityBenchmarks directory
        original_dir = Path("SecurityBenchmarks")
        backup_dir = Path("SecurityBenchmarks_backup")
        
        if original_dir.exists():
            original_dir.rename(backup_dir)
        
        # Rename temp directory to SecurityBenchmarks
        temp_benchmarks_dir.rename(original_dir)
        
        try:
            # Create temporary directory for test files
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                # Create sample Bicep file
                bicep_content = """
resource storageAccount 'Microsoft.Storage/storageAccounts@2023-01-01' = {
  name: 'teststorage'
  properties: {
    publicNetworkAccess: 'Enabled'  // Security issue for NS-2
    networkAcls: {
      defaultAction: 'Allow'  // Security issue for NS-1
    }
  }
}

resource networkSecurityGroup 'Microsoft.Network/networkSecurityGroups@2023-01-01' = {
  name: 'test-nsg'
  properties: {
    securityRules: []  // Security issue for NS-3
  }
}
"""
                bicep_file = temp_path / "test.bicep"
                bicep_file.write_text(bicep_content)
                
                # Initialize reviewer
                reviewer = SecurityPRReviewer(local_folder=str(temp_path))
                
                # Create sample findings with controls that have URLs
                sample_findings = [
                    {
                        "file_path": str(bicep_file),
                        "control_id": "NS-1",
                        "severity": "HIGH",
                        "line": 6,
                        "description": "Network security group rules not properly configured",
                        "remediation": "Configure NSG with deny-by-default rules",
                        "source": "ai_analysis"
                    },
                    {
                        "file_path": str(bicep_file),
                        "control_id": "NS-2",
                        "severity": "CRITICAL",
                        "line": 4,
                        "description": "Public network access enabled without restrictions",
                        "remediation": "Configure private endpoints and restrict public access",
                        "source": "ai_analysis"
                    },
                    {
                        "file_path": str(bicep_file),
                        "control_id": "NS-3",
                        "severity": "MEDIUM",
                        "line": 13,
                        "description": "NSG security rules are empty",
                        "remediation": "Add appropriate security rules to NSG",
                        "source": "ai_analysis"
                    }
                ]
                
                # Create output directory
                output_dir = Path("working_tooltip_reports")
                output_dir.mkdir(exist_ok=True)
                
                print(f"📊 Generating HTML report with {len(sample_findings)} findings...")
                
                # Export HTML with tooltip functionality
                reviewer.export_findings(sample_findings, format="html", output_dir=str(output_dir))
                
                # Find generated HTML file
                html_files = list(output_dir.glob("*.html"))
                if html_files:
                    html_file = html_files[0]
                    print(f"✅ Generated HTML report: {html_file}")
                    
                    # Check if tooltip data is present in HTML
                    with open(html_file, 'r', encoding='utf-8') as f:
                        html_content = f.read()
                    
                    # Check for tooltip functionality
                    has_tooltip_function = 'addTooltipFunctionality' in html_content
                    has_tooltip_data = 'window.tooltipLinks' in html_content and 'window.tooltipLinks = {};' not in html_content
                    has_tooltip_icons = '📚' in html_content
                    has_tooltip_css = '.custom-tooltip' in html_content
                    
                    print(f"   🔧 Tooltip function present: {'✅' if has_tooltip_function else '❌'}")
                    print(f"   📊 Tooltip data populated: {'✅' if has_tooltip_data else '❌'}")
                    print(f"   📚 Tooltip icons included: {'✅' if has_tooltip_icons else '❌'}")
                    print(f"   🎨 Tooltip CSS styling: {'✅' if has_tooltip_css else '❌'}")
                    
                    # Extract and analyze tooltip data
                    if 'window.tooltipLinks = ' in html_content:
                        start = html_content.find('window.tooltipLinks = ') + len('window.tooltipLinks = ')
                        end = html_content.find('};', start) + 1
                        tooltip_data_str = html_content[start:end]
                        
                        try:
                            tooltip_data = json.loads(tooltip_data_str)
                            print(f"   🔗 Tooltip data for controls: {list(tooltip_data.keys())}")
                            
                            total_links = 0
                            for control_id, data in tooltip_data.items():
                                links_count = len(data.get('raw_links', []))
                                has_guidance = bool(data.get('azure_guidance'))
                                total_links += links_count
                                print(f"      {control_id}: {links_count} links, guidance: {'Yes' if has_guidance else 'No'}")
                                
                                # Show first link as example
                                if data.get('raw_links'):
                                    first_link = data['raw_links'][0]
                                    print(f"         Example link: {first_link}")
                            
                            print(f"   📈 Total links across all controls: {total_links}")
                            
                            # Validate that we have working tooltip data
                            if total_links > 0:
                                print("   🎉 SUCCESS: Tooltip data contains actual URLs!")
                                return True
                            else:
                                print("   ⚠️ WARNING: No URLs found in tooltip data")
                                return False
                                
                        except json.JSONDecodeError as e:
                            print(f"   ❌ Could not parse tooltip data: {e}")
                            return False
                    else:
                        print("   ❌ No tooltip data found in HTML")
                        return False
                        
                else:
                    print("❌ No HTML file generated")
                    return False
                    
        finally:
            # Restore original directory structure
            if original_dir.exists():
                shutil.rmtree(original_dir)
            if backup_dir.exists():
                backup_dir.rename(original_dir)
            if temp_benchmarks_dir.exists():
                shutil.rmtree(temp_benchmarks_dir)
        
    except Exception as e:
        print(f"❌ Error during HTML generation test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run tooltip validation with real URLs."""
    print("🧪 TOOLTIP VALIDATION WITH REAL URLS")
    print("=" * 70)
    
    tests = [
        ("URL Extraction from Enhanced CSV", test_url_extraction_from_csv),
        ("HTML Generation with Working Tooltips", test_html_with_working_tooltips)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running {test_name} Test...")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"❌ {test_name} test ERROR: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 VALIDATION SUMMARY")
    print("=" * 70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tooltip validation tests with real URLs PASSED!")
        print("\n💡 HTML tooltip functionality is working correctly!")
        print("📋 Check the 'working_tooltip_reports' directory for the generated HTML with working tooltips")
        return True
    else:
        print("⚠️ Some tests failed. Check implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
