"""
Template Performance Monitoring for IaC Guardian

This module provides comprehensive performance monitoring and metrics collection
for the template system.
"""

import time
import json
import threading
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

@dataclass
class TemplateMetric:
    """Individual template operation metric"""
    template_path: str
    operation: str  # load, validate, render, cache_hit, cache_miss
    duration_ms: float
    timestamp: datetime
    success: bool
    error_message: Optional[str] = None
    template_size_bytes: Optional[int] = None
    cache_hit: bool = False

@dataclass
class PerformanceStats:
    """Aggregated performance statistics"""
    total_operations: int
    avg_duration_ms: float
    min_duration_ms: float
    max_duration_ms: float
    success_rate: float
    cache_hit_rate: float
    operations_per_second: float
    total_size_bytes: int
    
class TemplateMetricsCollector:
    """Collects and manages template performance metrics"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize metrics collector
        
        Args:
            config: Performance monitoring configuration
        """
        self.config = config
        self.enabled = config.get('monitoring_enabled', True)
        self.metrics_file = Path(config.get('metrics_file', 'logs/template_metrics.json'))
        self.slow_threshold_ms = config.get('slow_load_threshold_ms', 1000)
        self.log_slow_loads = config.get('log_slow_loads', True)
        
        # Thread-safe metrics storage
        self._lock = threading.Lock()
        self._metrics: deque = deque(maxlen=10000)  # Keep last 10k metrics
        self._stats_cache: Dict[str, PerformanceStats] = {}
        self._cache_stats = {'hits': 0, 'misses': 0, 'total': 0}
        
        # Ensure metrics directory exists
        self.metrics_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Load existing metrics if available
        self._load_existing_metrics()
    
    def record_metric(self, 
                     template_path: str,
                     operation: str,
                     duration_ms: float,
                     success: bool = True,
                     error_message: Optional[str] = None,
                     template_size_bytes: Optional[int] = None,
                     cache_hit: bool = False) -> None:
        """
        Record a template operation metric
        
        Args:
            template_path: Path to the template file
            operation: Type of operation (load, validate, render, etc.)
            duration_ms: Operation duration in milliseconds
            success: Whether the operation was successful
            error_message: Error message if operation failed
            template_size_bytes: Size of template in bytes
            cache_hit: Whether this was a cache hit
        """
        if not self.enabled:
            return
        
        metric = TemplateMetric(
            template_path=template_path,
            operation=operation,
            duration_ms=duration_ms,
            timestamp=datetime.now(),
            success=success,
            error_message=error_message,
            template_size_bytes=template_size_bytes,
            cache_hit=cache_hit
        )
        
        with self._lock:
            self._metrics.append(metric)
            
            # Update cache stats
            if operation in ['load', 'render']:
                self._cache_stats['total'] += 1
                if cache_hit:
                    self._cache_stats['hits'] += 1
                else:
                    self._cache_stats['misses'] += 1
            
            # Clear stats cache to force recalculation
            self._stats_cache.clear()
        
        # Log slow operations
        if self.log_slow_loads and duration_ms > self.slow_threshold_ms:
            logger.warning(
                f"Slow template operation: {operation} on {template_path} "
                f"took {duration_ms:.2f}ms (threshold: {self.slow_threshold_ms}ms)"
            )
        
        # Log errors
        if not success and error_message:
            logger.error(f"Template operation failed: {operation} on {template_path}: {error_message}")
    
    def get_stats(self, 
                  template_path: Optional[str] = None,
                  operation: Optional[str] = None,
                  time_window_hours: Optional[int] = None) -> PerformanceStats:
        """
        Get performance statistics
        
        Args:
            template_path: Filter by specific template path
            operation: Filter by specific operation type
            time_window_hours: Only include metrics from last N hours
            
        Returns:
            PerformanceStats object with aggregated statistics
        """
        cache_key = f"{template_path}_{operation}_{time_window_hours}"
        
        with self._lock:
            if cache_key in self._stats_cache:
                return self._stats_cache[cache_key]
            
            # Filter metrics
            filtered_metrics = list(self._metrics)
            
            if template_path:
                filtered_metrics = [m for m in filtered_metrics if m.template_path == template_path]
            
            if operation:
                filtered_metrics = [m for m in filtered_metrics if m.operation == operation]
            
            if time_window_hours:
                cutoff_time = datetime.now() - timedelta(hours=time_window_hours)
                filtered_metrics = [m for m in filtered_metrics if m.timestamp >= cutoff_time]
            
            if not filtered_metrics:
                return PerformanceStats(0, 0, 0, 0, 0, 0, 0, 0)
            
            # Calculate statistics
            durations = [m.duration_ms for m in filtered_metrics]
            successes = [m for m in filtered_metrics if m.success]
            cache_operations = [m for m in filtered_metrics if m.operation in ['load', 'render']]
            cache_hits = [m for m in cache_operations if m.cache_hit]
            
            # Time-based operations per second
            if time_window_hours and filtered_metrics:
                time_span_seconds = time_window_hours * 3600
                ops_per_second = len(filtered_metrics) / time_span_seconds
            else:
                ops_per_second = 0
            
            stats = PerformanceStats(
                total_operations=len(filtered_metrics),
                avg_duration_ms=sum(durations) / len(durations),
                min_duration_ms=min(durations),
                max_duration_ms=max(durations),
                success_rate=len(successes) / len(filtered_metrics) * 100,
                cache_hit_rate=len(cache_hits) / len(cache_operations) * 100 if cache_operations else 0,
                operations_per_second=ops_per_second,
                total_size_bytes=sum(m.template_size_bytes or 0 for m in filtered_metrics)
            )
            
            self._stats_cache[cache_key] = stats
            return stats
    
    def get_slow_operations(self, limit: int = 10) -> List[TemplateMetric]:
        """
        Get the slowest template operations
        
        Args:
            limit: Maximum number of operations to return
            
        Returns:
            List of slowest TemplateMetric objects
        """
        with self._lock:
            sorted_metrics = sorted(self._metrics, key=lambda m: m.duration_ms, reverse=True)
            return list(sorted_metrics[:limit])
    
    def get_error_summary(self, time_window_hours: int = 24) -> Dict[str, int]:
        """
        Get summary of errors in the specified time window
        
        Args:
            time_window_hours: Time window in hours
            
        Returns:
            Dictionary mapping error messages to occurrence counts
        """
        cutoff_time = datetime.now() - timedelta(hours=time_window_hours)
        
        with self._lock:
            error_metrics = [
                m for m in self._metrics 
                if not m.success and m.timestamp >= cutoff_time and m.error_message
            ]
            
            error_counts = defaultdict(int)
            for metric in error_metrics:
                error_counts[metric.error_message] += 1
            
            return dict(error_counts)
    
    def export_metrics(self, file_path: Optional[Path] = None) -> None:
        """
        Export metrics to JSON file
        
        Args:
            file_path: Optional custom file path, uses default if None
        """
        if not self.enabled:
            return
        
        export_path = file_path or self.metrics_file
        
        with self._lock:
            # Convert metrics to serializable format
            metrics_data = {
                'export_timestamp': datetime.now().isoformat(),
                'total_metrics': len(self._metrics),
                'cache_stats': self._cache_stats.copy(),
                'metrics': [
                    {
                        **asdict(metric),
                        'timestamp': metric.timestamp.isoformat()
                    }
                    for metric in self._metrics
                ]
            }
        
        try:
            with open(export_path, 'w') as f:
                json.dump(metrics_data, f, indent=2)
            logger.info(f"Exported {len(self._metrics)} metrics to {export_path}")
        except Exception as e:
            logger.error(f"Failed to export metrics: {e}")
    
    def _load_existing_metrics(self) -> None:
        """Load existing metrics from file if available"""
        if not self.metrics_file.exists():
            return
        
        try:
            with open(self.metrics_file, 'r') as f:
                data = json.load(f)
            
            # Load cache stats
            if 'cache_stats' in data:
                self._cache_stats.update(data['cache_stats'])
            
            # Load recent metrics (last 1000 to avoid memory issues)
            if 'metrics' in data:
                recent_metrics = data['metrics'][-1000:]
                for metric_data in recent_metrics:
                    # Convert timestamp back to datetime
                    metric_data['timestamp'] = datetime.fromisoformat(metric_data['timestamp'])
                    metric = TemplateMetric(**metric_data)
                    self._metrics.append(metric)
            
            logger.info(f"Loaded {len(self._metrics)} existing metrics from {self.metrics_file}")
            
        except Exception as e:
            logger.warning(f"Failed to load existing metrics: {e}")
    
    def clear_metrics(self) -> None:
        """Clear all collected metrics"""
        with self._lock:
            self._metrics.clear()
            self._stats_cache.clear()
            self._cache_stats = {'hits': 0, 'misses': 0, 'total': 0}
        logger.info("Cleared all template metrics")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics"""
        with self._lock:
            total = self._cache_stats['total']
            if total == 0:
                return {'hit_rate': 0, 'miss_rate': 0, 'total_operations': 0}
            
            return {
                'hit_rate': (self._cache_stats['hits'] / total) * 100,
                'miss_rate': (self._cache_stats['misses'] / total) * 100,
                'total_operations': total,
                'hits': self._cache_stats['hits'],
                'misses': self._cache_stats['misses']
            }

class PerformanceTimer:
    """Context manager for timing template operations"""
    
    def __init__(self, metrics_collector: TemplateMetricsCollector, 
                 template_path: str, operation: str, **kwargs):
        """
        Initialize performance timer
        
        Args:
            metrics_collector: Metrics collector instance
            template_path: Path to template being operated on
            operation: Type of operation being performed
            **kwargs: Additional metric parameters
        """
        self.metrics_collector = metrics_collector
        self.template_path = template_path
        self.operation = operation
        self.kwargs = kwargs
        self.start_time = None
        self.success = True
        self.error_message = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time is not None:
            duration_ms = (time.time() - self.start_time) * 1000
            
            if exc_type is not None:
                self.success = False
                self.error_message = str(exc_val)
            
            self.metrics_collector.record_metric(
                template_path=self.template_path,
                operation=self.operation,
                duration_ms=duration_ms,
                success=self.success,
                error_message=self.error_message,
                **self.kwargs
            )
        
        # Don't suppress exceptions
        return False

class HotReloadWatcher:
    """File system watcher for hot reloading templates"""

    def __init__(self, config: Dict[str, Any], callback):
        """
        Initialize hot reload watcher

        Args:
            config: Hot reload configuration
            callback: Function to call when files change
        """
        self.config = config
        self.callback = callback
        self.enabled = config.get('enabled', False)
        self.watch_dirs = [Path(d) for d in config.get('watch_directories', ['templates'])]
        self.watch_extensions = config.get('watch_extensions', ['.html', '.css', '.js', '.txt'])
        self.debounce_ms = config.get('debounce_ms', 500)

        self._last_change_time = {}
        self._watcher_thread = None
        self._stop_event = threading.Event()

        if self.enabled:
            self.start_watching()

    def start_watching(self):
        """Start the file system watcher"""
        if not self.enabled:
            return

        try:
            import watchdog
            from watchdog.observers import Observer
            from watchdog.events import FileSystemEventHandler

            class TemplateChangeHandler(FileSystemEventHandler):
                def __init__(self, parent):
                    self.parent = parent

                def on_modified(self, event):
                    if not event.is_directory:
                        self.parent._handle_file_change(event.src_path)

            self.observer = Observer()
            handler = TemplateChangeHandler(self)

            for watch_dir in self.watch_dirs:
                if watch_dir.exists():
                    self.observer.schedule(handler, str(watch_dir), recursive=True)
                    logger.info(f"Watching directory for changes: {watch_dir}")

            self.observer.start()
            logger.info("Hot reload watcher started")

        except ImportError:
            logger.warning("watchdog package not available, hot reload disabled")
            self.enabled = False
        except Exception as e:
            logger.error(f"Failed to start hot reload watcher: {e}")
            self.enabled = False

    def _handle_file_change(self, file_path: str):
        """Handle file change event with debouncing"""
        file_path = Path(file_path)

        # Check if file extension is watched
        if file_path.suffix not in self.watch_extensions:
            return

        # Debounce rapid changes
        now = time.time()
        last_change = self._last_change_time.get(str(file_path), 0)

        if (now - last_change) * 1000 < self.debounce_ms:
            return

        self._last_change_time[str(file_path)] = now

        logger.info(f"Template file changed: {file_path}")

        # Call the callback
        try:
            self.callback(file_path)
        except Exception as e:
            logger.error(f"Error in hot reload callback: {e}")

    def stop_watching(self):
        """Stop the file system watcher"""
        if hasattr(self, 'observer') and self.observer:
            self.observer.stop()
            self.observer.join()
            logger.info("Hot reload watcher stopped")

class TemplateVersionManager:
    """Manages template versioning and compatibility"""

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize version manager

        Args:
            config: Versioning configuration
        """
        self.config = config
        self.enabled = config.get('enabled', True)
        self.version_file = Path(config.get('version_file', 'templates/VERSION'))
        self.min_supported_version = config.get('min_supported_version', '1.0.0')
        self.compatibility_check = config.get('compatibility_check', True)

        self.current_version = self._load_current_version()

    def _load_current_version(self) -> str:
        """Load current template version"""
        if not self.version_file.exists():
            logger.warning(f"Version file not found: {self.version_file}")
            return "1.0.0"

        try:
            with open(self.version_file, 'r') as f:
                version = f.read().strip()
            logger.info(f"Loaded template version: {version}")
            return version
        except Exception as e:
            logger.error(f"Failed to load version file: {e}")
            return "1.0.0"

    def check_compatibility(self, required_version: str) -> bool:
        """
        Check if current version is compatible with required version

        Args:
            required_version: Required template version

        Returns:
            True if compatible, False otherwise
        """
        if not self.enabled or not self.compatibility_check:
            return True

        try:
            current_parts = [int(x) for x in self.current_version.split('.')]
            required_parts = [int(x) for x in required_version.split('.')]
            min_parts = [int(x) for x in self.min_supported_version.split('.')]

            # Check if required version is supported
            if required_parts < min_parts:
                logger.warning(f"Required version {required_version} is below minimum supported {self.min_supported_version}")
                return False

            # Check if current version meets requirements
            if current_parts < required_parts:
                logger.warning(f"Current version {self.current_version} is below required {required_version}")
                return False

            return True

        except Exception as e:
            logger.error(f"Error checking version compatibility: {e}")
            return False

    def get_version_info(self) -> Dict[str, str]:
        """Get version information"""
        return {
            'current_version': self.current_version,
            'min_supported_version': self.min_supported_version,
            'version_file': str(self.version_file),
            'compatibility_check_enabled': self.compatibility_check
        }
