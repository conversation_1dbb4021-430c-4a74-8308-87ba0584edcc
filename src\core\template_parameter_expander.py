import json
import re
import fnmatch
from typing import Dict, Optional, List, Union, Tuple, Any
import os
import logging
from pathlib import Path
import urllib.parse
import base64

logger = logging.getLogger(__name__)

# Define sensitive parameter keywords
SENSITIVE_PARAMETER_KEYWORDS = {
    'secret', 'password', 'pwd', 'key', 'certificate', 'cert',
    'token', 'auth', 'credentials', 'clientid', 'clientsecret',
    'connection', 'apikey', 'access', 'admin', 'private'
}

class TemplateParameterExpander:
    """Handles parameter expansion for both ARM and Bicep templates with enhanced reference resolution"""

    def __init__(self):
        self.security_findings = []
        self.variables_cache = {}  # Cache for resolved variables
        self.template_cache = {}   # Cache for loaded templates
        self.reference_depth = 0   # Track reference resolution depth to prevent infinite loops
        self.max_reference_depth = 10  # Maximum depth for reference resolution

        # Enhanced ARM template functions with better reference handling
        self.resource_functions = {
            'resourceGroup': self._resolve_resource_group,
            'subscription': self._resolve_subscription,
            'uniqueString': self._resolve_unique_string,
            'concat': self._resolve_concat,
            'variables': self._resolve_variables,
            'parameters': self._resolve_parameters,
            'reference': self._resolve_reference,
            'listKeys': self._resolve_list_keys,
            'listSecrets': self._resolve_list_secrets,
            'deployment': self._resolve_deployment,
            'resourceId': self._resolve_resource_id,
            'uri': self._resolve_uri,
            'base64': self._resolve_base64,
            'format': self._resolve_format,
            'replace': self._resolve_replace,
            'split': self._resolve_split,
            'join': self._resolve_join,
            'length': self._resolve_length,
            'first': self._resolve_first,
            'last': self._resolve_last,
            'skip': self._resolve_skip,
            'take': self._resolve_take,
            'contains': self._resolve_contains,
            'startsWith': self._resolve_starts_with,
            'endsWith': self._resolve_ends_with,
            'indexOf': self._resolve_index_of,
            'substring': self._resolve_substring,
            'toLower': self._resolve_to_lower,
            'toUpper': self._resolve_to_upper,
            'trim': self._resolve_trim,
            'padLeft': self._resolve_pad_left,
            'if': self._resolve_if,
            'equals': self._resolve_equals,
            'greater': self._resolve_greater,
            'less': self._resolve_less,
            'and': self._resolve_and,
            'or': self._resolve_or,
            'not': self._resolve_not,
            'add': self._resolve_add,
            'sub': self._resolve_sub,
            'mul': self._resolve_mul,
            'div': self._resolve_div,
            'mod': self._resolve_mod,
            'min': self._resolve_min,
            'max': self._resolve_max,
            'range': self._resolve_range,
            'string': self._resolve_string,
            'int': self._resolve_int,
            'float': self._resolve_float,
            'bool': self._resolve_bool,
            'array': self._resolve_array,
            'createArray': self._resolve_create_array,
            'empty': self._resolve_empty,
            'intersection': self._resolve_intersection,
            'union': self._resolve_union,
            'json': self._resolve_json,
            'base64ToString': self._resolve_base64_to_string,
            'dataUriToString': self._resolve_data_uri_to_string,
            'uriComponent': self._resolve_uri_component,
            'uriComponentToString': self._resolve_uri_component_to_string,
            'guid': self._resolve_guid,
            'newGuid': self._resolve_new_guid,
            'utcNow': self._resolve_utc_now,
            'dateTimeAdd': self._resolve_date_time_add,
        }

    # ARM Template Function Implementations
    def _resolve_resource_group(self) -> Dict[str, str]:
        """Resolve resourceGroup() function"""
        return {'name': 'placeholder-resourceGroup', 'location': 'placeholder-location', 'id': '/subscriptions/placeholder/resourceGroups/placeholder-resourceGroup'}

    def _resolve_subscription(self) -> Dict[str, str]:
        """Resolve subscription() function"""
        return {'subscriptionId': 'placeholder-subscriptionId', 'tenantId': 'placeholder-tenantId'}

    def _resolve_unique_string(self, *args) -> str:
        """Resolve uniqueString() function"""
        # Create a deterministic but unique-looking string for analysis
        combined = ''.join(str(arg) for arg in args)
        return f"unique{hash(combined) % 10000:04d}"

    def _resolve_concat(self, *args) -> str:
        """Resolve concat() function"""
        return ''.join(str(arg) for arg in args)

    def _resolve_variables(self, name: str) -> str:
        """Resolve variables() function with caching"""
        if name in self.variables_cache:
            return self.variables_cache[name]

        # For analysis purposes, return a placeholder that indicates variable usage
        placeholder = f"VARIABLE_{name}"
        self.variables_cache[name] = placeholder
        return placeholder

    def _resolve_parameters(self, name: str) -> str:
        """Resolve parameters() function"""
        return f"PARAMETER_{name}"

    def _resolve_reference(self, resource_name: str, api_version: str = None) -> str:
        """Resolve reference() function"""
        return f"REFERENCE_{resource_name}"

    def _resolve_list_keys(self, resource_id: str, api_version: str = None) -> Dict:
        """Resolve listKeys() function"""
        return {"keys": [{"keyName": "key1", "value": "PLACEHOLDER_KEY_VALUE"}]}

    def _resolve_list_secrets(self, resource_id: str, api_version: str = None) -> Dict:
        """Resolve listSecrets() function"""
        return {"secrets": [{"name": "secret1", "value": "PLACEHOLDER_SECRET_VALUE"}]}

    def _resolve_deployment(self) -> Dict:
        """Resolve deployment() function"""
        return {"name": "placeholder-deployment"}

    def _resolve_resource_id(self, *args) -> str:
        """Resolve resourceId() function"""
        if len(args) >= 2:
            resource_type = args[0]
            resource_name = args[1]
            return f"/subscriptions/placeholder/resourceGroups/placeholder/providers/{resource_type}/{resource_name}"
        return "PLACEHOLDER_RESOURCE_ID"

    def _resolve_uri(self, base_uri: str, relative_uri: str) -> str:
        """Resolve uri() function"""
        try:
            return urllib.parse.urljoin(str(base_uri), str(relative_uri))
        except Exception:
            return f"{base_uri}/{relative_uri}"

    def _resolve_base64(self, input_string: str) -> str:
        """Resolve base64() function"""
        try:
            import base64
            return base64.b64encode(str(input_string).encode()).decode()
        except Exception:
            return f"BASE64_{input_string}"

    def _resolve_format(self, format_string: str, *args) -> str:
        """Resolve format() function"""
        try:
            return str(format_string).format(*args)
        except Exception:
            return f"FORMAT_{format_string}"

    def _resolve_replace(self, original: str, old_value: str, new_value: str) -> str:
        """Resolve replace() function"""
        return str(original).replace(str(old_value), str(new_value))

    def _resolve_split(self, input_string: str, delimiter: str) -> List[str]:
        """Resolve split() function"""
        return str(input_string).split(str(delimiter))

    def _resolve_join(self, array: List, delimiter: str) -> str:
        """Resolve join() function"""
        if isinstance(array, list):
            return str(delimiter).join(str(item) for item in array)
        return str(array)

    def _resolve_length(self, input_value) -> int:
        """Resolve length() function"""
        if isinstance(input_value, (list, dict, str)):
            return len(input_value)
        return 0

    def _resolve_first(self, input_value):
        """Resolve first() function"""
        if isinstance(input_value, list) and input_value:
            return input_value[0]
        elif isinstance(input_value, str) and input_value:
            return input_value[0]
        return None

    def _resolve_last(self, input_value):
        """Resolve last() function"""
        if isinstance(input_value, list) and input_value:
            return input_value[-1]
        elif isinstance(input_value, str) and input_value:
            return input_value[-1]
        return None

    def _resolve_skip(self, input_value, number_to_skip: int):
        """Resolve skip() function"""
        try:
            skip_count = int(number_to_skip)
            if isinstance(input_value, (list, str)):
                return input_value[skip_count:]
            return input_value
        except (ValueError, TypeError):
            return input_value

    def _resolve_take(self, input_value, number_to_take: int):
        """Resolve take() function"""
        try:
            take_count = int(number_to_take)
            if isinstance(input_value, (list, str)):
                return input_value[:take_count]
            return input_value
        except (ValueError, TypeError):
            return input_value

    def _resolve_contains(self, input_value, item_to_find) -> bool:
        """Resolve contains() function"""
        if isinstance(input_value, (list, dict, str)):
            return item_to_find in input_value
        return False

    def _resolve_starts_with(self, input_string: str, prefix: str) -> bool:
        """Resolve startsWith() function"""
        return str(input_string).startswith(str(prefix))

    def _resolve_ends_with(self, input_string: str, suffix: str) -> bool:
        """Resolve endsWith() function"""
        return str(input_string).endswith(str(suffix))

    def _resolve_index_of(self, input_string: str, substring: str) -> int:
        """Resolve indexOf() function"""
        try:
            return str(input_string).index(str(substring))
        except ValueError:
            return -1

    def _resolve_substring(self, input_string: str, start_index: int, length: int = None) -> str:
        """Resolve substring() function"""
        try:
            start = int(start_index)
            if length is not None:
                end = start + int(length)
                return str(input_string)[start:end]
            return str(input_string)[start:]
        except (ValueError, TypeError):
            return str(input_string)

    def _resolve_to_lower(self, input_string: str) -> str:
        """Resolve toLower() function"""
        return str(input_string).lower()

    def _resolve_to_upper(self, input_string: str) -> str:
        """Resolve toUpper() function"""
        return str(input_string).upper()

    def _resolve_trim(self, input_string: str) -> str:
        """Resolve trim() function"""
        return str(input_string).strip()

    def _resolve_pad_left(self, input_string: str, total_length: int, pad_character: str = ' ') -> str:
        """Resolve padLeft() function"""
        try:
            return str(input_string).rjust(int(total_length), str(pad_character))
        except (ValueError, TypeError):
            return str(input_string)

    def _resolve_if(self, condition, true_value, false_value):
        """Resolve if() function"""
        return true_value if condition else false_value

    def _resolve_equals(self, value1, value2) -> bool:
        """Resolve equals() function"""
        return value1 == value2

    def _resolve_greater(self, value1, value2) -> bool:
        """Resolve greater() function"""
        try:
            return float(value1) > float(value2)
        except (ValueError, TypeError):
            return str(value1) > str(value2)

    def _resolve_less(self, value1, value2) -> bool:
        """Resolve less() function"""
        try:
            return float(value1) < float(value2)
        except (ValueError, TypeError):
            return str(value1) < str(value2)

    def _resolve_and(self, *conditions) -> bool:
        """Resolve and() function"""
        return all(bool(condition) for condition in conditions)

    def _resolve_or(self, *conditions) -> bool:
        """Resolve or() function"""
        return any(bool(condition) for condition in conditions)

    def _resolve_not(self, condition) -> bool:
        """Resolve not() function"""
        return not bool(condition)

    def _resolve_add(self, *values):
        """Resolve add() function"""
        try:
            return sum(float(value) for value in values)
        except (ValueError, TypeError):
            return 0

    def _resolve_sub(self, value1, value2):
        """Resolve sub() function"""
        try:
            return float(value1) - float(value2)
        except (ValueError, TypeError):
            return 0

    def _resolve_mul(self, value1, value2):
        """Resolve mul() function"""
        try:
            return float(value1) * float(value2)
        except (ValueError, TypeError):
            return 0

    def _resolve_div(self, value1, value2):
        """Resolve div() function"""
        try:
            return float(value1) / float(value2)
        except (ValueError, TypeError, ZeroDivisionError):
            return 0

    def _resolve_mod(self, value1, value2):
        """Resolve mod() function"""
        try:
            return float(value1) % float(value2)
        except (ValueError, TypeError, ZeroDivisionError):
            return 0

    def _resolve_min(self, *values):
        """Resolve min() function"""
        try:
            return min(float(value) for value in values)
        except (ValueError, TypeError):
            return 0

    def _resolve_max(self, *values):
        """Resolve max() function"""
        try:
            return max(float(value) for value in values)
        except (ValueError, TypeError):
            return 0

    def _resolve_range(self, start: int, count: int) -> List[int]:
        """Resolve range() function"""
        try:
            return list(range(int(start), int(start) + int(count)))
        except (ValueError, TypeError):
            return []

    def _resolve_string(self, value) -> str:
        """Resolve string() function"""
        return str(value)

    def _resolve_int(self, value) -> int:
        """Resolve int() function"""
        try:
            return int(float(value))
        except (ValueError, TypeError):
            return 0

    def _resolve_float(self, value) -> float:
        """Resolve float() function"""
        try:
            return float(value)
        except (ValueError, TypeError):
            return 0.0

    def _resolve_bool(self, value) -> bool:
        """Resolve bool() function"""
        if isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on')
        return bool(value)

    def _resolve_array(self, *values) -> List:
        """Resolve array() function"""
        return list(values)

    def _resolve_create_array(self, *values) -> List:
        """Resolve createArray() function"""
        return list(values)

    def _resolve_empty(self, value) -> bool:
        """Resolve empty() function"""
        if value is None:
            return True
        if isinstance(value, (list, dict, str)):
            return len(value) == 0
        return False

    def _resolve_intersection(self, array1, array2):
        """Resolve intersection() function"""
        if isinstance(array1, list) and isinstance(array2, list):
            return [item for item in array1 if item in array2]
        return []

    def _resolve_union(self, array1, array2):
        """Resolve union() function"""
        if isinstance(array1, list) and isinstance(array2, list):
            return list(set(array1) | set(array2))
        return []

    def _resolve_json(self, json_string: str):
        """Resolve json() function"""
        try:
            return json.loads(str(json_string))
        except json.JSONDecodeError:
            return {}

    def _resolve_base64_to_string(self, base64_string: str) -> str:
        """Resolve base64ToString() function"""
        try:
            import base64
            return base64.b64decode(str(base64_string)).decode()
        except Exception:
            return f"DECODED_{base64_string}"

    def _resolve_data_uri_to_string(self, data_uri: str) -> str:
        """Resolve dataUriToString() function"""
        try:
            if ',' in str(data_uri):
                return str(data_uri).split(',', 1)[1]
            return str(data_uri)
        except Exception:
            return str(data_uri)

    def _resolve_uri_component(self, string_to_encode: str) -> str:
        """Resolve uriComponent() function"""
        try:
            return urllib.parse.quote(str(string_to_encode))
        except Exception:
            return str(string_to_encode)

    def _resolve_uri_component_to_string(self, encoded_string: str) -> str:
        """Resolve uriComponentToString() function"""
        try:
            return urllib.parse.unquote(str(encoded_string))
        except Exception:
            return str(encoded_string)

    def _resolve_guid(self, *args) -> str:
        """Resolve guid() function"""
        import uuid
        # Create deterministic GUID for analysis consistency
        combined = ''.join(str(arg) for arg in args)
        return str(uuid.uuid5(uuid.NAMESPACE_DNS, combined))

    def _resolve_new_guid(self) -> str:
        """Resolve newGuid() function"""
        import uuid
        return str(uuid.uuid4())

    def _resolve_utc_now(self, format_string: str = None) -> str:
        """Resolve utcNow() function"""
        from datetime import datetime
        now = datetime.utcnow()
        if format_string:
            try:
                return now.strftime(str(format_string))
            except Exception:
                pass
        return now.isoformat() + 'Z'

    def _resolve_date_time_add(self, base_time: str, duration: str, format_string: str = None) -> str:
        """Resolve dateTimeAdd() function"""
        # Simplified implementation for analysis
        return f"DATETIME_ADD_{base_time}_{duration}"

    def expand_template(self, template_content: str, parameter_values: Optional[Dict] = None) -> Tuple[str, Dict]:
        """
        Expands parameters in a template with their actual values.
        
        Args:
            template_content: The template content (ARM or Bicep)
            parameter_values: Optional dictionary of parameter values
            
        Returns:
            Tuple of (expanded template content, actual parameter values used)
        """
        template_type = self._determine_template_type(template_content)
        
        if template_type == "ARM":
            return self._expand_arm_template(template_content, parameter_values)
        elif template_type == "Bicep":
            return self._expand_bicep_template(template_content, parameter_values)
        else:
            return template_content, {}

    def _determine_template_type(self, content: str) -> str:
        """Determines if content is ARM template, ARM parameter file, or Bicep template"""
        content_lower = content.lower().strip()

        if '"$schema"' in content:
            if 'deploymenttemplate.json' in content_lower:
                return "ARM"
            elif 'deploymentparameters.json' in content_lower:
                return "ARM_PARAMETER"
        elif any(keyword in content_lower for keyword in ['param ', 'var ', 'resource ', 'module ']):
            return "Bicep"
        return "Unknown"

    def _expand_arm_template(self, template_content: str, parameter_values: Optional[Dict]) -> Tuple[str, Dict]:
        """Expands parameters in ARM template with enhanced reference resolution"""
        try:
            template = json.loads(template_content)
            params_used = {}

            # Reset reference depth for this template
            self.reference_depth = 0

            # First, collect all parameter definitions and their default values
            defined_params = template.get("parameters", {})
            for param_name, param_def in defined_params.items():
                if parameter_values and param_name in parameter_values:
                    params_used[param_name] = parameter_values[param_name]
                elif "defaultValue" in param_def:
                    params_used[param_name] = param_def["defaultValue"]
                else:
                    # Use placeholder for required parameters without values
                    params_used[param_name] = f"REQUIRED_PARAM_{param_name}"

            # Collect and cache variables for reference resolution
            variables = template.get("variables", {})
            for var_name, var_value in variables.items():
                self.variables_cache[var_name] = var_value

            # Handle embedded parameters in nested deployments
            if "resources" in template:
                for resource in template["resources"]:
                    if "properties" in resource and "parameters" in resource["properties"]:
                        nested_params = resource["properties"]["parameters"]
                        for param_name, param_info in nested_params.items():
                            if "value" in param_info:
                                params_used[param_name] = param_info["value"]

            # Now expand the template with multiple passes for complex references
            expanded_content = template_content

            # Pass 1: Expand parameters
            expanded_content = self._expand_arm_parameters(expanded_content, params_used)

            # Pass 2: Expand variables (may reference parameters)
            expanded_content = self._expand_arm_variables(expanded_content, variables, params_used)

            # Pass 3: Expand ARM template functions
            expanded_content = self._expand_arm_functions(expanded_content)

            # Pass 4: Handle nested template references and linked templates
            expanded_content = self._expand_nested_templates(expanded_content, template)

            # Pass 5: Final cleanup and validation
            expanded_content = self._cleanup_arm_template(expanded_content)

            return expanded_content, params_used

        except json.JSONDecodeError as e:
            logger.error(f"Invalid ARM template JSON: {e}")
            return template_content, {}
        except Exception as e:
            logger.error(f"Error expanding ARM template: {e}")
            return template_content, {}

    def _expand_arm_parameters(self, content: str, params_used: Dict) -> str:
        """Expand parameter references in ARM template"""
        # Handle parameter references with various formats
        patterns = [
            r'\[parameters\([\'"]([^\'"]+)[\'"]\)\]',  # Standard format
            r'\[parameters\(([^)]+)\)\]',              # Without quotes
        ]

        for pattern in patterns:
            for match in re.finditer(pattern, content):
                param_name = match.group(1).strip("'\"")
                if param_name in params_used:
                    param_value = params_used[param_name]
                    # Handle different value types
                    if isinstance(param_value, (dict, list)):
                        replacement = json.dumps(param_value)
                    elif isinstance(param_value, str):
                        replacement = f'"{param_value}"'
                    else:
                        replacement = str(param_value)
                    content = content.replace(match.group(0), replacement)
                else:
                    logger.warning(f"Parameter '{param_name}' not found in parameter values")

        return content

    def _expand_arm_variables(self, content: str, variables: Dict, params_used: Dict) -> str:
        """Expand variable references in ARM template"""
        # Handle variable references
        var_pattern = r'\[variables\([\'"]([^\'"]+)[\'"]\)\]'

        # Multiple passes to handle variables that reference other variables
        max_passes = 5
        for pass_num in range(max_passes):
            changed = False
            for match in re.finditer(var_pattern, content):
                var_name = match.group(1)
                if var_name in variables:
                    var_value = variables[var_name]

                    # If variable value contains references, try to resolve them
                    if isinstance(var_value, str) and '[' in var_value:
                        # Recursively expand variable value
                        var_value = self._expand_arm_parameters(var_value, params_used)
                        var_value = self._expand_arm_variables(var_value, variables, params_used)

                    # Handle different value types
                    if isinstance(var_value, (dict, list)):
                        replacement = json.dumps(var_value)
                    elif isinstance(var_value, str):
                        replacement = f'"{var_value}"'
                    else:
                        replacement = str(var_value)

                    new_content = content.replace(match.group(0), replacement)
                    if new_content != content:
                        changed = True
                        content = new_content
                        break  # Start over to handle nested references
                else:
                    logger.warning(f"Variable '{var_name}' not found in template variables")

            if not changed:
                break

        return content

    def _expand_arm_functions(self, content: str) -> str:
        """Expand ARM template functions with enhanced parsing"""
        # Handle nested function calls by processing from innermost to outermost
        max_iterations = 10
        iteration = 0

        while iteration < max_iterations:
            iteration += 1
            original_content = content

            # Find and expand ARM functions
            for func_name, func in self.resource_functions.items():
                # More sophisticated pattern to handle nested functions
                func_pattern = rf'\[{re.escape(func_name)}\(([^[\]]*(?:\[[^\]]*\][^[\]]*)*)\)\]'

                for match in re.finditer(func_pattern, content):
                    args_str = match.group(1)
                    try:
                        # Parse arguments more carefully
                        args = self._parse_function_arguments(args_str)
                        result = func(*args)

                        # Format result appropriately
                        if isinstance(result, (dict, list)):
                            result_str = json.dumps(result)
                        elif isinstance(result, str):
                            result_str = f'"{result}"'
                        else:
                            result_str = str(result)

                        content = content.replace(match.group(0), result_str)

                    except Exception as e:
                        logger.warning(f"Error expanding function {func_name} with args '{args_str}': {e}")
                        # Replace with placeholder to avoid infinite loops
                        content = content.replace(match.group(0), f'"FUNCTION_ERROR_{func_name}"')

            # If no changes were made, we're done
            if content == original_content:
                break

        return content

    def _parse_function_arguments(self, args_str: str) -> List[str]:
        """Parse function arguments handling nested quotes and brackets"""
        if not args_str.strip():
            return []

        args = []
        current_arg = ""
        bracket_depth = 0
        in_quotes = False
        quote_char = None

        i = 0
        while i < len(args_str):
            char = args_str[i]

            if char in ('"', "'") and (i == 0 or args_str[i-1] != '\\'):
                if not in_quotes:
                    in_quotes = True
                    quote_char = char
                elif char == quote_char:
                    in_quotes = False
                    quote_char = None
            elif char == '[' and not in_quotes:
                bracket_depth += 1
            elif char == ']' and not in_quotes:
                bracket_depth -= 1
            elif char == ',' and not in_quotes and bracket_depth == 0:
                args.append(current_arg.strip().strip("'\""))
                current_arg = ""
                i += 1
                continue

            current_arg += char
            i += 1

        if current_arg.strip():
            args.append(current_arg.strip().strip("'\""))

        return args

    def _expand_nested_templates(self, content: str, template: Dict) -> str:
        """Handle nested and linked template references"""
        try:
            # Look for nested deployments
            if "resources" in template:
                for resource in template["resources"]:
                    if (resource.get("type", "").lower() == "microsoft.resources/deployments" and
                        "properties" in resource):

                        properties = resource["properties"]

                        # Handle inline nested templates
                        if "template" in properties:
                            nested_template = properties["template"]
                            logger.info(f"Found inline nested template in resource: {resource.get('name', 'unnamed')}")

                        # Handle linked templates
                        elif "templateLink" in properties:
                            template_link = properties["templateLink"]
                            template_uri = template_link.get("uri", "")
                            logger.info(f"Found linked template reference: {template_uri}")

                            # For security analysis, we note the external reference
                            # but don't actually fetch it (security consideration)
                            placeholder = f'"LINKED_TEMPLATE_{template_uri}"'
                            content = content.replace(json.dumps(template_link), placeholder)

        except Exception as e:
            logger.warning(f"Error processing nested templates: {e}")

        return content

    def _cleanup_arm_template(self, content: str) -> str:
        """Final cleanup of expanded ARM template"""
        # Remove any remaining unresolved references that might cause issues
        # Replace with placeholders for analysis

        # Handle any remaining parameter references
        content = re.sub(r'\[parameters\([\'"]([^\'"]+)[\'"]\)\]',
                        r'"UNRESOLVED_PARAM_\1"', content)

        # Handle any remaining variable references
        content = re.sub(r'\[variables\([\'"]([^\'"]+)[\'"]\)\]',
                        r'"UNRESOLVED_VAR_\1"', content)

        # Handle any remaining function calls
        content = re.sub(r'\[(\w+)\([^]]*\)\]',
                        r'"UNRESOLVED_FUNC_\1"', content)

        return content

    def _expand_bicep_template(self, template_content: str, parameter_values: Optional[Dict]) -> Tuple[str, Dict]:
        """Expands parameters in Bicep template with enhanced module and reference handling"""
        params_used = {}
        variables_used = {}
        expanded_content = template_content

        # Reset reference depth for this template
        self.reference_depth = 0

        # First pass: collect parameter definitions and values
        expanded_content, params_used = self._expand_bicep_parameters(expanded_content, parameter_values)

        # Second pass: collect and expand variables
        expanded_content, variables_used = self._expand_bicep_variables(expanded_content, params_used)

        # Third pass: handle module references
        expanded_content = self._expand_bicep_modules(expanded_content, params_used, variables_used)

        # Fourth pass: handle resource references and outputs
        expanded_content = self._expand_bicep_references(expanded_content)

        # Fifth pass: handle string interpolation and expressions
        expanded_content = self._expand_bicep_interpolation(expanded_content, params_used, variables_used)

        # Final pass: cleanup
        expanded_content = self._cleanup_bicep_template(expanded_content)

        return expanded_content, params_used

    def _expand_bicep_parameters(self, content: str, parameter_values: Optional[Dict]) -> Tuple[str, Dict]:
        """Expand Bicep parameter definitions and references"""
        params_used = {}

        # Collect parameter definitions with enhanced parsing
        param_patterns = [
            r'param\s+(\w+)\s+([^=\n]+?)(?:\s*=\s*([^\n]+?))?(?:\s*@[^\n]*)?$',  # With decorators
            r'param\s+(\w+)\s+([^=\n]+)(?:\s*=\s*([^\n]+))?'  # Standard
        ]

        for pattern in param_patterns:
            for match in re.finditer(pattern, content, re.MULTILINE):
                param_name = match.group(1)
                param_type = match.group(2).strip()
                default_value = match.group(3).strip() if match.group(3) else None

                if parameter_values and param_name in parameter_values:
                    params_used[param_name] = parameter_values[param_name]
                elif default_value:
                    # Clean up default value and handle different types
                    default_value = default_value.strip("'\"")
                    # Try to parse as JSON for complex types
                    try:
                        if default_value.startswith('{') or default_value.startswith('['):
                            params_used[param_name] = json.loads(default_value)
                        else:
                            params_used[param_name] = default_value
                    except json.JSONDecodeError:
                        params_used[param_name] = default_value
                else:
                    params_used[param_name] = f"REQUIRED_PARAM_{param_name}"

        # Replace parameter references in content
        param_ref_patterns = [
            r'param\.(\w+)',  # Direct parameter reference
            r'(\w+):\s*param\.(\w+)',  # Property assignment
        ]

        for pattern in param_ref_patterns:
            for match in re.finditer(pattern, content):
                if len(match.groups()) == 1:
                    # Direct reference
                    param_name = match.group(1)
                    if param_name in params_used:
                        value = params_used[param_name]
                        if isinstance(value, str):
                            replacement = f"'{value}'"
                        else:
                            replacement = str(value)
                        content = content.replace(match.group(0), replacement)
                else:
                    # Property assignment
                    prop_name = match.group(1)
                    param_name = match.group(2)
                    if param_name in params_used:
                        value = params_used[param_name]
                        if isinstance(value, str):
                            replacement = f'{prop_name}: \'{value}\''
                        else:
                            replacement = f'{prop_name}: {value}'
                        content = content.replace(match.group(0), replacement)

        return content, params_used

    def _expand_bicep_variables(self, content: str, params_used: Dict) -> Tuple[str, Dict]:
        """Expand Bicep variable definitions and references"""
        variables_used = {}

        # Collect variable definitions
        var_pattern = r'var\s+(\w+)\s*=\s*([^\n]+)'
        for match in re.finditer(var_pattern, content):
            var_name = match.group(1)
            var_expression = match.group(2).strip()

            # Try to evaluate the variable expression
            try:
                # Replace parameter references in variable expression
                for param_name, param_value in params_used.items():
                    var_expression = var_expression.replace(f'param.{param_name}', str(param_value))

                # Store the variable value
                variables_used[var_name] = var_expression

            except Exception as e:
                logger.warning(f"Error evaluating variable '{var_name}': {e}")
                variables_used[var_name] = f"VARIABLE_ERROR_{var_name}"

        # Replace variable references in content
        var_ref_patterns = [
            r'var\.(\w+)',  # Direct variable reference
            r'(\w+):\s*var\.(\w+)',  # Property assignment
        ]

        for pattern in var_ref_patterns:
            for match in re.finditer(pattern, content):
                if len(match.groups()) == 1:
                    # Direct reference
                    var_name = match.group(1)
                    if var_name in variables_used:
                        value = variables_used[var_name]
                        content = content.replace(match.group(0), str(value))
                else:
                    # Property assignment
                    prop_name = match.group(1)
                    var_name = match.group(2)
                    if var_name in variables_used:
                        value = variables_used[var_name]
                        replacement = f'{prop_name}: {value}'
                        content = content.replace(match.group(0), replacement)

        return content, variables_used

    def _expand_bicep_modules(self, content: str, params_used: Dict, variables_used: Dict) -> str:
        """Handle Bicep module references and expansions"""
        # Find module declarations
        module_pattern = r'module\s+(\w+)\s+[\'"]([^\'"]+)[\'"]\s*=\s*\{([^}]+)\}'

        for match in re.finditer(module_pattern, content, re.DOTALL):
            module_name = match.group(1)
            module_path = match.group(2)
            module_params = match.group(3)

            logger.info(f"Found Bicep module reference: {module_name} -> {module_path}")

            # For security analysis, we note the module reference
            # In a full implementation, we would load and expand the module
            placeholder = f'// MODULE_REFERENCE: {module_name} from {module_path}\n// Parameters: {module_params.strip()}'
            content = content.replace(match.group(0), placeholder)

        return content

    def _expand_bicep_references(self, content: str) -> str:
        """Handle Bicep resource references and outputs"""
        # Handle resource references
        resource_ref_pattern = r'(\w+)\.(\w+)'

        for match in re.finditer(resource_ref_pattern, content):
            resource_name = match.group(1)
            property_name = match.group(2)

            # Common resource properties that might be security-relevant
            if property_name in ['id', 'name', 'properties', 'outputs']:
                placeholder = f'RESOURCE_REF_{resource_name}_{property_name}'
                content = content.replace(match.group(0), placeholder)

        return content

    def _expand_bicep_interpolation(self, content: str, params_used: Dict, variables_used: Dict) -> str:
        """Handle Bicep string interpolation and expressions"""
        # Handle string interpolation
        interp_pattern = r'\$\{([^}]+)\}'

        for match in re.finditer(interp_pattern, content):
            expression = match.group(1)
            original_expression = expression

            # Replace parameter references
            for param_name, value in params_used.items():
                expression = expression.replace(f"param.{param_name}", str(value))
                expression = expression.replace(f"parameters.{param_name}", str(value))

            # Replace variable references
            for var_name, value in variables_used.items():
                expression = expression.replace(f"var.{var_name}", str(value))

            # If expression was modified, replace it
            if expression != original_expression:
                content = content.replace(match.group(0), expression)

        return content

    def _cleanup_bicep_template(self, content: str) -> str:
        """Final cleanup of expanded Bicep template"""
        # Replace any remaining unresolved references

        # Handle remaining parameter references
        content = re.sub(r'param\.(\w+)', r'UNRESOLVED_PARAM_\1', content)

        # Handle remaining variable references
        content = re.sub(r'var\.(\w+)', r'UNRESOLVED_VAR_\1', content)

        # Handle remaining resource references
        content = re.sub(r'(\w+)\.(\w+)', r'UNRESOLVED_REF_\1_\2', content)

        return content

    def expand_template_file(self, template_path: str) -> Tuple[str, Dict]:
        """
        Expands parameters in a template file with enhanced cross-reference resolution.

        Args:
            template_path: Path to the template file

        Returns:
            Tuple of (expanded template content, parameter values used)
        """
        try:
            with open(template_path, 'r') as f:
                template_content = f.read()

            # Store template in cache for cross-reference resolution
            self.template_cache[template_path] = template_content

            # Try to find parameter file
            param_values = self._load_parameter_values(template_path)

            # Enhanced expansion with cross-template reference resolution
            expanded_content, params_used = self.expand_template_with_references(
                template_content, param_values, template_path
            )

            return expanded_content, params_used

        except Exception as e:
            logger.error(f"Error expanding template file {template_path}: {e}")
            return "", {}

    def expand_template_with_references(self, template_content: str, parameter_values: Optional[Dict] = None,
                                      template_path: str = None) -> Tuple[str, Dict]:
        """
        Enhanced template expansion with cross-template reference resolution.

        Args:
            template_content: The template content
            parameter_values: Optional parameter values
            template_path: Path to the template file for resolving relative references

        Returns:
            Tuple of (expanded template content, parameter values used)
        """
        # Prevent infinite recursion
        if self.reference_depth >= self.max_reference_depth:
            logger.warning(f"Maximum reference depth ({self.max_reference_depth}) reached")
            return template_content, parameter_values or {}

        self.reference_depth += 1

        try:
            # Determine template type
            template_type = self._determine_template_type(template_content)

            if template_type == "ARM":
                expanded_content, params_used = self._expand_arm_template_with_references(
                    template_content, parameter_values, template_path
                )
            elif template_type == "Bicep":
                expanded_content, params_used = self._expand_bicep_template_with_references(
                    template_content, parameter_values, template_path
                )
            else:
                expanded_content, params_used = template_content, parameter_values or {}

            # Resolve any remaining cross-template references
            expanded_content = self._resolve_cross_template_references(expanded_content, template_path)

            return expanded_content, params_used

        finally:
            self.reference_depth -= 1

    def _expand_arm_template_with_references(self, template_content: str, parameter_values: Optional[Dict],
                                           template_path: str = None) -> Tuple[str, Dict]:
        """ARM template expansion with cross-template reference resolution"""
        # First do standard ARM expansion
        expanded_content, params_used = self._expand_arm_template(template_content, parameter_values)

        # Then handle cross-template references
        if template_path:
            expanded_content = self._resolve_arm_linked_templates(expanded_content, template_path)
            expanded_content = self._resolve_arm_nested_deployments(expanded_content, template_path)

        return expanded_content, params_used

    def _expand_bicep_template_with_references(self, template_content: str, parameter_values: Optional[Dict],
                                             template_path: str = None) -> Tuple[str, Dict]:
        """Bicep template expansion with cross-template reference resolution"""
        # First do standard Bicep expansion
        expanded_content, params_used = self._expand_bicep_template(template_content, parameter_values)

        # Then handle cross-template references
        if template_path:
            expanded_content = self._resolve_bicep_modules(expanded_content, template_path)
            expanded_content = self._resolve_bicep_imports(expanded_content, template_path)

        return expanded_content, params_used

    def _resolve_arm_linked_templates(self, content: str, template_path: str) -> str:
        """Resolve ARM linked template references"""
        try:
            template = json.loads(content)
            template_dir = os.path.dirname(template_path)

            if "resources" in template:
                for resource in template["resources"]:
                    if (resource.get("type", "").lower() == "microsoft.resources/deployments" and
                        "properties" in resource and "templateLink" in resource["properties"]):

                        template_link = resource["properties"]["templateLink"]
                        uri = template_link.get("uri", "")

                        # Handle relative paths
                        if not uri.startswith(("http://", "https://", "/")):
                            linked_template_path = os.path.join(template_dir, uri)
                            if os.path.exists(linked_template_path):
                                logger.info(f"Resolving linked template: {linked_template_path}")

                                # Load and expand the linked template
                                try:
                                    with open(linked_template_path, 'r') as f:
                                        linked_content = f.read()

                                    # Get parameters for the linked template
                                    linked_params = resource["properties"].get("parameters", {})
                                    param_values = {}
                                    for param_name, param_def in linked_params.items():
                                        if "value" in param_def:
                                            param_values[param_name] = param_def["value"]

                                    # Recursively expand the linked template
                                    expanded_linked, _ = self.expand_template_with_references(
                                        linked_content, param_values, linked_template_path
                                    )

                                    # Replace the templateLink with a comment about the expanded content
                                    replacement = {
                                        "uri": f"EXPANDED_LINKED_TEMPLATE_{uri}",
                                        "expandedContent": "See expanded content in analysis"
                                    }
                                    resource["properties"]["templateLink"] = replacement

                                except Exception as e:
                                    logger.warning(f"Error expanding linked template {linked_template_path}: {e}")

            return json.dumps(template, indent=2)

        except json.JSONDecodeError:
            return content
        except Exception as e:
            logger.warning(f"Error resolving ARM linked templates: {e}")
            return content

    def _resolve_arm_nested_deployments(self, content: str, template_path: str) -> str:
        """Resolve ARM nested deployment references"""
        try:
            template = json.loads(content)

            if "resources" in template:
                for resource in template["resources"]:
                    if (resource.get("type", "").lower() == "microsoft.resources/deployments" and
                        "properties" in resource and "template" in resource["properties"]):

                        nested_template = resource["properties"]["template"]
                        nested_params = resource["properties"].get("parameters", {})

                        # Extract parameter values for nested template
                        param_values = {}
                        for param_name, param_def in nested_params.items():
                            if "value" in param_def:
                                param_values[param_name] = param_def["value"]

                        # Recursively expand the nested template
                        nested_content = json.dumps(nested_template)
                        expanded_nested, _ = self.expand_template_with_references(
                            nested_content, param_values, template_path
                        )

                        try:
                            resource["properties"]["template"] = json.loads(expanded_nested)
                        except json.JSONDecodeError:
                            # If expansion resulted in non-JSON, store as comment
                            resource["properties"]["expandedTemplate"] = expanded_nested

            return json.dumps(template, indent=2)

        except json.JSONDecodeError:
            return content
        except Exception as e:
            logger.warning(f"Error resolving ARM nested deployments: {e}")
            return content

    def _resolve_bicep_modules(self, content: str, template_path: str) -> str:
        """Resolve Bicep module references"""
        template_dir = os.path.dirname(template_path)

        # Find module declarations with file paths
        module_pattern = r'module\s+(\w+)\s+[\'"]([^\'"]+\.bicep)[\'"]\s*=\s*\{([^}]+)\}'

        for match in re.finditer(module_pattern, content, re.DOTALL):
            module_name = match.group(1)
            module_path = match.group(2)
            module_params_block = match.group(3)

            # Resolve relative module path
            if not os.path.isabs(module_path):
                full_module_path = os.path.join(template_dir, module_path)
            else:
                full_module_path = module_path

            if os.path.exists(full_module_path):
                logger.info(f"Resolving Bicep module: {module_name} -> {full_module_path}")

                try:
                    with open(full_module_path, 'r') as f:
                        module_content = f.read()

                    # Parse module parameters
                    param_values = self._parse_bicep_module_params(module_params_block)

                    # Recursively expand the module
                    expanded_module, _ = self.expand_template_with_references(
                        module_content, param_values, full_module_path
                    )

                    # Replace module declaration with expanded content comment
                    replacement = f'''// EXPANDED_MODULE: {module_name} from {module_path}
// Original parameters: {module_params_block.strip()}
// Expanded content available in analysis'''

                    content = content.replace(match.group(0), replacement)

                except Exception as e:
                    logger.warning(f"Error expanding Bicep module {full_module_path}: {e}")
            else:
                logger.warning(f"Bicep module file not found: {full_module_path}")

        return content

    def _resolve_bicep_imports(self, content: str, template_path: str) -> str:
        """Resolve Bicep import statements"""
        template_dir = os.path.dirname(template_path)

        # Find import statements
        import_pattern = r'import\s+[\'"]([^\'"]+)[\'"]\s+as\s+(\w+)'

        for match in re.finditer(import_pattern, content):
            import_path = match.group(1)
            import_alias = match.group(2)

            # Resolve relative import path
            if not os.path.isabs(import_path):
                full_import_path = os.path.join(template_dir, import_path)
            else:
                full_import_path = import_path

            if os.path.exists(full_import_path):
                logger.info(f"Found Bicep import: {import_alias} -> {full_import_path}")

                # Replace import with comment for analysis
                replacement = f'// IMPORT: {import_alias} from {import_path}'
                content = content.replace(match.group(0), replacement)
            else:
                logger.warning(f"Bicep import file not found: {full_import_path}")

        return content

    def _parse_bicep_module_params(self, params_block: str) -> Dict:
        """Parse Bicep module parameter block"""
        params = {}

        # Simple parameter parsing - could be enhanced for complex scenarios
        param_pattern = r'(\w+):\s*([^\n,}]+)'

        for match in re.finditer(param_pattern, params_block):
            param_name = match.group(1)
            param_value = match.group(2).strip()

            # Clean up the value
            param_value = param_value.rstrip(',').strip("'\"")
            params[param_name] = param_value

        return params

    def _resolve_cross_template_references(self, content: str, template_path: str = None) -> str:
        """Resolve any remaining cross-template references"""
        if not template_path:
            return content

        template_dir = os.path.dirname(template_path)

        # Look for file references in comments or strings that might indicate dependencies
        file_ref_pattern = r'[\'"]([^\'"\s]+\.(json|bicep|bicepparam))[\'"]'

        for match in re.finditer(file_ref_pattern, content):
            referenced_file = match.group(1)

            # Check if it's a relative path that exists
            if not os.path.isabs(referenced_file):
                full_ref_path = os.path.join(template_dir, referenced_file)
                if os.path.exists(full_ref_path):
                    logger.info(f"Found potential template reference: {full_ref_path}")
                    # Note the reference for security analysis
                    content = content.replace(
                        match.group(0),
                        f'"TEMPLATE_REFERENCE_{referenced_file}"'
                    )

        return content

    def _get_pattern_config(self) -> Dict[str, List[str]]:
        """Loads and parses pattern configuration from environment variables"""
        # Load patterns from env vars with default patterns as fallback
        template_patterns = os.getenv('TEMPLATE_PATTERNS', '*Template*,*template*,main.json,*.json').split(',')
        param_patterns = os.getenv('PARAMETER_PATTERNS', '*Param*,*Parameter*,*params*,*parameters*').split(',')
        template_identifiers = os.getenv('TEMPLATE_IDENTIFIERS', 'Template,template,main,deploy,ArmTemplate').split(',')
        param_identifiers = os.getenv('PARAMETER_IDENTIFIERS', 'Param,Parameter,params,parameters,ArmParam').split(',')

        return {
            'template_patterns': [p.strip() for p in template_patterns if p.strip()],
            'param_patterns': [p.strip() for p in param_patterns if p.strip()],
            'template_identifiers': [i.strip() for i in template_identifiers if i.strip()],
            'param_identifiers': [i.strip() for i in param_identifiers if i.strip()]
        }

    def _find_matching_parameter_files(self, template_path: str) -> List[str]:
        """Find parameter files that match the given template path using configured patterns"""
        base_path = os.path.splitext(template_path)[0]
        template_dir = os.path.dirname(template_path)
        template_name = os.path.basename(template_path)
        param_files = []
        patterns = self._get_pattern_config()

        logger.debug(f"Looking for parameter files for template: {template_path}")
        logger.debug(f"Using patterns: {patterns}")

        # Search in the same directory and parent directory
        search_dirs = [template_dir]
        parent_dir = os.path.dirname(template_dir)
        if parent_dir and parent_dir != template_dir:
            search_dirs.append(parent_dir)

        for directory in search_dirs:
            # List all files in directory
            try:
                files = os.listdir(directory)
            except Exception as e:
                logger.warning(f"Error listing directory {directory}: {e}")
                continue

            for filename in files:
                file_path = os.path.join(directory, filename)
                if not os.path.isfile(file_path):
                    continue

                # Skip the template file itself
                if file_path == template_path:
                    continue

                # Check if file matches any parameter patterns
                is_param_file = any(fnmatch.fnmatch(filename, pattern) for pattern in patterns['param_patterns'])

                # Check for identifier-based matching
                if not is_param_file:
                    for identifier in patterns['param_identifiers']:
                        if identifier in filename:
                            is_param_file = True
                            break

                if is_param_file:
                    # First, check for identifier-based file pairs
                    template_base = Path(template_name).stem
                    param_base = Path(filename).stem
                    identifier_matched = False

                    for t_id in patterns['template_identifiers']:
                        for p_id in patterns['param_identifiers']:
                            if (t_id in template_base and p_id in param_base and
                                template_base.replace(t_id, '') == param_base.replace(p_id, '')):
                                param_files.append(file_path)
                                identifier_matched = True
                                logger.debug(f"Matched parameter file by identifier: {file_path} (template_id: {t_id}, param_id: {p_id})")
                                break
                        if identifier_matched:
                            break

                    # If no identifier match but it's a parameter file, check for base name matching
                    if not identifier_matched:
                        template_base_clean = Path(template_name).stem
                        param_base_clean = Path(filename).stem

                        # Define suffix patterns for matching
                        # Template suffixes (what to remove from template names)
                        template_suffixes = ['deploymentTemplate', 'Template', 'template', 'main', 'deploy']
                        # Parameter suffixes (what to remove from parameter names)
                        param_suffixes = ['deploymentParameters', 'Parameters', 'parameters', 'params', 'param', 'Parameter', 'Param']

                        # Clean template file base name by removing template suffixes
                        template_base_root = template_base_clean
                        for suffix in template_suffixes:
                            if template_base_clean.endswith(f'.{suffix}'):
                                template_base_root = template_base_clean[:-len(f'.{suffix}')]
                                break
                            elif template_base_clean.endswith(suffix):
                                template_base_root = template_base_clean[:-len(suffix)]
                                break

                        # Clean parameter file base name by removing parameter suffixes
                        param_base_root = param_base_clean
                        for suffix in param_suffixes:
                            if param_base_clean.endswith(f'.{suffix}'):
                                param_base_root = param_base_clean[:-len(f'.{suffix}')]
                                break
                            elif param_base_clean.endswith(suffix):
                                param_base_root = param_base_clean[:-len(suffix)]
                                break

                        # Check if root base names match
                        # Example: Grafana (from Grafana.deploymentTemplate) == Grafana (from Grafana.deploymentParameters)
                        if template_base_root == param_base_root and template_base_root != template_base_clean:
                            param_files.append(file_path)
                            logger.debug(f"Matched parameter file by base name: {file_path} (template_root: {template_base_root}, param_root: {param_base_root})")
                        # Also match if roots are equal even if no suffix was removed (exact base match)
                        elif template_base_root == param_base_root and len(template_base_root) > 3:
                            param_files.append(file_path)
                            logger.debug(f"Matched parameter file by exact base: {file_path} (template_root: {template_base_root}, param_root: {param_base_root})")
                        # Fallback: check if parameter base starts with template base (for partial matches)
                        elif param_base_root.startswith(template_base_root) and len(template_base_root) > 3:
                            param_files.append(file_path)
                            logger.debug(f"Matched parameter file by prefix: {file_path} (template_root: {template_base_root}, param_root: {param_base_root})")

        # Add default parameter file patterns as fallback
        template_base = Path(template_path).stem
        template_dir = os.path.dirname(template_path)

        # Generate default patterns based on template naming conventions
        default_patterns = [
            f"{base_path}.parameters.json",
            f"{base_path}.params.json",
            f"{base_path}.bicepparam",
            template_path.replace('.json', '.parameters.json'),
            template_path.replace('.bicep', '.bicepparam'),
        ]

        # Handle specific suffix replacement patterns
        suffix_replacements = [
            ('deploymentTemplate.json', 'deploymentParameters.json'),
            ('Template.json', 'Parameters.json'),
            ('template.json', 'parameters.json'),
            ('.template.json', '.parameters.json'),
            ('Template.json', 'Parameter.json'),
            ('deploy.json', 'params.json'),
            ('main.json', 'parameters.json'),
        ]

        for old_suffix, new_suffix in suffix_replacements:
            if template_path.endswith(old_suffix):
                param_pattern = template_path.replace(old_suffix, new_suffix)
                if param_pattern not in default_patterns:
                    default_patterns.append(param_pattern)

        # Additional patterns for common naming conventions
        additional_patterns = [
            os.path.join(template_dir, f"{template_base}.parameters.json"),
            os.path.join(template_dir, f"{template_base}.params.json"),
            os.path.join(template_dir, f"{template_base}.bicepparam"),
            os.path.join(template_dir, f"{template_base}.parameter.json"),
            os.path.join(template_dir, f"{template_base}.param.json"),
            os.path.join(template_dir, f"{template_base}.deploymentParameters.json"),
            os.path.join(template_dir, f"{template_base}.Parameters.json"),
        ]

        default_patterns.extend(additional_patterns)
        
        for pattern in default_patterns:
            if os.path.exists(pattern) and pattern not in param_files and pattern != template_path:
                param_files.append(pattern)
                logger.debug(f"Added parameter file from default pattern: {pattern}")

        logger.debug(f"Found {len(param_files)} parameter files for {template_path}: {param_files}")
        return param_files

    def _check_sensitive_parameters(self, param_file: str, content: str) -> None:
        """Checks for sensitive parameters that should use secureString"""
        try:
            data = json.loads(content)
            params = data.get("parameters", {})
            
            for param_name, param_def in params.items():
                param_name_lower = param_name.lower()
                # Check if parameter name contains sensitive keywords
                if any(keyword in param_name_lower for keyword in SENSITIVE_PARAMETER_KEYWORDS):
                    # Check if it's using secureString
                    param_type = param_def.get("type", "").lower()
                    if param_type != "securestring":
                        finding = {
                            "file_path": param_file,
                            "severity": "HIGH",
                            "control_id": "DP-3",
                            "description": f"Sensitive parameter '{param_name}' should use secureString type",
                            "remediation": f"Change the type of parameter '{param_name}' to 'secureString' to protect sensitive information",
                            "line": self._find_parameter_line(content, param_name),
                            "source": "parameter_check"
                        }
                        self.security_findings.append(finding)
                        logger.warning(f"Security issue in {param_file}: Parameter '{param_name}' contains sensitive keyword but uses type '{param_type}'")

        except json.JSONDecodeError:
            logger.warning(f"Could not parse parameter file {param_file} as JSON")

    def _find_parameter_line(self, content: str, param_name: str) -> int:
        """Find the line number where a parameter is defined"""
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            if f'"{param_name}"' in line and '"parameters"' not in line:
                return i
        return 1  # Return first line if not found

    def _load_parameter_values(self, template_path: str) -> Optional[Dict]:
        """Loads parameter values from associated parameter file"""
        param_files = self._find_matching_parameter_files(template_path)
        
        for param_file in param_files:
            try:
                with open(param_file, 'r') as f:
                    content = f.read()
                if param_file.endswith('.json'):
                    params = json.loads(content)
                    # Check for sensitive parameters
                    self._check_sensitive_parameters(param_file, content)
                    # Handle ARM parameter file format
                    if "parameters" in params:
                        logger.info(f"Found matching parameter file: {param_file}")
                        return {k: v.get("value") for k, v in params["parameters"].items()}
                    return params
                else:
                    # Handle Bicep parameter file
                    logger.info(f"Found matching Bicep parameter file: {param_file}")
                    return self._parse_bicep_params(content)
            except Exception as e:
                logger.warning(f"Error loading parameter file {param_file}: {e}")

        logger.info("No matching parameter file found")
        return None

    def get_security_findings(self) -> List[Dict]:
        """Returns any security findings from parameter analysis"""
        return self.security_findings

    def _parse_bicep_params(self, content: str) -> Dict:
        """Parses Bicep parameter file content"""
        params = {}
        # Basic parsing - could be enhanced for more complex Bicep param files
        param_pattern = r'param\s+(\w+)\s*=\s*([^\n]+)'
        for match in re.finditer(param_pattern, content):
            param_name = match.group(1)
            param_value = match.group(2).strip("'\" \t\n")
            params[param_name] = param_value
        return params
