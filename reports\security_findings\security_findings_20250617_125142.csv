Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P1-Identity-HIGH,Identity Management,HIGH,IM-2,template.json,181,App Service config does not enforce or reference Multi-Factor Authentication (MFA) for users or administrators.,Enforce MFA for all users and administrators accessing the App Service through Azure AD Conditional Access policies.,N/A,AI,Generic
P1-Identity-MEDIUM,Identity Management,MEDIUM,IM-3,template.json,181,App Service config does not reference use of Privileged Identity Management (PIM) for privileged access.,Implement Azure AD Privileged Identity Management (PIM) for managing privileged access to the App Service.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,181,"App Service config allows public network access (publicNetworkAccess: 'Enabled') and has ipSecurityRestrictions allowing 'Any' IP, exposing the app to the public internet.",Restrict publicNetworkAccess to 'Disabled' or configure ipSecurityRestrictions to allow only trusted IP ranges.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,192,"App Service config allows public SCM endpoint access (scmIpSecurityRestrictions allows 'Any' IP), exposing the SCM endpoint to the public internet.",Restrict scmIpSecurityRestrictions to allow only trusted IP ranges and avoid 'Any' to minimize public exposure.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,181,"App Service config does not use private endpoints (publicNetworkAccess: 'Enabled'), increasing risk of unauthorized public access.",Configure a private endpoint for the App Service and set publicNetworkAccess to 'Disabled' to restrict access to internal networks only.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,181,App Service config does not specify use of customer-managed keys or explicit encryption at rest settings.,"Enable encryption at rest for all data storage and, if required, configure customer-managed keys for the App Service.",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,54,"App Service hostNameSslStates: SSL is disabled for 'onefuzz-daily-ui.azurewebsites.net' and 'onefuzz-daily-ui.scm.azurewebsites.net', violating requirement for encryption in transit (TLS 1.2+).",Enable SSL (SniEnabled or IpBasedEnabled) for all hostNameSslStates entries to ensure all endpoints use TLS 1.2 or higher.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,181,"App Service config includes a 'publishingUsername' property, which may expose sensitive information if not securely managed.",Store sensitive publishing credentials in Azure Key Vault and reference them securely in the template.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-6,template.json,181,App Service config does not specify use of customer-managed keys (CMK) for encryption.,Configure the App Service to use customer-managed keys for data encryption if handling sensitive or regulated data.,N/A,AI,Generic
P6-Other-MEDIUM,Unknown,MEDIUM,IM-12,template.json,181,App Service config does not reference use of Conditional Access Policies.,Implement Azure AD Conditional Access Policies to enforce secure access to the App Service.,N/A,AI,Generic
