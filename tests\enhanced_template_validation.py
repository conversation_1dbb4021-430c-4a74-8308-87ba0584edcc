#!/usr/bin/env python3
"""
IaC Guardian Enhanced Template Validation and Testing System
Ensures enhanced templates maintain backward compatibility and produce consistent results
"""

import json
import logging
import unittest
import tempfile
import hashlib
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import sys
import os

# Add project root to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

@dataclass
class ValidationResult:
    """Represents the result of a template validation test"""
    test_name: str
    passed: bool
    message: str
    details: Dict[str, Any]
    execution_time: float

class EnhancedTemplateValidator:
    """
    Comprehensive validation system for enhanced security analyst templates
    """
    
    def __init__(self, templates_dir: Path = None):
        self.templates_dir = templates_dir or Path(__file__).parent.parent / 'templates'
        self.logger = logging.getLogger(__name__)
        self.validation_results = []
        
    def validate_template_structure(self) -> ValidationResult:
        """Validate that all required template files exist and have correct structure"""
        start_time = datetime.now()
        
        required_files = [
            'prompts/system/security_analyst_role.txt',
            'prompts/security/main_analysis_prompt.txt',
            'prompts/context/template_context_builder.txt',
            'output/enhanced_analysis_output_schema.json'
        ]
        
        missing_files = []
        invalid_files = []
        
        for file_path in required_files:
            full_path = self.templates_dir / file_path
            if not full_path.exists():
                missing_files.append(file_path)
            else:
                # Validate file content
                try:
                    content = full_path.read_text(encoding='utf-8')
                    if len(content.strip()) == 0:
                        invalid_files.append(f"{file_path} (empty)")
                    elif file_path.endswith('.json'):
                        json.loads(content)  # Validate JSON syntax
                except Exception as e:
                    invalid_files.append(f"{file_path} ({str(e)})")
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        if missing_files or invalid_files:
            return ValidationResult(
                test_name="template_structure",
                passed=False,
                message=f"Template structure validation failed",
                details={
                    "missing_files": missing_files,
                    "invalid_files": invalid_files
                },
                execution_time=execution_time
            )
        
        return ValidationResult(
            test_name="template_structure",
            passed=True,
            message="All required template files exist and are valid",
            details={"validated_files": required_files},
            execution_time=execution_time
        )
    
    def validate_enhanced_features(self) -> ValidationResult:
        """Validate that enhanced features are properly implemented"""
        start_time = datetime.now()
        
        # Check security analyst role template for enhanced features
        role_template_path = self.templates_dir / 'prompts/system/security_analyst_role.txt'
        role_content = role_template_path.read_text(encoding='utf-8')
        
        required_enhancements = [
            'MITRE ATT&CK',
            'Container Security (CS-*)',
            'API Security (AS-*)',
            'DevOps Security (DS-*)',
            'confidence_score',
            'exploitation_complexity',
            'remediation_effort_hours',
            'AI-Enhanced',
            'False Positive Prevention'
        ]
        
        missing_enhancements = []
        for enhancement in required_enhancements:
            if enhancement not in role_content:
                missing_enhancements.append(enhancement)
        
        # Check main analysis prompt for enhanced steps
        analysis_prompt_path = self.templates_dir / 'prompts/security/main_analysis_prompt.txt'
        analysis_content = analysis_prompt_path.read_text(encoding='utf-8')
        
        required_analysis_features = [
            'MITRE ATT&CK',
            'AI-ENHANCED CONTEXT',
            'COMPREHENSIVE MULTI-FRAMEWORK',
            'confidence_score',
            'mitre_attack_techniques',
            'compliance_frameworks'
        ]
        
        missing_analysis_features = []
        for feature in required_analysis_features:
            if feature not in analysis_content:
                missing_analysis_features.append(feature)
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        if missing_enhancements or missing_analysis_features:
            return ValidationResult(
                test_name="enhanced_features",
                passed=False,
                message="Enhanced features validation failed",
                details={
                    "missing_role_enhancements": missing_enhancements,
                    "missing_analysis_features": missing_analysis_features
                },
                execution_time=execution_time
            )
        
        return ValidationResult(
            test_name="enhanced_features",
            passed=True,
            message="All enhanced features are properly implemented",
            details={
                "validated_enhancements": required_enhancements,
                "validated_analysis_features": required_analysis_features
            },
            execution_time=execution_time
        )
    
    def validate_output_schema(self) -> ValidationResult:
        """Validate the enhanced output schema"""
        start_time = datetime.now()
        
        schema_path = self.templates_dir / 'output/enhanced_analysis_output_schema.json'
        
        try:
            with open(schema_path, 'r', encoding='utf-8') as f:
                schema = json.load(f)
            
            # Validate required schema components
            required_properties = [
                'findings',
                'analysis_metadata',
                'executive_summary'
            ]
            
            missing_properties = []
            for prop in required_properties:
                if prop not in schema.get('properties', {}):
                    missing_properties.append(prop)
            
            # Validate findings schema has enhanced fields
            findings_schema = schema.get('properties', {}).get('findings', {})
            findings_items = findings_schema.get('items', {}).get('properties', {})
            
            required_finding_fields = [
                'confidence_score',
                'exploitation_complexity',
                'remediation_effort_hours',
                'mitre_attack_techniques',
                'compliance_frameworks',
                'azure_policy_definitions'
            ]
            
            missing_finding_fields = []
            for field in required_finding_fields:
                if field not in findings_items:
                    missing_finding_fields.append(field)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            if missing_properties or missing_finding_fields:
                return ValidationResult(
                    test_name="output_schema",
                    passed=False,
                    message="Output schema validation failed",
                    details={
                        "missing_properties": missing_properties,
                        "missing_finding_fields": missing_finding_fields
                    },
                    execution_time=execution_time
                )
            
            return ValidationResult(
                test_name="output_schema",
                passed=True,
                message="Output schema is valid and complete",
                details={
                    "schema_version": schema.get('title', 'Unknown'),
                    "validated_properties": required_properties,
                    "validated_finding_fields": required_finding_fields
                },
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            return ValidationResult(
                test_name="output_schema",
                passed=False,
                message=f"Schema validation error: {str(e)}",
                details={"error": str(e)},
                execution_time=execution_time
            )
    
    def validate_backward_compatibility(self) -> ValidationResult:
        """Validate that enhanced templates maintain backward compatibility"""
        start_time = datetime.now()
        
        # Check that original required fields are still present
        role_template_path = self.templates_dir / 'prompts/system/security_analyst_role.txt'
        role_content = role_template_path.read_text(encoding='utf-8')
        
        legacy_requirements = [
            'Azure Security Benchmark',
            'control_id',
            'severity',
            'line',
            'description',
            'remediation',
            'JSON Response Format'
        ]
        
        missing_legacy_features = []
        for requirement in legacy_requirements:
            if requirement not in role_content:
                missing_legacy_features.append(requirement)
        
        # Check analysis prompt maintains core structure
        analysis_prompt_path = self.templates_dir / 'prompts/security/main_analysis_prompt.txt'
        analysis_content = analysis_prompt_path.read_text(encoding='utf-8')
        
        legacy_analysis_features = [
            'STEP 1:',
            'STEP 2:',
            'control_id',
            'findings',
            'CONTROL ID VALIDATION'
        ]
        
        missing_legacy_analysis = []
        for feature in legacy_analysis_features:
            if feature not in analysis_content:
                missing_legacy_analysis.append(feature)
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        if missing_legacy_features or missing_legacy_analysis:
            return ValidationResult(
                test_name="backward_compatibility",
                passed=False,
                message="Backward compatibility validation failed",
                details={
                    "missing_legacy_features": missing_legacy_features,
                    "missing_legacy_analysis": missing_legacy_analysis
                },
                execution_time=execution_time
            )
        
        return ValidationResult(
            test_name="backward_compatibility",
            passed=True,
            message="Backward compatibility maintained",
            details={
                "validated_legacy_features": legacy_requirements,
                "validated_legacy_analysis": legacy_analysis_features
            },
            execution_time=execution_time
        )
    
    def validate_consistency_requirements(self) -> ValidationResult:
        """Validate that templates support consistent analysis results"""
        start_time = datetime.now()
        
        # Check for deterministic analysis requirements
        analysis_prompt_path = self.templates_dir / 'prompts/security/main_analysis_prompt.txt'
        analysis_content = analysis_prompt_path.read_text(encoding='utf-8')
        
        consistency_requirements = [
            'Same template + same controls = identical findings',
            'deterministic',
            'consistent',
            'exact line number',
            'CONSISTENCY REQUIREMENTS'
        ]
        
        missing_consistency = []
        for requirement in consistency_requirements:
            if requirement not in analysis_content:
                missing_consistency.append(requirement)
        
        # Check for false positive prevention measures
        fp_prevention_features = [
            'FALSE POSITIVE PREVENTION',
            'context analysis',
            'semantic analysis',
            'confidence',
            'deployment worthiness'
        ]
        
        missing_fp_prevention = []
        for feature in fp_prevention_features:
            if feature.lower() not in analysis_content.lower():
                missing_fp_prevention.append(feature)
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        if missing_consistency or missing_fp_prevention:
            return ValidationResult(
                test_name="consistency_requirements",
                passed=False,
                message="Consistency requirements validation failed",
                details={
                    "missing_consistency": missing_consistency,
                    "missing_fp_prevention": missing_fp_prevention
                },
                execution_time=execution_time
            )
        
        return ValidationResult(
            test_name="consistency_requirements",
            passed=True,
            message="Consistency requirements are properly implemented",
            details={
                "validated_consistency": consistency_requirements,
                "validated_fp_prevention": fp_prevention_features
            },
            execution_time=execution_time
        )
    
    def run_comprehensive_validation(self) -> Dict[str, Any]:
        """Run all validation tests and return comprehensive results"""
        self.logger.info("Starting comprehensive template validation...")
        
        validation_tests = [
            self.validate_template_structure,
            self.validate_enhanced_features,
            self.validate_output_schema,
            self.validate_backward_compatibility,
            self.validate_consistency_requirements
        ]
        
        results = []
        total_start_time = datetime.now()
        
        for test_func in validation_tests:
            try:
                result = test_func()
                results.append(result)
                self.validation_results.append(result)
                
                status = "PASS" if result.passed else "FAIL"
                self.logger.info(f"{status}: {result.test_name} - {result.message}")
                
            except Exception as e:
                error_result = ValidationResult(
                    test_name=test_func.__name__,
                    passed=False,
                    message=f"Test execution error: {str(e)}",
                    details={"error": str(e)},
                    execution_time=0
                )
                results.append(error_result)
                self.validation_results.append(error_result)
                self.logger.error(f"FAIL: {test_func.__name__} - {str(e)}")
        
        total_execution_time = (datetime.now() - total_start_time).total_seconds()
        
        # Calculate summary statistics
        passed_tests = sum(1 for r in results if r.passed)
        total_tests = len(results)
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        summary = {
            "validation_timestamp": datetime.now().isoformat(),
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "success_rate": success_rate,
            "total_execution_time": total_execution_time,
            "overall_status": "PASS" if passed_tests == total_tests else "FAIL",
            "test_results": [
                {
                    "test_name": r.test_name,
                    "passed": r.passed,
                    "message": r.message,
                    "execution_time": r.execution_time,
                    "details": r.details
                }
                for r in results
            ]
        }
        
        self.logger.info(f"Validation complete: {passed_tests}/{total_tests} tests passed ({success_rate:.1f}%)")
        
        return summary
    
    def save_validation_report(self, results: Dict[str, Any], output_path: Path = None) -> Path:
        """Save validation results to a detailed report"""
        if output_path is None:
            output_path = Path(__file__).parent / f"validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Validation report saved to {output_path}")
        return output_path

class TestEnhancedTemplates(unittest.TestCase):
    """Unit tests for enhanced template validation"""
    
    def setUp(self):
        self.validator = EnhancedTemplateValidator()
    
    def test_template_structure(self):
        """Test that all required template files exist"""
        result = self.validator.validate_template_structure()
        self.assertTrue(result.passed, f"Template structure validation failed: {result.message}")
    
    def test_enhanced_features(self):
        """Test that enhanced features are implemented"""
        result = self.validator.validate_enhanced_features()
        self.assertTrue(result.passed, f"Enhanced features validation failed: {result.message}")
    
    def test_output_schema(self):
        """Test that output schema is valid"""
        result = self.validator.validate_output_schema()
        self.assertTrue(result.passed, f"Output schema validation failed: {result.message}")
    
    def test_backward_compatibility(self):
        """Test that backward compatibility is maintained"""
        result = self.validator.validate_backward_compatibility()
        self.assertTrue(result.passed, f"Backward compatibility validation failed: {result.message}")
    
    def test_consistency_requirements(self):
        """Test that consistency requirements are implemented"""
        result = self.validator.validate_consistency_requirements()
        self.assertTrue(result.passed, f"Consistency requirements validation failed: {result.message}")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # Run comprehensive validation
    validator = EnhancedTemplateValidator()
    results = validator.run_comprehensive_validation()
    report_path = validator.save_validation_report(results)
    
    print(f"\nValidation Summary:")
    print(f"Tests Passed: {results['passed_tests']}/{results['total_tests']}")
    print(f"Success Rate: {results['success_rate']:.1f}%")
    print(f"Overall Status: {results['overall_status']}")
    print(f"Report saved to: {report_path}")
    
    # Run unit tests
    print("\nRunning unit tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
