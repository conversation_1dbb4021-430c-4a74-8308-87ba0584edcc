#!/usr/bin/env python3
"""
Test script for Enhanced Resource Control Mappings V2
"""

import json
import logging
from pathlib import Path
from src.core.enhanced_resource_control_mappings_v2 import EnhancedResourceControlMapperV2

def test_enhanced_features():
    """Test the enhanced features of the V2 mapper."""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    print("🧪 Testing Enhanced Resource Control Mappings V2")
    print("=" * 60)
    
    # Initialize mapper
    print("\n1️⃣ Initializing Enhanced Mapper V2...")
    mapper = EnhancedResourceControlMapperV2()
    
    # Test 1: Control Priority and Severity
    print("\n2️⃣ Testing Control Priority and Severity Mapping...")
    test_controls = ["IM-1", "NS-1", "DP-1"]
    for control_id in test_controls:
        if control_id in mapper.control_database:
            control = mapper.control_database[control_id]
            severity = mapper.get_control_severity(control_id)
            print(f"\n  Control: {control_id}")
            print(f"    - Priority: {control.get('priority', 'N/A')}")
            print(f"    - Severity: {severity['severity']}")
            print(f"    - Impact Score: {severity['impact_score']}")
            print(f"    - Automation Possible: {control.get('automation_possible', False)}")
            print(f"    - Monitoring Required: {control.get('monitoring_required', False)}")
    
    # Test 2: Control Relationships
    print("\n3️⃣ Testing Control Relationships...")
    for control_id in test_controls[:1]:  # Test first control
        related = mapper.get_related_controls(control_id)
        print(f"\n  Control {control_id} relationships:")
        if related:
            for rel in related[:3]:  # Show first 3 relationships
                print(f"    - Related to: {rel['control_id']}")
                print(f"      Type: {rel['relationship_type']}")
                print(f"      Common: {', '.join(rel['common_elements'])}")
        else:
            print("    - No related controls found")
    
    # Test 3: Compliance Framework Mapping
    print("\n4️⃣ Testing Compliance Framework Mapping...")
    compliance_summary = {}
    for control_id, frameworks in mapper.compliance_frameworks.items():
        for framework in frameworks:
            if framework not in compliance_summary:
                compliance_summary[framework] = 0
            compliance_summary[framework] += 1
    
    print("\n  Compliance Framework Coverage:")
    for framework, count in sorted(compliance_summary.items()):
        print(f"    - {framework}: {count} controls")
    
    # Test 4: Risk Assessment for Resources
    print("\n5️⃣ Testing Risk Assessment...")
    test_resources = [
        "Microsoft.Storage/storageAccounts",
        "Microsoft.KeyVault/vaults", 
        "Microsoft.Sql/servers",
        "Microsoft.Network/virtualNetworks",
        "Microsoft.ContainerService/managedClusters"
    ]
    
    risk_summary = {"HIGH": 0, "MEDIUM": 0, "LOW": 0}
    for resource_type in test_resources:
        category = mapper._determine_resource_category(resource_type)
        if category and category in mapper.resource_mappings:
            risk_level = mapper.resource_mappings[category].get('risk_level', 'UNKNOWN')
            vulnerabilities = mapper.resource_mappings[category].get('common_vulnerabilities', [])
            
            print(f"\n  {resource_type}:")
            print(f"    - Category: {category}")
            print(f"    - Risk Level: {risk_level}")
            print(f"    - Common Vulnerabilities: {', '.join(vulnerabilities[:3])}")
            
            if risk_level in risk_summary:
                risk_summary[risk_level] += 1
    
    print(f"\n  Risk Summary:")
    for risk, count in risk_summary.items():
        print(f"    - {risk}: {count} resources")
    
    # Test 5: Security Posture Report
    print("\n6️⃣ Testing Security Posture Report Generation...")
    posture_report = mapper.generate_security_posture_report(test_resources)
    
    print(f"\n  Security Posture Summary:")
    print(f"    - Analysis Time: {posture_report['timestamp']}")
    print(f"    - Resources Analyzed: {posture_report['total_resources']}")
    print(f"    - Total Controls Applied: {posture_report['total_controls']}")
    
    if posture_report['priority_actions']:
        print(f"\n  🚨 Priority Actions Required:")
        for action in posture_report['priority_actions']:
            print(f"    - {action['resource_type']}")
            print(f"      Risk: {action['risk_level']}, High Priority Controls: {action['high_priority_controls']}")
    
    # Test 6: URL Link Extraction Enhancement
    print("\n7️⃣ Testing Enhanced URL Extraction...")
    controls_with_urls = 0
    total_urls = 0
    aka_ms_links = 0
    
    for control_id, links in mapper.url_links_database.items():
        if links['raw_links']:
            controls_with_urls += 1
            total_urls += len(links['raw_links'])
            aka_ms_links += sum(1 for url in links['raw_links'] if 'aka.ms' in url)
    
    print(f"\n  URL Statistics:")
    print(f"    - Controls with URLs: {controls_with_urls}")
    print(f"    - Total URLs extracted: {total_urls}")
    print(f"    - Microsoft short links (aka.ms): {aka_ms_links}")
    
    # Test 7: New Resource Categories
    print("\n8️⃣ Testing Extended Resource Categories...")
    new_categories = ["DataFactory", "MachineLearning", "Synapse", "IoTHub", "Purview", "Defender"]
    
    print(f"\n  New Categories Added:")
    for category in new_categories:
        if category in mapper.resource_mappings:
            mapping = mapper.resource_mappings[category]
            print(f"    - {category}:")
            print(f"      Controls: {len(mapping['applicable_controls'])}")
            print(f"      Risk Level: {mapping.get('risk_level', 'N/A')}")
            print(f"      Focus Areas: {', '.join(mapping['focus_areas'][:3])}")
    
    # Test 8: Export Enhanced Mappings
    print("\n9️⃣ Testing Export Functionality...")
    export_path = "test_enhanced_mappings_v2.json"
    if mapper.export_enhanced_mappings(export_path):
        print(f"  ✅ Successfully exported to {export_path}")
        
        # Verify exported file
        with open(export_path, 'r') as f:
            exported_data = json.load(f)
            
        print(f"\n  Exported Data Summary:")
        print(f"    - Version: {exported_data['metadata']['version']}")
        print(f"    - Features: {len(exported_data['metadata']['features'])} features")
        print(f"    - Control Database: {len(exported_data['control_database'])} controls")
        print(f"    - Resource Mappings: {len(exported_data['resource_mappings'])} categories")
        
        # Clean up test file
        Path(export_path).unlink(missing_ok=True)
    else:
        print("  ❌ Export failed")
    
    print("\n✅ All tests completed successfully!")

def compare_versions():
    """Compare V1 and V2 mappers to show improvements."""
    print("\n🔄 Comparing Original vs V2 Enhanced Mappers")
    print("=" * 60)
    
    try:
        # Import both versions
        from src.core.enhanced_resource_control_mappings import EnhancedResourceControlMapper
        
        # Initialize both mappers
        mapper_v1 = EnhancedResourceControlMapper()
        mapper_v2 = EnhancedResourceControlMapperV2()
        
        print("\n📊 Feature Comparison:")
        print("\n  Original Mapper:")
        print("    - Basic control mapping")
        print("    - URL extraction")
        print("    - Resource categorization")
        print(f"    - Controls loaded: {len(mapper_v1.control_database)}")
        print(f"    - Resource categories: {len(mapper_v1.resource_mappings)}")
        
        print("\n  Enhanced Mapper V2:")
        print("    - Advanced control mapping with priority & severity")
        print("    - Enhanced URL extraction (aka.ms support)")
        print("    - Control relationships mapping")
        print("    - Compliance framework mapping")
        print("    - Risk assessment & vulnerability tracking")
        print("    - Security posture reporting")
        print("    - Extended resource categories")
        print(f"    - Controls loaded: {len(mapper_v2.control_database)}")
        print(f"    - Resource categories: {len(mapper_v2.resource_mappings)}")
        print(f"    - Control relationships: {len(mapper_v2.control_relationships)}")
        print(f"    - Compliance frameworks: {len(set(f for frameworks in mapper_v2.compliance_frameworks.values() for f in frameworks))}")
        
        # Show new capabilities
        print("\n🆕 New V2 Capabilities Demonstrated:")
        
        # 1. Priority-based filtering
        high_priority_controls = [c for c in mapper_v2.control_database.values() if c.get('priority') == 'HIGH']
        print(f"\n  1. Priority-based Analysis:")
        print(f"     - High priority controls: {len(high_priority_controls)}")
        
        # 2. Automation candidates
        automation_controls = [c for c in mapper_v2.control_database.values() if c.get('automation_possible')]
        print(f"\n  2. Automation Opportunities:")
        print(f"     - Controls that can be automated: {len(automation_controls)}")
        
        # 3. Risk-based resource grouping
        risk_groups = {"HIGH": [], "MEDIUM": [], "LOW": []}
        for category, mapping in mapper_v2.resource_mappings.items():
            risk_level = mapping.get('risk_level', 'MEDIUM')
            if risk_level in risk_groups:
                risk_groups[risk_level].append(category)
        
        print(f"\n  3. Risk-based Resource Grouping:")
        for risk, categories in risk_groups.items():
            print(f"     - {risk} risk: {len(categories)} categories")
        
    except ImportError as e:
        print(f"  ⚠️  Could not import original mapper for comparison: {e}")
    except Exception as e:
        print(f"  ❌ Error during comparison: {e}")

def main():
    """Main test function."""
    print("🚀 Enhanced Resource Control Mappings V2 Test Suite")
    print("=" * 70)
    
    try:
        # Run feature tests
        test_enhanced_features()
        
        # Run comparison
        compare_versions()
        
        print("\n🎉 All tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        logging.exception("Detailed error:")

if __name__ == "__main__":
    main()
