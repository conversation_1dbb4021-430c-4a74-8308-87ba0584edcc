Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,template.json,133,App Service 'onefuzz-daily-ui' is not protected by network security groups (NSGs) or Azure Firewall. 'ipSecurityRestrictions' allows all traffic.,Apply NSGs or Azure Firewall to restrict access to the App Service. Update 'ipSecurityRestrictions' to allow only trusted IPs.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,44,"App Service 'onefuzz-daily-ui' exposes public endpoints with 'sslState' set to 'Disabled' for 'onefuzz-daily-ui.azurewebsites.net', increasing risk of public exposure.",Enable HTTPS for all public endpoints by setting 'sslState' to 'SniEnabled' or 'IpBasedEnabled' and ensure 'httpsOnly' is true.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,51,"App Service 'onefuzz-daily-ui' exposes public SCM endpoint with 'sslState' set to 'Disabled' for 'onefuzz-daily-ui.scm.azurewebsites.net', increasing risk of public exposure.",Enable HTTPS for all public endpoints by setting 'sslState' to 'SniEnabled' or 'IpBasedEnabled' and ensure 'httpsOnly' is true.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,133,App Service 'onefuzz-daily-ui' allows all inbound traffic by setting 'ipSecurityRestrictions' with 'ipAddress': 'Any' and 'action': 'Allow'. This exposes the app to the public internet.,Restrict 'ipSecurityRestrictions' to only allow trusted IP ranges. Remove or replace 'ipAddress': 'Any' with specific addresses or ranges.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,141,App Service 'onefuzz-daily-ui' allows all inbound SCM traffic by setting 'scmIpSecurityRestrictions' with 'ipAddress': 'Any' and 'action': 'Allow'. This exposes the SCM endpoint to the public internet.,Restrict 'scmIpSecurityRestrictions' to only allow trusted IP ranges. Remove or replace 'ipAddress': 'Any' with specific addresses or ranges.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-3,template.json,133,App Service 'onefuzz-daily-ui' does not use Network Security Groups (NSGs) to control inbound and outbound traffic. 'ipSecurityRestrictions' allows all.,Implement NSGs to restrict inbound and outbound traffic to the App Service. Update 'ipSecurityRestrictions' to enforce least privilege.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,133,"App Service 'onefuzz-daily-ui' is configured with 'publicNetworkAccess': 'Enabled' and does not use private endpoints, increasing exposure to the public internet.",Set 'publicNetworkAccess' to 'Disabled' and configure a private endpoint for the App Service to restrict access to internal networks only.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,44,App Service 'onefuzz-daily-ui' does not explicitly configure encryption at rest. Encryption at rest must be enabled for all data storage.,Ensure App Service uses encrypted storage by default and configure Azure Storage or App Service settings to enforce encryption at rest.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,44,"App Service 'onefuzz-daily-ui' has 'hostNameSslStates' with 'sslState' set to 'Disabled' for 'onefuzz-daily-ui.azurewebsites.net', which allows unencrypted HTTP connections. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates to enforce HTTPS. Ensure all endpoints require TLS 1.2 or higher.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,51,"App Service 'onefuzz-daily-ui' has 'hostNameSslStates' with 'sslState' set to 'Disabled' for 'onefuzz-daily-ui.scm.azurewebsites.net', which allows unencrypted HTTP connections. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates to enforce HTTPS. Ensure all endpoints require TLS 1.2 or higher.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,133,"App Service 'onefuzz-daily-ui' has 'publishingUsername' property set in plain text, which may expose sensitive information if not managed securely.",Store sensitive publishing credentials in Azure Key Vault and reference them securely in the template. Remove plain text credentials from configuration.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-6,template.json,44,App Service 'onefuzz-daily-ui' does not specify use of customer-managed keys (CMK) for encryption at rest.,Configure App Service to use customer-managed keys for encryption at rest if handling sensitive or regulated data.,N/A,AI,Generic
