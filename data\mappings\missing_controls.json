{"summary": {"total_missing": 80, "coverage_percentage": 4.761904761904762}, "missing_controls": [{"id": "AM-1", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "1.1 - Utilize an Active Discovery Tool\n1.2 - Use a Passive Asset Discovery Tool\n1.4 - Maintain Detailed Asset Inventory\n1.5 - Maintain Asset Inventory Information\n2.1 - Maintain Inventory of Authorized Software"}, {"framework": "CIS Controls v8 ID(s)", "control": "1.1 - Establish and Maintain Detailed Enterprise Asset Inventory\n1.5 - Use a Passive Asset Discovery Tool\n2.1 - Establish and Maintain a Software Inventory\n2.4 - Utilize Automated Software Inventory Tools"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CM-8: INFORMATION SYSTEM COMPONENT INVENTORY\nPM-5: INFORMATION SYSTEM INVENTORY"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "2.4"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "AM-2", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "2.7 - Utilize Application Whitelisting\n2.8 - Implement Application Whitelisting of Libraries\n2.9 - Implement Application Whitelisting of Scripts\n9.2 - Ensure Only Approved Ports, Protocols, and Services Are Running"}, {"framework": "CIS Controls v8 ID(s)", "control": "2.5 - Allowlist Authorized Software\n2.6 - Allowlist Authorized Libraries\n2.7 - Allowlist Authorized Scripts\n4.8 - Uninstall or Disable Unnecessary Services on Enterprise Assets and Software"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CM-8: INFORMATION SYSTEM COMPONENT INVENTORY\nPM-5: INFORMATION SYSTEM INVENTORY"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "6.3"}, {"framework": "Azure Policy Mapping", "control": "Virtual machines should be migrated to new Azure Resource Manager resources\nStorage accounts should be migrated to new Azure Resource Manager resources"}]}, {"id": "AM-3", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "1.4 - Maintain Detailed Asset Inventory\n1.5 - Maintain Asset Inventory Information\n2.1 - Maintain Inventory of Authorized Software\n2.4 - Track Software Inventory Information"}, {"framework": "CIS Controls v8 ID(s)", "control": "1.1 - Establish and Maintain Detailed Enterprise Asset Inventory\n2.1 - Establish and Maintain a Software Inventory"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CM-8: INFORMATION SYSTEM COMPONENT INVENTORY\nCM-7: LEAST FUNCTIONALITY"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "2.4"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "AM-4", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "14.6 - Protect Information Through Access Control Lists"}, {"framework": "CIS Controls v8 ID(s)", "control": "3.3 - Configure Data Access Control Lists"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-3: ACCESS ENFORCEMENT"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "AM-5", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "2.7 - Utilize Application Whitelisting\n2.8 - Implement Application Whitelisting of Libraries\n2.9 - Implement Application Whitelisting of Scripts\n9.2 - Ensure Only Approved Ports, Protocols, and Services Are Running"}, {"framework": "CIS Controls v8 ID(s)", "control": "2.5 - Allowlist Authorized Software\n2.6 - Allowlist Authorized Libraries\n2.7 - Allowlist Authorized Scripts\n4.8 - Uninstall or Disable Unnecessary Services on Enterprise Assets and Software"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CM-8: INFORMA<PERSON><PERSON> SYSTEM COMPONENT INVENTORY\nCM-7: LEAST FUNCTIONALITY\nCM-10: SOFTWARE USAGE RESTRICTIONS\nCM-11: USER-INSTALLED SOFTWARE"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "6.3"}, {"framework": "Azure Policy Mapping", "control": "Allowlist rules in your adaptive application control policy should be updated\nAdaptive application controls for defining safe applications should be enabled on your machines"}]}, {"id": "BR-1", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "10.1 - Ensure Regular Automated Backups"}, {"framework": "CIS Controls v8 ID(s)", "control": "11.2 - Perform Automated Backups"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CP-2: CONTINGENCY PLAN\nCP-4: CONTINGENCY PLAN TESTING\nCP-9: INFORMATION SYSTEM BACKUP"}, {"framework": "Azure Policy Mapping", "control": "Azure Backup should be enabled for Virtual Machines\nGeo-redundant backup should be enabled for Azure Database for MariaDB\nGeo-redundant backup should be enabled for Azure Database for PostgreSQL\nGeo-redundant backup should be enabled for Azure Database for MySQL"}]}, {"id": "BR-2", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "10.4 - Ensure Protection of Backups"}, {"framework": "CIS Controls v8 ID(s)", "control": "11.3 - Protect Recovery Data"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CP-6: <PERSON><PERSON><PERSON><PERSON> STORAGE SITE\nCP-9: INFORMATION SYSTEM BACKUP"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "3.4"}, {"framework": "Azure Policy Mapping", "control": "Azure Backup should be enabled for Virtual Machines\nGeo-redundant backup should be enabled for Azure Database for MariaDB\nGeo-redundant backup should be enabled for Azure Database for PostgreSQL\nGeo-redundant backup should be enabled for Azure Database for MySQL"}]}, {"id": "BR-3", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "10.4 - Ensure Protection of Backups"}, {"framework": "CIS Controls v8 ID(s)", "control": "11.3 - Protect Recovery Data"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CP-9: INFORMATION SYSTEM BACKUP"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "BR-4", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "10.3 - Test Data on Backup Media"}, {"framework": "CIS Controls v8 ID(s)", "control": "11.5 - Test Data Recovery"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CP-4: CONT<PERSON><PERSON><PERSON><PERSON> PLAN TESTING\nCP-9: INFORMATION SYSTEM BACKUP"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "DP-1", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "13.1 - \tMaintain an Inventory of Sensitive Information\n14.5 - Utilize an Active Discovery Tool to Identify Sensitive Data"}, {"framework": "CIS Controls v8 ID(s)", "control": "3.2 - Establish and Maintain a Data Inventory\n3.7 - Establish and Maintain a Data Classification Scheme\n3.13 - Deploy a Data Loss Prevention Solution"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "RA-2: SECURITY CATEGORIZATION\nSC-28: PROTECTION OF INFORMATION AT REST"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "A3.2"}, {"framework": "Azure Policy Mapping", "control": "[Preview]: Sensitive data in your SQL databases should be classified"}]}, {"id": "DP-5", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "14.8 - Encrypt Sensitive Information at Rest"}, {"framework": "CIS Controls v8 ID(s)", "control": "3.11 - Encrypt Sensitive Data at Rest"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SC-12: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> KEY ESTAB<PERSON><PERSON><PERSON>MENT AND MANAGEMENT\nSC-28: PROTECTION OF INFORMATION AT REST"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "3.4\n3.5\n3.6"}, {"framework": "Azure Policy Mapping", "control": "SQL managed instances should use customer-managed keys to encrypt data at rest\nSQL servers should use customer-managed keys to encrypt data at rest\nPostgreSQL servers should use customer-managed keys to encrypt data at rest\nAzure Cosmos DB accounts should use customer-managed keys to encrypt data at rest\nContainer registries should be encrypted with a customer-managed key\nCognitive Services accounts should enable data encryption with a customer-managed key\nStorage accounts should use customer-managed key for encryption\nMySQL servers should use customer-managed keys to encrypt data at rest\nAzure Machine Learning workspaces should be encrypted with a customer-managed key"}]}, {"id": "DP-6", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "NIST SP800-53 r4 ID(s)", "control": "IA-5: AUTHEN<PERSON><PERSON><PERSON><PERSON> MANAGEMENT\nSC-12: CRYPTO<PERSON><PERSON><PERSON><PERSON> KEY ESTABLISHMENT AND MANAGEMENT\nSC-28: PROTECTION OF INFORMATION AT REST"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "3.6"}, {"framework": "Azure Policy Mapping", "control": "Key Vault keys should have an expiration date\nKey Vault secrets should have an expiration date"}]}, {"id": "DP-7", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "NIST SP800-53 r4 ID(s)", "control": "IA-5: AUTH<PERSON><PERSON><PERSON><PERSON><PERSON> MANAGEMENT\nSC-12: CRYPTO<PERSON><PERSON><PERSON><PERSON> KEY ESTABLISHMENT AND MANAGEMENT\nSC-17: <PERSON><PERSON><PERSON><PERSON> KEY INFRASTRUCTURE CERTIFICATES"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "3.6"}, {"framework": "Azure Policy Mapping", "control": "[Preview]: Certificates should have the specified maximum validity period"}]}, {"id": "DP-8", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "NIST SP800-53 r4 ID(s)", "control": "IA-5: AUTH<PERSON><PERSON><PERSON><PERSON><PERSON> MANAGEMENT\nSC-12: CRYPTO<PERSON><PERSON><PERSON><PERSON> KEY ESTABLISHMENT AND MANAGEMENT\nSC-17: <PERSON><PERSON><PERSON><PERSON> KEY INFRASTRUCTURE CERTIFICATES"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "3.6"}, {"framework": "Azure Policy Mapping", "control": "Key vaults should have purge protection enabled\nAzure Defender for Key Vault should be enabled\nKey vaults should have soft delete enabled\n[Preview]: Azure Key Vault should disable public network access\n[Preview]: Private endpoint should be configured for Key Vault\nResource logs in Key Vault should be enabled"}]}, {"id": "DS-1", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v8 ID(s)", "control": "16.10 - Apply Secure Design Principles in Application Architectures\n16.14 - Conduct Threat Modeling"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SA-15: DEVELOPMENT PROCESS, STANDARDS, AND TOOLS"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "6.5\n12.2"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "DS-2", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "18.3 - Verify That Acquired Software is Still Supported\n18.4 - Only Use Up-to-Date And Trusted Third-Party Components\n18.8 - Establish a Process to Accept and Address Reports of Software Vulnerabilities"}, {"framework": "CIS Controls v8 ID(s)", "control": "16.4 - Establish and Manage an Inventory of Third-Party Software Components\n16.6 - Establish and Maintain a Severity Rating System and Process for Application Vulnerabilities\n16.11 - Leverage Vetted Modules or Services for Application Security Components"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SA-12: <PERSON><PERSON><PERSON><PERSON> CHAIN PROTECTION\nSA-15: DEVELOPMENT PROCESS, STANDARDS, AND TOOLS"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "6.3\n6.5"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "DS-3", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "18.11 - Use Standard Hardening Configuration Templates for Databases"}, {"framework": "CIS Controls v8 ID(s)", "control": "16.7 - Use Standard Hardening Configuration Templates for Application Infrastructure"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CM-2: <PERSON><PERSON><PERSON><PERSON><PERSON> CONFIGURATION\nCM-6: CONFIGURATION SETTINGS\nAC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nAC-6: LEAST PRIVILEGE"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "2.2\n6.3\n7.1"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "DS-4", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "18.7 - Apply Static and Dynamic Code Analysis Tools"}, {"framework": "CIS Controls v8 ID(s)", "control": "16.12 - Implement Code-Level Security Checks"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SA-11: <PERSON><PERSON><PERSON><PERSON><PERSON> TESTING AND EVALUATION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "6.3\n6.5"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "DS-5", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "18.7 - Apply Static and Dynamic Code Analysis Tools"}, {"framework": "CIS Controls v8 ID(s)", "control": "16.12 - Implement Code-Level Security Checks"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SA-11: <PERSON><PERSON><PERSON><PERSON><PERSON> TESTING AND EVALUATION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "6.3\n6.5"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "DS-6", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "5.2 - Deploy System Configuration Management Tools\n5.3 - Securely Store Master Images\n5.4 - Deploy System Configuration Management Tools\n5.5 - Implement Automated Configuration Monitoring Systems\n18.1 - Establish Secure Coding Practices"}, {"framework": "CIS Controls v8 ID(s)", "control": "7.5 - Perform Automated Vulnerability Scans of Internal Enterprise Assets\n7.6 - Perform Automated Vulnerability Scans of Externally-Exposed Enterprise Assets\n7.7 - Remediate Detected Vulnerabilities\n16.1 - Establish and Maintain a Secure Application Development Process\n16.7 - Use Standard Hardening Configuration Templates for Application Infrastructure"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CM-2: <PERSON><PERSON><PERSON><PERSON><PERSON> CONFIGURATION\nCM-6: CONFIGURATION SETTINGS\nAC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nAC-6: LEAST PRIVILEGE"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "6.1\n6.2\n6.3"}, {"framework": "Azure Policy Mapping", "control": "Vulnerabilities in Azure Container Registry images should be remediated\nVulnerabilities in container security configurations should be remediated"}]}, {"id": "DS-7", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "6.2 - Activate audit logging\n6.3 - Enable Detailed Logging\n6.5 - Central Log Management\n6.6 - Deploy SIEM or Log Analytic tool\n6.7 - Regularly Review Logs\n6.8 - Regularly Tune SIEM"}, {"framework": "CIS Controls v8 ID(s)", "control": "8.2 Collect Audit Logs\n8.5 Collect Detailed Audit Logs\n8.9 Centralize Audit Logs\n8.11 Conduct Audit Log Reviews"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AU-3: CONTENT OF AUDIT RECORDS\nAU-6: AUDIT RE<PERSON><PERSON><PERSON>, ANALYSIS, AND REPORTING\nAU-12: AUDIT GENERATION\nSI-4: INFORMATION SYSTEM MONITORING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "10.1\n10.2\n10.3\n10.6"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "ES-1", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "9.4 - Apply Host-Based Firewalls or Port Filtering"}, {"framework": "CIS Controls v8 ID(s)", "control": "13.7 - Deploy a Host-Based Intrusion Prevention Solution"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SC-3: SECURITY FUNCTION ISOLATION\nSI-2: FLAW REMEDIATION\nSI-3: MALICIOUS CODE PROTECTION\nSI-16 MEMORY PROTECTION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "11.5"}, {"framework": "Azure Policy Mapping", "control": "Azure Defender for servers should be enabled"}]}, {"id": "ES-2", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "8.1 - Utilize Centrally Managed Anti-malware Software"}, {"framework": "CIS Controls v8 ID(s)", "control": "10.1 - Deploy and Maintain Anti-Malware Software"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SC-3: SECURITY FUNCTION ISOLATION\nSI-2: FLAW REMEDIATION\nSI-3: MALICIOUS CODE PROTECTION\nSI-16 MEMORY PROTECTION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "5.1"}, {"framework": "Azure Policy Mapping", "control": "Endpoint protection should be installed on your machines\nEndpoint protection solution should be installed on virtual machine scale sets\nEndpoint protection health issues should be resolved on your machines\nMonitor missing Endpoint Protection in Azure Security Center\nWindows Defender Exploit Guard should be enabled on your machines"}]}, {"id": "ES-3", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "8.2 - Ensure Anti-Malware Software and Signatures are Updated"}, {"framework": "CIS Controls v8 ID(s)", "control": "10.2 - Configure Automatic Anti-Malware Signature Updates"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SI-2: FLAW REMEDIATION\nSI-3: MALICIOUS CODE PROTECTION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "5.2\n5.3"}, {"framework": "Azure Policy Mapping", "control": "Endpoint protection health issues should be resolved on your machines"}]}, {"id": "GS-1", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "17.2 - Deliver Training to Fill the Skills Gap"}, {"framework": "CIS Controls v8 ID(s)", "control": "14.9 - Conduct Role-Specific Security Awareness and Skills Training"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "PL-9: <PERSON><PERSON><PERSON><PERSON> MANAGEMENT\nPM-10: SECURITY AUTHORIZATION PROCESS\nPM-13: INFORMATION SECURITY WORKFORCE\t\nAT-1: SECURITY AWARENESS AND TRAINING POLICY AND PROCEDURES\nAT-3: ROLE-BASED SECURITY TRAINING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "12.4"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "GS-10", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "5.1 - Establish Secure Configurations\n18.1 - Establish Secure Coding Practices\n18.8 - Establish a Process to Accept and Address Reports of Software Vulnerabilities"}, {"framework": "CIS Controls v8 ID(s)", "control": "4.1 - Establish and Maintain a Secure Configuration Process\n4.2 - Establish and Maintain a Secure Configuration Process for Network Infrastructure\n16.1 - Establish and Maintain a Secure Application Development Process\n16.2 - Establish and Maintain a Process to Accept and Address Software Vulnerabilities"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SA-12: <PERSON><PERSON><PERSON><PERSON> CHAIN PROTECTION\nSA-15: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> PROCESS, STANDARD<PERSON>, AND TOOLS\nCM-1: CONFIGURATION MANAGEMENT POLICY AND PROCEDURES\nCM-2: BASELINE CONFIGURATION\nCM-6: CONFIGURATION SETTINGS\nAC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nAC-6: LEAST PRIVILEGE\nSA-11: DEVELOPER TESTING AND EVALUATION\nAU-6: AUDIT REVIEW, ANALYSIS, AND REPORTING\nAU-12: AUDIT GENERATION\nSI-4: INFORMATION SYSTEM MONITORING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "2.2\n6.1\n6.2\n6.3\n6.5\n7.1\n10.1\n10.2\n10.3\n10.6\n12.2"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "GS-2", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "2.10 - Physically or Logically Segregate High Risk Applications\n14.1 - Segment the Network Based on Sensitivity"}, {"framework": "CIS Controls v8 ID(s)", "control": "3.12 - Segment Data Processing and Storage Based on Sensitivity"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-4: INFORMATION FLOW ENFORCEMENT\nSC-7: BOUNDARY PROTECTION\nSC-2: APPLICATION PARTITIONING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "1.2\n6.4"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "GS-3", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "14.1 - Segment the Network Based on Sensitivity"}, {"framework": "CIS Controls v8 ID(s)", "control": "3.1 - Establish and Maintain a Data Management Process\n3.7 - Establish and Maintain a Data Classification Scheme\n3.12 - Segment Data Processing and Storage Based on Sensitivity"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-4: INFORMATION FLOW ENFORCEMENT\nSI-4: INFORMATI<PERSON> SYSTEM MONITORING\nSC-8: TRANSMISSION CONFIDENTIALITY AND INTEGRITY\nSC-12: CRYPTO<PERSON><PERSON><PERSON><PERSON> KEY ESTABLISHMENT AND <PERSON><PERSON><PERSON>MENT\nSC-17: <PERSON><PERSON><PERSON><PERSON> KEY INFRASTRUCTURE CERTIFICATES\nSC-28: PROTECTION OF INFORMATION AT REST\nRA-2: SECURITY CATEGORIZATION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "3.1\n3.2 \n3.3\n3.4\n3.5\n3.6\n3.7\n4.1\nA3.2"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "GS-4", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "12.1 - Maintain an Inventory of Network Boundaries"}, {"framework": "CIS Controls v8 ID(s)", "control": "12.2 - Establish and Maintain a Secure Network Infrastructure\n12.4 - Establish and Maintain Architecture Diagram(s)"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-4: INFORMATION FLOW ENFORCEMENT\nAC-17: R<PERSON><PERSON><PERSON> ACCESS\nCA-3: SYSTEM INTERCONNECTIONS\nCM-1: CONFIGURATION MANAGEMENT POLICY AND PROCEDURES\nCM-2: BASELINE CONFIGURATION\nCM-6: CONFIGURATION SETTINGS\nCM-7: LEAST FUNCTIONALITY\nSC-1: SYSTEM AND COMMUNICATIONS PROTECTION POLICY AND PROCEDURES\nSC-2: APPLICATION PARTITIONING\nSC-5: DENIAL OF SERVICE PROTECTION\nSC-7: BOUNDARY PROTECTION\nSC-20: SECURE NAME / ADDRESS RESOLUTION SERVICE (AUTHORITATIVE SOURCE)\nSC-21: SECURE NAME / ADDRESS RESOLUTION SERVICE (RECURSIVE OR CACHING RESOLVER)\nSI-4: INFORMATION SYSTEM MONITORING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "1.1\n1.2\n1.3\n1.5\n4.1\n6.6\n11.4\nA2.1\nA2.2\nA2.3\nA3.2"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "GS-5", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "5.1 - Establish Secure Configurations"}, {"framework": "CIS Controls v8 ID(s)", "control": "4.1 - Establish and Maintain a Secure Configuration Process\n4.2 - Establish and Maintain a Secure Configuration Process for Network Infrastructure"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CA-1: <PERSON><PERSON><PERSON><PERSON> ASSESSMENT AND <PERSON><PERSON><PERSON>I<PERSON><PERSON><PERSON> POLICY AND PROCEDURES\nCA-8: PENETRATION TESTING\nCM-1: CONFIGURATION MANAGEMENT POLICY AND PROCEDURES\nCM-2: BASELINE CONFIGURATION\nCM-6: CONFIGURATION SETTINGS\nRA-1: <PERSON><PERSON><PERSON> ASSESSMENT POLICY AND PROCEDURES\nRA-3: <PERSON><PERSON><PERSON> ASSESSMENT\nRA-5: VULNERABILITY SCANNING\nSI-1: SYSTEM AND INFORMATION INTEGRITY POLICY AND PROCEDURES\nSI-2: FLAW REMEDIATION\nSI-5: SECURITY ALERTS, ADVISORIES, AND DIRECTIVES"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "1.1\n1.2\n2.2\n6.1\n6.2\n6.5\n6.6\n11.2\n11.3\n11.5"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "GS-6", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "4.5 - Use Multifactor Authentication For All Administrative Access\n16.2 - Configure Centralized Point of Authentication"}, {"framework": "CIS Controls v8 ID(s)", "control": "5.6 - Centralize Account Management\n6.5 - Require MFA for Administrative Access\n6.7 - Centralize Access Control"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-1: ACCESS CONTROL POLICY AND PROCEDURES\nAC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nAC-4: INFORMATION FLOW ENFORCEMENT\nAC-5: SEPARATION OF DUTIES\nAC-6: LEAST PRIVILEGE\nIA-1: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> POLICY AND PROCEDURES\nIA-2: ID<PERSON><PERSON><PERSON><PERSON><PERSON>ON AND AUTHENTICATION (ORGANIZATIONAL USERS)\nIA-4: IDENTIFIER MANAGEMENT\nIA-5: AUTHENTICATOR MANAGEMENT\nIA-8: IDENTIFICATION AND AUTHENTICATION (NON-ORGANIZAT<PERSON>AL USERS)\nIA-9: SERVICE IDENTIFICATION AND AUTHENTICATION\nSI-4: INFORMATION SYSTEM MONITORING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "7.1\n7.2\n7.3\n8.1\n8.2\n8.3\n8.4\n8.5\n8.6\n8.7\n8.8\nA3.4"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "GS-7", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "6.2 -Activate audit logging\n6.3 - Enable Detailed Logging\n6.6 - Deploy SIEM or Log Analytic tool\n6.7 - Regularly Review Logs\n19.1 - Document Incident Response Procedures\n19.5 - Maintain Contact Information For Reporting Security Incidents\n19.7 - Conduct Periodic Incident Scenario Sessions for Personnel"}, {"framework": "CIS Controls v8 ID(s)", "control": "8.1 - Establish and Maintain an Audit Log Management Process\n13.1 - Centralize Security Event Alerting\n17.2 - Establish and Maintain Contact Information for Reporting Security Incidents\n17.4 - Establish and Maintain an Incident Response Process\n17.7 - Conduct Routine Incident Response Exercises"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AU-1: AUDIT AND ACCOUNTA<PERSON><PERSON><PERSON>Y POLICY AND PROCEDURES\nIR-1: INCIDENT RESPONSE POLICY AND PROCEDURES\nIR-2: INCIDENT RESPONSE TRAINING\nIR-10: INTEGRATED INFORMATION SECURITY ANALYSIS TEAM\nSI-1: <PERSON><PERSON><PERSON><PERSON> AND INFORMA<PERSON>ON INTEGRITY POLICY AND PROCEDURES\nSI-5: SECURITY ALERTS, ADVISORIES, AND DIRECTIVES"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "10.1\n10.2\n10.3\n10.4\n10.5\n10.6\n10.7\n10.8\n10.9\n12.10\nA3.5"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "GS-8", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "10.1 - Ensure Regular Automated Backups"}, {"framework": "CIS Controls v8 ID(s)", "control": "11.1 - Establish and Maintain a Data Recovery Process"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CP-1: CONT<PERSON><PERSON><PERSON><PERSON> PLANNING POLICY AND PROCEDURES\nCP-9: INFORMATION SYSTEM BACKUP\nCP-10: INFORMATION SYSTEM RECOVERY AND RECON<PERSON><PERSON>UTION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "3.4"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "GS-9", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "8.1 - Utilize Centrally Managed Anti-malware Software\n9.4 - Apply Host-Based Firewalls or Port-Filtering"}, {"framework": "CIS Controls v8 ID(s)", "control": "4.4 - Implement and Manage a Firewall on Servers\n10.1 - Deploy and Maintain Anti-Malware Software"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SI-2: FL<PERSON>W REMEDIATION\nSI-3: MALICIOUS CODE PROTECTION\nSC-3: SECURITY FUNCTION ISOLATION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "5.1\n5.2\n5.3\n5.4\n11.5"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "IM-1", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "16.1 - Maintain an Inventory of \nAuthentication Systems\n16.2 - Configure Centralized \nPoint of Authentication"}, {"framework": "CIS Controls v8 ID(s)", "control": "6.7 - Centralize Access Control\n12.5 - Centralize Network Authentication, Authorization, and Auditing (AAA)"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nIA-2: IDENTIFICATION AND AUTHENTICATION (ORGANIZATIONAL USERS)\nIA-8: IDENTIFICATION AND AUTHENTICATION (NON-ORGANIZATIONAL USERS)"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "7.2\n8.3"}, {"framework": "Azure Policy Mapping", "control": "An Azure Active Directory administrator should be provisioned for SQL servers\nService Fabric clusters should only use Azure Active Directory for client authentication"}]}, {"id": "IM-2", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "4.3 - Ensure the Use of Dedicated Administrative Accounts\n4.5 - Use Multi-Factor Authentication for All Administrative Access"}, {"framework": "CIS Controls v8 ID(s)", "control": "5.4 - Restrict Administrator Privileges to Dedicated Administrator Accounts\n6.5 - Require <PERSON><PERSON> for Administrative Access"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nIA-2: IDENTIFICATION AND AUTHENTICATION (ORGANIZATIONAL USERS)\nIA-8: IDENTIFICATION AND AUTHENTICATION (NON-ORGANIZATIONAL USERS)\nSI-4: INFORMATION SYSTEM MONITORING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "8.2\n8.3"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "IM-3", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nIA-4: IDENTIFIER MANAGEMENT\nIA-5: AUTHENTICATOR MANAGEMENT\nIA-9: SERVICE IDENTIFICATION AND AUTHENTICATION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "N/A"}, {"framework": "Azure Policy Mapping", "control": "Managed identity should be used in your Function App\nManaged identity should be used in your Web App\nService principals should be used to protect your subscriptions instead of management certificates\nManaged identity should be used in your API App\nVirtual machines' Guest Configuration extension should be deployed with system-assigned managed identity"}]}, {"id": "IM-4", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "NIST SP800-53 r4 ID(s)", "control": "IA-9: SERVICE IDENTITIFICATION AND AUTHENTICATION"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "IM-5", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "16.2 - Configure Centralized Point of Authentication"}, {"framework": "CIS Controls v8 ID(s)", "control": "12.5 - Centralize Network Authentication, Authorization, and Auditing (AAA)"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "IA-4: <PERSON><PERSON><PERSON><PERSON><PERSON> MANAGEMENT\nIA-2: IDEN<PERSON><PERSON>CATION AND AUTHENTICATION (ORGANIZATIONAL USERS)\nIA-8: IDENTIFICATION AND AUTHENTICATION (NON-ORGANIZATIONAL USERS)"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "IM-6", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "4.2 - Change Default Passwords\n4.5 - Use Multifactor Authentication For All Administrative Access\n12.11 - Require All Remote Logins to Use Multi-Factor Authentication\n16.3 - Require Multi-Factor Authentication"}, {"framework": "CIS Controls v8 ID(s)", "control": "6.3 - Require MFA for Externally-Exposed Applications\n6.4 - Require MFA for Administrative Access"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nIA-2: IDENTIFICATION AND AUTHENTICATION (ORGANIZATIONAL USERS)\nIA-5: AUTHENTICATOR MANAGEMENT\nIA-8: IDENTI<PERSON>CATION AND AUTHENTICATION (NON-ORGANIZATIONAL USERS)"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "7.2\n8.2\n8.3\n8.4"}, {"framework": "Azure Policy Mapping", "control": "Authentication to Linux machines should require SSH keys\nMFA should be enabled accounts with write permissions on your subscription\nMFA should be enabled on accounts with owner permissions on your subscription\nMFA should be enabled on accounts with read permissions on your subscription"}]}, {"id": "IM-7", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "12.11 - Require All Remote Logins to Use Multi-Factor Authentication\n12.12 - Manage All Devices Remotely Logging Into Internal Network\n14.6 - Protect Information Through Access Control Lists\n16.3 - Require Multi-Factor Authentication"}, {"framework": "CIS Controls v8 ID(s)", "control": "3.3 - Configure Data Access Control Lists\n6.4 - Require MFA for Administrative Access\n13.5 - Manage Access Control for Remote Assets"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nAC-6: LEAST PRIVILEGE"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "7.2"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "IM-8", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "18.1 - Establish Secure Coding Practices\n18.6 - Ensure Software Development Personnel Are Trained in Secure Coding\n18.7 - Apply Static and Dynamic Code Analysis Tools"}, {"framework": "CIS Controls v8 ID(s)", "control": "16.9 - Train Developers in Application Security Concepts and Secure Coding\n16.12 - Implement Code-Level Security Checks"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "IA-5: AUTHENTICATOR MANAGEMENT"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "3.5\n6.3\n8.2"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "IM-9", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "12.10 Decrypt Network Traffic at Proxy\n16.2 Configure Centralized Point of Authentication"}, {"framework": "CIS Controls v8 ID(s)", "control": "6.7 - Centralize Access Control\n12.5 - Centralize Network Authentication, Authorization, and Auditing (AAA)"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nSC-11: TRUSTED PATH"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "IR-1", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "19.1 - Document Incident Response Procedures\n19.7 - Conduct Periodic Incident Scenario Sessions for Personnel"}, {"framework": "CIS Controls v8 ID(s)", "control": "17.4 - Establish and Maintain an Incident Response Process\n17.7 - Conduct Routine Incident Response Exercises"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "IR-4: INCIDENT HANDLING\nIR-8: INCIDENT RESPONSE PLAN"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "10.8"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "IR-2", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "19.2 - Assign Job Titles and Duties for Incident Response\n19.3 - Designate Management Personnel to Support Incident Handling\n19.4 - Devise Organization-wide Standards for Reporting Incidents\n19.5 - Maintain Contact Information For Reporting Security Incidents"}, {"framework": "CIS Controls v8 ID(s)", "control": "17.1 - Designate Personnel to Manage Incident Handling\n17.3 - Establish and Maintain an Enterprise Process for Reporting Incidents\n17.6 - Define Mechanisms for Communicating During Incident Response"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "IR-4: INCIDENT HANDLING\nIR-8: INCIDENT RESPONSE PLAN\nIR-5: INCIDENT MONITORING\nIR-6: INCIDENT REPORTING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "12.10"}, {"framework": "Azure Policy Mapping", "control": "Email notification to subscription owner for high severity alerts should be enabled\nSubscriptions should have a contact email address for security issues\nEmail notification for high severity alerts should be enabled"}]}, {"id": "IR-3", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "19.8 - Create Incident Scoring and Prioritization Schema"}, {"framework": "CIS Controls v8 ID(s)", "control": "17.9 - Establish and Maintain Security Incident Thresholds"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "IR-4: INCIDENT HANDLING\nIR-5: INCIDENT MONITORING\nIR-7 INCIDENT RESPONSE ASSISTANCE"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "10.8"}, {"framework": "Azure Policy Mapping", "control": "Azure Defender for open-source relational databases should be enabled\nAzure Defender for Key Vault should be enabled\nAzure Defender for App Service should be enabled\nAzure Defender for Storage should be enabled\nAzure Defender for servers should be enabled\nAzure Defender for Kubernetes should be enabled\nAzure Defender for SQL servers on machines should be enabled\nAzure Defender for Azure SQL Database servers should be enabled\nAzure Defender for SQL should be enabled for unprotected Azure SQL servers\nAzure Defender for SQL should be enabled for unprotected SQL Managed Instances\n[Preview]: Azure Defender for DNS should be enabled\nAzure Defender for container registries should be enabled\nAzure Defender for Resource Manager should be enabled"}]}, {"id": "IR-4", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "NIST SP800-53 r4 ID(s)", "control": "IR-4: INCIDENT HANDLING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "12.10"}, {"framework": "Azure Policy Mapping", "control": "Network Watcher should be enabled"}]}, {"id": "IR-5", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "19.8 - Create Incident Scoring and Prioritization Schema"}, {"framework": "CIS Controls v8 ID(s)", "control": "17.4 - Establish and Maintain an Incident Response Process\n17.9 - Establish and Maintain Security Incident Thresholds"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "IR-4: INCIDENT HANDLING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "12.10"}, {"framework": "Azure Policy Mapping", "control": "Azure Defender for open-source relational databases should be enabled\nAzure Defender for Key Vault should be enabled\nAzure Defender for App Service should be enabled\nAzure Defender for Storage should be enabled\nAzure Defender for servers should be enabled\nAzure Defender for Kubernetes should be enabled\nAzure Defender for SQL servers on machines should be enabled\nAzure Defender for Azure SQL Database servers should be enabled\nAzure Defender for SQL should be enabled for unprotected Azure SQL servers\nAzure Defender for SQL should be enabled for unprotected SQL Managed Instances\n[Preview]: Azure Defender for DNS should be enabled\nAzure Defender for container registries should be enabled\nAzure Defender for Resource Manager should be enabled"}]}, {"id": "IR-6", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "NIST SP800-53 r4 ID(s)", "control": "IR-4: INCIDENT HANDLING\nIR-5: INCIDENT MONITORING\nIR-6: INCIDENT REPORTING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "12.10"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "IR-7", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v8 ID(s)", "control": "17.8 - Conduct Post-Incident Reviews"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "IR-4 INCIDENT HANDLING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "12.10"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "LT-1", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "6.7 - Regularly Review Logs"}, {"framework": "CIS Controls v8 ID(s)", "control": "8.11 - Conduct Audit Log Reviews"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AU-3: CONTENT OF AUDIT RECORDS\nAU-6: AUDIT RE<PERSON><PERSON><PERSON>, ANALYSIS, AND REPORTING\nAU-12: AUDIT GENERATION\nSI-4: INFORMATION SYSTEM MONITORING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "10.6\n10.8\nA3.5"}, {"framework": "Azure Policy Mapping", "control": "Azure Defender for open-source relational databases should be enabled\nAzure Defender for Key Vault should be enabled\nAzure Defender for App Service should be enabled\nAzure Defender for Storage should be enabled\nAzure Defender for servers should be enabled\nAzure Defender for Kubernetes should be enabled\nAzure Defender for SQL servers on machines should be enabled\nAzure Defender for Azure SQL Database servers should be enabled\n[Preview]: Azure Arc enabled Kubernetes clusters should have Azure Defender's extension installed\n[Preview]: Azure Kubernetes Service clusters should have Azure Defender profile enabled\nAzure Defender for SQL should be enabled for unprotected Azure SQL servers\nAzure Defender for SQL should be enabled for unprotected SQL Managed Instances\n[Preview]: Azure Defender for DNS should be enabled\nWindows Defender Exploit Guard should be enabled on your machines\nAzure Defender for container registries should be enabled\nAzure Defender for Resource Manager should be enabled"}]}, {"id": "LT-2", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "4.9 - <PERSON><PERSON> and <PERSON><PERSON> on Unsuccessful Administrative Account Login\n6.7 - Regularly Review Logs\n16.13 - <PERSON><PERSON> on Account Login Behavior Deviation"}, {"framework": "CIS Controls v8 ID(s)", "control": "8.11 - Conduct Audit Log Reviews"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AU-3: CONTENT OF AUDIT RECORDS\nAU-6: AUDIT RE<PERSON><PERSON><PERSON>, ANALYSIS, AND REPORTING\nAU-12: AUDIT GENERATION\nSI-4: INFORMATION SYSTEM MONITORING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "10.6\n10.8\nA3.5"}, {"framework": "Azure Policy Mapping", "control": "Azure Defender for open-source relational databases should be enabled\nAzure Defender for Key Vault should be enabled\nAzure Defender for App Service should be enabled\nAzure Defender for Storage should be enabled\nAzure Defender for servers should be enabled\nAzure Defender for Kubernetes should be enabled\nAzure Defender for SQL servers on machines should be enabled\nAzure Defender for Azure SQL Database servers should be enabled\n[Preview]: Azure Arc enabled Kubernetes clusters should have Azure Defender's extension installed\n[Preview]: Azure Kubernetes Service clusters should have Azure Defender profile enabled\nAzure Defender for SQL should be enabled for unprotected Azure SQL servers\nAzure Defender for SQL should be enabled for unprotected SQL Managed Instances\n[Preview]: Azure Defender for DNS should be enabled\nWindows Defender Exploit Guard should be enabled on your machines\nAzure Defender for container registries should be enabled\nAzure Defender for Resource Manager should be enabled\nResource logs in Azure Kubernetes Service should be enabled"}]}, {"id": "LT-3", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "6.2 - Activate Audit Logging\n6.3 - Enable Detailed Logging\n8.8 - Enable Command-Line Audit Logging"}, {"framework": "CIS Controls v8 ID(s)", "control": "8.2 - Collect Audit Logs\n8.5 - Collect Detailed Audit Logs\n8.12 - Collect Service Provider Logs"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AU-3: CONTENT OF AUDIT RECORDS\nAU-6: AUDIT RE<PERSON><PERSON><PERSON>, ANALYSIS, AND REPORTING\nAU-12: AUDIT GENERATION\nSI-4: INFORMATION SYSTEM MONITORING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "10.1\n10.2\n10.3"}, {"framework": "Azure Policy Mapping", "control": "Resource logs in Azure Data Lake Store should be enabled\nResource logs in Logic Apps should be enabled\nResource logs in IoT Hub should be enabled\nResource logs in Batch accounts should be enabled\nResource logs in Virtual Machine Scale Sets should be enabled\nResource logs in Event Hub should be enabled\nAuditing on SQL server should be enabled\nResource logs in Search services should be enabled\nDiagnostic logs in App Services should be enabled\nResource logs in Data Lake Analytics should be enabled\nResource logs in Key Vault should be enabled\nResource logs in Service Bus should be enabled\nResource logs in Azure Stream Analytics should be enabled"}]}, {"id": "LT-4", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "6.2 - Activate Audit Logging\n6.3 - Enable Detailed Logging\n7.6 - Log All URL Requests\n8.7 - Enable DNS Query Logging\n12.8 - Deploy NetFlow Collection on Networking Boundary Devices"}, {"framework": "CIS Controls v8 ID(s)", "control": "8.2 - Collect Audit Logs\n8.5 - Collect Detailed Audit Logs\n8.6 - Collect DNS Query Audit Logs\n8.7 - Collect URL Request Audit Logs\n13.6 - Collect Network Traffic Flow Logs"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AU-3: CONTENT OF AUDIT RECORDS\nAU-6: AUDIT RE<PERSON><PERSON><PERSON>, ANALYSIS, AND REPORTING\nAU-12: AUDIT GENERATION\nSI-4: INFORMATION SYSTEM MONITORING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "10.8"}, {"framework": "Azure Policy Mapping", "control": "[Preview]: Network traffic data collection agent should be installed on Linux virtual machines\n[Preview]: Network traffic data collection agent should be installed on Windows virtual machines"}]}, {"id": "LT-5", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "6.5 - Central Log Management\n6.6 - Deploy SIEM or Log Analytic tool\n6.7 - Regularly Review Logs\n8.6 - Centralize Anti-Malware Logging"}, {"framework": "CIS Controls v8 ID(s)", "control": "8.9 - Centralize Audit Logs\n8.11 - Conduct Audit Log Reviews\n13.1 - Centralize Security Event Alerting"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AU-3: CONTENT OF AUDIT RECORDS\nAU-6: AUDIT RE<PERSON><PERSON><PERSON>, ANALYSIS, AND REPORTING\nAU-12: AUDIT GENERATION\nSI-4: INFORMATION SYSTEM MONITORING"}, {"framework": "Azure Policy Mapping", "control": "Auto provisioning of the Log Analytics agent should be enabled on your subscription\n[Preview]: Log Analytics agent should be installed on your Linux Azure Arc machines\nLog Analytics agent should be installed on your virtual machine scale sets for Azure Security Center monitoring\nLog Analytics agent should be installed on your virtual machine for Azure Security Center monitoring\nLog Analytics agent health issues should be resolved on your machines\n[Preview]: Log Analytics agent should be installed on your Windows Azure Arc machines"}]}, {"id": "LT-6", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "6.4 - Ensure Adequate Storage for Logs"}, {"framework": "CIS Controls v8 ID(s)", "control": "8.3 - Ensure Adequate Audit Log Storage\n8.10 - Retain Audit Logs"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AU-11: AUDIT RECORD RETENTION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "10.5\n10.7"}, {"framework": "Azure Policy Mapping", "control": "SQL servers with auditing to storage account destination should be configured with 90 days retention or higher"}]}, {"id": "LT-7", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "6.1 - Utilize Three Synchronized Time Sources"}, {"framework": "CIS Controls v8 ID(s)", "control": "8.4 - Standardize Time Synchronization"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AU-8: TIME STAMPS"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "10.4"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "NS-10", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "7.7 - Use of DNS Filtering Services"}, {"framework": "CIS Controls v8 ID(s)", "control": "4.9 - Configure Trusted DNS Servers on Enterprise Assets\n9.2 - Use DNS Filtering Services"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SC-20: SECURE NAME / ADDRESS RESOLUTION SERVICE (AUTHORITATIVE SOURCE)\nSC-21: SECURE NAME / ADDRESS RESOLUTION SERVICE (RECURSIVE OR CACHING RESOLVER)"}, {"framework": "Azure Policy Mapping", "control": "[Preview]: Azure Defender for DNS should be enabled"}]}, {"id": "NS-3", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "9.2 - Ensure Only Approved Ports, Protocols and Services Are Running\n9.4 - Apply Host-Based Firewalls or Port Filtering\n12.3 - Deny Communications with Known Malicious IP Addresses\n12.4 - Deny Communication over Unauthorized Ports\n14.1 - Segment the Network Based on Sensitivity\n14.2 - Enable Firewall Filtering Between VLANs"}, {"framework": "CIS Controls v8 ID(s)", "control": "4.4 - Implement and Manage a Firewall on Servers\n4.8 - Uninstall or Disable Unnecessary Services on Enterprise Assets and Software\n13.10 Perform Application Layer Filtering"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-4: INFORMATION FLOW ENFORCEMENT\nSC-7: BOUNDARY PROTECTION\nCM-7: LEAST FUNCTIONALITY"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "1.1\n1.2\n1.3"}, {"framework": "Azure Policy Mapping", "control": "Management ports should be closed on your virtual machines\nManagement ports of virtual machines should be protected with just-in-time network access control\nIP Forwarding on your virtual machine should be disabled\n[Preview]: All Internet traffic should be routed via your deployed Azure Firewall"}]}, {"id": "NS-4", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "12.6 - Deploy Network-Based IDS Sensors\n12.7 - Deploy Network-Based Intrusion Prevention Systems"}, {"framework": "CIS Controls v8 ID(s)", "control": "13.2 Deploy a Host-Based Intrusion Detection Solution\n13.3 - Deploy a Network Intrusion Detection Solution\n13.7 Deploy a Host-Based Intrusion Prevention Solution\n13.8 - Deploy a Network Intrusion Prevention Solution"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SC-7: BOUNDARY PROTECTION\nSI-4: INFORMATION SYSTEM MONITORING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "11.4"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "NS-5", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "9.5 - Implement Application Firewalls\n12.3 - Deny Communications with Known Malicious IP Addresses"}, {"framework": "CIS Controls v8 ID(s)", "control": "13.10 - Perform Application Layer Filtering"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SC-5: DE<PERSON><PERSON> OF SERVICE PROTECTION\nSC-7: BOUNDARY PROTECTION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "1.1\n1.2\n1.3\n6.6"}, {"framework": "Azure Policy Mapping", "control": "Azure DDoS Protection Standard should be enabled"}]}, {"id": "NS-6", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "9.5 - Implement Application Firewalls\n12.3 - Deny Communications with Known Malicious IP Addresses\n12.9 - Deploy Application Layer Filtering Proxy Server\n18.10 - Deploy Web Application Firewalls (WAFs)"}, {"framework": "CIS Controls v8 ID(s)", "control": "13.10 - Perform Application Layer Filtering"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SC-7: BOUNDARY PROTECTION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "1.1\n1.2\n1.3\n6.6"}, {"framework": "Azure Policy Mapping", "control": "Web Application Firewall (WAF) should be enabled for Azure Front Door Service service\nWeb Application Firewall (WAF) should be enabled for Application Gateway"}]}, {"id": "NS-7", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "9.2 - Ensure Only Approved Ports, Protocols and Services Are Running"}, {"framework": "CIS Controls v8 ID(s)", "control": "4.4 - Implement and Manage a Firewall on Severs\n4.8 - Uninstall or Disable Unnecessary Services on Enterprise Assets and Software"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-4: INFORMATION FLOW ENFORCEMENT\nSC-2: APPLICATION PARTITIONING\nSC-7: BOUNDARY PROTECTION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "1.1\n1.2\n1.3"}, {"framework": "Azure Policy Mapping", "control": "Adaptive network hardening recommendations should be applied on internet facing virtual machines"}]}, {"id": "NS-8", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "9.2 - Ensure Only Approved Ports, Protocols and Services Are Running"}, {"framework": "CIS Controls v8 ID(s)", "control": "4.4 - Implement and Manage a Firewall on Severs\n4.8 - Uninstall or Disable Unnecessary Services on Enterprise Assets and Software"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CM-2: <PERSON><PERSON><PERSON><PERSON>E CONFIGURATION\nCM-6: CONFIGURATION SETTINGS\nCM-7: LEAST FUNCTIONALITY"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "4.1\nA2.1\nA2.2\nA2.3"}, {"framework": "Azure Policy Mapping", "control": "Latest TLS version should be used in your API App\nLatest TLS version should be used in your Web App\nLatest TLS version should be used in your Function App"}]}, {"id": "NS-9", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v8 ID(s)", "control": "12.7 - Ensure Remote Devices Utilize a VPN and are Connecting to \nan Enterprise’s AAA Infrastructure"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CA-3: SYSTEM INTERCONNECTIONS\nAC-17: REMOTE ACCESS\nAC-4: INFORMATION FLOW ENFORCEMENT"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "PA-1", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "4.3 - Ensure the Use of Dedicated Administrative Accounts\n14.6 - Protect Information Through Access Control Lists"}, {"framework": "CIS Controls v8 ID(s)", "control": "5.4 - Restrict Administrator Privileges to Dedicated Administrator Accounts\n6.8 - Define and Maintain Role-Based Access Control"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT\nAC-6: LEAST PRIVILEGE"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "7.1\n7.2\n8.1"}, {"framework": "Azure Policy Mapping", "control": "There should be more than one owner assigned to your subscription\nA maximum of 3 owners should be designated for your subscription\nDeprecated accounts with owner permissions should be removed from your subscription\nExternal accounts with owner permissions should be removed from your subscription"}]}, {"id": "PA-2", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "N/A"}, {"framework": "Azure Policy Mapping", "control": "Management ports of virtual machines should be protected with just-in-time network access control"}]}, {"id": "PA-3", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "16.7 - Establish Process for Revoking Access"}, {"framework": "CIS Controls v8 ID(s)", "control": "6.1 - Establish an Access Granting Process\n6.2 - Establish an Access Revoking Process"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT\nAC-5: SEPARATION OF DUTIES\nAC-6: LEAST PRIVILEGE"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "7.1\n7.2\n8.1"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "PA-4", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "4.1 - Maintain Inventory of Administrative Accounts\n16.6 - Maintain an Inventory of Accounts\n16.8 - Disable Any Unassociated Accounts\nDisable <PERSON><PERSON><PERSON> Accounts\n16.9 - Disable <PERSON><PERSON><PERSON> Accounts"}, {"framework": "CIS Controls v8 ID(s)", "control": "5.1 - <PERSON><PERSON>blish and Maintain an Inventory of Accounts\n5.3 - <PERSON><PERSON> <PERSON><PERSON><PERSON> Accounts\n5.5 - <PERSON><PERSON>blish and Maintain an Inventory of Service Accounts"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT\nAC-6: LEAST PRIVILEGE"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "7.1\n7.2\n8.1\nA3.4"}, {"framework": "Azure Policy Mapping", "control": "External accounts with write permissions should be removed from your subscription\nExternal accounts with read permissions should be removed from your subscription\nDeprecated accounts should be removed from your subscription\nDeprecated accounts with owner permissions should be removed from your subscription\nExternal accounts with owner permissions should be removed from your subscription"}]}, {"id": "PA-5", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "PA-6", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "4.6 - Use Dedicated Workstations For All Administrative Tasks\n11.6 - Use Dedicated Machines For All Network Administrative Tasks\n12.12 - Manage All Devices Remotely Logging into Internal Network"}, {"framework": "CIS Controls v8 ID(s)", "control": "12.8 - Establish and Maintain Dedicated Computing Resources for All Administrative Work\n13.5 Manage Access Control for Remote Assets"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT\nSC-2 APPLICATION PARTITIONING\nSC-7: BOUNDARY PROTECTION"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "PA-7", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "14.6 - Protect Information Through Access Control Lists"}, {"framework": "CIS Controls v8 ID(s)", "control": "3.3 - Configure Data Access Control Lists\n6.8 - Define and Maintain Role-Based Access Control"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nAC-6: LEAST PRIVILEGE"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "7.1\n7.2"}, {"framework": "Azure Policy Mapping", "control": "Audit usage of custom RBAC rules\nRole-Based Access Control (RBAC) should be used on Kubernetes Services"}]}, {"id": "PA-8", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "16.7 - Establish Process for Revoking Access"}, {"framework": "CIS Controls v8 ID(s)", "control": "6.1 - Establish an Access Granting Process\n6.2 - Establish an Access Revoking Process"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-4: INFORMATION FLOW ENFORCEMENT\nAC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "PV-1", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "5.1 - Establish Secure Configurations\n11.1 - Maintain Standard Security Configurations for Network Devices"}, {"framework": "CIS Controls v8 ID(s)", "control": "4.1 - Establish and Maintain a Secure Configuration Process\n4.2 - Establish and Maintain a Secure Configuration Process for Network Infrastructure"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CM-2: <PERSON><PERSON><PERSON><PERSON>E CONFIGURATION\nCM-6: CONFIGURATION SETTINGS"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "1.1\n2.2"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "PV-2", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "5.4 - Deploy System Configuration Management Tools\n5.5 - Implement Automated Configuration Monitoring Systems\n11.3 - Use Automated Tools to Verify Standard Device Configurations and Detect Changes"}, {"framework": "CIS Controls v8 ID(s)", "control": "4.1 - Establish and Maintain a Secure Configuration Process\n4.2 - Establish and Maintain a Secure Configuration Process for Network Infrastructure"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CM-2: <PERSON><PERSON><PERSON><PERSON>E CONFIGURATION\nCM-6: CONFIGURATION SETTINGS"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "2.2"}, {"framework": "Azure Policy Mapping", "control": "CORS should not allow every resource to access your Function Apps\nKubernetes cluster pod hostPath volumes should only use allowed host paths\nAzure Policy Add-on for Kubernetes service (AKS) should be installed and enabled on your clusters\nEnsure API app has 'Client Certificates (Incoming client certificates)' set to 'On'\nRemote debugging should be turned off for Function Apps\nKubernetes clusters should not allow container privilege escalation\nKubernetes cluster services should listen only on allowed ports\nCORS should not allow every resource to access your API App\n[Preview]: Kubernetes clusters should disable automounting API credentials\nKubernetes cluster containers should only listen on allowed ports\nKubernetes cluster containers should not share host process ID or host IPC namespace\nKubernetes cluster containers should only use allowed AppArmor profiles\nCORS should not allow every resource to access your Web Applications\nOperating system version should be the most current version for your cloud service roles\nEnsure WEB app has 'Client Certificates (Incoming client certificates)' set to 'On'\nKubernetes cluster pods should only use approved host network and port range\nKubernetes cluster should not allow privileged containers\n[Preview]: Kubernetes clusters should not use the default namespace\nKubernetes cluster containers should only use allowed capabilities\nRemote debugging should be turned off for Web Applications\n[Preview]: Kubernetes clusters should not grant CAP_SYS_ADMIN security capabilities\nKubernetes cluster containers should run with a read only root file system\nKubernetes cluster containers CPU and memory resource limits should not exceed the specified limits\nRemote debugging should be turned off for API Apps\nFunction apps should have 'Client Certificates (Incoming client certificates)' enabled\nKubernetes cluster pods and containers should only run with approved user and group IDs\nKubernetes cluster containers should only use allowed images\nKubernetes clusters should gate deployment of vulnerable images"}]}, {"id": "PV-3", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "5.1 - Establish Secure Configurations\n5.5 - Implement Automated Configuration Monitoring Systems"}, {"framework": "CIS Controls v8 ID(s)", "control": "4.1 - Establish and Maintain a Secure Configuration Process"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CM-2: <PERSON><PERSON><PERSON><PERSON>E CONFIGURATION\nCM-6: CONFIGURATION SETTINGS"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "2.2\n11.5"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "PV-4", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "5.4 - Deploy System Configuration Management Tools\n5.5 - Implement Automated Configuration Monitoring Systems\n11.3 - Use Automated Tools to Verify Standard Device Configurations and Detect Changes"}, {"framework": "CIS Controls v8 ID(s)", "control": "4.1 - Establish and Maintain a Secure Configuration Process"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CM-2: <PERSON><PERSON><PERSON><PERSON>E CONFIGURATION\nCM-6: CONFIGURATION SETTINGS"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "2.2"}, {"framework": "Azure Policy Mapping", "control": "[Preview]: vTPM should be enabled on supported virtual machines\n[Preview]: Guest Attestation extension should be installed on supported Windows virtual machines\n[Preview]: Guest Attestation extension should be installed on supported Linux virtual machines\n[Preview]: Windows machines should meet requirements of the Azure compute security baseline\n[Preview]: Secure Boot should be enabled on supported Windows virtual machines\n[Preview]: Guest Attestation extension should be installed on supported Linux virtual machines scale sets\nGuest Configuration extension should be installed on your machines\nVirtual machines' Guest Configuration extension should be deployed with system-assigned managed identity\n[Preview]: Guest Attestation extension should be installed on supported Windows virtual machines scale sets\n[Preview]: Linux machines should meet requirements for the Azure compute security baseline"}]}, {"id": "PV-5", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "3.1 - Run Automated Vulnerability Scanning Tools\n3.3 - Protect Dedicated Assessment Accounts\n3.6 - Compare Back-to-back Vulnerability Scans"}, {"framework": "CIS Controls v8 ID(s)", "control": "5.5 - Establish and Maintain an Inventory of Service Accounts\n7.1 - Establish and Maintain a Vulnerability Management Process\n7.5 - Perform Automated Vulnerability Scans of Internal Enterprise Assets\n7.6 - Perform Automated Vulnerability Scans of Externally-Exposed Enterprise Assets"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "RA-3: <PERSON><PERSON><PERSON> ASSESSMENT\nRA-5: VULNERABILITY SCANNING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "6.1 \n6.2\n6.6\n11.2"}, {"framework": "Azure Policy Mapping", "control": "Vulnerability assessment should be enabled on SQL Managed Instance\nA vulnerability assessment solution should be enabled on your virtual machines\nVulnerability assessment should be enabled on your SQL servers"}]}, {"id": "PV-6", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "3.4 - Deploy Automated Operating System Patch Management Tools\n3.5 - Deploy Automated Software Patch Management Tools\n3.7 - Utilize a Risk-rating Process"}, {"framework": "CIS Controls v8 ID(s)", "control": "7.2 - Establish and Maintain a Remediation Process\n7.3 - Perform Automated Operating System Patch Management\n7.4 - Perform Automated Application Patch Management\n7.7 - Remediate Detected Vulnerabilities"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "RA-3: <PERSON><PERSON><PERSON> ASSESSMENT\nRA-5: VU<PERSON>NERABI<PERSON>ITY SCANNING\nSI-2: FLAW REMEDIATION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "6.1\n6.2\n6.5\n11.2"}, {"framework": "Azure Policy Mapping", "control": "Ensure that 'PHP version' is the latest, if used as a part of the Api app\nEnsure that 'PHP version' is the latest, if used as a part of the API app\nVulnerabilities in security configuration on your virtual machine scale sets should be remediated\nEnsure that 'Java version' is the latest, if used as a part of the Web app\nEnsure that 'Java version' is the latest, if used as a part of the Web app\nVulnerabilities in Azure Container Registry images should be remediated\nSQL servers on machines should have vulnerability findings resolved\nEnsure that 'Python version' is the latest, if used as a part of the Web app\nEnsure that 'Python version' is the latest, if used as a part of the Web app\nEnsure that 'Python version' is the latest, if used as a part of the Function app\nEnsure that 'Python version' is the latest, if used as a part of the Function app\nEnsure that 'PHP version' is the latest, if used as a part of the WEB app\nEnsure that 'PHP version' is the latest, if used as a part of the WEB app\nEnsure that 'Python version' is the latest, if used as a part of the Api app\nEnsure that 'Python version' is the latest, if used as a part of the API app\nVulnerabilities should be remediated by a Vulnerability Assessment solution\nSystem updates should be installed on your machines\nEnsure that 'Java version' is the latest, if used as a part of the Api app\nEnsure that 'Java version' is the latest, if used as a part of the API app\nEnsure that 'Java version' is the latest, if used as a part of the Function app\nEnsure that 'Java version' is the latest, if used as a part of the Function app\nSystem updates on virtual machine scale sets should be installed\nVulnerabilities in security configuration on your machines should be remediated\nVulnerabilities in container security configurations should be remediated\n[Preview]: Kubernetes Services should be upgraded to a non-vulnerable Kubernetes version\nSQL databases should have vulnerability findings resolved"}]}, {"id": "PV-7", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "20.1 - Establish a Penetration Testing Program\n20.2 - Conduct Regular External and Internal Penetration Tests\n20.3 - Perform Periodic Red Team Exercises"}, {"framework": "CIS Controls v8 ID(s)", "control": "18.1 - Establish and Maintain a Penetration Testing Program\n18.2 - Perform Periodic External Penetration Tests\n18.3 - Remediate Penetration Test Findings\n18.4 - Validate Security Measures\n18.5 - Perform Periodic Internal Penetration Tests"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CA-8: PENETRA<PERSON>ON TESTING\nRA-5: VULNERABILITY SCANNING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "6.6\n11.2\n11.3"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}]}