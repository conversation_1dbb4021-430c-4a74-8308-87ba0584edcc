File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
network_demo.tf,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,24.0,"The security_rule in azurerm_network_security_group.demo allows inbound SSH (port 22) from any source (source_address_prefix = ""0.0.0.0/0""), exposing the resource to the entire internet. This enables initial access for attackers, allowing brute-force or credential-based attacks, and significantly increases the blast radius for lateral movement within the network.","Restrict the source_address_prefix for SSH to trusted IP ranges only (e.g., your corporate IP or a jump host). Change source_address_prefix from ""0.0.0.0/0"" to a specific CIDR block. Consider implementing just-in-time (JIT) access for management ports and ensure deny-by-default for all other inbound traffic.",,,,ai_analysis,,Validated
network_demo.tf,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,43.0,"The allow_blob_public_access property in azurerm_storage_account.demo is set to true, enabling public anonymous access to blobs. This exposes storage data to the internet, allowing attackers to enumerate, read, or exfiltrate sensitive data without authentication, greatly increasing the data exposure blast radius.",Set allow_blob_public_access to false to disable anonymous public access. Use private endpoints and restrict access to trusted networks only. Review and update access policies to enforce authentication and authorization for all blob data.,,,,ai_analysis,,Validated
network_demo.tf,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,46.0,"The https_traffic_only property in azurerm_storage_account.demo is set to false, allowing unencrypted HTTP connections. This exposes data in transit to interception and man-in-the-middle attacks, enabling attackers to read or modify sensitive data as it traverses the network.",Set https_traffic_only to true to enforce encryption for all data in transit. Ensure all clients and applications use HTTPS endpoints when accessing the storage account.,,,,ai_analysis,,Validated
storage_demo.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,26.0,"The 'minimumTlsVersion' property is set to 'TLS1_0', which enables weak encryption for data in transit. Attackers can exploit known vulnerabilities in TLS 1.0 to intercept or modify data between clients and the storage account, leading to data exfiltration or tampering. The blast radius includes all data transferred to and from this storage account.",Set 'minimumTlsVersion' to 'TLS1_2' or higher in the storage account properties to enforce strong encryption for data in transit. Example: minimumTlsVersion: 'TLS1_2'.,,,,ai_analysis,,Validated
storage_demo.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,23.0,"The 'supportsHttpsTrafficOnly' property is set to false, allowing unencrypted HTTP traffic. This exposes data in transit to interception and man-in-the-middle attacks, enabling attackers to steal or manipulate sensitive information. The blast radius includes all data accessed or uploaded via HTTP.",Set 'supportsHttpsTrafficOnly' to true in the storage account properties to enforce HTTPS-only access. Example: supportsHttpsTrafficOnly: true.,,,,ai_analysis,,Validated
storage_demo.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,17.0,"The 'allowBlobPublicAccess' property is set to true, enabling anonymous public access to blob data. Attackers can access and exfiltrate any data stored in public containers without authentication, resulting in potential data breaches. The blast radius includes all blobs in containers with public access enabled.",Set 'allowBlobPublicAccess' to false in the storage account properties to disable anonymous public access. Example: allowBlobPublicAccess: false.,,,,ai_analysis,,Validated
storage_demo.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,40.0,"The 'networkAcls.defaultAction' property is set to 'Allow', which permits unrestricted public network access to the storage account. This exposes the storage account to the internet, enabling attackers to attempt unauthorized access, brute force, or data exfiltration. The blast radius includes all data and services within the storage account.","Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only trusted networks or private endpoints. Example: networkAcls: { defaultAction: 'Deny', ... }.",,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 7,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 0,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T15:12:55.682354,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
