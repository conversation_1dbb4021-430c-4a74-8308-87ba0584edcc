# Core dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database
asyncpg==0.29.0
sqlalchemy[asyncio]==2.0.23
alembic==1.13.1

# Caching
redis==5.0.1
aioredis==2.0.1

# AI Processing
openai==1.3.7
azure-openai==1.3.7
tiktoken==0.5.2

# Data processing
pandas==2.1.4
openpyxl==3.1.2
xlrd==2.0.1

# HTTP client
httpx==0.25.2
aiohttp==3.9.1

# Utilities
python-dotenv==1.0.0
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Logging and monitoring
structlog==23.2.0
prometheus-client==0.19.0
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation-fastapi==0.42b0
opentelemetry-instrumentation-sqlalchemy==0.42b0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
httpx==0.25.2
factory-boy==3.3.0

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# Documentation
mkdocs==1.5.3
mkdocs-material==9.4.8
mkdocs-mermaid2-plugin==1.1.1

# Deployment
gunicorn==21.2.0
docker==6.1.3
kubernetes==28.1.0
