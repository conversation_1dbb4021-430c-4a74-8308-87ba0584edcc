#!/usr/bin/env python3
"""
Demo script to test real file content in code dialogs.
This creates a permanent test file and generates a report to verify real content display.
"""

import os
import sys
from pathlib import Path
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from security_opt import SecurityPRReviewer

def create_permanent_test_file():
    """Create a permanent test file that won't be deleted."""
    
    # Create a demo directory
    demo_dir = "demo_files"
    os.makedirs(demo_dir, exist_ok=True)
    
    # Create a Bicep file with real security issues
    bicep_file = os.path.join(demo_dir, "storage_demo.bicep")
    
    bicep_content = """// Demo Azure Storage Account with Security Issues
// This file demonstrates real file content in code dialogs

param storageAccountName string = 'demostorage${uniqueString(resourceGroup().id)}'
param location string = resourceGroup().location

// Storage Account with intentional security issues for demonstration
resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {
  name: storageAccountName
  location: location
  kind: 'StorageV2'
  sku: {
    name: 'Standard_LRS'
  }
  properties: {
    // 🚨 SECURITY ISSUE: Public blob access enabled (Line 16)
    allowBlobPublicAccess: true
    
    // 🚨 SECURITY ISSUE: Weak TLS version (Line 19)
    minimumTlsVersion: 'TLS1_0'
    
    // 🚨 SECURITY ISSUE: HTTP traffic allowed (Line 22)
    supportsHttpsTrafficOnly: false
    
    // Good configuration
    encryption: {
      services: {
        blob: {
          enabled: true
        }
        file: {
          enabled: true
        }
      }
      keySource: 'Microsoft.Storage'
    }
    
    // 🚨 SECURITY ISSUE: Default network access (Line 37)
    networkAcls: {
      defaultAction: 'Allow'
    }
  }
  
  tags: {
    Environment: 'Demo'
    Purpose: 'Real Content Testing'
    CreatedBy: 'IaC Guardian'
  }
}

// Output
output storageAccountId string = storageAccount.id
output storageAccountName string = storageAccount.name"""

    with open(bicep_file, 'w', encoding='utf-8') as f:
        f.write(bicep_content)
    
    # Create a Terraform file
    tf_file = os.path.join(demo_dir, "network_demo.tf")
    
    tf_content = """# Demo Network Security Group with Security Issues
# This file demonstrates real file content in code dialogs

resource "azurerm_resource_group" "demo" {
  name     = "rg-demo-security"
  location = "East US"
}

# Network Security Group with security issues
resource "azurerm_network_security_group" "demo" {
  name                = "nsg-demo-security"
  location            = azurerm_resource_group.demo.location
  resource_group_name = azurerm_resource_group.demo.name

  # 🚨 SECURITY ISSUE: SSH from anywhere (Line 16)
  security_rule {
    name                       = "AllowSSH"
    priority                   = 1001
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "22"
    source_address_prefix      = "0.0.0.0/0"  # CRITICAL: Open to internet
    destination_address_prefix = "*"
  }

  tags = {
    Environment = "Demo"
    Purpose     = "Real Content Testing"
  }
}

# Storage account with issues
resource "azurerm_storage_account" "demo" {
  name                     = "demostorageaccount"
  resource_group_name      = azurerm_resource_group.demo.name
  location                 = azurerm_resource_group.demo.location
  account_tier             = "Standard"
  account_replication_type = "LRS"

  # 🚨 SECURITY ISSUE: Public blob access (Line 40)
  allow_blob_public_access = true
  
  # 🚨 SECURITY ISSUE: HTTP allowed (Line 43)
  https_traffic_only = false

  tags = {
    Environment = "Demo"
    Purpose     = "Real Content Testing"
  }
}"""

    with open(tf_file, 'w', encoding='utf-8') as f:
        f.write(tf_content)
    
    return [bicep_file, tf_file]

def create_demo_findings(file_paths):
    """Create demo findings that reference the real files."""
    
    findings = [
        {
            "control_id": "DEMO-01",
            "severity": "HIGH",
            "file_path": file_paths[0],  # Bicep file
            "line": 16,
            "description": "Storage account allows public blob access. This creates a significant data exposure risk by allowing anonymous access to all blobs.",
            "remediation": "Set allowBlobPublicAccess to false and implement proper access controls using private endpoints or SAS tokens.",
            "domain": "Data Protection"
        },
        {
            "control_id": "DEMO-02",
            "severity": "MEDIUM",
            "file_path": file_paths[0],  # Bicep file
            "line": 19,
            "description": "Storage account configured with weak TLS version (TLS1_0). This version has known security vulnerabilities.",
            "remediation": "Update minimumTlsVersion to 'TLS1_2' or higher to ensure secure data transmission.",
            "domain": "Data Protection"
        },
        {
            "control_id": "DEMO-03",
            "severity": "HIGH",
            "file_path": file_paths[0],  # Bicep file
            "line": 22,
            "description": "Storage account allows HTTP traffic (supportsHttpsTrafficOnly: false). This permits unencrypted data transmission.",
            "remediation": "Set supportsHttpsTrafficOnly to true to enforce encrypted HTTPS connections.",
            "domain": "Data Protection"
        },
        {
            "control_id": "DEMO-04",
            "severity": "CRITICAL",
            "file_path": file_paths[1],  # Terraform file
            "line": 24,
            "description": "Network Security Group allows SSH access from any source (0.0.0.0/0). This exposes SSH to internet-based attacks.",
            "remediation": "Restrict SSH access to specific IP ranges or use Azure Bastion for secure access.",
            "domain": "Network Security"
        },
        {
            "control_id": "DEMO-05",
            "severity": "HIGH",
            "file_path": file_paths[1],  # Terraform file
            "line": 40,
            "description": "Storage account allows public blob access. This exposes storage data to the internet without authentication.",
            "remediation": "Set allow_blob_public_access to false and implement proper access controls.",
            "domain": "Data Protection"
        }
    ]
    
    return findings

def main():
    """Main demo function."""
    
    print("🎬 Creating Real File Content Demo...")
    
    # Create permanent test files
    file_paths = create_permanent_test_file()
    print(f"✅ Created demo files:")
    for file_path in file_paths:
        file_size = os.path.getsize(file_path)
        with open(file_path, 'r') as f:
            line_count = sum(1 for _ in f)
        print(f"   📄 {file_path} ({line_count} lines, {file_size:,} bytes)")
    
    # Create demo findings
    findings = create_demo_findings(file_paths)
    print(f"✅ Created {len(findings)} demo findings")
    
    # Initialize reviewer
    reviewer = SecurityPRReviewer(local_folder="./")
    
    # Generate HTML report
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    html_path = f"real_content_demo_{timestamp}.html"
    
    print(f"📄 Generating demo report with real file content...")
    reviewer._export_findings_to_html(findings, html_path)
    
    print(f"✅ Demo report generated: {html_path}")
    print(f"💾 File size: {os.path.getsize(html_path):,} bytes")
    
    print(f"\n🎯 Demo Instructions:")
    print(f"   1. Open {html_path} in your browser")
    print(f"   2. Click any 'View Code' button")
    print(f"   3. Open browser developer console (F12)")
    print(f"   4. Look for debug messages starting with 🔍, 📄, 🎯, 📝")
    print(f"   5. Verify that real file content is displayed (not sample code)")
    print(f"   6. Check that line numbers are accurate")
    print(f"   7. Notice ±100 lines of context around each issue")
    
    print(f"\n📁 Demo files will remain in 'demo_files/' directory for testing")
    print(f"   You can examine the actual files to verify content accuracy")
    
    return html_path

if __name__ == "__main__":
    try:
        html_path = main()
        
        # Optional: Open in browser
        import webbrowser
        user_input = input(f"\n🚀 Open the demo report in your browser? (y/n): ")
        if user_input.lower() in ['y', 'yes']:
            webbrowser.open(f"file://{os.path.abspath(html_path)}")
            print("🌐 Demo report opened in your browser!")
            print("\n🔍 Check the browser console for debug messages when clicking 'View Code'")
        
    except Exception as e:
        print(f"❌ Error during demo: {str(e)}")
        import traceback
        traceback.print_exc()
