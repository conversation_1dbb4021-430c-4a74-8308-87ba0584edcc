import unittest
from template_parameter_expander import TemplateParameterExpander
from security_opt import SecurityPRReviewer
import os
from pathlib import Path
import json

class TestTemplateParameterExpander(unittest.TestCase):
    def setUp(self):
        self.expander = TemplateParameterExpander()
        self.test_files_dir = Path("test-files")
        self.test_files_dir.mkdir(exist_ok=True)
        # Save original environment variables
        self.original_env = {
            'TEMPLATE_PATTERNS': os.getenv('TEMPLATE_PATTERNS'),
            'PARAMETER_PATTERNS': os.getenv('PARAMETER_PATTERNS'),
            'TEMPLATE_IDENTIFIERS': os.getenv('TEMPLATE_IDENTIFIERS'),
            'PARAMETER_IDENTIFIERS': os.getenv('PARAMETER_IDENTIFIERS')
        }

    def tearDown(self):
        # Restore original environment variables
        for key, value in self.original_env.items():
            if value is None:
                os.environ.pop(key, None)
            else:
                os.environ[key] = value

    def test_pattern_config_loading(self):
        """Test loading of pattern configuration from environment variables"""
        # Set custom patterns
        os.environ['TEMPLATE_PATTERNS'] = 'custom*.json,*.template'
        os.environ['PARAMETER_PATTERNS'] = 'custom*.params,*.parameters'
        os.environ['TEMPLATE_IDENTIFIERS'] = 'CustomTemplate,Template'
        os.environ['PARAMETER_IDENTIFIERS'] = 'CustomParam,Param'

        config = self.expander._get_pattern_config()

        self.assertEqual(config['template_patterns'], ['custom*.json', '*.template'])
        self.assertEqual(config['param_patterns'], ['custom*.params', '*.parameters'])
        self.assertEqual(config['template_identifiers'], ['CustomTemplate', 'Template'])
        self.assertEqual(config['param_identifiers'], ['CustomParam', 'Param'])

    def test_sensitive_parameters(self):
        """Test detection of sensitive parameters not using secureString"""
        # Test parameter file with sensitive parameters not using secureString
        param_content = {
            "parameters": {
                "adminPassword": {
                    "type": "string",  # Should be secureString
                    "value": "someValue"
                },
                "clientSecret": {
                    "type": "string",  # Should be secureString
                    "value": "someSecret"
                },
                "normalParam": {
                    "type": "string",  # This is fine
                    "value": "normalValue"
                },
                "apiKey": {
                    "type": "string",  # Should be secureString
                    "value": "someKey"
                }
            }
        }
        
        param_file = self.test_files_dir / 'test.parameters.json'
        param_file.write_text(json.dumps(param_content))
        
        # Test the sensitive parameter detection
        with open(param_file, 'r') as f:
            content = f.read()
        self.expander._check_sensitive_parameters(str(param_file), content)
        
        findings = self.expander.get_security_findings()
        
        # Should find 3 issues (adminPassword, clientSecret, apiKey)
        self.assertEqual(len(findings), 3)
        
        # Verify finding details
        sensitive_params = {'adminPassword', 'clientSecret', 'apiKey'}
        found_params = {finding["description"].split("'")[1] for finding in findings}
        self.assertEqual(sensitive_params, found_params)
        
        # Verify finding properties
        for finding in findings:
            self.assertEqual(finding["severity"], "HIGH")
            self.assertEqual(finding["control_id"], "DP-3")
            self.assertTrue("secureString" in finding["remediation"])

    def test_parameter_file_matching_with_patterns(self):
        """Test parameter file matching using configured patterns"""
        # Create test files
        self.test_files_dir.joinpath('template1.CustomTemplate.json').write_text('{}')
        self.test_files_dir.joinpath('template1.CustomParam.json').write_text('{"parameters":{}}')
        
        os.environ['TEMPLATE_IDENTIFIERS'] = 'CustomTemplate'
        os.environ['PARAMETER_IDENTIFIERS'] = 'CustomParam'

        template_path = str(self.test_files_dir / 'template1.CustomTemplate.json')
        param_files = self.expander._find_matching_parameter_files(template_path)

        self.assertEqual(len(param_files), 1)
        self.assertTrue(any('template1.CustomParam.json' in f for f in param_files))

    def test_default_pattern_fallback(self):
        """Test fallback to default patterns when env vars not set"""
        # Create test files
        self.test_files_dir.joinpath('test.json').write_text('{}')
        self.test_files_dir.joinpath('test.parameters.json').write_text('{"parameters":{}}')
        
        # Clear environment variables
        for key in ['TEMPLATE_PATTERNS', 'PARAMETER_PATTERNS', 
                   'TEMPLATE_IDENTIFIERS', 'PARAMETER_IDENTIFIERS']:
            os.environ.pop(key, None)

        template_path = str(self.test_files_dir / 'test.json')
        param_files = self.expander._find_matching_parameter_files(template_path)

        self.assertEqual(len(param_files), 1)
        self.assertTrue(any('test.parameters.json' in f for f in param_files))

    def test_custom_pattern_matching(self):
        """Test matching with custom glob patterns"""
        # Create test files
        self.test_files_dir.joinpath('custom.deploy.json').write_text('{}')
        self.test_files_dir.joinpath('custom.config.json').write_text('{"parameters":{}}')
        
        os.environ['TEMPLATE_PATTERNS'] = '*.deploy.json'
        os.environ['PARAMETER_PATTERNS'] = '*.config.json'

        template_path = str(self.test_files_dir / 'custom.deploy.json')
        param_files = self.expander._find_matching_parameter_files(template_path)

        self.assertEqual(len(param_files), 1)
        self.assertTrue(any('custom.config.json' in f for f in param_files))

    def test_fabric_app_template_matching(self):
        # Test template and parameter with your naming convention
        template_content = """
{
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
    "contentVersion": "1.0.0.0",
    "parameters": {
        "appName": {
            "type": "string",
            "defaultValue": "default-app"
        },
        "location": {
            "type": "string",
            "defaultValue": "eastus"
        }
    },
    "resources": [
        {
            "type": "Microsoft.ServiceFabric/managedclusters/applications",
            "apiVersion": "2021-05-01",
            "name": "[parameters('appName')]",
            "location": "[parameters('location')]",
            "properties": {}
        }
    ]
}
"""
        param_content = """
{
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#",
    "contentVersion": "1.0.0.0",
    "parameters": {
        "appName": {
            "value": "test-fabric-app"
        },
        "location": {
            "value": "westus"
        }
    }
}
"""
        # Test template expansion
        expanded_content, used_params = self.expander.expand_template(template_content, param_content)
        
        # Verify parameter values were correctly applied
        self.assertIn("test-fabric-app", expanded_content)
        self.assertIn("westus", expanded_content)
        self.assertEqual(used_params.get("appName"), "test-fabric-app")
        self.assertEqual(used_params.get("location"), "westus")

    def test_parameter_file_matching(self):
        """Test the complete template-parameter matching workflow"""
        # Initialize SecurityPRReviewer with test directory
        reviewer = SecurityPRReviewer(local_folder=str(self.test_files_dir))
        
        # Analyze the test directory
        files = reviewer.analyze_folder(str(self.test_files_dir))
        
        # Verify file detection
        self.assertTrue(any(f["path"].endswith("ArmTemplate-FabricAppA.json") for f in files))
        self.assertTrue(any(f["path"].endswith("ArmParam-FabricAppA.json") for f in files))
        
        # Test match_template_parameter_files
        matched_files = reviewer._match_template_parameter_files(files)
        
        # Find our test template in the matches
        template_path = None
        for path, info in matched_files.items():
            if "FabricAppA" in path:
                template_path = path
                break
        
        self.assertIsNotNone(template_path, "Template file not found in matches")
        template_info = matched_files[template_path]
        
        # Verify parameter file was matched
        self.assertEqual(len(template_info["parameters"]), 1, "Expected exactly one parameter file match")
        param_file = template_info["parameters"][0]
        self.assertTrue("FabricAppA" in param_file["path"], "Parameter file not matched correctly")
        
        # Test security analysis
        findings = reviewer.analyze_files(files)
        
        # Verify security findings
        has_https_finding = False
        has_tls_finding = False
        
        for finding in findings:
            if "enableHttps" in finding.get("description", ""):
                has_https_finding = True
            if "TLS1_1" in finding.get("description", ""):
                has_tls_finding = True
        
        self.assertTrue(has_https_finding, "Should detect HTTPS disabled security issue")
        self.assertTrue(has_tls_finding, "Should detect TLS 1.1 security issue")
    
    def test_expanded_template_analysis(self):
        """Test security analysis with expanded templates"""
        # Initialize reviewer with parameter expansion enabled
        os.environ["ENABLE_PARAMETER_EXPANSION"] = "true"
        reviewer = SecurityPRReviewer(local_folder=str(self.test_files_dir))
        
        # Analyze folder which triggers expansion
        files = reviewer.analyze_folder(str(self.test_files_dir))
        
        # Verify we have expanded templates
        has_expanded = False
        for file_info in files:
            if "original_content" in file_info and "parameters_used" in file_info:
                has_expanded = True
                # Verify parameter values were applied
                self.assertIn("prod-fabric-app", file_info["content"])
                self.assertIn("westus", file_info["content"])
                break
        
        self.assertTrue(has_expanded, "Should find expanded templates")
        
        # Analyze the expanded files
        findings = reviewer.analyze_files(files)
        
        # Look for findings that reference parameter values
        has_param_context = False
        for finding in findings:
            if "parameter_file" in finding and "parameters_used" in finding:
                has_param_context = True
                break
        
        self.assertTrue(has_param_context, "Findings should include parameter context")

    def test_logging(self):
        """Test that logging output is generated correctly"""
        # Set up logging verification
        import logging
        import io
        
        # Create a string buffer to capture log output
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        logger = logging.getLogger()
        logger.addHandler(handler)
        
        try:
            # Initialize reviewer and trigger template matching
            reviewer = SecurityPRReviewer(local_folder=str(self.test_files_dir))
            files = reviewer.analyze_folder(str(self.test_files_dir))
            
            # Get log output
            log_output = log_stream.getvalue()
            
            # Verify key log messages are present
            self.assertIn("Starting template-parameter matching process", log_output)
            self.assertIn("Categorizing files", log_output)
            self.assertIn("Identified template file:", log_output)
            self.assertIn("Identified parameter file:", log_output)
            self.assertIn("Matching files using strategy:", log_output)
            self.assertIn("✓ Matched", log_output)  # Check for match indicators
            self.assertIn("Matching Summary", log_output)
            
            # Verify specific file matching logs
            self.assertIn("FabricAppA", log_output)  # Our test file should be mentioned
            
            # Check for step-by-step matching process logs
            if reviewer.matching_strategy == "smart":
                self.assertIn("Using smart matching", log_output)
                
            # Verify no errors were logged
            self.assertNotIn("ERROR", log_output.upper())
            
        finally:
            # Clean up logging
            logger.removeHandler(handler)
            log_stream.close()

if __name__ == '__main__':
    unittest.main()
