#!/usr/bin/env python3
"""
Test cases for IaC Guardian security analysis prompt optimizations.
Validates false positive reduction and consistency improvements.
"""

import os
import sys
import json
import tempfile
from pathlib import Path
from typing import Dict, List, Any

# Add current directory to path
sys.path.append('.')

def create_test_cases() -> Dict[str, Dict[str, Any]]:
    """Create test cases with known false positive scenarios."""
    
    test_cases = {
        "false_positive_secret_variables": {
            "description": "Variables with 'secret' in name that are configuration, not secrets",
            "terraform_content": '''
variable "secret_mode" {
  description = "UI display mode for secrets"
  type        = bool
  default     = false
}

variable "show_secret_fields" {
  description = "Whether to show secret input fields in UI"
  type        = bool
  default     = true
}

variable "secret_config_name" {
  description = "Name of the secret configuration section"
  type        = string
  default     = "app-secrets-config"
}

resource "azurerm_storage_account" "example" {
  name                     = "examplestorageacc"
  resource_group_name      = azurerm_resource_group.example.name
  location                 = azurerm_resource_group.example.location
  account_tier             = "Standard"
  account_replication_type = "LRS"
  
  # This should NOT trigger false positives
  tags = {
    secret_mode = var.secret_mode
    show_secrets = var.show_secret_fields
    config_name = var.secret_config_name
  }
}
''',
            "expected_false_positives": [
                "secret_mode", "show_secret_fields", "secret_config_name"
            ],
            "expected_real_issues": [],
            "resource_type": "Storage"
        },
        
        "ui_configuration_patterns": {
            "description": "UI configuration that should not trigger security warnings",
            "bicep_content": '''
param secretDisplayMode string = 'hidden'
param showPasswordField bool = false
param keyInputType string = 'password'
param authFormVisible bool = true

resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {
  name: 'examplestorage'
  location: resourceGroup().location
  sku: {
    name: 'Standard_LRS'
  }
  kind: 'StorageV2'
  properties: {
    // These are UI configuration, not security issues
    tags: {
      secretDisplay: secretDisplayMode
      showPassword: showPasswordField
      keyInput: keyInputType
      authVisible: authFormVisible
    }
  }
}
''',
            "expected_false_positives": [
                "secretDisplayMode", "showPasswordField", "keyInputType", "authFormVisible"
            ],
            "expected_real_issues": [],
            "resource_type": "Storage"
        },
        
        "mixed_real_and_false_positives": {
            "description": "Mix of real security issues and false positives",
            "terraform_content": '''
# False positive - UI configuration
variable "secret_visibility" {
  description = "UI visibility setting for secret fields"
  type        = bool
  default     = false
}

# Real issue - actual secret in plain text
variable "database_password" {
  description = "Database password"
  type        = string
  default     = "hardcoded_password_123"  # This is a real security issue
}

# False positive - configuration name
variable "secret_store_name" {
  description = "Name of the secret store configuration"
  type        = string
  default     = "app-secret-store"
}

resource "azurerm_storage_account" "example" {
  name                     = "examplestorageacc"
  resource_group_name      = azurerm_resource_group.example.name
  location                 = azurerm_resource_group.example.location
  account_tier             = "Standard"
  account_replication_type = "LRS"
  
  # Real security issue - HTTP traffic allowed
  enable_https_traffic_only = false
  
  tags = {
    secret_ui = var.secret_visibility
    store_name = var.secret_store_name
  }
}
''',
            "expected_false_positives": [
                "secret_visibility", "secret_store_name"
            ],
            "expected_real_issues": [
                "database_password", "enable_https_traffic_only"
            ],
            "resource_type": "Storage"
        },
        
        "consistency_test": {
            "description": "Test case for consistency across multiple runs",
            "terraform_content": '''
variable "secret_mode_enabled" {
  description = "Enable secret mode in UI"
  type        = bool
  default     = true
}

variable "api_key_name" {
  description = "Name of the API key configuration"
  type        = string
  default     = "primary-api-key"
}

resource "azurerm_key_vault" "example" {
  name                = "example-keyvault"
  location            = azurerm_resource_group.example.location
  resource_group_name = azurerm_resource_group.example.name
  tenant_id           = data.azurerm_client_config.current.tenant_id
  
  # Real security issue - purge protection disabled
  purge_protection_enabled = false
  
  tags = {
    secret_mode = var.secret_mode_enabled
    key_config = var.api_key_name
  }
}
''',
            "expected_false_positives": [
                "secret_mode_enabled", "api_key_name"
            ],
            "expected_real_issues": [
                "purge_protection_enabled"
            ],
            "resource_type": "KeyVault",
            "consistency_runs": 3  # Run multiple times to test consistency
        }
    }
    
    return test_cases

def run_analysis_test(test_name: str, test_case: Dict[str, Any]) -> Dict[str, Any]:
    """Run analysis on a test case and return results."""
    try:
        from security_opt import SecurityPRReviewer
        
        # Set environment variables for optimal analysis
        os.environ.setdefault('ENFORCE_DOMAIN_PRIORITY', 'true')
        os.environ.setdefault('USE_OPTIMIZED_PROMPTS', 'true')
        os.environ.setdefault('ANALYSIS_SEED', '42')
        os.environ.setdefault('ENABLE_DETERMINISTIC_ANALYSIS', 'true')
        
        # Create temporary file with test content
        content_key = 'terraform_content' if 'terraform_content' in test_case else 'bicep_content'
        content = test_case[content_key]
        file_extension = '.tf' if content_key == 'terraform_content' else '.bicep'
        
        with tempfile.NamedTemporaryFile(mode='w', suffix=file_extension, delete=False) as f:
            f.write(content)
            temp_file_path = f.name
        
        try:
            # Initialize reviewer
            reviewer = SecurityPRReviewer(local_folder='.')
            
            # Prepare file info
            file_info = {
                "path": temp_file_path,
                "content": content,
                "resource_type": test_case.get("resource_type", "Generic"),
                "extension": file_extension
            }
            
            # Run analysis
            findings = reviewer.analyze_files([file_info])
            
            # Analyze results
            result = {
                "test_name": test_name,
                "description": test_case["description"],
                "total_findings": len(findings),
                "findings": findings,
                "false_positive_analysis": analyze_false_positives(
                    findings, 
                    test_case.get("expected_false_positives", []),
                    test_case.get("expected_real_issues", [])
                )
            }
            
            return result
            
        finally:
            # Clean up temporary file
            os.unlink(temp_file_path)
            
    except Exception as e:
        return {
            "test_name": test_name,
            "error": str(e),
            "success": False
        }

def analyze_false_positives(findings: List[Dict], expected_false_positives: List[str], 
                          expected_real_issues: List[str]) -> Dict[str, Any]:
    """Analyze findings to identify false positives and missed real issues."""
    
    # Extract variable names and issues from findings
    finding_variables = []
    finding_issues = []
    
    for finding in findings:
        description = finding.get("description", "").lower()
        
        # Check for variable-related findings
        for expected_fp in expected_false_positives:
            if expected_fp.lower() in description:
                finding_variables.append(expected_fp)
        
        # Check for real security issues
        for expected_issue in expected_real_issues:
            if expected_issue.lower() in description:
                finding_issues.append(expected_issue)
    
    # Calculate metrics
    false_positives_found = len(finding_variables)
    real_issues_found = len(finding_issues)
    false_positives_expected = len(expected_false_positives)
    real_issues_expected = len(expected_real_issues)
    
    # Calculate false positive reduction rate
    fp_reduction_rate = max(0, (false_positives_expected - false_positives_found) / false_positives_expected) if false_positives_expected > 0 else 1.0
    
    # Calculate real issue detection rate
    real_issue_detection_rate = real_issues_found / real_issues_expected if real_issues_expected > 0 else 1.0
    
    return {
        "false_positives_expected": false_positives_expected,
        "false_positives_found": false_positives_found,
        "false_positive_reduction_rate": fp_reduction_rate,
        "real_issues_expected": real_issues_expected,
        "real_issues_found": real_issues_found,
        "real_issue_detection_rate": real_issue_detection_rate,
        "false_positive_variables": finding_variables,
        "real_issues_detected": finding_issues
    }

def test_consistency(test_case: Dict[str, Any]) -> Dict[str, Any]:
    """Test consistency across multiple analysis runs."""
    consistency_runs = test_case.get("consistency_runs", 3)
    results = []
    
    for run in range(consistency_runs):
        print(f"  Running consistency test {run + 1}/{consistency_runs}...")
        result = run_analysis_test(f"consistency_run_{run + 1}", test_case)
        results.append(result)
    
    # Analyze consistency
    if len(results) < 2:
        return {"consistency_score": 1.0, "results": results}
    
    # Compare findings across runs
    first_run_findings = results[0].get("findings", [])
    consistency_scores = []
    
    for i in range(1, len(results)):
        current_findings = results[i].get("findings", [])
        similarity = calculate_findings_similarity(first_run_findings, current_findings)
        consistency_scores.append(similarity)
    
    avg_consistency = sum(consistency_scores) / len(consistency_scores) if consistency_scores else 1.0
    
    return {
        "consistency_score": avg_consistency,
        "consistency_scores": consistency_scores,
        "results": results,
        "consistent": avg_consistency >= 0.95  # 95% consistency threshold
    }

def calculate_findings_similarity(findings1: List[Dict], findings2: List[Dict]) -> float:
    """Calculate similarity between two sets of findings."""
    if not findings1 and not findings2:
        return 1.0
    
    if len(findings1) != len(findings2):
        return 0.0
    
    # Compare findings by control_id, severity, and line
    findings1_keys = set()
    findings2_keys = set()
    
    for finding in findings1:
        key = (finding.get("control_id"), finding.get("severity"), finding.get("line"))
        findings1_keys.add(key)
    
    for finding in findings2:
        key = (finding.get("control_id"), finding.get("severity"), finding.get("line"))
        findings2_keys.add(key)
    
    if not findings1_keys and not findings2_keys:
        return 1.0
    
    intersection = findings1_keys.intersection(findings2_keys)
    union = findings1_keys.union(findings2_keys)
    
    return len(intersection) / len(union) if union else 1.0

def main():
    """Run all optimization tests."""
    print("🧪 Testing IaC Guardian Security Analysis Prompt Optimizations")
    print("=" * 70)
    
    test_cases = create_test_cases()
    results = {}
    
    for test_name, test_case in test_cases.items():
        print(f"\n📋 Running test: {test_name}")
        print(f"   Description: {test_case['description']}")
        
        if test_case.get("consistency_runs", 0) > 1:
            # Run consistency test
            result = test_consistency(test_case)
            results[test_name] = result
            
            consistency_score = result["consistency_score"]
            print(f"   ✅ Consistency Score: {consistency_score:.2%}")
            
            if result.get("consistent", False):
                print(f"   ✅ PASSED: Analysis is consistent across runs")
            else:
                print(f"   ❌ FAILED: Analysis inconsistent (score: {consistency_score:.2%})")
        else:
            # Run single analysis test
            result = run_analysis_test(test_name, test_case)
            results[test_name] = result
            
            if "error" in result:
                print(f"   ❌ ERROR: {result['error']}")
                continue
            
            fp_analysis = result["false_positive_analysis"]
            fp_reduction = fp_analysis["false_positive_reduction_rate"]
            real_detection = fp_analysis["real_issue_detection_rate"]
            
            print(f"   📊 False Positive Reduction: {fp_reduction:.2%}")
            print(f"   📊 Real Issue Detection: {real_detection:.2%}")
            print(f"   📊 Total Findings: {result['total_findings']}")
            
            # Determine pass/fail
            if fp_reduction >= 0.8 and real_detection >= 0.8:
                print(f"   ✅ PASSED: Good balance of FP reduction and issue detection")
            elif fp_reduction >= 0.6:
                print(f"   ⚠️  PARTIAL: Moderate false positive reduction")
            else:
                print(f"   ❌ FAILED: Insufficient false positive reduction")
    
    # Generate summary report
    print("\n" + "=" * 70)
    print("📊 OPTIMIZATION TEST SUMMARY")
    print("=" * 70)
    
    total_tests = len(results)
    passed_tests = 0
    
    for test_name, result in results.items():
        if "error" not in result:
            if "consistency_score" in result:
                if result.get("consistent", False):
                    passed_tests += 1
                    status = "✅ PASSED"
                else:
                    status = "❌ FAILED"
                print(f"{test_name}: {status} (Consistency: {result['consistency_score']:.2%})")
            else:
                fp_analysis = result["false_positive_analysis"]
                fp_reduction = fp_analysis["false_positive_reduction_rate"]
                real_detection = fp_analysis["real_issue_detection_rate"]
                
                if fp_reduction >= 0.8 and real_detection >= 0.8:
                    passed_tests += 1
                    status = "✅ PASSED"
                elif fp_reduction >= 0.6:
                    status = "⚠️  PARTIAL"
                else:
                    status = "❌ FAILED"
                
                print(f"{test_name}: {status} (FP: {fp_reduction:.2%}, Detection: {real_detection:.2%})")
        else:
            print(f"{test_name}: ❌ ERROR - {result['error']}")
    
    success_rate = passed_tests / total_tests if total_tests > 0 else 0
    print(f"\nOverall Success Rate: {success_rate:.2%} ({passed_tests}/{total_tests})")
    
    if success_rate >= 0.8:
        print("🎉 OPTIMIZATION SUCCESSFUL: Prompt optimizations are working well!")
    elif success_rate >= 0.6:
        print("⚠️  OPTIMIZATION PARTIAL: Some improvements needed")
    else:
        print("❌ OPTIMIZATION FAILED: Significant improvements required")
    
    # Save detailed results
    with open("optimization_test_results.json", "w") as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n📄 Detailed results saved to: optimization_test_results.json")

if __name__ == "__main__":
    main()
