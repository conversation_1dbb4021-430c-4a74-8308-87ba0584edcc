<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Assessment Report - IaC Guardian</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
/* Glass UI Framework Styles */
/* Glass UI Framework - IaC Guardian Security Reports */

:root {
    /* 🎨 Primary Color Palette (Blue tone) */
    --hue-primary: 223;
    --primary500: hsl(var(--hue-primary), 90%, 50%);
    --primary600: hsl(var(--hue-primary), 90%, 60%);
    --primary700: hsl(var(--hue-primary), 90%, 70%);

    /* 🟢 Secondary Color Palette (Teal tone) */
    --hue-secondary: 178;
    --secondary800: hsl(var(--hue-secondary), 90%, 80%);

    /* 🌑 Dark Grays (used for dark backgrounds) */
    --dark-gray50: hsl(var(--hue-primary), 90%, 5%);
    --dark-gray100: hsl(var(--hue-primary), 90%, 10%);

    /* ⚪ White Transparency Palette (used for glass effects, overlays) */
    --white0: hsla(0, 0%, 100%, 0);
    --white50: hsla(0, 0%, 100%, 0.05);
    --white100: hsla(0, 0%, 100%, 0.1);
    --white200: hsla(0, 0%, 100%, 0.2);
    --white300: hsla(0, 0%, 100%, 0.3);
    --white400: hsla(0, 0%, 100%, 0.4);
    --white500: hsla(0, 0%, 100%, 0.5);
    --white: hsl(0, 0%, 100%);

    /* 🧮 Base font scaling */
    font-size: clamp(0.75rem, 0.65rem + 0.5vw, 1.25rem);

    /* Glass UI Semantic Colors with New Palette */
    --success-green: hsl(142, 76%, 36%);
    --warning-amber: hsl(38, 92%, 50%);
    --danger-red: hsl(0, 84%, 60%);
    --info-cyan: var(--secondary800);

    /* Glass UI Components using White Transparency */
    --glass-white: var(--white200);
    --glass-white-light: var(--white100);
    --glass-white-strong: var(--white400);
    --glass-border: var(--white200);

    /* 📝 Text Color Palette - Optimized for Glass UI */
    --text-primary: var(--white);
    --text-secondary: hsla(0, 0%, 100%, 0.85);
    --text-muted: hsla(0, 0%, 100%, 0.65);
    --text-accent: hsl(var(--hue-secondary), 90%, 85%);
    --text-on-glass: hsla(0, 0%, 100%, 0.95);
    --text-on-dark: var(--white);
    --text-on-light: hsl(var(--hue-primary), 90%, 15%);
    --text-interactive: hsl(var(--hue-primary), 90%, 85%);
    --text-hover: hsl(var(--hue-secondary), 90%, 90%);

    /* Semantic Colors with Glass Effects */
    --critical-glass: hsla(0, 84%, 60%, 0.2);
    --critical-border: hsla(0, 84%, 60%, 0.3);
    --critical-text: hsl(0, 84%, 80%);
    --high-glass: hsla(38, 92%, 50%, 0.2);
    --high-border: hsla(38, 92%, 50%, 0.3);
    --high-text: hsl(38, 92%, 75%);
    --medium-glass: hsla(45, 93%, 47%, 0.2);
    --medium-border: hsla(45, 93%, 47%, 0.3);
    --medium-text: hsl(45, 93%, 75%);
    --low-glass: hsla(var(--hue-secondary), 90%, 80%, 0.2);
    --low-border: hsla(var(--hue-secondary), 90%, 80%, 0.3);
    --low-text: var(--secondary800);

    /* Glass UI Layout */
    --max-width: 1400px;
    --border-radius: 16px;
    --border-radius-sm: 12px;
    --border-radius-lg: 24px;
    --glass-blur: blur(16px);
    --glass-blur-strong: blur(24px);
    --glass-shadow: 0 8px 32px hsla(var(--hue-primary), 90%, 5%, 0.12);
    --glass-shadow-lg: 0 16px 64px hsla(var(--hue-primary), 90%, 5%, 0.16);
    --glass-shadow-xl: 0 24px 96px hsla(var(--hue-primary), 90%, 5%, 0.2);
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-on-glass);
    background: linear-gradient(135deg,
        var(--dark-gray50) 0%,
        var(--dark-gray100) 30%,
        hsl(var(--hue-primary), 60%, 15%) 70%,
        hsl(var(--hue-secondary), 40%, 20%) 100%);
    background-attachment: fixed;
    min-height: 100vh;
    overflow-x: hidden;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, hsla(var(--hue-primary), 90%, 50%, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, hsla(var(--hue-secondary), 90%, 60%, 0.12) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, var(--white50) 0%, transparent 70%);
    pointer-events: none;
    z-index: -1;
}

.main-container {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 2rem 1rem;
    position: relative;
    z-index: 1;
}

/* Glass UI Header Section */
.report-header {
    background: var(--glass-white);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    padding: 3rem 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--glass-shadow-lg);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.report-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary500), var(--primary600), var(--secondary800));
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.report-header::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
    pointer-events: none;
    border-radius: inherit;
}

.report-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-on-glass);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    text-shadow: 0 2px 8px hsla(var(--hue-primary), 90%, 5%, 0.3);
    position: relative;
    z-index: 2;
}

.report-subtitle {
    font-size: 1.125rem;
    color: var(--text-accent);
    font-weight: 400;
    margin-bottom: 1rem;
    position: relative;
    z-index: 2;
    text-shadow: 0 1px 4px hsla(var(--hue-primary), 90%, 5%, 0.2);
}

.report-meta {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 1.5rem;
    font-size: 0.875rem;
    color: var(--text-interactive);
    position: relative;
    z-index: 2;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--glass-white-light);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

/* Glass UI Controls Section */
.controls-section {
    background: var(--glass-white);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--glass-shadow);
}

.controls-grid {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 1.5rem;
    align-items: center;
}

.search-container {
    position: relative;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-sm);
    font-size: 0.875rem;
    transition: all 0.3s ease;
    background: var(--glass-white-light);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    color: var(--text-on-glass);
}

.search-input::placeholder {
    color: var(--text-interactive);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary500);
    background: var(--glass-white-strong);
    box-shadow: 0 0 0 3px hsla(var(--hue-primary), 90%, 50%, 0.2);
    transform: translateY(-1px);
    color: var(--text-on-glass);
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-interactive);
}

.filter-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--glass-border);
    border-radius: 2rem;
    background: var(--glass-white-light);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    color: var(--text-interactive);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    white-space: nowrap;
}

.filter-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--glass-shadow);
    background: var(--glass-white-strong);
    color: var(--text-hover);
}

.filter-btn.active {
    color: var(--text-on-dark);
    transform: translateY(-2px);
    box-shadow: var(--glass-shadow-lg);
    border-color: transparent;
}

.filter-btn.all.active {
    background: linear-gradient(135deg, var(--primary500), var(--primary600));
}
.filter-btn.critical.active {
    background: linear-gradient(135deg, var(--danger-red), var(--critical-text));
}
.filter-btn.high.active {
    background: linear-gradient(135deg, var(--warning-amber), var(--high-text));
}
.filter-btn.medium.active {
    background: linear-gradient(135deg, hsl(45, 93%, 47%), var(--medium-text));
}
.filter-btn.low.active {
    background: linear-gradient(135deg, var(--info-cyan), var(--secondary800));
}

/* Multi-select filter enhancements */
.filter-btn.active {
    position: relative;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.multi-select-info {
    text-align: center;
    margin-top: 0.5rem;
}

.filter-summary {
    text-align: center;
    font-weight: 500;
}

/* Animation for filter changes */
.severity-group {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.severity-group[style*="display: none"] {
    opacity: 0;
    transform: translateY(-10px);
}

.finding-item {
    transition: opacity 0.2s ease;
}

.finding-item[style*="display: none"] {
    opacity: 0;
}

/* Glass UI Summary Section */
.summary-section {
    background: var(--glass-white);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--glass-shadow);
}

.summary-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-on-glass);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-shadow: 0 2px 8px hsla(var(--hue-primary), 90%, 5%, 0.3);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--glass-white-light);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--glass-shadow-lg);
    background: var(--glass-white-strong);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary500), var(--secondary800));
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.stat-card::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.06) 0%, transparent 60%);
    pointer-events: none;
    border-radius: inherit;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-on-glass);
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 8px hsla(var(--hue-primary), 90%, 5%, 0.3);
    position: relative;
    z-index: 2;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-accent);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: relative;
    z-index: 2;
}

.severity-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1.5rem;
}

.severity-badge {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    transition: all 0.3s ease;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid var(--glass-border);
    position: relative;
    overflow: hidden;
}

.severity-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: inherit;
    opacity: 0.1;
    z-index: -1;
}

.severity-badge:hover {
    transform: translateY(-2px);
    box-shadow: var(--glass-shadow);
}

.severity-badge.critical {
    background: var(--critical-glass);
    border-color: var(--critical-border);
    color: var(--critical-text);
}

.severity-badge.high {
    background: var(--high-glass);
    border-color: var(--high-border);
    color: var(--high-text);
}

.severity-badge.medium {
    background: var(--medium-glass);
    border-color: var(--medium-border);
    color: var(--medium-text);
}

.severity-badge.low {
    background: var(--low-glass);
    border-color: var(--low-border);
    color: var(--low-text);
}


/* Responsive Design Styles */
/* Responsive Design for Glass UI Framework */

/* Large Desktop (1200px+) - Enhanced Glass Effects */
@media (min-width: 1200px) {
    .main-container {
        padding: 3rem 2rem;
    }

    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .severity-overview {
        grid-template-columns: repeat(4, 1fr);
    }

    /* Enhanced glass blur for larger screens */
    .report-header,
    .controls-section,
    .summary-section,
    .severity-group,
    .domain-section {
        backdrop-filter: var(--glass-blur-strong);
        -webkit-backdrop-filter: var(--glass-blur-strong);
    }
}

/* Desktop (992px - 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
    .main-container {
        padding: 2rem 1.5rem;
    }

    .report-title {
        font-size: 2.25rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .severity-overview {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Tablet (768px - 991px) */
@media (min-width: 768px) and (max-width: 991px) {
    .main-container {
        padding: 1.5rem 1rem;
    }

    .report-header {
        padding: 2rem 1.5rem;
    }

    .report-title {
        font-size: 2rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .report-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .controls-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .filter-buttons {
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .severity-overview {
        grid-template-columns: repeat(2, 1fr);
    }

    .finding-title {
        font-size: 1rem;
    }

    .finding-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Mobile Large (576px - 767px) */
@media (min-width: 576px) and (max-width: 767px) {
    .main-container {
        padding: 1rem 0.75rem;
    }

    .report-header {
        padding: 1.5rem 1rem;
    }

    .report-title {
        font-size: 1.75rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .report-subtitle {
        font-size: 1rem;
    }

    .report-meta {
        flex-direction: column;
        gap: 0.25rem;
        font-size: 0.8125rem;
    }

    .controls-section {
        padding: 1rem;
    }

    .controls-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .filter-buttons {
        justify-content: center;
        gap: 0.375rem;
    }

    .filter-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.6875rem;
    }

    .summary-section {
        padding: 1.5rem 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .severity-overview {
        grid-template-columns: 1fr;
    }

    .finding-item {
        padding: 1rem;
    }

    .finding-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .finding-title {
        font-size: 0.9375rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .export-actions {
        flex-direction: column;
        align-items: center;
    }
}

/* Mobile Small (up to 575px) */
@media (max-width: 575px) {
    .main-container {
        padding: 0.75rem 0.5rem;
    }

    .report-header {
        padding: 1.25rem 0.75rem;
        margin-bottom: 1rem;
    }

    .report-title {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.375rem;
    }

    .report-subtitle {
        font-size: 0.9375rem;
    }

    .report-meta {
        flex-direction: column;
        gap: 0.25rem;
        font-size: 0.75rem;
    }

    .controls-section {
        padding: 0.75rem;
    }

    .search-input {
        font-size: 1rem; /* Prevents zoom on iOS */
        padding: 0.75rem 1rem 0.75rem 2.25rem;
    }

    .filter-buttons {
        gap: 0.25rem;
    }

    .filter-btn {
        padding: 0.375rem 0.625rem;
        font-size: 0.625rem;
        min-width: auto;
    }

    .summary-section {
        padding: 1.25rem 0.75rem;
    }

    .summary-title {
        font-size: 1.25rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .severity-overview {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .severity-header {
        padding: 1rem;
        font-size: 1rem;
    }

    .finding-item {
        padding: 0.75rem;
    }

    .finding-icon {
        width: 2rem;
        height: 2rem;
        font-size: 0.875rem;
    }

    .finding-title {
        font-size: 0.875rem;
    }

    .control-id {
        font-size: 0.6875rem;
        padding: 0.1875rem 0.5rem;
    }

    .finding-meta {
        font-size: 0.8125rem;
    }

    .finding-description {
        font-size: 0.875rem;
    }

    .code-snippet {
        font-size: 0.75rem;
        padding: 0.75rem;
    }

    .export-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .export-btn {
        width: 100%;
        justify-content: center;
    }

    /* Mobile Line Number Fixes - Simplified Structure */
    .meta-item.line-number {
        width: auto !important;
        min-width: max-content !important;
        flex-shrink: 0 !important;
        font-size: 0.75rem !important;
        padding: 0.3rem 0.6rem !important;
        gap: 0.25rem !important;
        white-space: nowrap !important;
        overflow: visible !important;
        box-sizing: border-box !important;
    }
}

/* Print Styles */
@media print {
    body {
        background: white !important;
    }

    .main-container {
        max-width: none;
        padding: 0;
    }

    .report-header,
    .controls-section,
    .summary-section,
    .severity-group,
    .report-footer {
        box-shadow: none !important;
        break-inside: avoid;
    }

    .controls-section,
    .export-actions {
        display: none !important;
    }

    .findings-list {
        max-height: none !important;
    }
}

    </style>
    
</head>

<body>
    <div class="main-container">
        <!-- Header Section -->
        <header class="report-header">
            <h1 class="report-title">
                <i class="fas fa-shield-alt"></i>
                Security Assessment Report
            </h1>
            <p class="report-subtitle">Infrastructure as Code Security Analysis</p>
            <div class="report-meta">
                <div class="meta-item">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Generated: June 20, 2025 at 06:46 PM</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-robot"></i>
                    <span>IaC Guardian</span>
                </div>
            </div>
        </header>

        <!-- Controls Section -->
        <section class="controls-section">
            <div class="controls-grid">
                <div class="search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="Search findings...">
                </div>
                <div class="filter-buttons">
                    <button class="filter-btn all active" data-severity="all">
                        <i class="fas fa-list"></i>
                        All
                    </button>
                    <button class="filter-btn critical" data-severity="critical">
                        <i class="fas fa-exclamation-triangle"></i>
                        Critical
                    </button>
                    <button class="filter-btn high" data-severity="high">
                        <i class="fas fa-exclamation-circle"></i>
                        High
                    </button>
                    <button class="filter-btn medium" data-severity="medium">
                        <i class="fas fa-exclamation"></i>
                        Medium
                    </button>
                    <button class="filter-btn low" data-severity="low">
                        <i class="fas fa-info-circle"></i>
                        Low
                    </button>
                </div>
            </div>
        </section>

        <!-- Summary Section -->
        
        <!-- Summary Section -->
        <section class="summary-section">
            <h2 class="summary-title">
                <i class="fas fa-chart-bar"></i>
                Executive Summary
            </h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">7</div>
                    <div class="stat-label">Total Findings</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">2</div>
                    <div class="stat-label">Files Affected</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">7</div>
                    <div class="stat-label">High Priority</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">4</div>
                    <div class="stat-label">Security Controls</div>
                </div>
            </div>
            <div class="severity-overview">
                <div class="severity-badge critical">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        Critical
                    </div>
                    <div class="severity-count">7</div>
                </div>
            </div>
        </section>

        <!-- Graph Section -->
        

        <!-- Findings Section -->
        <div class="findings-container">
            
        <section class="severity-group" data-severity="critical">
            <header class="severity-header critical">
                <div class="severity-header-left">
                    <div class="severity-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="severity-title">Critical Severity</div>
                    <div class="severity-count">7</div>
                </div>
                <i class="fas fa-chevron-down toggle-icon"></i>
            </header>
            <div class="findings-list">
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-1</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>network_demo.tf</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 17</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('network_demo.tf', 17, 'NS-1', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The security rule 'AllowSSH' in the Network Security Group allows inbound SSH (TCP/22) from '0.0.0.0/0', exposing all virtual machines associated with this NSG to the public internet. This enables attackers to attempt brute-force attacks, credential stuffing, or exploit SSH vulnerabilities, potentially leading to initial access and lateral movement within the environment. The blast radius includes all resources protected by this NSG.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Restrict the 'source_address_prefix' property to trusted IP ranges (e.g., corporate office IPs or a jump host). Remove '0.0.0.0/0' and implement just-in-time (JIT) access for management ports. Example: source_address_prefix = "***********/24". Reference: Azure Security Benchmark v3.0, NS-1.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-2</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>network_demo.tf</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 43</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('network_demo.tf', 43, 'NS-2', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The storage account 'demostorageaccount' has 'allow_blob_public_access' set to true, enabling anonymous public access to blobs. This exposes sensitive or regulated data to the internet, allowing data exfiltration by unauthenticated users. The blast radius includes all data stored in public containers within this storage account.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Set 'allow_blob_public_access' to false to disable anonymous public access. Use Azure RBAC or shared access signatures (SAS) for controlled access. Example: allow_blob_public_access = false. Reference: Azure Security Benchmark v3.0, NS-2.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-2</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>storage_demo.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 40</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('storage_demo.bicep', 40, 'NS-2', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The 'networkAcls.defaultAction' property is set to 'Allow', which permits all network traffic to the storage account from any source. This exposes the storage account to the public internet, enabling initial access, lateral movement, and data exfiltration by attackers.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only trusted networks or private endpoints. Implement Azure Private Link to restrict access to the storage account from private networks only. Reference: Azure Security Benchmark NS-2.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-8</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>storage_demo.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 20</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('storage_demo.bicep', 20, 'NS-8', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The 'minimumTlsVersion' property is set to 'TLS1_0', which is an insecure protocol. TLS 1.0 is vulnerable to multiple cryptographic attacks, allowing attackers to intercept or modify data in transit. This compromises the confidentiality and integrity of data and enables downgrade attacks.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Set 'minimumTlsVersion' to 'TLS1_2' or higher to enforce strong encryption for data in transit. Review all client applications to ensure compatibility with TLS 1.2+. Reference: Azure Security Benchmark NS-8.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-3</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>network_demo.tf</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 46</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('network_demo.tf', 46, 'DP-3', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The storage account 'demostorageaccount' has 'https_traffic_only' set to false, allowing unencrypted HTTP connections. This exposes data in transit to interception, man-in-the-middle attacks, and credential theft. Attackers can eavesdrop or modify data sent to and from the storage account, increasing the risk of data compromise.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Set 'https_traffic_only' to true to enforce encrypted connections for all data transfers. Example: https_traffic_only = true. Reference: Azure Security Benchmark v3.0, DP-3.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-3</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>storage_demo.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 17</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('storage_demo.bicep', 17, 'DP-3', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The 'allowBlobPublicAccess' property is set to true, enabling public anonymous access to blobs in the storage account. This allows unauthenticated users to read data, creating an initial access vector for data exfiltration and increasing the blast radius of a compromise. Attackers can enumerate and download sensitive data without authentication.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Set 'allowBlobPublicAccess' to false to disable anonymous public access. Enforce authentication and authorization for all blob access. Review and restrict access policies to ensure only authorized users and applications can access storage data. Reference: Azure Security Benchmark DP-3.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-3</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>storage_demo.bicep</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 23</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('storage_demo.bicep', 23, 'DP-3', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The 'supportsHttpsTrafficOnly' property is set to false, allowing HTTP (unencrypted) traffic to the storage account. This exposes data in transit to interception, man-in-the-middle attacks, and credential theft, significantly increasing the risk of data compromise.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Set 'supportsHttpsTrafficOnly' to true to enforce HTTPS-only access. Ensure all clients and applications use HTTPS endpoints for storage access. Reference: Azure Security Benchmark DP-3.</div>
                                </div>
                            </div>
                        </header>
                    </article>
            </div>
        </section>
        </div>

        <!-- Footer Section -->
        <footer class="report-footer">
            <div class="export-actions">
                <button class="export-btn" onclick="exportToJson()">
                    <i class="fas fa-download"></i>
                    Export JSON
                </button>
                <button class="export-btn" onclick="window.print()">
                    <i class="fas fa-print"></i>
                    Print Report
                </button>
            </div>
            <div class="footer-info">
                <p><strong>IaC Guardian</strong> - Infrastructure as Code Security Analysis</p>
                <p>Generated with Azure Security Benchmark v3.0 compliance checks</p>
                <p>Report generated on June 20, 2025 at 06:46 PM</p>
            </div>
        </footer>
    </div>

    <!-- No Results Message -->
    <div class="no-findings" style="display: none;">
        <div class="no-findings-icon">
            <i class="fas fa-search"></i>
        </div>
        <h3>No findings match your criteria</h3>
        <p>Try adjusting your search terms or filters</p>
    </div>

    <!-- Embedded File Content for Code Dialog -->
    
                <script type="application/json" data-finding-context="network_demo.tf_17" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;network_demo.tf&quot;, &quot;line_number&quot;: 17, &quot;total_lines&quot;: 52, &quot;start_line&quot;: 1, &quot;end_line&quot;: 52, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;# Demo Network Security Group with Security Issues&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;# This file demonstrates real file content in code dialogs&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;resource \&quot;azurerm_resource_group\&quot; \&quot;demo\&quot; {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;  name     = \&quot;rg-demo-security\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;  location = \&quot;East US\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;# Network Security Group with security issues&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;resource \&quot;azurerm_network_security_group\&quot; \&quot;demo\&quot; {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;  name                = \&quot;nsg-demo-security\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;  location            = azurerm_resource_group.demo.location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;  resource_group_name = azurerm_resource_group.demo.name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;  # 🚨 SECURITY ISSUE: SSH from anywhere (Line 16)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;  security_rule {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;    name                       = \&quot;AllowSSH\&quot;&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;    priority                   = 1001&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;    direction                  = \&quot;Inbound\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;    access                     = \&quot;Allow\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;    protocol                   = \&quot;Tcp\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;    source_port_range          = \&quot;*\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;    destination_port_range     = \&quot;22\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;    source_address_prefix      = \&quot;0.0.0.0/0\&quot;  # CRITICAL: Open to internet&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;    destination_address_prefix = \&quot;*\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;  tags = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;    Environment = \&quot;Demo\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;    Purpose     = \&quot;Real Content Testing\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;# Storage account with issues&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;resource \&quot;azurerm_storage_account\&quot; \&quot;demo\&quot; {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;  name                     = \&quot;demostorageaccount\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;  resource_group_name      = azurerm_resource_group.demo.name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;  location                 = azurerm_resource_group.demo.location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;  account_tier             = \&quot;Standard\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;  account_replication_type = \&quot;LRS\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;  # 🚨 SECURITY ISSUE: Public blob access (Line 40)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;  allow_blob_public_access = true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 44, &quot;content&quot;: &quot;  &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 45, &quot;content&quot;: &quot;  # 🚨 SECURITY ISSUE: HTTP allowed (Line 43)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 46, &quot;content&quot;: &quot;  https_traffic_only = false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 47, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 48, &quot;content&quot;: &quot;  tags = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 49, &quot;content&quot;: &quot;    Environment = \&quot;Demo\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 50, &quot;content&quot;: &quot;    Purpose     = \&quot;Real Content Testing\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 51, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 52, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: # Demo Network Security Group with Security Issues\n       2: # This file demonstrates real file content in code dialogs\n       3: \n       4: resource \&quot;azurerm_resource_group\&quot; \&quot;demo\&quot; {\n       5:   name     = \&quot;rg-demo-security\&quot;\n       6:   location = \&quot;East US\&quot;\n       7: }\n       8: \n       9: # Network Security Group with security issues\n      10: resource \&quot;azurerm_network_security_group\&quot; \&quot;demo\&quot; {\n      11:   name                = \&quot;nsg-demo-security\&quot;\n      12:   location            = azurerm_resource_group.demo.location\n      13:   resource_group_name = azurerm_resource_group.demo.name\n      14: \n      15:   # 🚨 SECURITY ISSUE: SSH from anywhere (Line 16)\n      16:   security_rule {\n&gt;&gt;&gt;   17:     name                       = \&quot;AllowSSH\&quot;\n      18:     priority                   = 1001\n      19:     direction                  = \&quot;Inbound\&quot;\n      20:     access                     = \&quot;Allow\&quot;\n      21:     protocol                   = \&quot;Tcp\&quot;\n      22:     source_port_range          = \&quot;*\&quot;\n      23:     destination_port_range     = \&quot;22\&quot;\n      24:     source_address_prefix      = \&quot;0.0.0.0/0\&quot;  # CRITICAL: Open to internet\n      25:     destination_address_prefix = \&quot;*\&quot;\n      26:   }\n      27: \n      28:   tags = {\n      29:     Environment = \&quot;Demo\&quot;\n      30:     Purpose     = \&quot;Real Content Testing\&quot;\n      31:   }\n      32: }\n      33: \n      34: # Storage account with issues\n      35: resource \&quot;azurerm_storage_account\&quot; \&quot;demo\&quot; {\n      36:   name                     = \&quot;demostorageaccount\&quot;\n      37:   resource_group_name      = azurerm_resource_group.demo.name\n      38:   location                 = azurerm_resource_group.demo.location\n      39:   account_tier             = \&quot;Standard\&quot;\n      40:   account_replication_type = \&quot;LRS\&quot;\n      41: \n      42:   # 🚨 SECURITY ISSUE: Public blob access (Line 40)\n      43:   allow_blob_public_access = true\n      44:   \n      45:   # 🚨 SECURITY ISSUE: HTTP allowed (Line 43)\n      46:   https_traffic_only = false\n      47: \n      48:   tags = {\n      49:     Environment = \&quot;Demo\&quot;\n      50:     Purpose     = \&quot;Real Content Testing\&quot;\n      51:   }\n      52: }&quot;, &quot;highlighted_line_content&quot;: &quot;name                       = \&quot;AllowSSH\&quot;&quot;}
                </script>
                <script type="application/json" data-finding-context="network_demo.tf_43" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;network_demo.tf&quot;, &quot;line_number&quot;: 43, &quot;total_lines&quot;: 52, &quot;start_line&quot;: 1, &quot;end_line&quot;: 52, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;# Demo Network Security Group with Security Issues&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;# This file demonstrates real file content in code dialogs&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;resource \&quot;azurerm_resource_group\&quot; \&quot;demo\&quot; {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;  name     = \&quot;rg-demo-security\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;  location = \&quot;East US\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;# Network Security Group with security issues&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;resource \&quot;azurerm_network_security_group\&quot; \&quot;demo\&quot; {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;  name                = \&quot;nsg-demo-security\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;  location            = azurerm_resource_group.demo.location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;  resource_group_name = azurerm_resource_group.demo.name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;  # 🚨 SECURITY ISSUE: SSH from anywhere (Line 16)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;  security_rule {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;    name                       = \&quot;AllowSSH\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;    priority                   = 1001&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;    direction                  = \&quot;Inbound\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;    access                     = \&quot;Allow\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;    protocol                   = \&quot;Tcp\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;    source_port_range          = \&quot;*\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;    destination_port_range     = \&quot;22\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;    source_address_prefix      = \&quot;0.0.0.0/0\&quot;  # CRITICAL: Open to internet&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;    destination_address_prefix = \&quot;*\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;  tags = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;    Environment = \&quot;Demo\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;    Purpose     = \&quot;Real Content Testing\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;# Storage account with issues&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;resource \&quot;azurerm_storage_account\&quot; \&quot;demo\&quot; {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;  name                     = \&quot;demostorageaccount\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;  resource_group_name      = azurerm_resource_group.demo.name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;  location                 = azurerm_resource_group.demo.location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;  account_tier             = \&quot;Standard\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;  account_replication_type = \&quot;LRS\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;  # 🚨 SECURITY ISSUE: Public blob access (Line 40)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;  allow_blob_public_access = true&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 44, &quot;content&quot;: &quot;  &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 45, &quot;content&quot;: &quot;  # 🚨 SECURITY ISSUE: HTTP allowed (Line 43)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 46, &quot;content&quot;: &quot;  https_traffic_only = false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 47, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 48, &quot;content&quot;: &quot;  tags = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 49, &quot;content&quot;: &quot;    Environment = \&quot;Demo\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 50, &quot;content&quot;: &quot;    Purpose     = \&quot;Real Content Testing\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 51, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 52, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: # Demo Network Security Group with Security Issues\n       2: # This file demonstrates real file content in code dialogs\n       3: \n       4: resource \&quot;azurerm_resource_group\&quot; \&quot;demo\&quot; {\n       5:   name     = \&quot;rg-demo-security\&quot;\n       6:   location = \&quot;East US\&quot;\n       7: }\n       8: \n       9: # Network Security Group with security issues\n      10: resource \&quot;azurerm_network_security_group\&quot; \&quot;demo\&quot; {\n      11:   name                = \&quot;nsg-demo-security\&quot;\n      12:   location            = azurerm_resource_group.demo.location\n      13:   resource_group_name = azurerm_resource_group.demo.name\n      14: \n      15:   # 🚨 SECURITY ISSUE: SSH from anywhere (Line 16)\n      16:   security_rule {\n      17:     name                       = \&quot;AllowSSH\&quot;\n      18:     priority                   = 1001\n      19:     direction                  = \&quot;Inbound\&quot;\n      20:     access                     = \&quot;Allow\&quot;\n      21:     protocol                   = \&quot;Tcp\&quot;\n      22:     source_port_range          = \&quot;*\&quot;\n      23:     destination_port_range     = \&quot;22\&quot;\n      24:     source_address_prefix      = \&quot;0.0.0.0/0\&quot;  # CRITICAL: Open to internet\n      25:     destination_address_prefix = \&quot;*\&quot;\n      26:   }\n      27: \n      28:   tags = {\n      29:     Environment = \&quot;Demo\&quot;\n      30:     Purpose     = \&quot;Real Content Testing\&quot;\n      31:   }\n      32: }\n      33: \n      34: # Storage account with issues\n      35: resource \&quot;azurerm_storage_account\&quot; \&quot;demo\&quot; {\n      36:   name                     = \&quot;demostorageaccount\&quot;\n      37:   resource_group_name      = azurerm_resource_group.demo.name\n      38:   location                 = azurerm_resource_group.demo.location\n      39:   account_tier             = \&quot;Standard\&quot;\n      40:   account_replication_type = \&quot;LRS\&quot;\n      41: \n      42:   # 🚨 SECURITY ISSUE: Public blob access (Line 40)\n&gt;&gt;&gt;   43:   allow_blob_public_access = true\n      44:   \n      45:   # 🚨 SECURITY ISSUE: HTTP allowed (Line 43)\n      46:   https_traffic_only = false\n      47: \n      48:   tags = {\n      49:     Environment = \&quot;Demo\&quot;\n      50:     Purpose     = \&quot;Real Content Testing\&quot;\n      51:   }\n      52: }&quot;, &quot;highlighted_line_content&quot;: &quot;allow_blob_public_access = true&quot;}
                </script>
                <script type="application/json" data-finding-context="network_demo.tf_46" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;network_demo.tf&quot;, &quot;line_number&quot;: 46, &quot;total_lines&quot;: 52, &quot;start_line&quot;: 1, &quot;end_line&quot;: 52, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;# Demo Network Security Group with Security Issues&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;# This file demonstrates real file content in code dialogs&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;resource \&quot;azurerm_resource_group\&quot; \&quot;demo\&quot; {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;  name     = \&quot;rg-demo-security\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;  location = \&quot;East US\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;# Network Security Group with security issues&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;resource \&quot;azurerm_network_security_group\&quot; \&quot;demo\&quot; {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;  name                = \&quot;nsg-demo-security\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;  location            = azurerm_resource_group.demo.location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;  resource_group_name = azurerm_resource_group.demo.name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;  # 🚨 SECURITY ISSUE: SSH from anywhere (Line 16)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;  security_rule {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;    name                       = \&quot;AllowSSH\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;    priority                   = 1001&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;    direction                  = \&quot;Inbound\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;    access                     = \&quot;Allow\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;    protocol                   = \&quot;Tcp\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;    source_port_range          = \&quot;*\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;    destination_port_range     = \&quot;22\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;    source_address_prefix      = \&quot;0.0.0.0/0\&quot;  # CRITICAL: Open to internet&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;    destination_address_prefix = \&quot;*\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;  tags = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;    Environment = \&quot;Demo\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;    Purpose     = \&quot;Real Content Testing\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;# Storage account with issues&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;resource \&quot;azurerm_storage_account\&quot; \&quot;demo\&quot; {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;  name                     = \&quot;demostorageaccount\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;  resource_group_name      = azurerm_resource_group.demo.name&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;  location                 = azurerm_resource_group.demo.location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;  account_tier             = \&quot;Standard\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;  account_replication_type = \&quot;LRS\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;  # 🚨 SECURITY ISSUE: Public blob access (Line 40)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;  allow_blob_public_access = true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 44, &quot;content&quot;: &quot;  &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 45, &quot;content&quot;: &quot;  # 🚨 SECURITY ISSUE: HTTP allowed (Line 43)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 46, &quot;content&quot;: &quot;  https_traffic_only = false&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 47, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 48, &quot;content&quot;: &quot;  tags = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 49, &quot;content&quot;: &quot;    Environment = \&quot;Demo\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 50, &quot;content&quot;: &quot;    Purpose     = \&quot;Real Content Testing\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 51, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 52, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: # Demo Network Security Group with Security Issues\n       2: # This file demonstrates real file content in code dialogs\n       3: \n       4: resource \&quot;azurerm_resource_group\&quot; \&quot;demo\&quot; {\n       5:   name     = \&quot;rg-demo-security\&quot;\n       6:   location = \&quot;East US\&quot;\n       7: }\n       8: \n       9: # Network Security Group with security issues\n      10: resource \&quot;azurerm_network_security_group\&quot; \&quot;demo\&quot; {\n      11:   name                = \&quot;nsg-demo-security\&quot;\n      12:   location            = azurerm_resource_group.demo.location\n      13:   resource_group_name = azurerm_resource_group.demo.name\n      14: \n      15:   # 🚨 SECURITY ISSUE: SSH from anywhere (Line 16)\n      16:   security_rule {\n      17:     name                       = \&quot;AllowSSH\&quot;\n      18:     priority                   = 1001\n      19:     direction                  = \&quot;Inbound\&quot;\n      20:     access                     = \&quot;Allow\&quot;\n      21:     protocol                   = \&quot;Tcp\&quot;\n      22:     source_port_range          = \&quot;*\&quot;\n      23:     destination_port_range     = \&quot;22\&quot;\n      24:     source_address_prefix      = \&quot;0.0.0.0/0\&quot;  # CRITICAL: Open to internet\n      25:     destination_address_prefix = \&quot;*\&quot;\n      26:   }\n      27: \n      28:   tags = {\n      29:     Environment = \&quot;Demo\&quot;\n      30:     Purpose     = \&quot;Real Content Testing\&quot;\n      31:   }\n      32: }\n      33: \n      34: # Storage account with issues\n      35: resource \&quot;azurerm_storage_account\&quot; \&quot;demo\&quot; {\n      36:   name                     = \&quot;demostorageaccount\&quot;\n      37:   resource_group_name      = azurerm_resource_group.demo.name\n      38:   location                 = azurerm_resource_group.demo.location\n      39:   account_tier             = \&quot;Standard\&quot;\n      40:   account_replication_type = \&quot;LRS\&quot;\n      41: \n      42:   # 🚨 SECURITY ISSUE: Public blob access (Line 40)\n      43:   allow_blob_public_access = true\n      44:   \n      45:   # 🚨 SECURITY ISSUE: HTTP allowed (Line 43)\n&gt;&gt;&gt;   46:   https_traffic_only = false\n      47: \n      48:   tags = {\n      49:     Environment = \&quot;Demo\&quot;\n      50:     Purpose     = \&quot;Real Content Testing\&quot;\n      51:   }\n      52: }&quot;, &quot;highlighted_line_content&quot;: &quot;https_traffic_only = false&quot;}
                </script>
                <script type="application/json" data-finding-context="storage_demo.bicep_17" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;storage_demo.bicep&quot;, &quot;line_number&quot;: 17, &quot;total_lines&quot;: 53, &quot;start_line&quot;: 1, &quot;end_line&quot;: 53, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;// Demo Azure Storage Account with Security Issues&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;// This file demonstrates real file content in code dialogs&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;param storageAccountName string = 'demostorage${uniqueString(resourceGroup().id)}'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;param location string = resourceGroup().location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;// Storage Account with intentional security issues for demonstration&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;  name: storageAccountName&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;  kind: 'StorageV2'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;  sku: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;    name: 'Standard_LRS'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;    // 🚨 SECURITY ISSUE: Public blob access enabled (Line 16)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;    allowBlobPublicAccess: true&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;    &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;    // 🚨 SECURITY ISSUE: Weak TLS version (Line 19)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;    minimumTlsVersion: 'TLS1_0'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;    &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;    // 🚨 SECURITY ISSUE: HTTP traffic allowed (Line 22)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;    supportsHttpsTrafficOnly: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;    &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;    // Good configuration&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;    encryption: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;      services: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;        blob: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;          enabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;        file: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;          enabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;      keySource: 'Microsoft.Storage'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;    &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;    // 🚨 SECURITY ISSUE: Default network access (Line 37)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;    networkAcls: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;      defaultAction: 'Allow'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;  &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 44, &quot;content&quot;: &quot;  tags: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 45, &quot;content&quot;: &quot;    Environment: 'Demo'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 46, &quot;content&quot;: &quot;    Purpose: 'Real Content Testing'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 47, &quot;content&quot;: &quot;    CreatedBy: 'IaC Guardian'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 48, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 49, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 50, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 51, &quot;content&quot;: &quot;// Output&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 52, &quot;content&quot;: &quot;output storageAccountId string = storageAccount.id&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 53, &quot;content&quot;: &quot;output storageAccountName string = storageAccount.name&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: // Demo Azure Storage Account with Security Issues\n       2: // This file demonstrates real file content in code dialogs\n       3: \n       4: param storageAccountName string = 'demostorage${uniqueString(resourceGroup().id)}'\n       5: param location string = resourceGroup().location\n       6: \n       7: // Storage Account with intentional security issues for demonstration\n       8: resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {\n       9:   name: storageAccountName\n      10:   location: location\n      11:   kind: 'StorageV2'\n      12:   sku: {\n      13:     name: 'Standard_LRS'\n      14:   }\n      15:   properties: {\n      16:     // 🚨 SECURITY ISSUE: Public blob access enabled (Line 16)\n&gt;&gt;&gt;   17:     allowBlobPublicAccess: true\n      18:     \n      19:     // 🚨 SECURITY ISSUE: Weak TLS version (Line 19)\n      20:     minimumTlsVersion: 'TLS1_0'\n      21:     \n      22:     // 🚨 SECURITY ISSUE: HTTP traffic allowed (Line 22)\n      23:     supportsHttpsTrafficOnly: false\n      24:     \n      25:     // Good configuration\n      26:     encryption: {\n      27:       services: {\n      28:         blob: {\n      29:           enabled: true\n      30:         }\n      31:         file: {\n      32:           enabled: true\n      33:         }\n      34:       }\n      35:       keySource: 'Microsoft.Storage'\n      36:     }\n      37:     \n      38:     // 🚨 SECURITY ISSUE: Default network access (Line 37)\n      39:     networkAcls: {\n      40:       defaultAction: 'Allow'\n      41:     }\n      42:   }\n      43:   \n      44:   tags: {\n      45:     Environment: 'Demo'\n      46:     Purpose: 'Real Content Testing'\n      47:     CreatedBy: 'IaC Guardian'\n      48:   }\n      49: }\n      50: \n      51: // Output\n      52: output storageAccountId string = storageAccount.id\n      53: output storageAccountName string = storageAccount.name&quot;, &quot;highlighted_line_content&quot;: &quot;allowBlobPublicAccess: true&quot;}
                </script>
                <script type="application/json" data-finding-context="storage_demo.bicep_20" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;storage_demo.bicep&quot;, &quot;line_number&quot;: 20, &quot;total_lines&quot;: 53, &quot;start_line&quot;: 1, &quot;end_line&quot;: 53, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;// Demo Azure Storage Account with Security Issues&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;// This file demonstrates real file content in code dialogs&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;param storageAccountName string = 'demostorage${uniqueString(resourceGroup().id)}'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;param location string = resourceGroup().location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;// Storage Account with intentional security issues for demonstration&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;  name: storageAccountName&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;  kind: 'StorageV2'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;  sku: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;    name: 'Standard_LRS'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;    // 🚨 SECURITY ISSUE: Public blob access enabled (Line 16)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;    allowBlobPublicAccess: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;    &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;    // 🚨 SECURITY ISSUE: Weak TLS version (Line 19)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;    minimumTlsVersion: 'TLS1_0'&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;    &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;    // 🚨 SECURITY ISSUE: HTTP traffic allowed (Line 22)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;    supportsHttpsTrafficOnly: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;    &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;    // Good configuration&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;    encryption: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;      services: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;        blob: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;          enabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;        file: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;          enabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;      keySource: 'Microsoft.Storage'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;    &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;    // 🚨 SECURITY ISSUE: Default network access (Line 37)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;    networkAcls: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;      defaultAction: 'Allow'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;  &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 44, &quot;content&quot;: &quot;  tags: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 45, &quot;content&quot;: &quot;    Environment: 'Demo'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 46, &quot;content&quot;: &quot;    Purpose: 'Real Content Testing'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 47, &quot;content&quot;: &quot;    CreatedBy: 'IaC Guardian'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 48, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 49, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 50, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 51, &quot;content&quot;: &quot;// Output&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 52, &quot;content&quot;: &quot;output storageAccountId string = storageAccount.id&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 53, &quot;content&quot;: &quot;output storageAccountName string = storageAccount.name&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: // Demo Azure Storage Account with Security Issues\n       2: // This file demonstrates real file content in code dialogs\n       3: \n       4: param storageAccountName string = 'demostorage${uniqueString(resourceGroup().id)}'\n       5: param location string = resourceGroup().location\n       6: \n       7: // Storage Account with intentional security issues for demonstration\n       8: resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {\n       9:   name: storageAccountName\n      10:   location: location\n      11:   kind: 'StorageV2'\n      12:   sku: {\n      13:     name: 'Standard_LRS'\n      14:   }\n      15:   properties: {\n      16:     // 🚨 SECURITY ISSUE: Public blob access enabled (Line 16)\n      17:     allowBlobPublicAccess: true\n      18:     \n      19:     // 🚨 SECURITY ISSUE: Weak TLS version (Line 19)\n&gt;&gt;&gt;   20:     minimumTlsVersion: 'TLS1_0'\n      21:     \n      22:     // 🚨 SECURITY ISSUE: HTTP traffic allowed (Line 22)\n      23:     supportsHttpsTrafficOnly: false\n      24:     \n      25:     // Good configuration\n      26:     encryption: {\n      27:       services: {\n      28:         blob: {\n      29:           enabled: true\n      30:         }\n      31:         file: {\n      32:           enabled: true\n      33:         }\n      34:       }\n      35:       keySource: 'Microsoft.Storage'\n      36:     }\n      37:     \n      38:     // 🚨 SECURITY ISSUE: Default network access (Line 37)\n      39:     networkAcls: {\n      40:       defaultAction: 'Allow'\n      41:     }\n      42:   }\n      43:   \n      44:   tags: {\n      45:     Environment: 'Demo'\n      46:     Purpose: 'Real Content Testing'\n      47:     CreatedBy: 'IaC Guardian'\n      48:   }\n      49: }\n      50: \n      51: // Output\n      52: output storageAccountId string = storageAccount.id\n      53: output storageAccountName string = storageAccount.name&quot;, &quot;highlighted_line_content&quot;: &quot;minimumTlsVersion: 'TLS1_0'&quot;}
                </script>
                <script type="application/json" data-finding-context="storage_demo.bicep_23" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;storage_demo.bicep&quot;, &quot;line_number&quot;: 23, &quot;total_lines&quot;: 53, &quot;start_line&quot;: 1, &quot;end_line&quot;: 53, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;// Demo Azure Storage Account with Security Issues&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;// This file demonstrates real file content in code dialogs&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;param storageAccountName string = 'demostorage${uniqueString(resourceGroup().id)}'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;param location string = resourceGroup().location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;// Storage Account with intentional security issues for demonstration&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;  name: storageAccountName&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;  kind: 'StorageV2'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;  sku: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;    name: 'Standard_LRS'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;    // 🚨 SECURITY ISSUE: Public blob access enabled (Line 16)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;    allowBlobPublicAccess: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;    &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;    // 🚨 SECURITY ISSUE: Weak TLS version (Line 19)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;    minimumTlsVersion: 'TLS1_0'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;    &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;    // 🚨 SECURITY ISSUE: HTTP traffic allowed (Line 22)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;    supportsHttpsTrafficOnly: false&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;    &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;    // Good configuration&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;    encryption: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;      services: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;        blob: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;          enabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;        file: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;          enabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;      keySource: 'Microsoft.Storage'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;    &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;    // 🚨 SECURITY ISSUE: Default network access (Line 37)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;    networkAcls: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;      defaultAction: 'Allow'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;  &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 44, &quot;content&quot;: &quot;  tags: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 45, &quot;content&quot;: &quot;    Environment: 'Demo'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 46, &quot;content&quot;: &quot;    Purpose: 'Real Content Testing'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 47, &quot;content&quot;: &quot;    CreatedBy: 'IaC Guardian'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 48, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 49, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 50, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 51, &quot;content&quot;: &quot;// Output&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 52, &quot;content&quot;: &quot;output storageAccountId string = storageAccount.id&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 53, &quot;content&quot;: &quot;output storageAccountName string = storageAccount.name&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: // Demo Azure Storage Account with Security Issues\n       2: // This file demonstrates real file content in code dialogs\n       3: \n       4: param storageAccountName string = 'demostorage${uniqueString(resourceGroup().id)}'\n       5: param location string = resourceGroup().location\n       6: \n       7: // Storage Account with intentional security issues for demonstration\n       8: resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {\n       9:   name: storageAccountName\n      10:   location: location\n      11:   kind: 'StorageV2'\n      12:   sku: {\n      13:     name: 'Standard_LRS'\n      14:   }\n      15:   properties: {\n      16:     // 🚨 SECURITY ISSUE: Public blob access enabled (Line 16)\n      17:     allowBlobPublicAccess: true\n      18:     \n      19:     // 🚨 SECURITY ISSUE: Weak TLS version (Line 19)\n      20:     minimumTlsVersion: 'TLS1_0'\n      21:     \n      22:     // 🚨 SECURITY ISSUE: HTTP traffic allowed (Line 22)\n&gt;&gt;&gt;   23:     supportsHttpsTrafficOnly: false\n      24:     \n      25:     // Good configuration\n      26:     encryption: {\n      27:       services: {\n      28:         blob: {\n      29:           enabled: true\n      30:         }\n      31:         file: {\n      32:           enabled: true\n      33:         }\n      34:       }\n      35:       keySource: 'Microsoft.Storage'\n      36:     }\n      37:     \n      38:     // 🚨 SECURITY ISSUE: Default network access (Line 37)\n      39:     networkAcls: {\n      40:       defaultAction: 'Allow'\n      41:     }\n      42:   }\n      43:   \n      44:   tags: {\n      45:     Environment: 'Demo'\n      46:     Purpose: 'Real Content Testing'\n      47:     CreatedBy: 'IaC Guardian'\n      48:   }\n      49: }\n      50: \n      51: // Output\n      52: output storageAccountId string = storageAccount.id\n      53: output storageAccountName string = storageAccount.name&quot;, &quot;highlighted_line_content&quot;: &quot;supportsHttpsTrafficOnly: false&quot;}
                </script>
                <script type="application/json" data-finding-context="storage_demo.bicep_40" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;storage_demo.bicep&quot;, &quot;line_number&quot;: 40, &quot;total_lines&quot;: 53, &quot;start_line&quot;: 1, &quot;end_line&quot;: 53, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;// Demo Azure Storage Account with Security Issues&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;// This file demonstrates real file content in code dialogs&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;param storageAccountName string = 'demostorage${uniqueString(resourceGroup().id)}'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;param location string = resourceGroup().location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;// Storage Account with intentional security issues for demonstration&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;  name: storageAccountName&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;  location: location&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;  kind: 'StorageV2'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;  sku: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;    name: 'Standard_LRS'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;  properties: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;    // 🚨 SECURITY ISSUE: Public blob access enabled (Line 16)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;    allowBlobPublicAccess: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;    &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;    // 🚨 SECURITY ISSUE: Weak TLS version (Line 19)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;    minimumTlsVersion: 'TLS1_0'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;    &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;    // 🚨 SECURITY ISSUE: HTTP traffic allowed (Line 22)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;    supportsHttpsTrafficOnly: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;    &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;    // Good configuration&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;    encryption: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;      services: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;        blob: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;          enabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;        file: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;          enabled: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;      keySource: 'Microsoft.Storage'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;    &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;    // 🚨 SECURITY ISSUE: Default network access (Line 37)&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;    networkAcls: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;      defaultAction: 'Allow'&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;  &quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 44, &quot;content&quot;: &quot;  tags: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 45, &quot;content&quot;: &quot;    Environment: 'Demo'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 46, &quot;content&quot;: &quot;    Purpose: 'Real Content Testing'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 47, &quot;content&quot;: &quot;    CreatedBy: 'IaC Guardian'&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 48, &quot;content&quot;: &quot;  }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 49, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 50, &quot;content&quot;: &quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 51, &quot;content&quot;: &quot;// Output&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 52, &quot;content&quot;: &quot;output storageAccountId string = storageAccount.id&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 53, &quot;content&quot;: &quot;output storageAccountName string = storageAccount.name&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: // Demo Azure Storage Account with Security Issues\n       2: // This file demonstrates real file content in code dialogs\n       3: \n       4: param storageAccountName string = 'demostorage${uniqueString(resourceGroup().id)}'\n       5: param location string = resourceGroup().location\n       6: \n       7: // Storage Account with intentional security issues for demonstration\n       8: resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {\n       9:   name: storageAccountName\n      10:   location: location\n      11:   kind: 'StorageV2'\n      12:   sku: {\n      13:     name: 'Standard_LRS'\n      14:   }\n      15:   properties: {\n      16:     // 🚨 SECURITY ISSUE: Public blob access enabled (Line 16)\n      17:     allowBlobPublicAccess: true\n      18:     \n      19:     // 🚨 SECURITY ISSUE: Weak TLS version (Line 19)\n      20:     minimumTlsVersion: 'TLS1_0'\n      21:     \n      22:     // 🚨 SECURITY ISSUE: HTTP traffic allowed (Line 22)\n      23:     supportsHttpsTrafficOnly: false\n      24:     \n      25:     // Good configuration\n      26:     encryption: {\n      27:       services: {\n      28:         blob: {\n      29:           enabled: true\n      30:         }\n      31:         file: {\n      32:           enabled: true\n      33:         }\n      34:       }\n      35:       keySource: 'Microsoft.Storage'\n      36:     }\n      37:     \n      38:     // 🚨 SECURITY ISSUE: Default network access (Line 37)\n      39:     networkAcls: {\n&gt;&gt;&gt;   40:       defaultAction: 'Allow'\n      41:     }\n      42:   }\n      43:   \n      44:   tags: {\n      45:     Environment: 'Demo'\n      46:     Purpose: 'Real Content Testing'\n      47:     CreatedBy: 'IaC Guardian'\n      48:   }\n      49: }\n      50: \n      51: // Output\n      52: output storageAccountId string = storageAccount.id\n      53: output storageAccountName string = storageAccount.name&quot;, &quot;highlighted_line_content&quot;: &quot;defaultAction: 'Allow'&quot;}
                </script>

    
    <script>
// Report functionality
// Modern JavaScript for enhanced interactivity with multi-select filtering
let searchTimeout;
let allFindings = [];
let activeFilters = new Set(['all']); // Support multiple active filters

document.addEventListener('DOMContentLoaded', function() {
    initializeReport();
    setupEventListeners();
    loadFindings();
});

function initializeReport() {
    // Create code dialog on page load
    createCodeDialog();
    console.log('✅ Code dialog created');

    // Initialize filter buttons with multi-select support
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            // Allow Ctrl/Cmd + click for multi-select
            const isMultiSelect = e.ctrlKey || e.metaKey;
            toggleFilter(this.dataset.severity, isMultiSelect);
        });
    });

    // Initialize search
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.addEventListener('input', debounceSearch);
    }

    // Initialize collapsible sections
    const severityHeaders = document.querySelectorAll('.severity-header');
    severityHeaders.forEach(header => {
        header.addEventListener('click', function() {
            toggleSeverityGroup(this);
        });
    });

    // Add instructions for multi-select
    addMultiSelectInstructions();
}

function addMultiSelectInstructions() {
    const controlsSection = document.querySelector('.controls-section');
    if (controlsSection) {
        const instructions = document.createElement('div');
        instructions.className = 'multi-select-info';
        instructions.innerHTML = `
            <small style="color: var(--gray-500); font-size: 0.75rem; margin-top: 0.5rem; display: block;">
                <i class="fas fa-info-circle"></i>
                Tip: Hold Ctrl/Cmd and click to select multiple severity levels
            </small>
        `;
        controlsSection.appendChild(instructions);
    }
}

function setupEventListeners() {
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            clearSearch();
            resetFilters();
        }
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            focusSearch();
        }
    });
}

function debounceSearch() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        performSearch();
    }, 300);
}

function performSearch() {
    const searchTerm = document.querySelector('.search-input').value.toLowerCase();
    applyFilters(searchTerm);
}

function toggleFilter(severity, isMultiSelect = false) {
    if (severity === 'all') {
        // If "All" is clicked, reset to show all
        activeFilters.clear();
        activeFilters.add('all');
    } else {
        if (isMultiSelect) {
            // Multi-select mode
            if (activeFilters.has('all')) {
                activeFilters.clear();
            }

            if (activeFilters.has(severity)) {
                activeFilters.delete(severity);
            } else {
                activeFilters.add(severity);
            }

            // If no filters selected, default to "all"
            if (activeFilters.size === 0) {
                activeFilters.add('all');
            }
        } else {
            // Single select mode (default behavior)
            activeFilters.clear();
            activeFilters.add(severity);
        }
    }

    updateFilterButtons();
    applyFilters();
    updateUrlHash();
}

function updateFilterButtons() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(btn => {
        const severity = btn.dataset.severity;
        const isActive = activeFilters.has(severity);
        btn.classList.toggle('active', isActive);

        // Add visual indicator for multi-select
        if (activeFilters.size > 1 && !activeFilters.has('all')) {
            btn.style.position = 'relative';
            if (isActive && !btn.querySelector('.multi-indicator')) {
                const indicator = document.createElement('span');
                indicator.className = 'multi-indicator';
                indicator.innerHTML = '✓';
                indicator.style.cssText = `
                    position: absolute;
                    top: -2px;
                    right: -2px;
                    background: var(--success-green);
                    color: white;
                    border-radius: 50%;
                    width: 16px;
                    height: 16px;
                    font-size: 10px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: bold;
                `;
                btn.appendChild(indicator);
            }
        } else {
            // Remove multi-select indicators
            const indicator = btn.querySelector('.multi-indicator');
            if (indicator) {
                indicator.remove();
            }
        }
    });
}

function applyFilters(searchTerm = '') {
    if (!searchTerm) {
        searchTerm = document.querySelector('.search-input').value.toLowerCase();
    }

    const severityGroups = document.querySelectorAll('.severity-group');
    let totalVisibleCount = 0;

    severityGroups.forEach(group => {
        const groupSeverity = group.dataset.severity;
        const findings = group.querySelectorAll('.finding-item');
        let groupVisibleCount = 0;

        // Check if this severity group should be visible
        const severityMatches = activeFilters.has('all') || activeFilters.has(groupSeverity);

        findings.forEach(finding => {
            const text = finding.textContent.toLowerCase();
            const searchMatches = searchTerm === '' || text.includes(searchTerm);
            const isVisible = severityMatches && searchMatches;

            finding.style.display = isVisible ? 'block' : 'none';
            if (isVisible) {
                groupVisibleCount++;
                totalVisibleCount++;
            }
        });

        // Show/hide the entire severity group
        group.style.display = groupVisibleCount > 0 ? 'block' : 'none';
    });

    updateNoResultsMessage(totalVisibleCount === 0);
    updateFilterSummary();
}

function updateFilterSummary() {
    // Update or create filter summary
    let summary = document.querySelector('.filter-summary');
    if (!summary) {
        summary = document.createElement('div');
        summary.className = 'filter-summary';
        summary.style.cssText = `
            margin-top: 0.5rem;
            padding: 0.5rem;
            background: var(--gray-100);
            border-radius: var(--border-radius-sm);
            font-size: 0.8125rem;
            color: var(--gray-600);
        `;
        document.querySelector('.controls-section').appendChild(summary);
    }

    if (activeFilters.has('all')) {
        summary.textContent = 'Showing all severity levels';
    } else {
        const filterList = Array.from(activeFilters).map(f => f.charAt(0).toUpperCase() + f.slice(1)).join(', ');
        summary.textContent = `Showing: ${filterList} severity levels`;
    }
}

function resetFilters() {
    activeFilters.clear();
    activeFilters.add('all');
    updateFilterButtons();
    applyFilters();
    updateUrlHash();
}

function updateUrlHash() {
    const params = new URLSearchParams();
    if (!activeFilters.has('all')) {
        params.set('filters', Array.from(activeFilters).join(','));
    }
    const searchTerm = document.querySelector('.search-input').value;
    if (searchTerm) {
        params.set('search', searchTerm);
    }

    const hash = params.toString();
    if (hash) {
        window.location.hash = hash;
    } else {
        window.history.replaceState(null, null, window.location.pathname);
    }
}

function loadFromUrlHash() {
    const hash = window.location.hash.substring(1);
    if (hash) {
        const params = new URLSearchParams(hash);
        const filters = params.get('filters');
        const search = params.get('search');

        if (filters) {
            activeFilters.clear();
            filters.split(',').forEach(filter => activeFilters.add(filter.trim()));
            updateFilterButtons();
        }

        if (search) {
            document.querySelector('.search-input').value = search;
        }

        applyFilters();
    }
}

function toggleSeverityGroup(header) {
    const group = header.parentElement;
    const findingsList = group.querySelector('.findings-list');
    const isCollapsed = header.classList.contains('collapsed');

    if (isCollapsed) {
        header.classList.remove('collapsed');
        findingsList.classList.remove('collapsed');
        findingsList.style.maxHeight = findingsList.scrollHeight + 'px';
    } else {
        header.classList.add('collapsed');
        findingsList.classList.add('collapsed');
        findingsList.style.maxHeight = '0';
    }
}

function clearSearch() {
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.value = '';
        applyFilters();
    }
}

function focusSearch() {
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.focus();
        searchInput.select();
    }
}

function updateNoResultsMessage(show) {
    const noResults = document.querySelector('.no-findings');
    if (noResults) {
        noResults.style.display = show ? 'block' : 'none';
        if (show) {
            // Update message based on active filters
            const message = noResults.querySelector('p');
            if (activeFilters.has('all')) {
                message.textContent = 'Try adjusting your search terms';
            } else {
                const filterList = Array.from(activeFilters).join(', ');
                message.textContent = `No ${filterList} severity findings match your search. Try different filters or search terms.`;
            }
        }
    }
}

function loadFindings() {
    // Initialize findings data - this would be populated with actual findings
    allFindings = [];

    // Load from URL hash if present
    loadFromUrlHash();
}

// Export functionality
function exportToJson() {
    try {
        // Collect all findings data
        const findings = [];
        document.querySelectorAll('.finding-item').forEach(item => {
            const controlId = item.querySelector('.control-id')?.textContent || 'UNKNOWN';
            const severity = item.dataset.severity || 'UNKNOWN';
            const filePath = item.querySelector('.meta-item:first-child span')?.textContent || 'Unknown';
            const lineNumber = item.querySelector('.line-number span')?.textContent?.replace(/[^0-9]/g, '') || '0';
            const description = item.querySelector('.finding-description')?.textContent || '';
            const remediation = item.querySelector('.remediation-content')?.textContent || '';

            findings.push({
                control_id: controlId,
                severity: severity.toUpperCase(),
                file_path: filePath,
                line: parseInt(lineNumber),
                description: description.trim(),
                remediation: remediation.trim()
            });
        });

        // Create export data
        const exportData = {
            metadata: {
                export_timestamp: new Date().toISOString(),
                total_findings: findings.length,
                export_source: 'IaC Guardian HTML Report'
            },
            findings: findings
        };

        // Download as JSON file
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `security_findings_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        // Show success feedback
        showExportFeedback('JSON export completed successfully!');
    } catch (error) {
        console.error('Export failed:', error);
        showExportFeedback('Export failed: ' + error.message, 'error');
    }
}

function showExportFeedback(message, type = 'success') {
    const feedback = document.createElement('div');
    feedback.style.cssText = `
        position: fixed;
        top: 2rem;
        right: 2rem;
        background: ${type === 'success' ? 'var(--success-green)' : 'var(--danger-red)'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: var(--border-radius-sm);
        box-shadow: var(--glass-shadow-lg);
        z-index: 10001;
        font-size: 0.875rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        animation: slideInRight 0.3s ease;
    `;

    feedback.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : 'exclamation-triangle'}"></i>
        ${message}
    `;

    document.body.appendChild(feedback);

    setTimeout(() => {
        feedback.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (feedback.parentNode) {
                feedback.parentNode.removeChild(feedback);
            }
        }, 300);
    }, 3000);
}

// Add animation CSS for export feedback
const exportAnimationCSS = `
@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}
`;

if (!document.getElementById('export-animations')) {
    const style = document.createElement('style');
    style.id = 'export-animations';
    style.textContent = exportAnimationCSS;
    document.head.appendChild(style);
}


// Utility functions (must come before dialogs.js since dialogs.js depends on utils.js functions)
// Utility Functions for IaC Guardian Reports

// Glass UI Enhancement Functions
function addGlassEffects() {
    // Add parallax scrolling effect to background elements
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const parallax = document.querySelector('body::before');
        if (parallax) {
            parallax.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
    });

    // Add hover effects to glass cards
    document.querySelectorAll('.stat-card, .severity-badge, .finding-item').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
            this.style.boxShadow = 'var(--glass-shadow-xl)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = 'var(--glass-shadow)';
        });
    });

    // Add glass ripple effect to buttons
    document.querySelectorAll('.filter-btn, .export-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

// Line Number Interaction Functions
function initLineNumberHighlighting() {
    document.querySelectorAll('.meta-item.line-number').forEach(lineItem => {
        lineItem.addEventListener('click', function() {
            const lineText = this.querySelector('span').textContent;
            const lineNumber = lineText.replace(/[^0-9]/g, '');
            const fileName = this.closest('.finding-item').querySelector('.meta-item:first-child span').textContent;

            // Copy line reference to clipboard
            const lineReference = `${fileName}:${lineNumber}`;
            navigator.clipboard.writeText(lineReference).then(() => {
                showLineNumberFeedback(this, 'Copied to clipboard!');
            }).catch(() => {
                showLineNumberFeedback(this, 'Line: ' + lineNumber);
            });
        });

        // Add hover effect for line numbers
        lineItem.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05) translateY(-2px)';
        });

        lineItem.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) translateY(0)';
        });
    });
}

function showLineNumberFeedback(element, message) {
    const feedback = document.createElement('div');
    feedback.textContent = message;
    feedback.style.cssText = `
        position: absolute;
        top: -40px;
        left: 50%;
        transform: translateX(-50%);
        background: var(--success-green);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: var(--border-radius-sm);
        font-size: 0.75rem;
        font-weight: 600;
        box-shadow: var(--glass-shadow-lg);
        z-index: 1001;
        animation: feedback-bounce 2s ease forwards;
        pointer-events: none;
    `;

    element.style.position = 'relative';
    element.appendChild(feedback);

    setTimeout(() => {
        if (feedback.parentNode) {
            feedback.parentNode.removeChild(feedback);
        }
    }, 2000);
}

// File Content Fetching Functions
async function fetchRealFileContentWithContext(filePath, lineNumber) {
    try {
        console.log('🔍 Fetching real file content with context for:', filePath, 'line:', lineNumber);

        // Clear any cached results to ensure fresh fetch
        const timestamp = Date.now();
        console.log('🕒 Fetch timestamp:', timestamp);

        // Create finding ID to match embedded context data
        const findingId = `${filePath}:${lineNumber}`;
        console.log('🔧 Original finding ID:', findingId);

        // Enhanced normalization patterns that match Python exactly
        const normalizedIds = [
            // Primary approach - matches Python normalization exactly
            findingId.replace(/\\\\/g, '/').replace(/:/g, '_').replace(/\//g, '_'),
            // Alternative approaches for robustness
            findingId.split('\\').join('/').split(':').join('_').split('/').join('_'),
            findingId.replace(/\\\\/g, '/').replace(/:/g, '_').replace(/\//g, '_'),
            findingId.replace(/\\\\/g, '_').replace(/:/g, '_').replace(/\//g, '_'),
            // Keep original separators for debugging
            findingId,
            findingId.replace(/\\\\/g, '/'),
            findingId.split('\\\\').join('/'),
            // URL-encoded version
            encodeURIComponent(findingId),
            // Base64 encoded version (fallback)
            btoa(findingId).replace(/[+\/=]/g, '_'),
            // Additional patterns for edge cases
            findingId.replace(/\\\\/g, '_').replace(/:/g, '_'),
            findingId.replace(/[\\/:]/g, '_'),
            // Try with just filename
            `${filePath.split(/[\\\/]/).pop()}:${lineNumber}`.replace(/:/g, '_'),
            // Try with relative path normalization
            findingId.replace(/^.*[\\\/]/, '').replace(/:/g, '_')
        ];

        console.log('🔧 Trying normalized IDs:', normalizedIds);

        // Debug: List all available embedded context elements
        const allContextElements = document.querySelectorAll('[data-finding-context]');
        console.log('📄 Available embedded context IDs:', Array.from(allContextElements).map(el => el.getAttribute('data-finding-context')));

        // Helper function to escape CSS selector values
        function escapeCSSSelector(value) {
            // Escape special characters that are invalid in CSS selectors
            return value.replace(/([\\"'])/g, '\\$1')
                        .replace(/\n/g, '\\A ')
                        .replace(/\r/g, '\\D ')
                        .replace(/\t/g, '\\9 ');
        }

        // Try to find embedded context data with any of the normalized IDs
        let contextElement = null;
        let usedId = null;

        for (const normalizedId of normalizedIds) {
            try {
                const escapedId = escapeCSSSelector(normalizedId);
                contextElement = document.querySelector(`[data-finding-context="${escapedId}"]`);
                if (contextElement) {
                    usedId = normalizedId;
                    console.log('✅ Found context element with ID:', usedId);
                    break;
                }
            } catch (selectorError) {
                console.warn('⚠️ Invalid CSS selector for ID:', normalizedId, 'Error:', selectorError.message);
                // Try alternative approach with getAttribute
                const allElements = document.querySelectorAll('[data-finding-context]');
                for (const element of allElements) {
                    if (element.getAttribute('data-finding-context') === normalizedId) {
                        contextElement = element;
                        usedId = normalizedId;
                        console.log('✅ Found context element with ID (fallback method):', usedId);
                        break;
                    }
                }
                if (contextElement) break;
            }
        }

        console.log('🎯 Found embedded context element:', !!contextElement, 'using ID:', usedId);

        if (contextElement) {
            let contextJson = contextElement.textContent || contextElement.innerText;
            console.log('📝 Context JSON length:', contextJson.length);
            console.log('📝 First 200 chars of raw JSON:', contextJson.substring(0, 200));

            // Unescape HTML entities that were escaped during embedding
            contextJson = contextJson
                .replace(/&quot;/g, '"')
                .replace(/&lt;/g, '<')
                .replace(/&gt;/g, '>')
                .replace(/&amp;/g, '&');

            console.log('📝 First 200 chars of unescaped JSON:', contextJson.substring(0, 200));

            try {
                const contextData = JSON.parse(contextJson);
                console.log('✅ Parsed context data successfully:', contextData.success);

                if (contextData.success) {
                    console.log('📊 Context info:', {
                        file_path: contextData.file_path,
                        total_lines: contextData.total_lines,
                        start_line: contextData.start_line,
                        end_line: contextData.end_line,
                        content_lines_count: contextData.content_lines?.length
                    });

                    // Validate that we have the expected data structure
                    if (!contextData.content_lines || !Array.isArray(contextData.content_lines)) {
                        console.error('❌ Invalid context data structure - missing content_lines array');
                        return { success: false, error: 'Invalid context data structure' };
                    }

                    return contextData;
                } else {
                    console.warn('⚠️ Context data indicates failure:', contextData.error);
                    return contextData; // Return the error data for fallback handling
                }
            } catch (parseError) {
                console.error('❌ Error parsing context JSON:', parseError);
                console.error('❌ Raw JSON content (first 500 chars):', contextJson.substring(0, 500));
                return { success: false, error: `JSON parse error: ${parseError.message}` };
            }
        }

        console.log('❌ No content available from any source');
        console.log('❌ Debug info - Finding ID:', findingId, 'Normalized IDs tried:', normalizedIds);
        return { success: false, error: 'File content not available - no embedded data found for any path variation' };

    } catch (error) {
        console.error('❌ Error fetching file content with context:', error);
        return { success: false, error: error.message };
    }
}

function displayRealCodeWithLineNumbers(contextData, highlightLine) {
    const lineNumbersEl = document.getElementById('line-numbers');
    const codeContentEl = document.getElementById('code-content');

    if (!lineNumbersEl || !codeContentEl) {
        console.error('❌ Line numbers or code content elements not found');
        return;
    }

    const startLine = contextData.start_line;
    const contentLines = contextData.content_lines;

    // Generate line numbers and code content
    let lineNumbersHTML = '';
    let codeHTML = '';

    contentLines.forEach((line, index) => {
        const lineNum = startLine + index;
        const isHighlighted = lineNum === parseInt(highlightLine);
        
        lineNumbersHTML += `<div class="line-number ${isHighlighted ? 'highlighted' : ''}">${lineNum}</div>`;
        codeHTML += `<div class="code-line ${isHighlighted ? 'highlighted' : ''}">${escapeHtml(line)}</div>`;
    });

    lineNumbersEl.innerHTML = lineNumbersHTML;
    codeContentEl.innerHTML = codeHTML;

    // Scroll to highlighted line
    setTimeout(() => {
        const highlightedLine = codeContentEl.querySelector('.code-line.highlighted');
        if (highlightedLine) {
            highlightedLine.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }, 100);
}

function showContentWarning(message) {
    const dialogContent = document.querySelector('.code-dialog-content');
    if (dialogContent) {
        // Remove any existing warning
        const existingWarning = dialogContent.querySelector('.content-warning');
        if (existingWarning) {
            existingWarning.remove();
        }

        // Create new warning
        const warning = document.createElement('div');
        warning.className = 'content-warning';
        warning.style.cssText = `
            background: var(--warning-amber);
            color: var(--text-on-dark);
            padding: 0.75rem 1rem;
            margin-bottom: 1rem;
            border-radius: var(--border-radius-sm);
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        `;
        warning.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            <span>${message}</span>
        `;

        // Insert at the beginning of dialog content
        dialogContent.insertBefore(warning, dialogContent.firstChild);
    }
}

// Animation keyframes (add to CSS)
const animationCSS = `
@keyframes feedback-bounce {
    0% { opacity: 0; transform: translateX(-50%) translateY(10px) scale(0.8); }
    20% { opacity: 1; transform: translateX(-50%) translateY(0) scale(1.1); }
    40% { transform: translateX(-50%) translateY(0) scale(1); }
    100% { opacity: 0; transform: translateX(-50%) translateY(-10px) scale(0.9); }
}

@keyframes tooltip-fade-in {
    from { opacity: 0; transform: translateX(-50%) translateY(5px); }
    to { opacity: 1; transform: translateX(-50%) translateY(0); }
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}
`;

// Add animation CSS to document
if (!document.getElementById('utils-animations')) {
    const style = document.createElement('style');
    style.id = 'utils-animations';
    style.textContent = animationCSS;
    document.head.appendChild(style);
}

// Additional utility functions for enhanced user experience
function initTooltips() {
    // Add tooltips to control IDs and severity badges
    document.querySelectorAll('.control-id').forEach(controlId => {
        controlId.addEventListener('mouseenter', function(e) {
            showTooltip(e.target, `Azure Security Benchmark Control: ${this.textContent}`);
        });

        controlId.addEventListener('mouseleave', function() {
            hideTooltip();
        });
    });

    // Add tooltips to severity badges
    document.querySelectorAll('.severity-badge').forEach(badge => {
        const severity = badge.classList[1]; // Get severity class
        const severityDescriptions = {
            'critical': 'Critical: Immediate action required - high risk of security breach',
            'high': 'High: Important security issue that should be addressed soon',
            'medium': 'Medium: Moderate security concern that should be reviewed',
            'low': 'Low: Minor security improvement opportunity'
        };

        badge.addEventListener('mouseenter', function(e) {
            showTooltip(e.target, severityDescriptions[severity] || 'Security finding');
        });

        badge.addEventListener('mouseleave', function() {
            hideTooltip();
        });
    });
}

function showTooltip(element, text) {
    // Remove existing tooltip
    hideTooltip();

    const tooltip = document.createElement('div');
    tooltip.id = 'custom-tooltip';
    tooltip.textContent = text;
    tooltip.style.cssText = `
        position: absolute;
        background: var(--dark-gray100);
        color: var(--text-on-glass);
        padding: 0.5rem 0.75rem;
        border-radius: var(--border-radius-sm);
        font-size: 0.75rem;
        font-weight: 500;
        box-shadow: var(--glass-shadow-lg);
        z-index: 10002;
        max-width: 300px;
        word-wrap: break-word;
        border: 1px solid var(--glass-border);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        animation: tooltip-fade-in 0.2s ease;
        pointer-events: none;
    `;

    document.body.appendChild(tooltip);

    // Position tooltip
    const rect = element.getBoundingClientRect();
    const tooltipRect = tooltip.getBoundingClientRect();

    let left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
    let top = rect.top - tooltipRect.height - 8;

    // Adjust if tooltip goes off screen
    if (left < 8) left = 8;
    if (left + tooltipRect.width > window.innerWidth - 8) {
        left = window.innerWidth - tooltipRect.width - 8;
    }
    if (top < 8) {
        top = rect.bottom + 8;
    }

    tooltip.style.left = left + 'px';
    tooltip.style.top = top + 'px';
}

function hideTooltip() {
    const tooltip = document.getElementById('custom-tooltip');
    if (tooltip) {
        tooltip.remove();
    }
}

// Keyboard shortcuts
function initKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K: Focus search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            focusSearch();
        }

        // Ctrl/Cmd + E: Export JSON
        if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
            e.preventDefault();
            if (typeof exportToJson === 'function') {
                exportToJson();
            }
        }

        // Ctrl/Cmd + P: Print
        if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
            e.preventDefault();
            window.print();
        }

        // Escape: Clear search and reset filters
        if (e.key === 'Escape') {
            if (typeof clearSearch === 'function') clearSearch();
            if (typeof resetFilters === 'function') resetFilters();
        }

        // Arrow keys for navigation between findings
        if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
            navigateFindings(e.key === 'ArrowDown' ? 'next' : 'prev');
        }
    });
}

function navigateFindings(direction) {
    const visibleFindings = Array.from(document.querySelectorAll('.finding-item'))
        .filter(item => item.style.display !== 'none');

    if (visibleFindings.length === 0) return;

    const currentFocused = document.querySelector('.finding-item.keyboard-focused');
    let currentIndex = currentFocused ? visibleFindings.indexOf(currentFocused) : -1;

    // Remove current focus
    if (currentFocused) {
        currentFocused.classList.remove('keyboard-focused');
    }

    // Calculate next index
    if (direction === 'next') {
        currentIndex = (currentIndex + 1) % visibleFindings.length;
    } else {
        currentIndex = currentIndex <= 0 ? visibleFindings.length - 1 : currentIndex - 1;
    }

    // Focus new finding
    const nextFinding = visibleFindings[currentIndex];
    nextFinding.classList.add('keyboard-focused');
    nextFinding.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

// Performance monitoring
function initPerformanceMonitoring() {
    // Monitor search performance
    let searchStartTime;
    const originalPerformSearch = window.performSearch;

    if (originalPerformSearch) {
        window.performSearch = function() {
            searchStartTime = performance.now();
            originalPerformSearch.apply(this, arguments);

            // Log search performance
            requestAnimationFrame(() => {
                const searchTime = performance.now() - searchStartTime;
                if (searchTime > 100) { // Log slow searches
                    console.warn(`Slow search detected: ${searchTime.toFixed(2)}ms`);
                }
            });
        };
    }

    // Monitor filter performance
    let filterStartTime;
    const originalApplyFilters = window.applyFilters;

    if (originalApplyFilters) {
        window.applyFilters = function() {
            filterStartTime = performance.now();
            originalApplyFilters.apply(this, arguments);

            requestAnimationFrame(() => {
                const filterTime = performance.now() - filterStartTime;
                if (filterTime > 50) { // Log slow filters
                    console.warn(`Slow filter detected: ${filterTime.toFixed(2)}ms`);
                }
            });
        };
    }
}

// Add keyboard focus styles
const keyboardFocusCSS = `
.finding-item.keyboard-focused {
    outline: 2px solid var(--primary500);
    outline-offset: 2px;
    background: var(--glass-white-strong);
    transform: translateY(-2px);
    box-shadow: var(--glass-shadow-lg);
}

.keyboard-shortcuts-help {
    position: fixed;
    bottom: 1rem;
    left: 1rem;
    background: var(--glass-white);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-sm);
    padding: 0.75rem;
    font-size: 0.75rem;
    color: var(--text-secondary);
    z-index: 1000;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.keyboard-shortcuts-help:hover {
    opacity: 1;
}
`;

if (!document.getElementById('keyboard-focus-styles')) {
    const style = document.createElement('style');
    style.id = 'keyboard-focus-styles';
    style.textContent = keyboardFocusCSS;
    document.head.appendChild(style);
}

// Add keyboard shortcuts help
function addKeyboardShortcutsHelp() {
    const help = document.createElement('div');
    help.className = 'keyboard-shortcuts-help';
    help.innerHTML = `
        <div><strong>Keyboard Shortcuts:</strong></div>
        <div>Ctrl+K: Search</div>
        <div>Ctrl+E: Export</div>
        <div>Ctrl+P: Print</div>
        <div>↑↓: Navigate</div>
        <div>Esc: Reset</div>
    `;
    document.body.appendChild(help);
}

// Initialize utility functions when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    addGlassEffects();
    initLineNumberHighlighting();
    initTooltips();
    initKeyboardShortcuts();
    initPerformanceMonitoring();
    addKeyboardShortcutsHelp();
});


// Dialog functionality
// Code Dialog Functions for IaC Guardian Reports

// Global variable to track current dialog request
let currentDialogRequestId = 0;

function showCodeDialog(filePath, lineNumber, controlId, severity) {
    // Create dialog if it doesn't exist
    let dialog = document.getElementById('code-dialog-overlay');
    if (!dialog) {
        createCodeDialog();
        dialog = document.getElementById('code-dialog-overlay');
    }

    // Update dialog content
    updateCodeDialog(filePath, lineNumber, controlId, severity);

    // Show dialog
    dialog.style.display = 'flex';
    document.body.style.overflow = 'hidden';

    // Focus trap
    dialog.querySelector('.code-dialog-close').focus();
}

function createCodeDialog() {
    const dialogHTML = `
        <div id="code-dialog-overlay" class="code-dialog-overlay" onclick="closeCodeDialog(event)">
            <div class="code-dialog" onclick="event.stopPropagation()">
                <div class="code-dialog-header">
                    <div class="code-dialog-title">
                        <i class="fas fa-file-code"></i>
                        <span id="dialog-title">Code Snippet</span>
                    </div>
                    <button class="code-dialog-close" onclick="closeCodeDialog()" title="Close dialog">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="code-dialog-content">
                    <div class="code-snippet-container">
                        <div class="code-snippet-with-lines">
                            <div class="line-numbers" id="line-numbers"></div>
                            <div class="code-content" id="code-content"></div>
                        </div>
                    </div>
                </div>
                <div class="code-dialog-footer">
                    <div class="code-dialog-info">
                        <span id="dialog-file-info"></span>
                        <span id="dialog-severity-info"></span>
                    </div>
                    <div class="code-dialog-actions">
                        <button class="code-dialog-btn" onclick="copyCodeSnippet()" title="Copy code to clipboard">
                            <i class="fas fa-copy"></i>
                            Copy Code
                        </button>
                        <button class="code-dialog-btn primary" onclick="closeCodeDialog()">
                            <i class="fas fa-check"></i>
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', dialogHTML);

    // Add dialog-specific CSS styles
    addDialogStyles();

    // Add keyboard event listener
    document.addEventListener('keydown', handleDialogKeydown);
}

function addDialogStyles() {
    if (!document.getElementById('code-dialog-styles')) {
        const dialogCSS = `
        <style id="code-dialog-styles">
        /* Code Dialog Overlay */
        .code-dialog-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            padding: 1rem;
        }

        /* Code Dialog Container */
        .code-dialog {
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur-strong);
            -webkit-backdrop-filter: var(--glass-blur-strong);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--glass-shadow-xl);
            max-width: 90vw;
            max-height: 90vh;
            width: 1000px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* Dialog Header */
        .code-dialog-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid var(--glass-border);
            background: var(--glass-white-light);
        }

        .code-dialog-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-on-glass);
        }

        .code-dialog-close {
            background: none;
            border: none;
            color: var(--text-interactive);
            font-size: 1.25rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: var(--border-radius-sm);
            transition: all 0.2s ease;
        }

        .code-dialog-close:hover {
            background: var(--glass-white-strong);
            color: var(--text-hover);
            transform: scale(1.1);
        }

        /* Dialog Content */
        .code-dialog-content {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .code-snippet-container {
            flex: 1;
            overflow: auto;
            background: var(--dark-gray100);
            border-radius: var(--border-radius-sm);
            margin: 1rem;
        }

        .code-snippet-with-lines {
            display: flex;
            min-height: 100%;
        }

        /* Line Numbers */
        .line-numbers {
            background: var(--dark-gray50);
            color: var(--text-muted);
            padding: 1rem 0.75rem;
            font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
            font-size: 0.875rem;
            line-height: 1.5;
            text-align: right;
            user-select: none;
            border-right: 1px solid var(--glass-border);
            min-width: 4rem;
        }

        .line-number {
            padding: 0.125rem 0;
            transition: all 0.2s ease;
        }

        .line-number.highlighted {
            background: var(--critical-glass);
            color: var(--critical-text);
            font-weight: 600;
            border-radius: 4px;
            margin: 0 -0.25rem;
            padding: 0.125rem 0.25rem;
        }

        .line-number.highlighted-security {
            background: var(--danger-red);
            color: white;
        }

        /* Code Content */
        .code-content {
            flex: 1;
            padding: 1rem;
            font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
            font-size: 0.875rem;
            line-height: 1.5;
            color: var(--text-on-glass);
            overflow-x: auto;
        }

        .code-line {
            padding: 0.125rem 0;
            white-space: pre;
            transition: all 0.2s ease;
        }

        .code-line.highlighted {
            background: var(--critical-glass);
            border-radius: 4px;
            margin: 0 -0.5rem;
            padding: 0.125rem 0.5rem;
            border-left: 4px solid var(--critical-text);
        }

        .code-line.highlighted-security {
            background: var(--danger-red);
            color: white;
        }

        .security-marker {
            color: var(--critical-text);
            font-weight: 600;
            margin-left: 1rem;
            font-size: 0.75rem;
            opacity: 0.8;
        }

        /* Dialog Footer */
        .code-dialog-footer {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1.5rem 2rem;
            border-top: 1px solid var(--glass-border);
            background: var(--glass-white-light);
        }

        .code-dialog-info {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .code-dialog-actions {
            display: flex;
            gap: 1rem;
        }

        .code-dialog-btn {
            padding: 0.75rem 1.5rem;
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-sm);
            background: var(--glass-white-light);
            color: var(--text-interactive);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.875rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .code-dialog-btn:hover {
            background: var(--glass-white-strong);
            transform: translateY(-2px);
            box-shadow: var(--glass-shadow);
        }

        .code-dialog-btn.primary {
            background: var(--primary500);
            color: var(--text-on-dark);
            border-color: var(--primary600);
        }

        .code-dialog-btn.primary:hover {
            background: var(--primary600);
        }

        /* Content Warning */
        .content-warning {
            background: var(--warning-amber);
            color: var(--text-on-dark);
            padding: 0.75rem 1rem;
            margin-bottom: 1rem;
            border-radius: var(--border-radius-sm);
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: var(--glass-shadow);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .code-dialog {
                width: 95vw;
                max-height: 85vh;
            }

            .code-dialog-header,
            .code-dialog-footer {
                padding: 1rem;
            }

            .code-dialog-footer {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }

            .code-dialog-actions {
                justify-content: center;
            }

            .line-numbers {
                min-width: 3rem;
                padding: 1rem 0.5rem;
            }

            .code-content {
                padding: 1rem 0.5rem;
            }
        }
        </style>
        `;
        document.head.insertAdjacentHTML('beforeend', dialogCSS);
    }
}

function updateCodeDialog(filePath, lineNumber, controlId, severity) {
    // Generate unique request ID to prevent race conditions
    const requestId = ++currentDialogRequestId;
    console.log('🔄 Updating code dialog for:', filePath, 'line:', lineNumber, 'control:', controlId, 'requestId:', requestId);

    // Clear any existing content and warnings first
    clearCodeDialogContent();

    // Show loading state
    showCodeDialogLoading();

    // Ensure dialog exists before updating
    if (!document.getElementById('code-dialog-overlay')) {
        createCodeDialog();
    }

    // Update title and info with safety checks
    const titleEl = document.getElementById('dialog-title');
    const fileInfoEl = document.getElementById('dialog-file-info');
    const severityInfoEl = document.getElementById('dialog-severity-info');

    if (titleEl) {
        titleEl.textContent = `${controlId} - Code Snippet`;
    } else {
        console.error('❌ dialog-title element not found');
    }

    if (fileInfoEl) {
        fileInfoEl.innerHTML = `<i class="fas fa-file"></i> ${filePath}`;
    } else {
        console.error('❌ dialog-file-info element not found');
    }

    if (severityInfoEl) {
        severityInfoEl.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${severity.toUpperCase()} Severity`;
    } else {
        console.error('❌ dialog-severity-info element not found');
    }

    // Fetch real file content with ±100 lines context
    fetchRealFileContentWithContext(filePath, lineNumber).then(content => {
        // Check if this is still the current request (prevent race conditions)
        if (requestId !== currentDialogRequestId) {
            console.log('🚫 Ignoring outdated request', requestId, 'current is', currentDialogRequestId);
            return;
        }

        // Hide loading state
        hideCodeDialogLoading();

        if (content.success) {
            displayRealCodeWithLineNumbers(content, lineNumber);
            console.log('✅ Successfully displayed real file content for', filePath, 'line', lineNumber, 'requestId:', requestId);
        } else {
            // Fallback to sample code if real content unavailable
            const sampleCode = generateSampleCode(filePath, lineNumber, severity);
            displayCodeWithLineNumbers(sampleCode, lineNumber);

            // Show detailed warning about fallback content
            const errorMsg = content.error || 'File content not embedded in report';
            showContentWarning(`Real file content unavailable: ${errorMsg}. Showing representative example.`);
            console.warn('⚠️ Using fallback sample code for', filePath, ':', errorMsg);
        }
    }).catch(error => {
        // Check if this is still the current request
        if (requestId !== currentDialogRequestId) {
            console.log('🚫 Ignoring outdated error for request', requestId);
            return;
        }

        // Hide loading state
        hideCodeDialogLoading();

        console.error('❌ Failed to fetch real file content for', filePath, ':', error);
        const sampleCode = generateSampleCode(filePath, lineNumber, severity);
        displayCodeWithLineNumbers(sampleCode, lineNumber);
        showContentWarning(`Error loading file content: ${error.message || error}. Showing representative example.`);
    });
}

function clearCodeDialogContent() {
    // Ensure dialog exists
    if (!document.getElementById('code-dialog-overlay')) {
        console.log('🧹 Dialog not found, creating it first');
        createCodeDialog();
        return;
    }

    // Clear previous content with safety checks
    const lineNumbersEl = document.getElementById('line-numbers');
    const codeContentEl = document.getElementById('code-content');

    if (lineNumbersEl) {
        lineNumbersEl.innerHTML = '';
    } else {
        console.warn('⚠️ line-numbers element not found');
    }

    if (codeContentEl) {
        codeContentEl.innerHTML = '';
    } else {
        console.warn('⚠️ code-content element not found');
    }

    // Remove any existing warnings
    const dialogContent = document.querySelector('.code-dialog-content');
    if (dialogContent) {
        const existingWarning = dialogContent.querySelector('.content-warning');
        if (existingWarning) {
            existingWarning.remove();
        }
    } else {
        console.warn('⚠️ code-dialog-content element not found');
    }

    console.log('🧹 Cleared previous code dialog content');
}

function showCodeDialogLoading() {
    // Ensure dialog exists
    if (!document.getElementById('code-dialog-overlay')) {
        console.log('📤 Dialog not found, creating it first');
        createCodeDialog();
    }

    const codeContentEl = document.getElementById('code-content');
    if (codeContentEl) {
        codeContentEl.innerHTML = `
            <div style="
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 2rem;
                color: var(--text-accent);
                font-size: 0.875rem;
            ">
                <i class="fas fa-spinner fa-spin" style="margin-right: 0.5rem;"></i>
                Loading file content...
            </div>
        `;
    } else {
        console.error('❌ code-content element not found for loading state');
    }
}

function hideCodeDialogLoading() {
    // Loading content will be replaced by actual content, so no explicit action needed
    console.log('📤 Loading state will be replaced by content');
}

function closeCodeDialog(event) {
    if (event && event.target !== event.currentTarget) {
        return; // Don't close if clicking inside dialog
    }

    const dialog = document.getElementById('code-dialog-overlay');
    if (dialog) {
        dialog.style.display = 'none';
        document.body.style.overflow = '';
    }
}

function handleDialogKeydown(event) {
    if (event.key === 'Escape') {
        closeCodeDialog();
    }
}

function copyCodeSnippet() {
    const codeContent = document.getElementById('code-content');
    if (codeContent) {
        // Get clean text content without HTML tags
        let text = '';
        const codeLines = codeContent.querySelectorAll('.code-line');

        if (codeLines.length > 0) {
            // Extract text from each line, preserving line breaks
            codeLines.forEach((line, index) => {
                // Remove security markers from copied text
                const lineText = line.textContent || line.innerText;
                const cleanText = lineText.replace(/\s*← Security Issue\s*$/, '');
                text += cleanText;
                if (index < codeLines.length - 1) {
                    text += '\n';
                }
            });
        } else {
            // Fallback to full content
            text = codeContent.textContent || codeContent.innerText;
        }

        // Copy to clipboard
        navigator.clipboard.writeText(text).then(() => {
            // Show feedback
            const btn = event.target.closest('button');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
            btn.style.background = 'var(--success-green)';
            btn.style.borderColor = 'var(--success-green)';

            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.style.background = '';
                btn.style.borderColor = '';
            }, 2000);

            // Also show a toast notification
            showCopyToast('Code snippet copied to clipboard!');
        }).catch(err => {
            console.error('Failed to copy code:', err);

            // Fallback for older browsers
            try {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.opacity = '0';
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                showCopyToast('Code snippet copied to clipboard!');
            } catch (fallbackErr) {
                console.error('Fallback copy also failed:', fallbackErr);
                showCopyToast('Failed to copy code snippet', 'error');
            }
        });
    }
}

function showCopyToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: ${type === 'success' ? 'var(--success-green)' : 'var(--danger-red)'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: var(--border-radius-sm);
        box-shadow: var(--glass-shadow-xl);
        z-index: 10003;
        font-size: 0.875rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        animation: copyToastAnimation 0.3s ease;
    `;

    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : 'exclamation-triangle'}"></i>
        ${message}
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.style.animation = 'copyToastFadeOut 0.3s ease';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 2000);
}

// Add copy toast animations
const copyToastCSS = `
@keyframes copyToastAnimation {
    0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
    100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
}

@keyframes copyToastFadeOut {
    0% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
    100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
}
`;

if (!document.getElementById('copy-toast-styles')) {
    const style = document.createElement('style');
    style.id = 'copy-toast-styles';
    style.textContent = copyToastCSS;
    document.head.appendChild(style);
}

function generateSampleCode(filePath, lineNumber, severity) {
    // This is a sample code generator - in real implementation, you'd fetch actual file content
    const fileExtension = filePath.split('.').pop().toLowerCase();

    if (fileExtension === 'bicep') {
        return `// Azure Bicep Template
param location string = resourceGroup().location
param storageAccountName string

resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {
  name: storageAccountName
  location: location
  kind: 'StorageV2'
  sku: {
    name: 'Standard_LRS'  // ← Security issue on this line
  }
  properties: {
    allowBlobPublicAccess: true  // ← Potential security risk
    minimumTlsVersion: 'TLS1_0'  // ← Outdated TLS version
    supportsHttpsTrafficOnly: false
  }
}`;
    } else if (fileExtension === 'tf') {
        return `# Terraform Configuration
resource "azurerm_storage_account" "example" {
  name                     = var.storage_account_name
  resource_group_name      = var.resource_group_name
  location                 = var.location
  account_tier             = "Standard"
  account_replication_type = "LRS"  # ← Security issue on this line
  
  # Security configurations
  allow_blob_public_access = true    # ← Potential security risk
  min_tls_version         = "TLS1_0" # ← Outdated TLS version
  https_traffic_only      = false    # ← Should be true
}`;
    } else {
        return `{
  "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
  "contentVersion": "1.0.0.0",
  "parameters": {
    "storageAccountName": {
      "type": "string"
    }
  },
  "resources": [
    {
      "type": "Microsoft.Storage/storageAccounts",
      "apiVersion": "2021-04-01",
      "name": "[parameters('storageAccountName')]",
      "location": "[resourceGroup().location]",
      "sku": {
        "name": "Standard_LRS"
      },
      "properties": {
        "allowBlobPublicAccess": true,
        "minimumTlsVersion": "TLS1_0",
        "supportsHttpsTrafficOnly": false
      }
    }
  ]
}`;
    }
}

function displayCodeWithLineNumbers(code, highlightLine) {
    const lines = code.split('\n');
    const lineNumbersEl = document.getElementById('line-numbers');
    const codeContentEl = document.getElementById('code-content');

    if (!lineNumbersEl || !codeContentEl) {
        console.error('❌ Line numbers or code content elements not found');
        return;
    }

    // Generate line numbers
    let lineNumbersHTML = '';
    let codeHTML = '';

    lines.forEach((line, index) => {
        const lineNum = index + 1;
        const isHighlighted = lineNum === parseInt(highlightLine);
        
        lineNumbersHTML += `<div class="line-number ${isHighlighted ? 'highlighted' : ''}">${lineNum}</div>`;
        codeHTML += `<div class="code-line ${isHighlighted ? 'highlighted' : ''}">${escapeHtml(line)}</div>`;
    });

    lineNumbersEl.innerHTML = lineNumbersHTML;
    codeContentEl.innerHTML = codeHTML;

    // Scroll to highlighted line
    setTimeout(() => {
        const highlightedLine = codeContentEl.querySelector('.code-line.highlighted');
        if (highlightedLine) {
            highlightedLine.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }, 100);
}

function displayRealCodeWithLineNumbers(contextData, highlightLine) {
    const lineNumbersEl = document.getElementById('line-numbers');
    const codeContentEl = document.getElementById('code-content');

    if (!lineNumbersEl || !codeContentEl) {
        console.error('❌ Line numbers or code content elements not found');
        return;
    }

    console.log('📊 Displaying real code with context:', {
        start_line: contextData.start_line,
        end_line: contextData.end_line,
        total_lines: contextData.total_lines,
        content_lines_count: contextData.content_lines?.length,
        highlight_line: highlightLine
    });

    const contentLines = contextData.content_lines;
    if (!contentLines || !Array.isArray(contentLines)) {
        console.error('❌ Invalid content_lines data:', contentLines);
        return;
    }

    // Generate line numbers and code content
    let lineNumbersHTML = '';
    let codeHTML = '';

    contentLines.forEach((lineInfo) => {
        const lineNum = lineInfo.number;
        const lineContent = lineInfo.content || '';
        const isHighlighted = lineInfo.highlighted || lineNum === parseInt(highlightLine);

        // Add severity-based styling for highlighted lines
        const severityClass = isHighlighted ? 'highlighted-security' : '';
        const securityMarker = isHighlighted ? ' <span class="security-marker">← Security Issue</span>' : '';

        lineNumbersHTML += `<div class="line-number ${isHighlighted ? 'highlighted' : ''} ${severityClass}">${lineNum}</div>`;
        codeHTML += `<div class="code-line ${isHighlighted ? 'highlighted' : ''} ${severityClass}">${escapeHtml(lineContent)}${securityMarker}</div>`;
    });

    lineNumbersEl.innerHTML = lineNumbersHTML;
    codeContentEl.innerHTML = codeHTML;

    // Scroll to highlighted line
    setTimeout(() => {
        const highlightedLine = codeContentEl.querySelector('.code-line.highlighted');
        if (highlightedLine) {
            highlightedLine.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }, 100);

    console.log('✅ Successfully displayed real code with line numbers');
}

function showContentWarning(message) {
    const dialogContent = document.querySelector('.code-dialog-content');
    if (dialogContent) {
        // Remove any existing warning
        const existingWarning = dialogContent.querySelector('.content-warning');
        if (existingWarning) {
            existingWarning.remove();
        }

        // Create new warning
        const warning = document.createElement('div');
        warning.className = 'content-warning';
        warning.style.cssText = `
            background: var(--warning-amber);
            color: var(--text-on-dark);
            padding: 0.75rem 1rem;
            margin-bottom: 1rem;
            border-radius: var(--border-radius-sm);
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: var(--glass-shadow);
        `;
        warning.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            <span>${message}</span>
        `;

        // Insert at the beginning of dialog content
        dialogContent.insertBefore(warning, dialogContent.firstChild);
    }
}

function parseFileContentWithAccurateLines(content, targetLine, contextLines) {
    // Split content into lines (preserving empty lines)
    const lines = content.split('\n');
    const totalLines = lines.length;
    const targetLineNum = parseInt(targetLine);

    // Validate target line
    if (targetLineNum < 1 || targetLineNum > totalLines) {
        return {
            success: false,
            error: `Line ${targetLineNum} out of range (1-${totalLines})`
        };
    }

    // Calculate context range with specified context lines
    const startLine = Math.max(1, targetLineNum - contextLines);
    const endLine = Math.min(totalLines, targetLineNum + contextLines);

    // Build content lines with accurate numbering
    const contentLines = [];
    for (let lineNum = startLine; lineNum <= endLine; lineNum++) {
        const lineIndex = lineNum - 1; // Convert to 0-based index
        contentLines.push({
            number: lineNum,
            content: lines[lineIndex] || '',
            highlighted: lineNum === targetLineNum
        });
    }

    return {
        success: true,
        file_path: filePath,
        total_lines: totalLines,
        highlighted_line: targetLineNum,
        start_line: startLine,
        end_line: endLine,
        context_size: contextLines,
        content_lines: contentLines
    };
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

    </script>
    
    

            <script>
            // Enhanced Validation Metadata (for internal use only)
            window.validationMetadata = {
  "generation_timestamp": "2025-06-20T18:46:29.215985",
  "total_findings": 7,
  "validation_statistics": {
    "total_validations": 0,
    "successful_validations": 0,
    "corrections_made": 0,
    "fictional_ids_prevented": 0,
    "domain_corrections": 0
  },
  "analysis_statistics": {},
  "control_id_validation": {
    "total_validations": 0,
    "success_rate": 100.0,
    "corrections_made": 0,
    "fictional_ids_prevented": 0
  },
  "cross_reference_analysis": {
    "cross_ref_findings": 0,
    "template_relationships": 2,
    "security_boundaries_analyzed": 0
  },
  "domain_distribution": {
    "Network Security": 4,
    "Data Protection": 3
  },
  "severity_distribution": {
    "CRITICAL": 7
  },
  "resource_type_coverage": {
    "unique_resource_types": 0,
    "analyzed_files": 2,
    "resource_types": []
  },
  "benchmark_version": "Azure Security Benchmark v3.0",
  "analysis_configuration": {
    "domain_priority_enabled": true,
    "optimized_prompts_enabled": true,
    "control_id_validation_enabled": true,
    "cross_reference_analysis_enabled": true
  }
};

            // Tooltip Links Data
            window.tooltipLinks = {
  "NS-1": {
    "formatted_links": "[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)",
    "azure_guidance": "Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices\n\u2022 Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet\n\u2022 NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic\n\u2022 Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups\n\u2022 Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nCompliance Mappings:\n\u2022 CIS Controls v8: 3.12, 13.4, 4.4\n\u2022 NIST SP800-53 r4: AC-4, SC-2, SC-7\n\u2022 PCI-DSS v3.2.1: 1.1, 1.2, 1.3\n\nAzure Policy Examples:\n\u2022 Adaptive network hardening recommendations should be applied on internet facing virtual machines\n\u2022 All network ports should be restricted on network security groups associated to your virtual machine\n\u2022 Subnets should be associated with a Network Security Group",
    "raw_links": [
      "https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices",
      "https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet",
      "https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic",
      "https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups",
      "https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture"
    ]
  },
  "NS-2": {
    "formatted_links": "[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)",
    "azure_guidance": "Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview\n\u2022 Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints\n\u2022 SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview\n\u2022 Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service\n\u2022 Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nCompliance Mappings:\n\u2022 CIS Controls v8: 3.12, 4.4\n\u2022 NIST SP800-53 r4: AC-4, SC-2, SC-7\n\u2022 PCI-DSS v3.2.1: 1.1, 1.2, 1.3\n\nAzure Policy Examples:\n\u2022 Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers\n\u2022 Storage accounts should use private link\n\u2022 Azure SQL Database should disable public network access\n\u2022 Cognitive Services accounts should restrict network access\n\u2022 Container registries should use private link",
    "raw_links": [
      "https://docs.microsoft.com/azure/private-link/private-link-overview",
      "https://docs.microsoft.com/azure/storage/common/storage-private-endpoints",
      "https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview",
      "https://docs.microsoft.com/azure/key-vault/general/private-link-service",
      "https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture"
    ]
  },
  "DP-3": {
    "formatted_links": "[Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account)",
    "azure_guidance": "Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windows remote management.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Double encryption for Azure data in transit: https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit\n\u2022 Encryption in transit overview: https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit\n\u2022 TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem\n\u2022 Azure Storage secure transfer: https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account\n\nCompliance Mappings:\n\u2022 CIS Controls v8: 3.10\n\u2022 NIST SP800-53 r4: SC-8\n\u2022 PCI-DSS v3.2.1: 3.5, 3.6, 4.1\n\nAzure Policy Examples:\n\u2022 Kubernetes clusters should be accessible only over HTTPS\n\u2022 Only secure connections to your Azure Cache for Redis should be enabled\n\u2022 FTPS only should be required in your Function App\n\u2022 Secure transfer to storage accounts should be enabled\n\u2022 Function App should only be accessible over HTTPS\n\u2022 Latest TLS version should be used in your API App\n\u2022 Web Application should only be accessible over HTTPS\n\u2022 Enforce SSL connection should be enabled for PostgreSQL database servers\n\u2022 Latest TLS version should be used in your Web App",
    "raw_links": [
      "https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit",
      "https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit",
      "https://docs.microsoft.com/security/engineering/solving-tls1-problem",
      "https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account"
    ]
  },
  "NS-8": {
    "formatted_links": "[Enhanced Implementation Context](https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Secure protocol configuration](https://docs.microsoft.com/azure/security/fundamentals/network-best-practices) | [Network security monitoring](https://docs.microsoft.com/azure/security/fundamentals/network-monitoring) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)",
    "azure_guidance": "Use Azure Sentinel's Insecure Protocol Workbook to discover insecure services like SSL/TLSv1 SSHv1 SMBv1 LM/NTLMv1. Disable insecure protocols or use compensating controls like NSG/Firewall blocking.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Azure Sentinel insecure protocols workbook: https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks\n\u2022 TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem\n\u2022 Secure protocol configuration: https://docs.microsoft.com/azure/security/fundamentals/network-best-practices\n\u2022 Network security monitoring: https://docs.microsoft.com/azure/security/fundamentals/network-monitoring\n\u2022 Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nCompliance Mappings:\n\u2022 CIS Controls v8: 4.4, 4.8\n\u2022 NIST SP800-53 r4: CM-2, CM-6, CM-7\n\u2022 PCI-DSS v3.2.1: 4.1, A2.1, A2.2, A2.3\n\nAzure Policy Examples:\n\u2022 Latest TLS version should be used in your API App\n\u2022 Latest TLS version should be used in your Web App\n\u2022 Latest TLS version should be used in your Function App\n\u2022 Secure transfer to storage accounts should be enabled",
    "raw_links": [
      "https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks",
      "https://docs.microsoft.com/security/engineering/solving-tls1-problem",
      "https://docs.microsoft.com/azure/security/fundamentals/network-best-practices",
      "https://docs.microsoft.com/azure/security/fundamentals/network-monitoring",
      "https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture"
    ]
  }
};

            // Initialize tooltip functionality
            document.addEventListener('DOMContentLoaded', function() {
                // Add tooltip functionality to control IDs
                addTooltipFunctionality();
            });

            function addTooltipFunctionality() {
                // Find all control ID elements and add tooltips
                const controlElements = document.querySelectorAll('.control-id, .finding-control');

                controlElements.forEach(element => {
                    const controlId = element.textContent.trim();
                    if (window.tooltipLinks[controlId]) {
                        const tooltipData = window.tooltipLinks[controlId];

                        // Add tooltip icon
                        const tooltipIcon = document.createElement('span');
                        tooltipIcon.className = 'tooltip-icon';
                        tooltipIcon.innerHTML = ' 📚';
                        tooltipIcon.style.cursor = 'pointer';
                        tooltipIcon.style.marginLeft = '5px';
                        tooltipIcon.style.fontSize = '0.8em';

                        // Create tooltip content
                        let tooltipContent = '<div class="tooltip-content">';

                        if (tooltipData.azure_guidance) {
                            tooltipContent += `<div class="tooltip-section">
                                <strong>🔵 Azure Guidance:</strong><br>
                                ${tooltipData.azure_guidance.substring(0, 200)}${tooltipData.azure_guidance.length > 200 ? '...' : ''}
                            </div>`;
                        }

                        if (tooltipData.raw_links && tooltipData.raw_links.length > 0) {
                            tooltipContent += '<div class="tooltip-section"><strong>📚 Reference Links:</strong><br>';
                            tooltipData.raw_links.forEach((link, index) => {
                                const linkText = link.length > 50 ? link.substring(0, 50) + '...' : link;
                                tooltipContent += `<a href="${link}" target="_blank" class="tooltip-link">${linkText}</a><br>`;
                            });
                            tooltipContent += '</div>';
                        }

                        tooltipContent += '</div>';

                        // Add tooltip functionality
                        tooltipIcon.setAttribute('data-tooltip', tooltipContent);
                        element.appendChild(tooltipIcon);

                        // Add click handler to show tooltip
                        tooltipIcon.addEventListener('click', function(e) {
                            e.preventDefault();
                            showTooltip(e.target, tooltipContent);
                        });
                    }
                });
            }

            function showTooltip(element, content) {
                // Remove existing tooltips
                const existingTooltips = document.querySelectorAll('.custom-tooltip');
                existingTooltips.forEach(tooltip => tooltip.remove());

                // Create new tooltip
                const tooltip = document.createElement('div');
                tooltip.className = 'custom-tooltip';
                tooltip.innerHTML = content;

                // Position tooltip
                document.body.appendChild(tooltip);
                const rect = element.getBoundingClientRect();
                tooltip.style.position = 'fixed';
                tooltip.style.top = (rect.bottom + 10) + 'px';
                tooltip.style.left = rect.left + 'px';
                tooltip.style.zIndex = '10000';

                // Auto-hide after 10 seconds
                setTimeout(() => {
                    if (tooltip.parentNode) {
                        tooltip.remove();
                    }
                }, 10000);

                // Hide on click outside
                document.addEventListener('click', function hideTooltip(e) {
                    if (!tooltip.contains(e.target) && e.target !== element) {
                        tooltip.remove();
                        document.removeEventListener('click', hideTooltip);
                    }
                });
            }
            </script>

            <style>
            /* Removed validation statistics CSS styles - not relevant for end users */

            .tooltip-icon {
                transition: all 0.2s ease;
            }

            .tooltip-icon:hover {
                transform: scale(1.2);
                filter: brightness(1.3);
            }

            .custom-tooltip {
                background: rgba(0, 0, 0, 0.9);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 15px;
                max-width: 400px;
                color: white;
                font-size: 0.9em;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                animation: tooltipFadeIn 0.2s ease;
            }

            @keyframes tooltipFadeIn {
                from { opacity: 0; transform: translateY(-10px); }
                to { opacity: 1; transform: translateY(0); }
            }

            .tooltip-section {
                margin-bottom: 10px;
            }

            .tooltip-section:last-child {
                margin-bottom: 0;
            }

            .tooltip-link {
                color: #4ade80;
                text-decoration: none;
                word-break: break-all;
            }

            .tooltip-link:hover {
                color: #6ee7b7;
                text-decoration: underline;
            }
            </style>
            </body>
</html>
