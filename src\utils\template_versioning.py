"""
Template Versioning System for IaC Guardian

This module provides comprehensive template versioning, compatibility checking,
and migration support for the template system.
"""

import json
import re
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@dataclass
class TemplateVersion:
    """Represents a template version"""
    major: int
    minor: int
    patch: int
    
    def __str__(self) -> str:
        return f"{self.major}.{self.minor}.{self.patch}"
    
    def __lt__(self, other: 'TemplateVersion') -> bool:
        return (self.major, self.minor, self.patch) < (other.major, other.minor, other.patch)
    
    def __le__(self, other: 'TemplateVersion') -> bool:
        return (self.major, self.minor, self.patch) <= (other.major, other.minor, other.patch)
    
    def __gt__(self, other: 'TemplateVersion') -> bool:
        return (self.major, self.minor, self.patch) > (other.major, other.minor, other.patch)
    
    def __ge__(self, other: 'TemplateVersion') -> bool:
        return (self.major, self.minor, self.patch) >= (other.major, other.minor, other.patch)
    
    def __eq__(self, other: 'TemplateVersion') -> bool:
        return (self.major, self.minor, self.patch) == (other.major, other.minor, other.patch)
    
    @classmethod
    def from_string(cls, version_str: str) -> 'TemplateVersion':
        """Create TemplateVersion from string like '1.2.3'"""
        try:
            parts = version_str.strip().split('.')
            if len(parts) != 3:
                raise ValueError(f"Invalid version format: {version_str}")
            
            major, minor, patch = map(int, parts)
            return cls(major, minor, patch)
        except (ValueError, TypeError) as e:
            raise ValueError(f"Invalid version string '{version_str}': {e}")

@dataclass
class CompatibilityRule:
    """Represents a compatibility rule between versions"""
    min_version: TemplateVersion
    max_version: Optional[TemplateVersion]
    breaking_changes: List[str]
    migration_notes: List[str]

class TemplateVersionManager:
    """Manages template versioning and compatibility"""
    
    def __init__(self, templates_dir: Path, config: Dict[str, Any]):
        """
        Initialize version manager
        
        Args:
            templates_dir: Path to templates directory
            config: Versioning configuration
        """
        self.templates_dir = Path(templates_dir)
        self.config = config
        self.enabled = config.get('enabled', True)
        self.version_file = self.templates_dir / config.get('version_file', 'VERSION')
        self.compatibility_file = self.templates_dir / 'config' / 'compatibility.json'
        self.version_header = config.get('version_header', '<!-- Template Version: {version} -->')
        
        self.current_version = self._load_current_version()
        self.compatibility_rules = self._load_compatibility_rules()
        self.min_supported_version = TemplateVersion.from_string(
            config.get('min_supported_version', '1.0.0')
        )
    
    def _load_current_version(self) -> TemplateVersion:
        """Load current template version"""
        if not self.version_file.exists():
            logger.warning(f"Version file not found: {self.version_file}")
            return TemplateVersion(1, 0, 0)
        
        try:
            with open(self.version_file, 'r') as f:
                version_str = f.read().strip()
            
            version = TemplateVersion.from_string(version_str)
            logger.info(f"Loaded template version: {version}")
            return version
            
        except Exception as e:
            logger.error(f"Failed to load version file: {e}")
            return TemplateVersion(1, 0, 0)
    
    def _load_compatibility_rules(self) -> List[CompatibilityRule]:
        """Load compatibility rules from configuration"""
        if not self.compatibility_file.exists():
            logger.info("No compatibility rules file found, using defaults")
            return []
        
        try:
            with open(self.compatibility_file, 'r') as f:
                data = json.load(f)
            
            rules = []
            for rule_data in data.get('compatibility_rules', []):
                min_version = TemplateVersion.from_string(rule_data['min_version'])
                max_version = None
                if rule_data.get('max_version'):
                    max_version = TemplateVersion.from_string(rule_data['max_version'])
                
                rule = CompatibilityRule(
                    min_version=min_version,
                    max_version=max_version,
                    breaking_changes=rule_data.get('breaking_changes', []),
                    migration_notes=rule_data.get('migration_notes', [])
                )
                rules.append(rule)
            
            logger.info(f"Loaded {len(rules)} compatibility rules")
            return rules
            
        except Exception as e:
            logger.error(f"Failed to load compatibility rules: {e}")
            return []
    
    def check_compatibility(self, required_version: str) -> Tuple[bool, List[str]]:
        """
        Check if current version is compatible with required version
        
        Args:
            required_version: Required template version string
            
        Returns:
            Tuple of (is_compatible, warnings/errors)
        """
        if not self.enabled:
            return True, []
        
        try:
            required = TemplateVersion.from_string(required_version)
            messages = []
            
            # Check if required version is supported
            if required < self.min_supported_version:
                messages.append(
                    f"Required version {required} is below minimum supported {self.min_supported_version}"
                )
                return False, messages
            
            # Check if current version meets requirements
            if self.current_version < required:
                messages.append(
                    f"Current version {self.current_version} is below required {required}"
                )
                return False, messages
            
            # Check compatibility rules
            for rule in self.compatibility_rules:
                if (required >= rule.min_version and 
                    (rule.max_version is None or required <= rule.max_version)):
                    
                    if rule.breaking_changes:
                        messages.extend([f"Breaking change: {change}" for change in rule.breaking_changes])
                    
                    if rule.migration_notes:
                        messages.extend([f"Migration note: {note}" for note in rule.migration_notes])
            
            return True, messages
            
        except Exception as e:
            logger.error(f"Error checking version compatibility: {e}")
            return False, [f"Version compatibility check failed: {e}"]
    
    def extract_template_version(self, content: str) -> Optional[TemplateVersion]:
        """
        Extract version from template content
        
        Args:
            content: Template content
            
        Returns:
            TemplateVersion if found, None otherwise
        """
        # Look for version header in content
        version_pattern = r'<!--\s*Template Version:\s*(\d+\.\d+\.\d+)\s*-->'
        match = re.search(version_pattern, content)
        
        if match:
            try:
                return TemplateVersion.from_string(match.group(1))
            except ValueError:
                pass
        
        # Look for other version patterns
        patterns = [
            r'@version\s+(\d+\.\d+\.\d+)',
            r'version:\s*["\'](\d+\.\d+\.\d+)["\']',
            r'VERSION\s*=\s*["\'](\d+\.\d+\.\d+)["\']'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                try:
                    return TemplateVersion.from_string(match.group(1))
                except ValueError:
                    continue
        
        return None
    
    def add_version_header(self, content: str, version: Optional[TemplateVersion] = None) -> str:
        """
        Add version header to template content
        
        Args:
            content: Template content
            version: Version to add (uses current if None)
            
        Returns:
            Content with version header
        """
        if not self.enabled:
            return content
        
        version = version or self.current_version
        header = self.version_header.format(version=str(version))
        
        # Check if version header already exists
        if self.extract_template_version(content):
            # Replace existing version header
            version_pattern = r'<!--\s*Template Version:\s*\d+\.\d+\.\d+\s*-->'
            content = re.sub(version_pattern, header, content)
        else:
            # Add new version header at the beginning
            if content.strip().startswith('<!DOCTYPE'):
                # Insert after DOCTYPE for HTML
                lines = content.split('\n')
                lines.insert(1, header)
                content = '\n'.join(lines)
            else:
                # Insert at the very beginning
                content = header + '\n' + content
        
        return content
    
    def update_version(self, new_version: str) -> bool:
        """
        Update the current template version
        
        Args:
            new_version: New version string
            
        Returns:
            True if successful, False otherwise
        """
        try:
            version = TemplateVersion.from_string(new_version)
            
            # Write to version file
            with open(self.version_file, 'w') as f:
                f.write(str(version))
            
            self.current_version = version
            logger.info(f"Updated template version to {version}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update version: {e}")
            return False
    
    def get_version_info(self) -> Dict[str, Any]:
        """Get comprehensive version information"""
        return {
            'current_version': str(self.current_version),
            'min_supported_version': str(self.min_supported_version),
            'version_file': str(self.version_file),
            'compatibility_rules_count': len(self.compatibility_rules),
            'versioning_enabled': self.enabled,
            'version_header_template': self.version_header
        }
    
    def validate_version_consistency(self) -> List[str]:
        """
        Validate version consistency across all templates
        
        Returns:
            List of inconsistency warnings
        """
        warnings = []
        
        if not self.enabled:
            return warnings
        
        # Check all template files for version consistency
        template_files = []
        for ext in ['.html', '.css', '.js', '.txt']:
            template_files.extend(self.templates_dir.rglob(f'*{ext}'))
        
        for template_file in template_files:
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                template_version = self.extract_template_version(content)
                if template_version and template_version != self.current_version:
                    warnings.append(
                        f"Version mismatch in {template_file}: "
                        f"found {template_version}, expected {self.current_version}"
                    )
                    
            except Exception as e:
                warnings.append(f"Failed to check version in {template_file}: {e}")
        
        return warnings
