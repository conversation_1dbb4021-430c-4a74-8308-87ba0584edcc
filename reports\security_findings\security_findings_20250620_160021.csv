File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
app-config.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyValues' flows across template boundary,Secure parameter 'keyValues' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'app_insights_key' flows across template boundary,Secure parameter 'app_insights_key' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyvault_name' flows across template boundary,Secure parameter 'keyvault_name' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
keyvault.bicep,NS-1,Network Security,Establish network segmentation boundaries,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,Review template dependencies and ensure secure communication between templates,,,,cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'secrets' flows across template boundary,Secure parameter 'secrets' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
storage-accounts.bicep,NS-1,Network Security,Establish network segmentation boundaries,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,Review template dependencies and ensure secure communication between templates,,,,cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,DP-6,Data Protection,Use a secure key management process,HIGH,1.0,Parameter flow security risk: Sensitive parameters may be exposed through template dependencies,Use Key Vault references for sensitive parameters and validate parameter flow security,,,,cross_reference_analysis,parameter_flow,Validated
function-settings.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,47.0,"The storage account resource 'funcStorage' (Microsoft.Storage/storageAccounts) is declared as 'existing' on line 46, but there is no evidence in this chunk that public network access is disabled or that private endpoints are enforced. Without explicit restriction, the storage account may be accessible from public networks, enabling attackers to gain initial access, exfiltrate data, or move laterally within the environment. The blast radius includes potential exposure of all data in the storage account and compromise of any services relying on it.","Explicitly configure the storage account to disable public network access and require private endpoints. In your Bicep or ARM template, set 'publicNetworkAccess' to 'Disabled' and define a 'privateEndpoint' resource for the storage account. Review all network access rules to ensure only trusted networks can access the storage account, following Azure Policy 'RequirePrivateEndpoint' and 'DisablePublicAccess'.",,,,ai_analysis,,Validated
function-settings.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,50.0,"The App Service resource 'function' (Microsoft.Web/sites) is declared as 'existing' on line 50, but there is no evidence in this chunk that public network access is disabled or that VNet integration/private endpoints are enforced. Without explicit restriction, the App Service may be exposed to the public internet, enabling attackers to exploit vulnerabilities, gain initial access, or move laterally to other resources. The blast radius includes compromise of the application, underlying data, and any connected services.","Explicitly configure the App Service to restrict public network access. Use Access Restrictions to allow only trusted IPs or subnets, and enable VNet integration or private endpoints. In your Bicep or ARM template, define 'ipSecurityRestrictions' or 'virtualNetworkSubnetId' properties as appropriate. Follow Azure Policy 'RequirePrivateEndpoint' and 'DisablePublicAccess' for App Service resources.",,,,ai_analysis,,Validated
function.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,89.0,"The 'clientAffinityEnabled: true' property in the App Service resource (Line 89) enables client affinity (ARR Affinity), which can allow session stickiness based on client IP. This can increase the attack surface for session hijacking and lateral movement, as attackers who compromise a client or intercept traffic can maintain persistent sessions to the same backend instance, bypassing some load balancer protections. The blast radius includes potential unauthorized access to session data and increased risk of session fixation attacks.",Set 'clientAffinityEnabled' to false in the App Service configuration to disable client affinity and reduce the risk of session hijacking and lateral movement. Review application session management to ensure statelessness and secure session handling.,,,,ai_analysis,,Validated
hub-network.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,14.0,"The subnet 'hub-subnet' defined at line 14 does not specify a networkSecurityGroup (NSG) association. Without an NSG, the subnet lacks explicit inbound and outbound traffic controls, enabling potential attack vectors such as lateral movement, unauthorized access, and network-based attacks. This increases the blast radius, as any resource deployed in this subnet could be exposed to unfiltered network traffic.",Associate a networkSecurityGroup with the 'hub-subnet' by adding the 'networkSecurityGroup' property referencing a properly configured NSG resource. Ensure the NSG implements a deny-by-default policy and only allows required traffic. Example: 'networkSecurityGroup: nsg.id'.,,,,ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,4.0,"The 'corpNetIps' variable on line 4 includes the IP range '*******/8', which is an extremely broad public IP range. Allowing such a wide range in network rules can enable initial access for attackers from any address within this /8, significantly increasing the attack surface and blast radius. This configuration violates network segmentation and isolation principles, making lateral movement and data exfiltration much easier for adversaries.","Restrict allowed IP ranges to only trusted, specific corporate or service IPs. Remove or replace '*******/8' with a minimal set of required addresses. Implement deny-by-default network security group (NSG) rules and only allow access from explicitly trusted sources as per your enterprise segmentation strategy. Reference ASB control NS-1.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,5.0,"The 'corpNetIps' variable on line 5 includes the IP range '********/8', which is a very broad public IP range. Allowing such a large range increases the risk of unauthorized access, as any host within this /8 can reach protected resources, enabling initial access and lateral movement for attackers.","Limit allowed IP ranges to only those necessary for business operations. Replace '********/8' with specific, trusted subnets or addresses. Enforce a deny-by-default policy and review all allowed IPs for necessity and risk. Reference ASB control NS-1.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,6.0,"The 'corpNetIps' variable on line 6 includes the IP range '20.0.0.0/8', which is a large public IP block. Allowing this range exposes resources to a vast number of potential sources, increasing the risk of compromise and expanding the blast radius for any attack.","Remove or replace '20.0.0.0/8' with only the minimal, trusted IP addresses required. Use network segmentation and NSGs to enforce least privilege network access. Reference ASB control NS-1.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,7.0,"The 'corpNetIps' variable on line 7 includes the IP range '40.0.0.0/8', which is an excessively broad public IP range. This configuration allows access from millions of potential sources, enabling attackers to bypass network segmentation and increasing the risk of lateral movement and data exfiltration.","Restrict access to only necessary, trusted IP addresses. Remove '40.0.0.0/8' and replace with specific, validated corporate IPs. Apply deny-by-default NSG rules. Reference ASB control NS-1.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,8.0,"The 'corpNetIps' variable on line 8 includes the IP range '********/8', which is a large public IP block. Allowing this range increases the attack surface and enables initial access from a wide range of sources, violating network segmentation best practices.","Remove '********/8' and only allow access from specific, trusted IP addresses. Enforce deny-by-default and review all allowed IPs for necessity. Reference ASB control NS-1.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,9.0,"The 'corpNetIps' variable on line 9 includes the IP range '********/8', which is a very broad public IP range. This configuration enables a large attack surface, allowing initial access and lateral movement from any address within this /8.","Replace '********/8' with only the required, trusted IP addresses. Use NSGs to enforce least privilege and deny-by-default. Reference ASB control NS-1.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,10.0,"The 'corpNetIps' variable on line 10 includes the IP range '********/8', which is an extremely broad public IP range. Allowing this range exposes resources to a massive number of potential attackers, increasing the risk of compromise and blast radius.","Remove '********/8' and restrict access to only necessary, trusted IP addresses. Apply deny-by-default NSG rules and review all allowed IPs. Reference ASB control NS-1.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,11.0,"The 'corpNetIps' variable on line 11 includes the IP range '70.0.0.0/8', which is a large public IP block. This configuration allows access from a vast number of sources, enabling initial access and lateral movement for attackers.","Replace '70.0.0.0/8' with only the minimal, trusted IP addresses required for business operations. Enforce deny-by-default and review all allowed IPs. Reference ASB control NS-1.",,,,ai_analysis,,Validated
keyvault.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,28.0,"The 'networkAcls.defaultAction' property is set to 'Allow' for the Key Vault resource. This configuration allows public network access to the Key Vault, enabling attackers to attempt initial access from any IP address. This significantly increases the attack surface and blast radius, as unauthorized users could attempt brute force, enumeration, or exploit vulnerabilities to access sensitive secrets and keys stored in the vault.","Set 'networkAcls.defaultAction' to 'Deny' to restrict public network access. Only allow access from explicitly defined IP addresses or virtual networks using 'ipRules' and 'virtualNetworkRules'. Additionally, consider enabling Private Endpoint for the Key Vault to ensure access is only possible from trusted networks.",,,,ai_analysis,,Validated
keyvault.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,29.0,"The 'networkAcls.bypass' property is set to 'AzureServices', which allows all Azure services to bypass network restrictions and access the Key Vault. This configuration enables lateral movement from any Azure service, increasing the risk of unauthorized access if any Azure service in the tenant is compromised. The blast radius includes all secrets and keys in the Key Vault, as attackers could leverage a compromised Azure service to access sensitive data.",Set 'networkAcls.bypass' to 'None' to prevent Azure services from bypassing network restrictions. Only allow access from explicitly trusted services or networks. Review and restrict the list of allowed services to the minimum necessary for business operations.,,,,ai_analysis,,Validated
keyvault.bicep,DP-8,Data Protection,Ensure security of key and certificate repository,HIGH,22.0,"The 'accessPolicies' property is set to an empty array while 'enableRbacAuthorization' is set to true. If RBAC roles are not explicitly assigned, this can result in either overly permissive access (if default roles are assigned) or unintentional denial of access. Attackers who gain privileged Azure RBAC roles (such as Owner or Contributor) at the subscription or resource group level could escalate privileges and access all secrets and keys in the Key Vault, increasing the blast radius to all stored sensitive data.",Explicitly assign least-privilege RBAC roles to trusted identities at the Key Vault scope. Regularly audit RBAC assignments to ensure only authorized users and services have access. Avoid relying on default or inherited permissions at higher scopes.,,,,ai_analysis,,Validated
operational-insights.bicep,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,173.0,"The output 'appInsightsInstrumentationKey' on line 173 exposes the Application Insights Instrumentation Key, which is a sensitive secret. If this key is disclosed, an attacker can send arbitrary telemetry to the Application Insights resource, potentially polluting logs, exfiltrating data, or using the key for reconnaissance. This increases the blast radius by enabling unauthorized data injection and possible data exfiltration from monitoring systems.","Do not output sensitive secrets such as Instrumentation Keys. Instead, store secrets in Azure Key Vault and reference them securely at runtime. Remove the output of 'appInsightsInstrumentationKey' from the template and ensure all sensitive values are handled via secure secret management solutions. Review all outputs for potential information disclosure and apply data classification and labeling as per DP-1.",,,,ai_analysis,,Validated
scaleset-networks.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,66.0,"The subnet configuration on line 66 sets 'defaultOutboundAccess: true' for the 'scaleset' subnet. This enables default outbound internet access for all resources in the subnet, creating a direct attack vector for initial access, lateral movement, and data exfiltration. Without a Network Security Group (NSG) associated to the subnet, there is no deny-by-default boundary, significantly increasing the blast radius if any resource is compromised.",Set 'defaultOutboundAccess' to false on line 66 and associate a Network Security Group (NSG) with the subnet to enforce deny-by-default rules. Explicitly define only required outbound rules and restrict all unnecessary traffic. Example: Add an NSG resource and reference it in the subnet's 'networkSecurityGroup' property.,,,,ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,29.0,"The 'defaultAction' property in 'networkAcls' for storage accounts (Line 031) is set to 'Allow', which permits public network access to the storage account. This enables initial access attack vectors, allowing attackers to reach the storage account from any network not explicitly denied, increasing the blast radius for data exfiltration and lateral movement.",Set 'networkAcls.defaultAction' to 'Deny' to restrict public network access. Only allow access from explicitly defined IP rules and virtual network rules. Example: change 'defaultAction' to 'Deny' on Line 031.,,,,ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,29.0,"The 'defaultAction' property in 'networkAcls' for storage accounts (Line 065) is set to 'Allow', which permits public network access to the storage account. This exposes the storage account to the public internet, enabling attack vectors for unauthorized access, data exfiltration, and lateral movement.",Set 'networkAcls.defaultAction' to 'Deny' to restrict public network access. Only allow access from explicitly defined IP rules and virtual network rules. Example: change 'defaultAction' to 'Deny' on Line 065.,,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 26,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 7,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T16:00:21.389237,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
