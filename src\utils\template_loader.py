"""
Enhanced Template Loader Utility for IaC Guardian

This module provides comprehensive functionality to load HTML templates, CSS styles,
JavaScript files, and prompts with validation, hot reloading, versioning, and
performance monitoring capabilities.
"""

import os
import json
import time
from pathlib import Path
from typing import Dict, Any, Optional
import logging

# Import enhanced template system components
try:
    from .template_validator import TemplateValida<PERSON>, ValidationSeverity
    from .template_metrics import TemplateMetricsCollector, PerformanceTimer, TemplateVersionManager
    from .template_hot_reload import create_hot_reloader
    ENHANCED_FEATURES_AVAILABLE = True
except ImportError:
    ENHANCED_FEATURES_AVAILABLE = False

logger = logging.getLogger(__name__)

class EnhancedTemplateLoader:
    """Enhanced template loader with validation, hot reloading, versioning, and performance monitoring"""

    def __init__(self, templates_dir: Optional[Path] = None, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the enhanced template loader

        Args:
            templates_dir: Path to templates directory. If None, uses default location.
            config: Configuration dictionary for enhanced features
        """
        if templates_dir is None:
            # Default to templates directory in project root
            project_root = Path(__file__).resolve().parent.parent.parent
            templates_dir = project_root / "templates"

        self.templates_dir = Path(templates_dir)
        self._template_cache = {}

        # Load configuration
        self.config = config or self._load_default_config()

        # Initialize enhanced features if available
        if ENHANCED_FEATURES_AVAILABLE:
            self._init_enhanced_features()
        else:
            logger.warning("Enhanced template features not available - using basic functionality")
            self.validator = None
            self.metrics_collector = None
            self.hot_reloader = None
            self.version_manager = None

        # Verify templates directory exists
        if not self.templates_dir.exists():
            logger.warning(f"Templates directory not found: {self.templates_dir}")

    def _load_default_config(self) -> Dict[str, Any]:
        """Load default configuration"""
        config_file = self.templates_dir / "config" / "template_config.json"

        if config_file.exists():
            try:
                with open(config_file, 'r') as f:
                    config = json.load(f)
                logger.info(f"Loaded template configuration from {config_file}")
                return config.get('template_system', {})
            except Exception as e:
                logger.warning(f"Failed to load config file: {e}")

        # Return default configuration
        return {
            'validation': {'enabled': True, 'strict_mode': False},
            'hot_reloading': {'enabled': False},
            'versioning': {'enabled': True},
            'performance': {'monitoring_enabled': True}
        }

    def _init_enhanced_features(self):
        """Initialize enhanced template features"""
        try:
            # Initialize validator
            if self.config.get('validation', {}).get('enabled', True):
                self.validator = TemplateValidator(self.config)
                logger.info("Template validator initialized")
            else:
                self.validator = None

            # Initialize metrics collector
            if self.config.get('performance', {}).get('monitoring_enabled', True):
                self.metrics_collector = TemplateMetricsCollector(self.config.get('performance', {}))
                logger.info("Template metrics collector initialized")
            else:
                self.metrics_collector = None

            # Initialize version manager
            if self.config.get('versioning', {}).get('enabled', True):
                self.version_manager = TemplateVersionManager(self.config.get('versioning', {}))
                logger.info("Template version manager initialized")
            else:
                self.version_manager = None

            # Initialize hot reloader
            if self.config.get('hot_reloading', {}).get('enabled', False):
                self.hot_reloader = create_hot_reloader(self.config.get('hot_reloading', {}), self)
                logger.info("Template hot reloader initialized")
            else:
                self.hot_reloader = None

        except Exception as e:
            logger.error(f"Failed to initialize enhanced features: {e}")
            self.validator = None
            self.metrics_collector = None
            self.hot_reloader = None
            self.version_manager = None
            
    def load_html_template(self, template_name: str, validate: bool = True) -> str:
        """
        Load an HTML template file with enhanced features

        Args:
            template_name: Name of the template file (e.g., 'base.html')
            validate: Whether to validate the template

        Returns:
            Template content as string
        """
        template_path = self.templates_dir / "html" / template_name
        return self._load_template_file_enhanced(template_path, 'html', validate)
    
    def load_css_template(self, css_name: str) -> str:
        """
        Load a CSS template file
        
        Args:
            css_name: Name of the CSS file (e.g., 'glass-ui.css')
            
        Returns:
            CSS content as string
        """
        css_path = self.templates_dir / "css" / css_name
        return self._load_template_file(css_path)
    
    def load_js_template(self, js_name: str) -> str:
        """
        Load a JavaScript template file
        
        Args:
            js_name: Name of the JS file (e.g., 'report.js')
            
        Returns:
            JavaScript content as string
        """
        js_path = self.templates_dir / "js" / js_name
        return self._load_template_file(js_path)
    
    def load_prompt_template(self, category: str, prompt_name: str) -> str:
        """
        Load a prompt template file
        
        Args:
            category: Prompt category (e.g., 'security', 'system', 'context')
            prompt_name: Name of the prompt file (e.g., 'main_analysis_prompt.txt')
            
        Returns:
            Prompt content as string
        """
        prompt_path = self.templates_dir / "prompts" / category / prompt_name
        return self._load_template_file(prompt_path)
    
    def load_complete_html_report(self, template_vars: Dict[str, Any]) -> str:
        """
        Load and render the complete HTML report template
        
        Args:
            template_vars: Dictionary of variables to substitute in template
            
        Returns:
            Rendered HTML report as string
        """
        try:
            # Load base HTML template
            base_html = self.load_html_template("base.html")
            
            # Load CSS files
            glass_ui_css = self.load_css_template("glass-ui.css")
            responsive_css = self.load_css_template("responsive.css")
            
            # Load JavaScript files
            report_js = self.load_js_template("report.js")
            dialogs_js = self.load_js_template("dialogs.js")
            utils_js = self.load_js_template("utils.js")
            
            # Embed CSS and JS directly in HTML for self-contained report
            embedded_styles = f"""
    <style>
/* Glass UI Framework Styles */
{glass_ui_css}

/* Responsive Design Styles */
{responsive_css}
    </style>"""
            
            embedded_scripts = f"""
    <script>
// Report functionality
{report_js}

// Utility functions (must come before dialogs.js since dialogs.js depends on utils.js functions)
{utils_js}

// Dialog functionality
{dialogs_js}
    </script>"""
            
            # Apply template variables to base HTML BEFORE embedding CSS/JS
            rendered_html = self._apply_template_variables(base_html, template_vars)

            # Replace external CSS/JS references with embedded content AFTER template variable substitution
            rendered_html = rendered_html.replace('<link rel="stylesheet" href="../css/glass-ui.css">', embedded_styles)
            rendered_html = rendered_html.replace('<link rel="stylesheet" href="../css/responsive.css">', '')
            rendered_html = rendered_html.replace('<script src="../js/report.js"></script>', embedded_scripts)
            rendered_html = rendered_html.replace('<script src="../js/dialogs.js"></script>', '')
            rendered_html = rendered_html.replace('<script src="../js/utils.js"></script>', '')
            
            return rendered_html
            
        except Exception as e:
            logger.error(f"Error loading complete HTML report: {e}")
            return self._get_fallback_html_template(template_vars)
    
    def load_security_analysis_prompt(self, template_vars: Dict[str, Any]) -> str:
        """
        Load and render the security analysis prompt
        
        Args:
            template_vars: Dictionary of variables to substitute in prompt
            
        Returns:
            Rendered prompt as string
        """
        try:
            # Load system role
            system_role = self.load_prompt_template("system", "security_analyst_role.txt")
            
            # Load context builder
            context_template = self.load_prompt_template("context", "template_context_builder.txt")
            
            # Load main analysis instructions
            analysis_instructions = self.load_prompt_template("security", "main_analysis_prompt.txt")
            
            # Apply variables to context template
            context_content = self._apply_template_variables(context_template, template_vars)
            
            # Apply analysis instructions to context
            final_vars = {**template_vars, "analysis_instructions": analysis_instructions}
            final_prompt = self._apply_template_variables(context_content, final_vars)
            
            return final_prompt
            
        except Exception as e:
            logger.error(f"Error loading security analysis prompt: {e}")
            return self._get_fallback_prompt_template(template_vars)
    
    def _load_template_file(self, file_path: Path) -> str:
        """
        Load a template file with caching (legacy method)

        Args:
            file_path: Path to the template file

        Returns:
            File content as string
        """
        return self._load_template_file_enhanced(file_path, 'unknown', validate=False)

    def _load_template_file_enhanced(self, file_path: Path, template_type: str, validate: bool = True) -> str:
        """
        Load a template file with enhanced features

        Args:
            file_path: Path to the template file
            template_type: Type of template (html, css, js, prompts, etc.)
            validate: Whether to validate the template

        Returns:
            File content as string
        """
        cache_key = str(file_path)

        # Check cache first
        if cache_key in self._template_cache:
            if self.metrics_collector:
                self.metrics_collector.record_metric(
                    template_path=str(file_path),
                    operation='load',
                    duration_ms=0.1,  # Cache hit is very fast
                    cache_hit=True,
                    template_size_bytes=len(self._template_cache[cache_key])
                )
            return self._template_cache[cache_key]

        # Use performance timer if available
        if self.metrics_collector and ENHANCED_FEATURES_AVAILABLE:
            with PerformanceTimer(self.metrics_collector, str(file_path), 'load') as timer:
                content = self._load_file_content(file_path, template_type, validate, timer)
        else:
            content = self._load_file_content(file_path, template_type, validate)

        return content

    def _load_file_content(self, file_path: Path, template_type: str, validate: bool, timer=None) -> str:
        """Load file content with validation and caching"""
        try:
            if not file_path.exists():
                logger.warning(f"Template file not found: {file_path}")
                return ""

            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Add performance metrics
            if timer:
                timer.template_size_bytes = len(content.encode('utf-8'))

            # Validate template if requested and validator available
            if validate and self.validator:
                validation_result = self.validator.validate_template(file_path, content, template_type)

                if not validation_result.is_valid:
                    error_count = validation_result.error_count
                    logger.warning(f"Template validation failed for {file_path}: {error_count} errors")

                    # Log validation issues
                    for issue in validation_result.issues:
                        if issue.severity.value == 'error':
                            logger.error(f"Validation error in {file_path}: {issue.message}")
                        elif issue.severity.value == 'warning':
                            logger.warning(f"Validation warning in {file_path}: {issue.message}")

            # Check version compatibility if version manager available
            if self.version_manager:
                template_version = self.version_manager.extract_template_version(content)
                if template_version:
                    is_compatible, messages = self.version_manager.check_compatibility(str(template_version))
                    if not is_compatible:
                        logger.warning(f"Version compatibility issue in {file_path}: {'; '.join(messages)}")

            # Cache the content
            self._template_cache[str(file_path)] = content

            return content

        except Exception as e:
            logger.error(f"Error loading template file {file_path}: {e}")
            if timer:
                timer.success = False
                timer.error_message = str(e)
            return ""
    
    def _apply_template_variables(self, template: str, variables: Dict[str, Any]) -> str:
        """
        Apply template variables using Python string formatting with safe brace handling

        Args:
            template: Template string with {variable} placeholders
            variables: Dictionary of variables to substitute

        Returns:
            Rendered template string
        """
        try:
            # Handle missing variables gracefully
            safe_variables = {}
            for key, value in variables.items():
                if value is None:
                    safe_variables[key] = ""
                elif isinstance(value, (list, dict)):
                    safe_variables[key] = str(value)
                else:
                    safe_variables[key] = value

            # Check if template contains any valid placeholders before attempting formatting
            import re
            placeholder_pattern = r'\{([^{}]+)\}'
            placeholders = re.findall(placeholder_pattern, template)

            # Only apply formatting if we have valid placeholders that match our variables
            valid_placeholders = [p for p in placeholders if p in safe_variables]

            if valid_placeholders:
                # Use safe formatting that handles unmatched braces
                return self._safe_format(template, safe_variables)
            else:
                # No valid placeholders found, return template as-is
                return template

        except KeyError as e:
            logger.warning(f"Missing template variable: {e}")
            return template
        except Exception as e:
            logger.error(f"Error applying template variables: {e}")
            return template

    def _safe_format(self, template: str, variables: Dict[str, Any]) -> str:
        """
        Safely format template string, handling unmatched braces

        Args:
            template: Template string
            variables: Variables to substitute

        Returns:
            Formatted string with unmatched braces preserved
        """
        try:
            # First, escape any literal braces that aren't placeholders
            # This is a more robust approach than the standard format()
            import re

            # Find all placeholder patterns {variable_name}
            placeholder_pattern = r'\{([a-zA-Z_][a-zA-Z0-9_]*)\}'

            def replace_placeholder(match):
                var_name = match.group(1)
                if var_name in variables:
                    return str(variables[var_name])
                else:
                    # Keep the placeholder if variable not found
                    return match.group(0)

            # Replace only valid placeholders, leave other braces alone
            result = re.sub(placeholder_pattern, replace_placeholder, template)
            return result

        except Exception as e:
            logger.warning(f"Safe format failed: {e}, returning original template")
            return template
    
    def _get_fallback_html_template(self, template_vars: Dict[str, Any]) -> str:
        """
        Get a basic fallback HTML template if loading fails
        
        Args:
            template_vars: Template variables
            
        Returns:
            Basic HTML template
        """
        return f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Assessment Report - IaC Guardian</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 2rem; }}
        .header {{ text-align: center; margin-bottom: 2rem; }}
        .finding {{ margin: 1rem 0; padding: 1rem; border: 1px solid #ccc; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Security Assessment Report</h1>
        <p>Generated: {template_vars.get('timestamp', 'Unknown')}</p>
    </div>
    <div class="content">
        {template_vars.get('findings', 'No findings available')}
    </div>
</body>
</html>"""
    
    def _get_fallback_prompt_template(self, template_vars: Dict[str, Any]) -> str:
        """
        Get a basic fallback prompt template if loading fails
        
        Args:
            template_vars: Template variables
            
        Returns:
            Basic prompt template
        """
        return f"""You are a security analyst. Analyze the following template for security issues:

{template_vars.get('numbered_content', 'No content available')}

Return findings in JSON format with control_id, severity, line, description, and remediation fields."""

    def clear_cache(self):
        """Clear the template cache"""
        self._template_cache.clear()
        logger.info("Template cache cleared")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """
        Get information about the template cache

        Returns:
            Dictionary with cache statistics
        """
        cache_info = {
            "cached_templates": len(self._template_cache),
            "cache_keys": list(self._template_cache.keys()),
            "templates_dir": str(self.templates_dir)
        }

        # Add enhanced cache info if metrics collector available
        if self.metrics_collector:
            cache_stats = self.metrics_collector.get_cache_stats()
            cache_info.update(cache_stats)

        return cache_info

    def validate_all_templates(self) -> Dict[str, Any]:
        """
        Validate all templates in the templates directory

        Returns:
            Dictionary with validation results
        """
        if not self.validator:
            return {"error": "Template validator not available"}

        results = {
            "total_templates": 0,
            "valid_templates": 0,
            "invalid_templates": 0,
            "validation_results": []
        }

        # Find all template files
        template_extensions = ['.html', '.css', '.js', '.txt', '.md']
        template_files = []

        for ext in template_extensions:
            template_files.extend(self.templates_dir.rglob(f'*{ext}'))

        for template_file in template_files:
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Determine template type from path
                if 'html' in str(template_file):
                    template_type = 'html'
                elif 'css' in str(template_file):
                    template_type = 'css'
                elif 'js' in str(template_file):
                    template_type = 'js'
                elif 'prompts' in str(template_file):
                    template_type = 'prompts'
                else:
                    template_type = 'unknown'

                validation_result = self.validator.validate_template(template_file, content, template_type)

                results["total_templates"] += 1
                if validation_result.is_valid:
                    results["valid_templates"] += 1
                else:
                    results["invalid_templates"] += 1

                results["validation_results"].append({
                    "file": str(template_file),
                    "type": template_type,
                    "valid": validation_result.is_valid,
                    "errors": validation_result.error_count,
                    "warnings": validation_result.warning_count,
                    "validation_time_ms": validation_result.validation_time_ms
                })

            except Exception as e:
                logger.error(f"Failed to validate {template_file}: {e}")
                results["validation_results"].append({
                    "file": str(template_file),
                    "type": "unknown",
                    "valid": False,
                    "error": str(e)
                })

        return results

    def get_performance_report(self) -> Dict[str, Any]:
        """
        Get comprehensive performance report

        Returns:
            Dictionary with performance metrics and analysis
        """
        if not self.metrics_collector:
            return {"error": "Performance monitoring not available"}

        # Get overall statistics
        overall_stats = self.metrics_collector.get_stats()

        # Get slow operations
        slow_operations = self.metrics_collector.get_slow_operations(limit=5)

        # Get error summary
        error_summary = self.metrics_collector.get_error_summary()

        return {
            "overall_performance": {
                "total_operations": overall_stats.total_operations,
                "average_duration_ms": round(overall_stats.avg_duration_ms, 2),
                "success_rate_percent": round(overall_stats.success_rate, 2),
                "cache_hit_rate_percent": round(overall_stats.cache_hit_rate, 2)
            },
            "slow_operations": [
                {
                    "template": op.template_path,
                    "operation": op.operation,
                    "duration_ms": round(op.duration_ms, 2),
                    "timestamp": op.timestamp.isoformat()
                }
                for op in slow_operations
            ],
            "error_summary": error_summary,
            "cache_performance": self.metrics_collector.get_cache_stats()
        }

    def export_performance_dashboard(self, output_path: Path) -> bool:
        """
        Export performance dashboard as HTML file

        Args:
            output_path: Path to save the dashboard

        Returns:
            True if successful, False otherwise
        """
        try:
            from .template_dashboard import TemplateDashboard

            dashboard = TemplateDashboard(self.metrics_collector, {"enabled": True})
            return dashboard.export_dashboard(output_path)

        except ImportError:
            logger.error("Template dashboard not available")
            return False
        except Exception as e:
            logger.error(f"Failed to export dashboard: {e}")
            return False

    def get_system_status(self) -> Dict[str, Any]:
        """
        Get comprehensive system status

        Returns:
            Dictionary with system status information
        """
        status = {
            "template_loader": "active",
            "templates_directory": str(self.templates_dir),
            "templates_directory_exists": self.templates_dir.exists(),
            "cached_templates": len(self._template_cache),
            "enhanced_features": ENHANCED_FEATURES_AVAILABLE
        }

        if ENHANCED_FEATURES_AVAILABLE:
            status.update({
                "validator_enabled": self.validator is not None,
                "metrics_collector_enabled": self.metrics_collector is not None,
                "hot_reloader_enabled": self.hot_reloader is not None,
                "version_manager_enabled": self.version_manager is not None
            })

            if self.version_manager:
                status["version_info"] = self.version_manager.get_version_info()

            if self.hot_reloader:
                status["hot_reload_status"] = self.hot_reloader.get_status()

        return status

# Maintain backward compatibility
class TemplateLoader(EnhancedTemplateLoader):
    """Backward compatible template loader"""

    def __init__(self, templates_dir: Optional[Path] = None):
        """Initialize with basic configuration for backward compatibility"""
        super().__init__(templates_dir, config=None)
