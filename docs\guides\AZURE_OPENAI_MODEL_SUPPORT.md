# Azure OpenAI Model Support Guide

This guide explains how IaC Guardian supports both GPT-4o and O1 models (O1-preview and O1-mini) from Azure OpenAI.

## Supported Models

### GPT-4o Models
- **gpt-4o**: Latest GPT-4 Omni model with multimodal capabilities
- **gpt-4**: Standard GPT-4 model
- **gpt-35-turbo**: GPT-3.5 Turbo model

### O1 Models
- **o1-preview**: Latest O1 preview model with advanced reasoning
- **o1-mini**: Smaller, faster O1 model optimized for coding tasks

## Model Detection and Parameter Handling

IaC Guardian automatically detects the model type based on the deployment name and applies the appropriate API parameters:

### Automatic Detection
The system recognizes O1 models by checking for these patterns in the deployment name:
- `o1-preview`
- `o1-mini`
- `o1_preview`
- `o1_mini`
- `o-1-preview`
- `o-1-mini`

### Parameter Differences

#### GPT-4o and Standard Models
```python
{
    "model": "gpt-4o",
    "messages": [...],
    "max_completion_tokens": 3000,
    "temperature": 0.0,
    "seed": 42,
    "response_format": {"type": "json_object"}
}
```

#### O1 Models
```python
{
    "model": "o1-mini",
    "messages": [...],
    "max_completion_tokens": 3000
    # Note: O1 models don't support temperature, seed, or response_format
}
```

## Configuration

### Environment Variables

Set your Azure OpenAI configuration in your `.env` file:

```bash
# Azure OpenAI Configuration
AZURE_OPENAI_ENDPOINT=https://your-openai-resource.openai.azure.com/
AZURE_OPENAI_API_KEY=your_api_key_here
AZURE_OPENAI_API_VERSION=2024-02-01

# For GPT-4o
AZURE_OPENAI_DEPLOYMENT=gpt-4o

# For O1-mini
# AZURE_OPENAI_DEPLOYMENT=o1-mini

# For O1-preview
# AZURE_OPENAI_DEPLOYMENT=o1-preview
```

### Model-Specific Recommendations

#### GPT-4o
- **Best for**: General security analysis with balanced speed and accuracy
- **Token limit**: Up to 128K context, 4K output
- **Features**: Supports all API parameters including temperature control and JSON formatting

#### O1-preview
- **Best for**: Complex security analysis requiring deep reasoning
- **Token limit**: Up to 128K context, 32K output
- **Features**: Advanced reasoning capabilities, slower but more thorough analysis

#### O1-mini
- **Best for**: Fast security analysis and code review
- **Token limit**: Up to 128K context, 65K output
- **Features**: Optimized for coding tasks, faster than O1-preview

## Usage Examples

### Using GPT-4o
```bash
# Set environment variables
export AZURE_OPENAI_DEPLOYMENT=gpt-4o
export AZURE_OPENAI_API_VERSION=2024-02-01

# Run analysis
python security_opt.py --local-folder ./templates
```

### Using O1-mini
```bash
# Set environment variables
export AZURE_OPENAI_DEPLOYMENT=o1-mini
export AZURE_OPENAI_API_VERSION=2024-02-01

# Run analysis
python security_opt.py --local-folder ./templates
```

### Using O1-preview
```bash
# Set environment variables
export AZURE_OPENAI_DEPLOYMENT=o1-preview
export AZURE_OPENAI_API_VERSION=2024-02-01

# Run analysis
python security_opt.py --local-folder ./templates
```

## Performance Considerations

### Speed Comparison
1. **O1-mini**: Fastest for code analysis
2. **GPT-4o**: Balanced speed and quality
3. **O1-preview**: Slowest but most thorough

### Cost Considerations
- **O1-mini**: Most cost-effective for simple analysis
- **GPT-4o**: Good balance of cost and performance
- **O1-preview**: Most expensive but highest quality

### Use Case Recommendations

#### Development/Testing
- Use **O1-mini** for quick feedback during development
- Use **GPT-4o** for regular CI/CD pipeline checks

#### Production Security Reviews
- Use **O1-preview** for critical infrastructure analysis
- Use **GPT-4o** for standard production deployments

#### Continuous Monitoring
- Use **GPT-4o** for automated daily scans
- Use **O1-mini** for frequent lightweight checks

## Troubleshooting

### Common Issues

#### "Unsupported parameter" Error
If you see an error about `max_tokens` not being supported:
1. Ensure you're using the latest version of IaC Guardian
2. Check that your deployment name correctly identifies the model type
3. Verify your API version is compatible (use `2024-02-01` or later)

#### Model Detection Issues
If the wrong parameters are being used:
1. Check your deployment name matches the expected patterns
2. Ensure the deployment name is correctly set in environment variables
3. Check the logs for model detection messages

### Debugging
Enable debug logging to see which parameters are being used:
```bash
export LOG_LEVEL=DEBUG
python security_opt.py --local-folder ./templates
```

Look for log messages like:
- "Using O1 model parameters for o1-mini"
- "Using standard model parameters for gpt-4o"

## Migration Guide

### From GPT-4 to GPT-4o
1. Update `AZURE_OPENAI_DEPLOYMENT=gpt-4o`
2. No code changes required - parameters are automatically adjusted

### From GPT-4o to O1-mini
1. Update `AZURE_OPENAI_DEPLOYMENT=o1-mini`
2. No code changes required - O1 restrictions are automatically applied
3. Expect slightly different analysis results due to model differences

### From O1-mini to O1-preview
1. Update `AZURE_OPENAI_DEPLOYMENT=o1-preview`
2. No code changes required
3. Expect slower but more detailed analysis

## Best Practices

1. **Start with O1-mini** for development and testing
2. **Use GPT-4o** for production CI/CD pipelines
3. **Reserve O1-preview** for critical security reviews
4. **Monitor costs** when using O1 models extensively
5. **Test thoroughly** when switching between model types
6. **Keep API versions updated** for best compatibility

## Support

For issues related to model support:
1. Check the troubleshooting section above
2. Verify your Azure OpenAI deployment configuration
3. Ensure you have access to the specific model type
4. Review the logs for detailed error messages
