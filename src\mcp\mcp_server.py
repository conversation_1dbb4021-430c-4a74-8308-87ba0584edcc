#!/usr/bin/env python3
"""
MCP Server for IaC Guardian GPT Security Analysis
Provides security analysis tools for VS Code Copilot integration.
"""

import asyncio
import json
import logging
import os
import tempfile
from pathlib import Path
from typing import Any, Dict, List, Optional, Sequence

try:
    import mcp.server.stdio
    import mcp.types as types
    from mcp.server import NotificationOptions, Server
    from mcp.server.models import InitializationOptions
except ImportError as e:
    print(f"❌ MCP library not found. Please install: pip install mcp")
    print(f"Error: {e}")
    exit(1)

# Import our security analysis logic
from security_opt import SecurityPRReviewer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("iac-guardian-mcp")

# Initialize the MCP server
server = Server("iac-guardian")

# Global security reviewer instance
security_reviewer: Optional[SecurityPRReviewer] = None

@server.list_tools()
async def handle_list_tools() -> List[types.Tool]:
    """List available security analysis tools."""
    return [
        types.Tool(
            name="analyze_iac_file",
            description="Analyze a single Infrastructure-as-Code file for security issues",
            inputSchema={
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "Path to the IaC file to analyze"
                    },
                    "file_content": {
                        "type": "string",
                        "description": "Content of the IaC file (optional, will read from file_path if not provided)"
                    },
                    "format": {
                        "type": "string",
                        "enum": ["json", "markdown", "summary"],
                        "description": "Output format for the analysis results",
                        "default": "markdown"
                    }
                },
                "required": ["file_path"]
            }
        ),
        types.Tool(
            name="analyze_iac_folder",
            description="Analyze all IaC files in a folder for security issues",
            inputSchema={
                "type": "object",
                "properties": {
                    "folder_path": {
                        "type": "string",
                        "description": "Path to the folder containing IaC files"
                    },
                    "format": {
                        "type": "string",
                        "enum": ["json", "markdown", "summary", "html"],
                        "description": "Output format for the analysis results",
                        "default": "markdown"
                    },
                    "export_report": {
                        "type": "boolean",
                        "description": "Whether to export a detailed HTML/CSV report",
                        "default": false
                    }
                },
                "required": ["folder_path"]
            }
        ),
        types.Tool(
            name="get_security_controls",
            description="Get available Azure Security Benchmark controls for a resource type",
            inputSchema={
                "type": "object",
                "properties": {
                    "resource_type": {
                        "type": "string",
                        "description": "Azure resource type (e.g., 'Storage', 'Network', 'Compute')"
                    },
                    "domain": {
                        "type": "string",
                        "enum": ["Identity Management", "Network Security", "Data Protection", "Access Management", "Logging and Monitoring"],
                        "description": "Security domain to filter controls (optional)"
                    }
                },
                "required": ["resource_type"]
            }
        ),
        types.Tool(
            name="validate_security_config",
            description="Validate a specific security configuration against Azure Security Benchmark",
            inputSchema={
                "type": "object",
                "properties": {
                    "resource_config": {
                        "type": "string",
                        "description": "JSON configuration of the Azure resource to validate"
                    },
                    "resource_type": {
                        "type": "string",
                        "description": "Type of Azure resource (e.g., 'Microsoft.Storage/storageAccounts')"
                    },
                    "control_ids": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Specific control IDs to check (optional, checks all relevant if not provided)"
                    }
                },
                "required": ["resource_config", "resource_type"]
            }
        )
    ]

@server.call_tool()
async def handle_call_tool(
    name: str, arguments: Dict[str, Any]
) -> List[types.TextContent]:
    """Handle tool calls from VS Code Copilot."""
    global security_reviewer
    
    try:
        # Initialize security reviewer if not already done
        if security_reviewer is None:
            security_reviewer = SecurityPRReviewer(local_folder=".")
            logger.info("Initialized SecurityPRReviewer")
        
        if name == "analyze_iac_file":
            return await analyze_iac_file(arguments)
        elif name == "analyze_iac_folder":
            return await analyze_iac_folder(arguments)
        elif name == "get_security_controls":
            return await get_security_controls(arguments)
        elif name == "validate_security_config":
            return await validate_security_config(arguments)
        else:
            raise ValueError(f"Unknown tool: {name}")
            
    except Exception as e:
        logger.error(f"Error handling tool call {name}: {str(e)}")
        return [types.TextContent(
            type="text",
            text=f"Error: {str(e)}"
        )]

async def analyze_iac_file(arguments: Dict[str, Any]) -> List[types.TextContent]:
    """Analyze a single IaC file for security issues."""
    file_path = arguments["file_path"]
    file_content = arguments.get("file_content")
    format_type = arguments.get("format", "markdown")
    
    # Validate file exists
    if not file_content and not Path(file_path).exists():
        return [types.TextContent(
            type="text",
            text=f"Error: File not found: {file_path}"
        )]
    
    try:
        # Create temporary directory for analysis
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_file_path = Path(temp_dir) / Path(file_path).name
            
            # Write content to temporary file
            if file_content:
                temp_file_path.write_text(file_content)
            else:
                temp_file_path.write_text(Path(file_path).read_text())
            
            # Analyze the file
            files_to_analyze = security_reviewer.analyze_folder(temp_dir)
            findings = security_reviewer.analyze_files(files_to_analyze)
            
            # Format results
            if format_type == "json":
                result = json.dumps(findings, indent=2)
            elif format_type == "summary":
                result = format_summary(findings, file_path)
            else:  # markdown
                result = format_markdown_report(findings, file_path)
            
            return [types.TextContent(type="text", text=result)]
            
    except Exception as e:
        logger.error(f"Error analyzing file {file_path}: {str(e)}")
        return [types.TextContent(
            type="text",
            text=f"Error analyzing file: {str(e)}"
        )]

async def analyze_iac_folder(arguments: Dict[str, Any]) -> List[types.TextContent]:
    """Analyze all IaC files in a folder."""
    folder_path = arguments["folder_path"]
    format_type = arguments.get("format", "markdown")
    export_report = arguments.get("export_report", False)
    
    if not Path(folder_path).exists():
        return [types.TextContent(
            type="text",
            text=f"Error: Folder not found: {folder_path}"
        )]
    
    try:
        # Update security reviewer to use the specified folder
        security_reviewer.local_folder = folder_path
        
        # Analyze the folder
        files_to_analyze = security_reviewer.analyze_folder(folder_path)
        findings = security_reviewer.analyze_files(files_to_analyze)
        
        # Export detailed report if requested
        if export_report and findings:
            security_reviewer.export_findings(findings, "both", "security_findings")
        
        # Format results
        if format_type == "json":
            result = json.dumps(findings, indent=2)
        elif format_type == "summary":
            result = format_summary(findings, folder_path)
        elif format_type == "html":
            # Export HTML and return path
            if findings:
                security_reviewer.export_findings(findings, "html", "security_findings")
                result = f"HTML report exported to security_findings/ directory\n\n{format_summary(findings, folder_path)}"
            else:
                result = "No security issues found."
        else:  # markdown
            result = format_markdown_report(findings, folder_path)
        
        return [types.TextContent(type="text", text=result)]
        
    except Exception as e:
        logger.error(f"Error analyzing folder {folder_path}: {str(e)}")
        return [types.TextContent(
            type="text",
            text=f"Error analyzing folder: {str(e)}"
        )]

async def get_security_controls(arguments: Dict[str, Any]) -> List[types.TextContent]:
    """Get available security controls for a resource type."""
    resource_type = arguments["resource_type"]
    domain_filter = arguments.get("domain")
    
    try:
        # Get relevant controls
        controls = security_reviewer._find_relevant_controls(resource_type)
        
        # Filter by domain if specified
        if domain_filter:
            controls = [c for c in controls if c.get("domain") == domain_filter]
        
        # Format controls information
        result = f"# Security Controls for {resource_type}\n\n"
        
        if domain_filter:
            result += f"**Domain Filter:** {domain_filter}\n\n"
        
        if not controls:
            result += "No relevant controls found for this resource type.\n"
        else:
            result += f"Found {len(controls)} relevant controls:\n\n"
            
            # Group by domain
            by_domain = {}
            for control in controls:
                domain = control.get("domain", "Unknown")
                if domain not in by_domain:
                    by_domain[domain] = []
                by_domain[domain].append(control)
            
            for domain, domain_controls in by_domain.items():
                result += f"## {domain}\n\n"
                for control in domain_controls:
                    result += f"- **{control.get('id', 'N/A')}**: {control.get('name', 'N/A')}\n"
                    if control.get('description'):
                        result += f"  - {control['description'][:100]}...\n"
                result += "\n"
        
        return [types.TextContent(type="text", text=result)]
        
    except Exception as e:
        logger.error(f"Error getting controls for {resource_type}: {str(e)}")
        return [types.TextContent(
            type="text",
            text=f"Error getting security controls: {str(e)}"
        )]

async def validate_security_config(arguments: Dict[str, Any]) -> List[types.TextContent]:
    """Validate a specific security configuration."""
    resource_config = arguments["resource_config"]
    resource_type = arguments["resource_type"]
    control_ids = arguments.get("control_ids", [])
    
    try:
        # Parse the resource configuration
        config = json.loads(resource_config)
        
        # Create a minimal ARM template for analysis
        template = {
            "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
            "contentVersion": "*******",
            "resources": [config]
        }
        
        # Create temporary file for analysis
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_file = Path(temp_dir) / "validation.json"
            temp_file.write_text(json.dumps(template, indent=2))
            
            # Analyze the configuration
            files_to_analyze = security_reviewer.analyze_folder(temp_dir)
            findings = security_reviewer.analyze_files(files_to_analyze)
            
            # Filter by specific control IDs if provided
            if control_ids:
                findings = [f for f in findings if f.get("control_id") in control_ids]
            
            # Format validation results
            result = f"# Security Validation for {resource_type}\n\n"
            
            if not findings:
                result += "✅ **No security issues found** - Configuration appears to follow security best practices.\n"
            else:
                result += f"⚠️ **{len(findings)} security issues found:**\n\n"
                
                # Group by severity
                by_severity = {}
                for finding in findings:
                    severity = finding.get("severity", "UNKNOWN")
                    if severity not in by_severity:
                        by_severity[severity] = []
                    by_severity[severity].append(finding)
                
                for severity in ["CRITICAL", "HIGH", "MEDIUM", "LOW"]:
                    if severity in by_severity:
                        result += f"## {severity} Issues\n\n"
                        for finding in by_severity[severity]:
                            result += f"- **{finding.get('control_id', 'N/A')}**: {finding.get('description', 'N/A')}\n"
                            result += f"  - **Fix**: {finding.get('remediation', 'N/A')}\n\n"
            
            return [types.TextContent(type="text", text=result)]
            
    except json.JSONDecodeError:
        return [types.TextContent(
            type="text",
            text="Error: Invalid JSON in resource_config"
        )]
    except Exception as e:
        logger.error(f"Error validating config: {str(e)}")
        return [types.TextContent(
            type="text",
            text=f"Error validating configuration: {str(e)}"
        )]

def format_summary(findings: List[Dict], path: str) -> str:
    """Format findings as a summary."""
    if not findings:
        return f"✅ **No security issues found** in {path}"
    
    # Count by severity
    severity_counts = {}
    for finding in findings:
        severity = finding.get("severity", "UNKNOWN")
        severity_counts[severity] = severity_counts.get(severity, 0) + 1
    
    result = f"# Security Analysis Summary for {path}\n\n"
    result += f"**Total Issues Found:** {len(findings)}\n\n"
    
    # Severity breakdown
    result += "**Severity Breakdown:**\n"
    for severity in ["CRITICAL", "HIGH", "MEDIUM", "LOW"]:
        count = severity_counts.get(severity, 0)
        if count > 0:
            emoji = {"CRITICAL": "🔴", "HIGH": "🟠", "MEDIUM": "🟡", "LOW": "🔵"}.get(severity, "⚪")
            result += f"- {emoji} {severity}: {count} issues\n"
    
    return result

def format_markdown_report(findings: List[Dict], path: str) -> str:
    """Format findings as a detailed markdown report."""
    if not findings:
        return f"# Security Analysis Report for {path}\n\n✅ **No security issues found**"

    # Sort findings by domain priority
    sorted_findings = security_reviewer._sort_findings_by_priority(findings)

    result = f"# Security Analysis Report for {path}\n\n"
    result += f"**Analysis Date:** {Path(path).name}\n"
    result += f"**Total Issues:** {len(findings)}\n\n"
    
    # Group by domain and severity
    current_domain = None
    current_severity = None
    
    for finding in sorted_findings:
        # Get domain
        domain = security_reviewer._get_finding_domain(finding)
        severity = finding.get("severity", "UNKNOWN")
        
        # Add domain header
        if domain != current_domain:
            current_domain = domain
            result += f"## 🛡️ {domain}\n\n"
            current_severity = None
        
        # Add severity header
        if severity != current_severity:
            current_severity = severity
            emoji = {"CRITICAL": "🔴", "HIGH": "🟠", "MEDIUM": "🟡", "LOW": "🔵"}.get(severity, "⚪")
            result += f"### {emoji} {severity} Issues\n\n"
        
        # Add finding details
        control_id = finding.get("control_id", "N/A")
        description = finding.get("description", "N/A")
        remediation = finding.get("remediation", "N/A")
        file_path = finding.get("file_path", "N/A")
        line = finding.get("line", "N/A")
        
        result += f"#### {control_id}\n"
        result += f"**File:** `{file_path}` (Line {line})\n\n"
        result += f"**Issue:** {description}\n\n"
        result += f"**Remediation:** {remediation}\n\n"
        result += "---\n\n"
    
    return result

async def main():
    """Main entry point for the MCP server."""
    # Set up environment for security analysis
    os.environ.setdefault('ENFORCE_DOMAIN_PRIORITY', 'true')
    os.environ.setdefault('USE_OPTIMIZED_PROMPTS', 'true')
    os.environ.setdefault('ANALYSIS_SEED', '42')
    
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="iac-guardian",
                server_version="1.0.0",
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )

if __name__ == "__main__":
    asyncio.run(main())
