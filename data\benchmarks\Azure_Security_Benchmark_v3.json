{"metadata": {"version": "3.0", "source": "Microsoft Azure Security Benchmark", "processed_date": "2025-03-20T14:54:37.100466", "sheets": ["<PERSON><PERSON>", "Azure Security Benchmark v3"], "rag_optimized": true, "embedding_model": "text-embedding-ada-002", "chunk_strategy": "semantic_control_based"}, "controls": [{"id": "NS-2", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "14.1 - Segment the Network Based on Sensitivity"}, {"framework": "CIS Controls v8 ID(s)", "control": "3.12 - Segment Data Processing and Storage Based on Sensitivity\n4.4 - Implement and Manage a Firewall on Servers"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-4: INFORMATION FLOW ENFORCEMENT\nSC-2: APPLICATION PARTITIONING\nSC-7: BOUNDARY PROTECTION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "1.1\n1.2\n1.3"}, {"framework": "Azure Policy Mapping", "control": "Cognitive Services accounts should restrict network access\nPrivate endpoint should be enabled for PostgreSQL servers\nCognitive Services accounts should disable public network access\nPrivate endpoint should be enabled for MariaDB servers\nAuthorized IP ranges should be defined on Kubernetes Services\nPublic network access on Azure SQL Database should be disabled\nVM Image Builder templates should use private link\nStorage accounts should restrict network access using virtual network rules\nStorage accounts should restrict network access\nAzure Machine Learning workspaces should use private link\nAzure Event Grid topics should use private link\n[Preview]: Storage account public access should be disallowed\nAzure SignalR Service should use private link\n[Preview]: Azure Key Vault should disable public network access\n[Preview]: Private endpoint should be configured for Key Vault\nStorage accounts should use private link\nPrivate endpoint should be enabled for MySQL servers\nPrivate endpoint connections on Azure SQL Database should be enabled\nAzure Cache for Redis should reside within a virtual network\nAzure Cosmos DB accounts should have firewall rules\nAzure Event Grid domains should use private link\nAzure Spring Cloud should use network injection\nPublic network access should be disabled for PostgreSQL servers\nApp Configuration should use private link\nContainer registries should not allow unrestricted network access\nPublic network access should be disabled for MySQL servers\nContainer registries should use private link\nAPI Management services should use a virtual network\nPublic network access should be disabled for MariaDB servers"}]}, {"id": "NS-3", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "9.2 - Ensure Only Approved Ports, Protocols and Services Are Running\n9.4 - Apply Host-Based Firewalls or Port Filtering\n12.3 - Deny Communications with Known Malicious IP Addresses\n12.4 - Deny Communication over Unauthorized Ports\n14.1 - Segment the Network Based on Sensitivity\n14.2 - Enable Firewall Filtering Between VLANs"}, {"framework": "CIS Controls v8 ID(s)", "control": "4.4 - Implement and Manage a Firewall on Servers\n4.8 - Uninstall or Disable Unnecessary Services on Enterprise Assets and Software\n13.10 Perform Application Layer Filtering"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-4: INFORMATION FLOW ENFORCEMENT\nSC-7: BOUNDARY PROTECTION\nCM-7: LEAST FUNCTIONALITY"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "1.1\n1.2\n1.3"}, {"framework": "Azure Policy Mapping", "control": "Management ports should be closed on your virtual machines\nManagement ports of virtual machines should be protected with just-in-time network access control\nIP Forwarding on your virtual machine should be disabled\n[Preview]: All Internet traffic should be routed via your deployed Azure Firewall"}]}, {"id": "NS-4", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "12.6 - Deploy Network-Based IDS Sensors\n12.7 - Deploy Network-Based Intrusion Prevention Systems"}, {"framework": "CIS Controls v8 ID(s)", "control": "13.2 Deploy a Host-Based Intrusion Detection Solution\n13.3 - Deploy a Network Intrusion Detection Solution\n13.7 Deploy a Host-Based Intrusion Prevention Solution\n13.8 - Deploy a Network Intrusion Prevention Solution"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SC-7: BOUNDARY PROTECTION\nSI-4: INFORMATION SYSTEM MONITORING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "11.4"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "NS-5", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "9.5 - Implement Application Firewalls\n12.3 - Deny Communications with Known Malicious IP Addresses"}, {"framework": "CIS Controls v8 ID(s)", "control": "13.10 - Perform Application Layer Filtering"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SC-5: DE<PERSON><PERSON> OF SERVICE PROTECTION\nSC-7: BOUNDARY PROTECTION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "1.1\n1.2\n1.3\n6.6"}, {"framework": "Azure Policy Mapping", "control": "Azure DDoS Protection Standard should be enabled"}]}, {"id": "NS-6", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "9.5 - Implement Application Firewalls\n12.3 - Deny Communications with Known Malicious IP Addresses\n12.9 - Deploy Application Layer Filtering Proxy Server\n18.10 - Deploy Web Application Firewalls (WAFs)"}, {"framework": "CIS Controls v8 ID(s)", "control": "13.10 - Perform Application Layer Filtering"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SC-7: BOUNDARY PROTECTION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "1.1\n1.2\n1.3\n6.6"}, {"framework": "Azure Policy Mapping", "control": "Web Application Firewall (WAF) should be enabled for Azure Front Door Service service\nWeb Application Firewall (WAF) should be enabled for Application Gateway"}]}, {"id": "NS-7", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "9.2 - Ensure Only Approved Ports, Protocols and Services Are Running"}, {"framework": "CIS Controls v8 ID(s)", "control": "4.4 - Implement and Manage a Firewall on Severs\n4.8 - Uninstall or Disable Unnecessary Services on Enterprise Assets and Software"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-4: INFORMATION FLOW ENFORCEMENT\nSC-2: APPLICATION PARTITIONING\nSC-7: BOUNDARY PROTECTION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "1.1\n1.2\n1.3"}, {"framework": "Azure Policy Mapping", "control": "Adaptive network hardening recommendations should be applied on internet facing virtual machines"}]}, {"id": "NS-8", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "9.2 - Ensure Only Approved Ports, Protocols and Services Are Running"}, {"framework": "CIS Controls v8 ID(s)", "control": "4.4 - Implement and Manage a Firewall on Severs\n4.8 - Uninstall or Disable Unnecessary Services on Enterprise Assets and Software"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CM-2: <PERSON><PERSON><PERSON><PERSON>E CONFIGURATION\nCM-6: CONFIGURATION SETTINGS\nCM-7: LEAST FUNCTIONALITY"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "4.1\nA2.1\nA2.2\nA2.3"}, {"framework": "Azure Policy Mapping", "control": "Latest TLS version should be used in your API App\nLatest TLS version should be used in your Web App\nLatest TLS version should be used in your Function App"}]}, {"id": "NS-9", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v8 ID(s)", "control": "12.7 - Ensure Remote Devices Utilize a VPN and are Connecting to \nan Enterprise’s AAA Infrastructure"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CA-3: SYSTEM INTERCONNECTIONS\nAC-17: REMOTE ACCESS\nAC-4: INFORMATION FLOW ENFORCEMENT"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "NS-10", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "7.7 - Use of DNS Filtering Services"}, {"framework": "CIS Controls v8 ID(s)", "control": "4.9 - Configure Trusted DNS Servers on Enterprise Assets\n9.2 - Use DNS Filtering Services"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SC-20: SECURE NAME / ADDRESS RESOLUTION SERVICE (AUTHORITATIVE SOURCE)\nSC-21: SECURE NAME / ADDRESS RESOLUTION SERVICE (RECURSIVE OR CACHING RESOLVER)"}, {"framework": "Azure Policy Mapping", "control": "[Preview]: Azure Defender for DNS should be enabled"}]}, {"id": "DP-1", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "13.1 - \tMaintain an Inventory of Sensitive Information\n14.5 - Utilize an Active Discovery Tool to Identify Sensitive Data"}, {"framework": "CIS Controls v8 ID(s)", "control": "3.2 - Establish and Maintain a Data Inventory\n3.7 - Establish and Maintain a Data Classification Scheme\n3.13 - Deploy a Data Loss Prevention Solution"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "RA-2: SECURITY CATEGORIZATION\nSC-28: PROTECTION OF INFORMATION AT REST"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "A3.2"}, {"framework": "Azure Policy Mapping", "control": "[Preview]: Sensitive data in your SQL databases should be classified"}]}, {"id": "DP-2", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "13.3 - Monitor and Block Unauthorized Network Traffic\n14.7 - Enforce Access Control to Data through Automated Tools"}, {"framework": "CIS Controls v8 ID(s)", "control": "3.13 - Deploy a Data Loss Prevention Solution"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-4: INFORMATION FLOW ENFORCEMENT\nSI-4: INFORMATION SYSTEM MONITORING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "A3.2"}, {"framework": "Azure Policy Mapping", "control": "Azure Defender for open-source relational databases should be enabled\nAzure Defender for Storage should be enabled\nAzure Defender for SQL servers on machines should be enabled\nAzure Defender for Azure SQL Database servers should be enabled\nAzure Defender for SQL should be enabled for unprotected SQL Managed Instances\nAzure Defender for SQL should be enabled for unprotected SQL Managed Instances"}]}, {"id": "DP-3", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "14.4 - Encrypt All Sensitive Information in Transit"}, {"framework": "CIS Controls v8 ID(s)", "control": "3.10 - Encrypt Sensitive Data In Transit"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SC-8: TRANS<PERSON><PERSON>ION CONFIDENTIALITY AND INTEGRITY"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "3.5\n3.6\n4.1"}, {"framework": "Azure Policy Mapping", "control": "Kubernetes clusters should be accessible only over HTTPS\nOnly secure connections to your Azure Cache for Redis should be enabled\nFTPS only should be required in your Function App\nSecure transfer to storage accounts should be enabled\nFTPS should be required in your Web App\nWindows web servers should be configured to use secure communication protocols\nFunction App should only be accessible over HTTPS\nLatest TLS version should be used in your API App\nFTPS only should be required in your API App\nWeb Application should only be accessible over HTTPS\nAPI App should only be accessible over HTTPS\nEnforce SSL connection should be enabled for PostgreSQL database servers\nEnforce SSL connection should be enabled for MySQL database servers\nLatest TLS version should be used in your Web App\nLatest TLS version should be used in your Function App"}]}, {"id": "DP-4", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "14.8 - Encrypt Sensitive Information at Rest"}, {"framework": "CIS Controls v8 ID(s)", "control": "3.11 - Encrypt Sensitive Data at Rest"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SC-28: PROTECTION OF INFORMATION AT REST"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "3.4\n3.5"}, {"framework": "Azure Policy Mapping", "control": "Virtual machines should encrypt temp disks, caches, and data flows between Compute and Storage resources\nTransparent Data Encryption on SQL databases should be enabled\nAutomation account variables should be encrypted\nService Fabric clusters should have the ClusterProtectionLevel property set to EncryptAndSign"}]}, {"id": "DP-5", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "14.8 - Encrypt Sensitive Information at Rest"}, {"framework": "CIS Controls v8 ID(s)", "control": "3.11 - Encrypt Sensitive Data at Rest"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SC-12: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> KEY ESTAB<PERSON><PERSON><PERSON>MENT AND MANAGEMENT\nSC-28: PROTECTION OF INFORMATION AT REST"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "3.4\n3.5\n3.6"}, {"framework": "Azure Policy Mapping", "control": "SQL managed instances should use customer-managed keys to encrypt data at rest\nSQL servers should use customer-managed keys to encrypt data at rest\nPostgreSQL servers should use customer-managed keys to encrypt data at rest\nAzure Cosmos DB accounts should use customer-managed keys to encrypt data at rest\nContainer registries should be encrypted with a customer-managed key\nCognitive Services accounts should enable data encryption with a customer-managed key\nStorage accounts should use customer-managed key for encryption\nMySQL servers should use customer-managed keys to encrypt data at rest\nAzure Machine Learning workspaces should be encrypted with a customer-managed key"}]}, {"id": "DP-6", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "NIST SP800-53 r4 ID(s)", "control": "IA-5: AUTHEN<PERSON><PERSON><PERSON><PERSON> MANAGEMENT\nSC-12: CRYPTO<PERSON><PERSON><PERSON><PERSON> KEY ESTABLISHMENT AND MANAGEMENT\nSC-28: PROTECTION OF INFORMATION AT REST"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "3.6"}, {"framework": "Azure Policy Mapping", "control": "Key Vault keys should have an expiration date\nKey Vault secrets should have an expiration date"}]}, {"id": "DP-7", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "NIST SP800-53 r4 ID(s)", "control": "IA-5: AUTH<PERSON><PERSON><PERSON><PERSON><PERSON> MANAGEMENT\nSC-12: CRYPTO<PERSON><PERSON><PERSON><PERSON> KEY ESTABLISHMENT AND MANAGEMENT\nSC-17: <PERSON><PERSON><PERSON><PERSON> KEY INFRASTRUCTURE CERTIFICATES"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "3.6"}, {"framework": "Azure Policy Mapping", "control": "[Preview]: Certificates should have the specified maximum validity period"}]}, {"id": "DP-8", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "NIST SP800-53 r4 ID(s)", "control": "IA-5: AUTH<PERSON><PERSON><PERSON><PERSON><PERSON> MANAGEMENT\nSC-12: CRYPTO<PERSON><PERSON><PERSON><PERSON> KEY ESTABLISHMENT AND MANAGEMENT\nSC-17: <PERSON><PERSON><PERSON><PERSON> KEY INFRASTRUCTURE CERTIFICATES"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "3.6"}, {"framework": "Azure Policy Mapping", "control": "Key vaults should have purge protection enabled\nAzure Defender for Key Vault should be enabled\nKey vaults should have soft delete enabled\n[Preview]: Azure Key Vault should disable public network access\n[Preview]: Private endpoint should be configured for Key Vault\nResource logs in Key Vault should be enabled"}]}, {"id": "IM-1", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "16.1 - Maintain an Inventory of \nAuthentication Systems\n16.2 - Configure Centralized \nPoint of Authentication"}, {"framework": "CIS Controls v8 ID(s)", "control": "6.7 - Centralize Access Control\n12.5 - Centralize Network Authentication, Authorization, and Auditing (AAA)"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nIA-2: IDENTIFICATION AND AUTHENTICATION (ORGANIZATIONAL USERS)\nIA-8: IDENTIFICATION AND AUTHENTICATION (NON-ORGANIZATIONAL USERS)"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "7.2\n8.3"}, {"framework": "Azure Policy Mapping", "control": "An Azure Active Directory administrator should be provisioned for SQL servers\nService Fabric clusters should only use Azure Active Directory for client authentication"}]}, {"id": "IM-2", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "4.3 - Ensure the Use of Dedicated Administrative Accounts\n4.5 - Use Multi-Factor Authentication for All Administrative Access"}, {"framework": "CIS Controls v8 ID(s)", "control": "5.4 - Restrict Administrator Privileges to Dedicated Administrator Accounts\n6.5 - Require <PERSON><PERSON> for Administrative Access"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nIA-2: IDENTIFICATION AND AUTHENTICATION (ORGANIZATIONAL USERS)\nIA-8: IDENTIFICATION AND AUTHENTICATION (NON-ORGANIZATIONAL USERS)\nSI-4: INFORMATION SYSTEM MONITORING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "8.2\n8.3"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "IM-3", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nIA-4: IDENTIFIER MANAGEMENT\nIA-5: AUTHENTICATOR MANAGEMENT\nIA-9: SERVICE IDENTIFICATION AND AUTHENTICATION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "N/A"}, {"framework": "Azure Policy Mapping", "control": "Managed identity should be used in your Function App\nManaged identity should be used in your Web App\nService principals should be used to protect your subscriptions instead of management certificates\nManaged identity should be used in your API App\nVirtual machines' Guest Configuration extension should be deployed with system-assigned managed identity"}]}, {"id": "IM-4", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "NIST SP800-53 r4 ID(s)", "control": "IA-9: SERVICE IDENTITIFICATION AND AUTHENTICATION"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "IM-5", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "16.2 - Configure Centralized Point of Authentication"}, {"framework": "CIS Controls v8 ID(s)", "control": "12.5 - Centralize Network Authentication, Authorization, and Auditing (AAA)"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "IA-4: <PERSON><PERSON><PERSON><PERSON><PERSON> MANAGEMENT\nIA-2: IDEN<PERSON><PERSON>CATION AND AUTHENTICATION (ORGANIZATIONAL USERS)\nIA-8: IDENTIFICATION AND AUTHENTICATION (NON-ORGANIZATIONAL USERS)"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "IM-6", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "4.2 - Change Default Passwords\n4.5 - Use Multifactor Authentication For All Administrative Access\n12.11 - Require All Remote Logins to Use Multi-Factor Authentication\n16.3 - Require Multi-Factor Authentication"}, {"framework": "CIS Controls v8 ID(s)", "control": "6.3 - Require MFA for Externally-Exposed Applications\n6.4 - Require MFA for Administrative Access"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nIA-2: IDENTIFICATION AND AUTHENTICATION (ORGANIZATIONAL USERS)\nIA-5: AUTHENTICATOR MANAGEMENT\nIA-8: IDENTI<PERSON>CATION AND AUTHENTICATION (NON-ORGANIZATIONAL USERS)"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "7.2\n8.2\n8.3\n8.4"}, {"framework": "Azure Policy Mapping", "control": "Authentication to Linux machines should require SSH keys\nMFA should be enabled accounts with write permissions on your subscription\nMFA should be enabled on accounts with owner permissions on your subscription\nMFA should be enabled on accounts with read permissions on your subscription"}]}, {"id": "IM-7", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "12.11 - Require All Remote Logins to Use Multi-Factor Authentication\n12.12 - Manage All Devices Remotely Logging Into Internal Network\n14.6 - Protect Information Through Access Control Lists\n16.3 - Require Multi-Factor Authentication"}, {"framework": "CIS Controls v8 ID(s)", "control": "3.3 - Configure Data Access Control Lists\n6.4 - Require MFA for Administrative Access\n13.5 - Manage Access Control for Remote Assets"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nAC-6: LEAST PRIVILEGE"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "7.2"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "IM-8", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "18.1 - Establish Secure Coding Practices\n18.6 - Ensure Software Development Personnel Are Trained in Secure Coding\n18.7 - Apply Static and Dynamic Code Analysis Tools"}, {"framework": "CIS Controls v8 ID(s)", "control": "16.9 - Train Developers in Application Security Concepts and Secure Coding\n16.12 - Implement Code-Level Security Checks"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "IA-5: AUTHENTICATOR MANAGEMENT"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "3.5\n6.3\n8.2"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "IM-9", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "12.10 Decrypt Network Traffic at Proxy\n16.2 Configure Centralized Point of Authentication"}, {"framework": "CIS Controls v8 ID(s)", "control": "6.7 - Centralize Access Control\n12.5 - Centralize Network Authentication, Authorization, and Auditing (AAA)"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nSC-11: TRUSTED PATH"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "PA-1", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "4.3 - Ensure the Use of Dedicated Administrative Accounts\n14.6 - Protect Information Through Access Control Lists"}, {"framework": "CIS Controls v8 ID(s)", "control": "5.4 - Restrict Administrator Privileges to Dedicated Administrator Accounts\n6.8 - Define and Maintain Role-Based Access Control"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT\nAC-6: LEAST PRIVILEGE"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "7.1\n7.2\n8.1"}, {"framework": "Azure Policy Mapping", "control": "There should be more than one owner assigned to your subscription\nA maximum of 3 owners should be designated for your subscription\nDeprecated accounts with owner permissions should be removed from your subscription\nExternal accounts with owner permissions should be removed from your subscription"}]}, {"id": "PA-2", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "N/A"}, {"framework": "Azure Policy Mapping", "control": "Management ports of virtual machines should be protected with just-in-time network access control"}]}, {"id": "PA-3", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "16.7 - Establish Process for Revoking Access"}, {"framework": "CIS Controls v8 ID(s)", "control": "6.1 - Establish an Access Granting Process\n6.2 - Establish an Access Revoking Process"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT\nAC-5: SEPARATION OF DUTIES\nAC-6: LEAST PRIVILEGE"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "7.1\n7.2\n8.1"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "PA-4", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "4.1 - Maintain Inventory of Administrative Accounts\n16.6 - Maintain an Inventory of Accounts\n16.8 - Disable Any Unassociated Accounts\nDisable <PERSON><PERSON><PERSON> Accounts\n16.9 - Disable <PERSON><PERSON><PERSON> Accounts"}, {"framework": "CIS Controls v8 ID(s)", "control": "5.1 - <PERSON><PERSON>blish and Maintain an Inventory of Accounts\n5.3 - <PERSON><PERSON> <PERSON><PERSON><PERSON> Accounts\n5.5 - <PERSON><PERSON>blish and Maintain an Inventory of Service Accounts"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT\nAC-6: LEAST PRIVILEGE"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "7.1\n7.2\n8.1\nA3.4"}, {"framework": "Azure Policy Mapping", "control": "External accounts with write permissions should be removed from your subscription\nExternal accounts with read permissions should be removed from your subscription\nDeprecated accounts should be removed from your subscription\nDeprecated accounts with owner permissions should be removed from your subscription\nExternal accounts with owner permissions should be removed from your subscription"}]}, {"id": "PA-5", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "PA-6", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "4.6 - Use Dedicated Workstations For All Administrative Tasks\n11.6 - Use Dedicated Machines For All Network Administrative Tasks\n12.12 - Manage All Devices Remotely Logging into Internal Network"}, {"framework": "CIS Controls v8 ID(s)", "control": "12.8 - Establish and Maintain Dedicated Computing Resources for All Administrative Work\n13.5 Manage Access Control for Remote Assets"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT\nSC-2 APPLICATION PARTITIONING\nSC-7: BOUNDARY PROTECTION"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "PA-7", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "14.6 - Protect Information Through Access Control Lists"}, {"framework": "CIS Controls v8 ID(s)", "control": "3.3 - Configure Data Access Control Lists\n6.8 - Define and Maintain Role-Based Access Control"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nAC-6: LEAST PRIVILEGE"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "7.1\n7.2"}, {"framework": "Azure Policy Mapping", "control": "Audit usage of custom RBAC rules\nRole-Based Access Control (RBAC) should be used on Kubernetes Services"}]}, {"id": "PA-8", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "16.7 - Establish Process for Revoking Access"}, {"framework": "CIS Controls v8 ID(s)", "control": "6.1 - Establish an Access Granting Process\n6.2 - Establish an Access Revoking Process"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-4: INFORMATION FLOW ENFORCEMENT\nAC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "PV-1", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "5.1 - Establish Secure Configurations\n11.1 - Maintain Standard Security Configurations for Network Devices"}, {"framework": "CIS Controls v8 ID(s)", "control": "4.1 - Establish and Maintain a Secure Configuration Process\n4.2 - Establish and Maintain a Secure Configuration Process for Network Infrastructure"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CM-2: <PERSON><PERSON><PERSON><PERSON>E CONFIGURATION\nCM-6: CONFIGURATION SETTINGS"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "1.1\n2.2"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "PV-2", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "5.4 - Deploy System Configuration Management Tools\n5.5 - Implement Automated Configuration Monitoring Systems\n11.3 - Use Automated Tools to Verify Standard Device Configurations and Detect Changes"}, {"framework": "CIS Controls v8 ID(s)", "control": "4.1 - Establish and Maintain a Secure Configuration Process\n4.2 - Establish and Maintain a Secure Configuration Process for Network Infrastructure"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CM-2: <PERSON><PERSON><PERSON><PERSON>E CONFIGURATION\nCM-6: CONFIGURATION SETTINGS"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "2.2"}, {"framework": "Azure Policy Mapping", "control": "CORS should not allow every resource to access your Function Apps\nKubernetes cluster pod hostPath volumes should only use allowed host paths\nAzure Policy Add-on for Kubernetes service (AKS) should be installed and enabled on your clusters\nEnsure API app has 'Client Certificates (Incoming client certificates)' set to 'On'\nRemote debugging should be turned off for Function Apps\nKubernetes clusters should not allow container privilege escalation\nKubernetes cluster services should listen only on allowed ports\nCORS should not allow every resource to access your API App\n[Preview]: Kubernetes clusters should disable automounting API credentials\nKubernetes cluster containers should only listen on allowed ports\nKubernetes cluster containers should not share host process ID or host IPC namespace\nKubernetes cluster containers should only use allowed AppArmor profiles\nCORS should not allow every resource to access your Web Applications\nOperating system version should be the most current version for your cloud service roles\nEnsure WEB app has 'Client Certificates (Incoming client certificates)' set to 'On'\nKubernetes cluster pods should only use approved host network and port range\nKubernetes cluster should not allow privileged containers\n[Preview]: Kubernetes clusters should not use the default namespace\nKubernetes cluster containers should only use allowed capabilities\nRemote debugging should be turned off for Web Applications\n[Preview]: Kubernetes clusters should not grant CAP_SYS_ADMIN security capabilities\nKubernetes cluster containers should run with a read only root file system\nKubernetes cluster containers CPU and memory resource limits should not exceed the specified limits\nRemote debugging should be turned off for API Apps\nFunction apps should have 'Client Certificates (Incoming client certificates)' enabled\nKubernetes cluster pods and containers should only run with approved user and group IDs\nKubernetes cluster containers should only use allowed images\nKubernetes clusters should gate deployment of vulnerable images"}]}, {"id": "PV-3", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "5.1 - Establish Secure Configurations\n5.5 - Implement Automated Configuration Monitoring Systems"}, {"framework": "CIS Controls v8 ID(s)", "control": "4.1 - Establish and Maintain a Secure Configuration Process"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CM-2: <PERSON><PERSON><PERSON><PERSON>E CONFIGURATION\nCM-6: CONFIGURATION SETTINGS"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "2.2\n11.5"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "PV-4", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "5.4 - Deploy System Configuration Management Tools\n5.5 - Implement Automated Configuration Monitoring Systems\n11.3 - Use Automated Tools to Verify Standard Device Configurations and Detect Changes"}, {"framework": "CIS Controls v8 ID(s)", "control": "4.1 - Establish and Maintain a Secure Configuration Process"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CM-2: <PERSON><PERSON><PERSON><PERSON>E CONFIGURATION\nCM-6: CONFIGURATION SETTINGS"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "2.2"}, {"framework": "Azure Policy Mapping", "control": "[Preview]: vTPM should be enabled on supported virtual machines\n[Preview]: Guest Attestation extension should be installed on supported Windows virtual machines\n[Preview]: Guest Attestation extension should be installed on supported Linux virtual machines\n[Preview]: Windows machines should meet requirements of the Azure compute security baseline\n[Preview]: Secure Boot should be enabled on supported Windows virtual machines\n[Preview]: Guest Attestation extension should be installed on supported Linux virtual machines scale sets\nGuest Configuration extension should be installed on your machines\nVirtual machines' Guest Configuration extension should be deployed with system-assigned managed identity\n[Preview]: Guest Attestation extension should be installed on supported Windows virtual machines scale sets\n[Preview]: Linux machines should meet requirements for the Azure compute security baseline"}]}, {"id": "PV-5", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "3.1 - Run Automated Vulnerability Scanning Tools\n3.3 - Protect Dedicated Assessment Accounts\n3.6 - Compare Back-to-back Vulnerability Scans"}, {"framework": "CIS Controls v8 ID(s)", "control": "5.5 - Establish and Maintain an Inventory of Service Accounts\n7.1 - Establish and Maintain a Vulnerability Management Process\n7.5 - Perform Automated Vulnerability Scans of Internal Enterprise Assets\n7.6 - Perform Automated Vulnerability Scans of Externally-Exposed Enterprise Assets"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "RA-3: <PERSON><PERSON><PERSON> ASSESSMENT\nRA-5: VULNERABILITY SCANNING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "6.1 \n6.2\n6.6\n11.2"}, {"framework": "Azure Policy Mapping", "control": "Vulnerability assessment should be enabled on SQL Managed Instance\nA vulnerability assessment solution should be enabled on your virtual machines\nVulnerability assessment should be enabled on your SQL servers"}]}, {"id": "PV-6", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "3.4 - Deploy Automated Operating System Patch Management Tools\n3.5 - Deploy Automated Software Patch Management Tools\n3.7 - Utilize a Risk-rating Process"}, {"framework": "CIS Controls v8 ID(s)", "control": "7.2 - Establish and Maintain a Remediation Process\n7.3 - Perform Automated Operating System Patch Management\n7.4 - Perform Automated Application Patch Management\n7.7 - Remediate Detected Vulnerabilities"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "RA-3: <PERSON><PERSON><PERSON> ASSESSMENT\nRA-5: VU<PERSON>NERABI<PERSON>ITY SCANNING\nSI-2: FLAW REMEDIATION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "6.1\n6.2\n6.5\n11.2"}, {"framework": "Azure Policy Mapping", "control": "Ensure that 'PHP version' is the latest, if used as a part of the Api app\nEnsure that 'PHP version' is the latest, if used as a part of the API app\nVulnerabilities in security configuration on your virtual machine scale sets should be remediated\nEnsure that 'Java version' is the latest, if used as a part of the Web app\nEnsure that 'Java version' is the latest, if used as a part of the Web app\nVulnerabilities in Azure Container Registry images should be remediated\nSQL servers on machines should have vulnerability findings resolved\nEnsure that 'Python version' is the latest, if used as a part of the Web app\nEnsure that 'Python version' is the latest, if used as a part of the Web app\nEnsure that 'Python version' is the latest, if used as a part of the Function app\nEnsure that 'Python version' is the latest, if used as a part of the Function app\nEnsure that 'PHP version' is the latest, if used as a part of the WEB app\nEnsure that 'PHP version' is the latest, if used as a part of the WEB app\nEnsure that 'Python version' is the latest, if used as a part of the Api app\nEnsure that 'Python version' is the latest, if used as a part of the API app\nVulnerabilities should be remediated by a Vulnerability Assessment solution\nSystem updates should be installed on your machines\nEnsure that 'Java version' is the latest, if used as a part of the Api app\nEnsure that 'Java version' is the latest, if used as a part of the API app\nEnsure that 'Java version' is the latest, if used as a part of the Function app\nEnsure that 'Java version' is the latest, if used as a part of the Function app\nSystem updates on virtual machine scale sets should be installed\nVulnerabilities in security configuration on your machines should be remediated\nVulnerabilities in container security configurations should be remediated\n[Preview]: Kubernetes Services should be upgraded to a non-vulnerable Kubernetes version\nSQL databases should have vulnerability findings resolved"}]}, {"id": "PV-7", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "20.1 - Establish a Penetration Testing Program\n20.2 - Conduct Regular External and Internal Penetration Tests\n20.3 - Perform Periodic Red Team Exercises"}, {"framework": "CIS Controls v8 ID(s)", "control": "18.1 - Establish and Maintain a Penetration Testing Program\n18.2 - Perform Periodic External Penetration Tests\n18.3 - Remediate Penetration Test Findings\n18.4 - Validate Security Measures\n18.5 - Perform Periodic Internal Penetration Tests"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CA-8: PENETRA<PERSON>ON TESTING\nRA-5: VULNERABILITY SCANNING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "6.6\n11.2\n11.3"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "LT-1", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "6.7 - Regularly Review Logs"}, {"framework": "CIS Controls v8 ID(s)", "control": "8.11 - Conduct Audit Log Reviews"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AU-3: CONTENT OF AUDIT RECORDS\nAU-6: AUDIT RE<PERSON><PERSON><PERSON>, ANALYSIS, AND REPORTING\nAU-12: AUDIT GENERATION\nSI-4: INFORMATION SYSTEM MONITORING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "10.6\n10.8\nA3.5"}, {"framework": "Azure Policy Mapping", "control": "Azure Defender for open-source relational databases should be enabled\nAzure Defender for Key Vault should be enabled\nAzure Defender for App Service should be enabled\nAzure Defender for Storage should be enabled\nAzure Defender for servers should be enabled\nAzure Defender for Kubernetes should be enabled\nAzure Defender for SQL servers on machines should be enabled\nAzure Defender for Azure SQL Database servers should be enabled\n[Preview]: Azure Arc enabled Kubernetes clusters should have Azure Defender's extension installed\n[Preview]: Azure Kubernetes Service clusters should have Azure Defender profile enabled\nAzure Defender for SQL should be enabled for unprotected Azure SQL servers\nAzure Defender for SQL should be enabled for unprotected SQL Managed Instances\n[Preview]: Azure Defender for DNS should be enabled\nWindows Defender Exploit Guard should be enabled on your machines\nAzure Defender for container registries should be enabled\nAzure Defender for Resource Manager should be enabled"}]}, {"id": "LT-2", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "4.9 - <PERSON><PERSON> and <PERSON><PERSON> on Unsuccessful Administrative Account Login\n6.7 - Regularly Review Logs\n16.13 - <PERSON><PERSON> on Account Login Behavior Deviation"}, {"framework": "CIS Controls v8 ID(s)", "control": "8.11 - Conduct Audit Log Reviews"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AU-3: CONTENT OF AUDIT RECORDS\nAU-6: AUDIT RE<PERSON><PERSON><PERSON>, ANALYSIS, AND REPORTING\nAU-12: AUDIT GENERATION\nSI-4: INFORMATION SYSTEM MONITORING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "10.6\n10.8\nA3.5"}, {"framework": "Azure Policy Mapping", "control": "Azure Defender for open-source relational databases should be enabled\nAzure Defender for Key Vault should be enabled\nAzure Defender for App Service should be enabled\nAzure Defender for Storage should be enabled\nAzure Defender for servers should be enabled\nAzure Defender for Kubernetes should be enabled\nAzure Defender for SQL servers on machines should be enabled\nAzure Defender for Azure SQL Database servers should be enabled\n[Preview]: Azure Arc enabled Kubernetes clusters should have Azure Defender's extension installed\n[Preview]: Azure Kubernetes Service clusters should have Azure Defender profile enabled\nAzure Defender for SQL should be enabled for unprotected Azure SQL servers\nAzure Defender for SQL should be enabled for unprotected SQL Managed Instances\n[Preview]: Azure Defender for DNS should be enabled\nWindows Defender Exploit Guard should be enabled on your machines\nAzure Defender for container registries should be enabled\nAzure Defender for Resource Manager should be enabled\nResource logs in Azure Kubernetes Service should be enabled"}]}, {"id": "LT-3", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "6.2 - Activate Audit Logging\n6.3 - Enable Detailed Logging\n8.8 - Enable Command-Line Audit Logging"}, {"framework": "CIS Controls v8 ID(s)", "control": "8.2 - Collect Audit Logs\n8.5 - Collect Detailed Audit Logs\n8.12 - Collect Service Provider Logs"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AU-3: CONTENT OF AUDIT RECORDS\nAU-6: AUDIT RE<PERSON><PERSON><PERSON>, ANALYSIS, AND REPORTING\nAU-12: AUDIT GENERATION\nSI-4: INFORMATION SYSTEM MONITORING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "10.1\n10.2\n10.3"}, {"framework": "Azure Policy Mapping", "control": "Resource logs in Azure Data Lake Store should be enabled\nResource logs in Logic Apps should be enabled\nResource logs in IoT Hub should be enabled\nResource logs in Batch accounts should be enabled\nResource logs in Virtual Machine Scale Sets should be enabled\nResource logs in Event Hub should be enabled\nAuditing on SQL server should be enabled\nResource logs in Search services should be enabled\nDiagnostic logs in App Services should be enabled\nResource logs in Data Lake Analytics should be enabled\nResource logs in Key Vault should be enabled\nResource logs in Service Bus should be enabled\nResource logs in Azure Stream Analytics should be enabled"}]}, {"id": "LT-4", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "6.2 - Activate Audit Logging\n6.3 - Enable Detailed Logging\n7.6 - Log All URL Requests\n8.7 - Enable DNS Query Logging\n12.8 - Deploy NetFlow Collection on Networking Boundary Devices"}, {"framework": "CIS Controls v8 ID(s)", "control": "8.2 - Collect Audit Logs\n8.5 - Collect Detailed Audit Logs\n8.6 - Collect DNS Query Audit Logs\n8.7 - Collect URL Request Audit Logs\n13.6 - Collect Network Traffic Flow Logs"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AU-3: CONTENT OF AUDIT RECORDS\nAU-6: AUDIT RE<PERSON><PERSON><PERSON>, ANALYSIS, AND REPORTING\nAU-12: AUDIT GENERATION\nSI-4: INFORMATION SYSTEM MONITORING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "10.8"}, {"framework": "Azure Policy Mapping", "control": "[Preview]: Network traffic data collection agent should be installed on Linux virtual machines\n[Preview]: Network traffic data collection agent should be installed on Windows virtual machines"}]}, {"id": "LT-5", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "6.5 - Central Log Management\n6.6 - Deploy SIEM or Log Analytic tool\n6.7 - Regularly Review Logs\n8.6 - Centralize Anti-Malware Logging"}, {"framework": "CIS Controls v8 ID(s)", "control": "8.9 - Centralize Audit Logs\n8.11 - Conduct Audit Log Reviews\n13.1 - Centralize Security Event Alerting"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AU-3: CONTENT OF AUDIT RECORDS\nAU-6: AUDIT RE<PERSON><PERSON><PERSON>, ANALYSIS, AND REPORTING\nAU-12: AUDIT GENERATION\nSI-4: INFORMATION SYSTEM MONITORING"}, {"framework": "Azure Policy Mapping", "control": "Auto provisioning of the Log Analytics agent should be enabled on your subscription\n[Preview]: Log Analytics agent should be installed on your Linux Azure Arc machines\nLog Analytics agent should be installed on your virtual machine scale sets for Azure Security Center monitoring\nLog Analytics agent should be installed on your virtual machine for Azure Security Center monitoring\nLog Analytics agent health issues should be resolved on your machines\n[Preview]: Log Analytics agent should be installed on your Windows Azure Arc machines"}]}, {"id": "LT-6", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "6.4 - Ensure Adequate Storage for Logs"}, {"framework": "CIS Controls v8 ID(s)", "control": "8.3 - Ensure Adequate Audit Log Storage\n8.10 - Retain Audit Logs"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AU-11: AUDIT RECORD RETENTION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "10.5\n10.7"}, {"framework": "Azure Policy Mapping", "control": "SQL servers with auditing to storage account destination should be configured with 90 days retention or higher"}]}, {"id": "LT-7", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "6.1 - Utilize Three Synchronized Time Sources"}, {"framework": "CIS Controls v8 ID(s)", "control": "8.4 - Standardize Time Synchronization"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AU-8: TIME STAMPS"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "10.4"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "AM-1", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "1.1 - Utilize an Active Discovery Tool\n1.2 - Use a Passive Asset Discovery Tool\n1.4 - Maintain Detailed Asset Inventory\n1.5 - Maintain Asset Inventory Information\n2.1 - Maintain Inventory of Authorized Software"}, {"framework": "CIS Controls v8 ID(s)", "control": "1.1 - Establish and Maintain Detailed Enterprise Asset Inventory\n1.5 - Use a Passive Asset Discovery Tool\n2.1 - Establish and Maintain a Software Inventory\n2.4 - Utilize Automated Software Inventory Tools"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CM-8: INFORMATION SYSTEM COMPONENT INVENTORY\nPM-5: INFORMATION SYSTEM INVENTORY"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "2.4"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "AM-2", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "2.7 - Utilize Application Whitelisting\n2.8 - Implement Application Whitelisting of Libraries\n2.9 - Implement Application Whitelisting of Scripts\n9.2 - Ensure Only Approved Ports, Protocols, and Services Are Running"}, {"framework": "CIS Controls v8 ID(s)", "control": "2.5 - Allowlist Authorized Software\n2.6 - Allowlist Authorized Libraries\n2.7 - Allowlist Authorized Scripts\n4.8 - Uninstall or Disable Unnecessary Services on Enterprise Assets and Software"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CM-8: INFORMATION SYSTEM COMPONENT INVENTORY\nPM-5: INFORMATION SYSTEM INVENTORY"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "6.3"}, {"framework": "Azure Policy Mapping", "control": "Virtual machines should be migrated to new Azure Resource Manager resources\nStorage accounts should be migrated to new Azure Resource Manager resources"}]}, {"id": "AM-3", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "1.4 - Maintain Detailed Asset Inventory\n1.5 - Maintain Asset Inventory Information\n2.1 - Maintain Inventory of Authorized Software\n2.4 - Track Software Inventory Information"}, {"framework": "CIS Controls v8 ID(s)", "control": "1.1 - Establish and Maintain Detailed Enterprise Asset Inventory\n2.1 - Establish and Maintain a Software Inventory"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CM-8: INFORMATION SYSTEM COMPONENT INVENTORY\nCM-7: LEAST FUNCTIONALITY"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "2.4"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "AM-4", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "14.6 - Protect Information Through Access Control Lists"}, {"framework": "CIS Controls v8 ID(s)", "control": "3.3 - Configure Data Access Control Lists"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-3: ACCESS ENFORCEMENT"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "AM-5", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "2.7 - Utilize Application Whitelisting\n2.8 - Implement Application Whitelisting of Libraries\n2.9 - Implement Application Whitelisting of Scripts\n9.2 - Ensure Only Approved Ports, Protocols, and Services Are Running"}, {"framework": "CIS Controls v8 ID(s)", "control": "2.5 - Allowlist Authorized Software\n2.6 - Allowlist Authorized Libraries\n2.7 - Allowlist Authorized Scripts\n4.8 - Uninstall or Disable Unnecessary Services on Enterprise Assets and Software"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CM-8: INFORMA<PERSON><PERSON> SYSTEM COMPONENT INVENTORY\nCM-7: LEAST FUNCTIONALITY\nCM-10: SOFTWARE USAGE RESTRICTIONS\nCM-11: USER-INSTALLED SOFTWARE"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "6.3"}, {"framework": "Azure Policy Mapping", "control": "Allowlist rules in your adaptive application control policy should be updated\nAdaptive application controls for defining safe applications should be enabled on your machines"}]}, {"id": "ES-1", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "9.4 - Apply Host-Based Firewalls or Port Filtering"}, {"framework": "CIS Controls v8 ID(s)", "control": "13.7 - Deploy a Host-Based Intrusion Prevention Solution"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SC-3: SECURITY FUNCTION ISOLATION\nSI-2: FLAW REMEDIATION\nSI-3: MALICIOUS CODE PROTECTION\nSI-16 MEMORY PROTECTION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "11.5"}, {"framework": "Azure Policy Mapping", "control": "Azure Defender for servers should be enabled"}]}, {"id": "ES-2", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "8.1 - Utilize Centrally Managed Anti-malware Software"}, {"framework": "CIS Controls v8 ID(s)", "control": "10.1 - Deploy and Maintain Anti-Malware Software"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SC-3: SECURITY FUNCTION ISOLATION\nSI-2: FLAW REMEDIATION\nSI-3: MALICIOUS CODE PROTECTION\nSI-16 MEMORY PROTECTION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "5.1"}, {"framework": "Azure Policy Mapping", "control": "Endpoint protection should be installed on your machines\nEndpoint protection solution should be installed on virtual machine scale sets\nEndpoint protection health issues should be resolved on your machines\nMonitor missing Endpoint Protection in Azure Security Center\nWindows Defender Exploit Guard should be enabled on your machines"}]}, {"id": "ES-3", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "8.2 - Ensure Anti-Malware Software and Signatures are Updated"}, {"framework": "CIS Controls v8 ID(s)", "control": "10.2 - Configure Automatic Anti-Malware Signature Updates"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SI-2: FLAW REMEDIATION\nSI-3: MALICIOUS CODE PROTECTION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "5.2\n5.3"}, {"framework": "Azure Policy Mapping", "control": "Endpoint protection health issues should be resolved on your machines"}]}, {"id": "BR-1", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "10.1 - Ensure Regular Automated Backups"}, {"framework": "CIS Controls v8 ID(s)", "control": "11.2 - Perform Automated Backups"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CP-2: CONTINGENCY PLAN\nCP-4: CONTINGENCY PLAN TESTING\nCP-9: INFORMATION SYSTEM BACKUP"}, {"framework": "Azure Policy Mapping", "control": "Azure Backup should be enabled for Virtual Machines\nGeo-redundant backup should be enabled for Azure Database for MariaDB\nGeo-redundant backup should be enabled for Azure Database for PostgreSQL\nGeo-redundant backup should be enabled for Azure Database for MySQL"}]}, {"id": "BR-2", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "10.4 - Ensure Protection of Backups"}, {"framework": "CIS Controls v8 ID(s)", "control": "11.3 - Protect Recovery Data"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CP-6: <PERSON><PERSON><PERSON><PERSON> STORAGE SITE\nCP-9: INFORMATION SYSTEM BACKUP"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "3.4"}, {"framework": "Azure Policy Mapping", "control": "Azure Backup should be enabled for Virtual Machines\nGeo-redundant backup should be enabled for Azure Database for MariaDB\nGeo-redundant backup should be enabled for Azure Database for PostgreSQL\nGeo-redundant backup should be enabled for Azure Database for MySQL"}]}, {"id": "BR-3", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "10.4 - Ensure Protection of Backups"}, {"framework": "CIS Controls v8 ID(s)", "control": "11.3 - Protect Recovery Data"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CP-9: INFORMATION SYSTEM BACKUP"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "BR-4", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "10.3 - Test Data on Backup Media"}, {"framework": "CIS Controls v8 ID(s)", "control": "11.5 - Test Data Recovery"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CP-4: CONT<PERSON><PERSON><PERSON><PERSON> PLAN TESTING\nCP-9: INFORMATION SYSTEM BACKUP"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "IR-1", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "19.1 - Document Incident Response Procedures\n19.7 - Conduct Periodic Incident Scenario Sessions for Personnel"}, {"framework": "CIS Controls v8 ID(s)", "control": "17.4 - Establish and Maintain an Incident Response Process\n17.7 - Conduct Routine Incident Response Exercises"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "IR-4: INCIDENT HANDLING\nIR-8: INCIDENT RESPONSE PLAN"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "10.8"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "IR-2", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "19.2 - Assign Job Titles and Duties for Incident Response\n19.3 - Designate Management Personnel to Support Incident Handling\n19.4 - Devise Organization-wide Standards for Reporting Incidents\n19.5 - Maintain Contact Information For Reporting Security Incidents"}, {"framework": "CIS Controls v8 ID(s)", "control": "17.1 - Designate Personnel to Manage Incident Handling\n17.3 - Establish and Maintain an Enterprise Process for Reporting Incidents\n17.6 - Define Mechanisms for Communicating During Incident Response"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "IR-4: INCIDENT HANDLING\nIR-8: INCIDENT RESPONSE PLAN\nIR-5: INCIDENT MONITORING\nIR-6: INCIDENT REPORTING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "12.10"}, {"framework": "Azure Policy Mapping", "control": "Email notification to subscription owner for high severity alerts should be enabled\nSubscriptions should have a contact email address for security issues\nEmail notification for high severity alerts should be enabled"}]}, {"id": "IR-3", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "19.8 - Create Incident Scoring and Prioritization Schema"}, {"framework": "CIS Controls v8 ID(s)", "control": "17.9 - Establish and Maintain Security Incident Thresholds"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "IR-4: INCIDENT HANDLING\nIR-5: INCIDENT MONITORING\nIR-7 INCIDENT RESPONSE ASSISTANCE"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "10.8"}, {"framework": "Azure Policy Mapping", "control": "Azure Defender for open-source relational databases should be enabled\nAzure Defender for Key Vault should be enabled\nAzure Defender for App Service should be enabled\nAzure Defender for Storage should be enabled\nAzure Defender for servers should be enabled\nAzure Defender for Kubernetes should be enabled\nAzure Defender for SQL servers on machines should be enabled\nAzure Defender for Azure SQL Database servers should be enabled\nAzure Defender for SQL should be enabled for unprotected Azure SQL servers\nAzure Defender for SQL should be enabled for unprotected SQL Managed Instances\n[Preview]: Azure Defender for DNS should be enabled\nAzure Defender for container registries should be enabled\nAzure Defender for Resource Manager should be enabled"}]}, {"id": "IR-4", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "NIST SP800-53 r4 ID(s)", "control": "IR-4: INCIDENT HANDLING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "12.10"}, {"framework": "Azure Policy Mapping", "control": "Network Watcher should be enabled"}]}, {"id": "IR-5", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "19.8 - Create Incident Scoring and Prioritization Schema"}, {"framework": "CIS Controls v8 ID(s)", "control": "17.4 - Establish and Maintain an Incident Response Process\n17.9 - Establish and Maintain Security Incident Thresholds"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "IR-4: INCIDENT HANDLING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "12.10"}, {"framework": "Azure Policy Mapping", "control": "Azure Defender for open-source relational databases should be enabled\nAzure Defender for Key Vault should be enabled\nAzure Defender for App Service should be enabled\nAzure Defender for Storage should be enabled\nAzure Defender for servers should be enabled\nAzure Defender for Kubernetes should be enabled\nAzure Defender for SQL servers on machines should be enabled\nAzure Defender for Azure SQL Database servers should be enabled\nAzure Defender for SQL should be enabled for unprotected Azure SQL servers\nAzure Defender for SQL should be enabled for unprotected SQL Managed Instances\n[Preview]: Azure Defender for DNS should be enabled\nAzure Defender for container registries should be enabled\nAzure Defender for Resource Manager should be enabled"}]}, {"id": "IR-6", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "NIST SP800-53 r4 ID(s)", "control": "IR-4: INCIDENT HANDLING\nIR-5: INCIDENT MONITORING\nIR-6: INCIDENT REPORTING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "12.10"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "IR-7", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v8 ID(s)", "control": "17.8 - Conduct Post-Incident Reviews"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "IR-4 INCIDENT HANDLING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "12.10"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "DS-1", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v8 ID(s)", "control": "16.10 - Apply Secure Design Principles in Application Architectures\n16.14 - Conduct Threat Modeling"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SA-15: DEVELOPMENT PROCESS, STANDARDS, AND TOOLS"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "6.5\n12.2"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "DS-2", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "18.3 - Verify That Acquired Software is Still Supported\n18.4 - Only Use Up-to-Date And Trusted Third-Party Components\n18.8 - Establish a Process to Accept and Address Reports of Software Vulnerabilities"}, {"framework": "CIS Controls v8 ID(s)", "control": "16.4 - Establish and Manage an Inventory of Third-Party Software Components\n16.6 - Establish and Maintain a Severity Rating System and Process for Application Vulnerabilities\n16.11 - Leverage Vetted Modules or Services for Application Security Components"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SA-12: <PERSON><PERSON><PERSON><PERSON> CHAIN PROTECTION\nSA-15: DEVELOPMENT PROCESS, STANDARDS, AND TOOLS"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "6.3\n6.5"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "DS-3", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "18.11 - Use Standard Hardening Configuration Templates for Databases"}, {"framework": "CIS Controls v8 ID(s)", "control": "16.7 - Use Standard Hardening Configuration Templates for Application Infrastructure"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CM-2: <PERSON><PERSON><PERSON><PERSON><PERSON> CONFIGURATION\nCM-6: CONFIGURATION SETTINGS\nAC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nAC-6: LEAST PRIVILEGE"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "2.2\n6.3\n7.1"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "DS-4", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "18.7 - Apply Static and Dynamic Code Analysis Tools"}, {"framework": "CIS Controls v8 ID(s)", "control": "16.12 - Implement Code-Level Security Checks"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SA-11: <PERSON><PERSON><PERSON><PERSON><PERSON> TESTING AND EVALUATION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "6.3\n6.5"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "DS-5", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "18.7 - Apply Static and Dynamic Code Analysis Tools"}, {"framework": "CIS Controls v8 ID(s)", "control": "16.12 - Implement Code-Level Security Checks"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SA-11: <PERSON><PERSON><PERSON><PERSON><PERSON> TESTING AND EVALUATION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "6.3\n6.5"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "DS-6", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "5.2 - Deploy System Configuration Management Tools\n5.3 - Securely Store Master Images\n5.4 - Deploy System Configuration Management Tools\n5.5 - Implement Automated Configuration Monitoring Systems\n18.1 - Establish Secure Coding Practices"}, {"framework": "CIS Controls v8 ID(s)", "control": "7.5 - Perform Automated Vulnerability Scans of Internal Enterprise Assets\n7.6 - Perform Automated Vulnerability Scans of Externally-Exposed Enterprise Assets\n7.7 - Remediate Detected Vulnerabilities\n16.1 - Establish and Maintain a Secure Application Development Process\n16.7 - Use Standard Hardening Configuration Templates for Application Infrastructure"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CM-2: <PERSON><PERSON><PERSON><PERSON><PERSON> CONFIGURATION\nCM-6: CONFIGURATION SETTINGS\nAC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nAC-6: LEAST PRIVILEGE"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "6.1\n6.2\n6.3"}, {"framework": "Azure Policy Mapping", "control": "Vulnerabilities in Azure Container Registry images should be remediated\nVulnerabilities in container security configurations should be remediated"}]}, {"id": "DS-7", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "6.2 - Activate audit logging\n6.3 - Enable Detailed Logging\n6.5 - Central Log Management\n6.6 - Deploy SIEM or Log Analytic tool\n6.7 - Regularly Review Logs\n6.8 - Regularly Tune SIEM"}, {"framework": "CIS Controls v8 ID(s)", "control": "8.2 Collect Audit Logs\n8.5 Collect Detailed Audit Logs\n8.9 Centralize Audit Logs\n8.11 Conduct Audit Log Reviews"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AU-3: CONTENT OF AUDIT RECORDS\nAU-6: AUDIT RE<PERSON><PERSON><PERSON>, ANALYSIS, AND REPORTING\nAU-12: AUDIT GENERATION\nSI-4: INFORMATION SYSTEM MONITORING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "10.1\n10.2\n10.3\n10.6"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "GS-1", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "17.2 - Deliver Training to Fill the Skills Gap"}, {"framework": "CIS Controls v8 ID(s)", "control": "14.9 - Conduct Role-Specific Security Awareness and Skills Training"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "PL-9: <PERSON><PERSON><PERSON><PERSON> MANAGEMENT\nPM-10: SECURITY AUTHORIZATION PROCESS\nPM-13: INFORMATION SECURITY WORKFORCE\t\nAT-1: SECURITY AWARENESS AND TRAINING POLICY AND PROCEDURES\nAT-3: ROLE-BASED SECURITY TRAINING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "12.4"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "GS-2", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "2.10 - Physically or Logically Segregate High Risk Applications\n14.1 - Segment the Network Based on Sensitivity"}, {"framework": "CIS Controls v8 ID(s)", "control": "3.12 - Segment Data Processing and Storage Based on Sensitivity"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-4: INFORMATION FLOW ENFORCEMENT\nSC-7: BOUNDARY PROTECTION\nSC-2: APPLICATION PARTITIONING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "1.2\n6.4"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "GS-3", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "14.1 - Segment the Network Based on Sensitivity"}, {"framework": "CIS Controls v8 ID(s)", "control": "3.1 - Establish and Maintain a Data Management Process\n3.7 - Establish and Maintain a Data Classification Scheme\n3.12 - Segment Data Processing and Storage Based on Sensitivity"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-4: INFORMATION FLOW ENFORCEMENT\nSI-4: INFORMATI<PERSON> SYSTEM MONITORING\nSC-8: TRANSMISSION CONFIDENTIALITY AND INTEGRITY\nSC-12: CRYPTO<PERSON><PERSON><PERSON><PERSON> KEY ESTABLISHMENT AND <PERSON><PERSON><PERSON>MENT\nSC-17: <PERSON><PERSON><PERSON><PERSON> KEY INFRASTRUCTURE CERTIFICATES\nSC-28: PROTECTION OF INFORMATION AT REST\nRA-2: SECURITY CATEGORIZATION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "3.1\n3.2 \n3.3\n3.4\n3.5\n3.6\n3.7\n4.1\nA3.2"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "GS-4", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "12.1 - Maintain an Inventory of Network Boundaries"}, {"framework": "CIS Controls v8 ID(s)", "control": "12.2 - Establish and Maintain a Secure Network Infrastructure\n12.4 - Establish and Maintain Architecture Diagram(s)"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-4: INFORMATION FLOW ENFORCEMENT\nAC-17: R<PERSON><PERSON><PERSON> ACCESS\nCA-3: SYSTEM INTERCONNECTIONS\nCM-1: CONFIGURATION MANAGEMENT POLICY AND PROCEDURES\nCM-2: BASELINE CONFIGURATION\nCM-6: CONFIGURATION SETTINGS\nCM-7: LEAST FUNCTIONALITY\nSC-1: SYSTEM AND COMMUNICATIONS PROTECTION POLICY AND PROCEDURES\nSC-2: APPLICATION PARTITIONING\nSC-5: DENIAL OF SERVICE PROTECTION\nSC-7: BOUNDARY PROTECTION\nSC-20: SECURE NAME / ADDRESS RESOLUTION SERVICE (AUTHORITATIVE SOURCE)\nSC-21: SECURE NAME / ADDRESS RESOLUTION SERVICE (RECURSIVE OR CACHING RESOLVER)\nSI-4: INFORMATION SYSTEM MONITORING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "1.1\n1.2\n1.3\n1.5\n4.1\n6.6\n11.4\nA2.1\nA2.2\nA2.3\nA3.2"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "GS-5", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "5.1 - Establish Secure Configurations"}, {"framework": "CIS Controls v8 ID(s)", "control": "4.1 - Establish and Maintain a Secure Configuration Process\n4.2 - Establish and Maintain a Secure Configuration Process for Network Infrastructure"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CA-1: <PERSON><PERSON><PERSON><PERSON> ASSESSMENT AND <PERSON><PERSON><PERSON>I<PERSON><PERSON><PERSON> POLICY AND PROCEDURES\nCA-8: PENETRATION TESTING\nCM-1: CONFIGURATION MANAGEMENT POLICY AND PROCEDURES\nCM-2: BASELINE CONFIGURATION\nCM-6: CONFIGURATION SETTINGS\nRA-1: <PERSON><PERSON><PERSON> ASSESSMENT POLICY AND PROCEDURES\nRA-3: <PERSON><PERSON><PERSON> ASSESSMENT\nRA-5: VULNERABILITY SCANNING\nSI-1: SYSTEM AND INFORMATION INTEGRITY POLICY AND PROCEDURES\nSI-2: FLAW REMEDIATION\nSI-5: SECURITY ALERTS, ADVISORIES, AND DIRECTIVES"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "1.1\n1.2\n2.2\n6.1\n6.2\n6.5\n6.6\n11.2\n11.3\n11.5"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "GS-6", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "4.5 - Use Multifactor Authentication For All Administrative Access\n16.2 - Configure Centralized Point of Authentication"}, {"framework": "CIS Controls v8 ID(s)", "control": "5.6 - Centralize Account Management\n6.5 - Require MFA for Administrative Access\n6.7 - Centralize Access Control"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AC-1: ACCESS CONTROL POLICY AND PROCEDURES\nAC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nAC-4: INFORMATION FLOW ENFORCEMENT\nAC-5: SEPARATION OF DUTIES\nAC-6: LEAST PRIVILEGE\nIA-1: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> POLICY AND PROCEDURES\nIA-2: ID<PERSON><PERSON><PERSON><PERSON><PERSON>ON AND AUTHENTICATION (ORGANIZATIONAL USERS)\nIA-4: IDENTIFIER MANAGEMENT\nIA-5: AUTHENTICATOR MANAGEMENT\nIA-8: IDENTIFICATION AND AUTHENTICATION (NON-ORGANIZAT<PERSON>AL USERS)\nIA-9: SERVICE IDENTIFICATION AND AUTHENTICATION\nSI-4: INFORMATION SYSTEM MONITORING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "7.1\n7.2\n7.3\n8.1\n8.2\n8.3\n8.4\n8.5\n8.6\n8.7\n8.8\nA3.4"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "GS-7", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "6.2 -Activate audit logging\n6.3 - Enable Detailed Logging\n6.6 - Deploy SIEM or Log Analytic tool\n6.7 - Regularly Review Logs\n19.1 - Document Incident Response Procedures\n19.5 - Maintain Contact Information For Reporting Security Incidents\n19.7 - Conduct Periodic Incident Scenario Sessions for Personnel"}, {"framework": "CIS Controls v8 ID(s)", "control": "8.1 - Establish and Maintain an Audit Log Management Process\n13.1 - Centralize Security Event Alerting\n17.2 - Establish and Maintain Contact Information for Reporting Security Incidents\n17.4 - Establish and Maintain an Incident Response Process\n17.7 - Conduct Routine Incident Response Exercises"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "AU-1: AUDIT AND ACCOUNTA<PERSON><PERSON><PERSON>Y POLICY AND PROCEDURES\nIR-1: INCIDENT RESPONSE POLICY AND PROCEDURES\nIR-2: INCIDENT RESPONSE TRAINING\nIR-10: INTEGRATED INFORMATION SECURITY ANALYSIS TEAM\nSI-1: <PERSON><PERSON><PERSON><PERSON> AND INFORMA<PERSON>ON INTEGRITY POLICY AND PROCEDURES\nSI-5: SECURITY ALERTS, ADVISORIES, AND DIRECTIVES"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "10.1\n10.2\n10.3\n10.4\n10.5\n10.6\n10.7\n10.8\n10.9\n12.10\nA3.5"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "GS-8", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "10.1 - Ensure Regular Automated Backups"}, {"framework": "CIS Controls v8 ID(s)", "control": "11.1 - Establish and Maintain a Data Recovery Process"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "CP-1: CONT<PERSON><PERSON><PERSON><PERSON> PLANNING POLICY AND PROCEDURES\nCP-9: INFORMATION SYSTEM BACKUP\nCP-10: INFORMATION SYSTEM RECOVERY AND RECON<PERSON><PERSON>UTION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "3.4"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "GS-9", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "8.1 - Utilize Centrally Managed Anti-malware Software\n9.4 - Apply Host-Based Firewalls or Port-Filtering"}, {"framework": "CIS Controls v8 ID(s)", "control": "4.4 - Implement and Manage a Firewall on Servers\n10.1 - Deploy and Maintain Anti-Malware Software"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SI-2: FL<PERSON>W REMEDIATION\nSI-3: MALICIOUS CODE PROTECTION\nSC-3: SECURITY FUNCTION ISOLATION"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "5.1\n5.2\n5.3\n5.4\n11.5"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}, {"id": "GS-10", "name": "", "description": "", "sheet": "Azure Security Benchmark v3", "resource_types": ["General"], "severity": "HIGH", "related_controls": [{"framework": "CIS Controls v7.1 ID(s)", "control": "5.1 - Establish Secure Configurations\n18.1 - Establish Secure Coding Practices\n18.8 - Establish a Process to Accept and Address Reports of Software Vulnerabilities"}, {"framework": "CIS Controls v8 ID(s)", "control": "4.1 - Establish and Maintain a Secure Configuration Process\n4.2 - Establish and Maintain a Secure Configuration Process for Network Infrastructure\n16.1 - Establish and Maintain a Secure Application Development Process\n16.2 - Establish and Maintain a Process to Accept and Address Software Vulnerabilities"}, {"framework": "NIST SP800-53 r4 ID(s)", "control": "SA-12: <PERSON><PERSON><PERSON><PERSON> CHAIN PROTECTION\nSA-15: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> PROCESS, STANDARD<PERSON>, AND TOOLS\nCM-1: CONFIGURATION MANAGEMENT POLICY AND PROCEDURES\nCM-2: BASELINE CONFIGURATION\nCM-6: CONFIGURATION SETTINGS\nAC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nAC-6: LEAST PRIVILEGE\nSA-11: DEVELOPER TESTING AND EVALUATION\nAU-6: AUDIT REVIEW, ANALYSIS, AND REPORTING\nAU-12: AUDIT GENERATION\nSI-4: INFORMATION SYSTEM MONITORING"}, {"framework": "PCI-DSS v3.2.1 ID(s)", "control": "2.2\n6.1\n6.2\n6.3\n6.5\n7.1\n10.1\n10.2\n10.3\n10.6\n12.2"}, {"framework": "Azure Policy Mapping", "control": "No applicable policy"}]}], "sheets": {"Readme": [{"Unnamed: 0": NaN, "Unnamed: 1": "      Azure Security Benchmark v3", "Unnamed: 2": NaN}, {"Unnamed: 0": NaN, "Unnamed: 1": "This spreadsheet is designed to provide you a spreadsheet(Excel) format of the Azure Security Benchmark(ASB) v3. For the web version of the content, please refer to ttps://docs.microsoft.com/en-us/security/benchmark/azure/overview\n\nDisclaimer: \na. The control mappings between ASB and industry benchmarks (such as NIST, CIS and PCI) only indicate that a specific Azure feature can be used to fully or partially address a control requirement defined in NIST, CIS or PCI. You should be aware that such implementation does not necessarily translate to the full compliance of the corresponding control in CIS, NIST or PCI.\nb. This document is developed as a reference and should not be used to define all means by which a customer can meet specific compliance requirements and regulations. Customers should seek legal support from their organization on approved customer implementations. ", "Unnamed: 2": NaN}, {"Unnamed: 0": NaN, "Unnamed: 1": NaN, "Unnamed: 2": NaN}, {"Unnamed: 0": NaN, "Unnamed: 1": NaN, "Unnamed: 2": NaN}, {"Unnamed: 0": NaN, "Unnamed: 1": "<PERSON>umn <PERSON>", "Unnamed: 2": "Overview"}, {"Unnamed: 0": NaN, "Unnamed: 1": "ASB ID", "Unnamed: 2": "The Azure Security Benchmark ID."}, {"Unnamed: 0": NaN, "Unnamed: 1": "ASB Control Domain", "Unnamed: 2": "The Azure Security Benchmark control domain."}, {"Unnamed: 0": NaN, "Unnamed: 1": "CIS Controls v8 ID(s)", "Unnamed: 2": "The CIS Controls v8 control(s) that correspond to the recommendation."}, {"Unnamed: 0": NaN, "Unnamed: 1": "CIS Controls v8 ID(s)", "Unnamed: 2": "The CIS Controls v8 control(s) that correspond to the recommendation."}, {"Unnamed: 0": NaN, "Unnamed: 1": "PCI-DSS v3.2.1 ID(s)", "Unnamed: 2": "The PCI-DSS v3.2.1 control(s) that correspond to the recommendation."}, {"Unnamed: 0": NaN, "Unnamed: 1": "NIST SP 800-53 r4 ID(s)", "Unnamed: 2": "The NIST SP 800-53 r4 (Moderate and High) control(s) that correspond to this recommendation."}, {"Unnamed: 0": NaN, "Unnamed: 1": "Security Principle", "Unnamed: 2": "The recommendation focused on the \"what\", explaining the control at the technology-agnostic level."}, {"Unnamed: 0": NaN, "Unnamed: 1": "Recommendation", "Unnamed: 2": "The Azure Security Benchmark control recommendation in summarized format."}, {"Unnamed: 0": NaN, "Unnamed: 1": "ASB Guideline ", "Unnamed: 2": "The recommendation focused on the \"how\", explaining the Azure technical features and implementation basics."}, {"Unnamed: 0": NaN, "Unnamed: 1": "Implementation and additional context", "Unnamed: 2": "The implementation details and other relevant context which links to the Azure service offering documentation articles."}, {"Unnamed: 0": NaN, "Unnamed: 1": "Customer Stakeholders", "Unnamed: 2": "The security functions at the customer organization who may be accountable, responsible, or consulted for the respective control. It may be different from organization to organization depending on your company’s security organization structure, and the roles and responsibilities you set up related to Azure security."}, {"Unnamed: 0": NaN, "Unnamed: 1": "Azure Policy Mapping", "Unnamed: 2": "The Azure Policy that can be used to audit the services that correspond to the ASB control."}, {"Unnamed: 0": NaN, "Unnamed: 1": "Azure Policy GUID", "Unnamed: 2": "The GUID(s) that correspond to the Azure Policy. "}], "Azure Security Benchmark v3": [{"ASB ID": "NS-1", "Control Domain": "Network Security", "CIS Controls v7.1 ID(s)": "9.2 - Ensure Only Approved Ports, Protocols and Services Are Running\n9.4 - Apply Host-Based Firewalls or Port Filtering\n12.3 - Deny Communications with Known Malicious IP Addresses\n12.4 - Deny Communication over Unauthorized Ports\n14.1 - Segment the Network Based on Sensitivity\n14.2 - Enable Firewall Filtering Between VLANs\n", "CIS Controls v8 ID(s)": "3.12 - Segment Data Processing and Storage Based on Sensitivity\n13.4 - Perform Traffic Filtering Between Network Segments\n4.4 - Implement and Manage a Firewall on Severs", "NIST SP800-53 r4 ID(s)": "AC-4: INFORMATION FLOW ENFORCEMENT\nSC-2: APPLICATION PARTITIONING\nSC-7: BOUNDARY PROTECTION", "PCI-DSS v3.2.1 ID(s)": "1.1\n1.2\n1.3", "Recommendation": "Establish network segmentation boundaries ", "Security Principle": "Ensure that your virtual network deployment aligns to your enterprise segmentation strategy defined in the GS-2 security control. Any workload that could incur higher risk for the organization should be in isolated virtual networks. \nExamples of high-risk workload include:\n- An application storing or processing highly sensitive data.\n- An external network-facing application accessible by the public or users outside of your organization.\n- An application using insecure architecture or containing vulnerabilities that cannot be easily remediated.\n\nTo enhance your enterprise segmentation strategy, restrict or monitor traffic between internal resources using network controls. For specific, well-defined applications (such as a 3-tier app), this can be a highly secure \"deny by default, permit by exception\" approach by restricting the ports, protocols, source, and destination IPs of the network traffic. If you have many applications and endpoints interacting with each other, blocking traffic may not scale well, and you may only be able to monitor traffic. ", "Azure Guidance": "Create a virtual network (VNet) as a fundamental segmentation approach in your Azure network, so resources such as VMs can be deployed into the VNet within a network boundary. To further segment the network, you can create subnets inside VNet for smaller sub-networks.\n\nUse network security groups (NSG) as a network layer control to restrict or monitor traffic by port, protocol, source IP address, or destination IP address.  \n\nYou can also use application security groups (ASGs) to simplify complex configuration. Instead of defining policy based on explicit IP addresses in network security groups, ASGs enable you to configure network security as a natural extension of an application's structure, allowing you to group virtual machines and define network security policies based on those groups. ", "Implementation and additional context": "Azure Virtual Network concepts and best practices:\nhttps://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices\n\nAdd, change, or delete a virtual network subnet:\nhttps://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet\n\nHow to create a network security group with security rules:\nhttps://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic\n\nUnderstand and use application security groups:\nhttps://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups", "Customer Security Stakeholders:": "Security architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management  \n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops", "Azure Policy Mapping": "Adaptive network hardening recommendations should be applied on internet facing virtual machines\nAll network ports should be restricted on network security groups associated to your virtual machine\nNon-internet-facing virtual machines should be protected with network security groups\nSubnets should be associated with a Network Security Group\nInternet-facing virtual machines should be protected with Network Security Groups", "Azure Policy GUID": "08e6af2d-db70-460a-bfe9-d5bd474ba9d6\n9daedab3-fb2d-461e-b861-71790eead4f6\nbb91dfba-c30d-4263-9add-9c2384e659a6\ne71308d3-144b-4262-b144-efdc3cc90517\nf6de0be7-9a8a-4b8a-b349-43cf02d22f7c"}, {"ASB ID": "NS-2", "Control Domain": "Network Security", "CIS Controls v7.1 ID(s)": "14.1 - Segment the Network Based on Sensitivity", "CIS Controls v8 ID(s)": "3.12 - Segment Data Processing and Storage Based on Sensitivity\n4.4 - Implement and Manage a Firewall on Servers\n", "NIST SP800-53 r4 ID(s)": "AC-4: INFORMATION FLOW ENFORCEMENT\nSC-2: APPLICATION PARTITIONING\nSC-7: BOUNDARY PROTECTION", "PCI-DSS v3.2.1 ID(s)": "1.1\n1.2\n1.3\n", "Recommendation": "Secure cloud services with network controls", "Security Principle": "Secure cloud services by establishing a private access point for the resources. You should also disable or restrict access from public network when possible. ", "Azure Guidance": "Deploy private endpoints for all Azure resources that support the Private Link feature, to establish a private access point for the resources. You should also disable or restrict public network access to services where feasible. \n\nFor certain services, you also have the  option to deploy VNet integration for the service where you can restrict the VNET to establish a private access point for the service.", "Implementation and additional context": "Understand Azure Private Link: \nhttps://docs.microsoft.com/azure/private-link/private-link-overview", "Customer Security Stakeholders:": "Security architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management \n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops", "Azure Policy Mapping": "Cognitive Services accounts should restrict network access\nPrivate endpoint should be enabled for PostgreSQL servers\nCognitive Services accounts should disable public network access\nPrivate endpoint should be enabled for MariaDB servers\nAuthorized IP ranges should be defined on Kubernetes Services\nPublic network access on Azure SQL Database should be disabled\nVM Image Builder templates should use private link\nStorage accounts should restrict network access using virtual network rules\nStorage accounts should restrict network access\nAzure Machine Learning workspaces should use private link\nAzure Event Grid topics should use private link\n[Preview]: Storage account public access should be disallowed\nAzure SignalR Service should use private link\n[Preview]: Azure Key Vault should disable public network access\n[Preview]: Private endpoint should be configured for Key Vault\nStorage accounts should use private link\nPrivate endpoint should be enabled for MySQL servers\nPrivate endpoint connections on Azure SQL Database should be enabled\nAzure Cache for Redis should reside within a virtual network\nAzure Cosmos DB accounts should have firewall rules\nAzure Event Grid domains should use private link\nAzure Spring Cloud should use network injection\nPublic network access should be disabled for PostgreSQL servers\nApp Configuration should use private link\nContainer registries should not allow unrestricted network access\nPublic network access should be disabled for MySQL servers\nContainer registries should use private link\nAPI Management services should use a virtual network\nPublic network access should be disabled for MariaDB servers", "Azure Policy GUID": "037eea7a-bd0a-46c5-9a66-03aea78705d3\n0564d078-92f5-4f97-8398-b9f58a51f70b\n0725b4dd-7e76-479c-a735-68e7ee23d5ca\n0a1302fb-a631-4106-9753-f3d494733990\n0e246bcf-5f6f-4f87-bc6f-775d4712c7ea\n1b8ca024-1d5c-4dec-8995-b1a932b41780\n2154edb9-244f-4741-9970-660785bccdaa\n2a1a9cdf-e04d-429a-8416-3bfb72a1b26f\n34c877ad-507e-4c82-993e-3452a6e0ad3c\n40cec1dd-a100-4920-b15b-3024fe8901ab\n4b90e17e-8448-49db-875e-bd83fb6f804f\n4fa4b6c0-31ca-4c0d-b10d-24b96f62a751\n53503636-bcc9-4748-9663-5348217f160f\n55615ac9-af46-4a59-874e-391cc3dfb490\n5f0bc445-**************-011aa2b46147\n6edd7eda-6dd8-40f7-810d-67160c639cd9\n7595c971-233d-4bcf-bd18-596129188c49\n7698e800-9299-47a6-b3b6-5a0fee576eed\n7d092e0a-7acd-40d2-a975-dca21cae48c4\n862e97cf-49fc-4a5c-9de4-40d4e2e7c8eb\n9830b652-8523-49cc-b1b3-e17dce1127ca\naf35e2a4-ef96-44e7-a9ae-853dd97032c4\nb52376f7-9612-48a1-81cd-1ffe4b61032c\nca610c1d-041c-4332-9d88-7ed3094967c7\nd0793b48-0edc-4296-a390-4c75d1bdfd71\nd9844e8a-1437-4aeb-a32c-0c992f056095\ne8eef0a8-67cf-4eb4-9386-14b0e78733d4\nef619a2c-cc4d-4d03-b2ba-8c94a834d85b\nfdccbe47-f3e3-4213-ad5d-ea459b2fa077"}, {"ASB ID": "NS-3", "Control Domain": "Network Security", "CIS Controls v7.1 ID(s)": "9.2 - Ensure Only Approved Ports, Protocols and Services Are Running\n9.4 - Apply Host-Based Firewalls or Port Filtering\n12.3 - Deny Communications with Known Malicious IP Addresses\n12.4 - Deny Communication over Unauthorized Ports\n14.1 - Segment the Network Based on Sensitivity\n14.2 - Enable Firewall Filtering Between VLANs\n\n", "CIS Controls v8 ID(s)": "4.4 - Implement and Manage a Firewall on Servers\n4.8 - Uninstall or Disable Unnecessary Services on Enterprise Assets and Software\n13.10 Perform Application Layer Filtering", "NIST SP800-53 r4 ID(s)": "AC-4: INFORMATION FLOW ENFORCEMENT\nSC-7: BOUNDARY PROTECTION\nCM-7: LEAST FUNCTIONALITY", "PCI-DSS v3.2.1 ID(s)": "1.1\n1.2\n1.3", "Recommendation": "Deploy firewall at the edge of enterprise network", "Security Principle": "Deploy a firewall to perform advanced filtering on network traffic to and from external networks. You can also use firewalls between internal segments to support a segmentation strategy. If required, use custom routes for your subnet to override the system route when you need to force the network traffic to go through a network appliance for security control purpose.\n\nAt a minimum, block known bad IP addresses and high-risk protocols, such as remote management (for example, RDP and SSH) and intranet protocols (for example, SMB and Kerberos).    ", "Azure Guidance": "Use Azure Firewall to provide fully stateful application layer traffic restriction (such as URL filtering) and/or central management over a large number of enterprise segments or spokes (in a hub/spoke topology).  \n\nIf you have a complex network topology, such as a hub/spoke setup, you may need to create user-defined routes (UDR) to ensure the traffic goes through the desired route. For example, you have option to use an UDR to redirect egress internet traffic through a specific Azure Firewall or a network virtual appliance.", "Implementation and additional context": "How to deploy Azure Firewall: \nhttps://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal\n\nVirtual network traffic routing:\nhttps://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview", "Customer Security Stakeholders:": "Security architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management  \n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops", "Azure Policy Mapping": "Management ports should be closed on your virtual machines\nManagement ports of virtual machines should be protected with just-in-time network access control\nIP Forwarding on your virtual machine should be disabled\n[Preview]: All Internet traffic should be routed via your deployed Azure Firewall", "Azure Policy GUID": "22730e10-96f6-4aac-ad84-9383d35b5917\nb0f33259-77d7-4c9e-aac6-3aabcfae693c\nbd352bd5-2853-4985-bf0d-73806b4a5744\nfc5e4038-4584-4632-8c85-c0448d374b2c"}, {"ASB ID": "NS-4", "Control Domain": "Network Security", "CIS Controls v7.1 ID(s)": "12.6 - Deploy Network-Based IDS Sensors\n12.7 - Deploy Network-Based Intrusion Prevention Systems", "CIS Controls v8 ID(s)": "13.2 Deploy a Host-Based Intrusion Detection Solution\n13.3 - Deploy a Network Intrusion Detection Solution\n13.7 Deploy a Host-Based Intrusion Prevention Solution\n13.8 - Deploy a Network Intrusion Prevention Solution\n", "NIST SP800-53 r4 ID(s)": "SC-7: BOUNDARY PROTECTION\nSI-4: INFORMATION SYSTEM MONITORING", "PCI-DSS v3.2.1 ID(s)": "11.4\n\n", "Recommendation": "Deploy intrusion detection/intrusion prevention systems (IDS/IPS)", "Security Principle": "Use network intrusion detection and intrusion prevention systems (IDS/IPS) to inspect the network and payload traffic to or from your workload. Ensure that IDS/IPS is always tuned to provide high-quality alerts to your SIEM solution.\n\nFor more in-depth host level detection and prevention capability, use host-based IDS/IPS or a host-based endpoint detection and response (EDR) solution in conjunction with the network IDS/IPS.", "Azure Guidance": "Use Azure Firewall’s IDPS capability on your network to alert on and/or block traffic to and from known malicious IP addresses and domains. \n\nFor more in-depth host level detection and prevention capability, deploy host-based IDS/IPS or a host-based endpoint detection and response (EDR) solution, such as Microsoft Defender for Endpoint, at the VM level in conjunction with the network IDS/IPS. ", "Implementation and additional context": "Azure Firewall IDPS:\nhttps://docs.microsoft.com/azure/firewall/premium-features#idps\n\nMicrosoft Defender for Endpoint capability: https://docs.microsoft.com/windows/security/threat-protection/microsoft-defender-atp/overview-endpoint-detection-response", "Customer Security Stakeholders:": "Security architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management \n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "NS-5", "Control Domain": "Network Security", "CIS Controls v7.1 ID(s)": "9.5 - Implement Application Firewalls\n12.3 - Deny Communications with Known Malicious IP Addresses\n\n", "CIS Controls v8 ID(s)": "13.10 - Perform Application Layer Filtering", "NIST SP800-53 r4 ID(s)": "SC-5: DE<PERSON><PERSON> OF SERVICE PROTECTION\nSC-7: BOUNDARY PROTECTION", "PCI-DSS v3.2.1 ID(s)": "1.1\n1.2\n1.3\n6.6", "Recommendation": "Deploy DDOS protection", "Security Principle": "Deploy distributed denial of service (DDoS) protection to protect your network and applications from attacks.", "Azure Guidance": "Enable DDoS standard protection plan on your VNet to protect resources that are exposed to the public networks.", "Implementation and additional context": "Manage Azure DDoS Protection Standard using the Azure portal:\nhttps://docs.microsoft.com/azure/virtual-network/manage-ddos-protection", "Customer Security Stakeholders:": "Security architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management \n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops", "Azure Policy Mapping": "Azure DDoS Protection Standard should be enabled", "Azure Policy GUID": "a7aca53f-2ed4-4466-a25e-0b45ade68efd"}, {"ASB ID": "NS-6", "Control Domain": "Network Security", "CIS Controls v7.1 ID(s)": "9.5 - Implement Application Firewalls\n12.3 - Deny Communications with Known Malicious IP Addresses\n12.9 - Deploy Application Layer Filtering Proxy Server\n18.10 - Deploy Web Application Firewalls (WAFs)\n\n", "CIS Controls v8 ID(s)": "13.10 - Perform Application Layer Filtering", "NIST SP800-53 r4 ID(s)": "SC-7: BOUNDARY PROTECTION", "PCI-DSS v3.2.1 ID(s)": "1.1\n1.2\n1.3\n6.6", "Recommendation": "Deploy web application firewall", "Security Principle": "Deploy a web application firewall (WAF) and configure the appropriate rules to protect your web applications and APIs from application-specific attacks. ", "Azure Guidance": "Use web application firewall (WAF) capabilities in Azure Application Gateway, Azure Front Door, and Azure Content Delivery Network (CDN) to protect your applications, services and APIs against application layer attacks at the edge of your network. Set your WAF in \"detection\" or \"prevention mode,\" depending on your needs and threat landscape. Choose a built-in ruleset, such as OWASP Top 10 vulnerabilities, and tune it to your application. ", "Implementation and additional context": "How to deploy Azure WAF: \nhttps://docs.microsoft.com/azure/web-application-firewall/overview\n\n", "Customer Security Stakeholders:": "Security architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management \n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops", "Azure Policy Mapping": "Web Application Firewall (WAF) should be enabled for Azure Front Door Service service\nWeb Application Firewall (WAF) should be enabled for Application Gateway", "Azure Policy GUID": "055aa869-bc98-4af8-bafc-23f1ab6ffe2c\n564feb30-bf6a-4854-b4bb-0d2d2d1e6c66"}, {"ASB ID": "NS-7", "Control Domain": "Network Security", "CIS Controls v7.1 ID(s)": "9.2 - Ensure Only Approved Ports, Protocols and Services Are Running\n\n\n\n\n\n", "CIS Controls v8 ID(s)": "4.4 - Implement and Manage a Firewall on Severs\n4.8 - Uninstall or Disable Unnecessary Services on Enterprise Assets and Software\n", "NIST SP800-53 r4 ID(s)": "AC-4: INFORMATION FLOW ENFORCEMENT\nSC-2: APPLICATION PARTITIONING\nSC-7: BOUNDARY PROTECTION", "PCI-DSS v3.2.1 ID(s)": "1.1\n1.2\n1.3\n", "Recommendation": "Simplify network security configuration", "Security Principle": "When managing a complex network environment, use tools to simplify, centralize and enhance the network security management.", "Azure Guidance": "Use the following features to simplify the implementation and management of the NSG and Azure Firewall rules: \n- Use Microsoft Defender for Cloud Adaptive Network Hardening to recommend NSG hardening rules that further limit ports, protocols and source IPs based on threat intelligence and traffic analysis result. \n- Use Azure Firewall Manager to centralize the firewall policy and route management of the virtual network. To simplify the firewall rules and network security groups implementation, you can also use the Azure Firewall Manager ARM (Azure Resource Manager) template.", "Implementation and additional context": "Adaptive Network Hardening in Microsoft Defender for Cloud:\nhttps://docs.microsoft.com/azure/security-center/security-center-adaptive-network-hardening\n\nAzure Firewall Manager: \nhttps://docs.microsoft.com/azure/firewall-manager/overview\n\nCreate an Azure Firewall and a firewall policy - ARM template\nhttps://docs.microsoft.com/azure/firewall-manager/quick-firewall-policy", "Customer Security Stakeholders:": "Security architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management \n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops", "Azure Policy Mapping": "Adaptive network hardening recommendations should be applied on internet facing virtual machines", "Azure Policy GUID": "08e6af2d-db70-460a-bfe9-d5bd474ba9d6"}, {"ASB ID": "NS-8", "Control Domain": "Network Security", "CIS Controls v7.1 ID(s)": "9.2 - Ensure Only Approved Ports, Protocols and Services Are Running\n\n\n\n\n\n", "CIS Controls v8 ID(s)": "4.4 - Implement and Manage a Firewall on Severs\n4.8 - Uninstall or Disable Unnecessary Services on Enterprise Assets and Software\n", "NIST SP800-53 r4 ID(s)": "CM-2: <PERSON><PERSON><PERSON><PERSON>E CONFIGURATION\nCM-6: CONFIGURATION SETTINGS\nCM-7: LEAST FUNCTIONALITY", "PCI-DSS v3.2.1 ID(s)": "4.1\nA2.1\nA2.2\nA2.3\n\n", "Recommendation": "Detect and disable insecure services and  protocols", "Security Principle": "Detect and disable insecure services and protocols at the OS, application, or software package layer. Deploy compensating controls if disabling insecure services and protocols are not possible.", "Azure Guidance": "Use Azure Sentinel’s built-in Insecure Protocol Workbook to discover the use of insecure services and protocols such as SSL/TLSv1, SSHv1, SMBv1, LM/NTLMv1, wDigest, Unsigned LDAP Binds, and weak ciphers in Kerberos. Disable insecure services and protocols that do not meet the appropriate security standard. \n\nNote: If disabling insecure services or protocols is not possible, use compensating controls such as blocking access to the resources through network security group, Azure Firewall, or Azure Web Application Firewall to reduce the attack surface.", "Implementation and additional context": "Azure Sentinel insecure protocols workbook: \nhttps://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks", "Customer Security Stakeholders:": "Security architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management  \n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops", "Azure Policy Mapping": "Latest TLS version should be used in your API App\nLatest TLS version should be used in your Web App\nLatest TLS version should be used in your Function App", "Azure Policy GUID": "8cb6aa8b-9e41-4f4e-aa25-089a7ac2581e\nf0e6e85b-9b9f-4a4b-b67b-f730d42f1b0b\nf9d614c5-c173-4d56-95a7-b4437057d193"}, {"ASB ID": "NS-9", "Control Domain": "Network Security", "CIS Controls v7.1 ID(s)": NaN, "CIS Controls v8 ID(s)": "12.7 - Ensure Remote Devices Utilize a VPN and are Connecting to \nan Enterprise’s AAA Infrastructure", "NIST SP800-53 r4 ID(s)": "CA-3: SYSTEM INTERCONNECTIONS\nAC-17: REMOTE ACCESS\nAC-4: INFORMATION FLOW ENFORCEMENT\n", "PCI-DSS v3.2.1 ID(s)": NaN, "Recommendation": "Connect on-premises or cloud network privately \n", "Security Principle": "Use private connections for secure communication between different networks, such as cloud service provider datacenters and on-premises infrastructure in a colocation environment.", "Azure Guidance": "For lightweight connectivity between site-to-site or point-to-site, use Azure virtual private network (VPN) to create a secure connection between your on-premises site or end-user device to the Azure virtual network. \n\nFor enterprise-level high performance connection, use Azure ExpressRoute (or Virtual WAN) to connect Azure datacenters and on-premises infrastructure in a co-location environment.\n\nWhen connecting two or more Azure virtual networks together, use virtual network peering. Network traffic between peered virtual networks is private and is kept on the Azure backbone network. ", "Implementation and additional context": "Azure VPN overview: \nhttps://docs.microsoft.com/azure/vpn-gateway/vpn-gateway-about-vpngateways\n\nWhat are the ExpressRoute connectivity models: \nhttps://docs.microsoft.com/azure/expressroute/expressroute-connectivity-models\n\nVirtual network peering: \nhttps://docs.microsoft.com/azure/virtual-network/virtual-network-peering-overview \n", "Customer Security Stakeholders:": "Security architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management \n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "NS-10", "Control Domain": "Network Security", "CIS Controls v7.1 ID(s)": "7.7 - Use of DNS Filtering Services", "CIS Controls v8 ID(s)": "4.9 - Configure Trusted DNS Servers on Enterprise Assets\n9.2 - Use DNS Filtering Services", "NIST SP800-53 r4 ID(s)": "SC-20: SECURE NAME / ADDRESS RESOLUTION SERVICE (AUTHORITATIVE SOURCE)\nSC-21: SECURE NAME / ADDRESS RESOLUTION SERVICE (RECURSIVE OR CACHING RESOLVER)", "PCI-DSS v3.2.1 ID(s)": NaN, "Recommendation": "Ensure Domain Name System (DNS) security", "Security Principle": "Ensure that Domain Name System (DNS) security configuration protects against known risks:\n- Use trusted authoritative and recursive DNS services across your cloud environment to ensure the client (such as operating systems and applications) receive the correct resolution result. \n- Separate the public and private DNS resolution so the DNS resolution process for the private network can be isolated from the public network.\n- Ensure your DNS security strategy also includes mitigations against common attacks, such as dangling DNS, DNS amplifications attacks, DNS poisoning and spoofing, and so on.", "Azure Guidance": "Use Azure recursive DNS or a trusted external DNS server in your workload recursive DNS setup, such as in VM's operating system or in the application. \n\nUse Azure Private DNS for private DNS zone setup where the DNS resolution process does not leave the virtual network. Use a custom DNS to restrict the DNS resolution which only allows the trusted resolution to your client.\n\nUse Azure Defender for DNS for the advanced protection against the following security threats to your workload or your DNS service:\n- Data exfiltration from your Azure resources using DNS tunneling\n- Malware communicating with command-and-control server\n- Communication with malicious domains as phishing and crypto mining\n- DNS attacks in communication with malicious DNS resolvers\n\nYou can also use Azure Defender for App Service to detect dangling DNS records if you decommission an App Service website without removing its custom domain from your DNS registrar.", "Implementation and additional context": "Azure DNS overview: \nhttps://docs.microsoft.com/azure/dns/dns-overview \n\nSecure Domain Name System (DNS) Deployment Guide: \nhttps://csrc.nist.gov/publications/detail/sp/800-81/2/final \n\nAzure Private DNS:\nhttps://docs.microsoft.com/azure/dns/private-dns-overview \n\nAzure Defender for DNS:\nhttps://docs.microsoft.com/azure/security-center/defender-for-dns-introduction \n\nPrevent dangling DNS entries and avoid subdomain takeover: \nhttps://docs.microsoft.com/azure/security/fundamentals/subdomain-takeover ", "Customer Security Stakeholders:": "Security architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management \n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops", "Azure Policy Mapping": "[Preview]: Azure Defender for DNS should be enabled", "Azure Policy GUID": "bdc59948-5574-49b3-bb91-76b7c986428d"}, {"ASB ID": "DP-1", "Control Domain": "Data Protection", "CIS Controls v7.1 ID(s)": "13.1 - \tMaintain an Inventory of Sensitive Information\n14.5 - Utilize an Active Discovery Tool to Identify Sensitive Data", "CIS Controls v8 ID(s)": "3.2 - Establish and Maintain a Data Inventory\n3.7 - Establish and Maintain a Data Classification Scheme\n3.13 - Deploy a Data Loss Prevention Solution", "NIST SP800-53 r4 ID(s)": "RA-2: SECURITY CATEGORIZATION\nSC-28: PROTECTION OF INFORMATION AT REST", "PCI-DSS v3.2.1 ID(s)": "A3.2", "Recommendation": "Discover, classify, and label sensitive data\n\n", "Security Principle": "Establish and maintain an inventory of the sensitive data, based on the defined sensitive data scope. Use tools to discover, classify and label the in- scope sensitive data.", "Azure Guidance": "Use tools such as Azure Purview, Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan, classify and label the sensitive data that reside in the Azure, on-premises, Microsoft 365, and other locations.", "Implementation and additional context": "Data classification overview: \nhttps://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification\n\nLabel your sensitive data using Azure Purview:\nhttps://docs.microsoft.com/azure/purview/create-sensitivity-label\n\nTag sensitive information using Azure Information Protection: \nhttps://docs.microsoft.com/azure/information-protection/what-is-information-protection\n\nHow to implement Azure SQL Data Discovery: \nhttps://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification\n\nAzure Purview data sources:\nhttps://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources", "Customer Security Stakeholders:": "Application Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nData Security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-data-security\n\nInfrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-infrastructure-endpoint", "Azure Policy Mapping": "[Preview]: Sensitive data in your SQL databases should be classified", "Azure Policy GUID": "cc9835f2-9f6b-4cc8-ab4a-f8ef615eb349"}, {"ASB ID": "DP-2", "Control Domain": "Data Protection", "CIS Controls v7.1 ID(s)": "13.3 - Monitor and Block Unauthorized Network Traffic\n14.7 - Enforce Access Control to Data through Automated Tools", "CIS Controls v8 ID(s)": "3.13 - Deploy a Data Loss Prevention Solution", "NIST SP800-53 r4 ID(s)": "AC-4: INFORMATION FLOW ENFORCEMENT\nSI-4: INFORMATION SYSTEM MONITORING", "PCI-DSS v3.2.1 ID(s)": "A3.2", "Recommendation": "Monitor anomalies and threats targeting sensitive data", "Security Principle": "Monitor for anomalies around sensitive data, such as unauthorized transfer of data to locations outside of enterprise visibility and control. This typically involves monitoring for anomalous activities (large or unusual transfers) that could indicate unauthorized data exfiltration. ", "Azure Guidance": "Use Azure Information protection (AIP) to monitor the data that has been classified and labeled.\n\nUse Azure Defender for Storage, Azure Defender for SQL and Azure Cosmos DB to alert on anomalous transfer of information that might indicate unauthorized transfers of sensitive data information. \n\nNote: If required for compliance of data loss prevention (DLP), you can use a host based DLP solution from Azure Marketplace or a  Microsoft 365 DLP solution to enforce detective and/or preventative controls to prevent data exfiltration.", "Implementation and additional context": "Enable Azure Defender for SQL: \nhttps://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql \n\nEnable Azure Defender for Storage: \nhttps://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center\n\nAzure Purview data insights:\nhttps://docs.microsoft.com/azure/purview/concept-insights", "Customer Security Stakeholders:": "Security operations: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security\n\nApplication security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nInfrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-infrastructure-endpoint", "Azure Policy Mapping": "Azure Defender for open-source relational databases should be enabled\nAzure Defender for Storage should be enabled\nAzure Defender for SQL servers on machines should be enabled\nAzure Defender for Azure SQL Database servers should be enabled\nAzure Defender for SQL should be enabled for unprotected SQL Managed Instances\nAzure Defender for SQL should be enabled for unprotected SQL Managed Instances", "Azure Policy GUID": "0a9fbe0d-c5c4-4da8-87d8-f4fd77338835\n308fbb08-4ab8-4e67-9b29-592e93fb94fa\n6581d072-105e-4418-827f-bd446d56421b\n7fe3b40f-802b-4cdd-8bd4-fd799c948cc2\nabfb7388-5bf4-4ad7-ba99-2cd2f41cebb9\nabfb7388-5bf4-4ad7-ba99-2cd2f41cebb9"}, {"ASB ID": "DP-3", "Control Domain": "Data Protection", "CIS Controls v7.1 ID(s)": "14.4 - Encrypt All Sensitive Information in Transit", "CIS Controls v8 ID(s)": "3.10 - Encrypt Sensitive Data In Transit", "NIST SP800-53 r4 ID(s)": "SC-8: TRANS<PERSON><PERSON>ION CONFIDENTIALITY AND INTEGRITY", "PCI-DSS v3.2.1 ID(s)": "3.5\n3.6\n4.1", "Recommendation": "Encrypt sensitive data in transit", "Security Principle": "Protect the data in transit against 'out of band' attacks (such as traffic capture) using encryption to ensure that attackers cannot easily read or modify the data. \n\nSet the network boundary and service scope where data in transit encryption is mandatory inside and outside of the network. While this is optional for traffic on private networks, this is critical for traffic on external and public networks. ", "Azure Guidance": "Enforce secure transfer in services such as Azure Storage, where a native data in transit encryption feature is built in. \n\nEnforce HTTPS for workload web application and services by ensuring that any clients connecting to your Azure resources use transportation layer security (TLS) v1.2 or later. For remote management of VMs, use SSH (for Linux) or RDP/TLS (for Windows) instead of an unencrypted protocol. \n\nNote: Data in transit encryption is enabled for all Azure traffic traveling between Azure datacenters. TLS v1.2 or later is enabled on most Azure PaaS services by default.", "Implementation and additional context": "Double encryption for Azure data in transit: \nhttps://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit\n\nUnderstand encryption in transit with Azure: \nhttps://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit\n\nInformation on TLS Security: \nhttps://docs.microsoft.com/security/engineering/solving-tls1-problem\n\nEnforce secure transfer in Azure storage:\nhttps://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account", "Customer Security Stakeholders:": "Security architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nInfrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-infrastructure-endpoint\n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nData Security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-data-security", "Azure Policy Mapping": "Kubernetes clusters should be accessible only over HTTPS\nOnly secure connections to your Azure Cache for Redis should be enabled\nFTPS only should be required in your Function App\nSecure transfer to storage accounts should be enabled\nFTPS should be required in your Web App\nWindows web servers should be configured to use secure communication protocols\nFunction App should only be accessible over HTTPS\nLatest TLS version should be used in your API App\nFTPS only should be required in your API App\nWeb Application should only be accessible over HTTPS\nAPI App should only be accessible over HTTPS\nEnforce SSL connection should be enabled for PostgreSQL database servers\nEnforce SSL connection should be enabled for MySQL database servers\nLatest TLS version should be used in your Web App\nLatest TLS version should be used in your Function App", "Azure Policy GUID": "1a5b4dca-0b6f-4cf5-907c-56316bc1bf3d\n22bee202-a82f-4305-9a2a-6d7f44d4dedb\n399b2637-a50f-4f95-96f8-3a145476eb15\n404c3081-a854-4457-ae30-26a93ef643f9\n4d24b6d4-5e53-4a4f-a7f4-618fa573ee4b\n5752e6d6-1206-46d8-8ab1-ecc2f71a8112\n6d555dd1-86f2-4f1c-8ed7-5abae7c6cbab\n8cb6aa8b-9e41-4f4e-aa25-089a7ac2581e\n9a1b8c48-453a-4044-86c3-d8bfd823e4f5\na4af4a39-4135-47fb-b175-47fbdf85311d\nb7ddfbdc-1260-477d-91fd-98bd9be789a6\nd158790f-bfb0-486c-8631-2dc6b4e8e6af\ne802a67a-daf5-4436-9ea6-f6d821dd0c5d\nf0e6e85b-9b9f-4a4b-b67b-f730d42f1b0b\nf9d614c5-c173-4d56-95a7-b4437057d193"}, {"ASB ID": "DP-4", "Control Domain": "Data Protection", "CIS Controls v7.1 ID(s)": "14.8 - Encrypt Sensitive Information at Rest", "CIS Controls v8 ID(s)": "3.11 - Encrypt Sensitive Data at Rest", "NIST SP800-53 r4 ID(s)": "SC-28: PROTECTION OF INFORMATION AT REST", "PCI-DSS v3.2.1 ID(s)": "3.4\n3.5", "Recommendation": "Enable data at rest encryption by default  ", "Security Principle": "To complement access controls, data at rest should be protected against 'out of band' attacks (such as accessing underlying storage) using encryption. This helps ensure that attackers cannot easily read or modify the data.", "Azure Guidance": "Many Azure services have data at rest encryption enabled by default at the infrastructure layer using a service-managed key.\n\nWhere technically feasible and not enabled by default, you can enable data at rest encryption in the Azure services, or in your VMs for storage level, file level, or database level encryption.", "Implementation and additional context": "Understand encryption at rest in Azure: https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services\n\nData at rest double encryption in Azure: https://docs.microsoft.com/azure/security/fundamentals/encryption-models\n\nEncryption model and key management table: \nhttps://docs.microsoft.com/azure/security/fundamentals/encryption-models", "Customer Security Stakeholders:": "Security architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nInfrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-infrastructure-endpoint\n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nData Security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-data-security", "Azure Policy Mapping": "Virtual machines should encrypt temp disks, caches, and data flows between Compute and Storage resources\nTransparent Data Encryption on SQL databases should be enabled\nAutomation account variables should be encrypted\nService Fabric clusters should have the ClusterProtectionLevel property set to EncryptAndSign", "Azure Policy GUID": "0961003e-5a0a-4549-abde-af6a37f2724d\n17k78e20-9358-41c9-923c-fb736d382a12\n3657f5a0-770e-44a3-b44e-9431ba1e9735\n617c02be-7f02-4efd-8836-3180d47b6c68"}, {"ASB ID": "DP-5", "Control Domain": "Data Protection", "CIS Controls v7.1 ID(s)": "14.8 - Encrypt Sensitive Information at Rest", "CIS Controls v8 ID(s)": "3.11 - Encrypt Sensitive Data at Rest", "NIST SP800-53 r4 ID(s)": "SC-12: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> KEY ESTAB<PERSON><PERSON><PERSON>MENT AND MANAGEMENT\nSC-28: PROTECTION OF INFORMATION AT REST", "PCI-DSS v3.2.1 ID(s)": "3.4\n3.5\n3.6", "Recommendation": "Use customer-managed key option in data at rest encryption when required", "Security Principle": "If required for regulatory compliance, define the use case and service scope where customer-managed key option is needed. Enable and implement data at rest encryption using customer-managed key in services.\n", "Azure Guidance": "Azure also provides encryption option using keys managed by yourself (customer-managed keys) for certain services. However, using customer-managed key option requires additional operational efforts to manage the key lifecycle. This may include encryption key generation, rotation, revoke and access control, etc. ", "Implementation and additional context": "Encryption model and key management table: \nhttps://docs.microsoft.com/azure/security/fundamentals/encryption-models\n\nServices that support encryption using customer-managed key: https://docs.microsoft.com/azure/security/fundamentals/encryption-models#supporting-services\n\nHow to configure customer managed encryption keys in Azure Storage: https://docs.microsoft.com/azure/storage/common/storage-encryption-keys-portal", "Customer Security Stakeholders:": "Security architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nInfrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-infrastructure-endpoint\n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nData Security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-data-security", "Azure Policy Mapping": "SQL managed instances should use customer-managed keys to encrypt data at rest\nSQL servers should use customer-managed keys to encrypt data at rest\nPostgreSQL servers should use customer-managed keys to encrypt data at rest\nAzure Cosmos DB accounts should use customer-managed keys to encrypt data at rest\nContainer registries should be encrypted with a customer-managed key\nCognitive Services accounts should enable data encryption with a customer-managed key\nStorage accounts should use customer-managed key for encryption\nMySQL servers should use customer-managed keys to encrypt data at rest\nAzure Machine Learning workspaces should be encrypted with a customer-managed key", "Azure Policy GUID": "048248b0-55cd-46da-b1ff-39efd52db260\n0d134df8-db83-46fb-ad72-fe0c9428c8dd\n18adea5e-f416-4d0f-8aa8-d24321e3e274\n1f905d99-2ab7-462c-a6b0-f709acca6c8f\n5b9159ae-1701-4a6f-9a7a-aa9c8ddd0580\n67121cc7-ff39-4ab8-b7e3-95b84dab487d\n6fac406b-40ca-413b-bf8e-0bf964659c25\n83cef61d-dbd1-4b20-a4fc-5fbc7da10833\nba769a63-b8cc-4b2d-abf6-ac33c7204be8"}, {"ASB ID": "DP-6", "Control Domain": "Data Protection", "CIS Controls v7.1 ID(s)": NaN, "CIS Controls v8 ID(s)": NaN, "NIST SP800-53 r4 ID(s)": "IA-5: AUTHEN<PERSON><PERSON><PERSON><PERSON> MANAGEMENT\nSC-12: CRYPTO<PERSON><PERSON><PERSON><PERSON> KEY ESTABLISHMENT AND MANAGEMENT\nSC-28: PROTECTION OF INFORMATION AT REST", "PCI-DSS v3.2.1 ID(s)": 3.6, "Recommendation": "Use a secure key management process", "Security Principle": "Document and implement an enterprise cryptographic key management standard, processes, and procedures to control your key lifecycle. When there is a need to use customer-managed key in the services, use a secured key vault service for key generation, distribution, and storage. Rotate and revoke your keys based on the defined schedule and when there is a key retirement or compromise.", "Azure Guidance": "Use Azure Key Vault to create and control your encryption keys life cycle, including key generation, distribution, and storage. Rotate and revoke your keys in Azure Key Vault and your service based on the defined schedule and when there is a key retirement or compromise.\n\nWhen there is a need to use customer-managed key (CMK) in the workload services or applications, ensure you follow the best practices:\n- Use a key hierarchy to generate a separate data encryption key (DEK) with your key encryption key (KEK) in your key vault. \n- Ensure keys are registered with Azure Key Vault and implement via key IDs in each service or application.\n\nIf you need to bring your own key (BYOK) to the services (i.e., importing HSM-protected keys from your on-premises HSMs into Azure Key Vault), follow the recommended guideline to perform the key generation and key transfer. \n\nNote: Refer to the below for the FIPS 140-2 level for Azure Key Vault types and FIPS compliance level. \n- Software-protected keys in vaults (Premium & Standard SKUs):  FIPS 140-2 Level 1\n- HSM-protected keys in vaults (Premium SKU): FIPS 140-2 Level 2\n- HSM-protected keys in Managed HSM: FIPS 140-2 Level 3", "Implementation and additional context": "Azure Key Vault overview: \nhttps://docs.microsoft.com/azure/key-vault/general/overview\n\nAzure data encryption at rest--Key Hierarchy:\nhttps://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#key-hierarchy\n\nBYOK(Bring Your Own Key) specification:\nhttps://docs.microsoft.com/azure/key-vault/keys/byok-specification", "Customer Security Stakeholders:": "Identity and key management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys\n\nSecurity architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nData Security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-data-security", "Azure Policy Mapping": "Key Vault keys should have an expiration date\nKey Vault secrets should have an expiration date", "Azure Policy GUID": "152b15f7-8e1f-4c1f-ab71-8c010ba5dbc0\n98728c90-32c7-4049-8429-847dc0f4fe37"}, {"ASB ID": "DP-7", "Control Domain": "Data Protection", "CIS Controls v7.1 ID(s)": NaN, "CIS Controls v8 ID(s)": NaN, "NIST SP800-53 r4 ID(s)": "IA-5: AUTH<PERSON><PERSON><PERSON><PERSON><PERSON> MANAGEMENT\nSC-12: CRYPTO<PERSON><PERSON><PERSON><PERSON> KEY ESTABLISHMENT AND MANAGEMENT\nSC-17: <PERSON><PERSON><PERSON><PERSON> KEY INFRASTRUCTURE CERTIFICATES", "PCI-DSS v3.2.1 ID(s)": 3.6, "Recommendation": "Use a secure certificate management process", "Security Principle": "Document and implement an enterprise certificate management standard, processes and procedures which includes the certificate lifecycle control, and certificate policies (if a public key infrastructure is needed).\n\nEnsure certificates used by the critical services in your organization are inventoried, tracked, monitored, and renewed timely using automated mechanism to avoid service disruption.\n", "Azure Guidance": "Use Azure Key Vault to create and control the certificate lifecycle, including creation/import, rotation, revocation, storage, and purge of the certificate. Ensure the certificate generation follows the defined standard without using any insecure properties, such as insufficient key size, overly long validity period, insecure cryptography and so on. Setup automatic rotation of the certificate in Azure Key Vault and Azure service (if supported) based on the defined schedule and when there is a certificate expiration. If automatic rotation is not supported in the front application, use a manual rotation in Azure Key Vault.\n\nAvoid using self-signed certificate and wildcard certificate in your critical services due to the limited security assurance. Instead, you can create public signed certificate in Azure Key Vault. The following CAs are the current partnered providers with Azure Key Vault. \n- DigiCert: Azure Key Vault offers OV TLS/SSL certificates with DigiCert.\n- GlobalSign: Azure Key Vault offers OV TLS/SSL certificates with GlobalSign.\n\nNote: Use only approved Certificate Authority (CA) and ensure the known bad CA root/intermediate certificates and certificates issued by these CAs are disabled.", "Implementation and additional context": "Get started with Key Vault certificates:\nhttps://docs.microsoft.com/azure/key-vault/certificates/certificate-scenarios\n\nCertificate Access Control in Azure Key Vault:\nhttps://docs.microsoft.com/azure/key-vault/certificates/certificate-access-control", "Customer Security Stakeholders:": "Identity and key management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys\n\nSecurity architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nData Security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-data-security", "Azure Policy Mapping": "[Preview]: Certificates should have the specified maximum validity period", "Azure Policy GUID": "0a075868-4c26-42ef-914c-5bc007359560"}, {"ASB ID": "DP-8", "Control Domain": "Data Protection", "CIS Controls v7.1 ID(s)": NaN, "CIS Controls v8 ID(s)": NaN, "NIST SP800-53 r4 ID(s)": "IA-5: AUTH<PERSON><PERSON><PERSON><PERSON><PERSON> MANAGEMENT\nSC-12: CRYPTO<PERSON><PERSON><PERSON><PERSON> KEY ESTABLISHMENT AND MANAGEMENT\nSC-17: <PERSON><PERSON><PERSON><PERSON> KEY INFRASTRUCTURE CERTIFICATES", "PCI-DSS v3.2.1 ID(s)": 3.6, "Recommendation": "Ensure security of key and certificate repository ", "Security Principle": "Ensure the security of the key vault service used for the cryptographic key and certificate lifecycle management. Harden your key vault service through access control, network security, logging and monitoring and backup to ensure keys and certificates are always protected using the maximum security.", "Azure Guidance": "Secure your cryptographic keys and certificates by hardening your Azure Key Vault service through the following controls:\n- Restrict the access to keys and certificates in Azure Key Vault using built-in access policies or Azure RBAC to ensure the least privileges principle are in place for management plane access and data plane access. \n- Secure the Azure Key Vault using Private Link and Azure Firewall to ensure the minimal exposure of the service \n- Ensure separation of duties is place for users who manages encryption keys not have the ability to access encrypted data, and vice versa. \n- Use managed identity to access keys stored in the Azure Key Vault in your workload applications.\n- Never have the keys stored in plaintext format outside of the Azure Key Vault.\n- When purging data, ensure your keys are not deleted before the actual data, backups and archives are purged.\n- Backup your keys and certificates using the Azure Key Vault. Enable soft delete and purge protection to avoid accidental deletion of keys. \n- Turn on Azure Key Vault logging to ensure the critical management plane and data plane activities are logged. ", "Implementation and additional context": "Azure Key Vault overview: \nhttps://docs.microsoft.com/azure/key-vault/general/overview\n\nAzure Key Vault security best practices:\nhttps://docs.microsoft.com/azure/key-vault/general/best-practices\n\nUse managed identity to access Azure Key Vault:\nhttps://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad\n\n\n", "Customer Security Stakeholders:": "Identity and key management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys\n\nSecurity architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nData Security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-data-security", "Azure Policy Mapping": "Key vaults should have purge protection enabled\nAzure Defender for Key Vault should be enabled\nKey vaults should have soft delete enabled\n[Preview]: Azure Key Vault should disable public network access\n[Preview]: Private endpoint should be configured for Key Vault\nResource logs in Key Vault should be enabled", "Azure Policy GUID": "0b60c0b2-2dc2-4e1c-b5c9-abbed971de53\n0e6763cc-5078-4e64-889d-ff4d9a839047\n1e66c121-a66a-4b1f-9b83-0fd99bf0fc2d\n55615ac9-af46-4a59-874e-391cc3dfb490\n5f0bc445-**************-011aa2b46147\ncf820ca0-f99e-4f3e-84fb-66e913812d21"}, {"ASB ID": "IM-1", "Control Domain": "Identity Management", "CIS Controls v7.1 ID(s)": "16.1 - Maintain an Inventory of \nAuthentication Systems\n16.2 - Configure Centralized \nPoint of Authentication", "CIS Controls v8 ID(s)": "6.7 - Centralize Access Control\n12.5 - Centralize Network Authentication, Authorization, and Auditing (AAA)\n", "NIST SP800-53 r4 ID(s)": "AC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nIA-2: IDENTIFICATION AND AUTHENTICATION (ORGANIZATIONAL USERS)\nIA-8: IDENTIFICATION AND AUTHENTICATION (NON-ORGANIZATIONAL USERS)", "PCI-DSS v3.2.1 ID(s)": "7.2\n8.3", "Recommendation": "Use centralized identity and authentication system", "Security Principle": "Use a centralized identity and authentication system to govern your organization's identities and authentications for cloud and non-cloud resources. ", "Azure Guidance": "Azure Active Directory (Azure AD) is Azure's identity and authentication management service. You should standardize on Azure AD to govern your organization's identity and authentication in:\n- Microsoft cloud resources, such as the Azure Storage, Azure Virtual Machines (Linux and Windows), Azure Key Vault, PaaS, and SaaS applications.\n- Your organization's resources, such as applications on Azure, third-party applications running on your corporate network resources, and third-party SaaS applications. \n- Your enterprise identities in Active Directory by synchronization to Azure AD to ensure a consistent and centrally managed identity strategy.\n\nNote: As soon as it is technically feasible, you should migrate on-premises Active Directory based applications to Azure AD. This could be an Azure AD Enterprise Directory, Business to Business configuration, or Business to consumer configuration.\n", "Implementation and additional context": "Tenancy in Azure AD: \nhttps://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps\n\nHow to create and configure an Azure AD instance: \nhttps://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant\n\nDefine Azure AD tenants: \nhttps://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/\n\nUse external identity providers for an application: \nhttps://docs.microsoft.com/azure/active-directory/b2b/identity-providers\n\n", "Customer Security Stakeholders:": "Identity and key management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys\n\nSecurity architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nApplication security and DevSecOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management", "Azure Policy Mapping": "An Azure Active Directory administrator should be provisioned for SQL servers\nService Fabric clusters should only use Azure Active Directory for client authentication", "Azure Policy GUID": "1f314764-cb73-4fc9-b863-8eca98ac36e9\nb54ed75b-3e1a-44ac-a333-05ba39b99ff0"}, {"ASB ID": "IM-2", "Control Domain": "Identity Management", "CIS Controls v7.1 ID(s)": "4.3 - Ensure the Use of Dedicated Administrative Accounts\n4.5 - Use Multi-Factor Authentication for All Administrative Access", "CIS Controls v8 ID(s)": "5.4 - Restrict Administrator Privileges to Dedicated Administrator Accounts\n6.5 - Require <PERSON><PERSON> for Administrative Access\n", "NIST SP800-53 r4 ID(s)": "AC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nIA-2: IDENTIFICATION AND AUTHENTICATION (ORGANIZATIONAL USERS)\nIA-8: IDENTIFICATION AND AUTHENTICATION (NON-ORGANIZATIONAL USERS)\nSI-4: INFORMATION SYSTEM MONITORING", "PCI-DSS v3.2.1 ID(s)": "8.2\n8.3\n", "Recommendation": "Protect identity and authentication systems", "Security Principle": "Secure your identity and authentication system as a high priority in your organization's cloud security practice. Common security controls include: \n- Restrict privileged roles and accounts\n- Require strong authentication for all privileged access\n- Monitor and audit high risk activities", "Azure Guidance": "Use the Azure AD security baseline and the Azure AD Identity Secure Score to evaluate your Azure AD identity security posture, and remediate security and configuration gaps.\nThe Azure AD Identity Secure Score evaluates Azure AD for the following configurations:\n-Use limited administrative roles\n- Turn on user risk policy\n- Designate more than one global admin\n-  Enable policy to block legacy authentication\n- Ensure all users can complete multi-factor authentication for secure access\n- Require M<PERSON> for administrative roles\n- Enable self-service password reset\n- Do not expire passwords\n- Turn on sign-in risk policy\n- Do not allow users to grant consent to unmanaged applications\n\nNote: Follow published best practices for all other identity components, including the on-premises Active Directory and any third party capabilities, and the infrastructures (such as operating systems, networks, databases) that host them.\n\n", "Implementation and additional context": "What is the identity secure score in Azure AD: https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score\n\nBest Practices for Securing Active Directory: \nhttps://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory", "Customer Security Stakeholders:": "Identity and key management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys\n\nSecurity architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nApplication security and DevSecOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "IM-3", "Control Domain": "Identity Management", "CIS Controls v7.1 ID(s)": NaN, "CIS Controls v8 ID(s)": NaN, "NIST SP800-53 r4 ID(s)": "AC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nIA-4: IDENTIFIER MANAGEMENT\nIA-5: AUTHENTICATOR MANAGEMENT\nIA-9: SERVICE IDENTIFICATION AND AUTHENTICATION", "PCI-DSS v3.2.1 ID(s)": "N/A\n", "Recommendation": "Manage application identities securely and automatically", "Security Principle": "Use managed application identities instead of creating human accounts for applications to access resources and execute code. Managed application identities provide benefits such as reducing the exposure of credentials. Automate the rotation of credential to ensure the security of the identities. ", "Azure Guidance": "Use Azure managed identities, which can authenticate to Azure services and resources that support Azure AD authentication. Managed identity credentials are fully managed, rotated, and protected by the platform, avoiding hard-coded credentials in source code or configuration files. \n\nFor services that don't support managed identities, use Azure AD to create a service principal with restricted permissions at the resource level. It is recommended to configure service principals with certificate credentials and fall back to client secrets for authentication. ", "Implementation and additional context": "Azure managed identities: \nhttps://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview\n\nServices that support managed identities for Azure resources: \nhttps://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities\n\nAzure service principal: \nhttps://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps\n\nCreate a service principal with certificates: \nhttps://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell", "Customer Security Stakeholders:": "Identity and key management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys\n\nApplication security and DevSecOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops", "Azure Policy Mapping": "Managed identity should be used in your Function App\nManaged identity should be used in your Web App\nService principals should be used to protect your subscriptions instead of management certificates\nManaged identity should be used in your API App\nVirtual machines' Guest Configuration extension should be deployed with system-assigned managed identity", "Azure Policy GUID": "0da106f2-4ca3-48e8-bc85-c638fe6aea8f\n2b9ad585-36bc-4615-b300-fd4435808332\n6646a0bd-e110-40ca-bb97-84fcee63c414\nc4d441f8-f9d9-4a9e-9cef-e82117cb3eef\nd26f7642-7545-4e18-9b75-8c9bbdee3a9a"}, {"ASB ID": "IM-4", "Control Domain": "Identity Management", "CIS Controls v7.1 ID(s)": NaN, "CIS Controls v8 ID(s)": NaN, "NIST SP800-53 r4 ID(s)": "IA-9: SERVICE IDENTITIFICATION AND AUTHENTICATION", "PCI-DSS v3.2.1 ID(s)": NaN, "Recommendation": "Authenticate server and services", "Security Principle": "Authenticate remote servers and services from your client side to ensure you are connecting to trusted server and services. The most common server authentication protocol is Transport Layer Security (TLS), where the client-side (often a browser or client device) verifies the server by verifying the server’s certificate was issued by a trusted certificate authority.\n\nNote: Mutual authentication can be used when both the server and the client authenticate one-another.", "Azure Guidance": "Many Azure services support TLS authentication by default. For the services supporting TLS enable/disable switch by the user, ensure it's always enabled to support the server/service authentication. Your client application should also be designed to verify server/service identity (by verifying the server’s certificate issued by a trusted certificate authority) in the handshake stage.", "Implementation and additional context": "Enforce Transport Layer Security (TLS) for a storage account:\nhttps://docs.microsoft.com/azure/storage/common/transport-layer-security-configure-minimum-version?tabs=portal#use-azure-policy-to-enforce-the-minimum-tls-version", "Customer Security Stakeholders:": "Identity and key management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys\n\nApplication security and DevSecOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "IM-5", "Control Domain": "Identity Management", "CIS Controls v7.1 ID(s)": "16.2 - Configure Centralized Point of Authentication", "CIS Controls v8 ID(s)": "12.5 - Centralize Network Authentication, Authorization, and Auditing (AAA)", "NIST SP800-53 r4 ID(s)": "IA-4: <PERSON><PERSON><PERSON><PERSON><PERSON> MANAGEMENT\nIA-2: IDEN<PERSON><PERSON>CATION AND AUTHENTICATION (ORGANIZATIONAL USERS)\nIA-8: IDENTIFICATION AND AUTHENTICATION (NON-ORGANIZATIONAL USERS)", "PCI-DSS v3.2.1 ID(s)": NaN, "Recommendation": "Use single sign-on (SSO) for application access", "Security Principle": "Use single sign-on (SSO) to simplify the user experience for authenticating to resources including applications and data across cloud services and on-premises environments. ", "Azure Guidance": "Use Azure AD for workload application access through Azure AD single sign-on (SSO), obviating the need for multiple accounts. Azure AD provides identity and access management to Azure resources (management plane including CLI, PowerShell, portal), cloud applications, and on-premises applications. \n\nAzure AD supports SSO for enterprise identities such as corporate user identities, as well as external user identities from trusted third-party and public users.", "Implementation and additional context": "Understand application SSO with Azure AD: \nhttps://docs.microsoft.com/azure/active-directory/manage-apps/what-is-single-sign-on", "Customer Security Stakeholders:": "Security architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nIdentity and key management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys\n\nApplication security and DevSecOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "IM-6", "Control Domain": "Identity Management", "CIS Controls v7.1 ID(s)": "4.2 - Change Default Passwords\n4.5 - Use Multifactor Authentication For All Administrative Access\n12.11 - Require All Remote Logins to Use Multi-Factor Authentication\n16.3 - Require Multi-Factor Authentication", "CIS Controls v8 ID(s)": "6.3 - Require MFA for Externally-Exposed Applications\n6.4 - Require MFA for Administrative Access", "NIST SP800-53 r4 ID(s)": "AC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nIA-2: IDENTIFICATION AND AUTHENTICATION (ORGANIZATIONAL USERS)\nIA-5: AUTHENTICATOR MANAGEMENT\nIA-8: IDENTI<PERSON>CATION AND AUTHENTICATION (NON-ORGANIZATIONAL USERS)", "PCI-DSS v3.2.1 ID(s)": "7.2\n8.2\n8.3\n8.4", "Recommendation": "Use strong authentication controls", "Security Principle": "Enforce strong authentication controls (strong passwordless authentication or multi-factor authentication) with your centralized identity and authentication management system for all access to resources. Authentication based on password credentials alone is considered legacy, as it is insecure and does not stand up to popular attack methods. \n\nWhen deploying strong authentication, configure administrators and privileged users first, to ensure the highest level of the strong authentication method, quickly followed by rolling out the appropriate strong authentication policy to all users.\n\nNote: If legacy password-based authentication is required for legacy applications and scenarios, ensure password security best practices such as complexity requirements, are followed. ", "Azure Guidance": "Azure AD supports strong authentication controls through passwordless methods and multi-factor authentication (MFA).\n- Passwordless authentication: Use passwordless authentication as your default authentication method. There are three options available in passwordless authentication: Windows Hello for Business, Microsoft Authenticator app phone sign-in, and FIDO 2Keys. In addition, customers can use on-premises authentication methods such as smart cards.\n- Multi-factor authentication: Azure MFA can be enforced on all users, select users, or at the per-user level based on sign-in conditions and risk factors. Enable Azure MFA and follow Microsoft Defender for Cloud identity and access management recommendations for your MFA setup. \n\nIf legacy password-based authentication is still used for Azure AD authentication, be aware that cloud-only accounts (user accounts created directly in Azure) have a default baseline password policy. And hybrid accounts (user accounts that come from on-premises Active Directory) follow the on-premises password policies. \n\nFor third-party applications and services that may have default IDs and passwords, you should disable or change them during initial service setup.", "Implementation and additional context": "How to enable MFA in Azure: \nhttps://docs.microsoft.com/azure/active-directory/authentication/howto-mfa-getstarted\n\nIntroduction to passwordless authentication options for Azure Active Directory: \nhttps://docs.microsoft.com/azure/active-directory/authentication/concept-authentication-passwordless\n\nAzure AD default password policy: \nhttps://docs.microsoft.com/azure/active-directory/authentication/concept-sspr-policy#password-policies-that-only-apply-to-cloud-user-accounts\n\nEliminate bad passwords using Azure AD Password Protection: https://docs.microsoft.com/azure/active-directory/authentication/concept-password-ban-bad\n\nBlock legacy authentication:\nhttps://docs.microsoft.com/azure/active-directory/conditional-access/block-legacy-authentication", "Customer Security Stakeholders:": "Security architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nIdentity and key management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys\n\nApplication security and DevSecOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops", "Azure Policy Mapping": "Authentication to Linux machines should require SSH keys\nMFA should be enabled accounts with write permissions on your subscription\nMFA should be enabled on accounts with owner permissions on your subscription\nMFA should be enabled on accounts with read permissions on your subscription", "Azure Policy GUID": "630c64f9-8b6b-4c64-b511-6544ceff6fd6\n9297c21d-2ed6-4474-b48f-163f75654ce3\naa633080-8b72-40c4-a2d7-d00c03e80bed\ne3576e28-8b17-4677-84c3-db2990658d64"}, {"ASB ID": "IM-7", "Control Domain": "Identity Management", "CIS Controls v7.1 ID(s)": "12.11 - Require All Remote Logins to Use Multi-Factor Authentication\n12.12 - Manage All Devices Remotely Logging Into Internal Network\n14.6 - Protect Information Through Access Control Lists\n16.3 - Require Multi-Factor Authentication", "CIS Controls v8 ID(s)": "3.3 - Configure Data Access Control Lists\n6.4 - Require MFA for Administrative Access\n13.5 - Manage Access Control for Remote Assets", "NIST SP800-53 r4 ID(s)": "AC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nAC-6: LEAST PRIVILEGE", "PCI-DSS v3.2.1 ID(s)": 7.2, "Recommendation": "Restrict resource access based on  conditions", "Security Principle": "Explicitly validate trusted signals to allow or deny user access to resources, as part of a zero-trust access model. Signals to validate should include strong authentication of user account, behavioral analytics of user account, device trustworthiness, user or group membership, locations and so on.", "Azure Guidance": "Use Azure AD conditional access for more granular access controls based on user-defined conditions, such as requiring user logins from certain IP ranges (or devices) to use MFA. Azure AD Conditional Access allows you to enforce access controls on your organization’s apps based on certain conditions. \n\nDefine the applicable conditions and criteria for Azure AD conditional access in the workload. Consider the following common use cases: \n- Requiring multi-factor authentication for users with administrative roles\n- Requiring multi-factor authentication for Azure management tasks\n- Blocking sign-ins for users attempting to use legacy authentication protocols\n- Requiring trusted locations for Azure AD Multi-Factor Authentication registration\n- Blocking or granting access from specific locations\n- Blocking risky sign-in behaviors\n- Requiring organization-managed devices for specific applications\n\nNote: A granular authentication session management can also be used to through Azure AD conditional access policy for controls such as sign-in frequency and persistent browser session.\n", "Implementation and additional context": "Azure Conditional Access overview: \nhttps://docs.microsoft.com/azure/active-directory/conditional-access/overview\n\nCommon Conditional Access policies: \nhttps://docs.microsoft.com/azure/active-directory/conditional-access/concept-conditional-access-policy-common\n\nConditional Access insights and reporting:\nhttps://docs.microsoft.com/azure/active-directory/conditional-access/howto-conditional-access-insights-reporting\n\nConfigure authentication session management with Conditional Access: \nhttps://docs.microsoft.com/azure/active-directory/conditional-access/howto-conditional-access-session-lifetime", "Customer Security Stakeholders:": "Identity and key management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys\n\nApplication security and DevSecOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management\n\nThreat intelligence: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-threat-intelligence", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "IM-8", "Control Domain": "Identity Management", "CIS Controls v7.1 ID(s)": "18.1 - Establish Secure Coding Practices\n18.6 - Ensure Software Development Personnel Are Trained in Secure Coding\n18.7 - Apply Static and Dynamic Code Analysis Tools", "CIS Controls v8 ID(s)": "16.9 - Train Developers in Application Security Concepts and Secure Coding\n16.12 - Implement Code-Level Security Checks\n", "NIST SP800-53 r4 ID(s)": "IA-5: AUTHENTICATOR MANAGEMENT", "PCI-DSS v3.2.1 ID(s)": "3.5\n6.3\n8.2\n\n", "Recommendation": "Restrict the exposure of credential and secrets", "Security Principle": "Ensure that application developers securely handle credentials and secrets:\n- Avoid embedding the credentials and secrets into the code and configuration files\n- Use key vault or a secure key store service to store the credentials and secrets\n- Scan for credentials in source code.  \n\nNote: This is often governed and enforced through a secure software development lifecycle (SDLC) and DevOps security process", "Azure Guidance": "Ensure that secrets and credentials are stored in secure locations such as Azure Key Vault, instead of embedding them into the code and configuration files. \n- Implement Azure DevOps Credential Scanner to identify credentials within the code. \n- For GitHub, use the native secret scanning feature to identify credentials or other form of secrets within the code.\n\nClients such as Azure Functions, Azure Apps services, and VMs can use managed identities to access Azure Key Vault securely. See Data Protection controls related to the use of Azure Key Vault for secrets management.", "Implementation and additional context": "How to setup Credential Scanner: \nhttps://secdevtools.azurewebsites.net/helpcredscan.html\n\nGitHub secret scanning: \nhttps://docs.github.com/github/administering-a-repository/about-secret-scanning", "Customer Security Stakeholders:": "Application security and DevSecOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "IM-9", "Control Domain": "Identity Management", "CIS Controls v7.1 ID(s)": "12.10 Decrypt Network Traffic at Proxy\n16.2 Configure Centralized Point of Authentication", "CIS Controls v8 ID(s)": "6.7 - Centralize Access Control\n12.5 - Centralize Network Authentication, Authorization, and Auditing (AAA)", "NIST SP800-53 r4 ID(s)": "AC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nSC-11: TRUSTED PATH", "PCI-DSS v3.2.1 ID(s)": NaN, "Recommendation": "Secure user access to  existing applications", "Security Principle": "In a hybrid environment, where you have on-premises applications or non-native cloud applications using legacy authentication, consider solutions such as cloud access security broker (CASB), application proxy, single sign-on (SSO)  to govern the access to these applications for the following benefits:\n- Enforce a centralized strong authentication\n- Monitor and control risky end-user activities \n- Monitor and remediate risky legacy applications activities\n- Detect and prevent sensitive data transmission", "Azure Guidance": "Protect your on-premises and non-native cloud applications using legacy authentication by connecting them to:\n- Azure AD Application Proxy in conjunction with header-based authentication for publishing legacy on-premises applications to remote users with single sign-on (SSO) while explicitly validating the trustworthiness of both remote users and devices with Azure AD Conditional Access. If required, use third-party Software-Defined Perimeter (SDP) solution which can offer similar functionality.\n- Your existing third-party application delivery controllers and networks\n- Microsoft Cloud App Security (MCAS), using it as a cloud access security broker (CASB) service to provide controls for monitoring a user's application sessions and blocking actions (for both legacy on-premises applications and cloud software as a service (SaaS) applications).\n\nNote: VPNs are commonly used to access legacy applications, they often have only basic access control and limited session monitoring. \n", "Implementation and additional context": "Azure AD Application Proxy: \nhttps://docs.microsoft.com/azure/active-directory/manage-apps/application-proxy#what-is-application-proxy\n\nMicrosoft Cloud App Security best practices: \nhttps://docs.microsoft.com/cloud-app-security/best-practices\n\nAzure AD secure hybrid access:\nhttps://docs.microsoft.com/azure/active-directory/manage-apps/secure-hybrid-access", "Customer Security Stakeholders:": "Security architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nInfrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nApplication security and DevSecOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "PA-1", "Control Domain": "Privileged Access", "CIS Controls v7.1 ID(s)": "4.3 - Ensure the Use of Dedicated Administrative Accounts\n14.6 - Protect Information Through Access Control Lists", "CIS Controls v8 ID(s)": "5.4 - Restrict Administrator Privileges to Dedicated Administrator Accounts\n6.8 - Define and Maintain Role-Based Access Control", "NIST SP800-53 r4 ID(s)": "AC-2: ACCOUNT MANAGEMENT\nAC-6: LEAST PRIVILEGE", "PCI-DSS v3.2.1 ID(s)": "7.1\n7.2\n8.1\n", "Recommendation": "Separate and limit highly privileged/administrative users", "Security Principle": "Ensure you are identifying all high business impact accounts. Limit the number of privileged/administrative accounts in your cloud's control plane, management plane and data/workload plane. ", "Azure Guidance": "Azure Active Directory (Azure AD) is Azure's default identity and access management service. The most critical built-in roles in Azure AD are Global Administrator and Privileged Role Administrator, because users assigned to these two roles can delegate administrator roles. With these privileges, users can directly or indirectly read and modify every resource in your Azure environment:\n- Global Administrator / Company Administrator: Users with this role have access to all administrative features in Azure AD, as well as services that use Azure AD identities.\n- Privileged Role Administrator: Users with this role can manage role assignments in Azure AD, as well as within Azure AD Privileged Identity Management (PIM). In addition, this role allows management of all aspects of PIM and administrative units.\n\nOutside of the Azure AD, Azure has built-in roles that can be critical for privileged access at the resource level.\n- Owner: Grants full access to manage all resources, including the ability to assign roles in Azure RBAC.\n- Contributor: Grants full access to manage all resources, but does not allow you to assign roles in Azure RBAC, manage assignments in Azure Blueprints, or share image galleries.\n- User Access Administrator: Lets you manage user access to Azure resources.\nNote: You may have other critical roles that need to be governed if you use custom roles in the Azure AD level or resource level with certain privileged permissions assigned. \n\nEnsure that you also restrict privileged accounts in other management, identity, and security systems that have administrative access to your business-critical assets, such as Active Directory Domain Controllers (DCs), security tools, and system management tools with agents installed on business critical systems. Attackers who compromise these management and security systems can immediately weaponize them to compromise business critical assets.", "Implementation and additional context": "Administrator role permissions in Azure AD: \nhttps://docs.microsoft.com/azure/active-directory/users-groups-roles/directory-assign-admin-roles\n\nUse Azure Privileged Identity Management security alerts: \nhttps://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-how-to-configure-security-alerts\n\nSecuring privileged access for hybrid and cloud deployments in Azure AD: \nhttps://docs.microsoft.com/azure/active-directory/users-groups-roles/directory-admin-roles-secure", "Customer Security Stakeholders:": "Identity and key management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys\n\nSecurity architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nSecurity Compliance Management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-compliance-management\n\nSecurity Operations: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-operations-center", "Azure Policy Mapping": "There should be more than one owner assigned to your subscription\nA maximum of 3 owners should be designated for your subscription\nDeprecated accounts with owner permissions should be removed from your subscription\nExternal accounts with owner permissions should be removed from your subscription", "Azure Policy GUID": "09024ccc-0c5f-475e-9457-b7c0d9ed487b\n4f11b553-d42e-4e3a-89be-32ca364cad4c\nebb62a0c-3560-49e1-89ed-27e074e9f8ad\nf8456c1c-aa66-4dfb-861a-25d127b775c9"}, {"ASB ID": "PA-2", "Control Domain": "Privileged Access", "CIS Controls v7.1 ID(s)": NaN, "CIS Controls v8 ID(s)": NaN, "NIST SP800-53 r4 ID(s)": "AC-2: ACCOUNT MANAGEMENT", "PCI-DSS v3.2.1 ID(s)": "N/A\n", "Recommendation": "Avoid standing access for user accounts and permissions", "Security Principle": "Instead of creating standing privileges, use just-in-time (JIT) mechanism to assign privileged access to the different resource tiers.", "Azure Guidance": "Enable just-in-time (JIT) privileged access to Azure resources and Azure AD using Azure AD Privileged Identity Management (PIM). JIT is a model in which users receive temporary permissions to perform privileged tasks, which prevents malicious or unauthorized users from gaining access after the permissions have expired. Access is granted only when users need it. PIM can also generate security alerts when there is suspicious or unsafe activity in your Azure AD organization.\n\nRestrict inbound traffic to your sensitive virtual machines (VM) management ports with Microsoft Defender for Cloud's just-in-time (JIT) for VM access feature. This ensures the privileged access to the VM are granted only when users need it. ", "Implementation and additional context": "Azure PIM just-in-time access deployment:\nhttps://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-deployment-plan", "Customer Security Stakeholders:": "Identity and key management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys\n\nSecurity architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nSecurity Compliance Management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-compliance-management\n\nSecurity Operations: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-operations-center", "Azure Policy Mapping": "Management ports of virtual machines should be protected with just-in-time network access control", "Azure Policy GUID": "b0f33259-77d7-4c9e-aac6-3aabcfae693c"}, {"ASB ID": "PA-3", "Control Domain": "Privileged Access", "CIS Controls v7.1 ID(s)": "16.7 - Establish Process for Revoking Access", "CIS Controls v8 ID(s)": "6.1 - Establish an Access Granting Process\n6.2 - Establish an Access Revoking Process", "NIST SP800-53 r4 ID(s)": "AC-2: ACCOUNT MANAGEMENT\nAC-5: SEPARATION OF DUTIES\nAC-6: LEAST PRIVILEGE", "PCI-DSS v3.2.1 ID(s)": "7.1\n7.2\n8.1\t\n", "Recommendation": "Manage lifecycle of identities and entitlements", "Security Principle": "Use an automated process or technical control to manage the identity and access lifecycle including the request, review, approval, provision, and deprovision. ", "Azure Guidance": "Use Azure AD entitlement management features to automate access (for Azure resource groups) request workflows. This enables workflows for Azure resource groups to manage access assignments, reviews, expiration, and dual or multi-stage approval.", "Implementation and additional context": "What are Azure AD access reviews: \nhttps://docs.microsoft.com/azure/active-directory/governance/access-reviews-overview\n\nWhat is Azure AD entitlement management: \nhttps://docs.microsoft.com/azure/active-directory/governance/entitlement-management-overview", "Customer Security Stakeholders:": "Identity and key management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys\n\nApplication security and DevSecOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nSecurity Compliance Management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-compliance-management", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "PA-4", "Control Domain": "Privileged Access", "CIS Controls v7.1 ID(s)": "4.1 - Maintain Inventory of Administrative Accounts\n16.6 - Maintain an Inventory of Accounts\n16.8 - Disable Any Unassociated Accounts\nDisable <PERSON><PERSON><PERSON> Accounts\n16.9 - Disable <PERSON><PERSON><PERSON> Accounts", "CIS Controls v8 ID(s)": "5.1 - <PERSON><PERSON>blish and Maintain an Inventory of Accounts\n5.3 - <PERSON><PERSON> <PERSON><PERSON><PERSON> Accounts\n5.5 - <PERSON><PERSON>blish and Maintain an Inventory of Service Accounts\n", "NIST SP800-53 r4 ID(s)": "AC-2: ACCOUNT MANAGEMENT\nAC-6: LEAST PRIVILEGE", "PCI-DSS v3.2.1 ID(s)": "7.1\n7.2\n8.1\nA3.4", "Recommendation": "Review and reconcile user access regularly", "Security Principle": "Conduct regular review of privileged account entitlements. Ensure the access granted to the accounts are valid for administration of control plane, management plane, and workloads. ", "Azure Guidance": "Review all privileged accounts and the access entitlements in Azure including such as Azure tenant, Azure services, VM/IaaS, CI/CD processes, and enterprise management and security tools. \n\nUse Azure AD access reviews to review Azure AD roles and Azure resource access roles, group memberships, access to enterprise applications. Azure AD reporting can also provide logs to help discover stale accounts, accounts not being used for certain amount of time.\n\nIn addition, Azure AD Privileged Identity Management can be configured to alert when an excessive number of administrator accounts are created for a specific role, and to identify administrator accounts that are stale or improperly configured. ", "Implementation and additional context": "Create an access review of Azure resource roles in Privileged Identity Management (PIM):\nhttps://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-resource-roles-start-access-review\n\nHow to use Azure AD identity and access reviews: \nhttps://docs.microsoft.com/azure/active-directory/governance/access-reviews-overview\n", "Customer Security Stakeholders:": "Identity and key management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys\n\nApplication security and DevSecOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nSecurity Compliance Management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-compliance-management", "Azure Policy Mapping": "External accounts with write permissions should be removed from your subscription\nExternal accounts with read permissions should be removed from your subscription\nDeprecated accounts should be removed from your subscription\nDeprecated accounts with owner permissions should be removed from your subscription\nExternal accounts with owner permissions should be removed from your subscription", "Azure Policy GUID": "5c607a2e-c700-4744-8254-d77e7c9eb5e4\n5f76cf89-fbf2-47fd-a3f4-b891fa780b60\n6b1cbf55-e8b6-442f-ba4c-7246b6381474\nebb62a0c-3560-49e1-89ed-27e074e9f8ad\nf8456c1c-aa66-4dfb-861a-25d127b775c9"}, {"ASB ID": "PA-5", "Control Domain": "Privileged Access", "CIS Controls v7.1 ID(s)": NaN, "CIS Controls v8 ID(s)": NaN, "NIST SP800-53 r4 ID(s)": "AC-2: ACCOUNT MANAGEMENT", "PCI-DSS v3.2.1 ID(s)": NaN, "Recommendation": "Set up emergency access", "Security Principle": "Set up emergency access to ensure that you are not accidentally locked out of your critical cloud infrastructure (such as your identity and access management system) in an emergency. \n\nEmergency access accounts should be rarely used and can be highly damaging to the organization if compromised, but their availability to the organization is also critically important for the few scenarios when they are required. ", "Azure Guidance": "To prevent being accidentally locked out of your Azure AD organization, set up an emergency access account (e.g. an account with Global Administrator role) for access when normal administrative accounts cannot be used. Emergency access accounts are usually highly privileged, and they should not be assigned to specific individuals. Emergency access accounts are limited to emergency or \"break glass\"' scenarios where normal administrative accounts can't be used.\n\nYou should ensure that the credentials (such as password, certificate, or smart card) for emergency access accounts are kept secure and known only to individuals who are authorized to use them only in an emergency. You may also use additional controls, such dual controls (e.g., splitting the credential into two pieces and giving it to separate persons) to enhance the security of this process. You should also monitor the sign-in and audit logs to ensure the emergency access accounts can only be used under authorization. \n", "Implementation and additional context": "Manage emergency access accounts in Azure AD: \nhttps://docs.microsoft.com/azure/active-directory/users-groups-roles/directory-emergency-access\n\n", "Customer Security Stakeholders:": "Identity and key management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys\n\nApplication security and DevSecOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nSecurity Compliance Management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-compliance-management\n\nSecurity Operations (SecOps): https://docs.microsoft.com//azure/cloud-adoption-framework/organize/cloud-security-operations-center", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "PA-6", "Control Domain": "Privileged Access", "CIS Controls v7.1 ID(s)": "4.6 - Use Dedicated Workstations For All Administrative Tasks\n11.6 - Use Dedicated Machines For All Network Administrative Tasks\n12.12 - Manage All Devices Remotely Logging into Internal Network", "CIS Controls v8 ID(s)": "12.8 - Establish and Maintain Dedicated Computing Resources for All Administrative Work\n13.5 Manage Access Control for Remote Assets\n", "NIST SP800-53 r4 ID(s)": "AC-2: ACCOUNT MANAGEMENT\nSC-2 APPLICATION PARTITIONING\nSC-7: BOUNDARY PROTECTION", "PCI-DSS v3.2.1 ID(s)": NaN, "Recommendation": "Use privileged access workstations", "Security Principle": "Secured, isolated workstations are critically important for the security of sensitive roles like administrator, developer, and critical service operator.  ", "Azure Guidance": "Use Azure Active Directory, Microsoft Defender, and/or Microsoft Intune to deploy privileged access workstations (PAW) on-premises or in the Azure for privileged tasks. The PAW should be centrally managed to enforce secured configuration, including strong authentication, software and hardware baselines, and restricted logical and network access.\n\nYou may also use Azure Bastion which is a fully platform-managed PaaS service that can be provisioned inside your virtual network. Azure Bastion allows RDP/SSH connectivity to your virtual machines directly from the Azure portal using browser. ", "Implementation and additional context": "Understand privileged access workstations: \nhttps://docs.microsoft.com/azure/active-directory/devices/concept-azure-managed-workstation\n\nPrivileged access workstations deployment: \nhttps://docs.microsoft.com/security/compass/privileged-access-deploymenthttps", "Customer Security Stakeholders:": "Application security and DevSecOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nSecurity Operations (SecOps): https://docs.microsoft.com/en-us/azure/cloud-adoption-framework/organize/cloud-security-operations-center\n\nIdentity and key management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "PA-7", "Control Domain": "Privileged Access", "CIS Controls v7.1 ID(s)": "14.6 - Protect Information Through Access Control Lists", "CIS Controls v8 ID(s)": "3.3 - Configure Data Access Control Lists\n6.8 - Define and Maintain Role-Based Access Control", "NIST SP800-53 r4 ID(s)": "AC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nAC-6: LEAST PRIVILEGE\n", "PCI-DSS v3.2.1 ID(s)": "7.1\n7.2", "Recommendation": "Follow just enough administration (least privilege) principle", "Security Principle": "Follow the just enough administration (least privilege) principle to manage permissions at fine-grained level. Use features such as role-based access control (RBAC) to manage resource access through role assignments.  ", "Azure Guidance": "Use Azure role-based access control (Azure RBAC) to manage Azure resource access through role assignments. Through RBAC, you can assign roles to users, group service principals, and managed identities. There are pre-defined built-in roles for certain resources, and these roles can be inventoried or queried through tools such as Azure CLI, Azure PowerShell, and the Azure portal. \n\nThe privileges you assign to resources through Azure RBAC should always be limited to what's required by the roles. Limited privileges will complement the just-in-time (JIT) approach of Azure AD Privileged Identity Management (PIM), and those privileges should be reviewed periodically. If required, you can also use PIM to define the time-length (time-bound-assignment) condition in role assignment where a user can activate or use the role only within start and end dates.\n\nNote: Use Azure built-in roles to allocate permissions and only create custom roles when required.", "Implementation and additional context": "What is Azure role-based access control (Azure RBAC): \nhttps://docs.microsoft.com/azure/role-based-access-control/overview\n\nHow to configure RBAC in Azure: \nhttps://docs.microsoft.com/azure/role-based-access-control/role-assignments-portal\n\nHow to use Azure AD identity and access reviews: \nhttps://docs.microsoft.com/azure/active-directory/governance/access-reviews-overview\n\nAzure AD Privileged Identity Management - Time-bound assignment: \nhttps://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure#what-does-it-do", "Customer Security Stakeholders:": "Application security and DevSecOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nSecurity Compliance Management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-compliance-management\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management \n\nIdentity and key management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys", "Azure Policy Mapping": "Audit usage of custom RBAC rules\nRole-Based Access Control (RBAC) should be used on Kubernetes Services", "Azure Policy GUID": "a451c1ef-c6ca-483d-87ed-f49761e3ffb5\nac4a19c2-fa67-49b4-8ae5-0b2e78c49457"}, {"ASB ID": "PA-8", "Control Domain": "Privileged Access", "CIS Controls v7.1 ID(s)": "16.7 - Establish Process for Revoking Access", "CIS Controls v8 ID(s)": "6.1 - Establish an Access Granting Process\n6.2 - Establish an Access Revoking Process", "NIST SP800-53 r4 ID(s)": "AC-4: INFORMATION FLOW ENFORCEMENT\nAC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT", "PCI-DSS v3.2.1 ID(s)": NaN, "Recommendation": "Determine access process for cloud provider support\n\n", "Security Principle": "Establish an approval process and access path for requesting and approving vendor support request and temporary access to your data through a secure channel.", "Azure Guidance": "In support scenarios where Microsoft needs to access your data, use Customer Lockbox to review and approve or reject each Microsoft's data access request.\n\n", "Implementation and additional context": "Understand Customer Lockbox: \nhttps://docs.microsoft.com/azure/security/fundamentals/customer-lockbox-overview", "Customer Security Stakeholders:": "Application security and DevSecOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nSecurity Compliance Management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-compliance-management\n\nIdentity and key management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "PV-1", "Control Domain": "Posture and Vulnerability Management", "CIS Controls v7.1 ID(s)": "5.1 - Establish Secure Configurations\n11.1 - Maintain Standard Security Configurations for Network Devices ", "CIS Controls v8 ID(s)": "4.1 - Establish and Maintain a Secure Configuration Process\n4.2 - Establish and Maintain a Secure Configuration Process for Network Infrastructure", "NIST SP800-53 r4 ID(s)": "CM-2: <PERSON><PERSON><PERSON><PERSON>E CONFIGURATION\nCM-6: CONFIGURATION SETTINGS", "PCI-DSS v3.2.1 ID(s)": "1.1\n2.2", "Recommendation": "Define and establish secure configurations ", "Security Principle": "Define the secure configuration baselines for different resource types in the cloud. Alternatively, use configuration management tools to establish the configuration baseline automatically before or during resource deployment so the environment can be compliant by default after the deployment. ", "Azure Guidance": "Use the Azure Security Benchmark and service baseline to define your configuration baseline for each respective Azure offering or service. Refer to Azure reference architecture and Cloud Adoption Framework landing zone architecture to understand the critical security controls and configurations that may need across Azure resources. \n\nUse Azure Blueprints to automate deployment and configuration of services and application environments, including Azure Resource Manager templates, Azure RBAC controls, and policies, in a single blueprint definition.", "Implementation and additional context": "Illustration of Guardrails implementation in Enterprise Scale Landing Zone: \nhttps://docs.microsoft.com/azure/cloud-adoption-framework/ready/enterprise-scale/architecture#landing-zone-expanded-definition\n\nWorking with security policies in Microsoft Defender for Cloud: \nhttps://docs.microsoft.com/azure/security-center/tutorial-security-policy\n\nTutorial: Create and manage policies to enforce compliance:\nhttps://docs.microsoft.com/azure/governance/policy/tutorials/create-and-manage\n\nAzure Blueprints: \nhttps://docs.microsoft.com/azure/governance/blueprints/overview", "Customer Security Stakeholders:": "Posture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management \n\nInfrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-infrastructure-endpoint\n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "PV-2", "Control Domain": "Posture and Vulnerability Management", "CIS Controls v7.1 ID(s)": "5.4 - Deploy System Configuration Management Tools\n5.5 - Implement Automated Configuration Monitoring Systems\n11.3 - Use Automated Tools to Verify Standard Device Configurations and Detect Changes", "CIS Controls v8 ID(s)": "4.1 - Establish and Maintain a Secure Configuration Process\n4.2 - Establish and Maintain a Secure Configuration Process for Network Infrastructure", "NIST SP800-53 r4 ID(s)": "CM-2: <PERSON><PERSON><PERSON><PERSON>E CONFIGURATION\nCM-6: CONFIGURATION SETTINGS", "PCI-DSS v3.2.1 ID(s)": "2.2\n", "Recommendation": "Audit and enforce secure configurations", "Security Principle": "Continuously monitor and alert when there is a deviation from the defined configuration baseline. Enforce the desired configuration according to the baseline configuration by denying the non-compliant configuration or deploy a configuration. ", "Azure Guidance": "Use Microsoft Defender for Cloud to configure Azure Policy to audit and enforce configurations of your Azure resources. Use Azure Monitor to create alerts when there is a configuration deviation detected on the resources. \n\nUse Azure Policy [deny] and [deploy if not exist] rule to enforce secure configuration across Azure resources. \n\nFor resource configuration audit and enforcement not supported by Azure Policy, you may need to write your own scripts or use third-party tooling to implement the configuration audit and enforcement. ", "Implementation and additional context": "Understand Azure Policy effects: \nhttps://docs.microsoft.com/azure/governance/policy/concepts/effects\n\nCreate and manage policies to enforce compliance: \nhttps://docs.microsoft.com/azure/governance/policy/tutorials/create-and-manage\n\nGet compliance data of Azure resources:\nhttps://docs.microsoft.com/azure/governance/policy/how-to/get-compliance-data", "Customer Security Stakeholders:": "Posture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management \n\nInfrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-infrastructure-endpoint\n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops", "Azure Policy Mapping": "CORS should not allow every resource to access your Function Apps\nKubernetes cluster pod hostPath volumes should only use allowed host paths\nAzure Policy Add-on for Kubernetes service (AKS) should be installed and enabled on your clusters\nEnsure API app has 'Client Certificates (Incoming client certificates)' set to 'On'\nRemote debugging should be turned off for Function Apps\nKubernetes clusters should not allow container privilege escalation\nKubernetes cluster services should listen only on allowed ports\nCORS should not allow every resource to access your API App\n[Preview]: Kubernetes clusters should disable automounting API credentials\nKubernetes cluster containers should only listen on allowed ports\nKubernetes cluster containers should not share host process ID or host IPC namespace\nKubernetes cluster containers should only use allowed AppArmor profiles\nCORS should not allow every resource to access your Web Applications\nOperating system version should be the most current version for your cloud service roles\nEnsure WEB app has 'Client Certificates (Incoming client certificates)' set to 'On'\nKubernetes cluster pods should only use approved host network and port range\nKubernetes cluster should not allow privileged containers\n[Preview]: Kubernetes clusters should not use the default namespace\nKubernetes cluster containers should only use allowed capabilities\nRemote debugging should be turned off for Web Applications\n[Preview]: Kubernetes clusters should not grant CAP_SYS_ADMIN security capabilities\nKubernetes cluster containers should run with a read only root file system\nKubernetes cluster containers CPU and memory resource limits should not exceed the specified limits\nRemote debugging should be turned off for API Apps\nFunction apps should have 'Client Certificates (Incoming client certificates)' enabled\nKubernetes cluster pods and containers should only run with approved user and group IDs\nKubernetes cluster containers should only use allowed images\nKubernetes clusters should gate deployment of vulnerable images", "Azure Policy GUID": "0820b7b9-23aa-4725-a1ce-ae4558f718e5\n098fc59e-46c7-4d99-9b16-64990e543d75\n0a15ec92-a229-4763-bb14-0ea34a568f8d\n0c192fe8-9cbb-4516-85b3-0ade8bd03886\n0e60b895-3786-45da-8377-9c6b4b6ac5f9\n1c6e92c9-99f0-4e55-9cf2-0c234dc48f99\n233a2a17-77ca-4fb1-9b6b-69223d272a44\n358c20a6-3f9e-4f0e-97ff-c6ce485e2aac\n423dd1ba-798e-40e4-9c4d-b6902674b423\n440b515e-a580-421e-abeb-b159a61ddcbc\n47a1ee2f-2a2a-4576-bf2a-e0e36709c2b8\n511f5417-5d12-434d-ab2e-816901e72a5e\n5744710e-cc2f-4ee8-8809-3b11e89f4bc9\n5a913c68-0590-402c-a531-e57e19379da3\n5bb220d9-2698-4ee4-8404-b9c30c9df609\n82985f06-dc18-4a48-bc1c-b9f4f0098cfe\n95edb821-ddaf-4404-9732-666045e056b4\n9f061a12-e40d-4183-a00e-171812443373\nc26596ff-4d70-4e6a-9a30-c2506bd2f80c\ncb510bfd-1cba-4d9f-a230-cb0976f4bb71\nd2e7ea85-6b44-4317-a0be-1b951587f626\ndf49d893-a74c-421d-bc95-c663042e5b80\ne345eecc-fa47-480f-9e88-67dcc122b164\ne9c8d085-d9cc-4b17-9cdc-059f1f01f19e\neaebaea7-8013-4ceb-9d14-7eb32271373c\nf06ddb64-5fa3-4b77-b166-acb36f7f6042\nfebd0533-8e55-448f-b837-bd0e06f16469\n13cd7ae3-5bc0-4ac4-a62d-4f7c120b9759"}, {"ASB ID": "PV-3", "Control Domain": "Posture and Vulnerability Management", "CIS Controls v7.1 ID(s)": "5.1 - Establish Secure Configurations\n5.5 - Implement Automated Configuration Monitoring Systems", "CIS Controls v8 ID(s)": "4.1 - Establish and Maintain a Secure Configuration Process", "NIST SP800-53 r4 ID(s)": "CM-2: <PERSON><PERSON><PERSON><PERSON>E CONFIGURATION\nCM-6: CONFIGURATION SETTINGS", "PCI-DSS v3.2.1 ID(s)": "2.2\n11.5", "Recommendation": "Define and establish secure configurations for compute resources", "Security Principle": "Define the secure configuration baselines for your compute resources, such as VMs and containers. Use configuration management tools to establish the configuration baseline automatically before or during the compute resource deployment so the environment can be compliant by default after the deployment. Alternatively, use a pre-configured image to build the desired configuration baseline into the compute resource image template. ", "Azure Guidance": "Use Azure recommended operating system baseline (for both Windows and Linux) as a benchmark to define your compute resource configuration baseline. \n\nAdditionally, you can use custom VM image or container image with Azure Policy guest configuration and Azure Automation State Configuration to establish the desired security configuration.  ", "Implementation and additional context": "Linux OS security configuration baseline:\nhttps://docs.microsoft.com/azure/governance/policy/samples/guest-configuration-baseline-linux\n\nWindows OS security configuration baseline:\nhttps://docs.microsoft.com/azure/governance/policy/samples/guest-configuration-baseline-windows\n\nSecurity configuration recommendation for compute resources: \nhttps://docs.microsoft.com/azure/security-center/recommendations-reference\n\nAzure Automation State Configuration Overview: \nhttps://docs.microsoft.com/azure/automation/automation-dsc-overview", "Customer Security Stakeholders:": "Posture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management \n\nInfrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-infrastructure-endpoint\n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "PV-4", "Control Domain": "Posture and Vulnerability Management", "CIS Controls v7.1 ID(s)": "5.4 - Deploy System Configuration Management Tools\n5.5 - Implement Automated Configuration Monitoring Systems\n11.3 - Use Automated Tools to Verify Standard Device Configurations and Detect Changes", "CIS Controls v8 ID(s)": "4.1 - Establish and Maintain a Secure Configuration Process", "NIST SP800-53 r4 ID(s)": "CM-2: <PERSON><PERSON><PERSON><PERSON>E CONFIGURATION\nCM-6: CONFIGURATION SETTINGS", "PCI-DSS v3.2.1 ID(s)": 2.2, "Recommendation": "Audit and enforce secure configurations for compute resources", "Security Principle": "Continuously monitor and alert when there is a deviation from the defined configuration baseline in your compute resources. Enforce the desired configuration according to the baseline configuration by denying the non-compliant configuration or deploy a configuration in compute resources. ", "Azure Guidance": "Use Microsoft Defender for Cloud and Azure Policy guest configuration agent to regularly assess and remediate configuration deviations on your Azure compute resources, including VMs, containers, and others. In addition, you can use Azure Resource Manager templates, custom operating system images, or Azure Automation State Configuration to maintain the security configuration of the operating system.  Microsoft VM templates in conjunction with Azure Automation State Configuration can assist in meeting and maintaining security requirements.\n\nNote: Azure Marketplace VM images published by Microsoft are managed and maintained by Microsoft.\n\n", "Implementation and additional context": "How to implement Microsoft Defender for Cloud vulnerability assessment recommendations:\nhttps://docs.microsoft.com/azure/security-center/security-center-vulnerability-assessment-recommendations\n\nHow to create an Azure virtual machine from an ARM template: \nhttps://docs.microsoft.com/azure/virtual-machines/windows/ps-template\n\nAzure Automation State Configuration overview: \nhttps://docs.microsoft.com/azure/automation/automation-dsc-overview\n\nCreate a Windows virtual machine in the Azure portal: \nhttps://docs.microsoft.com/azure/virtual-machines/windows/quick-create-portal\n\nContainer security in Microsoft Defender for Cloud: \nhttps://docs.microsoft.com/azure/security-center/container-security", "Customer Security Stakeholders:": "Posture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management \n\nInfrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-infrastructure-endpoint\n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops", "Azure Policy Mapping": "[Preview]: vTPM should be enabled on supported virtual machines\n[Preview]: Guest Attestation extension should be installed on supported Windows virtual machines\n[Preview]: Guest Attestation extension should be installed on supported Linux virtual machines\n[Preview]: Windows machines should meet requirements of the Azure compute security baseline\n[Preview]: Secure Boot should be enabled on supported Windows virtual machines\n[Preview]: Guest Attestation extension should be installed on supported Linux virtual machines scale sets\nGuest Configuration extension should be installed on your machines\nVirtual machines' Guest Configuration extension should be deployed with system-assigned managed identity\n[Preview]: Guest Attestation extension should be installed on supported Windows virtual machines scale sets\n[Preview]: Linux machines should meet requirements for the Azure compute security baseline", "Azure Policy GUID": "1c30f9cd-b84c-49cc-aa2c-9288447cc3b3\n1cb4d9c2-f88f-4069-bee0-dba239a57b09\n672fe5a1-2fcd-42d7-b85d-902b6e28c6ff\n72650e9f-97bc-4b2a-ab5f-9781a9fcecbc\n97566dd7-78ae-4997-8b36-1c7bfe0d8121\na21f8c92-9e22-4f09-b759-50500d1d2dda\nae89ebca-1c92-4898-ac2c-9f63decb045c\nd26f7642-7545-4e18-9b75-8c9bbdee3a9a\nf655e522-adff-494d-95c2-52d4f6d56a42\nfc9b3da7-8347-4380-8e70-0a0361d8dedd"}, {"ASB ID": "PV-5", "Control Domain": "Posture and Vulnerability Management", "CIS Controls v7.1 ID(s)": "3.1 - Run Automated Vulnerability Scanning Tools\n3.3 - Protect Dedicated Assessment Accounts\n3.6 - Compare Back-to-back Vulnerability Scans", "CIS Controls v8 ID(s)": "5.5 - Establish and Maintain an Inventory of Service Accounts\n7.1 - Establish and Maintain a Vulnerability Management Process\n7.5 - Perform Automated Vulnerability Scans of Internal Enterprise Assets\n7.6 - Perform Automated Vulnerability Scans of Externally-Exposed Enterprise Assets", "NIST SP800-53 r4 ID(s)": "RA-3: <PERSON><PERSON><PERSON> ASSESSMENT\nRA-5: VULNERABILITY SCANNING\n", "PCI-DSS v3.2.1 ID(s)": "6.1 \n6.2\n6.6\n11.2", "Recommendation": "Perform vulnerability assessments", "Security Principle": "Perform vulnerabilities assessment for your cloud resources at all tiers in a fixed schedule or on-demand. Track and compare the scan results to verify the vulnerabilities are remediated. The assessment should include all type of vulnerabilities, such as vulnerabilities in Azure services, network, web, operating systems, misconfigurations, and so on.\n\nBe aware of the potential risks associated with the privileged access used by the vulnerability scanners. Follow the privileged access security best practice to secure any administrative accounts used for the scanning.", "Azure Guidance": "Follow recommendations from Microsoft Defender for Cloud for performing vulnerability assessments on your Azure virtual machines, container images, and SQL servers. Microsoft Defender for Cloud has a built-in vulnerability scanner for virtual machine scan. Use a third-party solution for performing vulnerability assessments on network devices and applications (e.g., web applications)\n\nExport scan results at consistent intervals and compare the results with previous scans to verify that vulnerabilities have been remediated. When using vulnerability management recommendations suggested by Microsoft Defender for Cloud, you can pivot into the selected scan solution's portal to view historical scan data.\n\nWhen conducting remote scans, do not use a single, perpetual, administrative account. Consider implementing JIT (Just In Time) provisioning methodology for the scan account. Credentials for the scan account should be protected, monitored, and used only for vulnerability scanning.\n\nNote: Azure Defender services (including Defender for server, container registry, App Service, SQL, and DNS) embed certain vulnerability assessment capabilities. The alerts generated from Azure Defender services should be monitored and reviewed together with the result from Microsoft Defender for Cloud vulnerability scanning tool.\n\nNote: Ensure your setup email notifications in Microsoft Defender for Cloud. ", "Implementation and additional context": "How to implement Microsoft Defender for Cloud vulnerability assessment recommendations: https://docs.microsoft.com/azure/security-center/security-center-vulnerability-assessment-recommendations\n\nIntegrated vulnerability scanner for virtual machines: \nhttps://docs.microsoft.com/azure/security-center/built-in-vulnerability-assessment\n\nSQL vulnerability assessment: \nhttps://docs.microsoft.com/azure/azure-sql/database/sql-vulnerability-assessment\n\nExporting Microsoft Defender for Cloud vulnerability scan results: \nhttps://docs.microsoft.com/azure/security-center/built-in-vulnerability-assessment#exporting-results", "Customer Security Stakeholders:": "Posture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management \n\nInfrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-infrastructure-endpoint\n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops", "Azure Policy Mapping": "Vulnerability assessment should be enabled on SQL Managed Instance\nA vulnerability assessment solution should be enabled on your virtual machines\nVulnerability assessment should be enabled on your SQL servers", "Azure Policy GUID": "1b7aa243-30e4-4c9e-bca8-d0d3022b634a\n501541f7-f7e7-4cd6-868c-4190fdad3ac9\nef2a8f2a-b3d9-49cd-a8a8-9a3aaaf647d9"}, {"ASB ID": "PV-6", "Control Domain": "Posture and Vulnerability Management", "CIS Controls v7.1 ID(s)": "3.4 - Deploy Automated Operating System Patch Management Tools\n3.5 - Deploy Automated Software Patch Management Tools\n3.7 - Utilize a Risk-rating Process", "CIS Controls v8 ID(s)": "7.2 - Establish and Maintain a Remediation Process\n7.3 - Perform Automated Operating System Patch Management\n7.4 - Perform Automated Application Patch Management\n7.7 - Remediate Detected Vulnerabilities\n", "NIST SP800-53 r4 ID(s)": "RA-3: <PERSON><PERSON><PERSON> ASSESSMENT\nRA-5: VU<PERSON>NERABI<PERSON>ITY SCANNING\nSI-2: FLAW REMEDIATION", "PCI-DSS v3.2.1 ID(s)": "6.1\n6.2\n6.5\n11.2", "Recommendation": "Rapidly and automatically remediate vulnerabilities", "Security Principle": "Rapidly and automatically deploy patches and updates to remediate vulnerabilities in your cloud resources. Use the appropriate risk-based approach to prioritize the remediation of the vulnerabilities. For example, more severe vulnerabilities in a higher value asset should be addressed as a higher priority. ", "Azure Guidance": "Use Azure Automation Update Management or a third-party solution to ensure that the most recent security updates are installed on your Windows and Linux VMs. For Windows VMs, ensure Windows Update has been enabled and set to update automatically.\n\nFor third-party software, use a third-party patch management solution or System Center Updates Publisher for Configuration Manager.\n\nPrioritize which updates to deploy first using a common risk scoring program (such as Common Vulnerability Scoring System) or the default risk ratings provided by your third-party scanning tool and tailor to your environment. You should also consider which applications present a high security risk and which ones require high uptime.", "Implementation and additional context": "How to configure Update Management for virtual machines in Azure: \nhttps://docs.microsoft.com/azure/automation/update-management/overview\n\nManage updates and patches for your Azure VMs: \nhttps://docs.microsoft.com/azure/automation/update-management/manage-updates-for-vm", "Customer Security Stakeholders:": "Posture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management \n\nInfrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-infrastructure-endpoint\n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops", "Azure Policy Mapping": "Ensure that 'PHP version' is the latest, if used as a part of the Api app\nEnsure that 'PHP version' is the latest, if used as a part of the API app\nVulnerabilities in security configuration on your virtual machine scale sets should be remediated\nEnsure that 'Java version' is the latest, if used as a part of the Web app\nEnsure that 'Java version' is the latest, if used as a part of the Web app\nVulnerabilities in Azure Container Registry images should be remediated\nSQL servers on machines should have vulnerability findings resolved\nEnsure that 'Python version' is the latest, if used as a part of the Web app\nEnsure that 'Python version' is the latest, if used as a part of the Web app\nEnsure that 'Python version' is the latest, if used as a part of the Function app\nEnsure that 'Python version' is the latest, if used as a part of the Function app\nEnsure that 'PHP version' is the latest, if used as a part of the WEB app\nEnsure that 'PHP version' is the latest, if used as a part of the WEB app\nEnsure that 'Python version' is the latest, if used as a part of the Api app\nEnsure that 'Python version' is the latest, if used as a part of the API app\nVulnerabilities should be remediated by a Vulnerability Assessment solution\nSystem updates should be installed on your machines\nEnsure that 'Java version' is the latest, if used as a part of the Api app\nEnsure that 'Java version' is the latest, if used as a part of the API app\nEnsure that 'Java version' is the latest, if used as a part of the Function app\nEnsure that 'Java version' is the latest, if used as a part of the Function app\nSystem updates on virtual machine scale sets should be installed\nVulnerabilities in security configuration on your machines should be remediated\nVulnerabilities in container security configurations should be remediated\n[Preview]: Kubernetes Services should be upgraded to a non-vulnerable Kubernetes version\nSQL databases should have vulnerability findings resolved", "Azure Policy GUID": "1bc1795e-d44a-4d48-9b3b-6fff0fd5f9ba\n1bc1795e-d44a-4d48-9b3b-6fff0fd5f9ba\n3c735d8a-a4ba-4a3a-b7cf-db7754cf57f4\n496223c3-ad65-4ecd-878a-bae78737e9ed\n496223c3-ad65-4ecd-878a-bae78737e9ed\n5f0f936f-2f01-4bf5-b6be-d423792fa562\n6ba6d016-e7c3-4842-b8f2-4992ebc0d72d\n7008174a-fd10-4ef0-817e-fc820a951d73\n7008174a-fd10-4ef0-817e-fc820a951d73\n7238174a-fd10-4ef0-817e-fc820a951d73\n7238174a-fd10-4ef0-817e-fc820a951d73\n7261b898-8a84-4db8-9e04-18527132abb3\n7261b898-8a84-4db8-9e04-18527132abb3\n74c3584d-afae-46f7-a20a-6f8adba71a16\n74c3584d-afae-46f7-a20a-6f8adba71a16\n760a85ff-6162-42b3-8d70-698e268f648c\n86b3d65f-7626-441e-b690-81a8b71cff60\n88999f4c-376a-45c8-bcb3-4058f713cf39\n88999f4c-376a-45c8-bcb3-4058f713cf39\n9d0b6ea4-93e2-4578-bf2f-6bb17d22b4bc\n9d0b6ea4-93e2-4578-bf2f-6bb17d22b4bc\nc3f317a7-a95c-4547-b7e7-11017ebdf2fe\ne1e5fd5d-3e4c-4ce1-8661-7d1873ae6b15\ne8cbc669-f12d-49eb-93e7-9273119e9933\nfb893a29-21bb-418c-a157-e99480ec364c\nfeedbf84-6b99-488c-acc2-71c829aa5ffc"}, {"ASB ID": "PV-7", "Control Domain": "Posture and Vulnerability Management", "CIS Controls v7.1 ID(s)": "20.1 - Establish a Penetration Testing Program\n20.2 - Conduct Regular External and Internal Penetration Tests\n20.3 - Perform Periodic Red Team Exercises", "CIS Controls v8 ID(s)": "18.1 - Establish and Maintain a Penetration Testing Program\n18.2 - Perform Periodic External Penetration Tests\n18.3 - Remediate Penetration Test Findings\n18.4 - Validate Security Measures\n18.5 - Perform Periodic Internal Penetration Tests", "NIST SP800-53 r4 ID(s)": "CA-8: PENETRA<PERSON>ON TESTING\nRA-5: VULNERABILITY SCANNING", "PCI-DSS v3.2.1 ID(s)": "6.6\n11.2\n11.3\n", "Recommendation": "Conduct regular red team operations", "Security Principle": "Simulate real-world attacks to provide a more complete view of your organization's vulnerability. Red team operations and penetration testing complement the traditional vulnerability scanning approach to discover risks. \n\nFollow industry best practices to design, prepare and conduct this kind of testing to ensure it will not cause damage or disruption to your environment. This should always include discussing testing scope and constraints with relevant stakeholders and resource owners. ", "Azure Guidance": "As required, conduct penetration testing or red team activities on your Azure resources and ensure remediation of all critical security findings.\n\nFollow the Microsoft Cloud Penetration Testing Rules of Engagement to ensure your penetration tests are not in violation of Microsoft policies. Use Microsoft's strategy and execution of Red Teaming and live site penetration testing against Microsoft-managed cloud infrastructure, services, and applications.", "Implementation and additional context": "Penetration testing in Azure: \nhttps://docs.microsoft.com/azure/security/fundamentals/pen-testing\n\nPenetration Testing Rules of Engagement: \nhttps://www.microsoft.com/msrc/pentest-rules-of-engagement?rtc=1\n\nMicrosoft Cloud Red Teaming: \nhttps://gallery.technet.microsoft.com/Cloud-Red-Teaming-b837392e\n\nTechnical Guide to Information Security Testing and Assessment:\nhttps://nvlpubs.nist.gov/nistpubs/Legacy/SP/nistspecialpublication800-115.pdf", "Customer Security Stakeholders:": "Posture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management \n\nInfrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-infrastructure-endpoint\n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "LT-1", "Control Domain": "Logging and threat detection", "CIS Controls v7.1 ID(s)": "6.7 - Regularly Review Logs", "CIS Controls v8 ID(s)": "8.11 - Conduct Audit Log Reviews", "NIST SP800-53 r4 ID(s)": "AU-3: CONTENT OF AUDIT RECORDS\nAU-6: AUDIT RE<PERSON><PERSON><PERSON>, ANALYSIS, AND REPORTING\nAU-12: AUDIT GENERATION\nSI-4: INFORMATION SYSTEM MONITORING", "PCI-DSS v3.2.1 ID(s)": "10.6\n10.8\nA3.5", "Recommendation": "Enable threat detection capabilities", "Security Principle": "To support threat detection scenarios, monitor all known resource types for known and expected threats and anomalies. Configure your alert filtering and analytics rules to extract high-quality alerts from log data, agents, or other data sources to reduce false positives.", "Azure Guidance": "Use the threat detection capability of Azure Defender services in Microsoft Defender for Cloud for the respective Azure services. \n\nFor threat detection not included in Azure Defender services, refer to the Azure Security Benchmark service baselines for the respective services to enable the threat detection or security alert capabilities within the service. Extract the alerts to your Azure Monitor or Azure Sentinel to build analytics rules, which hunt threats that match specific criteria across your environment.\n\nFor Operational Technology (OT) environments that include computers that control or monitor Industrial Control System (ICS) or Supervisory Control and Data Acquisition (SCADA) resources, use Defender for IoT to inventory assets and detect threats and vulnerabilities. \n\nFor services that do not have a native threat detection capability, consider collecting the data plane logs and analyze the threats through Azure Sentinel.", "Implementation and additional context": "Introduction to Azure Defender:\nhttps://docs.microsoft.com/azure/security-center/azure-defender\n\nMicrosoft Defender for Cloud security alerts reference guide:\nhttps://docs.microsoft.com/azure/security-center/alerts-reference\n\nCreate custom analytics rules to detect threats:\nhttps://docs.microsoft.com/azure/sentinel/tutorial-detect-threats-custom\n\nCyber threat intelligence with Azure Sentinel:\nhttps://docs.microsoft.com/azure/architecture/example-scenario/data/sentinel-threat-intelligence", "Customer Security Stakeholders:": "Infrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-infrastructure-endpoint\n\nSecurity operations: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-operations-center\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management \n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nThreat intelligence: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-threat-intelligence", "Azure Policy Mapping": "Azure Defender for open-source relational databases should be enabled\nAzure Defender for Key Vault should be enabled\nAzure Defender for App Service should be enabled\nAzure Defender for Storage should be enabled\nAzure Defender for servers should be enabled\nAzure Defender for Kubernetes should be enabled\nAzure Defender for SQL servers on machines should be enabled\nAzure Defender for Azure SQL Database servers should be enabled\n[Preview]: Azure Arc enabled Kubernetes clusters should have Azure Defender's extension installed\n[Preview]: Azure Kubernetes Service clusters should have Azure Defender profile enabled\nAzure Defender for SQL should be enabled for unprotected Azure SQL servers\nAzure Defender for SQL should be enabled for unprotected SQL Managed Instances\n[Preview]: Azure Defender for DNS should be enabled\nWindows Defender Exploit Guard should be enabled on your machines\nAzure Defender for container registries should be enabled\nAzure Defender for Resource Manager should be enabled", "Azure Policy GUID": "0a9fbe0d-c5c4-4da8-87d8-f4fd77338835\n0e6763cc-5078-4e64-889d-ff4d9a839047\n2913021d-f2fd-4f3d-b958-22354e2bdbcb\n308fbb08-4ab8-4e67-9b29-592e93fb94fa\n4da35fc9-c9e7-4960-aec9-797fe7d9051d\n523b5cd1-3e23-492f-a539-13118b6d1e3a\n6581d072-105e-4418-827f-bd446d56421b\n7fe3b40f-802b-4cdd-8bd4-fd799c948cc2\n8dfab9c4-fe7b-49ad-85e4-1e9be085358f\na1840de2-8088-4ea8-b153-b4c723e9cb01\nabfb4388-5bf4-4ad7-ba82-2cd2f41ceae9\nabfb7388-5bf4-4ad7-ba99-2cd2f41cebb9\nbdc59948-5574-49b3-bb91-76b7c986428d\nbed48b13-6647-468e-aa2f-1af1d3f4dd40\nc25d9a16-bc35-4e15-a7e5-9db606bf9ed4\nc3d20c29-b36d-48fe-808b-99a87530ad99"}, {"ASB ID": "LT-2", "Control Domain": "Logging and threat detection", "CIS Controls v7.1 ID(s)": "4.9 - <PERSON><PERSON> and <PERSON><PERSON> on Unsuccessful Administrative Account Login\n6.7 - Regularly Review Logs\n16.13 - <PERSON><PERSON> on Account Login Behavior Deviation\n", "CIS Controls v8 ID(s)": "8.11 - Conduct Audit Log Reviews", "NIST SP800-53 r4 ID(s)": "AU-3: CONTENT OF AUDIT RECORDS\nAU-6: AUDIT RE<PERSON><PERSON><PERSON>, ANALYSIS, AND REPORTING\nAU-12: AUDIT GENERATION\nSI-4: INFORMATION SYSTEM MONITORING", "PCI-DSS v3.2.1 ID(s)": "10.6\n10.8\nA3.5", "Recommendation": "Enable threat detection for  identity and access management", "Security Principle": "Detect threats for identities and access management by monitoring the user and application sign-in and access anomalies. Behavioral patterns such as excessive number of failed login attempts, and deprecated accounts in the subscription, should be alerted. ", "Azure Guidance": "Azure AD provides the following logs that can be viewed in Azure AD reporting or integrated with Azure Monitor, Azure Sentinel or other SIEM/monitoring tools for more sophisticated monitoring and analytics use cases:\n- Sign-ins: The sign-ins report provides information about the usage of managed applications and user sign-in activities.\n- Audit logs: Provides traceability through logs for all changes done by various features within Azure AD. Examples of audit logs include changes made to any resources within Azure AD like adding or removing users, apps, groups, roles and policies.\n- Risky sign-ins: A risky sign-in is an indicator for a sign-in attempt that might have been performed by someone who is not the legitimate owner of a user account.\n- Users flagged for risk: A risky user is an indicator for a user account that might have been compromised.\n\nAzure AD also provides an Identity Protection module to detect, and remediate risks related to user accounts and sign-in behaviors. Examples risks include leaked credentials, sign-in from anonymous or malware linked IP addresses, password spray. The policies in the Azure AD Identity Protection allow you to enforce risk-based MFA authentication in conjunction with Azure Conditional Access on user accounts.\n\nIn addition, Microsoft Defender for Cloud can be configured to alert on deprecated accounts in the subscription and suspicious activities such as an excessive number of failed authentication attempts. In addition to the basic security hygiene monitoring, Microsoft Defender for Cloud's Threat Protection module can also collect more in-depth security alerts from individual Azure compute resources (such as virtual machines, containers, app service), data resources (such as SQL DB and storage), and Azure service layers. This capability allows you to see account anomalies inside the individual resources.\n\nNote: If you are connecting your on-premises Active Directory for synchronization, use the Microsoft Defender for Identity solution to consume your on-premises Active Directory signals to identify, detect, and investigate advanced threats, compromised identities, and malicious insider actions directed at your organization.", "Implementation and additional context": "Audit activity reports in Azure AD:\nhttps://docs.microsoft.com/azure/active-directory/reports-monitoring/concept-audit-logs\n\nEnable Azure Identity Protection:\nhttps://docs.microsoft.com/azure/active-directory/identity-protection/overview-identity-protection\n\nThreat protection in Microsoft Defender for Cloud:\nhttps://docs.microsoft.com/azure/security-center/threat-protection", "Customer Security Stakeholders:": "Infrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-infrastructure-endpoint\n\nSecurity operations: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-operations-center\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management \n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nThreat intelligence: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-threat-intelligence", "Azure Policy Mapping": "Azure Defender for open-source relational databases should be enabled\nAzure Defender for Key Vault should be enabled\nAzure Defender for App Service should be enabled\nAzure Defender for Storage should be enabled\nAzure Defender for servers should be enabled\nAzure Defender for Kubernetes should be enabled\nAzure Defender for SQL servers on machines should be enabled\nAzure Defender for Azure SQL Database servers should be enabled\n[Preview]: Azure Arc enabled Kubernetes clusters should have Azure Defender's extension installed\n[Preview]: Azure Kubernetes Service clusters should have Azure Defender profile enabled\nAzure Defender for SQL should be enabled for unprotected Azure SQL servers\nAzure Defender for SQL should be enabled for unprotected SQL Managed Instances\n[Preview]: Azure Defender for DNS should be enabled\nWindows Defender Exploit Guard should be enabled on your machines\nAzure Defender for container registries should be enabled\nAzure Defender for Resource Manager should be enabled\nResource logs in Azure Kubernetes Service should be enabled", "Azure Policy GUID": "0a9fbe0d-c5c4-4da8-87d8-f4fd77338835\n0e6763cc-5078-4e64-889d-ff4d9a839047\n2913021d-f2fd-4f3d-b958-22354e2bdbcb\n308fbb08-4ab8-4e67-9b29-592e93fb94fa\n4da35fc9-c9e7-4960-aec9-797fe7d9051d\n523b5cd1-3e23-492f-a539-13118b6d1e3a\n6581d072-105e-4418-827f-bd446d56421b\n7fe3b40f-802b-4cdd-8bd4-fd799c948cc2\n8dfab9c4-fe7b-49ad-85e4-1e9be085358f\na1840de2-8088-4ea8-b153-b4c723e9cb01\nabfb4388-5bf4-4ad7-ba82-2cd2f41ceae9\nabfb7388-5bf4-4ad7-ba99-2cd2f41cebb9\nbdc59948-5574-49b3-bb91-76b7c986428d\nbed48b13-6647-468e-aa2f-1af1d3f4dd40\nc25d9a16-bc35-4e15-a7e5-9db606bf9ed4\nc3d20c29-b36d-48fe-808b-99a87530ad99\n245fc9df-fa96-4414-9a0b-3738c2f7341c"}, {"ASB ID": "LT-3", "Control Domain": "Logging and threat detection", "CIS Controls v7.1 ID(s)": "6.2 - Activate Audit Logging\n6.3 - Enable Detailed Logging\n8.8 - Enable Command-Line Audit Logging", "CIS Controls v8 ID(s)": "8.2 - Collect Audit Logs\n8.5 - Collect Detailed Audit Logs\n8.12 - Collect Service Provider Logs", "NIST SP800-53 r4 ID(s)": "AU-3: CONTENT OF AUDIT RECORDS\nAU-6: AUDIT RE<PERSON><PERSON><PERSON>, ANALYSIS, AND REPORTING\nAU-12: AUDIT GENERATION\nSI-4: INFORMATION SYSTEM MONITORING", "PCI-DSS v3.2.1 ID(s)": "10.1\n10.2\n10.3", "Recommendation": "Enable logging for security investigation", "Security Principle": "Enable logging for your cloud resources to meet the requirements for security incident investigations and security response and compliance purposes. ", "Azure Guidance": "Enable logging capability for resources at the different tiers, such as logs for Azure resources, operating systems and applications inside in your VMs and other log types. \n\nBe mindful about different type of logs for security, audit, and other operation logs at the management/control plane and data plane tiers. There are three types of the logs available at the Azure platform:\n- Azure resource log: Logging of operations that are performed within an Azure resource (the data plane). For example, getting a secret from a key vault or making a request to a database. The content of resource logs varies by the Azure service and resource type.\n- Azure activity log: Logging of operations on each Azure resource at the subscription layer, from the outside (the management plane). You can use the Activity Log to determine the what, who, and when for any write operations (PUT, POST, DELETE) taken on the resources in your subscription. There is a single Activity log for each Azure subscription.\n- Azure Active Directory logs: Logs of the history of sign-in activity and audit trail of changes made in the Azure Active Directory for a particular tenant.\n\nYou can also use Microsoft Defender for Cloud and Azure Policy to enable resource logs and log data collecting on Azure resources.", "Implementation and additional context": "Understand logging and different log types in Azure:\nhttps://docs.microsoft.com/azure/azure-monitor/platform/platform-logs-overview\n\nUnderstand Microsoft Defender for Cloud data collection:\nhttps://docs.microsoft.com/azure/security-center/security-center-enable-data-collection\n\nEnable and configure antimalware monitoring:\nhttps://docs.microsoft.com/azure/security/fundamentals/antimalware#enable-and-configure-antimalware-monitoring-using-powershell-cmdlets\n\nOperating systems and application logs inside in your compute resources: \nhttps://docs.microsoft.com/azure/azure-monitor/agents/data-sources#operating-system-guest", "Customer Security Stakeholders:": "Infrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-infrastructure-endpoint\n\nSecurity operations: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-operations-center\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management \n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nThreat intelligence: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-threat-intelligence", "Azure Policy Mapping": "Resource logs in Azure Data Lake Store should be enabled\nResource logs in Logic Apps should be enabled\nResource logs in IoT Hub should be enabled\nResource logs in Batch accounts should be enabled\nResource logs in Virtual Machine Scale Sets should be enabled\nResource logs in Event Hub should be enabled\nAuditing on SQL server should be enabled\nResource logs in Search services should be enabled\nDiagnostic logs in App Services should be enabled\nResource logs in Data Lake Analytics should be enabled\nResource logs in Key Vault should be enabled\nResource logs in Service Bus should be enabled\nResource logs in Azure Stream Analytics should be enabled", "Azure Policy GUID": "057ef27e-665e-4328-8ea3-04b3122bd9fb\n34f95f76-5386-4de7-b824-0d8478470c9d\n383856f8-de7f-44a2-81fc-e5135b5c2aa4\n428256e6-1fac-4f48-a757-df34c2b3336d\n7c1b1214-f927-48bf-8882-84f0af6588b1\n83a214f7-d01a-484b-91a9-ed54470c9a6a\na6fb4358-5bf4-4ad7-ba82-2cd2f41ce5e9\nb4330a05-a843-4bc8-bf9a-cacce50c67f4\nb607c5de-e7d9-4eee-9e5c-83f1bcee4fa0\nc95c74d9-38fe-4f0d-af86-0c7d626a315c\ncf820ca0-f99e-4f3e-84fb-66e913812d21\nf8d36e2f-389b-4ee4-898d-21aeb69a0f45\nf9be5368-9bf5-4b84-9e0a-7850da98bb46"}, {"ASB ID": "LT-4", "Control Domain": "Logging and threat detection", "CIS Controls v7.1 ID(s)": "6.2 - Activate Audit Logging\n6.3 - Enable Detailed Logging\n7.6 - Log All URL Requests\n8.7 - Enable DNS Query Logging\n12.8 - Deploy NetFlow Collection on Networking Boundary Devices", "CIS Controls v8 ID(s)": "8.2 - Collect Audit Logs\n8.5 - Collect Detailed Audit Logs\n8.6 - Collect DNS Query Audit Logs\n8.7 - Collect URL Request Audit Logs\n13.6 - Collect Network Traffic Flow Logs", "NIST SP800-53 r4 ID(s)": "AU-3: CONTENT OF AUDIT RECORDS\nAU-6: AUDIT RE<PERSON><PERSON><PERSON>, ANALYSIS, AND REPORTING\nAU-12: AUDIT GENERATION\nSI-4: INFORMATION SYSTEM MONITORING", "PCI-DSS v3.2.1 ID(s)": 10.8, "Recommendation": "Enable network logging for security investigation\n", "Security Principle": "Enable logging for your network services to support network-related incident investigations, threat hunting, and security alert generation. The network logs may include logs from network services such as IP filtering, network and application firewall, DNS, flow monitoring and so on.", "Azure Guidance": "Enable and collect network security group (NSG) resource logs, NSG flow logs, Azure Firewall logs, and Web Application Firewall (WAF) logs for security analysis to support incident investigations, and security alert generation. You can send the flow logs to an Azure Monitor Log Analytics workspace and then use Traffic Analytics to provide insights.\n\nCollect DNS query logs to assist in correlating other network data.", "Implementation and additional context": "How to enable network security group flow logs: \nhttps://docs.microsoft.com/azure/network-watcher/network-watcher-nsg-flow-logging-portal\n\nAzure Firewall logs and metrics:\nhttps://docs.microsoft.com/azure/firewall/logs-and-metrics\n\nAzure networking monitoring solutions in Azure Monitor:\nhttps://docs.microsoft.com/azure/azure-monitor/insights/azure-networking-analytics\n\nGather insights about your DNS infrastructure with the DNS Analytics solution: \nhttps://docs.microsoft.com/azure/azure-monitor/insights/dns-analytics\n\n", "Customer Security Stakeholders:": "Security operations: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-operations-center\n\nInfrastructure and endpoint security\n\nApplication security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nThreat intelligence: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-threat-intelligence", "Azure Policy Mapping": "[Preview]: Network traffic data collection agent should be installed on Linux virtual machines\n[Preview]: Network traffic data collection agent should be installed on Windows virtual machines", "Azure Policy GUID": "04c4380f-3fae-46e8-96c9-30193528f602\n2f2ee1de-44aa-4762-b6bd-0893fc3f306d"}, {"ASB ID": "LT-5", "Control Domain": "Logging and threat detection", "CIS Controls v7.1 ID(s)": "6.5 - Central Log Management\n6.6 - Deploy SIEM or Log Analytic tool\n6.7 - Regularly Review Logs\n8.6 - Centralize Anti-Malware Logging", "CIS Controls v8 ID(s)": "8.9 - Centralize Audit Logs\n8.11 - Conduct Audit Log Reviews\n13.1 - Centralize Security Event Alerting", "NIST SP800-53 r4 ID(s)": "AU-3: CONTENT OF AUDIT RECORDS\nAU-6: AUDIT RE<PERSON><PERSON><PERSON>, ANALYSIS, AND REPORTING\nAU-12: AUDIT GENERATION\nSI-4: INFORMATION SYSTEM MONITORING", "PCI-DSS v3.2.1 ID(s)": NaN, "Recommendation": "Centralize security log management and analysis", "Security Principle": "Centralize logging storage and analysis to enable correlation across log data. For each log source, ensure that you have assigned a data owner, access guidance, storage location, what tools are used to process and access the data, and data retention requirements.", "Azure Guidance": "Ensure that you are integrating Azure activity logs into a centralized Log Analytics workspace. Use Azure Monitor to query and perform analytics and create alert rules using the logs aggregated from Azure services, endpoint devices, network resources, and other security systems.\n\nIn addition, enable and onboard data to Azure Sentinel which provides the security information event management (SIEM) and security orchestration automated response (SOAR) capability.", "Implementation and additional context": "How to collect platform logs and metrics with Azure Monitor:\nhttps://docs.microsoft.com/azure/azure-monitor/platform/diagnostic-settings\n\nHow to onboard Azure Sentinel: \nhttps://docs.microsoft.com/azure/sentinel/quickstart-onboard", "Customer Security Stakeholders:": "Security architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nInfrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-infrastructure-endpoint", "Azure Policy Mapping": "Auto provisioning of the Log Analytics agent should be enabled on your subscription\n[Preview]: Log Analytics agent should be installed on your Linux Azure Arc machines\nLog Analytics agent should be installed on your virtual machine scale sets for Azure Security Center monitoring\nLog Analytics agent should be installed on your virtual machine for Azure Security Center monitoring\nLog Analytics agent health issues should be resolved on your machines\n[Preview]: Log Analytics agent should be installed on your Windows Azure Arc machines", "Azure Policy GUID": "475aae12-b88a-4572-8b36-9b712b2b3a17\n842c54e8-c2f9-4d79-ae8d-38d8b8019373\na3a6ea0c-e018-4933-9ef0-5aaa1501449b\na4fe33eb-e377-4efb-ab31-0784311bc499\nd62cfe2b-3ab0-4d41-980d-76803b58ca65\nd69b1763-b96d-40b8-a2d9-ca31e9fd0d3e"}, {"ASB ID": "LT-6", "Control Domain": "Logging and threat detection", "CIS Controls v7.1 ID(s)": "6.4 - Ensure Adequate Storage for Logs", "CIS Controls v8 ID(s)": "8.3 - Ensure Adequate Audit Log Storage\n8.10 - Retain Audit Logs", "NIST SP800-53 r4 ID(s)": "AU-11: AUDIT RECORD RETENTION", "PCI-DSS v3.2.1 ID(s)": "10.5\n10.7", "Recommendation": "Configure log storage retention", "Security Principle": "Plan your log retention strategy according to your compliance, regulation, and business requirements. Configure the log retention policy at the individual logging services to ensure the logs are archived appropriately.", "Azure Guidance": "Logs such as Azure Activity Logs events are retained for 90 days then deleted. You should create a diagnostic setting and route the log entries to another location (such as Azure Monitor Log Analytics workspace, Event Hubs or Azure Storage) based on your needs. This strategy also applies to the other resource logs and resources managed by yourself such as logs in the operating systems and applications inside the VMs. \n\nYou have the log retention option as below:\n- Use Azure Monitor Log Analytics workspace for a log retention period of up to 1 year or per your response team requirements. \n- Use Azure Storage, Data Explorer or Data Lake for long-term and archival storage for greater than 1 year and to meet your security compliance requirements. \n- Use Azure Event Hubs to forward logs to outside of Azure. \n\nNote: Azure Sentinel uses Log Analytics workspace as its backend for log storage. You should consider a long-term storage strategy if you plan to retain SIEM logs for longer time.", "Implementation and additional context": "Change the data retention period in Log Analytics: \nhttps://docs.microsoft.com/azure/azure-monitor/platform/manage-cost-storage#change-the-data-retention-period\n\nHow to configure retention policy for Azure Storage account logs:\nhttps://docs.microsoft.com/azure/storage/common/storage-monitor-storage-account#configure-logging\n\nMicrosoft Defender for Cloud alerts and recommendations export: https://docs.microsoft.com/azure/security-center/continuous-export", "Customer Security Stakeholders:": "Security architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nSecurity operations: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-operations-center\n\nSecurity compliance management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-compliance-management", "Azure Policy Mapping": "SQL servers with auditing to storage account destination should be configured with 90 days retention or higher", "Azure Policy GUID": "89099bee-89e0-4b26-a5f4-************"}, {"ASB ID": "LT-7", "Control Domain": "Logging and threat detection", "CIS Controls v7.1 ID(s)": "6.1 - Utilize Three Synchronized Time Sources", "CIS Controls v8 ID(s)": "8.4 - Standardize Time Synchronization", "NIST SP800-53 r4 ID(s)": "AU-8: TIME STAMPS", "PCI-DSS v3.2.1 ID(s)": 10.4, "Recommendation": "Use approved time synchronization sources", "Security Principle": "Use approved time synchronization sources for your logging time stamp which include date, time and time zone information.", "Azure Guidance": "Microsoft maintains time sources for most Azure PaaS and SaaS services. For your compute resources operating systems, use a Microsoft default NTP server for time synchronization unless you have a specific requirement. If you need to stand up your own network time protocol (NTP) server, ensure you secure the UDP service port 123.\n\nAll logs generated by resources within Azure provide time stamps with the time zone specified by default.", "Implementation and additional context": "How to configure time synchronization for Azure Windows compute resources: \nhttps://docs.microsoft.com/azure/virtual-machines/windows/time-sync\n\nHow to configure time synchronization for Azure Linux compute resources:  \nhttps://docs.microsoft.com/azure/virtual-machines/linux/time-sync\n\nHow to disable inbound UDP for Azure services: \nhttps://support.microsoft.com/help/4558520/how-to-disable-inbound-udp-for-azure-services", "Customer Security Stakeholders:": "Policy and standards: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-policy-standards\n\nApplication Security and DevOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nInfrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-infrastructure-endpoint", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "AM-1", "Control Domain": "Asset Management", "CIS Controls v7.1 ID(s)": "1.1 - Utilize an Active Discovery Tool\n1.2 - Use a Passive Asset Discovery Tool\n1.4 - Maintain Detailed Asset Inventory\n1.5 - Maintain Asset Inventory Information\n2.1 - Maintain Inventory of Authorized Software\n", "CIS Controls v8 ID(s)": "1.1 - Establish and Maintain Detailed Enterprise Asset Inventory\n1.5 - Use a Passive Asset Discovery Tool\n2.1 - Establish and Maintain a Software Inventory\n2.4 - Utilize Automated Software Inventory Tools\n", "NIST SP800-53 r4 ID(s)": "CM-8: INFORMATION SYSTEM COMPONENT INVENTORY\nPM-5: INFORMATION SYSTEM INVENTORY", "PCI-DSS v3.2.1 ID(s)": 2.4, "Recommendation": "Track asset inventory and their risks", "Security Principle": "Track your asset inventory by query and discover all your cloud resources. Logically organize your assets by tagging and grouping your assets based on their service nature, location, or other characteristics. Ensure your security organization has access to a continuously updated inventory of assets.\n\nEnsure your security organization can monitor the risks of the cloud assets by always having security insights and risks aggregated centrally", "Azure Guidance": "The Microsoft Defender for Cloud inventory feature and Azure Resource Graph can query for and discover all resources in your subscriptions, including Azure services, applications, and network resources. Logically organize assets according to your organization's taxonomy using Tags as well as other metadata in Azure (Name, Description, and Category).\n\nEnsure that security organizations have access to a continuously updated inventory of assets on Azure. Security teams often need this inventory to evaluate their organization's potential exposure to emerging risks, and as an input to continuously security improvements.\n\nEnsure security organizations are granted Security Reader permissions in your Azure tenant and subscriptions so they can monitor for security risks using Microsoft Defender for Cloud. Security Reader permissions can be applied broadly to an entire tenant (Root Management Group) or scoped to management groups or specific subscriptions.\n\nNote: Additional permissions might be required to get visibility into workloads and services.", "Implementation and additional context": "How to create queries with Azure Resource Graph Explorer: \nhttps://docs.microsoft.com/azure/governance/resource-graph/first-query-portal\n\nMicrosoft Defender for Cloud asset inventory management: \nhttps://docs.microsoft.com/azure/security-center/asset-inventory\n\nFor more information about tagging assets, see the resource naming and tagging decision guide:\nhttps://docs.microsoft.com/azure/cloud-adoption-framework/decision-guides/resource-tagging/?toc=/azure/azure-resource-manager/management/toc.json\n\nOverview of Security Reader Role:\nhttps://docs.microsoft.com/azure/role-based-access-control/built-in-roles#security-reader", "Customer Security Stakeholders:": "Infrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-infrastructure-endpoint\n\nSecurity Compliance Management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-compliance-management", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "AM-2", "Control Domain": "Asset Management", "CIS Controls v7.1 ID(s)": "2.7 - Utilize Application Whitelisting\n2.8 - Implement Application Whitelisting of Libraries\n2.9 - Implement Application Whitelisting of Scripts\n9.2 - Ensure Only Approved Ports, Protocols, and Services Are Running", "CIS Controls v8 ID(s)": "2.5 - Allowlist Authorized Software\n2.6 - Allowlist Authorized Libraries\n2.7 - Allowlist Authorized Scripts\n4.8 - Uninstall or Disable Unnecessary Services on Enterprise Assets and Software", "NIST SP800-53 r4 ID(s)": "CM-8: INFORMATION SYSTEM COMPONENT INVENTORY\nPM-5: INFORMATION SYSTEM INVENTORY", "PCI-DSS v3.2.1 ID(s)": 6.3, "Recommendation": "Use only approved services\n", "Security Principle": "Ensure that only approved cloud services can be used, by auditing and restricting which services users can provision in the environment.", "Azure Guidance": "Use Azure Policy to audit and restrict which services users can provision in your environment. Use Azure Resource Graph to query for and discover resources within their subscriptions. You can also use Azure Monitor to create rules to trigger alerts when a non-approved service is detected.", "Implementation and additional context": "Configure and manage Azure Policy: \nhttps://docs.microsoft.com/azure/governance/policy/tutorials/create-and-manage\n\nHow to deny a specific resource type with Azure Policy: \nhttps://docs.microsoft.com/azure/governance/policy/samples/not-allowed-resource-types\n\nHow to create queries with Azure Resource Graph Explorer: \nhttps://docs.microsoft.com/azure/governance/resource-graph/first-query-portal", "Customer Security Stakeholders:": "Security Compliance Management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-compliance-management\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management", "Azure Policy Mapping": "Virtual machines should be migrated to new Azure Resource Manager resources\nStorage accounts should be migrated to new Azure Resource Manager resources", "Azure Policy GUID": "1d84d5fb-01f6-4d12-ba4f-4a26081d403d\n37e0d2fe-28a5-43d6-a273-67d37d1f5606"}, {"ASB ID": "AM-3", "Control Domain": "Asset Management", "CIS Controls v7.1 ID(s)": "1.4 - Maintain Detailed Asset Inventory\n1.5 - Maintain Asset Inventory Information\n2.1 - Maintain Inventory of Authorized Software\n2.4 - Track Software Inventory Information\n", "CIS Controls v8 ID(s)": "1.1 - Establish and Maintain Detailed Enterprise Asset Inventory\n2.1 - Establish and Maintain a Software Inventory", "NIST SP800-53 r4 ID(s)": "CM-8: INFORMATION SYSTEM COMPONENT INVENTORY\nCM-7: LEAST FUNCTIONALITY", "PCI-DSS v3.2.1 ID(s)": 2.4, "Recommendation": "Ensure security of asset lifecycle management\n", "Security Principle": "Ensure security attributes or configurations of the assets are always updated during the asset lifecycle.", "Azure Guidance": "Establish or update security policies/process that address asset lifecycle management processes for potentially high impact modifications. These modifications include changes to identity providers and access, data sensitivity, network configuration, and administrative privilege assignment.\n\nRemove Azure resources when they are no longer needed. ", "Implementation and additional context": "Delete Azure resource group and resource: \nhttps://docs.microsoft.com/azure/azure-resource-manager/management/delete-resource-group", "Customer Security Stakeholders:": "Infrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-infrastructure-endpoint\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management\n\nSecurity Compliance Management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-compliance-management", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "AM-4", "Control Domain": "Asset Management", "CIS Controls v7.1 ID(s)": "14.6 - Protect Information Through Access Control Lists", "CIS Controls v8 ID(s)": "3.3 - Configure Data Access Control Lists", "NIST SP800-53 r4 ID(s)": "AC-3: ACCESS ENFORCEMENT", "PCI-DSS v3.2.1 ID(s)": NaN, "Recommendation": "Limit access to asset management\n", "Security Principle": "Limit users' access to asset management features, to avoid accidental or malicious modification of the assets in your cloud.", "Azure Guidance": "Azure Resource Manager is the deployment and management service for Azure. It provides a management layer that enables you to create, update, and delete resources (assets) in Azure. Use Azure AD Conditional Access to limit users' ability to interact with Azure Resource Manager by configuring \"Block access\" for the \"Microsoft Azure Management\" App.", "Implementation and additional context": "How to configure Conditional Access to block access to Azure Resources Manager: \nhttps://docs.microsoft.com/azure/role-based-access-control/conditional-access-azure-management", "Customer Security Stakeholders:": "Posture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management\n\nInfrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-infrastructure-endpoint", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "AM-5", "Control Domain": "Asset Management", "CIS Controls v7.1 ID(s)": "2.7 - Utilize Application Whitelisting\n2.8 - Implement Application Whitelisting of Libraries\n2.9 - Implement Application Whitelisting of Scripts\n9.2 - Ensure Only Approved Ports, Protocols, and Services Are Running", "CIS Controls v8 ID(s)": "2.5 - Allowlist Authorized Software\n2.6 - Allowlist Authorized Libraries\n2.7 - Allowlist Authorized Scripts\n4.8 - Uninstall or Disable Unnecessary Services on Enterprise Assets and Software", "NIST SP800-53 r4 ID(s)": "CM-8: INFORMA<PERSON><PERSON> SYSTEM COMPONENT INVENTORY\nCM-7: LEAST FUNCTIONALITY\nCM-10: SOFTWARE USAGE RESTRICTIONS\nCM-11: USER-INSTALLED SOFTWARE", "PCI-DSS v3.2.1 ID(s)": 6.3, "Recommendation": "Use only approved applications in virtual machine", "Security Principle": "Ensure that only authorized software executes by creating an allow list and block the unauthorized software from executing in your environment. ", "Azure Guidance": "Use Microsoft Defender for Cloud adaptive application controls to discover and generate an application allow list. You can also use ASC adaptive application controls to ensure that only authorized software executes and all unauthorized software is blocked from executing on Azure Virtual Machines.\n\nUse Azure Automation Change Tracking and Inventory to automate the collection of inventory information from your Windows and Linux VMs. Software name, version, publisher, and refresh time are available from the Azure portal. To get the software installation date and other information, enable guest-level diagnostics and direct the Windows Event Logs to Log Analytics workspace.\n\nDepending on the type of scripts, you can use operating system-specific configurations or third-party resources to limit users' ability to execute scripts in Azure compute resources.\n\nYou can also use a third-party solution to discover and identify unapproved software.", "Implementation and additional context": "How to use Microsoft Defender for Cloud adaptive application controls: \nhttps://docs.microsoft.com/azure/security-center/security-center-adaptive-application\n\nUnderstand Azure Automation Change Tracking and Inventory: \nhttps://docs.microsoft.com/azure/automation/change-tracking\n\nHow to control PowerShell script execution in Windows environments:\nhttps://docs.microsoft.com/powershell/module/microsoft.powershell.security/set-executionpolicy?view=powershell-6", "Customer Security Stakeholders:": "Infrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-infrastructure-endpoint\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management\n\nSecurity Compliance Management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-compliance-management", "Azure Policy Mapping": "Allowlist rules in your adaptive application control policy should be updated\nAdaptive application controls for defining safe applications should be enabled on your machines", "Azure Policy GUID": "123a3936-f020-408a-ba0c-47873faf1534\n47a6b606-51aa-4496-8bb7-64b11cf66adc"}, {"ASB ID": "ES-1", "Control Domain": "Endpoint security", "CIS Controls v7.1 ID(s)": "9.4 - Apply Host-Based Firewalls or Port Filtering", "CIS Controls v8 ID(s)": "13.7 - Deploy a Host-Based Intrusion Prevention Solution", "NIST SP800-53 r4 ID(s)": "SC-3: SECURITY FUNCTION ISOLATION\nSI-2: FLAW REMEDIATION\nSI-3: MALICIOUS CODE PROTECTION\nSI-16 MEMORY PROTECTION", "PCI-DSS v3.2.1 ID(s)": 11.5, "Recommendation": "Use Endpoint Detection and Response (EDR)", "Security Principle": "Enable Endpoint Detection and Response (EDR) capabilities for VMs and integrate with SIEM and security operations processes.", "Azure Guidance": "Azure Defender for servers (with Microsoft Defender for Endpoint integrated) provides EDR capability to prevent, detect, investigate, and respond to advanced threats. \n\nUse Azure Security Center to deploy Azure Defender for servers for your endpoint and integrate the alerts to your SIEM solution such as Azure Sentinel. ", "Implementation and additional context": "Azure Defender for servers introduction:\nhttps://docs.microsoft.com/azure/security-center/defender-for-servers-introduction\n\nMicrosoft Defender for Endpoint overview: \nhttps://docs.microsoft.com/microsoft-365/security/defender-endpoint/microsoft-defender-endpoint?view=o365-worldwide\n\nMicrosoft Defender for Cloud feature coverage for machines:\nhttps://docs.microsoft.com/azure/security-center/security-center-services?tabs=features-windows\n\nConnector for Defender for servers integration into SIEM:\nhttps://docs.microsoft.com/azure/security-center/security-center-wdatp?WT.mc_id=Portal-Microsoft_Azure_Security_CloudNativeCompute&tabs=windows", "Customer Security Stakeholders:": "Infrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security\n\nThreat intelligence: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-threat-intelligence\n\nSecurity Compliance Management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-compliance-management\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-compliance-management", "Azure Policy Mapping": "Azure Defender for servers should be enabled", "Azure Policy GUID": "4da35fc9-c9e7-4960-aec9-797fe7d9051d"}, {"ASB ID": "ES-2", "Control Domain": "Endpoint security", "CIS Controls v7.1 ID(s)": "8.1 - Utilize Centrally Managed Anti-malware Software", "CIS Controls v8 ID(s)": "10.1 - Deploy and Maintain Anti-Malware Software", "NIST SP800-53 r4 ID(s)": "SC-3: SECURITY FUNCTION ISOLATION\nSI-2: FLAW REMEDIATION\nSI-3: MALICIOUS CODE PROTECTION\nSI-16 MEMORY PROTECTION", "PCI-DSS v3.2.1 ID(s)": 5.1, "Recommendation": "Use modern anti-malware software", "Security Principle": "Use anti-malware solutions capable of real-time protection and periodic scanning. ", "Azure Guidance": "Microsoft Defender for Cloud can automatically identify the use of a number of popular anti-malware solutions for your virtual machines and on-premises machines with Azure Arc configured, and report the endpoint protection running status and make recommendations.\n\nMicrosoft Defender Antivirus is the default anti-malware solution for Windows server 2016 and above. For Windows server 2012 R2, use Microsoft Antimalware extension to enable SCEP (System Center Endpoint Protection), and Microsoft Defender for Cloud to discover and assess the health status.  For Linux VMs, use Microsoft Defender for Endpoint on Linux. \n\nNote: You can also use Microsoft Defender for Cloud's Defender for Storage to detect malware uploaded to Azure Storage accounts. ", "Implementation and additional context": "Supported endpoint protection solutions: \nhttps://docs.microsoft.com/azure/security-center/security-center-services?tabs=features-windows#supported-endpoint-protection-solutions-\n\nHow to configure Microsoft Antimalware for Cloud Services and virtual machines: \nhttps://docs.microsoft.com/azure/security/fundamentals/antimalware\n\n", "Customer Security Stakeholders:": "Infrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security\n\nThreat intelligence: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-threat-intelligence\n\nSecurity Compliance Management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-compliance-management\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-compliance-management", "Azure Policy Mapping": "Endpoint protection should be installed on your machines\nEndpoint protection solution should be installed on virtual machine scale sets\nEndpoint protection health issues should be resolved on your machines\nMonitor missing Endpoint Protection in Azure Security Center\nWindows Defender Exploit Guard should be enabled on your machines", "Azure Policy GUID": "1f7c564c-0a90-4d44-b7e1-9d456cffaee8\n26a828e1-e88f-464e-bbb3-c134a282b9de\n8e42c1f2-a2ab-49bc-994a-12bcd0dc4ac2\naf6cd1bd-1635-48cb-bde7-5b15693900b9\nbed48b13-6647-468e-aa2f-1af1d3f4dd40"}, {"ASB ID": "ES-3", "Control Domain": "Endpoint security", "CIS Controls v7.1 ID(s)": "8.2 - Ensure Anti-Malware Software and Signatures are Updated", "CIS Controls v8 ID(s)": "10.2 - Configure Automatic Anti-Malware Signature Updates", "NIST SP800-53 r4 ID(s)": "SI-2: FLAW REMEDIATION\nSI-3: MALICIOUS CODE PROTECTION", "PCI-DSS v3.2.1 ID(s)": "5.2\n5.3", "Recommendation": "Ensure anti-malware software and signatures are updated", "Security Principle": "Ensure anti-malware signatures are updated rapidly and consistently for the anti-malware solution.", "Azure Guidance": "Follow recommendations in Microsoft Defender for Cloud: \"Compute & Apps\" to keep all endpoints up to date with the latest signatures. Microsoft Antimalware will automatically install the latest signatures and engine updates by default. For Linux, ensure the signatures are updated in the third-party anti-malware solution.", "Implementation and additional context": "How to deploy Microsoft Antimalware for Cloud Services and virtual machine:  \nhttps://docs.microsoft.com/azure/security/fundamentals/antimalware\n\nEndpoint protection assessment and recommendations in Microsoft Defender for Cloud: \nhttps://docs.microsoft.com/azure/security-center/security-center-endpoint-protection\n", "Customer Security Stakeholders:": "Infrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security\n\nThreat intelligence: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-threat-intelligence\n\nSecurity Compliance Management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-compliance-management\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-compliance-management", "Azure Policy Mapping": "Endpoint protection health issues should be resolved on your machines", "Azure Policy GUID": "8e42c1f2-a2ab-49bc-994a-12bcd0dc4ac2"}, {"ASB ID": "BR-1", "Control Domain": "Backup and recovery", "CIS Controls v7.1 ID(s)": "10.1 - Ensure Regular Automated Backups", "CIS Controls v8 ID(s)": "11.2 - Perform Automated Backups", "NIST SP800-53 r4 ID(s)": "CP-2: CONTINGENCY PLAN\nCP-4: CONTINGENCY PLAN TESTING\nCP-9: INFORMATION SYSTEM BACKUP", "PCI-DSS v3.2.1 ID(s)": NaN, "Recommendation": "Ensure regular automated backups", "Security Principle": "Ensure backup of business-critical resources, either during resource creation or enforced through policy for existing resources.", "Azure Guidance": "For Azure Backup supported resources, enable Azure Backup and configure the backup source (such as Azure VMs, SQL Server, HANA databases, or File Shares) on the desired frequency and retention period. For Azure VM, you can use Azure Policy to have backup automatically enabled using Azure Policy.\n\nFor resources not supported by Azure Backup, enable the backup as part of its resource creation. Where applicable, use built-in policies (Azure Policy) to ensure that your Azure resources are configured for backup. ", "Implementation and additional context": "How to enable Azure Backup:\nhttps://docs.microsoft.com/azure/backup/\n\nAuto-Enable Backup on VM Creation using Azure Policy:\nhttps://docs.microsoft.com/azure/backup/backup-azure-auto-enable-backup", "Customer Security Stakeholders:": "Policy and standards: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-policy-standards\n\nSecurity architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nInfrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-infrastructure-endpoint\n\nIncident preparation: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-incident-preparation", "Azure Policy Mapping": "Azure Backup should be enabled for Virtual Machines\nGeo-redundant backup should be enabled for Azure Database for MariaDB\nGeo-redundant backup should be enabled for Azure Database for PostgreSQL\nGeo-redundant backup should be enabled for Azure Database for MySQL", "Azure Policy GUID": "013e242c-8828-4970-87b3-ab247555486d\n0ec47710-77ff-4a3d-9181-6aa50af424d0\n48af4db5-9b8b-401c-8e74-076be876a430\n82339799-d096-41ae-8538-b108becf0970"}, {"ASB ID": "BR-2", "Control Domain": "Backup and recovery", "CIS Controls v7.1 ID(s)": "10.4 - Ensure Protection of Backups", "CIS Controls v8 ID(s)": "11.3 - Protect Recovery Data", "NIST SP800-53 r4 ID(s)": "CP-6: <PERSON><PERSON><PERSON><PERSON> STORAGE SITE\nCP-9: INFORMATION SYSTEM BACKUP", "PCI-DSS v3.2.1 ID(s)": 3.4, "Recommendation": "Protect backup and recovery data", "Security Principle": "Ensure backup data and operations are protected from data exfiltration, data compromise, ransomware/malware and malicious insiders. The security controls that should be applied include user and network access control, data encryption at-rest and in-transit. ", "Azure Guidance": "Use Azure RBAC and multi-factor-authentication to secure the critical Azure Backup operations (such as delete, change retention, updates to backup config). For Azure Backup supported resources, use Azure RBAC to segregate duties and enable fine grained access, and create private endpoints within your Azure Virtual Network to securely backup and restore data from your Recovery Services vaults.\n\nFor Azure Backup supported resources, backup data is automatically encrypted using Azure platform-managed keys with 256-bit AES encryption. You can also choose to encrypt the backups using customer managed key. In this case, ensure this customer-managed key in the Azure Key Vault is also in the backup scope. If you use customer-managed key options, use soft delete and purge protection in Azure Key Vault to protect keys from accidental or malicious deletion. For on-premises backups using Azure Backup, encryption-at-rest is provided using the passphrase you provide. \n\nSafeguard backup data from accidental or malicious deletion (such as ransomware attacks/attempts to encrypt or tamper backup data. For Azure Backup supported resources, enable soft delete to ensure recovery of items with no data loss for up to 14 days after an unauthorized deletion, and enable multifactor authentication using a PIN generated in the Azure portal. Also enable cross-region restore to ensure backup data is restorable when there is a disaster in primary region. \n\nNote: If you use resource's native backup feature or backup services other than Azure Backup, refer to the Azure Security Benchmark (and service baselines) to implement the above controls. ", "Implementation and additional context": "Overview of security features in Azure Backup:\nhttps://docs.microsoft.com/azure/backup/security-overview\n\nEncryption of backup data using customer-managed keys:\nhttps://docs.microsoft.com/azure/backup/encryption-at-rest-with-cmk\n\nSecurity features to help protect hybrid backups from attacks:\nhttps://docs.microsoft.com/azure/backup/backup-azure-security-feature#prevent-attacks\n\nAzure Backup - set cross region restore\nhttps://docs.microsoft.com/azure/backup/backup-create-rs-vault#set-cross-region-restore", "Customer Security Stakeholders:": "Security architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nInfrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-infrastructure-endpoint\n\nIncident preparation: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-incident-preparation", "Azure Policy Mapping": "Azure Backup should be enabled for Virtual Machines\nGeo-redundant backup should be enabled for Azure Database for MariaDB\nGeo-redundant backup should be enabled for Azure Database for PostgreSQL\nGeo-redundant backup should be enabled for Azure Database for MySQL", "Azure Policy GUID": "013e242c-8828-4970-87b3-ab247555486d\n0ec47710-77ff-4a3d-9181-6aa50af424d0\n48af4db5-9b8b-401c-8e74-076be876a430\n82339799-d096-41ae-8538-b108becf0970"}, {"ASB ID": "BR-3", "Control Domain": "Backup and recovery", "CIS Controls v7.1 ID(s)": "10.4 - Ensure Protection of Backups", "CIS Controls v8 ID(s)": "11.3 - Protect Recovery Data", "NIST SP800-53 r4 ID(s)": "CP-9: INFORMATION SYSTEM BACKUP", "PCI-DSS v3.2.1 ID(s)": NaN, "Recommendation": "Monitor backups  ", "Security Principle": "Ensure all business-critical protectable resources are compliant with the defined backup policy and standard. ", "Azure Guidance": "Monitor your Azure environment to ensure that all your critical resources are compliant from a backup perspective. Use Azure Policies for backup to audit and enforce such control.  For Azure Backup supported resources: Backup Center helps you centrally govern your backup estate.\n\nEnsure critical Backup operations (delete, change retention, updates to backup config) are monitored, audited and have alerts in place. For Azure Backup supported resources, monitor overall backup health, get alerted to critical backup incidents, audit user triggered actions on vaults. ", "Implementation and additional context": "Govern your backup estate using Backup Center:\nhttps://docs.microsoft.com/azure/backup/backup-center-govern-environment\n\nMonitor and operate backups using Backup center:\nhttps://docs.microsoft.com/azure/backup/backup-center-monitor-operate\n\nMonitoring and reporting solutions for Azure Backup:\nhttps://docs.microsoft.com/azure/backup/monitoring-and-alerts-overview\n", "Customer Security Stakeholders:": "Incident preparation: https://docs.microsoft.com/en-us/azure/cloud-adoption-framework/organize/cloud-security-incident-preparation\n\nSecurity Compliance Management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-compliance-management", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "BR-4", "Control Domain": "Backup and recovery", "CIS Controls v7.1 ID(s)": "10.3 - Test Data on Backup Media", "CIS Controls v8 ID(s)": "11.5 - Test Data Recovery", "NIST SP800-53 r4 ID(s)": "CP-4: CONT<PERSON><PERSON><PERSON><PERSON> PLAN TESTING\nCP-9: INFORMATION SYSTEM BACKUP", "PCI-DSS v3.2.1 ID(s)": NaN, "Recommendation": "Regularly test backup", "Security Principle": "Periodically perform data recovery tests of your backup to verify that the backup configurations and availability of the backup data meets the recovery needs as per defined in the RTO (Recovery Time Objective) and RPO (Recovery Point Objective). ", "Azure Guidance": "Periodically perform data recovery tests of your backup to verify that the backup configurations and availability of the backup data meets the recovery needs as per defined in the RTO and RPO. \n\nYou may need to define your backup recovery test strategy, including the test scope, frequency and method as performing the full recovery test each time can be difficult.", "Implementation and additional context": "How to recover files from Azure Virtual Machine backup:\nhttps://docs.microsoft.com/azure/backup/backup-azure-restore-files-from-vm\n\nHow to restore Key Vault keys in Azure:\nhttps://docs.microsoft.com/powershell/module/azurerm.keyvault/restore-azurekeyvaultkey?view=azurermps-6.13.0", "Customer Security Stakeholders:": "Security architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nIncident preparation: https://docs.microsoft.com/en-us/azure/cloud-adoption-framework/organize/cloud-security-incident-preparation\n\nData Security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-data-security", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "IR-1", "Control Domain": "Incident Response", "CIS Controls v7.1 ID(s)": "19.1 - Document Incident Response Procedures\n19.7 - Conduct Periodic Incident Scenario Sessions for Personnel", "CIS Controls v8 ID(s)": "17.4 - Establish and Maintain an Incident Response Process\n17.7 - Conduct Routine Incident Response Exercises", "NIST SP800-53 r4 ID(s)": "IR-4: INCIDENT HANDLING\nIR-8: INCIDENT RESPONSE PLAN", "PCI-DSS v3.2.1 ID(s)": 10.8, "Recommendation": "Preparation - update incident response plan and handling process", "Security Principle": "Ensure your organization follows industry best practice to develop processes and plans to respond to security incidents on the cloud platforms. Be mindful about the shared responsibility model and the variances across IaaS, PaaS and SaaS services. This will have a direct impact to how you collaborate with your cloud provider in incident response and handling activities, such as incident notification and triage, evidence collection, investigation, eradication and recovery.\n\nRegularly test the incident response plan and handling process to ensure they're up to date.", "Azure Guidance": "Update your organization's incident response process to include the handling of incident in Azure platform. Based on the Azure services used and your application nature, customize the incident response plan and playbook to ensure they can be used to respond to the incident in the cloud environment.", "Implementation and additional context": "Implement security across the enterprise environment: \nhttps://docs.microsoft.com/azure/cloud-adoption-framework/secure/security-top-10#4-process-update-incident-response-processes-for-cloud\n\nIncident response reference guide:\nhttps://docs.microsoft.com/microsoft-365/downloads/IR-Reference-Guide.pdf\n\nNIST SP800-61 Computer Security Incident Handling Guide\nhttps://nvlpubs.nist.gov/nistpubs/SpecialPublications/NIST.SP.800-61r2.pdf", "Customer Security Stakeholders:": "Security operations: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-operations-center\n\nIncident preparation: https://docs.microsoft.com/en-us/azure/cloud-adoption-framework/organize/cloud-security-incident-preparation\n\nThreat intelligence: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-threat-intelligence", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "IR-2", "Control Domain": "Incident Response", "CIS Controls v7.1 ID(s)": "19.2 - Assign Job Titles and Duties for Incident Response\n19.3 - Designate Management Personnel to Support Incident Handling\n19.4 - Devise Organization-wide Standards for Reporting Incidents\n19.5 - Maintain Contact Information For Reporting Security Incidents", "CIS Controls v8 ID(s)": "17.1 - Designate Personnel to Manage Incident Handling\n17.3 - Establish and Maintain an Enterprise Process for Reporting Incidents\n17.6 - Define Mechanisms for Communicating During Incident Response", "NIST SP800-53 r4 ID(s)": "IR-4: INCIDENT HANDLING\nIR-8: INCIDENT RESPONSE PLAN\nIR-5: INCIDENT MONITORING\nIR-6: INCIDENT REPORTING", "PCI-DSS v3.2.1 ID(s)": "12.10", "Recommendation": "Preparation - setup incident notification", "Security Principle": "Ensure the security alerts and incident notification from the cloud service provider's platform and your environments can be received by correct contact in your incident response organization. ", "Azure Guidance": "Set up security incident contact information in Microsoft Defender for Cloud. This contact information is used by Microsoft to contact you if the Microsoft Security Response Center (MSRC) discovers that your data has been accessed by an unlawful or unauthorized party. You also have options to customize incident alert and notification in different Azure services based on your incident response needs.", "Implementation and additional context": "How to set the Microsoft Defender for Cloud security contact: \nhttps://docs.microsoft.com/azure/security-center/security-center-provide-security-contact-details", "Customer Security Stakeholders:": "Security operations: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-operations-center\n\nIncident preparation: https://docs.microsoft.com/en-us/azure/cloud-adoption-framework/organize/cloud-security-incident-preparation", "Azure Policy Mapping": "Email notification to subscription owner for high severity alerts should be enabled\nSubscriptions should have a contact email address for security issues\nEmail notification for high severity alerts should be enabled", "Azure Policy GUID": "0b15565f-aa9e-48ba-8619-45960f2c314d\n4f4f78b8-e367-4b10-a341-d9a4ad5cf1c7\n6e2593d9-add6-4083-9c9b-4b7d2188c899"}, {"ASB ID": "IR-3", "Control Domain": "Incident Response", "CIS Controls v7.1 ID(s)": "19.8 - Create Incident Scoring and Prioritization Schema", "CIS Controls v8 ID(s)": "17.9 - Establish and Maintain Security Incident Thresholds", "NIST SP800-53 r4 ID(s)": "IR-4: INCIDENT HANDLING\nIR-5: INCIDENT MONITORING\nIR-7 INCIDENT RESPONSE ASSISTANCE", "PCI-DSS v3.2.1 ID(s)": 10.8, "Recommendation": "Detection and analysis - create incidents based on high-quality alerts", "Security Principle": "Ensure you have a process to create high-quality alerts and measure the quality of alerts. This allows you to learn lessons from past incidents and prioritize alerts for analysts, so they don't waste time on false positives.\n\nHigh-quality alerts can be built based on experience from past incidents, validated community sources, and tools designed to generate and clean up alerts by fusing and correlating diverse signal sources.", "Azure Guidance": "Microsoft Defender for Cloud provides high-quality alerts across many Azure assets. You can use the Microsoft Defender for Cloud data connector to stream the alerts to Azure Sentinel. Azure Sentinel lets you create advanced alert rules to generate incidents automatically for an investigation.\n\nExport your Microsoft Defender for Cloud alerts and recommendations using the export feature to help identify risks to Azure resources. Export alerts and recommendations either manually or in an ongoing, continuous fashion.", "Implementation and additional context": "How to configure export: \nhttps://docs.microsoft.com/azure/security-center/continuous-export\n\nHow to stream alerts into Azure Sentinel: \nhttps://docs.microsoft.com/azure/sentinel/connect-azure-security-center", "Customer Security Stakeholders:": "Security operations: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-operations-center\n\nIncident preparation: https://docs.microsoft.com/en-us/azure/cloud-adoption-framework/organize/cloud-security-incident-preparation\n\nThreat intelligence: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-threat-intelligence", "Azure Policy Mapping": "Azure Defender for open-source relational databases should be enabled\nAzure Defender for Key Vault should be enabled\nAzure Defender for App Service should be enabled\nAzure Defender for Storage should be enabled\nAzure Defender for servers should be enabled\nAzure Defender for Kubernetes should be enabled\nAzure Defender for SQL servers on machines should be enabled\nAzure Defender for Azure SQL Database servers should be enabled\nAzure Defender for SQL should be enabled for unprotected Azure SQL servers\nAzure Defender for SQL should be enabled for unprotected SQL Managed Instances\n[Preview]: Azure Defender for DNS should be enabled\nAzure Defender for container registries should be enabled\nAzure Defender for Resource Manager should be enabled", "Azure Policy GUID": "0a9fbe0d-c5c4-4da8-87d8-f4fd77338835\n0e6763cc-5078-4e64-889d-ff4d9a839047\n2913021d-f2fd-4f3d-b958-22354e2bdbcb\n308fbb08-4ab8-4e67-9b29-592e93fb94fa\n4da35fc9-c9e7-4960-aec9-797fe7d9051d\n523b5cd1-3e23-492f-a539-13118b6d1e3a\n6581d072-105e-4418-827f-bd446d56421b\n7fe3b40f-802b-4cdd-8bd4-fd799c948cc2\nabfb4388-5bf4-4ad7-ba82-2cd2f41ceae9\nabfb7388-5bf4-4ad7-ba99-2cd2f41cebb9\nbdc59948-5574-49b3-bb91-76b7c986428d\nc25d9a16-bc35-4e15-a7e5-9db606bf9ed4\nc3d20c29-b36d-48fe-808b-99a87530ad99"}, {"ASB ID": "IR-4", "Control Domain": "Incident Response", "CIS Controls v7.1 ID(s)": NaN, "CIS Controls v8 ID(s)": NaN, "NIST SP800-53 r4 ID(s)": "IR-4: INCIDENT HANDLING", "PCI-DSS v3.2.1 ID(s)": "12.10", "Recommendation": "Detection and analysis - investigate an incident", "Security Principle": "Ensure security operation team can query and use diverse data sources as they investigate potential incidents, to build a full view of what happened. Diverse logs should be collected to track the activities of a potential attacker across the kill chain to avoid blind spots. You should also ensure insights and learnings are captured for other analysts and for future historical reference.", "Azure Guidance": "The data sources for investigation are the centralized logging sources that are already being collected from the in-scope services and running systems, but can also include:\n- Network data: Use network security groups' flow logs, Azure Network Watcher, and Azure Monitor to capture network flow logs and other analytics information.\n- Snapshots of running systems:\na) Azure virtual machine's snapshot capability, to create a snapshot of the running system's disk.\nb) The operating system's native memory dump capability, to create a snapshot of the running system's memory.\nc) The snapshot feature of the Azure services or your software's own capability, to create snapshots of the running systems.\n\nAzure Sentinel provides extensive data analytics across virtually any log source and a case management portal to manage the full lifecycle of incidents. Intelligence information during an investigation can be associated with an incident for tracking and reporting purposes.", "Implementation and additional context": "Snapshot a Windows machine's disk:\nhttps://docs.microsoft.com/azure/virtual-machines/windows/snapshot-copy-managed-disk\n\nSnapshot a Linux machine's disk:\nhttps://docs.microsoft.com/azure/virtual-machines/linux/snapshot-copy-managed-disk\n\nMicrosoft Azure Support diagnostic information and memory dump collection:\nhttps://azure.microsoft.com/support/legal/support-diagnostic-information-collection/\n\nInvestigate incidents with Azure Sentinel:\nhttps://docs.microsoft.com/azure/sentinel/tutorial-investigate-cases", "Customer Security Stakeholders:": "Security operations: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-operations-center\n\nIncident preparation: https://docs.microsoft.com/en-us/azure/cloud-adoption-framework/organize/cloud-security-incident-preparation\n\nThreat intelligence: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-threat-intelligence", "Azure Policy Mapping": "Network Watcher should be enabled", "Azure Policy GUID": "b6e2945c-0b7b-40f5-9233-7a5323b5cdc6"}, {"ASB ID": "IR-5", "Control Domain": "Incident Response", "CIS Controls v7.1 ID(s)": "19.8 - Create Incident Scoring and Prioritization Schema", "CIS Controls v8 ID(s)": "17.4 - Establish and Maintain an Incident Response Process\n17.9 - Establish and Maintain Security Incident Thresholds", "NIST SP800-53 r4 ID(s)": "IR-4: INCIDENT HANDLING", "PCI-DSS v3.2.1 ID(s)": "12.10", "Recommendation": "Detection and analysis - prioritize incidents", "Security Principle": "Provide context to security operations teams to help them determine which incidents ought to first be focused on, based on alert severity and asset sensitivity defined in your organization’s incident response plan.", "Azure Guidance": "Microsoft Defender for Cloud assigns a severity to each alert to help you prioritize which alerts should be investigated first. The severity is based on how confident Security Center is in the finding or the analytics used to issue the alert, as well as the confidence level that there was malicious intent behind the activity that led to the alert.\n\nAdditionally, mark resources using tags and create a naming system to identify and categorize Azure resources, especially those processing sensitive data. It is your responsibility to prioritize the remediation of alerts based on the criticality of the Azure resources and environment where the incident occurred.", "Implementation and additional context": "Security alerts in Microsoft Defender for Cloud:\nhttps://docs.microsoft.com/azure/security-center/security-center-alerts-overview\n\nUse tags to organize your Azure resources:\nhttps://docs.microsoft.com/azure/azure-resource-manager/resource-group-using-tags", "Customer Security Stakeholders:": "Security operations: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-operations-center\n\nIncident preparation: https://docs.microsoft.com/en-us/azure/cloud-adoption-framework/organize/cloud-security-incident-preparation\n\nThreat intelligence: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-threat-intelligence", "Azure Policy Mapping": "Azure Defender for open-source relational databases should be enabled\nAzure Defender for Key Vault should be enabled\nAzure Defender for App Service should be enabled\nAzure Defender for Storage should be enabled\nAzure Defender for servers should be enabled\nAzure Defender for Kubernetes should be enabled\nAzure Defender for SQL servers on machines should be enabled\nAzure Defender for Azure SQL Database servers should be enabled\nAzure Defender for SQL should be enabled for unprotected Azure SQL servers\nAzure Defender for SQL should be enabled for unprotected SQL Managed Instances\n[Preview]: Azure Defender for DNS should be enabled\nAzure Defender for container registries should be enabled\nAzure Defender for Resource Manager should be enabled", "Azure Policy GUID": "0a9fbe0d-c5c4-4da8-87d8-f4fd77338835\n0e6763cc-5078-4e64-889d-ff4d9a839047\n2913021d-f2fd-4f3d-b958-22354e2bdbcb\n308fbb08-4ab8-4e67-9b29-592e93fb94fa\n4da35fc9-c9e7-4960-aec9-797fe7d9051d\n523b5cd1-3e23-492f-a539-13118b6d1e3a\n6581d072-105e-4418-827f-bd446d56421b\n7fe3b40f-802b-4cdd-8bd4-fd799c948cc2\nabfb4388-5bf4-4ad7-ba82-2cd2f41ceae9\nabfb7388-5bf4-4ad7-ba99-2cd2f41cebb9\nbdc59948-5574-49b3-bb91-76b7c986428d\nc25d9a16-bc35-4e15-a7e5-9db606bf9ed4\nc3d20c29-b36d-48fe-808b-99a87530ad99"}, {"ASB ID": "IR-6", "Control Domain": "Incident Response", "CIS Controls v7.1 ID(s)": NaN, "CIS Controls v8 ID(s)": NaN, "NIST SP800-53 r4 ID(s)": "IR-4: INCIDENT HANDLING\nIR-5: INCIDENT MONITORING\nIR-6: INCIDENT REPORTING", "PCI-DSS v3.2.1 ID(s)": "12.10", "Recommendation": "Containment, eradication and recovery - automate the incident handling", "Security Principle": "Automate the manual, repetitive tasks to speed up response time and reduce the burden on analysts. Manual tasks take longer to execute, slowing each incident and reducing how many incidents an analyst can handle. Manual tasks also increase analyst fatigue, which increases the risk of human error that causes delays and degrades the ability of analysts to focus effectively on complex tasks.", "Azure Guidance": "Use workflow automation features in Microsoft Defender for Cloud and Azure Sentinel to automatically trigger actions or run a playbook to respond to incoming security alerts. The playbook takes actions, such as sending notifications, disabling accounts, and isolating problematic networks.", "Implementation and additional context": "Configure workflow automation in Security Center:\nhttps://docs.microsoft.com/azure/security-center/workflow-automation\n\nSet up automated threat responses in Microsoft Defender for Cloud:\nhttps://docs.microsoft.com/azure/security-center/tutorial-security-incident#triage-security-alerts\n\nSet up automated threat responses in Azure Sentinel:\nhttps://docs.microsoft.com/azure/sentinel/tutorial-respond-threats-playbook", "Customer Security Stakeholders:": "Security operations: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-operations-center\n\nIncident preparation: https://docs.microsoft.com/en-us/azure/cloud-adoption-framework/organize/cloud-security-incident-preparation\n\nThreat intelligence: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-threat-intelligence", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "IR-7", "Control Domain": "Incident Response", "CIS Controls v7.1 ID(s)": NaN, "CIS Controls v8 ID(s)": "17.8 - Conduct Post-Incident Reviews", "NIST SP800-53 r4 ID(s)": "IR-4 INCIDENT HANDLING", "PCI-DSS v3.2.1 ID(s)": "12.10", "Recommendation": "Post-incident activity - conduct lesson learned and retain evidence", "Security Principle": "Conduct lesson learned in your organization periodically and/or after major incidents, to improve your future capability in incident response and handling. \n\nBased on the nature of the incident, retain the evidence related to the incident for the period defined in the incident handling standard for further analysis or legal actions. ", "Azure Guidance": "Use the outcome from the lesson learned activity to update your incident response plan, playbook (such as  Azure Sentinel playbook) and reincorporate findings into your environments (such as logging and threat detection to address any logging gap areas) to improve your future capability in detecting, respond, and handling of the incident in Azure.\n\nKeep the evidence collected during the \"Detection and analysis - investigate an incident step\" such as system logs, network traffic dump and running system snapshot in storage such as Azure Storage account for retention.", "Implementation and additional context": "Incident response process - Post-incident cleanup:\nhttps://docs.microsoft.com/security/compass/incident-response-process#2-post-incident-cleanup", "Customer Security Stakeholders:": "Security operations: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-operations-center\n\nIncident preparation: https://docs.microsoft.com/en-us/azure/cloud-adoption-framework/organize/cloud-security-incident-preparation\n\nThreat intelligence: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-threat-intelligence", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "DS-1", "Control Domain": "DevOps Security", "CIS Controls v7.1 ID(s)": NaN, "CIS Controls v8 ID(s)": "16.10 - Apply Secure Design Principles in Application Architectures\n16.14 - Conduct Threat Modeling\n", "NIST SP800-53 r4 ID(s)": "SA-15: DEVELOPMENT PROCESS, STANDARDS, AND TOOLS", "PCI-DSS v3.2.1 ID(s)": "6.5\n12.2", "Recommendation": "Conduct threat modeling\n\n", "Security Principle": "Perform threat modeling to identify the potential threats and enumerate the mitigating controls. Ensure your threat modeling serves the following purposes:\n- Secure your applications and services in the production run-time stage. \n- Secure the artifacts, underlying CI/CD pipeline and other tooling environment used for build, test, and deployment. \nThe threat modeling at least should include the following aspects:\n- Define the security requirements of the application. Ensure these requirements are adequately addressed in the threat modeling.\n- Analyze application components, data connections and their relationship. Ensure this analysis also includes the upstream and downstream connections outside of your application scope.\n- List the potential threats and attack vectors that your application components, data connections and upstream and downstream services may be exposed to. \n- Identify the applicable security controls that can be used to mitigate the threats enumerated and identify any controls gaps (e.g., security vulnerabilities) that may require additional treatment plans.\n- Enumerate and design the controls that can mitigate the vulnerabilities identified.", "Azure Guidance": "Use threat modeling tools such as Microsoft threat modeling tool with Azure threat model template embedded to drive your threat modeling process. Use the STRIDE model to enumerate the threats from both internal and external and identify the controls applicable. Ensure the threat modeling process includes the threat scenarios in the DevOps process, such as malicious code injection through an insecure artifacts repository with misconfigured access control policy. \n\nIf using a threat modeling tool is not applicable you should, at the minimum, use a questionnaire-based threat modeling process to identify the threats. \n\nEnsure the threat modeling or analysis results are recorded and updated when there is a major security-impact change in your application or in the threat landscape.", "Implementation and additional context": "Threat Modeling Overview: \nhttps://www.microsoft.com/securityengineering/sdl/threatmodeling \n\nApplication threat analysis (including STRIDE + questionnaire based method): \nhttps://docs.microsoft.com/azure/architecture/framework/security/design-threat-model \n\nAzure Template - Microsoft Security Threat Model Stencil: \nhttps://github.com/AzureArchitecture/threat-model-templates \n", "Customer Security Stakeholders:": "Policy and standards: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-policy-standards\n\nApplication security and DevSecOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "DS-2", "Control Domain": "DevOps Security", "CIS Controls v7.1 ID(s)": "18.3 - Verify That Acquired Software is Still Supported\n18.4 - Only Use Up-to-Date And Trusted Third-Party Components\n18.8 - Establish a Process to Accept and Address Reports of Software Vulnerabilities", "CIS Controls v8 ID(s)": "16.4 - Establish and Manage an Inventory of Third-Party Software Components\n16.6 - Establish and Maintain a Severity Rating System and Process for Application Vulnerabilities\n16.11 - Leverage Vetted Modules or Services for Application Security Components", "NIST SP800-53 r4 ID(s)": "SA-12: <PERSON><PERSON><PERSON><PERSON> CHAIN PROTECTION\nSA-15: DEVELOPMENT PROCESS, STANDARDS, AND TOOLS", "PCI-DSS v3.2.1 ID(s)": "6.3\n6.5", "Recommendation": "Ensure software supply chain security", "Security Principle": "Ensure your enterprise’s SDLC (Software Development Lifecycle) or process include a set of security controls to govern the in-house and third-party software components (including both proprietary and open-source software) where your applications have dependencies. Define gating criteria to prevent vulnerable or malicious components being integrated and deployed into the environment. \n\nThe software supply chain security controls at least should include following aspects:\n- Identify the upstream dependencies required at the development, build, integration and deployment phase.\n- Inventory and track the in-house and third-party software components for known vulnerability when there is a fix available in the upstream.\n- Assess the vulnerabilities and malware in the software components using static and dynamic application testing for unknown vulnerabilities.\n- Ensure the vulnerabilities and malware are mitigated using the appropriate approach. This may include source code local or upstream fix, feature exclusion and/or applying compensating controls if the direct mitigation is not available. \n\nIf closed source third-party components are used in your production environment, you may have limited visibility to its security posture. You should consider additional controls such as access control, network isolation and endpoint security to minimize the impact if there is a malicious activity or vulnerability associated with the component. ", "Azure Guidance": "For the GitHub platform, ensure the software supply chain security through the following capability or tools from GitHub Advanced Security or GitHub’s native feature: \n- Use Dependency Graph to scan, inventory and identify all your project’s dependencies and related vulnerabilities through Advisory Database. \n- Use Dependabot to ensure that the vulnerable dependency is tracked and remediated, and ensure your repository automatically keeps up with the latest releases of the packages and applications it depends on.\n- Use GitHub native code scanning capability to scan the source code when sourcing the code from external.\n- Use Microsoft Defender for Cloud to integrate vulnerability assessment for your container image in the CI/CD workflow. \n \nFor Azure DevOps, you can use third-party extensions to implement similar controls to inventory, analyze and remediate the third-party software components and their vulnerabilities.", "Implementation and additional context": "GitHub Dependency Graph: \nhttps://docs.github.com/en/code-security/supply-chain-security/understanding-your-software-supply-chain/about-the-dependency-graph \n\nGitHub Dependabot: \nhttps://docs.github.com/en/code-security/supply-chain-security/keeping-your-dependencies-updated-automatically/about-dependabot-version-updates \n\nIdentify vulnerable container images in your CI/CD workflows:\nhttps://docs.microsoft.com/azure/security-center/defender-for-container-registries-cicd\n\nAzure DevOps Marketplace – supply chain security: \nhttps://marketplace.visualstudio.com/search?term=tag%3ASupply%20Chain%20Security&target=VSTS ", "Customer Security Stakeholders:": "Application security and DevSecOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "DS-3", "Control Domain": "DevOps Security", "CIS Controls v7.1 ID(s)": "18.11 - Use Standard Hardening Configuration Templates for Databases", "CIS Controls v8 ID(s)": "16.7 - Use Standard Hardening Configuration Templates for Application Infrastructure", "NIST SP800-53 r4 ID(s)": "CM-2: <PERSON><PERSON><PERSON><PERSON><PERSON> CONFIGURATION\nCM-6: CONFIGURATION SETTINGS\nAC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nAC-6: LEAST PRIVILEGE", "PCI-DSS v3.2.1 ID(s)": "2.2\n6.3\n7.1\n\n", "Recommendation": "Secure DevOps infrastructure", "Security Principle": "Ensure the DevOps infrastructure and pipeline follow security best practices across environments including your build, test, and production stages. This typically includes the security controls for following scope:\n- Artifact repositories that store source code, built packages and images, project artifacts and business data.\n- Servers, services, and tooling that hosting CI/CD pipelines.\n- CI/CD pipeline configuration.", "Azure Guidance": "As part of applying the Azure Security Benchmark to your DevOps infrastructure security controls, prioritize the following controls:\n- Protect artifacts and underlying environment to ensure the CI/CD pipelines don’t become avenues to insert malicious code. For example, review your CI/CD pipeline to identify any misconfiguration in core areas of Azure DevOps such as Organization, Projects, Users, Pipelines (Build & Release), Connections, and Build Agent to identify any misconfigurations such as open access, weak authentication, insecure connection setup and so on. For GitHub, use the similar controls to secure the Organization permission levels \n- Configure identity/role permissions and entitlement policies in Azure AD, native services, and CI/CD tools in your pipeline to ensure changes to the pipelines are authorized.\n- Avoid providing permanent “standing” privileged access to the human accounts such as developers or testers by using features such as Azure managed identifies, just-in-time access.\n- Remove keys, credentials, and secrets from code and scripts used in CI/CD workflow jobs and keep them in key store or Azure Key Vault. \n- If you run self-hosted build/deployment agents, follow Azure Security Benchmark controls including network security, posture and vulnerability management, and endpoint security to secure your environment.", "Implementation and additional context": "DevSecOps controls overview – secure pipelines:\nhttps://docs.microsoft.com/azure/cloud-adoption-framework/secure/devsecops-controls \n\nSecure your GitHub organization: \nhttps://docs.github.com/en/code-security/getting-started/securing-your-organization \n\nAzure DevOps pipeline – Microsoft hosted agent security considerations:\nhttps://docs.microsoft.com/azure/devops/pipelines/agents/hosted?view=azure-devops&tabs=yaml#security\n\n\n", "Customer Security Stakeholders:": "Application security and DevSecOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management\n\nInfrastructure and endpoint security: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-infrastructure-endpoint\n\nSecurity architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "DS-4", "Control Domain": "DevOps Security", "CIS Controls v7.1 ID(s)": "18.7 - Apply Static and Dynamic Code Analysis Tools", "CIS Controls v8 ID(s)": "16.12 - Implement Code-Level Security Checks", "NIST SP800-53 r4 ID(s)": "SA-11: <PERSON><PERSON><PERSON><PERSON><PERSON> TESTING AND EVALUATION", "PCI-DSS v3.2.1 ID(s)": "6.3\n6.5", "Recommendation": "Integrate static application security testing into DevOps pipeline", "Security Principle": "Ensure static application security testing (SAST) are part of the gating controls in the CI/CD workflow. The gating can be set based on the testing results to prevent vulnerable packages from committing into the repository, building into the packages, or deploying into the production.", "Azure Guidance": "Integrate SAST into your pipeline so the source code can be scanned automatically in your CI/CD workflow. Azure DevOps Pipeline or GitHub can integrate tools below and third-party SAST tools into the workflow. \n- GitHub CodeQL for source code analysis.\n- Microsoft BinSkim Binary Analyzer for Windows and *nix binary analysis.\n- Azure DevOps Credential Scanner and GitHub native secret scanning for credential scan in the source code. ", "Implementation and additional context": "GitHub CodeQL:\nhttps://codeql.github.com/docs/\n\nBinSkim Binary Analyzer:\nhttps://github.com/microsoft/binskim \n\nAzure DevOps Credential Scan:\nhttps://secdevtools.azurewebsites.net/helpcredscan.html \n\nGitHub secret scanning:\nhttps://docs.github.com/en/code-security/secret-security/about-secret-scanning", "Customer Security Stakeholders:": "Application security and DevSecOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "DS-5", "Control Domain": "DevOps Security", "CIS Controls v7.1 ID(s)": "18.7 - Apply Static and Dynamic Code Analysis Tools", "CIS Controls v8 ID(s)": "16.12 - Implement Code-Level Security Checks", "NIST SP800-53 r4 ID(s)": "SA-11: <PERSON><PERSON><PERSON><PERSON><PERSON> TESTING AND EVALUATION", "PCI-DSS v3.2.1 ID(s)": "6.3\n6.5", "Recommendation": "Integrate dynamic application security testing into DevOps pipeline", "Security Principle": "Ensure dynamic application security testing (DAST) are part of the gating controls in the CI/CD workflow. The gating can be set based on the testing results to prevent vulnerability from building into the packages or deploying into the production.", "Azure Guidance": "Integrate DAST into your pipeline so the runtime application can be tested automatically in your CI/CD workflow set in the Azure DevOps or GitHub. The automated penetration testing (with manual assisted validation) should also be part of the DAST. \n\nAzure DevOps Pipeline or GitHub supports the integrate of third-party DAST tools into the CI/CD workflow.", "Implementation and additional context": "DAST tools in Azure DevOps marketplace:\nhttps://marketplace.visualstudio.com/search?term=DAST&target=AzureDevOps&category=All%20categories", "Customer Security Stakeholders:": "Application security and DevSecOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "DS-6", "Control Domain": "DevOps Security", "CIS Controls v7.1 ID(s)": "5.2 - Deploy System Configuration Management Tools\n5.3 - Securely Store Master Images\n5.4 - Deploy System Configuration Management Tools\n5.5 - Implement Automated Configuration Monitoring Systems\n18.1 - Establish Secure Coding Practices", "CIS Controls v8 ID(s)": "7.5 - Perform Automated Vulnerability Scans of Internal Enterprise Assets\n7.6 - Perform Automated Vulnerability Scans of Externally-Exposed Enterprise Assets\n7.7 - Remediate Detected Vulnerabilities\n16.1 - Establish and Maintain a Secure Application Development Process\n16.7 - Use Standard Hardening Configuration Templates for Application Infrastructure", "NIST SP800-53 r4 ID(s)": "CM-2: <PERSON><PERSON><PERSON><PERSON><PERSON> CONFIGURATION\nCM-6: CONFIGURATION SETTINGS\nAC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nAC-6: LEAST PRIVILEGE", "PCI-DSS v3.2.1 ID(s)": "6.1\n6.2\n6.3\n", "Recommendation": "Enforce security of workload throughout DevOps lifecycle", "Security Principle": "Ensure the workload is secured throughout the entire lifecycle in development, testing, and deployment stage. Use Azure Security Benchmark to evaluate the controls (such as network security, identity management, privileged access and so on) that can be set as guardrails by default or shift left prior to the deployment stage. In particular, ensure the following controls are in place in your DevOps process: \n- Automate the deployment by using Azure or third-party tooling in the CI/CD workflow, infrastructure management (infrastructure as code), and testing to reduce human error and attack surface. \n- Ensure VMs, container images and other artifacts are secure from malicious manipulation. \n- Scan the workload artifacts (in other words, container images, dependencies, SAST and DAST scans) prior to the deployment in the CI/CD workflow\n- Deploy vulnerability assessment and threat detection capability into the production environment and continuously use these capabilities in the run-time.", "Azure Guidance": "Guidance for Azure VMs:\n- Use Azure Shared Image Gallery to share and control access to your images by different users, service principals, or AD groups within your organization. Use Azure role-based access control (Azure RBAC) to ensure that only authorized users can access your custom images. \n- Define the secure configuration baselines for the VMs to eliminate unnecessary credentials, permissions, and packages. Through custom images, Azure Resource Manager template, and/or Azure Policy guest configuration to deploy and enforce these the configuration baseline.\n\nGuidance for Azure container services: \n- Use Azure Container Registry (ACR) to create your private container registry where a granular access can be restricted through Azure RBAC, so only authorized services and accounts can access the containers in the private registry. \n- Use Defender for Azure Container Registry for vulnerability assessment of the images in your private Azure Container Registry. In addition, you can use Microsoft Defender for Cloud to ingrate container images scan as part of your CI/CD workflows. \n\nFor Azure serverless services, adopt the similar controls to ensure security controls are shift left to the stage prior to the deployment.  \n", "Implementation and additional context": "Shared Image Gallery overview: \nhttps://docs.microsoft.com/azure/virtual-machines/windows/shared-image-galleries \n\nHow to implement Microsoft Defender for Cloud vulnerability assessment recommendations: https://docs.microsoft.com/azure/security-center/security-center-vulnerability-assessment-recommendations \n\nSecurity considerations for Azure Container:\nhttps://docs.microsoft.com/azure/container-instances/container-instances-image-security \n\nAzure Defender for container registries:\nhttps://docs.microsoft.com/azure/security-center/defender-for-container-registries-introduction ", "Customer Security Stakeholders:": "Application security and DevSecOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nPosture management: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-posture-management\n\nSecurity architecture: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture", "Azure Policy Mapping": "Vulnerabilities in Azure Container Registry images should be remediated\nVulnerabilities in container security configurations should be remediated", "Azure Policy GUID": "5f0f936f-2f01-4bf5-b6be-d423792fa562\ne8cbc669-f12d-49eb-93e7-9273119e9933"}, {"ASB ID": "DS-7", "Control Domain": "DevOps Security", "CIS Controls v7.1 ID(s)": "6.2 - Activate audit logging\n6.3 - Enable Detailed Logging\n6.5 - Central Log Management\n6.6 - Deploy SIEM or Log Analytic tool\n6.7 - Regularly Review Logs\n6.8 - Regularly Tune SIEM", "CIS Controls v8 ID(s)": "8.2 Collect Audit Logs\n8.5 Collect Detailed Audit Logs\n8.9 Centralize Audit Logs\n8.11 Conduct Audit Log Reviews", "NIST SP800-53 r4 ID(s)": "AU-3: CONTENT OF AUDIT RECORDS\nAU-6: AUDIT RE<PERSON><PERSON><PERSON>, ANALYSIS, AND REPORTING\nAU-12: AUDIT GENERATION\nSI-4: INFORMATION SYSTEM MONITORING", "PCI-DSS v3.2.1 ID(s)": "10.1\n10.2\n10.3\n10.6", "Recommendation": "Enable logging and monitoring in DevOps", "Security Principle": "Ensure your logging and monitoring scope includes non-production environments and CI/CD workflow elements used in DevOps (and any other development processes). The vulnerabilities and threats targeting these environments can introduce significant risks to your production environment if they are not monitored properly. The events from the CI/CD build, test and deployment workflow should also be monitored to identify any deviations in the CI/CD workflow jobs. ", "Azure Guidance": "Enable and configure the audit logging capabilities in non-production and CI/CD tooling environments (such as Azure DevOps and GitHub) used throughout the DevOps process. \n\nThe events from the Azure DevOps and GitHub CI/CD work for the build, test and deployment jobs should also be monitored to identify any exception results in the CI/CD jobs.\n\nIngest the above logs and events into Azure Sentinel or other SIEM tools through logging stream or API to ensure the security incidents are properly monitored and triaged for handling.\n\nFollow Azure Security Benchmark – Logging and Threat Detection as the guideline to implement your logging and monitoring controls for workload. ", "Implementation and additional context": "Azure DevOps - audit streaming: \nhttps://docs.microsoft.com/azure/devops/organizations/audit/auditing-streaming?view=azure-devops \n\nGitHub logging: \nhttps://docs.github.com/en/organizations/keeping-your-organization-secure/reviewing-the-audit-log-for-your-organization ", "Customer Security Stakeholders:": "Security operations: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-operations-center\n\nApplication security and DevSecOps: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops\n\nIncident preparation: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-incident-preparation", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "GS-1", "Control Domain": "Governance and Strategy", "CIS Controls v7.1 ID(s)": "17.2 - Deliver Training to Fill the Skills Gap", "CIS Controls v8 ID(s)": "14.9 - Conduct Role-Specific Security Awareness and Skills Training", "NIST SP800-53 r4 ID(s)": "PL-9: <PERSON><PERSON><PERSON><PERSON> MANAGEMENT\nPM-10: SECURITY AUTHORIZATION PROCESS\nPM-13: INFORMATION SECURITY WORKFORCE\t\nAT-1: SECURITY AWARENESS AND TRAINING POLICY AND PROCEDURES\nAT-3: ROLE-BASED SECURITY TRAINING\n", "PCI-DSS v3.2.1 ID(s)": 12.4, "Recommendation": "Align organization roles, responsibilities and accountabilities", "Security Principle": "N/A\n", "Azure Guidance": "Ensure that you define and communicate a clear strategy for roles and responsibilities in your security organization. Prioritize providing clear accountability for security decisions, educating everyone on the shared responsibility model, and educate technical teams on technology to secure the cloud.", "Implementation and additional context": "Azure Security Best Practice 1 – People: Educate Teams on Cloud Security Journey:\nhttps://docs.microsoft.com/azure/cloud-adoption-framework/security/security-top-10#1-people-educate-teams-about-the-cloud-security-journey\n\nAzure Security Best Practice 2 - People: Educate Teams on Cloud Security Technology:\nhttps://docs.microsoft.com/azure/cloud-adoption-framework/security/security-top-10#2-people-educate-teams-on-cloud-security-technology\n\nAzure Security Best Practice 3 - Process: Assign Accountability for Cloud Security Decisions:\nhttps://docs.microsoft.com/azure/cloud-adoption-framework/security/security-top-10#4-process-update-incident-response-ir-processes-for-cloud", "Customer Security Stakeholders:": "All stakeholders: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security#security-functions", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "GS-2", "Control Domain": "Governance and Strategy", "CIS Controls v7.1 ID(s)": "2.10 - Physically or Logically Segregate High Risk Applications\n14.1 - Segment the Network Based on Sensitivity\n", "CIS Controls v8 ID(s)": "3.12 - Segment Data Processing and Storage Based on Sensitivity", "NIST SP800-53 r4 ID(s)": "AC-4: INFORMATION FLOW ENFORCEMENT\nSC-7: BOUNDARY PROTECTION\nSC-2: APPLICATION PARTITIONING", "PCI-DSS v3.2.1 ID(s)": "1.2\n6.4\n", "Recommendation": "Define and implement enterprise segmentation/separation of duties strategy ", "Security Principle": "N/A\n", "Azure Guidance": "Establish an enterprise-wide strategy to segment access to assets using a combination of identity, network, application, subscription, management group, and other controls.\n\nCarefully balance the need for security separation with the need to enable daily operation of the systems that need to communicate with each other and access data.\n\nEnsure that the segmentation strategy is implemented consistently in the workload, including network security, identity and access models, and application permission/access models, and human process controls.", "Implementation and additional context": "Security in the Microsoft Cloud Adoption Framework for Azure - Segmentation: Separate to protect\nhttps://docs.microsoft.com/azure/cloud-adoption-framework/secure/access-control#segmentation-separate-to-protect\n\nSecurity in the Microsoft Cloud Adoption Framework for Azure - Architecture: establish a single unified security strategy:\nhttps://docs.microsoft.com/azure/cloud-adoption-framework/secure/security-top-10#11-architecture-establish-a-single-unified-security-strategy", "Customer Security Stakeholders:": "All stakeholders: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security#security-functions", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "GS-3", "Control Domain": "Governance and Strategy", "CIS Controls v7.1 ID(s)": "14.1 - Segment the Network Based on Sensitivity\n\n", "CIS Controls v8 ID(s)": "3.1 - Establish and Maintain a Data Management Process\n3.7 - Establish and Maintain a Data Classification Scheme\n3.12 - Segment Data Processing and Storage Based on Sensitivity", "NIST SP800-53 r4 ID(s)": "AC-4: INFORMATION FLOW ENFORCEMENT\nSI-4: INFORMATI<PERSON> SYSTEM MONITORING\nSC-8: TRANSMISSION CONFIDENTIALITY AND INTEGRITY\nSC-12: CRYPTO<PERSON><PERSON><PERSON><PERSON> KEY ESTABLISHMENT AND <PERSON><PERSON><PERSON>MENT\nSC-17: <PERSON><PERSON><PERSON><PERSON> KEY INFRASTRUCTURE CERTIFICATES\nSC-28: PROTECTION OF INFORMATION AT REST\nRA-2: SECURITY CATEGORIZATION", "PCI-DSS v3.2.1 ID(s)": "3.1\n3.2 \n3.3\n3.4\n3.5\n3.6\n3.7\n4.1\nA3.2", "Recommendation": "Define and implement data protection strategy", "Security Principle": "N/A\n", "Azure Guidance": "Establish an enterprise-wide strategy for data protection in Azure:\n- Define and apply the data classification and protection standard in accordance with the enterprise data management standard and regulatory compliance to dictate the security controls required for each level of the data classification. \n- Set up your cloud resource management hierarchy aligned to the enterprise segmentation strategy. The enterprise segmentation strategy should also be informed by the location of sensitive or business critical data and systems. \n- Define and apply the applicable zero-trust principles in your cloud environment to avoid implementing trust based on network location within a perimeter. Instead, use device and user trust claims to gate access to data and resources.\n- Track and minimize the sensitive data footprint (storage, transmission and processing) across the enterprise to reduce the attack surface and data protection cost. Consider techniques such as one-way hashing, truncation, and tokenization in the workload where possible, to avoid storing and transmitting sensitive data in its original form. \n- Ensure you have a full lifecycle control strategy to provide security assurance of the data and access keys. ", "Implementation and additional context": "Azure Security Benchmark - Data Protection:\nhttps://docs.microsoft.com/security/benchmark/azure/security-controls-v3-data-protection\n\nCloud Adoption Framework - Azure data security and encryption best practices:\nhttps://docs.microsoft.com/azure/security/fundamentals/data-encryption-best-practices\n\nAzure Security Fundamentals - Azure Data security, encryption, and storage:\nhttps://docs.microsoft.com/azure/security/fundamentals/encryption-overview\n", "Customer Security Stakeholders:": "All stakeholders: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security#security-functions", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "GS-4", "Control Domain": "Governance and Strategy", "CIS Controls v7.1 ID(s)": "12.1 - Maintain an Inventory of Network Boundaries", "CIS Controls v8 ID(s)": "12.2 - Establish and Maintain a Secure Network Infrastructure\n12.4 - Establish and Maintain Architecture Diagram(s)\n", "NIST SP800-53 r4 ID(s)": "AC-4: INFORMATION FLOW ENFORCEMENT\nAC-17: R<PERSON><PERSON><PERSON> ACCESS\nCA-3: SYSTEM INTERCONNECTIONS\nCM-1: CONFIGURATION MANAGEMENT POLICY AND PROCEDURES\nCM-2: BASELINE CONFIGURATION\nCM-6: CONFIGURATION SETTINGS\nCM-7: LEAST FUNCTIONALITY\nSC-1: SYSTEM AND COMMUNICATIONS PROTECTION POLICY AND PROCEDURES\nSC-2: APPLICATION PARTITIONING\nSC-5: DENIAL OF SERVICE PROTECTION\nSC-7: BOUNDARY PROTECTION\nSC-20: SECURE NAME / ADDRESS RESOLUTION SERVICE (AUTHORITATIVE SOURCE)\nSC-21: SECURE NAME / ADDRESS RESOLUTION SERVICE (RECURSIVE OR CACHING RESOLVER)\nSI-4: INFORMATION SYSTEM MONITORING\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "PCI-DSS v3.2.1 ID(s)": "1.1\n1.2\n1.3\n1.5\n4.1\n6.6\n11.4\nA2.1\nA2.2\nA2.3\nA3.2", "Recommendation": "Define and implement network security strategy", "Security Principle": "N/A\n", "Azure Guidance": "Establish an Azure network security strategy as part of your organization’s overall security strategy for access control. This strategy should include documented guidance, policy, and standards for the following elements:\n- Design a centralized/decentralized network management and security responsibility model to deploy and maintain network resources.\n- A virtual network segmentation model aligned with the enterprise segmentation strategy.\n- An Internet edge and ingress and egress strategy.\n- A hybrid cloud and on-premises interconnectivity strategy.\n- A network monitoring and logging strategy.\n- An up-to-date network security artifacts (such as network diagrams, reference network architecture).", "Implementation and additional context": "Azure Security Best Practice 11 - Architecture. Single unified security strategy:\nhttps://docs.microsoft.com/azure/cloud-adoption-framework/security/security-top-10#11-architecture-establish-a-single-unified-security-strategy\n\nAzure Security Benchmark - Network Security:\nhttps://docs.microsoft.com/security/benchmark/azure/security-controls-v3-network-security\n\nAzure network security overview:\nhttps://docs.microsoft.com/azure/security/fundamentals/network-overview\n\nEnterprise network architecture strategy: \nhttps://docs.microsoft.com/azure/cloud-adoption-framework/ready/enterprise-scale/architecture", "Customer Security Stakeholders:": "All stakeholders: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security#security-functions", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "GS-5", "Control Domain": "Governance and Strategy", "CIS Controls v7.1 ID(s)": "5.1 - Establish Secure Configurations", "CIS Controls v8 ID(s)": "4.1 - Establish and Maintain a Secure Configuration Process\n4.2 - Establish and Maintain a Secure Configuration Process for Network Infrastructure", "NIST SP800-53 r4 ID(s)": "CA-1: <PERSON><PERSON><PERSON><PERSON> ASSESSMENT AND <PERSON><PERSON><PERSON>I<PERSON><PERSON><PERSON> POLICY AND PROCEDURES\nCA-8: PENETRATION TESTING\nCM-1: CONFIGURATION MANAGEMENT POLICY AND PROCEDURES\nCM-2: BASELINE CONFIGURATION\nCM-6: CONFIGURATION SETTINGS\nRA-1: <PERSON><PERSON><PERSON> ASSESSMENT POLICY AND PROCEDURES\nRA-3: <PERSON><PERSON><PERSON> ASSESSMENT\nRA-5: VULNERABILITY SCANNING\nSI-1: SYSTEM AND INFORMATION INTEGRITY POLICY AND PROCEDURES\nSI-2: FLAW REMEDIATION\nSI-5: SECURITY ALERTS, ADVISORIES, AND DIRECTIVES", "PCI-DSS v3.2.1 ID(s)": "1.1\n1.2\n2.2\n6.1\n6.2\n6.5\n6.6\n11.2\n11.3\n11.5\n\n", "Recommendation": "Define and implement security posture management strategy", "Security Principle": "N/A\n", "Azure Guidance": "Establish a policy, procedure and standard to ensure the security configuration management and vulnerability management are in place in your cloud security mandate.\n\nThe security configuration management in Azure should include the following areas:\n- Define the secure configuration baselines for different resource types in the cloud, such as the Azure portal, management and control plane, and resources running in the IaaS, PaaS and SaaS services.\n- Ensure the security baselines address the risks in different control areas such as network security, identity management, privileged access, data protection and so on. \n- Use tools to continuously measure, audit, and enforce the configuration to prevent configuration deviating from the baseline.\n- Develop a cadence to stay update with Azure security features, for instance, subscribe to the service updates. \n- Utilize Secure Score in Microsoft Defender for Cloud to regularly review Azure’s security configuration posture and remediate the gaps identified.\n\nThe vulnerability management in Azure should include the following security aspects:\n- Regularly assess and remediate vulnerabilities in all cloud resource types, such as Azure native services, operating systems, and application components. \n- Use a risk-based approach to prioritize assessment and remediation. \n- Subscribe to the relevant Microsoft / Azure security advisory notices and blogs to receive the latest security updates about Azure. \n- Ensure the vulnerability assessment and remediation (such as schedule, scope, and techniques) meet the regularly compliance requirements for your organization.", "Implementation and additional context": "Azure Security Benchmark - Posture and vulnerability management:\nhttps://docs.microsoft.com/security/benchmark/azure/security-controls-v3-posture-vulnerability-management\n\nAzure Security Best Practice 9 - Establish security posture management:\nhttps://docs.microsoft.com/azure/cloud-adoption-framework/secure/security-top-10#5-process-establish-security-posture-management\n\n", "Customer Security Stakeholders:": "All stakeholders: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security#security-functions", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "GS-6", "Control Domain": "Governance and Strategy", "CIS Controls v7.1 ID(s)": "4.5 - Use Multifactor Authentication For All Administrative Access\n16.2 - Configure Centralized Point of Authentication", "CIS Controls v8 ID(s)": "5.6 - Centralize Account Management\n6.5 - Require MFA for Administrative Access\n6.7 - Centralize Access Control", "NIST SP800-53 r4 ID(s)": "AC-1: ACCESS CONTROL POLICY AND PROCEDURES\nAC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nAC-4: INFORMATION FLOW ENFORCEMENT\nAC-5: SEPARATION OF DUTIES\nAC-6: LEAST PRIVILEGE\nIA-1: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> POLICY AND PROCEDURES\nIA-2: ID<PERSON><PERSON><PERSON><PERSON><PERSON>ON AND AUTHENTICATION (ORGANIZATIONAL USERS)\nIA-4: IDENTIFIER MANAGEMENT\nIA-5: AUTHENTICATOR MANAGEMENT\nIA-8: IDENTIFICATION AND AUTHENTICATION (NON-ORGANIZAT<PERSON>AL USERS)\nIA-9: SERVICE IDENTIFICATION AND AUTHENTICATION\nSI-4: INFORMATION SYSTEM MONITORING", "PCI-DSS v3.2.1 ID(s)": "7.1\n7.2\n7.3\n8.1\n8.2\n8.3\n8.4\n8.5\n8.6\n8.7\n8.8\nA3.4", "Recommendation": "Define and implement identity and privileged access strategy", "Security Principle": "N/A\n", "Azure Guidance": "Establish an Azure identity and privileged access approach as part of your organization’s overall security access control strategy. This strategy should include documented guidance, policy, and standards for the following aspects:\n- Centralized identity and authentication system (Azure AD) and its interconnectivity with other internal and external identity systems\n- Privileged identity and access governance (such as access request, review and approval)\n- Privileged accounts in emergency (break-glass) situation\n- Strong authentication (passwordless authentication and multifactor authentication) methods in different use cases and conditions \n- Secure access by administrative operations through Azure portal, CLI and API.\n\nFor exception cases, where an enterprise system isn’t used, ensure adequate security controls are in place for identity, authentication and access management, and governed. These exceptions should be approved and periodically reviewed by the enterprise team. These exceptions are typically in cases such as:\n- Use of a non-enterprise designated identity and authentication system, such as cloud-based third-party systems (may introduce unknown risks) \n- Privileged users authenticated locally and/or use non-strong authentication methods", "Implementation and additional context": "Azure Security Benchmark - Identity management:\nhttps://docs.microsoft.com//security/benchmark/azure/security-controls-v3-identity-management\n\nAzure Security Benchmark - Privileged access: \nhttps://docs.microsoft.com/security/benchmark/azure/security-controls-v3-privileged-access\n\nAzure Security Best Practice 11 - Architecture. Single unified security strategy: \nhttps://docs.microsoft.com/azure/cloud-adoption-framework/security/security-top-10#11-architecture-establish-a-single-unified-security-strategy\n\nAzure identity management security overview: \nhttps://docs.microsoft.com/azure/security/fundamentals/identity-management-overview", "Customer Security Stakeholders:": "All stakeholders: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security#security-functions", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "GS-7", "Control Domain": "Governance and Strategy", "CIS Controls v7.1 ID(s)": "6.2 -Activate audit logging\n6.3 - Enable Detailed Logging\n6.6 - Deploy SIEM or Log Analytic tool\n6.7 - Regularly Review Logs\n19.1 - Document Incident Response Procedures\n19.5 - Maintain Contact Information For Reporting Security Incidents\n19.7 - Conduct Periodic Incident Scenario Sessions for Personnel", "CIS Controls v8 ID(s)": "8.1 - Establish and Maintain an Audit Log Management Process\n13.1 - Centralize Security Event Alerting\n17.2 - Establish and Maintain Contact Information for Reporting Security Incidents\n17.4 - Establish and Maintain an Incident Response Process\n17.7 - Conduct Routine Incident Response Exercises", "NIST SP800-53 r4 ID(s)": "AU-1: AUDIT AND ACCOUNTA<PERSON><PERSON><PERSON>Y POLICY AND PROCEDURES\nIR-1: INCIDENT RESPONSE POLICY AND PROCEDURES\nIR-2: INCIDENT RESPONSE TRAINING\nIR-10: INTEGRATED INFORMATION SECURITY ANALYSIS TEAM\nSI-1: <PERSON><PERSON><PERSON><PERSON> AND INFORMA<PERSON>ON INTEGRITY POLICY AND PROCEDURES\nSI-5: SECURITY ALERTS, ADVISORIES, AND DIRECTIVES", "PCI-DSS v3.2.1 ID(s)": "10.1\n10.2\n10.3\n10.4\n10.5\n10.6\n10.7\n10.8\n10.9\n12.10\nA3.5", "Recommendation": "Define and implement logging, threat detection and incident response strategy", "Security Principle": "N/A\n", "Azure Guidance": "Establish a logging, threat detection and incident response strategy to rapidly detect and remediate threats and meeting compliance requirements. Security operations (SecOps / SOC) team should prioritize high quality alerts and seamless experiences so that they can focus on threats rather than log integration and manual steps.\n\nThis strategy should include documented policy, procedure and standards for the following aspects:\n- The security operations (SecOps) organization's role and responsibilities\n- A well-defined and regularly tested incident response plan and handling process aligning with NIST or other industry frameworks.\n- Communication and notification plan with your customers, suppliers, and public parties of interest.\n- Preference of using extended detection and response (XDR) capabilities like Azure Defender capabilities to detect threats in the various areas. \n- Use of Azure native capability (e.g., as Microsoft Defender for Cloud) and third-party platforms for incident handling, such as logging and threat detection, forensics, and attack remediation and eradication. \n- Define key scenarios (such as threat detection, incident response, and compliance) and set up log capture and retention to meet the scenario requirements. \n- Centralized visibility of and correlation information about threats, using SIEM, native Azure threat detection capability, and other sources.\n- Post-incident activities, such as lessons learned and evidence retention.", "Implementation and additional context": "Azure Security Benchmark - Logging and threat detection: \nhttps://docs.microsoft.com/azure/security/benchmarks/security-benchmark-v3-logging-threat-detection\n\nAzure Security Benchmark - Incident response: \nhttps://docs.microsoft.com/azure/security/benchmarks/security-benchmark-v3-incident-response\n\nAzure Security Best Practice 4 - Process. Update Incident Response Processes for Cloud: \nhttps://aka.ms/AzSec4\n\nAzure Adoption Framework, logging, and reporting decision guide: \nhttps://docs.microsoft.com/azure/cloud-adoption-framework/decision-guides/logging-and-reporting/\n\nAzure enterprise scale, management, and monitoring: \nhttps://docs.microsoft.com/azure/cloud-adoption-framework/ready/enterprise-scale/management-and-monitoring", "Customer Security Stakeholders:": "All stakeholders: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security#security-functions", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "GS-8", "Control Domain": "Governance and Strategy", "CIS Controls v7.1 ID(s)": "10.1 - Ensure Regular Automated Backups", "CIS Controls v8 ID(s)": "11.1 - Establish and Maintain a Data Recovery Process ", "NIST SP800-53 r4 ID(s)": "CP-1: CONT<PERSON><PERSON><PERSON><PERSON> PLANNING POLICY AND PROCEDURES\nCP-9: INFORMATION SYSTEM BACKUP\nCP-10: INFORMATION SYSTEM RECOVERY AND RECON<PERSON><PERSON>UTION\n", "PCI-DSS v3.2.1 ID(s)": 3.4, "Recommendation": "Define and implement backup and recovery strategy", "Security Principle": "N/A\n", "Azure Guidance": "Establish an Azure backup and recovery strategy for your organization. This strategy should include documented guidance, policy, and standards in the following aspects:\n- Recovery time objective (RTO) and recovery point objective (RPO) definitions in accordance with your business resiliency objectives, and regulatory compliance requirements. \n- Redundancy design (including backup, restore and replication) in your applications and infrastructure for both in cloud and on-premises. Consider regional, region-pairs, cross-regional recovery and off-site storage location as part of your strategy.\n- Protection of backup from unauthorized access and tempering using controls such as data access control, encryption and network security.\n- Use of backup and recovery to mitigate the risks from emerging threats, such as ransomware attack. And also secure the backup and recovery data itself from these attacks. \n- Monitoring the backup and recovery data and operations for audit and alerting purposes. ", "Implementation and additional context": "Azure Security Benchmark - Backup and recovery:\nhttps://docs.microsoft.com/azure/security/benchmarks/security-benchmark-v3-backup-recovery\n\nAzure Well-Architecture Framework - Backup and disaster recover for Azure applications: https://docs.microsoft.com/azure/architecture/framework/resiliency/backup-and-recovery\n\nAzure Adoption Framework-business continuity and disaster recovery: \nhttps://docs.microsoft.com/azure/cloud-adoption-framework/ready/enterprise-scale/business-continuity-and-disaster-recovery\n\nBackup and restore plan to protect against ransomware:\nhttps://docs.microsoft.com/azure/security/fundamentals/backup-plan-to-protect-against-ransomware\n\n", "Customer Security Stakeholders:": "All stakeholders: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security#security-functions", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "GS-9", "Control Domain": "Governance and Strategy", "CIS Controls v7.1 ID(s)": "8.1 - Utilize Centrally Managed Anti-malware Software\n9.4 - Apply Host-Based Firewalls or Port-Filtering", "CIS Controls v8 ID(s)": "4.4 - Implement and Manage a Firewall on Servers\n10.1 - Deploy and Maintain Anti-Malware Software", "NIST SP800-53 r4 ID(s)": "SI-2: FL<PERSON>W REMEDIATION\nSI-3: MALICIOUS CODE PROTECTION\nSC-3: SECURITY FUNCTION ISOLATION\n", "PCI-DSS v3.2.1 ID(s)": "5.1\n5.2\n5.3\n5.4\n11.5", "Recommendation": "Define and implement endpoint security strategy", "Security Principle": "N/A\n", "Azure Guidance": "Establish a cloud endpoint security strategy which includes the following aspects: \n- Deploy the endpoint detection and response and antimalware capability into your endpoint and integrate with the threat detection and SIEM solution and security operations process.\n- Follow Azure Security Benchmark to ensure endpoint related security settings in other respective areas (such as network security, posture vulnerability management, identity and privileged access, and logging and threat detections) are also in place to provide a defense-in-depth protection for your endpoint. \n- Prioritize the endpoint security in your production environment, but ensure the non-production environments (such as test and build environment used in the DevOps process) are also secured and monitored, as these environment can also be used to introduce the malware and vulnerabilities into the production.", "Implementation and additional context": "Azure Security Benchmark - Endpoint security:\nhttps://docs.microsoft.com/azure/security/benchmarks/security-benchmark-v3-endpoint-security\n\nBest practices for endpoint security on Azure:\nhttps://docs.microsoft.com/azure/architecture/framework/security/design-network-endpoints", "Customer Security Stakeholders:": "All stakeholders: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security#security-functions", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}, {"ASB ID": "GS-10", "Control Domain": "Governance and Strategy", "CIS Controls v7.1 ID(s)": "5.1 - Establish Secure Configurations\n18.1 - Establish Secure Coding Practices\n18.8 - Establish a Process to Accept and Address Reports of Software Vulnerabilities\n\n", "CIS Controls v8 ID(s)": "4.1 - Establish and Maintain a Secure Configuration Process\n4.2 - Establish and Maintain a Secure Configuration Process for Network Infrastructure\n16.1 - Establish and Maintain a Secure Application Development Process\n16.2 - Establish and Maintain a Process to Accept and Address Software Vulnerabilities\n", "NIST SP800-53 r4 ID(s)": "SA-12: <PERSON><PERSON><PERSON><PERSON> CHAIN PROTECTION\nSA-15: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> PROCESS, STANDARD<PERSON>, AND TOOLS\nCM-1: CONFIGURATION MANAGEMENT POLICY AND PROCEDURES\nCM-2: BASELINE CONFIGURATION\nCM-6: CONFIGURATION SETTINGS\nAC-2: ACCOUNT MANAGEMENT\nAC-3: ACCESS ENFORCEMENT\nAC-6: LEAST PRIVILEGE\nSA-11: DEVELOPER TESTING AND EVALUATION\nAU-6: AUDIT REVIEW, ANALYSIS, AND REPORTING\nAU-12: AUDIT GENERATION\nSI-4: INFORMATION SYSTEM MONITORING", "PCI-DSS v3.2.1 ID(s)": "2.2\n6.1\n6.2\n6.3\n6.5\n7.1\n10.1\n10.2\n10.3\n10.6\n12.2", "Recommendation": "Define and implement DevOps security strategy", "Security Principle": "N/A\n", "Azure Guidance": "Mandate the security controls as part of the organization’s DevOps engineering and operation standard. Define the security objectives, control requirements, and tooling specifications in accordance with enterprise and cloud security standards in your organization. \n\nEncourage the use of DevOps as an essential operating model in your organization for its benefits in rapidly identifying and remediating vulnerabilities using different type of automations (such as infrastructure as code provision, and automated SAST and DAST scan) throughout the CI/CD workflow. This ‘shift left’ approach also increases visibility and ability to enforce consistent security checks in your deployment pipeline, effectively deploying security guardrails into the environment ahead of time to avoid last minute security surprises when deploying a workload into production.\n\nWhen shifting security controls left into the pre-deployment phases, implement security guardrails to ensure the controls are deployed and enforced throughout your DevOps process. This technology could include Azure ARM templates to define guardrails in the IaC (infrastructure as code), resource provisioning and Azure Policy to audit and restrict which services or configurations can be provisioned into the environment.\n\nFor the run-time security controls of your workload, follow the Azure Security Benchmark to design and implement effective the controls, such as identity and privileged access, network security, endpoint security, and data protection inside your workload applications and services. ", "Implementation and additional context": "Azure Security Benchmark - DevOps security:\nhttps://docs.microsoft.com/azure/security/benchmarks/security-benchmark-v3-devops-security\n\nSecure DevOps: \nhttps://www.microsoft.com/securityengineering/devsecops\n\nCloud Adoption Framework - DevSecOps controls: \nhttps://docs.microsoft.com/azure/cloud-adoption-framework/secure/devsecops-controls \n", "Customer Security Stakeholders:": "All stakeholders: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security#security-functions", "Azure Policy Mapping": "No applicable policy", "Azure Policy GUID": NaN}]}, "chunks": [{"id": "chunk_NS-2_overview", "control_id": "NS-2", "chunk_type": "control_overview", "content": "Control ID: NS-2\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "NS-2", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_NS-3_overview", "control_id": "NS-3", "chunk_type": "control_overview", "content": "Control ID: NS-3\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "NS-3", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_NS-4_overview", "control_id": "NS-4", "chunk_type": "control_overview", "content": "Control ID: NS-4\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "NS-4", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_NS-5_overview", "control_id": "NS-5", "chunk_type": "control_overview", "content": "Control ID: NS-5\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "NS-5", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_NS-6_overview", "control_id": "NS-6", "chunk_type": "control_overview", "content": "Control ID: NS-6\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "NS-6", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_NS-7_overview", "control_id": "NS-7", "chunk_type": "control_overview", "content": "Control ID: NS-7\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "NS-7", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_NS-8_overview", "control_id": "NS-8", "chunk_type": "control_overview", "content": "Control ID: NS-8\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "NS-8", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_NS-9_overview", "control_id": "NS-9", "chunk_type": "control_overview", "content": "Control ID: NS-9\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "NS-9", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_NS-10_overview", "control_id": "NS-10", "chunk_type": "control_overview", "content": "Control ID: NS-10\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "NS-10", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_DP-1_overview", "control_id": "DP-1", "chunk_type": "control_overview", "content": "Control ID: DP-1\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "DP-1", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_DP-2_overview", "control_id": "DP-2", "chunk_type": "control_overview", "content": "Control ID: DP-2\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "DP-2", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_DP-3_overview", "control_id": "DP-3", "chunk_type": "control_overview", "content": "Control ID: DP-3\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "DP-3", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_DP-4_overview", "control_id": "DP-4", "chunk_type": "control_overview", "content": "Control ID: DP-4\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "DP-4", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_DP-5_overview", "control_id": "DP-5", "chunk_type": "control_overview", "content": "Control ID: DP-5\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "DP-5", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_DP-6_overview", "control_id": "DP-6", "chunk_type": "control_overview", "content": "Control ID: DP-6\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "DP-6", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_DP-7_overview", "control_id": "DP-7", "chunk_type": "control_overview", "content": "Control ID: DP-7\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "DP-7", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_DP-8_overview", "control_id": "DP-8", "chunk_type": "control_overview", "content": "Control ID: DP-8\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "DP-8", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_IM-1_overview", "control_id": "IM-1", "chunk_type": "control_overview", "content": "Control ID: IM-1\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "IM-1", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_IM-2_overview", "control_id": "IM-2", "chunk_type": "control_overview", "content": "Control ID: IM-2\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "IM-2", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_IM-3_overview", "control_id": "IM-3", "chunk_type": "control_overview", "content": "Control ID: IM-3\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "IM-3", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_IM-4_overview", "control_id": "IM-4", "chunk_type": "control_overview", "content": "Control ID: IM-4\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "IM-4", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_IM-5_overview", "control_id": "IM-5", "chunk_type": "control_overview", "content": "Control ID: IM-5\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "IM-5", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_IM-6_overview", "control_id": "IM-6", "chunk_type": "control_overview", "content": "Control ID: IM-6\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "IM-6", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_IM-7_overview", "control_id": "IM-7", "chunk_type": "control_overview", "content": "Control ID: IM-7\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "IM-7", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_IM-8_overview", "control_id": "IM-8", "chunk_type": "control_overview", "content": "Control ID: IM-8\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "IM-8", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_IM-9_overview", "control_id": "IM-9", "chunk_type": "control_overview", "content": "Control ID: IM-9\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "IM-9", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_PA-1_overview", "control_id": "PA-1", "chunk_type": "control_overview", "content": "Control ID: PA-1\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "PA-1", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_PA-2_overview", "control_id": "PA-2", "chunk_type": "control_overview", "content": "Control ID: PA-2\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "PA-2", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_PA-3_overview", "control_id": "PA-3", "chunk_type": "control_overview", "content": "Control ID: PA-3\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "PA-3", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_PA-4_overview", "control_id": "PA-4", "chunk_type": "control_overview", "content": "Control ID: PA-4\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "PA-4", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_PA-5_overview", "control_id": "PA-5", "chunk_type": "control_overview", "content": "Control ID: PA-5\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "PA-5", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_PA-6_overview", "control_id": "PA-6", "chunk_type": "control_overview", "content": "Control ID: PA-6\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "PA-6", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_PA-7_overview", "control_id": "PA-7", "chunk_type": "control_overview", "content": "Control ID: PA-7\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "PA-7", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_PA-8_overview", "control_id": "PA-8", "chunk_type": "control_overview", "content": "Control ID: PA-8\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "PA-8", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_PV-1_overview", "control_id": "PV-1", "chunk_type": "control_overview", "content": "Control ID: PV-1\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "PV-1", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_PV-2_overview", "control_id": "PV-2", "chunk_type": "control_overview", "content": "Control ID: PV-2\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "PV-2", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_PV-3_overview", "control_id": "PV-3", "chunk_type": "control_overview", "content": "Control ID: PV-3\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "PV-3", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_PV-4_overview", "control_id": "PV-4", "chunk_type": "control_overview", "content": "Control ID: PV-4\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "PV-4", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_PV-5_overview", "control_id": "PV-5", "chunk_type": "control_overview", "content": "Control ID: PV-5\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "PV-5", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_PV-6_overview", "control_id": "PV-6", "chunk_type": "control_overview", "content": "Control ID: PV-6\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "PV-6", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_PV-7_overview", "control_id": "PV-7", "chunk_type": "control_overview", "content": "Control ID: PV-7\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "PV-7", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_LT-1_overview", "control_id": "LT-1", "chunk_type": "control_overview", "content": "Control ID: LT-1\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "LT-1", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_LT-2_overview", "control_id": "LT-2", "chunk_type": "control_overview", "content": "Control ID: LT-2\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "LT-2", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_LT-3_overview", "control_id": "LT-3", "chunk_type": "control_overview", "content": "Control ID: LT-3\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "LT-3", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_LT-4_overview", "control_id": "LT-4", "chunk_type": "control_overview", "content": "Control ID: LT-4\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "LT-4", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_LT-5_overview", "control_id": "LT-5", "chunk_type": "control_overview", "content": "Control ID: LT-5\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "LT-5", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_LT-6_overview", "control_id": "LT-6", "chunk_type": "control_overview", "content": "Control ID: LT-6\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "LT-6", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_LT-7_overview", "control_id": "LT-7", "chunk_type": "control_overview", "content": "Control ID: LT-7\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "LT-7", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_AM-1_overview", "control_id": "AM-1", "chunk_type": "control_overview", "content": "Control ID: AM-1\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "AM-1", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_AM-2_overview", "control_id": "AM-2", "chunk_type": "control_overview", "content": "Control ID: AM-2\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "AM-2", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_AM-3_overview", "control_id": "AM-3", "chunk_type": "control_overview", "content": "Control ID: AM-3\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "AM-3", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_AM-4_overview", "control_id": "AM-4", "chunk_type": "control_overview", "content": "Control ID: AM-4\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "AM-4", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_AM-5_overview", "control_id": "AM-5", "chunk_type": "control_overview", "content": "Control ID: AM-5\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "AM-5", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_ES-1_overview", "control_id": "ES-1", "chunk_type": "control_overview", "content": "Control ID: ES-1\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "ES-1", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_ES-2_overview", "control_id": "ES-2", "chunk_type": "control_overview", "content": "Control ID: ES-2\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "ES-2", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_ES-3_overview", "control_id": "ES-3", "chunk_type": "control_overview", "content": "Control ID: ES-3\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "ES-3", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_BR-1_overview", "control_id": "BR-1", "chunk_type": "control_overview", "content": "Control ID: BR-1\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "BR-1", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_BR-2_overview", "control_id": "BR-2", "chunk_type": "control_overview", "content": "Control ID: BR-2\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "BR-2", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_BR-3_overview", "control_id": "BR-3", "chunk_type": "control_overview", "content": "Control ID: BR-3\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "BR-3", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_BR-4_overview", "control_id": "BR-4", "chunk_type": "control_overview", "content": "Control ID: BR-4\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "BR-4", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_IR-1_overview", "control_id": "IR-1", "chunk_type": "control_overview", "content": "Control ID: IR-1\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "IR-1", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_IR-2_overview", "control_id": "IR-2", "chunk_type": "control_overview", "content": "Control ID: IR-2\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "IR-2", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_IR-3_overview", "control_id": "IR-3", "chunk_type": "control_overview", "content": "Control ID: IR-3\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "IR-3", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_IR-4_overview", "control_id": "IR-4", "chunk_type": "control_overview", "content": "Control ID: IR-4\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "IR-4", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_IR-5_overview", "control_id": "IR-5", "chunk_type": "control_overview", "content": "Control ID: IR-5\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "IR-5", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_IR-6_overview", "control_id": "IR-6", "chunk_type": "control_overview", "content": "Control ID: IR-6\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "IR-6", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_IR-7_overview", "control_id": "IR-7", "chunk_type": "control_overview", "content": "Control ID: IR-7\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "IR-7", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_DS-1_overview", "control_id": "DS-1", "chunk_type": "control_overview", "content": "Control ID: DS-1\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "DS-1", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_DS-2_overview", "control_id": "DS-2", "chunk_type": "control_overview", "content": "Control ID: DS-2\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "DS-2", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_DS-3_overview", "control_id": "DS-3", "chunk_type": "control_overview", "content": "Control ID: DS-3\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "DS-3", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_DS-4_overview", "control_id": "DS-4", "chunk_type": "control_overview", "content": "Control ID: DS-4\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "DS-4", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_DS-5_overview", "control_id": "DS-5", "chunk_type": "control_overview", "content": "Control ID: DS-5\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "DS-5", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_DS-6_overview", "control_id": "DS-6", "chunk_type": "control_overview", "content": "Control ID: DS-6\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "DS-6", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_DS-7_overview", "control_id": "DS-7", "chunk_type": "control_overview", "content": "Control ID: DS-7\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "DS-7", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_GS-1_overview", "control_id": "GS-1", "chunk_type": "control_overview", "content": "Control ID: GS-1\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "GS-1", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_GS-2_overview", "control_id": "GS-2", "chunk_type": "control_overview", "content": "Control ID: GS-2\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "GS-2", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_GS-3_overview", "control_id": "GS-3", "chunk_type": "control_overview", "content": "Control ID: GS-3\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "GS-3", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_GS-4_overview", "control_id": "GS-4", "chunk_type": "control_overview", "content": "Control ID: GS-4\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "GS-4", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_GS-5_overview", "control_id": "GS-5", "chunk_type": "control_overview", "content": "Control ID: GS-5\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "GS-5", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_GS-6_overview", "control_id": "GS-6", "chunk_type": "control_overview", "content": "Control ID: GS-6\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "GS-6", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_GS-7_overview", "control_id": "GS-7", "chunk_type": "control_overview", "content": "Control ID: GS-7\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "GS-7", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_GS-8_overview", "control_id": "GS-8", "chunk_type": "control_overview", "content": "Control ID: GS-8\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "GS-8", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_GS-9_overview", "control_id": "GS-9", "chunk_type": "control_overview", "content": "Control ID: GS-9\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "GS-9", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}, {"id": "chunk_GS-10_overview", "control_id": "GS-10", "chunk_type": "control_overview", "content": "Control ID: GS-10\nName: \nDescription: \nSeverity: HIGH\n", "metadata": {"control_id": "GS-10", "severity": "HIGH", "domain": "UNKNOWN", "chunk_type": "overview"}}]}