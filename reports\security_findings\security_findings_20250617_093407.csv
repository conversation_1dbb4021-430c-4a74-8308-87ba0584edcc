Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,143,"App Service 'onefuzz-daily-ui' allows public network access ('publicNetworkAccess': 'Enabled') and has 'ipSecurityRestrictions' with 'ipAddress': 'Any' and 'action': 'Allow', exposing the app to the public internet. This violates the requirement to secure all public endpoints.",Restrict 'ipSecurityRestrictions' to only allow trusted IP ranges. Set 'publicNetworkAccess' to 'Disabled' or configure access restrictions to limit exposure.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,151,"App Service 'onefuzz-daily-ui' allows public network access to the SCM endpoint ('scmIpSecurityRestrictions' with 'ipAddress': 'Any' and 'action': 'Allow'), exposing the SCM endpoint to the public internet. This violates the requirement to secure all public endpoints.",Restrict 'scmIpSecurityRestrictions' to only allow trusted IP ranges. Do not use 'Any' for SCM endpoint access.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,143,"App Service 'onefuzz-daily-ui' does not use private endpoints and allows public network access ('publicNetworkAccess': 'Enabled'), which is a network security weakness.",Implement private endpoints for the App Service and set 'publicNetworkAccess' to 'Disabled' to restrict access to internal networks only.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,143,App Service 'onefuzz-daily-ui' does not specify the use of customer-managed keys (CMK) or confirm encryption at rest for all data storage. This violates the requirement to enable encryption at rest.,"Configure the App Service to use encryption at rest, preferably with customer-managed keys (CMK) if handling sensitive data.",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,54,"App Service 'onefuzz-daily-ui' has 'sslState' set to 'Disabled' for hostName 'onefuzz-daily-ui.azurewebsites.net', which allows unencrypted HTTP traffic. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates entries to enforce HTTPS. Ensure all endpoints require TLS 1.2 or higher.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,61,"App Service 'onefuzz-daily-ui' has 'sslState' set to 'Disabled' for hostName 'onefuzz-daily-ui.scm.azurewebsites.net', which allows unencrypted HTTP traffic. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates entries to enforce HTTPS. Ensure all endpoints require TLS 1.2 or higher.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,143,"App Service 'onefuzz-daily-ui' configuration includes a 'publishingUsername' property, which may expose sensitive information if not stored securely. Sensitive data should be stored in Azure Key Vault.",Store sensitive information such as publishing credentials in Azure Key Vault and reference them securely in the template.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,143,"App Service 'onefuzz-daily-ui' has 'phpVersion' set to '5.6', which is deprecated and may expose sensitive data due to unpatched vulnerabilities.",Upgrade 'phpVersion' to a supported and secure version to reduce the risk of sensitive data exposure.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,143,"App Service 'onefuzz-daily-ui' has 'httpLoggingEnabled', 'requestTracingEnabled', and 'detailedErrorLoggingEnabled' all set to false, which may hinder detection of sensitive data exposure or security incidents.",Enable logging and tracing features to monitor for sensitive data exposure and security incidents.,N/A,AI,Generic
