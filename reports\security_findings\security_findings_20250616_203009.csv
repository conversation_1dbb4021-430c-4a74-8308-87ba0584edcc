Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
CRITICAL,DP-3,function-settings.bicep,12,Sensitive values such as 'app_insights_key' (Application Insights Instrumentation Key) are injected directly into the function app settings. Storing secrets in application settings rather than using Key Vault increases the risk of accidental disclosure.,"Store secrets such as instrumentation keys in Azure Key Vault, and reference them in the function app configuration via Key Vault references (using @Microsoft.KeyVault references). Avoid hard-coding or passing secrets via parameters or app settings.",N/A,AI,Generic
CRITICAL,NS-1,keyvault.bicep,20,"Key Vault 'networkAcls.defaultAction' is set to 'Allow', exposing the Key Vault to the public internet unless further restricted by IP or VNet rules. This violates network security best practices as per ASB, which recommends restricting access to sensitive resources like Key Vaults.",Set 'networkAcls.defaultAction' to 'Deny' and only allow specific IP addresses and virtual networks that require access. This change will prevent unauthorized access to the Key Vault from the public internet.,N/A,AI,Generic
CRITICAL,NS-2,keyvault.bicep,20,"Key Vault is exposed to public endpoints due to 'networkAcls.defaultAction: Allow' and 'bypass: AzureServices', enabling broad network access. ASB requires minimizing Key Vault endpoint exposure.","Set 'networkAcls.defaultAction' to 'Deny', configure 'bypass' to only necessary options, and tightly define the 'ipRules' and 'virtualNetworkRules' to allow access exclusively from trusted sources.",N/A,AI,Generic
HIGH,NS-5,app-config.bicep,7,"The App Configuration resource does not configure or restrict public network access, nor does it define a private endpoint. Without a private endpoint, the service is accessible over the public internet, increasing risk of unauthorized access, which violates ASB NS-5 - Use Private Endpoints.",Define a 'privateEndpointConnections' property or deploy a 'Microsoft.Network/privateEndpoints' resource linked to the App Configuration instance so that access is only permitted from your private network.,N/A,AI,Generic
HIGH,NS-2,app-config.bicep,7,"App Configuration endpoint is exposed as a public endpoint by default as there is no configuration explicitly securing or disabling public network access. This fails to minimize exposure, which violates ASB NS-2 - Protect public endpoints.","Set the 'publicNetworkAccess' property to 'Disabled' in the resource definition, or use Azure policies to restrict access to the service from only approved networks.",N/A,AI,Generic
HIGH,DP-3,app-config.bicep,21,"There is no evidence that sensitive configuration values in 'keyValues' are protected using Azure Key Vault references. If secrets, keys, or sensitive data are being placed directly into App Configuration through 'keyValues', this may lead to sensitive information disclosure, conflicting with ASB DP-3 - Manage sensitive information disclosure.","Do not store secrets or sensitive configuration data directly in App Configuration. Store secrets in Azure Key Vault, and reference them via Key Vault references within App Configuration.",N/A,AI,Generic
HIGH,NS-5,event-grid.bicep,1,"The Event Grid system topics and event subscriptions are configured to deliver events to a Storage Queue destination, but there is no indication that Private Endpoints are used for the associated storage accounts or the event grid resources. Without private endpoints, resources may be exposed to public networks, increasing the attack surface.","Update the storage accounts and Event Grid resources to use Private Endpoints to ensure access occurs only over private IP addresses, and not via public internet endpoints. This will help mitigate unauthorized access.",N/A,AI,Generic
HIGH,NS-1,event-grid.bicep,1,"The template does not provision or reference any Network Security Groups (NSGs) or Azure Firewall rules in connection with the deployment of resources. This leaves traffic to and from the storage accounts and Event Grid resources potentially unfiltered, in violation of Azure Security Benchmark recommendations.",Add and associate NSGs or Azure Firewall rules to control and restrict the network traffic to only what is needed for your Event Grid and storage resources. Ensure that only trusted sources are allowed.,N/A,AI,Generic
HIGH,NS-2,event-grid.bicep,1,"Public endpoint protection is not addressed for the Storage Accounts or Event Grid system topics, which may allow access from untrusted networks, increasing risk of data breach.",Restrict public network access by enabling access only from private endpoints or trusted networks. Configure the Storage Account and Event Grid resources to disallow public access where possible.,N/A,AI,Generic
HIGH,NS-1,function-settings.bicep,29,"The storage account 'funcStorage' is referenced as an existing resource, but there is no evidence in the template to show that it is protected by a Network Security Group (NSG) or Azure Firewall, as required by ASB NS-1 for sensitive resources.","Ensure that the underlying storage account is protected by a Network Security Group with restrictive rules or Azure Firewall. If using a public endpoint, consider limiting network access via firewall rules or converting to private endpoint access.",N/A,AI,Generic
HIGH,NS-2,function-settings.bicep,58,"The App Service function's endpoint is accessible at 'https://${instance_name}.azurewebsites.net', which is public by default. The template does not include configuration to restrict public access (e.g., using Access Restrictions, IP filtering, or Private Endpoints).",Configure access restrictions on the App Service by allowing only trusted IP addresses and/or use Private Endpoints to limit exposure to the internet.,N/A,AI,Generic
HIGH,NS-2,function-settings.bicep,53,"The Azure SignalR endpoint is exposed via 'https://${signalRName}.service.signalr.net', which defaults to public access. No network restrictions are apparent, exposing the service to the internet.",Restrict network access to the SignalR service by enabling Private Endpoint or configuring it to accept only traffic from trusted sources or from within your virtual networks.,N/A,AI,Generic
HIGH,NS-5,function-settings.bicep,29,"The storage account 'funcStorage' is referenced and used for app storage, but there is no indication that private endpoint connectivity is enforced or enabled, which could lead to exposure of sensitive data via public endpoints.",Enable Private Endpoint on the storage account so that traffic is restricted to your internal network only. Update function settings to reference the storage account via its private endpoint.,N/A,AI,Generic
HIGH,DP-3,function-settings.bicep,22,"Sensitive identifiers such as 'cli_app_id', 'registration_app_id', and others are provided as plain parameters and inserted into application settings, potentially leading to information disclosure if app settings are dumped or accessed inappropriately.","Consider whether these parameters are truly non-sensitive; if so, document decision. If any can be abused or constitute secrets, move them to Key Vault and reference via secure configuration.",N/A,AI,Generic
HIGH,AM-1,function-settings.bicep,0,"No access (RBAC) assignments are visible in the template, suggesting that least privilege and access scoping may not be explicitly configured for interacting resources such as Key Vault, Storage, or App Insights.","Add explicit RBAC assignments scoped to required resources, and assign only the minimum roles needed to the user-assigned or system-assigned managed identities.",N/A,AI,Generic
HIGH,NS-1,function.bicep,73,"The storage account 'funcStorage' does not specify any network security restrictions such as Virtual Network (VNet) service endpoints, firewall rules, or private endpoints. This exposes the storage account to potential access from the public internet, which violates ASB NS-1 (Protected resources using network security groups or Azure Firewall).",Restrict storage account network access by enabling network rules to allow only trusted subnets or use private endpoints. Specify 'networkAcls' and/or define a private endpoint resource for 'funcStorage'.,N/A,AI,Generic
HIGH,NS-2,function.bicep,73,No configuration enforces private endpoint usage or restricts public network access to the storage account 'funcStorage'. Failing to secure public access to endpoints increases risk of unauthorized access.,Configure storage account with 'publicNetworkAccess' set to 'Disabled' and use Private Endpoint resources to limit exposure to the public internet.,N/A,AI,Generic
HIGH,NS-3,function.bicep,73,"No Network Security Groups (NSGs) are defined or referenced for protecting the subnet where the storage account or the function app resides, which is required to control traffic flow to sensitive resources.",Deploy and associate NSGs with the relevant subnets (especially for 'hubSubnetId') used by the function app and ensure rules follow least privilege principles.,N/A,AI,Generic
HIGH,DP-3,function.bicep,86,"The diagnostic logs for the function app are written to a storage account using a SAS token generated inline within the template, which could be intercepted or inadvertently logged. Storing sensitive tokens or secrets directly in configuration increases risk of exposure.","Use Azure Key Vault references for storing and retrieving SAS tokens or sensitive information, and avoid generating or storing them inline in templates.",N/A,AI,Generic
HIGH,NS-5,function.bicep,58,"The function app's storage account and subnet integration ('virtualNetworkSubnetId: hubSubnetId') is present, but the template does not configure private endpoints for the storage account, leaving a gap in secure resource access.",Implement a private endpoint for the storage account and ensure only private connectivity from the function app subnet to the storage account.,N/A,AI,Generic
HIGH,NS-1,hub-network.bicep,2,"The virtual network subnet 'hub-subnet' does not have a Network Security Group (NSG) associated with it. Per ASB v3.0, sensitive network resources must be protected with NSGs or Azure Firewall.",Associate a Network Security Group (NSG) with the 'hub-subnet' to control and limit inbound and outbound traffic based on your security requirements.,N/A,AI,Generic
HIGH,NS-3,hub-network.bicep,2,"No Network Security Group (NSG) resource is defined or applied to the subnet 'hub-subnet', leaving the subnet unprotected from unwanted traffic.",Define an NSG resource in this Bicep file and assign it to 'hub-subnet' to implement granular access controls. Create specific inbound and outbound rules to enforce least privilege.,N/A,AI,Generic
HIGH,NS-1,instance-config.bicep,35,"The configuration defines a large address space ('10.0.0.0/8') and subnet ('10.0.0.0/16') for the VM network but does not specify the use of Network Security Groups (NSGs) or Azure Firewall to protect Compute resources. This violates ASB control NS-1, which requires explicit use of NSGs or Azure Firewall to limit access to sensitive or critical resources.",Explicitly define and implement NSGs or Azure Firewall in the network configuration to control both inbound and outbound network traffic for VMs. Ensure restrictive rules are in place and that access to management ports and trusted endpoints is controlled.,N/A,AI,Generic
HIGH,NS-3,instance-config.bicep,35,"No Network Security Groups (NSGs) are applied to the defined network (address_space: '10.0.0.0/8', subnet: '10.0.0.0/16'). ASB control NS-3 requires explicit use of NSGs to restrict inbound and outbound traffic to/from compute resources.",Add NSG definitions that restrict network traffic to only necessary ports and sources. Associate the NSGs with the relevant subnets or network interfaces of the VM resources.,N/A,AI,Generic
HIGH,NS-10,instance-config.bicep,1,"The configuration does not specify the use of Azure Bastion for secure VM access, which is required by ASB control NS-10. Without Bastion, management ports must be opened directly or via jump hosts, increasing exposure risks.","Include Azure Bastion in the design to provide secure, tunneled access to VM management consoles (SSH/RDP) without exposing these ports directly to the internet. Update network and deployment settings accordingly.",N/A,AI,Generic
HIGH,DP-3,instance-config.bicep,12,"Sensitive identity information, such as tenant IDs, client IDs, and tenant domains, is sourced from the external 'specificConfig' object and stored directly in VM function/application settings. This may expose sensitive details at runtime or via configuration leaks, violating control DP-3.","Exclude sensitive identity and configuration parameters from direct function/application settings, or ensure they are referenced securely from an Azure Key Vault instance. Apply access control to configuration files, and audit secret usage.",N/A,AI,Generic
HIGH,NS-2,ip-rules.bicep,3,"The 'corpNetIps' variable allows extremely broad IP address ranges (such as '*******/8', '********/8', etc.), potentially including large portions of the public internet. This undermines network isolation and exposes resources to unnecessary risk, violating the requirement to minimize exposure of public endpoints.","Restrict allowed IP address ranges to those that are specific and necessary. Where feasible, use private IP address ranges or smaller, tightly controlled public IP ranges to minimize exposure. Only permit trusted and required sources.",N/A,AI,Generic
HIGH,NS-1,ip-rules.bicep,3,"Broad IP ranges in 'corpNetIps' (such as complete /8 blocks) likely do not provide the granular network protection expected by NSG or Azure Firewall controls. This may inadvertently allow unwanted access to critical Azure resources, violating the principle of least privilege for network access.","Narrow the allowed IP address ranges to only those absolutely necessary for business operations, and use network security groups or firewalls to enforce access. Regularly review the IP allow list for unnecessary entries.",N/A,AI,Generic
HIGH,NS-2,ip-rules.bicep,20,"The list of IP addresses includes several /8 ranges (allowing over 16 million addresses each) and /16 ranges (over 65,000 addresses). Such large accessible ranges effectively expose your endpoint to a vast number of machines, many outside of your control.","Review all allowed IP ranges and reduce them to the minimal set required for your workload. Whenever possible, use specific IP addresses or smaller CIDR blocks, and implement additional authentication and authorization controls.",N/A,AI,Generic
HIGH,DP-3,operational-insights.bicep,88,"The output 'appInsightsInstrumentationKey' exposes the Application Insights Instrumentation Key, which is a sensitive credential. Revealing this key can allow unauthorized data ingestion or access to telemetry, violating sensitive information management policies.","Remove the 'appInsightsInstrumentationKey' output, or make it a secure output protected via Azure Key Vault. Application Insights Instrumentation Keys should be stored in Azure Key Vault and not exposed in templates or outputs.",N/A,AI,Generic
HIGH,DP-3,operational-insights.bicep,87,"The output 'appInsightsAppId' exposes the Application Insights App ID. While less sensitive than the Instrumentation Key, public exposure may still provide intelligence to attackers or facilitate reconnaissance.","Assess whether outputting 'appInsightsAppId' is necessary. If so, ensure access to outputs is restricted and never expose this value where not strictly needed.",N/A,AI,Generic
HIGH,DP-3,operational-insights.bicep,6,"The template has 'workbookData' as a parameter object, but there is no use of secure parameters, Key Vault references, or secret handling for possible sensitive values (such as embedded queries or connections inside workbooks). This risks unintentional sensitive data disclosure if workbook data includes secrets or connection strings.","For any properties (like workbookData) that may contain secrets, references, tokens, or other sensitive fields, require secure string parameters or Key Vault references, and ensure workbook authors avoid embedding secrets in definitions.",N/A,AI,Generic
HIGH,NS-1,scaleset-networks.bicep,1,"No Network Security Groups (NSGs) or Azure Firewall are defined or associated with the subnet 'scaleset', leaving deployed resources without protection from unwanted or malicious network traffic.","Define and associate an NSG with the 'scaleset' subnet. Configure rules to restrict inbound and outbound traffic to only required sources, destinations, and ports. Consider integrating with Azure Firewall for additional protection as appropriate.",N/A,AI,Generic
HIGH,NS-3,scaleset-networks.bicep,1,"The template creates a subnet ('scaleset') but does not deploy or associate any Network Security Group (NSG), missing a key layer of access control to filter traffic to/from network resources.",Deploy an NSG resource and associate it explicitly to the 'scaleset' subnet. Define granular inbound and outbound NSG rules following the least privilege principle.,N/A,AI,Generic
HIGH,NS-1,server-farms.bicep,0,No network security controls such as Network Security Groups (NSGs) or Azure Firewall are defined or referenced for the App Service or Key Vault resources. This potentially leaves resources exposed to unfiltered network traffic.,Implement Network Security Groups or link App Service and Key Vault behind Azure Firewall or Application Gateway with appropriate access controls to restrict traffic as per least privilege.,N/A,AI,Generic
HIGH,NS-2,server-farms.bicep,0,"The template does not configure private endpoints or access restrictions on the Key Vault or App Service, which may result in public endpoints being accessible over the internet. Public endpoints increase attack surface and risk of unauthorized access.","Configure private endpoints for Key Vault and App Service, or explicitly restrict public access with IP filtering or access restrictions.",N/A,AI,Generic
HIGH,NS-5,server-farms.bicep,0,"Key Vault and App Service do not have private endpoints defined, which means their endpoints may be accessible publicly. This raises the risk of data exposure.",Implement Private Endpoints for Key Vault and App Service to ensure network traffic flows only over the Azure backbone and is not accessible from the wider internet.,N/A,AI,Generic
HIGH,NS-2,storage-accounts.bicep,24,"The storage accounts defined (e.g., 'storageAccountFunc', 'storageAccountFuzz', 'storageAccountsCorpus') have 'networkAcls.defaultAction' set to 'Allow', which permits all network traffic by default, including from public IPs. This exposes storage accounts to the internet unless specifically locked down via IP or VNet rules.",Set 'networkAcls.defaultAction' to 'Deny' and explicitly define 'ipRules' and 'virtualNetworkRules' for approved networks. This ensures that only trusted networks can access these storage accounts and all other traffic is denied by default.,N/A,AI,Generic
HIGH,NS-6,storage-accounts.bicep,24,"Default network rule 'Allow' along with 'bypass' permitting 'AzureServices' exposes the storage account endpoints publicly to all Azure services, including potentially untrusted ones. This can increase your attack surface.","Limit 'bypass' to only necessary services. Evaluate if 'AzureServices' must be included, and minimize exposure by specifying only required trusted services in the 'bypass' property.",N/A,AI,Generic
MEDIUM,NS-4,event-grid.bicep,1,There is no reference to Azure Firewall or any third-party firewall solution to provide advanced threat protection to the Event Grid and Storage Account resources.,"Integrate Azure Firewall or a trusted third-party firewall with your virtual network to inspect, filter, and log network traffic to these resources as recommended by the benchmark.",N/A,AI,Generic
MEDIUM,DP-1,event-grid.bicep,1,The template does not specify that encryption at rest is enabled for Storage Queues or guarantee compliance for all underlying storage resources. Lack of explicit configuration leaves the risk of default or insufficient encryption.,"Explicitly set the Storage Account property 'enable encryption at rest' to true, and ensure all data destinations use encrypted storage.",N/A,AI,Generic
MEDIUM,DP-2,event-grid.bicep,1,The template does not explicitly enforce use of TLS 1.2 or higher for data delivery to Storage Queues or for any connections to storage resources.,"Configure the Storage Account to require TLS 1.2 or higher, and ensure that all client and service connections enforce this minimum protocol version.",N/A,AI,Generic
MEDIUM,NS-9,event-grid.bicep,1,"The template does not configure diagnostic settings or network security monitoring for any deployed resources. This impedes the ability to audit, detect, or respond to potential security events.","Enable diagnostic settings and log analytics for the Event Grid system topics, subscriptions, and associated Storage Accounts. Ensure logs are sent to Azure Monitor, Log Analytics, or a SIEM for ongoing monitoring.",N/A,AI,Generic
MEDIUM,DP-1,function-settings.bicep,29,There is no evidence in the template that encryption at rest is enforced or checked for the storage account used by the function app.,"Ensure that the referenced storage account has encryption at rest enabled (enabled by default in Azure, but if the resource is managed elsewhere this must be validated). Optionally, configure encryption with customer-managed keys (CMK) if required by your risk profile.",N/A,AI,Generic
MEDIUM,DP-2,function-settings.bicep,43,"The template does not specify the minimum TLS version (should be 1.2 or higher) for the App Service function app. By default, weaker protocols might be permitted, which violates data-in-transit requirements.",Set 'minTlsVersion' to '1.2' or higher for App Service by adding it to the 'Microsoft.Web/sites/config' resource properties.,N/A,AI,Generic
MEDIUM,IM-3,function-settings.bicep,63,There is no evidence in the template that conditional access policies are configured or enforced for accessing the App Service or associated resources.,Ensure conditional access policies are configured at the tenant or resource group level in Azure Active Directory for all users and applications accessing this app.,N/A,AI,Generic
MEDIUM,DP-2,function.bicep,36,"No explicit enforcement of TLS version (e.g., TLS 1.2 or above) in the App Service's site configuration. Without explicit declaration, weaker protocols might be permitted, risking data in transit.",Add 'minTlsVersion' property to the function app site configuration and set it to '1.2' or higher.,N/A,AI,Generic
MEDIUM,DP-3,function.bicep,86,SAS tokens for blob logging are generated and passed as parameters instead of leveraging Azure Key Vault for secure storage and reference. This increases the risk of accidental leak or exposure of sensitive credentials.,"Use Azure Key Vault to securely store and reference SAS tokens/secrets, and configure application settings to use Key Vault references rather than inline string interpolation.",N/A,AI,Generic
MEDIUM,IM-11,function.bicep,37,"There are no RBAC role assignments defined within the template for the managed identities or the app, risking overprivileged, default, or improper access grants at resource scope.","Explicitly assign required Azure RBAC roles to managed identities with the lowest necessary permissions, scoped only to the needed resources.",N/A,AI,Generic
MEDIUM,AM-1,function.bicep,48,"The managed identity is configured but there are no permissions (role assignments) granted in this template. If not defined elsewhere, this can result in manually or overly permissively assigned access.",Define all necessary but minimal RBAC role assignments for the managed identities in the template for traceability and to enforce least privilege.,N/A,AI,Generic
MEDIUM,NS-4,hub-network.bicep,2,The template does not deploy or reference Azure Firewall or any third-party firewall to further protect the virtual network perimeter as recommended by ASB v3.0.,"Consider deploying Azure Firewall in the hub network, and configure appropriate rules to control and inspect traffic within and across network boundaries, especially for critical workloads.",N/A,AI,Generic
MEDIUM,NS-9,hub-network.bicep,2,The template does not enable or configure network flow logging or monitoring via Azure Monitor or Network Watcher for the virtual network.,"Enable network diagnostic logging and monitoring for network resources (e.g., using Network Watcher flow logs) to ensure visibility and auditability of network activities.",N/A,AI,Generic
MEDIUM,NS-7,instance-config.bicep,1,There is no evidence that Just-in-Time (JIT) VM access is enabled for VM management ports (such as SSH or RDP) for the compute resources. ASB control NS-7 recommends enabling JIT VM access to reduce exposure of management ports.,Integrate Just-in-Time VM access configuration for all VMs to ensure management ports are exposed only when necessary and only to authorized users.,N/A,AI,Generic
MEDIUM,NS-5,ip-rules.bicep,1,"The provided configuration only outputs IP allow rules and does not leverage Azure Private Endpoints for internal access to resources, meaning any traffic allowed by the listed IPs is traversing the public Azure network.","Use Azure Private Endpoints to enable secure, private connectivity from virtual networks to Azure services, eliminating exposure to the public internet even if IP allow rules are present.",N/A,AI,Generic
MEDIUM,NS-6,ip-rules.bicep,1,"No virtual network service endpoints are configured or referenced, missing the opportunity to further restrict Azure resource access to specified VNets/subnets.","Implement Virtual Network Service Endpoints to limit access to Azure resources from specific subnets, and combine service endpoints with NSG rules for additional protection.",N/A,AI,Generic
MEDIUM,IM-6,keyvault.bicep,22,"Key Vault is configured with 'enableRbacAuthorization: true' but defines an empty 'accessPolicies' array. While RBAC is recommended, absence of managed role assignments in the template risks unintentional access gaps or reliance on later manual configuration, which can lead to privilege creep or misconfiguration.",Explicitly define required RBAC role assignments for principals needing access within the deployment process to ensure least privilege and auditability.,N/A,AI,Generic
MEDIUM,NS-2,scaleset-networks.bicep,6,"A static public IP address ('scaleset-outbound-ip') is provisioned and attached to a NAT Gateway, but there are no documented restrictions or protections for this endpoint, risking exposure.",Ensure that outbound public IPs are restricted via NSG outbound rules or an Azure Firewall. Confirm that only required outbound destinations are permitted. Monitor and audit traffic through the NAT Gateway.,N/A,AI,Generic
MEDIUM,NS-3,server-farms.bicep,0,There is no evidence of Network Security Groups (NSGs) being applied to protect inbound and outbound traffic for App Service or Key Vault.,Define and associate appropriate NSGs to control traffic flows for resources handling sensitive data.,N/A,AI,Generic
MEDIUM,IM-6,server-farms.bicep,0,"The template references Key Vault and App Service, but does not define any Role-Based Access Control (RBAC), role assignments, or access policies. This may result in default or overly permissive access.",Explicitly configure RBAC assignments and/or Key Vault access policies to limit privileges for users and applications to the minimum required.,N/A,AI,Generic
MEDIUM,IM-8,server-farms.bicep,0,"No Managed Identities are enabled or used for the App Service, which is recommended when accessing Azure resources such as Key Vault, to avoid use of secrets and reduce risk of credential leakage.",Enable a system-assigned or user-assigned managed identity for App Service and grant this identity least privilege access to Key Vault.,N/A,AI,Generic
MEDIUM,NS-2,signalR.bicep,4,"The SignalR Service resource is deployed without explicit network access controls restricting or securing its public endpoint. By default, Standard tier Azure SignalR exposes a public endpoint unless 'networkACLs' are configured. This leaves the service potentially accessible from the public internet, which violates the requirement to secure all public endpoints.","Add the 'networkACLs' property to explicitly configure an allow/deny list restricting public network access, only permitting trusted networks or private endpoints. Refer to Azure SignalR Service documentation to restrict access to required clients/networks only.",N/A,AI,Generic
MEDIUM,NS-1,signalR.bicep,4,"There is no use of network security groups (NSGs), virtual network service endpoints, or Azure Firewall to protect the deployed SignalR resource. Sensitive PaaS resources should restrict network-level access to only required sources.","Deploy the SignalR resource within a virtual network and configure private endpoints, NSGs, or Azure Firewall rules to limit inbound and outbound access. This can be achieved with 'networkACLs' (for SignalR) or deployment behind private endpoints.",N/A,AI,Generic
MEDIUM,DP-2,storage-accounts.bicep,23,"The property 'supportsHttpsTrafficOnly: true' enforces HTTPS, but the template does not explicitly set the minimum TLS version (e.g., TLS 1.2) for data in transit. TLS 1.2+ should be enforced to meet modern encryption standards.","Specify the 'minimumTlsVersion' property set to 'TLS1_2' (or higher, if available) within each storage account resource.",N/A,AI,Generic
MEDIUM,DP-3,storage-accounts.bicep,1,"Some parameters ('owner', 'storageAccountNameFunc', etc.) are defined as plain parameters. While no secrets are visible inline, there is no indication of sensitive data being referenced via Azure Key Vault or any secrets management. This may result in sensitive information being managed insecurely if future configuration changes introduce secrets.","For any future parameters holding secrets or sensitive information (such as SAS tokens or connection strings), reference them securely from Azure Key Vault and avoid inline definition.",N/A,AI,Generic
LOW,DP-6,event-grid.bicep,1,"There is no evidence that Customer-Managed Keys (CMK) are used for the storage account or the queue data, which may be required for sensitive or regulated workloads.",Update storage resources configuration to employ Customer-Managed Keys (CMK) for encryption to gain greater control and auditability over encryption keys.,N/A,AI,Generic
LOW,IM-8,function-settings.bicep,60,"A user assigned identity client ID ('func_user_assigned_identity_client_id') is referenced and used to support managed identity-based authentication, which aligns with best practices. However, the template does not show the actual assignment, nor does it restrict the identity's use with RBAC.",Ensure the User Assigned Managed Identity is properly assigned to this App Service and only granted the minimum privileges necessary for operation.,N/A,AI,Generic
LOW,DP-1,function.bicep,73,"The deployment does not explicitly enforce encryption settings for the storage account. Although Azure Storage enables encryption by default, best practices dictate explicit configuration to ensure compliance.","Specify 'encryption' settings in the storage account resource, explicitly enabling encryption for all services with customer-managed keys if required.",N/A,AI,Generic
LOW,IM-5,function.bicep,90,"No diagnostic settings are defined to monitor identity or access management activities (e.g., sign-ins, role changes) for resources impacted by identity such as managed identities and function app.",Enable diagnostic logging for Azure AD and resource logs relevant to identity and access activities.,N/A,AI,Generic
LOW,NS-8,hub-network.bicep,2,There is no evidence of DDoS Protection Standard being enabled on the virtual network. This exposes resources to volumetric attacks.,Attach an Azure DDoS Protection Plan to the virtual network to enhance its resilience against distributed denial-of-service attacks.,N/A,AI,Generic
LOW,NS-3,ip-rules.bicep,1,The code outputs allow rules but does not demonstrate the implementation or configuration of Network Security Groups (NSGs) for LogicApps or any other Azure resource.,"Apply NSGs to relevant Azure resources, defining explicit inbound and outbound rules according to the principle of least privilege, and ensure only trusted sources are allowed.",N/A,AI,Generic
LOW,NS-4,ip-rules.bicep,1,There is no reference to Azure Firewall or any third-party firewall deployment to protect Azure resources from unauthorized network access.,Consider deploying Azure Firewall or an equivalent third-party solution to provide centralized network filtering and logging capabilities.,N/A,AI,Generic
LOW,NS-8,ip-rules.bicep,1,There is no evidence of DDoS Protection being enabled or referenced for the network resources involved.,Ensure Azure DDoS Protection is enabled for all public-facing or business-critical resources to defend against distributed denial-of-service attacks.,N/A,AI,Generic
LOW,NS-9,ip-rules.bicep,1,"The template does not include any monitoring or logging configuration for network traffic, as recommended for visibility and incident response.",Implement Azure Monitor and Network Watcher for all resources. Set up diagnostic logging for all network-related resources to capture and retain traffic flow information.,N/A,AI,Generic
LOW,DP-6,keyvault.bicep,16,"'enabledForDiskEncryption' is set to 'false' on the Key Vault, meaning it cannot be used for VM disk encryption key management. While not always required, ASB recommends usage of Customer Managed Keys (CMK) for encryption of sensitive data, including disks, as appropriate.","If the Key Vault will be used for disk encryption scenarios, set 'enabledForDiskEncryption' to 'true'. Also consider configuring the Key Vault with appropriate CMK usage for additional resources if applicable.",N/A,AI,Generic
LOW,NS-6,scaleset-networks.bicep,27,"The 'serviceEndpoints' array for the subnet is empty, meaning no Azure Virtual Network Service Endpoints are configured. This misses the opportunity to secure any future PaaS resources accessed from this subnet.","Add required Service Endpoints (e.g., for Azure Storage, SQL, or Key Vault) as appropriate to the subnet configuration to ensure secured service access confined to the VNet.",N/A,AI,Generic
LOW,NS-5,scaleset-networks.bicep,25,"The subnet 'scaleset' enables 'privateEndpointNetworkPolicies' and 'privateLinkServiceNetworkPolicies', but does not actually implement any Private Endpoints, missing the ASB recommendation for private resource access.","Deploy Azure Private Endpoints for resources that support them to ensure communication remains on the Microsoft backbone, reducing public exposure.",N/A,AI,Generic
LOW,NS-8,scaleset-networks.bicep,1,"Template does not enable Azure DDoS Protection for the VNet or public IP, leaving resources at risk from volumetric attacks.",Enable Azure DDoS Protection Standard on the virtual network to protect against distributed denial-of-service attacks.,N/A,AI,Generic
LOW,NS-9,scaleset-networks.bicep,1,"There are no Diagnostic Settings or flow log configuration defined for any network resources, limiting the ability to monitor and investigate network activity for anomalies and threats.","Configure Network Watcher and enable NSG flow logs, and diagnostic logs for the NAT Gateway and Public IP to ensure network activity can be monitored and audited.",N/A,AI,Generic
LOW,DP-3,server-farms.bicep,87,"The 'CERTIFICATE_PASSWORD_GENEVACERT' setting is created with an empty value (''). If this property is used as a secret placeholder, it should reference a secure source such as Key Vault, and not be left unset or stored as a blank or inline secret.",Store all sensitive values such as secrets or passwords in Key Vault and reference them securely in the template. Remove inline or empty secret values as placeholders.,N/A,AI,Generic
LOW,DP-2,signalR.bicep,4,"The template does not explicitly enforce use of TLS 1.2+ for data in transit. While Azure SignalR supports TLS 1.2 by default, it is considered best practice to explicitly specify and enforce minimum TLS version where possible.","Set the 'tls' or equivalent property in the SignalR resource definition to enforce minimum TLS version 1.2 if available. If not available in this API version, regularly review for updated options to enforce stronger encryption-in-transit settings.",N/A,AI,Generic
LOW,DP-1,storage-accounts.bicep,20,"The definition does not specify customer-managed keys (CMK) or any explicit encryption settings for storage accounts. By default, Microsoft-managed keys will be used, which meets basic requirements but falls short of best practice for sensitive or regulated data.","Define 'encryption' properties for each storage account and, when appropriate, configure customer-managed keys (CMK) via Azure Key Vault for enhanced control and compliance.",N/A,AI,Generic
