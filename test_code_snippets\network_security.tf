# Network Security Configuration
# This file contains intentional security issues for testing code snippets

terraform {
  required_version = ">= 1.0"
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.0"
    }
  }
}

provider "azurerm" {
  features {}
}

# Resource Group
resource "azurerm_resource_group" "main" {
  name     = "rg-network-security-test"
  location = "East US"
  
  tags = {
    Environment = "Development"
    Purpose     = "Security Testing"
    Owner       = "DevSecOps Team"
  }
}

# Virtual Network
resource "azurerm_virtual_network" "main" {
  name                = "vnet-security-test"
  address_space       = ["10.0.0.0/16"]
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  
  tags = azurerm_resource_group.main.tags
}

# Subnet
resource "azurerm_subnet" "internal" {
  name                 = "internal"
  resource_group_name  = azurerm_resource_group.main.name
  virtual_network_name = azurerm_virtual_network.main.name
  address_prefixes     = ["********/24"]
}

# Network Security Group with security issues
resource "azurerm_network_security_group" "main" {
  name                = "nsg-security-test"
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name

  # SECURITY ISSUE #1: SSH from anywhere (Line 52)
  security_rule {
    name                       = "SSH"
    priority                   = 1001
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "22"
    source_address_prefix      = "0.0.0.0/0"
    destination_address_prefix = "*"
  }

  # SECURITY ISSUE #2: RDP from anywhere (Line 63)
  security_rule {
    name                       = "RDP"
    priority                   = 1002
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "3389"
    source_address_prefix      = "0.0.0.0/0"
    destination_address_prefix = "*"
  }

  # Good rule example
  security_rule {
    name                       = "HTTP"
    priority                   = 1003
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "80"
    source_address_prefix      = "10.0.0.0/16"
    destination_address_prefix = "*"
  }

  tags = azurerm_resource_group.main.tags
}

# Storage Account with issues
resource "azurerm_storage_account" "main" {
  name                     = "securityteststorage"
  resource_group_name      = azurerm_resource_group.main.name
  location                 = azurerm_resource_group.main.location
  account_tier             = "Standard"
  account_replication_type = "LRS"

  # SECURITY ISSUE #3: Public blob access (Line 95)
  allow_blob_public_access = true
  
  # SECURITY ISSUE #4: HTTP traffic allowed (Line 98)
  https_traffic_only = false
  
  # SECURITY ISSUE #5: Weak TLS version (Line 101)
  min_tls_version = "TLS1_0"

  tags = azurerm_resource_group.main.tags
}

# Outputs
output "resource_group_id" {
  description = "ID of the resource group"
  value       = azurerm_resource_group.main.id
}

output "storage_account_id" {
  description = "ID of the storage account"
  value       = azurerm_storage_account.main.id
}