{"properties": {"displayName": "Microsoft cloud security benchmark", "policyType": "BuiltIn", "description": "The Microsoft cloud security benchmark initiative represents the policies and controls implementing security recommendations defined in Microsoft cloud security benchmark, see https://aka.ms/azsecbm. This also serves as the Microsoft Defender for Cloud default policy initiative. You can directly assign this initiative, or manage its policies and compliance results within Microsoft Defender for Cloud.", "metadata": {"version": "57.51.0", "category": "Security Center"}, "version": "57.51.0", "policyDefinitionGroups": [{"name": "Azure_Security_Benchmark_v3.0_NS-1", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_NS-1"}, {"name": "Azure_Security_Benchmark_v3.0_NS-2", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_NS-2"}, {"name": "Azure_Security_Benchmark_v3.0_NS-3", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_NS-3"}, {"name": "Azure_Security_Benchmark_v3.0_NS-4", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_NS-4"}, {"name": "Azure_Security_Benchmark_v3.0_NS-5", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_NS-5"}, {"name": "Azure_Security_Benchmark_v3.0_NS-6", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_NS-6"}, {"name": "Azure_Security_Benchmark_v3.0_NS-7", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_NS-7"}, {"name": "Azure_Security_Benchmark_v3.0_NS-8", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_NS-8"}, {"name": "Azure_Security_Benchmark_v3.0_NS-9", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_NS-9"}, {"name": "Azure_Security_Benchmark_v3.0_NS-10", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_NS-10"}, {"name": "Azure_Security_Benchmark_v3.0_DP-1", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_DP-1"}, {"name": "Azure_Security_Benchmark_v3.0_DP-2", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_DP-2"}, {"name": "Azure_Security_Benchmark_v3.0_DP-3", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_DP-3"}, {"name": "Azure_Security_Benchmark_v3.0_DP-4", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_DP-4"}, {"name": "Azure_Security_Benchmark_v3.0_DP-5", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_DP-5"}, {"name": "Azure_Security_Benchmark_v3.0_DP-6", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_DP-6"}, {"name": "Azure_Security_Benchmark_v3.0_DP-7", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_DP-7"}, {"name": "Azure_Security_Benchmark_v3.0_DP-8", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_DP-8"}, {"name": "Azure_Security_Benchmark_v3.0_IM-1", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_IM-1"}, {"name": "Azure_Security_Benchmark_v3.0_IM-2", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_IM-2"}, {"name": "Azure_Security_Benchmark_v3.0_IM-3", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_IM-3"}, {"name": "Azure_Security_Benchmark_v3.0_IM-4", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_IM-4"}, {"name": "Azure_Security_Benchmark_v3.0_IM-5", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_IM-5"}, {"name": "Azure_Security_Benchmark_v3.0_IM-6", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_IM-6"}, {"name": "Azure_Security_Benchmark_v3.0_IM-7", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_IM-7"}, {"name": "Azure_Security_Benchmark_v3.0_IM-8", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_IM-8"}, {"name": "Azure_Security_Benchmark_v3.0_IM-9", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_IM-9"}, {"name": "Azure_Security_Benchmark_v3.0_PA-1", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_PA-1"}, {"name": "Azure_Security_Benchmark_v3.0_PA-2", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_PA-2"}, {"name": "Azure_Security_Benchmark_v3.0_PA-3", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_PA-3"}, {"name": "Azure_Security_Benchmark_v3.0_PA-4", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_PA-4"}, {"name": "Azure_Security_Benchmark_v3.0_PA-5", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_PA-5"}, {"name": "Azure_Security_Benchmark_v3.0_PA-6", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_PA-6"}, {"name": "Azure_Security_Benchmark_v3.0_PA-7", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_PA-7"}, {"name": "Azure_Security_Benchmark_v3.0_PA-8", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_PA-8"}, {"name": "Azure_Security_Benchmark_v3.0_PV-1", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_PV-1"}, {"name": "Azure_Security_Benchmark_v3.0_PV-2", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_PV-2"}, {"name": "Azure_Security_Benchmark_v3.0_PV-3", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_PV-3"}, {"name": "Azure_Security_Benchmark_v3.0_PV-4", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_PV-4"}, {"name": "Azure_Security_Benchmark_v3.0_PV-5", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_PV-5"}, {"name": "Azure_Security_Benchmark_v3.0_PV-6", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_PV-6"}, {"name": "Azure_Security_Benchmark_v3.0_PV-7", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_PV-7"}, {"name": "Azure_Security_Benchmark_v3.0_LT-1", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_LT-1"}, {"name": "Azure_Security_Benchmark_v3.0_LT-2", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_LT-2"}, {"name": "Azure_Security_Benchmark_v3.0_LT-3", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_LT-3"}, {"name": "Azure_Security_Benchmark_v3.0_LT-4", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_LT-4"}, {"name": "Azure_Security_Benchmark_v3.0_LT-5", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_LT-5"}, {"name": "Azure_Security_Benchmark_v3.0_LT-6", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_LT-6"}, {"name": "Azure_Security_Benchmark_v3.0_LT-7", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_LT-7"}, {"name": "Azure_Security_Benchmark_v3.0_AM-1", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_AM-1"}, {"name": "Azure_Security_Benchmark_v3.0_AM-2", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_AM-2"}, {"name": "Azure_Security_Benchmark_v3.0_AM-3", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_AM-3"}, {"name": "Azure_Security_Benchmark_v3.0_AM-4", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_AM-4"}, {"name": "Azure_Security_Benchmark_v3.0_AM-5", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_AM-5"}, {"name": "Azure_Security_Benchmark_v3.0_ES-1", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_ES-1"}, {"name": "Azure_Security_Benchmark_v3.0_ES-2", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_ES-2"}, {"name": "Azure_Security_Benchmark_v3.0_ES-3", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_ES-3"}, {"name": "Azure_Security_Benchmark_v3.0_BR-1", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_BR-1"}, {"name": "Azure_Security_Benchmark_v3.0_BR-2", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_BR-2"}, {"name": "Azure_Security_Benchmark_v3.0_BR-3", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_BR-3"}, {"name": "Azure_Security_Benchmark_v3.0_BR-4", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_BR-4"}, {"name": "Azure_Security_Benchmark_v3.0_IR-1", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_IR-1"}, {"name": "Azure_Security_Benchmark_v3.0_IR-2", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_IR-2"}, {"name": "Azure_Security_Benchmark_v3.0_IR-3", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_IR-3"}, {"name": "Azure_Security_Benchmark_v3.0_IR-4", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_IR-4"}, {"name": "Azure_Security_Benchmark_v3.0_IR-5", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_IR-5"}, {"name": "Azure_Security_Benchmark_v3.0_IR-6", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_IR-6"}, {"name": "Azure_Security_Benchmark_v3.0_IR-7", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_IR-7"}, {"name": "Azure_Security_Benchmark_v3.0_DS-1", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_DS-1"}, {"name": "Azure_Security_Benchmark_v3.0_DS-2", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_DS-2"}, {"name": "Azure_Security_Benchmark_v3.0_DS-3", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_DS-3"}, {"name": "Azure_Security_Benchmark_v3.0_DS-4", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_DS-4"}, {"name": "Azure_Security_Benchmark_v3.0_DS-5", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_DS-5"}, {"name": "Azure_Security_Benchmark_v3.0_DS-6", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_DS-6"}, {"name": "Azure_Security_Benchmark_v3.0_DS-7", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_DS-7"}, {"name": "Azure_Security_Benchmark_v3.0_GS-1", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_GS-1"}, {"name": "Azure_Security_Benchmark_v3.0_GS-2", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_GS-2"}, {"name": "Azure_Security_Benchmark_v3.0_GS-3", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_GS-3"}, {"name": "Azure_Security_Benchmark_v3.0_GS-4", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_GS-4"}, {"name": "Azure_Security_Benchmark_v3.0_GS-5", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_GS-5"}, {"name": "Azure_Security_Benchmark_v3.0_GS-6", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_GS-6"}, {"name": "Azure_Security_Benchmark_v3.0_GS-7", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_GS-7"}, {"name": "Azure_Security_Benchmark_v3.0_GS-8", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_GS-8"}, {"name": "Azure_Security_Benchmark_v3.0_GS-9", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_GS-9"}, {"name": "Azure_Security_Benchmark_v3.0_GS-10", "additionalMetadataId": "/providers/Microsoft.PolicyInsights/policyMetadata/Azure_Security_Benchmark_v3.0_GS-10"}], "parameters": {"useServicePrincipalToProtectSubscriptionsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Service principals should be used to protect your subscriptions instead of management certificates", "description": "[Deprecated: With Cloud Services (classic) retiring (see https://azure.microsoft.com/updates/cloud-services-retirement-announcement), there will no longer be a need for this assessment as management certificates will be obsolete.] Management certificates allow anyone who authenticates with them to manage the subscription(s) they are associated with. To manage subscriptions more securely, use of service principals with Resource Manager is recommended to limit the impact of a certificate compromise.", "deprecated": true}}, "updateOsVersionMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Operating system version should be the most current version for your cloud service roles", "description": "Keeping the operating system (OS) on the most recent supported version for your cloud service roles enhances the systems security posture.", "deprecated": true}}, "resolveLogAnalyticsHealthIssuesMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Log Analytics agent health issues should be resolved on your machines", "description": "Security Center uses the Log Analytics agent, formerly known as the Microsoft Monitoring Agent (MMA). To make sure your virtual machines are successfully monitored, you need to make sure the agent is installed on the virtual machines and properly collects security events to the configured workspace.", "deprecated": true}}, "installLogAnalyticsAgentOnVmMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Log Analytics agent should be installed on your virtual machine for Microsoft Defender for Cloud monitoring", "description": "This policy audits any Windows/Linux virtual machines (VMs) if the Log Analytics agent is not installed which Security Center uses to monitor for security vulnerabilities and threats", "deprecated": true}}, "installLogAnalyticsAgentOnVmssMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Log Analytics agent should be installed on your virtual machine scale sets for Microsoft Defender for Cloud monitoring", "description": "Security Center collects data from your Azure virtual machines (VMs) to monitor for security vulnerabilities and threats.", "deprecated": true}}, "certificatesValidityPeriodMonitoringEffect": {"type": "string", "defaultValue": "disabled", "allowedValues": ["audit", "deny", "disabled"], "metadata": {"displayName": "Manage certificate validity period", "description": "Enable or disable manage certificate validity period."}}, "certificatesValidityPeriodInMonths": {"type": "Integer", "defaultValue": 12, "metadata": {"displayName": "The maximum validity period in months of managed certificate", "description": "The limit to how long a certificate may be valid for. Certificates with lengthy validity periods aren't best practice."}}, "secretsExpirationSetEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Key Vault secrets should have expiration dates set", "description": "Enable or disable key vault secrets should have expiration dates set."}}, "keysExpirationSetEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Key Vault keys should have expiration dates set", "description": "Enable or disable key vault keys should have expiration dates set."}}, "azurePolicyforWindowsMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Guest Configuration extension should be installed on virtual machines", "description": "Enable or disable virtual machines reporting that the Guest Configuration extension should be installed"}}, "gcExtOnVMWithNoSAMIMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Virtual machines' Guest Configuration extension should be deployed with system-assigned managed identity", "description": "Enable or disable Virtual machines' Guest Configuration extension should be deployed with system-assigned managed identity"}}, "windowsDefenderExploitGuardMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Windows Defender Exploit Guard should be enabled on your Windows virtual machines", "description": "Enable or disable virtual machines reporting that Windows Defender Exploit Guard is enabled"}}, "windowsGuestConfigBaselinesMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Vulnerabilities in security configuration on your Windows machines should be remediated (powered by Guest Config)", "description": "Enable or disable virtual machines reporting Windows Baselines in Guest Config"}}, "linuxGuestConfigBaselinesMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Vulnerabilities in security configuration on your Linux machines should be remediated (powered by Guest Config)", "description": "Enable or disable virtual machines reporting Linux Baselines in Guest Config"}}, "vmssSystemUpdatesMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "System updates on virtual machine scale sets should be installed", "description": "Enable or disable virtual machine scale sets reporting of system updates", "deprecated": true}}, "vmssEndpointProtectionMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Endpoint protection solution should be installed on virtual machine scale sets", "description": "Enable or disable virtual machine scale sets endpoint protection monitoring", "deprecated": true}}, "vmssOsVulnerabilitiesMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Vulnerabilities in security configuration on your virtual machine scale sets should be remediated", "description": "Enable or disable virtual machine scale sets OS vulnerabilities monitoring", "deprecated": true}}, "systemUpdatesMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "System updates should be installed on your machines", "description": "Enable or disable reporting of system updates", "deprecated": true}}, "systemUpdatesV2MonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "System updates should be installed on your machines (powered by Update Center)", "description": "Enable or disable reporting of system updates (powered by Update Center)"}}, "systemUpdatesAutoAssessmentModeEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Machines should be configured to periodically check for missing system updates", "description": "Enable or disable monitoring of assessment mode"}}, "systemConfigurationsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Vulnerabilities in security configuration on your machines should be remediated", "description": "Enable or disable OS vulnerabilities monitoring (based on a configured baseline)", "deprecated": true}}, "endpointProtectionMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Monitor missing Endpoint Protection in Microsoft Defender for Cloud", "description": "Enable or disable endpoint protection monitoring", "deprecated": true}}, "diskEncryptionMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Virtual machines should encrypt temp disks, caches, and data flows between Compute and Storage resources", "description": "Enable or disable the monitoring for VM disk encryption", "deprecated": true}}, "gcLinuxDiskEncryptionMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Linux virtual machines should enable Azure Disk Encryption or EncryptionAtHost", "description": "Enable or disable the guest configuration monitoring for disk encryption on Linux machines"}}, "gcWindowsDiskEncryptionMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Windows virtual machines should enable Azure Disk Encryption or EncryptionAtHost", "description": "Enable or disable the guest configuration monitoring for disk encryption on Windows machines"}}, "networkSecurityGroupsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Monitor network security groups", "description": "Enable or disable monitoring of network security groups with permissive rules", "deprecated": true}}, "networkSecurityGroupsOnSubnetsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Network Security Groups on the subnet level should be enabled", "description": "Enable or disable monitoring of NSGs on subnets"}}, "networkSecurityGroupsOnVirtualMachinesMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Internet-facing virtual machines should be protected with network security groups", "description": "Enable or disable monitoring of NSGs on VMs"}}, "networkSecurityGroupsOnInternalVirtualMachinesMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Non-internet-facing virtual machines should be protected with network security groups", "description": "Enable or disable monitoring of NSGs on VMs"}}, "webApplicationFirewallMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Web ports should be restricted on Network Security Groups associated to your VM", "description": "Enable or disable the monitoring of unprotected web applications", "deprecated": true}}, "nextGenerationFirewallMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "All network ports should be restricted on network security groups associated to your virtual machine", "description": "Enable or disable overly permissive inbound NSG rules monitoring."}}, "vulnerabilityAssesmentMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Vulnerabilities should be remediated by a Vulnerability Assessment solution", "description": "Enable or disable the detection of VM vulnerabilities by a vulnerability assessment solution", "deprecated": true}}, "serverVulnerabilityAssessmentEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "A vulnerability assessment solution should be enabled on your virtual machines", "description": "Enable or disable the detection of virtual machine vulnerabilities by Microsoft Defender for Cloud vulnerability assessment"}}, "storageEncryptionMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Audit missing blob encryption for storage accounts", "description": "Enable or disable the monitoring of blob encryption for storage accounts", "deprecated": true}}, "jitNetworkAccessMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Management ports of virtual machines should be protected with just-in-time network access control", "description": "Enable or disable the monitoring of network just-in-time access"}}, "adaptiveApplicationControlsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Adaptive application controls for defining safe applications should be enabled on your machines", "description": "Enable or disable application controls to define the list of known-safe applications running on your machines, and alert you when other applications run", "deprecated": true}}, "adaptiveApplicationControlsUpdateMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Allowlist rules in your adaptive application control policy should be updated", "description": "Enable or disable the monitoring for changes in behavior on groups of machines configured for auditing by Microsoft Defender for Cloud's adaptive application controls", "deprecated": true}}, "sqlAuditingMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Monitor unaudited SQL servers in Microsoft Defender for Cloud", "description": "Enable or disable the monitoring of unaudited SQL databases", "deprecated": true}}, "sqlEncryptionMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Monitor unencrypted SQL databases in Microsoft Defender for Cloud", "description": "Enable or disable the monitoring of unencrypted SQL databases", "deprecated": true}}, "sqlDbEncryptionMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Transparent Data Encryption on SQL databases should be enabled", "description": "Enable or disable the monitoring of unencrypted SQL databases"}}, "sqlServerAuditingMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Auditing should be enabled on advanced data security settings on SQL Server", "description": "Enable or disable the monitoring of unaudited SQL Servers"}}, "sqlServerAuditingActionsAndGroupsMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "SQL Auditing settings should have Action-Groups configured to capture critical activities", "description": "Enable or disable the monitoring of auditing policy Action-Groups and Actions setting", "deprecated": true}}, "SqlServerAuditingRetentionDaysMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "SQL servers should be configured with auditing retention days greater than 90 days", "description": "Enable or disable the monitoring of SQL servers with auditing retention period less than 90", "deprecated": true}}, "diagnosticsLogsInAppServiceMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Monitor resource logs in Azure App Services", "description": "Enable or disable the monitoring of resource logs in Azure App Services", "deprecated": true}}, "diagnosticsLogsInSelectiveAppServicesMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Resource logs in App Services should be enabled", "description": "Enable or disable the monitoring of resource logs in Azure App Services", "deprecated": true}}, "encryptionOfAutomationAccountMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Automation account variables should be encrypted", "description": "Enable or disable the monitoring of automation account encryption"}}, "diagnosticsLogsInBatchAccountMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Resource logs in Batch accounts should be enabled", "description": "Enable or disable the monitoring of resource logs in Batch accounts"}}, "diagnosticsLogsInBatchAccountRetentionDays": {"type": "string", "defaultValue": "1", "metadata": {"displayName": "Required retention (in days) for logs in Batch accounts", "description": "The required resource logs retention period in days"}}, "metricAlertsInBatchAccountMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Metric alert rules should be configured on Batch accounts", "description": "Enable or disable the monitoring of metric alerts in Batch accounts", "deprecated": true}}, "classicComputeVMsMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Virtual machines should be migrated to new Azure Resource Manager resources", "description": "Enable or disable the monitoring of classic compute VMs"}}, "classicStorageAccountsMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Storage accounts should be migrated to new Azure Resource Manager resources", "description": "Enable or disable the monitoring of classic storage accounts"}}, "diagnosticsLogsInDataLakeAnalyticsMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Resource logs in Data Lake Analytics should be enabled", "description": "Enable or disable the monitoring of resource logs in Data Lake Analytics accounts"}}, "diagnosticsLogsInDataLakeAnalyticsRetentionDays": {"type": "string", "defaultValue": "1", "metadata": {"displayName": "Required retention (in days) of logs in Data Lake Analytics accounts", "description": "The required resource logs retention period in days"}}, "diagnosticsLogsInDataLakeStoreMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Resource logs in Azure Data Lake Store should be enabled", "description": "Enable or disable the monitoring of resource logs in Data Lake Store accounts"}}, "diagnosticsLogsInDataLakeStoreRetentionDays": {"type": "string", "defaultValue": "1", "metadata": {"displayName": "Required retention (in days) of logs in Data Lake Store accounts", "description": "The required resource logs retention period in days"}}, "diagnosticsLogsInEventHubMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Resource logs in Event Hub should be enabled", "description": "Enable or disable the monitoring of resource logs in Event Hub accounts"}}, "diagnosticsLogsInEventHubRetentionDays": {"type": "string", "defaultValue": "1", "metadata": {"displayName": "Required retention (in days) of logs in Event Hub accounts", "description": "The required resource logs retention period in days"}}, "diagnosticsLogsInKeyVaultMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Resource logs in Key Vault should be enabled", "description": "Enable or disable the monitoring of resource logs in Key Vault vaults"}}, "diagnosticsLogsInKeyVaultRetentionDays": {"type": "string", "defaultValue": "1", "metadata": {"displayName": "Required retention (in days) of logs in Key Vault vaults", "description": "The required resource logs retention period in days"}}, "diagnosticsLogsInKubernetesMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Resource logs in Azure Kubernetes Service should be enabled", "description": "Enable or disable the monitoring of resource logs in Kubernetes managed clusters. Enabling Azure Kubernetes Service's resource logs can help recreate activity trails when investigating future security incidents"}}, "diagnosticsLogsInKubernetesRetentionDays": {"type": "string", "defaultValue": "1", "metadata": {"displayName": "Required retention (in days) of logs in Kubernetes managed clusters", "description": "The required resource logs retention period in days"}}, "diagnosticsLogsInLogicAppsMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Resource logs in Logic Apps should be enabled", "description": "Enable or disable the monitoring of resource logs in Logic Apps workflows"}}, "diagnosticsLogsInLogicAppsRetentionDays": {"type": "string", "defaultValue": "1", "metadata": {"displayName": "Required retention (in days) of logs in Logic Apps workflows", "description": "The required resource logs retention period in days"}}, "diagnosticsLogsInRedisCacheMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Only secure connections to your Redis Cache should be enabled", "description": "Enable or disable the monitoring of resource logs in Azure Redis Cache"}}, "diagnosticsLogsInSearchServiceMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Resource logs in Search services should be enabled", "description": "Enable or disable the monitoring of resource logs in Azure Search service"}}, "diagnosticsLogsInSearchServiceRetentionDays": {"type": "string", "defaultValue": "1", "metadata": {"displayName": "Required retention (in days) of logs in Azure Search service", "description": "The required resource logs retention period in days"}}, "aadAuthenticationInServiceFabricMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Service Fabric clusters should only use Azure Active Directory for client authentication", "description": "Enable or disable the monitoring of Azure Active Directory for client authentication in Service Fabric"}}, "clusterProtectionLevelInServiceFabricMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Service Fabric clusters should have the ClusterProtectionLevel property set to EncryptAndSign", "description": "Enable or disable the monitoring of cluster protection level in Service Fabric"}}, "diagnosticsLogsInServiceBusMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Resource logs in Service Bus should be enabled", "description": "Enable or disable the monitoring of resource logs in Service Bus"}}, "diagnosticsLogsInServiceBusRetentionDays": {"type": "string", "defaultValue": "1", "metadata": {"displayName": "Required retention (in days) of logs in Service Bus", "description": "The required resource logs retention period in days"}}, "namespaceAuthorizationRulesInServiceBusMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "All authorization rules except RootManageSharedAccessKey should be removed from Service Bus namespace", "description": "Enable or disable the monitoring of Service Bus namespace authorization rules", "deprecated": true}}, "aadAuthenticationInSqlServerMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "An Azure Active Directory administrator should be provisioned for SQL servers", "description": "Enable or disable the monitoring of an Azure AD admininistrator for SQL server"}}, "secureTransferToStorageAccountMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Secure transfer to storage accounts should be enabled", "description": "Enable or disable the monitoring of secure transfer to storage account"}}, "diagnosticsLogsInStreamAnalyticsMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Resource logs in Azure Stream Analytics should be enabled", "description": "Enable or disable the monitoring of resource logs in Stream Analytics"}}, "diagnosticsLogsInStreamAnalyticsRetentionDays": {"type": "string", "defaultValue": "1", "metadata": {"displayName": "Required retention (in days) of logs in Stream Analytics", "description": "The required resource logs retention period in days"}}, "useRbacRulesMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Audit usage of custom RBAC rules", "description": "Enable or disable the monitoring of using built-in RBAC rules"}}, "disableUnrestrictedNetworkToStorageAccountMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Audit unrestricted network access to storage accounts", "description": "Enable or disable the monitoring of network access to storage account"}}, "diagnosticsLogsInServiceFabricMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Resource logs in Virtual Machine Scale Sets should be enabled", "description": "Enable or disable the monitoring of resource logs in Service Fabric", "deprecated": true}}, "accessRulesInEventHubNamespaceMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "All authorization rules except RootManageSharedAccessKey should be removed from Event Hub namespace", "description": "Enable or disable the monitoring of access rules in Event Hub namespaces", "deprecated": true}}, "accessRulesInEventHubMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Authorization rules on the Event Hub instance should be defined", "description": "Enable or disable the monitoring of access rules in Event Hubs", "deprecated": true}}, "sqlDbVulnerabilityAssesmentMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "SQL databases should have vulnerability findings resolved", "description": "Enable or disable the monitoring of vulnerability assessment scan results and recommendations for how to remediate database vulnerabilities."}}, "serverSqlDbVulnerabilityAssesmentMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "SQL servers on machines should have vulnerability findings resolved", "description": "SQL Vulnerability assessment scans your database for security vulnerabilities, and exposes any deviations from best practices such as misconfigurations, excessive permissions, and unprotected sensitive data. Resolving the vulnerabilities found can greatly improve your database security posture."}}, "sqlDbDataClassificationMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Sensitive data in your SQL databases should be classified", "description": "Enable or disable the monitoring of sensitive data classification in databases.", "deprecated": true}}, "identityDesignateLessThanOwnersMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "A maximum of 3 owners should be designated for your subscription", "description": "Enable or disable the monitoring of maximum owners in subscription"}}, "identityDesignateMoreThanOneOwnerMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "There should be more than one owner assigned to your subscription", "description": "Enable or disable the monitoring of minimum owners in subscription"}}, "identityEnableMFAForOwnerPermissionsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "MFA should be enabled on accounts with owner permissions on your subscription", "description": "Enable or disable the monitoring of MFA for accounts with owner permissions in subscription", "deprecated": true}}, "identityEnableMFAForAccountsWithOwnerPermissionsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Accounts with owner permissions on Azure resources should be MFA enabled", "description": "Enable or disable the monitoring of MFA for accounts with owner permissions in subscription", "deprecated": true}}, "identityEnableMFAForWritePermissionsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "MFA should be enabled accounts with write permissions on your subscription", "description": "Enable or disable the monitoring of MFA for accounts with write permissions in subscription", "deprecated": true}}, "identityEnableMFAForAccountsWithWritePermissionsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Accounts with write permissions on Azure resources should be MFA enabled", "description": "Enable or disable the monitoring of MFA for accounts with write permissions in subscription", "deprecated": true}}, "identityEnableMFAForReadPermissionsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "MFA should be enabled on accounts with read permissions on your subscription", "description": "Enable or disable the monitoring of MFA for accounts with read permissions in subscription", "deprecated": true}}, "identityEnableMFAForAccountsWithReadPermissionsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Accounts with read permissions on Azure resources should be MFA enabled", "description": "Enable or disable the monitoring of MFA for accounts with read permissions in subscription", "deprecated": true}}, "identityRemoveDeprecatedAccountWithOwnerPermissionsMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Deprecated accounts with owner permissions should be removed from your subscription", "description": "Enable or disable the monitoring of deprecated acounts with owner permissions in subscription"}}, "identityRemoveBlockedAccountsWithOwnerPermissionsMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Blocked accounts with owner permissions on Azure resources should be removed", "description": "Enable or disable the monitoring of deprecated acounts with owner permissions in subscription", "deprecated": true}}, "identityRemoveDeprecatedAccountMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Deprecated accounts should be removed from your subscription", "description": "Enable or disable the monitoring of deprecated acounts in subscription"}}, "identityRemoveBlockedAccountsWithReadWritePermissionsMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Blocked accounts with read and write permissions on Azure resources should be removed", "description": "Enable or disable the monitoring of deprecated acounts in subscription", "deprecated": true}}, "identityRemoveExternalAccountWithOwnerPermissionsMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "External accounts with owner permissions should be removed from your subscription", "description": "Enable or disable the monitoring of external acounts with owner permissions in subscription"}}, "identityRemoveGuestAccountsWithOwnerPermissionsMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Guest accounts with owner permissions on Azure resources should be removed", "description": "Enable or disable the monitoring of external acounts with owner permissions in subscription", "deprecated": true}}, "identityRemoveExternalAccountWithWritePermissionsMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "External accounts with write permissions should be removed from your subscription", "description": "Enable or disable the monitoring of external acounts with write permissions in subscription"}}, "identityRemoveGuestAccountsWithWritePermissionsMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Guest accounts with write permissions on Azure resources should be removed", "description": "Enable or disable the monitoring of external acounts with write permissions in subscription", "deprecated": true}}, "identityRemoveExternalAccountWithReadPermissionsMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "External accounts with read permissions should be removed from your subscription", "description": "Enable or disable the monitoring of external acounts with read permissions in subscription"}}, "identityRemoveGuestAccountsWithReadPermissionsMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Guest accounts with read permissions on Azure resources should be removed", "description": "Enable or disable the monitoring of external acounts with read permissions in subscription", "deprecated": true}}, "apiAppConfigureIPRestrictionsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Monitor Configure IP restrictions for API App", "description": "Enable or disable the monitoring of IP restrictions for API App", "deprecated": true}}, "functionAppConfigureIPRestrictionsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Monitor Configure IP restrictions for Function App", "description": "Enable or disable the monitoring of IP restrictions for Function App", "deprecated": true}}, "webAppConfigureIPRestrictionsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Monitor Configure IP restrictions for Web App", "description": "Enable or disable the monitoring of IP restrictions for Web App", "deprecated": true}}, "apiAppDisableRemoteDebuggingMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Remote debugging should be turned off for API App", "description": "Enable or disable the monitoring of remote debugging for API App", "deprecated": true}}, "functionAppDisableRemoteDebuggingMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Remote debugging should be turned off for Function App", "description": "Enable or disable the monitoring of remote debugging for Function App"}}, "webAppDisableRemoteDebuggingMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Remote debugging should be turned off for Web Application", "description": "Enable or disable the monitoring of remote debugging for Web App"}}, "apiAppAuditFtpsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "FTPS should be required in your API App", "description": "Enable FTPS enforcement for enhanced security", "deprecated": true}}, "functionAppAuditFtpsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "FTPS should be required in your Function App", "description": "Enable FTPS enforcement for enhanced security", "deprecated": true}}, "webAppAuditFtpsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "FTPS should be required in your Web App", "description": "Enable FTPS enforcement for enhanced security", "deprecated": true}}, "apiAppUseManagedIdentityMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "A managed identity should be used in your API App", "description": "Use a managed identity for enhanced authentication security", "deprecated": true}}, "functionAppUseManagedIdentityMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "A managed identity should be used in your Function App", "description": "Use a managed identity for enhanced authentication security", "deprecated": true}}, "webAppUseManagedIdentityMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "A managed identity should be used in your Web App", "description": "Use a managed identity for enhanced authentication security", "deprecated": true}}, "apiAppRequireLatestTlsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Latest TLS version should be used in your API App", "description": "Upgrade to the latest TLS version", "deprecated": true}}, "functionAppRequireLatestTlsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Latest TLS version should be used in your Function App", "description": "Upgrade to the latest TLS version", "deprecated": true}}, "webAppRequireLatestTlsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Latest TLS version should be used in your Web App", "description": "Upgrade to the latest TLS version", "deprecated": true}}, "apiAppDisableWebSocketsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Monitor disable web sockets for API App", "description": "Enable or disable the monitoring of web sockets for API App", "deprecated": true}}, "functionAppDisableWebSocketsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Monitor disable web sockets for Function App", "description": "Enable or disable the monitoring of web sockets for Function App", "deprecated": true}}, "webAppDisableWebSocketsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Monitor disable web sockets for Web App", "description": "Enable or disable the monitoring of web sockets for Web App", "deprecated": true}}, "apiAppEnforceHttpsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "API App should only be accessible over HTTPS", "description": "Enable or disable the monitoring of the use of HTTPS in API App", "deprecated": true}}, "functionAppEnforceHttpsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Function App should only be accessible over HTTPS", "description": "Enable or disable the monitoring of the use of HTTPS in function App", "deprecated": true}}, "webAppEnforceHttpsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Web Application should only be accessible over HTTPS", "description": "Enable or disable the monitoring of the use of HTTPS in Web App", "deprecated": true}}, "apiAppEnforceHttpsMonitoringEffectV2": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "API App should only be accessible over HTTPS V2", "description": "Enable or disable the monitoring of the use of HTTPS in API App V2", "deprecated": true}}, "functionAppEnforceHttpsMonitoringEffectV2": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Function App should only be accessible over HTTPS V2", "description": "Enable or disable the monitoring of the use of HTTPS in function App V2"}}, "webAppEnforceHttpsMonitoringEffectV2": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Web Application should only be accessible over HTTPS V2", "description": "Enable or disable the monitoring of the use of HTTPS in Web App V2"}}, "apiAppRestrictCORSAccessMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "CORS should not allow every resource to access your API App", "description": "Enable or disable the monitoring of CORS restrictions for API App", "deprecated": true}}, "functionAppRestrictCORSAccessMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "CORS should not allow every resource to access your Function App", "description": "Enable or disable the monitoring of CORS restrictions for API Function"}}, "webAppRestrictCORSAccessMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "CORS should not allow every resource to access your Web Application", "description": "Enable or disable the monitoring of CORS restrictions for API Web"}}, "apiAppUsedCustomDomainsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Monitor the custom domain use in API App", "description": "Enable or disable the monitoring of custom domain use in API App", "deprecated": true}}, "functionAppUsedCustomDomainsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Monitor the custom domain use in Function App", "description": "Enable or disable the monitoring of custom domain use in Function App", "deprecated": true}}, "webAppUsedCustomDomainsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Monitor the custom domain use in Web App", "description": "Enable or disable the monitoring of custom domain use in Web App", "deprecated": true}}, "apiAppUsedLatestDotNetMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Monitor use latest .NET in API App", "description": "Enable or disable the monitoring of .NET version in API App", "deprecated": true}}, "webAppUsedLatestDotNetMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Monitor use latest .NET in Web App", "description": "Enable or disable the monitoring of .NET version in Web App", "deprecated": true}}, "apiAppUsedLatestJavaMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Monitor use latest Java in API App", "description": "Enable or disable the monitoring of Java version in API App", "deprecated": true}}, "webAppUsedLatestJavaMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Monitor use latest Java in Web App", "description": "Enable or disable the monitoring of Java version in Web App", "deprecated": true}}, "webAppUsedLatestNodeJsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Monitor use latest Node.js in Web App", "description": "Enable or disable the monitoring of Node.js version in Web App", "deprecated": true}}, "apiAppUsedLatestPHPMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Monitor use latest PHP in API App", "description": "Enable or disable the monitoring of PHP version in API App", "deprecated": true}}, "webAppUsedLatestPHPMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Monitor use latest PHP in Web App", "description": "Enable or disable the monitoring of PHP version in Web App", "deprecated": true}}, "apiAppUsedLatestPythonMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Monitor use latest Python in API App", "description": "Enable or disable the monitoring of Python version in API App", "deprecated": true}}, "webAppUsedLatestPythonMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Monitor use latest Python in Web App", "description": "Enable or disable the monitoring of Python version in Web App", "deprecated": true}}, "vnetEnableDDoSProtectionMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Azure DDoS Protection should be enabled", "description": "Enable or disable the monitoring of DDoS protection for virtual network"}}, "diagnosticsLogsInIoTHubMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Resource logs in IoT Hub should be enabled", "description": "Enable or disable the monitoring of resource logs in IoT Hubs"}}, "diagnosticsLogsInIoTHubRetentionDays": {"type": "string", "defaultValue": "1", "metadata": {"displayName": "Required retention (in days) of logs in IoT Hub accounts", "description": "The required resource logs retention period in days"}}, "sqlServerAdvancedDataSecurityMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Azure Defender for SQL should be enabled for unprotected Azure SQL servers", "description": "Enable or disable the monitoring of SQL servers without Advanced Data Security"}}, "arcEnabledSqlServerDefenderStatusEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Microsoft Defender for SQL status should be protected for Arc-enabled SQL Servers", "description": "Enable or disable the monitoring of the protection status for Arc-enabled SQL Servers"}}, "sqlManagedInstanceAdvancedDataSecurityMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Azure Defender for SQL should be enabled for unprotected SQL Managed Instances", "description": "Enable or disable the monitoring of each SQL Managed Instance without advanced data security."}}, "sqlServerAdvancedDataSecurityEmailsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Advanced data security settings for SQL server should contain an email address to receive security alerts", "description": "Enable or disable the monitoring that advanced data security settings for SQL server contain at least one email address to receive security alerts", "deprecated": true}}, "sqlManagedInstanceAdvancedDataSecurityEmailsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Advanced data security settings for SQL Managed Instance should contain an email address to receive security alerts", "description": "Enable or disable the monitoring that advanced data security settings for SQL Managed Instance contain at least one email address to receive security alerts.", "deprecated": true}}, "sqlServerAdvancedDataSecurityEmailAdminsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Email notifications to admins and subscription owners should be enabled in SQL server advanced data security settings", "description": "Enable or disable auditing that 'email notification to admins and subscription owners' is enabled in the SQL Server advanced threat protection settings. This ensures that any detections of anomalous activities on SQL server are reported as soon as possible to the admins.", "deprecated": true}}, "sqlManagedInstanceAdvancedDataSecurityEmailAdminsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Email notifications to admins and subscription owners should be enabled in SQL Managed Instance advanced data security settings", "description": "Enable or disable auditing that 'email notification to admins and subscription owners' is enabled in SQL Managed Instance advanced threat protection settings. This setting ensures that any detections of anomalous activities on SQL Managed Instance are reported as soon as possible to the admins.", "deprecated": true}}, "kubernetesServiceRbacEnabledMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Role-Based Access Control (RBAC) should be used on Kubernetes Services", "description": "Enable or disable the monitoring of Kubernetes Services without RBAC enabled"}}, "kubernetesServicePspEnabledMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Pod Security Policies should be defined on Kubernetes Services", "description": "Enable or disable the monitoring of Kubernetes Services without Pod Security Policy enabled", "deprecated": true}}, "kubernetesServiceAuthorizedIPRangesEnabledMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Authorized IP ranges should be defined on Kubernetes Services", "description": "Enable or disable the monitoring of Kubernetes Services without Authorized IP Ranges enabled"}}, "kubernetesServiceVersionUpToDateMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Kubernetes Services should be upgraded to a non vulnerable Kubernetes version", "description": "Enable or disable the monitoring of the Kubernetes Services with versions that contain known vulnerabilities", "deprecated": true}}, "vulnerabilityAssessmentOnManagedInstanceMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Vulnerability assessment should be enabled on SQL Managed Instance", "description": "Audit each SQL Managed Instance which doesn't have recurring vulnerability assessment scans enabled. Vulnerability assessment can discover, track, and help you remediate potential database vulnerabilities."}}, "vulnerabilityAssessmentOnServerMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Vulnerability assessment should be enabled on your SQL servers", "description": "Audit Azure SQL servers which do not have recurring vulnerability assessment scans enabled. Vulnerability assessment can discover, track, and help you remediate potential database vulnerabilities."}}, "threatDetectionTypesOnManagedInstanceMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Advanced Threat Protection types should be set to 'All' in SQL Managed Instance advanced data security settings", "description": "It's recommended to enable all Advanced Threat Protection types on your SQL Managed Instance. Enabling all types protects against SQL injection, database vulnerabilities, and any other anomalous activities.", "deprecated": true}}, "threatDetectionTypesOnServerMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Advanced Threat Protection types should be set to 'All' in SQL server Advanced Data Security settings", "description": "It is recommended to enable all Advanced Threat Protection types on your SQL servers. Enabling all types protects against SQL injection, database vulnerabilities, and any other anomalous activities.", "deprecated": true}}, "adaptiveNetworkHardeningsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Adaptive network hardening recommendations should be applied on internet facing virtual machines", "description": "Enable or disable the monitoring of Internet-facing virtual machines for Network Security Group traffic hardening recommendations", "deprecated": true}}, "restrictAccessToManagementPortsMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Management ports should be closed on your virtual machines", "description": "Enable or disable the monitoring of open management ports on Virtual Machines"}}, "restrictAccessToAppServicesMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Access to App Services should be restricted", "description": "Enable or disable the monitoring of permissive network access to app-services", "deprecated": true}}, "disableIPForwardingMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "IP Forwarding on your virtual machine should be disabled", "description": "Enable or disable the monitoring of IP forwarding on virtual machines"}}, "ensureServerTDEIsEncryptedWithYourOwnKeyMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "SQL server TDE protector should be encrypted with your own key", "description": "Enable or disable the monitoring of Transparent Data Encryption (TDE) with your own key support. TDE with your own key support provides increased transparency and control over the TDE Protector, increased security with an HSM-backed external service, and promotion of separation of duties.", "deprecated": true}}, "ensureManagedInstanceTDEIsEncryptedWithYourOwnKeyMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "SQL Managed Instance TDE protector should be encrypted with your own key", "description": "Enable or disable the monitoring of Transparent Data Encryption (TDE) with your own key support. TDE with your own key support provides increased transparency and control over the TDE Protector, increased security with an HSM-backed external service, and promotion of separation of duties.", "deprecated": true}}, "ensureServerTDEIsEncryptedWithYourOwnKeyWithDenyMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "SQL server TDE protector should be encrypted with your own key", "description": "Enable or disable the monitoring of Transparent Data Encryption (TDE) with your own key support. TDE with your own key support provides increased transparency and control over the TDE Protector, increased security with an HSM-backed external service, and promotion of separation of duties."}}, "ensureManagedInstanceTDEIsEncryptedWithYourOwnKeyWithDenyMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "SQL Managed Instance TDE protector should be encrypted with your own key", "description": "Enable or disable the monitoring of Transparent Data Encryption (TDE) with your own key support. TDE with your own key support provides increased transparency and control over the TDE Protector, increased security with an HSM-backed external service, and promotion of separation of duties."}}, "containerBenchmarkMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Vulnerabilities in container security configurations should be remediated", "description": "Enable or disable container benchmark monitoring", "deprecated": true}}, "ASCDependencyAgentAuditWindowsEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Audit Dependency Agent for Windows VMs monitoring", "description": "Enable or disable Dependency Agent for Windows VMs"}}, "ASCDependencyAgentAuditLinuxEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Audit Dependency Agent for Linux VMs monitoring", "description": "Enable or disable Dependency Agent for Linux VMs"}}, "AzureFirewallEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "All Internet traffic should be routed via your deployed Azure Firewall", "description": "Enable or disable All Internet traffic should be routed via your deployed Azure Firewall"}}, "ArcWindowsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Log Analytics agent should be installed on your  Windows Azure Arc machines", "description": "Enable or disable Log Analytics agent should be installed on your  Windows Azure Arc machines", "deprecated": true}}, "ArcLinuxMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Log Analytics agent should be installed on your Linux Azure Arc machines", "description": "Enable or disable Log Analytics agent should be installed on your Linux Azure Arc machines"}}, "keyVaultsAdvancedDataSecurityMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Azure Defender for Key Vault should be enabled", "description": "Enable or disable Azure Defender for Key Vault"}}, "sqlServersAdvancedDataSecurityMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Azure Defender for Azure SQL Database servers should be enabled", "description": "Enable or disable Azure Defender for Azure SQL Database servers"}}, "sqlServersVirtualMachinesAdvancedDataSecurityMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Azure Defender for SQL servers on machines should be enabled", "description": "Enable or disable Azure Defender for SQL servers on Machines"}}, "storageAccountsAdvancedDataSecurityMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Azure Defender for Storage should be enabled", "description": "Enable or disable Azure Defender for storage", "deprecated": true}}, "appServicesAdvancedThreatProtectionMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Azure Defender for App Services should be enabled", "description": "Enable or disable Azure Defender for App Service"}}, "containerRegistryAdvancedThreatProtectionMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Azure Defender for container registries should be enabled", "description": "Enable or disable Azure Defender for container registries", "deprecated": true}}, "kubernetesServiceAdvancedThreatProtectionMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Azure Defender for Kubernetes should be enabled", "description": "Enable or disable Azure Defender for Kubernetes", "deprecated": true}}, "containersAdvancedThreatProtectionMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Microsoft Defender for Containers should be enabled", "description": "Enable or disable Microsoft Defender for Containers"}}, "virtualMachinesAdvancedThreatProtectionMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Azure Defender for servers should be enabled", "description": "Enable or disable Azure Defender for servers"}}, "azurePolicyAddonStatusEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Azure Policy Add-on for Kubernetes should be installed and enabled on Azure Kubernetes Service (AKS) clusters", "description": "Enable or disable reporting of the Azure Policy Add-on is enabled on Azure Kubernetes managed cluster"}}, "arcEnabledKubernetesClustersShouldHaveAzurePolicyExtensionInstalledEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Azure Arc enabled Kubernetes clusters should have Azure Policy's extension installed", "description": "Enable or disable monitoring of Arc enabled Kubernetes clusters without Azure Policy's extension installed"}}, "excludedImagesInKubernetesCluster": {"type": "Array", "metadata": {"displayName": "Kubernetes image to exclude from monitoring of all container related polices", "description": "The list of InitContainers and Containers to exclude from Kubernetes container related policy evaluation. It will apply to all container related policies except 'Container images should be deployed from trusted registries only' and 'Kubernetes clusters should gate deployment of vulnerable images'. The identifier is the image of container. Prefix-matching can be signified with `*`. For example: `myregistry.azurecr.io/istio:*`. It is recommended that users use the fully-qualified Docker image name (e.g. start with a domain name) in order to avoid unexpectedly exempting images from an untrusted repository."}, "defaultValue": []}, "allowedContainerImagesInKubernetesClusterEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"], "metadata": {"displayName": "Container images should be deployed from trusted registries only", "description": "Enable or disable monitoring of allowed container images in Kubernetes clusters"}}, "allowedContainerImagesInKubernetesClusterRegex": {"type": "string", "defaultValue": "^(.+){0}$", "metadata": {"displayName": "Allowed registry or registries regex", "description": "The RegEx rule used to match allowed container image field in a Kubernetes cluster. For example, to allow any Azure Container Registry image by matching partial path: ^[^\\/]+\\.azurecr\\.io\\/.+$ and for multiple registries: ^([^\\/]+\\.azurecr\\.io|registry\\.io)\\/.+$"}}, "allowedContainerImagesNamespaceExclusion": {"type": "Array", "defaultValue": ["kube-system", "gatekeeper-system", "azure-arc", "azuredefender", "mdc", "azure-extensions-usage-system"], "metadata": {"displayName": "Kubernetes namespaces to exclude from monitoring of allowed container images", "description": "List of Kubernetes namespaces to exclude from evaluation of allowed container images in Kubernetes clusters. To list multiple namespaces, use semicolons (;) to separate them. System namespaces \"kube-system\", \"gatekeeper-system\" and \"azure-arc\" are always excluded by design."}}, "allowedContainerImagesLabelSelector": {"type": "object", "metadata": {"displayName": "Kubernetes label selector to select monitoring of allowed container images", "description": "Label query to select Kubernetes resources for policy evaluation. An empty label selector matches all Kubernetes resources."}, "defaultValue": {}, "schema": {"description": "A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all resources.", "type": "object", "properties": {"matchLabels": {"description": "matchLabels is a map of {key,value} pairs.", "type": "object", "additionalProperties": {"type": "string"}, "minProperties": 1}, "matchExpressions": {"description": "matchExpressions is a list of values, a key, and an operator.", "type": "array", "items": {"type": "object", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values.", "type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty.", "type": "array", "items": {"type": "string"}}}, "required": ["key", "operator"], "additionalProperties": false}, "minItems": 1}}, "additionalProperties": false}}, "privilegedContainersShouldBeAvoidedEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"], "metadata": {"displayName": "Privileged containers should be avoided", "description": "Enable or disable monitoring of privileged containers in Kubernetes clusters"}}, "privilegedContainerNamespaceExclusion": {"type": "Array", "defaultValue": ["kube-system", "gatekeeper-system", "azure-arc", "azuredefender", "mdc", "azure-extensions-usage-system"], "metadata": {"displayName": "Kubernetes namespaces to exclude from monitoring of privileged containers", "description": "List of Kubernetes namespaces to exclude from evaluation of privileged containers in Kubernetes clusters. To list multiple namespaces, use semicolons (;) to separate them. System namespaces \"kube-system\", \"gatekeeper-system\" and \"azure-arc\" are always excluded by design."}}, "privilegedContainerLabelSelector": {"type": "object", "metadata": {"displayName": "Kubernetes label selector to select monitoring of privileged containers", "description": "Label query to select Kubernetes resources for policy evaluation. An empty label selector matches all Kubernetes resources."}, "defaultValue": {}, "schema": {"description": "A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all resources.", "type": "object", "properties": {"matchLabels": {"description": "matchLabels is a map of {key,value} pairs.", "type": "object", "additionalProperties": {"type": "string"}, "minProperties": 1}, "matchExpressions": {"description": "matchExpressions is a list of values, a key, and an operator.", "type": "array", "items": {"type": "object", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values.", "type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty.", "type": "array", "items": {"type": "string"}}}, "required": ["key", "operator"], "additionalProperties": false}, "minItems": 1}}, "additionalProperties": false}}, "allowedContainerPortsInKubernetesClusterEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"], "metadata": {"displayName": "Containers should listen on allowed ports only", "description": "Enable or disable monitoring of allowed container ports in Kubernetes clusters", "deprecated": true}}, "allowedContainerPortsInKubernetesClusterPorts": {"type": "Array", "defaultValue": ["-1"], "metadata": {"displayName": "Allowed container ports list in Kubernetes cluster", "description": "List of container ports allowed in Kubernetes cluster. Use ; to separate values", "deprecated": true}}, "allowedContainerPortsInKubernetesClusterNamespaceExclusion": {"type": "Array", "defaultValue": ["kube-system", "gatekeeper-system", "azure-arc", "azure-extensions-usage-system"], "metadata": {"displayName": "Kubernetes namespaces to exclude from monitoring of allowed container port", "description": "List of Kubernetes namespaces to exclude from evaluation of allowed container ports in Kubernetes clusters. To list multiple namespaces, use semicolons (;) to separate them. System namespaces \"kube-system\", \"gatekeeper-system\" and \"azure-arc\" are always excluded by design.", "deprecated": true}}, "allowedServicePortsInKubernetesClusterEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"], "metadata": {"displayName": "Services should listen on allowed ports only", "description": "Enable or disable monitoring of allowed service ports in Kubernetes clusters"}}, "allowedservicePortsInKubernetesClusterPorts": {"type": "Array", "defaultValue": ["-1"], "metadata": {"displayName": "Allowed service ports list in Kubernetes cluster", "description": "The list of service ports allowed in a Kubernetes cluster. Array only accepts strings. Example: [\"443\", \"80\"]"}}, "allowedServicePortsInKubernetesClusterNamespaceExclusion": {"type": "Array", "defaultValue": ["kube-system", "gatekeeper-system", "azure-arc", "azure-extensions-usage-system"], "metadata": {"displayName": "Kubernetes namespaces to exclude from monitoring of allowed service ports", "description": "List of Kubernetes namespaces to exclude from evaluation of allowed service ports in Kubernetes clusters. To list multiple namespaces, use semicolons (;) to separate them. System namespaces \"kube-system\", \"gatekeeper-system\" and \"azure-arc\" are always excluded by design."}}, "allowedServicePortsInKubernetesClusterLabelSelector": {"type": "object", "metadata": {"displayName": "Kubernetes label selector to select monitoring of allowed service ports", "description": "Label query to select Kubernetes resources for policy evaluation. An empty label selector matches all Kubernetes resources."}, "defaultValue": {}, "schema": {"description": "A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all resources.", "type": "object", "properties": {"matchLabels": {"description": "matchLabels is a map of {key,value} pairs.", "type": "object", "additionalProperties": {"type": "string"}, "minProperties": 1}, "matchExpressions": {"description": "matchExpressions is a list of values, a key, and an operator.", "type": "array", "items": {"type": "object", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values.", "type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty.", "type": "array", "items": {"type": "string"}}}, "required": ["key", "operator"], "additionalProperties": false}, "minItems": 1}}, "additionalProperties": false}}, "NoPrivilegeEscalationInKubernetesClusterEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"], "metadata": {"displayName": "Container with privileged escalation should be avoided", "description": "Enable or disable monitoring of privileged escalation containers in Kubernetes clusters"}}, "NoPrivilegeEscalationInKubernetesClusterNamespaceExclusion": {"type": "Array", "defaultValue": ["kube-system", "gatekeeper-system", "azure-arc", "azure-extensions-usage-system"], "metadata": {"displayName": "Kubernetes namespaces to exclude from monitoring of privileged escalation containers", "description": "List of Kubernetes namespaces to exclude from evaluation of privileged escalation containers in Kubernetes clusters. To list multiple namespaces, use semicolons (;) to separate them. System namespaces \"kube-system\", \"gatekeeper-system\" and \"azure-arc\" are always excluded by design."}}, "NoPrivilegeEscalationInKubernetesClusterLabelSelector": {"type": "object", "metadata": {"displayName": "Kubernetes label selector to select monitoring of privileged escalation containers", "description": "Label query to select Kubernetes resources for policy evaluation. An empty label selector matches all Kubernetes resources."}, "defaultValue": {}, "schema": {"description": "A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all resources.", "type": "object", "properties": {"matchLabels": {"description": "matchLabels is a map of {key,value} pairs.", "type": "object", "additionalProperties": {"type": "string"}, "minProperties": 1}, "matchExpressions": {"description": "matchExpressions is a list of values, a key, and an operator.", "type": "array", "items": {"type": "object", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values.", "type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty.", "type": "array", "items": {"type": "string"}}}, "required": ["key", "operator"], "additionalProperties": false}, "minItems": 1}}, "additionalProperties": false}}, "NoSharingSensitiveHostNamespacesInKubernetesEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"], "metadata": {"displayName": "Containers sharing sensitive host namespaces should be avoided", "description": "Enable or disable monitoring of shared sensitive host namespaces in Kubernetes clusters"}}, "NoSharingSensitiveHostNamespacesInKubernetesNamespaceExclusion": {"type": "Array", "defaultValue": ["kube-system", "gatekeeper-system", "azure-arc", "azure-extensions-usage-system"], "metadata": {"displayName": "Kubernetes namespaces to exclude from monitoring of sharing sensitive host namespaces in Kubernetes clusters", "description": "List of Kubernetes namespaces to exclude from evaluation of  sharing sensitive host namespaces in Kubernetes clusters. To list multiple namespaces, use semicolons (;) to separate them. System namespaces \"kube-system\", \"gatekeeper-system\" and \"azure-arc\" are always excluded by design."}}, "NoSharingSensitiveHostNamespacesInKubernetesLabelSelector": {"type": "object", "metadata": {"displayName": "Kubernetes label selector to select monitoring of sharing sensitive host namespaces in Kubernetes clusters", "description": "Label query to select Kubernetes resources for policy evaluation. An empty label selector matches all Kubernetes resources."}, "defaultValue": {}, "schema": {"description": "A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all resources.", "type": "object", "properties": {"matchLabels": {"description": "matchLabels is a map of {key,value} pairs.", "type": "object", "additionalProperties": {"type": "string"}, "minProperties": 1}, "matchExpressions": {"description": "matchExpressions is a list of values, a key, and an operator.", "type": "array", "items": {"type": "object", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values.", "type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty.", "type": "array", "items": {"type": "string"}}}, "required": ["key", "operator"], "additionalProperties": false}, "minItems": 1}}, "additionalProperties": false}}, "ReadOnlyRootFileSystemInKubernetesClusterEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"], "metadata": {"displayName": "Immutable (read-only) root filesystem should be enforced for containers", "description": "Enable or disable monitoring of containers running with a read only root file system in Kubernetes clusters"}}, "ReadOnlyRootFileSystemInKubernetesClusterNamespaceExclusion": {"type": "Array", "defaultValue": ["kube-system", "gatekeeper-system", "azure-arc", "azuredefender", "mdc", "azure-extensions-usage-system"], "metadata": {"displayName": "Kubernetes namespaces to exclude from monitoring of containers running with a read only root file system", "description": "List of Kubernetes namespaces to exclude from evaluation to monitoring of containers running with a read only root file system in Kubernetes clusters. To list multiple namespaces, use semicolons (;) to separate them. System namespaces \"kube-system\", \"gatekeeper-system\" and \"azure-arc\" are always excluded by design."}}, "ReadOnlyRootFileSystemInKubernetesClusterLabelSelector": {"type": "object", "metadata": {"displayName": "Kubernetes label selector to select monitoring of containers running with a read only root file system", "description": "Label query to select Kubernetes resources for policy evaluation. An empty label selector matches all Kubernetes resources."}, "defaultValue": {}, "schema": {"description": "A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all resources.", "type": "object", "properties": {"matchLabels": {"description": "matchLabels is a map of {key,value} pairs.", "type": "object", "additionalProperties": {"type": "string"}, "minProperties": 1}, "matchExpressions": {"description": "matchExpressions is a list of values, a key, and an operator.", "type": "array", "items": {"type": "object", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values.", "type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty.", "type": "array", "items": {"type": "string"}}}, "required": ["key", "operator"], "additionalProperties": false}, "minItems": 1}}, "additionalProperties": false}}, "AllowedCapabilitiesInKubernetesClusterEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"], "metadata": {"displayName": "Least privileged Linux capabilities should be enforced for containers", "description": "Enable or disable monitoring of Kubernetes containers using allowed capabilities only"}}, "AllowedCapabilitiesInKubernetesClusterNamespaceExclusion": {"type": "Array", "defaultValue": ["kube-system", "gatekeeper-system", "azure-arc", "azuredefender", "mdc", "azure-extensions-usage-system"], "metadata": {"displayName": "Kubernetes namespaces to exclude from monitoring of containers use only allowed capabilities", "description": "List of Kubernetes namespaces to exclude from evaluation to monitoring of containers using only allowed capabilities in Kubernetes clusters. To list multiple namespaces, use semicolons (;) to separate them. System namespaces \"kube-system\", \"gatekeeper-system\" and \"azure-arc\" are always excluded by design."}}, "AllowedCapabilitiesInKubernetesClusterLabelSelector": {"type": "object", "metadata": {"displayName": "Kubernetes label selector to select monitoring of containers use only allowed capabilities", "description": "Label query to select Kubernetes resources for policy evaluation. An empty label selector matches all Kubernetes resources."}, "defaultValue": {}, "schema": {"description": "A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all resources.", "type": "object", "properties": {"matchLabels": {"description": "matchLabels is a map of {key,value} pairs.", "type": "object", "additionalProperties": {"type": "string"}, "minProperties": 1}, "matchExpressions": {"description": "matchExpressions is a list of values, a key, and an operator.", "type": "array", "items": {"type": "object", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values.", "type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty.", "type": "array", "items": {"type": "string"}}}, "required": ["key", "operator"], "additionalProperties": false}, "minItems": 1}}, "additionalProperties": false}}, "AllowedCapabilitiesInKubernetesClusterList": {"type": "Array", "defaultValue": [], "metadata": {"displayName": "Allowed capabilities", "description": "The list of capabilities that are allowed to be added to a container. Provide empty list as input to block everything."}}, "DropCapabilitiesInKubernetesClusterList": {"type": "Array", "defaultValue": [], "metadata": {"displayName": "Required drop capabilities", "description": "The list of capabilities that must be dropped by a container."}}, "AllowedAppArmorProfilesInKubernetesClusterEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"], "metadata": {"displayName": "Containers should only use allowed AppArmor profiles", "description": "Enable or disable monitoring of modification of Kubernetes containers' AppArmor profile"}}, "AllowedAppArmorProfilesInKubernetesClusterNamespaceExclusion": {"type": "Array", "defaultValue": ["kube-system", "gatekeeper-system", "azure-arc", "azuredefender", "mdc", "azure-extensions-usage-system"], "metadata": {"displayName": "Kubernetes namespaces to exclude from monitoring of containers modification of AppArmor profile", "description": "List of Kubernetes namespaces to exclude from evaluation to monitoring of containers modifying of AppArmor profile in Kubernetes clusters. To list multiple namespaces, use semicolons (;) to separate them. System namespaces \"kube-system\", \"gatekeeper-system\" and \"azure-arc\" are always excluded by design."}}, "AllowedAppArmorProfilesInKubernetesClusterLabelSelector": {"type": "object", "metadata": {"displayName": "Kubernetes label selector to select monitoring of containers modification of AppArmor profile", "description": "Label query to select Kubernetes resources for policy evaluation. An empty label selector matches all Kubernetes resources."}, "defaultValue": {}, "schema": {"description": "A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all resources.", "type": "object", "properties": {"matchLabels": {"description": "matchLabels is a map of {key,value} pairs.", "type": "object", "additionalProperties": {"type": "string"}, "minProperties": 1}, "matchExpressions": {"description": "matchExpressions is a list of values, a key, and an operator.", "type": "array", "items": {"type": "object", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values.", "type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty.", "type": "array", "items": {"type": "string"}}}, "required": ["key", "operator"], "additionalProperties": false}, "minItems": 1}}, "additionalProperties": false}}, "AllowedAppArmorProfilesInKubernetesClusterList": {"type": "Array", "defaultValue": ["runtime/default"], "metadata": {"displayName": "Allowed AppArmor profiles", "description": "The list of AppArmor profiles that containers are allowed to use. E.g. [ \"runtime/default\", \"docker/default\" ]. Provide empty list as input to block everything."}}, "AllowedHostNetworkingAndPortsInKubernetesClusterEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"], "metadata": {"displayName": "Usage of host networking and ports should be restricted", "description": "Enable or disable monitoring of Kubernetes containers' host networking and port ranges"}}, "AllowedHostNetworkingAndPortsInKubernetesClusterNamespaceExclusion": {"type": "Array", "defaultValue": ["kube-system", "gatekeeper-system", "azure-arc", "azure-extensions-usage-system"], "metadata": {"displayName": "Kubernetes namespaces to exclude from monitoring of containers host networking and ports", "description": "List of Kubernetes namespaces to exclude from evaluation to monitoring of containers host networking and ports in Kubernetes clusters. To list multiple namespaces, use semicolons (;) to separate them. System namespaces \"kube-system\", \"gatekeeper-system\" and \"azure-arc\" are always excluded by design."}}, "AllowedHostNetworkingAndPortsInKubernetesClusterLabelSelector": {"type": "object", "metadata": {"displayName": "Kubernetes label selector to select monitoring of containers host networking and ports", "description": "Label query to select Kubernetes resources for policy evaluation. An empty label selector matches all Kubernetes resources."}, "defaultValue": {}, "schema": {"description": "A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all resources.", "type": "object", "properties": {"matchLabels": {"description": "matchLabels is a map of {key,value} pairs.", "type": "object", "additionalProperties": {"type": "string"}, "minProperties": 1}, "matchExpressions": {"description": "matchExpressions is a list of values, a key, and an operator.", "type": "array", "items": {"type": "object", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values.", "type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty.", "type": "array", "items": {"type": "string"}}}, "required": ["key", "operator"], "additionalProperties": false}, "minItems": 1}}, "additionalProperties": false}}, "AllowHostNetworkingInKubernetesCluster": {"type": "Boolean", "defaultValue": false, "metadata": {"displayName": "Allow host network usage", "description": "Set this value to true if pod is allowed to use host network otherwise false."}}, "AllowedHostMinPortInKubernetesCluster": {"type": "Integer", "defaultValue": 0, "metadata": {"displayName": "Min host port for pod in Kubernetes cluster", "description": "The minimum value in the allowable host port range that pods can use in the host network namespace."}}, "AllowedHostMaxPortInKubernetesCluster": {"type": "Integer", "defaultValue": 0, "metadata": {"displayName": "Max host port for pod in Kubernetes cluster", "description": "The maximum value in the allowable host port range that pods can use in the host network namespace."}}, "AllowedHostPathVolumesInKubernetesClusterEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"], "metadata": {"displayName": "Usage of pod HostPath volume mounts should be restricted to a known list to restrict node access from compromised containers", "description": "Enable or disable monitoring of pod HostPath volume mounts in Kubernetes clusters"}}, "AllowedHostPathVolumesInKubernetesClusterNamespaceExclusion": {"type": "Array", "defaultValue": ["kube-system", "gatekeeper-system", "azure-arc", "azuredefender", "mdc", "azure-extensions-usage-system"], "metadata": {"displayName": "Kubernetes namespaces to exclude from monitoring of pod HostPath volume mounts", "description": "List of Kubernetes namespaces to exclude from evaluation to monitoring of pod HostPath volume mounts in Kubernetes clusters. To list multiple namespaces, use semicolons (;) to separate them. System namespaces \"kube-system\", \"gatekeeper-system\" and \"azure-arc\" are always excluded by design."}}, "AllowedHostPathVolumesInKubernetesClusterLabelSelector": {"type": "object", "metadata": {"displayName": "Kubernetes label selector to select monitoring of pod HostPath volume mounts", "description": "Label query to select Kubernetes resources for policy evaluation. An empty label selector matches all Kubernetes resources."}, "defaultValue": {}, "schema": {"description": "A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all resources.", "type": "object", "properties": {"matchLabels": {"description": "matchLabels is a map of {key,value} pairs.", "type": "object", "additionalProperties": {"type": "string"}, "minProperties": 1}, "matchExpressions": {"description": "matchExpressions is a list of values, a key, and an operator.", "type": "array", "items": {"type": "object", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values.", "type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty.", "type": "array", "items": {"type": "string"}}}, "required": ["key", "operator"], "additionalProperties": false}, "minItems": 1}}, "additionalProperties": false}}, "AllowedHostPathVolumesInKubernetesClusterList": {"type": "Object", "metadata": {"displayName": "Allowed host paths for pod in Kubernetes cluster", "description": "The host paths allowed for pod hostPath volumes to use. Provide an empty paths list to block all host paths.", "schema": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "object", "properties": {"pathPrefix": {"type": "string"}, "readOnly": {"type": "boolean"}}, "required": ["pathPrefix", "readOnly"], "additionalProperties": false}}}, "required": ["paths"], "additionalProperties": false}}, "defaultValue": {"paths": []}}, "memoryAndCPULimitsInKubernetesClusterEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"], "metadata": {"displayName": "Containers' CPU and memory limits should be enforced", "description": "Enable or disable monitoring of containers' CPU and memory limits in Kubernetes clusters"}}, "memoryInKubernetesClusterLimit": {"type": "string", "defaultValue": "64Gi", "metadata": {"displayName": "Max allowed memory bytes in Kubernetes cluster", "description": "The maximum memory bytes allowed for a container. E.g. 1Gi. For more information, please refer https://aka.ms/k8s-policy-pod-limits"}}, "CPUInKubernetesClusterLimit": {"type": "string", "defaultValue": "32", "metadata": {"displayName": "Max allowed CPU units in Kubernetes cluster", "description": "The maximum CPU units allowed for a container. E.g. 200m. For more information, please refer https://aka.ms/k8s-policy-pod-limits"}}, "memoryAndCPULimitsInKubernetesClusterNamespaceExclusion": {"type": "Array", "defaultValue": ["kube-system", "gatekeeper-system", "azure-arc", "azuredefender", "mdc", "azure-extensions-usage-system"], "metadata": {"displayName": "Kubernetes namespaces to exclude from monitoring of memory and CPU limits", "description": "List of Kubernetes namespaces to exclude from evaluation of memory and CPU limits in Kubernetes clusters. To list multiple namespaces, use semicolons (;) to separate them. System namespaces \"kube-system\", \"gatekeeper-system\" and \"azure-arc\" are always excluded by design."}}, "memoryAndCPULimitsInKubernetesClusterLabelSelector": {"type": "object", "metadata": {"displayName": "Kubernetes label selector to select monitoring of memory and CPU limits", "description": "Label query to select Kubernetes resources for policy evaluation. An empty label selector matches all Kubernetes resources."}, "defaultValue": {}, "schema": {"description": "A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all resources.", "type": "object", "properties": {"matchLabels": {"description": "matchLabels is a map of {key,value} pairs.", "type": "object", "additionalProperties": {"type": "string"}, "minProperties": 1}, "matchExpressions": {"description": "matchExpressions is a list of values, a key, and an operator.", "type": "array", "items": {"type": "object", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values.", "type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty.", "type": "array", "items": {"type": "string"}}}, "required": ["key", "operator"], "additionalProperties": false}, "minItems": 1}}, "additionalProperties": false}}, "BlockVulnerableImagesInKubernetesClusterEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Kubernetes clusters should gate deployment of vulnerable images", "description": "Enable or disable gating containers' with vulnerable images in Kubernetes clusters", "deprecated": true}}, "BlockVulnerableImagesInKubernetesClusterNamespaceExclusion": {"type": "Array", "defaultValue": ["kube-system", "gatekeeper-system", "azure-arc", "azure-extensions-usage-system"], "metadata": {"displayName": "Kubernetes namespaces to exclude from monitoring of containers with vulnerable images", "description": "List of Kubernetes namespaces to exclude from evaluation of block vulnerable images in Kubernetes clusters. To list multiple namespaces, use semicolons (;) to separate them. System namespaces \"kube-system\", \"gatekeeper-system\" and \"azure-arc\" are always excluded by design.", "deprecated": true}}, "BlockVulnerableImagesNamespaces": {"type": "Array", "defaultValue": [], "metadata": {"displayName": "Namespace inclusions", "description": "List of Kubernetes namespaces to only include in policy evaluation. An empty list means the policy is applied to all resources in all namespaces.", "deprecated": true}}, "BlockVulnerableImagesExcludedImages": {"type": "Array", "metadata": {"displayName": "Excluded images regex for gating vulnerable images in Kubernetes cluster", "description": "A list of RegEx rules used to exclude container images from policy evaluation. For example: exclude all images from the repo microsoft-defender-in-cluster-defense-repo in the blockreg ACR -  [\"(blockreg.azurecr.io/microsoft-defender-in-cluster-defense-repo).*\"]. Use an empty list to apply this policy to all container images.", "deprecated": true}, "defaultValue": []}, "BlockVulnerableImagesSeverityThresholdForExcludingNotPatchableFindings": {"type": "string", "metadata": {"displayName": "Severity threshold for excluding gating of image vulnerabilities without a patch in Kubernetes cluster", "description": "Specify the maximum severity for exempting vulnerabilities without a patch. For example, specify Medium to ignore Low and Medium vulnerabilities without a patch.", "deprecated": true}, "allowedValues": ["None", "Low", "Medium", "High"], "defaultValue": "None"}, "BlockVulnerableImagesExcludeFindingIDs": {"type": "Array", "metadata": {"displayName": "Exclude finding IDs for gating vulnerable images scan results in Kubernetes cluster", "description": "A list of finding IDs that the policy should exempt.", "deprecated": true}, "defaultValue": []}, "severity": {"type": "Object", "metadata": {"displayName": "Severity threshold for excluding gating of image vulnerabilities in Kubernetes cluster", "description": "The number of allowed findings per severity for an image. e.g. \"{\"High\":0,\"Medium\":3,\"Low\":10}\"", "deprecated": true}, "defaultValue": {"High": 0, "Medium": 0, "Low": 0}, "schema": {"type": "object", "properties": {"High": {"type": "integer"}, "Medium": {"type": "integer"}, "Low": {"type": "integer"}}, "required": ["High", "Medium", "Low"], "additionalProperties": false}}, "MustRunAsNonRootNamespaceExclusion": {"type": "Array", "defaultValue": ["kube-system", "gatekeeper-system", "azure-arc", "azuredefender", "mdc", "azure-extensions-usage-system"], "metadata": {"displayName": "Kubernetes namespaces to exclude from monitoring of containers running as root user", "description": "List of Kubernetes namespaces to exclude from evaluation to monitoring of containers running as root users. To list multiple namespaces, use semicolons (;) to separate them. System namespaces \"kube-system\", \"gatekeeper-system\" and \"azure-arc\" are always excluded by design."}}, "MustRunAsNonRootLabelSelector": {"type": "object", "metadata": {"displayName": "Kubernetes label selector to select monitoring of containers running as root user", "description": "Label query to select Kubernetes resources for policy evaluation. An empty label selector matches all Kubernetes resources."}, "defaultValue": {}, "schema": {"description": "A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all resources.", "type": "object", "properties": {"matchLabels": {"description": "matchLabels is a map of {key,value} pairs.", "type": "object", "additionalProperties": {"type": "string"}, "minProperties": 1}, "matchExpressions": {"description": "matchExpressions is a list of values, a key, and an operator.", "type": "array", "items": {"type": "object", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values.", "type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty.", "type": "array", "items": {"type": "string"}}}, "required": ["key", "operator"], "additionalProperties": false}, "minItems": 1}}, "additionalProperties": false}}, "MustRunAsNonRootNamespaceEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"], "metadata": {"displayName": "Kubernetes containers should not be run as root user", "description": "Enable or disable monitoring of containers running as root user in Kubernetes nodes"}}, "runAsGroupRule-f06ddb64-5fa3-4b77-b166-acb36f7f6042": {"type": "String", "defaultValue": "MustRunAs", "allowedValues": ["MustRunAs", "MayRunAs", "RunAsAny"], "metadata": {"displayName": "Run as group rule for Kubernetes containers", "description": "The 'RunAsGroup' rule that containers are allowed to run with; for more information, visit https://aka.ms/kubepolicydoc"}}, "runAsGroupRanges-f06ddb64-5fa3-4b77-b166-acb36f7f6042": {"type": "Object", "defaultValue": {"ranges": [{"min": 1, "max": -1}]}, "metadata": {"displayName": "Allowed group ID ranges for Kubernetes containers", "description": "Group ID ranges that are allowed for containers to use; for more information, visit https://aka.ms/kubepolicydoc"}}, "supplementalGroupsRule-f06ddb64-5fa3-4b77-b166-acb36f7f6042": {"type": "String", "defaultValue": "MayRunAs", "allowedValues": ["MustRunAs", "MayRunAs", "RunAsAny"], "metadata": {"displayName": "Supplemental group rule for Kubernetes containers", "description": "The 'SupplementalGroups' rule that containers are allowed to run with; for more information, visit https://aka.ms/kubepolicydoc"}}, "supplementalGroupsRanges-f06ddb64-5fa3-4b77-b166-acb36f7f6042": {"type": "Object", "defaultValue": {"ranges": [{"min": 1, "max": -1}]}, "metadata": {"displayName": "Allowed supplemental group ID ranges for Kubernetes containers", "description": "Supplemental group ID ranges that are allowed for containers to use; for more information, visit https://aka.ms/kubepolicydoc"}}, "fsGroupRule-f06ddb64-5fa3-4b77-b166-acb36f7f6042": {"type": "String", "defaultValue": "MayRunAs", "allowedValues": ["MustRunAs", "MayRunAs", "RunAsAny"], "metadata": {"displayName": "File system group rule for Kubernetes containers", "description": "The 'FSGroup' rule that containers are allowed to run with; for more information, visit https://aka.ms/kubepolicydoc"}}, "fsGroupRanges-f06ddb64-5fa3-4b77-b166-acb36f7f6042": {"type": "Object", "defaultValue": {"ranges": [{"min": 1, "max": -1}]}, "metadata": {"displayName": "Allowed file system group ID ranges for Kubernetes cluster pods", "description": "File system group ranges that are allowed for pods to use; for more information, visit https://aka.ms/kubepolicydoc"}}, "arcEnabledKubernetesClustersShouldHaveAzureDefendersExtensionInstalled": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Azure Arc enabled Kubernetes clusters should have Azure Defender's extension installed", "description": "Enable or disable the monitoring of Arc enabled Kubernetes clusters without Azure Defender's extension installed"}}, "azureKubernetesServiceClustersShouldHaveSecurityProfileEnabled": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Azure Kubernetes Service clusters should have Azure Defender profile enabled", "description": "Enable or disable the monitoring of Azure Kubernetes Service clusters without Azure Defender's profile enabled"}}, "containerRegistryVulnerabilityAssessmentEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Vulnerabilities in Azure Container Registry images should be remediated", "description": "Enable or disable monitoring of Azure container registries by Microsoft Defender for Cloud vulnerability assessment (powered by Qualys)", "deprecated": true}}, "azureContainerRegistryVulnerabilityAssessmentEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Vulnerabilities in Azure Container Registry images should be remediated", "description": "Enable or disable monitoring of Azure container registries by Microsoft Defender for Cloud vulnerability assessment (powered by Microsoft Defender Vulnerability Management)"}}, "kubernetesRunningImagesVulnerabilityAssessmentEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Vulnerabilities in running images should be remediated", "description": "Enable or disable monitoring of Kubernetes Service clusters by Defender for Containers running images vulnerability assessment", "deprecated": true}}, "kubernetesRunningImagesVulnerabilityMDVMAssessmentEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Vulnerabilities in running images should be remediated", "description": "Enable or disable monitoring of Kubernetes Service clusters by Defender for Containers running images vulnerability assessment"}}, "disallowPublicBlobAccessEffect": {"type": "string", "defaultValue": "audit", "allowedValues": ["audit", "deny", "disabled"], "metadata": {"displayName": "Storage account public access should be disallowed", "description": "Enable or disable reporting of Storage Accounts that allow public access"}}, "azureBackupShouldBeEnabledForVirtualMachinesMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Azure Backup should be enabled for Virtual Machines", "description": "Ensure protection of your Azure Virtual Machines by enabling Azure Backup. Azure Backup is a secure and cost effective data protection solution for Azure."}}, "managedIdentityShouldBeUsedInYourFunctionAppMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Managed identity should be used in your Function App", "description": "Use a managed identity for enhanced authentication security"}}, "georedundantBackupShouldBeEnabledForAzureDatabaseForMariadbMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Georedundant backup should be enabled for Azure Database for MariaDB", "description": "Azure Database for MariaDB allows you to choose the redundancy option for your database server. It can be set to a geo-redundant backup storage in which the data is not only stored within the region in which your server is hosted, but is also replicated to a paired region to provide recovery option in case of a region failure. Configuring geo-redundant storage for backup is only allowed during server create."}}, "managedIdentityShouldBeUsedInYourWebAppMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Managed identity should be used in your Web App", "description": "Use a managed identity for enhanced authentication security"}}, "georedundantBackupShouldBeEnabledForAzureDatabaseForPostgresqlMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Georedundant backup should be enabled for Azure Database for PostgreSQL", "description": "Azure Database for PostgreSQL allows you to choose the redundancy option for your database server. It can be set to a geo-redundant backup storage in which the data is not only stored within the region in which your server is hosted, but is also replicated to a paired region to provide recovery option in case of a region failure. Configuring geo-redundant storage for backup is only allowed during server create."}}, "ensureWEBAppHasClientCertificatesIncomingClientCertificatesSetToOnMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Ensure WEB app has Client Certificates Incoming client certificates set to On", "description": "Client certificates allow for the app to request a certificate for incoming requests. Only clients that have a valid certificate will be able to reach the app.", "deprecated": true}}, "georedundantBackupShouldBeEnabledForAzureDatabaseForMysqlMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Georedundant backup should be enabled for Azure Database for MySQL", "description": "Azure Database for MySQL allows you to choose the redundancy option for your database server. It can be set to a geo-redundant backup storage in which the data is not only stored within the region in which your server is hosted, but is also replicated to a paired region to provide recovery option in case of a region failure. Configuring geo-redundant storage for backup is only allowed during server create."}}, "latestTLSVersionShouldBeUsedInYourAPIAppMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Latest TLS version should be used in your API App", "description": "Upgrade to the latest TLS version", "deprecated": true}}, "diagnosticLogsInAppServicesShouldBeEnabledMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Resource logs in App Services should be enabled", "description": "Audit enabling of resource logs on the app. This enables you to recreate activity trails for investigation purposes if a security incident occurs or your network is compromised"}}, "managedIdentityShouldBeUsedInYourAPIAppMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Managed identity should be used in your API App", "description": "Use a managed identity for enhanced authentication security", "deprecated": true}}, "enforceSSLConnectionShouldBeEnabledForPostgresqlDatabaseServersMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Enforce SSL connection should be enabled for PostgreSQL database servers", "description": "Azure Database for PostgreSQL supports connecting your Azure Database for PostgreSQL server to client applications using Secure Sockets Layer (SSL). Enforcing SSL connections between your database server and your client applications helps protect against 'man in the middle' attacks by encrypting the data stream between the server and your application. This configuration enforces that SSL is always enabled for accessing your database server."}}, "enforceSSLConnectionShouldBeEnabledForMysqlDatabaseServersMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Enforce SSL connection should be enabled for MySQL database servers", "description": "Azure Database for MySQL supports connecting your Azure Database for MySQL server to client applications using Secure Sockets Layer (SSL). Enforcing SSL connections between your database server and your client applications helps protect against 'man in the middle' attacks by encrypting the data stream between the server and your application. This configuration enforces that SSL is always enabled for accessing your database server."}}, "latestTLSVersionShouldBeUsedInYourWebAppMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Latest TLS version should be used in your Web App", "description": "Upgrade to the latest TLS version"}}, "latestTLSVersionShouldBeUsedInYourFunctionAppMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Latest TLS version should be used in your Function App", "description": "Upgrade to the latest TLS version"}}, "ensureThatPHPVersionIsTheLatestIfUsedAsAPartOfTheApiAppMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Ensure that PHP version is the latest if used as a part of the API app", "description": "Periodically, newer versions are released for PHP software either due to security flaws or to include additional functionality. Using the latest PHP version for API apps is recommended in order to take advantage of security fixes, if any, and/or new functionalities of the latest version. Currently, this policy only applies to Linux web apps.", "deprecated": true}}, "ensureThatPHPVersionIsTheLatestIfUsedAsAPartOfTheWEBAppMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Ensure that PHP version is the latest if used as a part of the WEB app", "description": "Periodically, newer versions are released for PHP software either due to security flaws or to include additional functionality. Using the latest PHP version for web apps is recommended in order to take advantage of security fixes, if any, and/or new functionalities of the latest version. Currently, this policy only applies to Linux web apps.", "deprecated": true}}, "ensureThatJavaVersionIsTheLatestIfUsedAsAPartOfTheWebAppMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Ensure that Java version is the latest if used as a part of the Web app", "description": "Periodically, newer versions are released for Java software either due to security flaws or to include additional functionality. Using the latest Java version for web apps is recommended in order to take advantage of security fixes, if any, and/or new functionalities of the latest version. Currently, this policy only applies to Linux web apps.", "deprecated": true}}, "ensureThatJavaVersionIsTheLatestIfUsedAsAPartOfTheFunctionAppMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Ensure that Java version is the latest if used as a part of the Function app", "description": "Periodically, newer versions are released for Java software either due to security flaws or to include additional functionality. Using the latest Java version for Function apps is recommended in order to take advantage of security fixes, if any, and/or new functionalities of the latest version. Currently, this policy only applies to Linux web apps.", "deprecated": true}}, "ensureThatJavaVersionIsTheLatestIfUsedAsAPartOfTheApiAppMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Ensure that Java version is the latest if used as a part of the API app", "description": "Periodically, newer versions are released for Java either due to security flaws or to include additional functionality. Using the latest Python version for API apps is recommended in order to take advantage of security fixes, if any, and/or new functionalities of the latest version. Currently, this policy only applies to Linux web apps.", "deprecated": true}}, "ensureThatPythonVersionIsTheLatestIfUsedAsAPartOfTheWebAppMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Ensure that Python version is the latest if used as a part of the Web app", "description": "Periodically, newer versions are released for Python software either due to security flaws or to include additional functionality. Using the latest Python version for web apps is recommended in order to take advantage of security fixes, if any, and/or new functionalities of the latest version. Currently, this policy only applies to Linux web apps.", "deprecated": true}}, "ensureThatPythonVersionIsTheLatestIfUsedAsAPartOfTheFunctionAppMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Ensure that Python version is the latest if used as a part of the Function app", "description": "Periodically, newer versions are released for Python software either due to security flaws or to include additional functionality. Using the latest Python version for Function apps is recommended in order to take advantage of security fixes, if any, and/or new functionalities of the latest version. Currently, this policy only applies to Linux web apps.", "deprecated": true}}, "ensureThatPythonVersionIsTheLatestIfUsedAsAPartOfTheApiAppMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Ensure that Python version is the latest if used as a part of the API app", "description": "Periodically, newer versions are released for Python software either due to security flaws or to include additional functionality. Using the latest Python version for API apps is recommended in order to take advantage of security fixes, if any, and/or new functionalities of the latest version. Currently, this policy only applies to Linux web apps.", "deprecated": true}}, "privateEndpointShouldBeEnabledForPostgresqlServersMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Private endpoint should be enabled for PostgreSQL servers", "description": "Private endpoint connections enforce secure communication by enabling private connectivity to Azure Database for PostgreSQL. Configure a private endpoint connection to enable access to traffic coming only from known networks and prevent access from all other IP addresses, including within Azure."}}, "privateEndpointShouldBeEnabledForMariadbServersMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Private endpoint should be enabled for MariaDB servers", "description": "Private endpoint connections enforce secure communication by enabling private connectivity to Azure Database for MariaDB. Configure a private endpoint connection to enable access to traffic coming only from known networks and prevent access from all other IP addresses, including within Azure."}}, "privateEndpointShouldBeEnabledForMysqlServersMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Private endpoint should be enabled for MySQL servers", "description": "Private endpoint connections enforce secure communication by enabling private connectivity to Azure Database for MySQL. Configure a private endpoint connection to enable access to traffic coming only from known networks and prevent access from all other IP addresses, including within Azure."}}, "sQLServersShouldBeConfiguredWithAuditingRetentionDaysGreaterThan90DaysMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "SQL servers should be configured with auditing retention days greater than 90 days", "description": "Audit SQL servers configured with an auditing retention period of less than 90 days."}}, "fTPSOnlyShouldBeRequiredInYourFunctionAppMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "FTPS only should be required in your Function App", "description": "Enable FTPS enforcement for enhanced security"}}, "fTPSShouldBeRequiredInYourWebAppMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "FTPS should be required in your Web App", "description": "Enable FTPS enforcement for enhanced security"}}, "fTPSOnlyShouldBeRequiredInYourAPIAppMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "FTPS only should be required in your API App", "description": "Enable FTPS enforcement for enhanced security", "deprecated": true}}, "functionAppsShouldHaveClientCertificatesEnabledMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Function apps should have 'Client Certificates (Incoming client certificates)' enabled", "description": "Client certificates allow for the app to request a certificate for incoming requests. Only clients with valid certificates will be able to reach the app.", "deprecated": true}}, "cognitiveServicesAccountsShouldEnableDataEncryptionWithACustomerManagedKeyMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Cognitive Services accounts should enable data encryption with a customer-managed key", "description": "Customer-managed keys are commonly required to meet regulatory compliance standards. Customer-managed keys enable the data stored in Cognitive Services to be encrypted with an Azure Key Vault key created and owned by you. You have full control and responsibility for the key lifecycle, including rotation and management. Learn more about customer-managed key encryption at https://aka.ms/cosmosdb-cmk."}}, "azureCosmosDbAccountsShouldUseCustomerManagedKeysToEncryptDataAtRestMonitoringEffect": {"type": "string", "defaultValue": "disabled", "allowedValues": ["audit", "deny", "disabled"], "metadata": {"displayName": "Azure Cosmos DB accounts should use customer-managed keys to encrypt data at rest", "description": "Use customer-managed keys to manage the encryption at rest of your Azure Cosmos DB. By default, the data is encrypted at rest with service-managed keys, but customer-managed keys are commonly required to meet regulatory compliance standards. Customer-managed keys enable the data to be encrypted with an Azure Key Vault key created and owned by you. You have full control and responsibility for the key lifecycle, including rotation and management. Learn more about customer-managed key encryption at https://aka.ms/cosmosdb-cmk."}}, "azureCosmosDbAccountsShouldHaveLocalAuthenticationMethodsDisabledMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Cosmos DB database accounts should have local authentication methods disabled", "description": "Disabling local authentication methods improves security by ensuring that Cosmos DB database accounts exclusively require Azure Active Directory identities for authentication. Learn more at: https://docs.microsoft.com/azure/cosmos-db/how-to-setup-rbac#disable-local-auth."}}, "keyVaultsShouldHavePurgeProtectionEnabledMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Key vaults should have purge protection enabled", "description": "Malicious deletion of a key vault can lead to permanent data loss. A malicious insider in your organization can potentially delete and purge key vaults. Purge protection protects you from insider attacks by enforcing a mandatory retention period for soft deleted key vaults. No one inside your organization or Microsoft will be able to purge your key vaults during the soft delete retention period."}}, "keyVaultsShouldHaveSoftDeleteEnabledMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Key vaults should have soft delete enabled", "description": "Deleting a key vault without soft delete enabled permanently deletes all secrets, keys, and certificates stored in the key vault. Accidental deletion of a key vault can lead to permanent data loss. Soft delete allows you to recover an accidentally deleted key vault for a configurable retention period."}}, "azureCacheForRedisShouldResideWithinAVirtualNetworkMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"deprecated": true, "displayName": "Azure Cache for Redis should reside within a virtual network", "description": "Azure Virtual Network deployment provides enhanced security and isolation for your Azure Cache for Redis, as well as subnets, access control policies, and other features to further restrict access.When an Azure Cache for Redis instance is configured with a virtual network, it is not publicly addressable and can only be accessed from virtual machines and applications within the virtual network."}}, "azureCacheForRedisShouldUsePrivateEndpointMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Azure Cache for Redis should use private link", "description": "Private endpoints lets you connect your virtual network to Azure services without a public IP address at the source or destination. By mapping private endpoints to your Azure Cache for Redis instances, data leakage risks are reduced. Learn more at: https://docs.microsoft.com/azure/azure-cache-for-redis/cache-private-link."}}, "storageAccountsShouldUseCustomerManagedKeyForEncryptionMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Storage accounts should use customer-managed key for encryption", "description": "Secure your storage account with greater flexibility using customer-managed keys. When you specify a customer-managed key, that key is used to protect and control access to the key that encrypts your data. Using customer-managed keys provides additional capabilities to control rotation of the key encryption key or cryptographically erase data."}}, "storageAccountsShouldRestrictNetworkAccessUsingVirtualNetworkRulesMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Storage accounts should restrict network access using virtual network rules", "description": "Protect your storage accounts from potential threats using virtual network rules as a preferred method instead of IP-based filtering. Disabling IP-based filtering prevents public IPs from accessing your storage accounts."}}, "containerRegistriesShouldBeEncryptedWithACustomerManagedKeyMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Container registries should be encrypted with a customer-managed key", "description": "Use customer-managed keys to manage the encryption at rest of the contents of your registries. By default, the data is encrypted at rest with service-managed keys, but customer-managed keys are commonly required to meet regulatory compliance standards. Customer-managed keys enable the data to be encrypted with an Azure Key Vault key created and owned by you. You have full control and responsibility for the key lifecycle, including rotation and management. Learn more about customer-managed key encryption at https://aka.ms/acr/CMK."}}, "containerRegistriesShouldNotAllowUnrestrictedNetworkAccessMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Container registries should not allow unrestricted network access", "description": "Azure container registries by default accept connections over the internet from hosts on any network. To protect your registries from potential threats, allow access from only specific public IP addresses or address ranges. If your registry doesn't have an IP/firewall rule or a configured virtual network, it will appear in the unhealthy resources. Learn more about Container Registry network rules here: https://aka.ms/acr/portal/public-network and here https://aka.ms/acr/vnet."}}, "containerRegistriesShouldUsePrivateLinkMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Container registries should use private link", "description": "Azure Private Link lets you connect your virtual network to Azure services without a public IP address at the source or destination. The private link platform handles the connectivity between the consumer and services over the Azure backbone network.By mapping private endpoints to your container registries instead of the entire service, you'll also be protected against data leakage risks. Learn more at: https://aka.ms/acr/private-link."}}, "appConfigurationShouldUsePrivateLinkMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "App Configuration should use private link", "description": "Azure Private Link lets you connect your virtual network to Azure services without a public IP address at the source or destination. The private link platform handles the connectivity between the consumer and services over the Azure backbone network. By mapping private endpoints to your app configuration instances instead of the entire service, you'll also be protected against data leakage risks. Learn more at: https://aka.ms/appconfig/private-endpoint."}}, "azureEventGridDomainsShouldUsePrivateLinkMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Azure Event Grid domains should use private link", "description": "Azure Private Link lets you connect your virtual network to Azure services without a public IP address at the source or destination. The private link platform handles the connectivity between the consumer and services over the Azure backbone network.By mapping private endpoints to your Event Grid domains instead of the entire service, you'll also be protected against data leakage risks.Learn more at: https://aka.ms/privateendpoints."}}, "azureEventGridTopicsShouldUsePrivateLinkMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Azure Event Grid topics should use private link", "description": "Azure Private Link lets you connect your virtual network to Azure services without a public IP address at the source or destination. The private link platform handles the connectivity between the consumer and services over the Azure backbone network. By mapping private endpoints to your topics instead of the entire service, you'll also be protected against data leakage risks. Learn more at: https://aka.ms/privateendpoints."}}, "azureSignalRServiceShouldUsePrivateLinkMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Azure SignalR Service should use private link", "description": "Azure Private Link lets you connect your virtual network to Azure services without a public IP address at the source or destination. The private link platform handles the connectivity between the consumer and services over the Azure backbone network. By mapping private endpoints to your  SignalR resources instead of the entire service, you'll also be protected against data leakage risks .Learn more at: https://aka.ms/asrs/privatelink."}}, "azureMachineLearningWorkspacesShouldBeEncryptedWithACustomerManagedKeyMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Azure Machine Learning workspaces should be encrypted with a customer-managed key", "description": "Manage encryption at rest of your Azure Machine Learning workspace data with customer-managed keys. By default, customer data is encrypted with service-managed keys, but customer-managed keys are commonly required to meet regulatory compliance standards. Customer-managed keys enable the data to be encrypted with an Azure Key Vault key created and owned by you. You have full control and responsibility for the key lifecycle, including rotation and management. Learn more about customer-managed key encryption at https://aka.ms/azureml-workspaces-cmk."}}, "azureMachineLearningWorkspacesShouldUsePrivateLinkMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Azure Machine Learning workspaces should use private link", "description": "Azure Private Link lets you connect your virtual network to Azure services without a public IP address at the source or destination. The private link platform handles the connectivity between the consumer and services over the Azure backbone network. By mapping private endpoints to your Azure Machine Learning workspaces instead of the entire service, you'll also be protected against data leakage risks. Learn more at: https://aka.ms/azureml-workspaces-privatelink."}}, "webApplicationFirewallShouldBeEnabledForAzureFrontDoorServiceServiceMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Azure Web Application Firewall should be enabled for Azure Front Door entry-points", "description": "Deploy Azure Web Application Firewall (WAF) in front of public facing web applications for additional inspection of incoming traffic. Web Application Firewall (WAF) provides centralized protection of your web applications from common exploits and vulnerabilities such as SQL injections, Cross-Site Scripting, local and remote file executions. You can also restrict access to your web applications by countries, IP address ranges, and other http(s) parameters via custom rules."}}, "webApplicationFirewallShouldBeEnabledForApplicationGatewayMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Web Application Firewall (WAF) should be enabled for Application Gateway", "description": "Deploy Azure Web Application Firewall (WAF) in front of public facing web applications for additional inspection of incoming traffic. Web Application Firewall (WAF) provides centralized protection of your web applications from common exploits and vulnerabilities such as SQL injections, Cross-Site Scripting, local and remote file executions. You can also restrict access to your web applications by countries, IP address ranges, and other http(s) parameters via custom rules."}}, "publicNetworkAccessShouldBeDisabledForMariaDbServersMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Public network access should be disabled for MariaDB servers", "description": "Disable the public network access property to improve security and ensure your Azure Database for MariaDB can only be accessed from a private endpoint. This configuration strictly disables access from any public address space outside of Azure IP range, and denies all logins that match IP or virtual network-based firewall rules."}}, "publicNetworkAccessShouldBeDisabledForMySqlServersMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Public network access should be disabled for MySQL servers", "description": "Disable the public network access property to improve security and ensure your Azure Database for MySQL can only be accessed from a private endpoint. This configuration strictly disables access from any public address space outside of Azure IP range, and denies all logins that match IP or virtual network-based firewall rules."}}, "bringYourOwnKeyDataProtectionShouldBeEnabledForMySqlServersMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "MySQL servers should use customer-managed keys to encrypt data at rest", "description": "Use customer-managed keys to manage the encryption at rest of your MySQL servers. By default, the data is encrypted at rest with service-managed keys, but customer-managed keys are commonly required to meet regulatory compliance standards. Customer-managed keys enable the data to be encrypted with an Azure Key Vault key created and owned by you. You have full control and responsibility for the key lifecycle, including rotation and management."}}, "publicNetworkAccessShouldBeDisabledForPostgreSqlServersMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Public network access should be disabled for PostgreSQL servers", "description": "Disable the public network access property to improve security and ensure your Azure Database for PostgreSQL can only be accessed from a private endpoint. This configuration disables access from any public address space outside of Azure IP range, and denies all logins that match IP or virtual network-based firewall rules."}}, "bringYourOwnKeyDataProtectionShouldBeEnabledForPostgreSqlServersMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "PostgreSQL servers should use customer-managed keys to encrypt data at rest", "description": "Use customer-managed keys to manage the encryption at rest of your PostgreSQL servers. By default, the data is encrypted at rest with service-managed keys, but customer-managed keys are commonly required to meet regulatory compliance standards. Customer-managed keys enable the data to be encrypted with an Azure Key Vault key created and owned by you. You have full control and responsibility for the key lifecycle, including rotation and management."}}, "vmImageBuilderTemplatesShouldUsePrivateLinkMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "VM Image Builder templates should use private link", "description": "Audit VM Image Builder templates that do not have a virtual network configured. When a virtual network is not configured, a public IP is created and used instead which may directly expose resources to the internet and increase the potential attack surface."}}, "firewallShouldBeEnabledOnKeyVaultMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Firewall should be enabled on Key Vault", "description": "Key vault's firewall prevents unauthorized traffic from reaching your key vault and provides an additional layer of protection for your secrets. Enable the firewall to make sure that only traffic from allowed networks can access your key vault."}}, "privateEndpointShouldBeConfiguredForKeyVaultMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Private endpoint should be configured for Key Vault", "description": "Private link provides a way to connect Key Vault to your Azure resources without sending traffic over the public internet. Private link provides defense in depth protection against data exfiltration."}}, "azureSpringCloudShouldUseNetworkInjectionMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Azure Spring Cloud should use network injection", "description": "Azure Spring Cloud instances should use virtual network injection for the following purposes: 1. Isolate Azure Spring Cloud from Internet. 2. Enable Azure Spring Cloud to interact with systems in either on premises data centers or Azure service in other virtual networks. 3. Empower customers to control inbound and outbound network communications for Azure Spring Cloud."}}, "subscriptionsShouldHaveAContactEmailAddressForSecurityIssuesMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Subscriptions should have a contact email address for security issues", "description": "To ensure the relevant people in your organization are notified when there is a potential security breach in one of your subscriptions, set a security contact to receive email notifications from Security Center."}}, "autoProvisioningOfTheLogAnalyticsAgentShouldBeEnabledOnYourSubscriptionMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Auto provisioning of the Log Analytics agent should be enabled on your subscription", "description": "To monitor for security vulnerabilities and threats, Microsoft Defender for Cloud collects data from your Azure virtual machines. Data is collected by the Log Analytics agent, formerly known as the Microsoft Monitoring Agent (MMA), which reads various security-related configurations and event logs from the machine and copies the data to your Log Analytics workspace for analysis. We recommend enabling auto provisioning to automatically deploy the agent to all supported Azure VMs and any new ones that are created.", "deprecated": true}}, "emailNotificationForHighSeverityAlertsShouldBeEnabledMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Email notification for high severity alerts should be enabled", "description": "To ensure the relevant people in your organization are notified when there is a potential security breach in one of your subscriptions, enable email notifications for high severity alerts in Security Center."}}, "emailNotificationToSubscriptionOwnerForHighSeverityAlertsShouldBeEnabledMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Email notification to subscription owner for high severity alerts should be enabled", "description": "To ensure your subscription owners are notified when there is a potential security breach in their subscription, set email notifications to subscription owners for high severity alerts in Security Center."}}, "storageAccountShouldUseAPrivateLinkConnectionMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Storage account should use a private link connection", "description": "Private links enforce secure communication, by providing private connectivity to the storage account"}}, "authenticationToLinuxMachinesShouldRequireSSHKeysMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Authentication to Linux machines should require SSH keys", "description": "Although SSH itself provides an encrypted connection, using passwords with SSH still leaves the VM vulnerable to brute-force attacks. The most secure option for authenticating to an Azure Linux virtual machine over SSH is with a public-private key pair, also known as SSH keys. Learn more: https://docs.microsoft.com/azure/virtual-machines/linux/create-ssh-keys-detailed."}}, "privateEndpointConnectionsOnAzureSQLDatabaseShouldBeEnabledMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Private endpoint connections on Azure SQL Database should be enabled", "description": "Private endpoint connections enforce secure communication by enabling private connectivity to Azure SQL Database."}}, "publicNetworkAccessOnAzureSQLDatabaseShouldBeDisabledMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Public network access on Azure SQL Database should be disabled", "description": "Disabling the public network access property improves security by ensuring your Azure SQL Database can only be accessed from a private endpoint. This configuration denies all logins that match IP or virtual network based firewall rules."}}, "ensureAPIAppHasClientCertificatesIncomingClientCertificatesSetToOnMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Ensure API app has Client Certificates Incoming client certificates set to On", "description": "Client certificates allow for the app to request a certificate for incoming requests. Only clients that have a valid certificate will be able to reach the app.", "deprecated": true}}, "kubernetesClustersShouldBeAccessibleOnlyOverHTTPSMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"], "metadata": {"displayName": "Kubernetes clusters should be accessible only over HTTPS", "description": "Use of HTTPS ensures authentication and protects data in transit from network layer eavesdropping attacks. This capability is currently generally available for Kubernetes Service (AKS), and in preview for Azure Arc enabled Kubernetes. For more info, visit https://aka.ms/kubepolicydoc"}}, "kubernetesClustersShouldBeAccessibleOnlyOverHTTPSExcludedNamespaces": {"type": "Array", "defaultValue": ["kube-system", "gatekeeper-system", "azure-arc", "azure-extensions-usage-system"], "metadata": {"displayName": "Kubernetes namespaces to exclude from evaluation of HTTPS only access", "description": "List of Kubernetes namespaces to exclude from evaluation of HTTPS only access. To list multiple namespaces, use semicolons (;) to separate them. System namespaces \"kube-system\", \"gatekeeper-system\" and \"azure-arc\" are always excluded by design."}}, "kubernetesClustersShouldBeAccessibleOnlyOverHTTPSLabelSelector": {"type": "object", "metadata": {"displayName": "Kubernetes label selector to select evaluation of HTTPS only access", "description": "Label query to select Kubernetes resources for policy evaluation. An empty label selector matches all Kubernetes resources."}, "defaultValue": {}, "schema": {"description": "A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all resources.", "type": "object", "properties": {"matchLabels": {"description": "matchLabels is a map of {key,value} pairs.", "type": "object", "additionalProperties": {"type": "string"}, "minProperties": 1}, "matchExpressions": {"description": "matchExpressions is a list of values, a key, and an operator.", "type": "array", "items": {"type": "object", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values.", "type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty.", "type": "array", "items": {"type": "string"}}}, "required": ["key", "operator"], "additionalProperties": false}, "minItems": 1}}, "additionalProperties": false}}, "kubernetesClustersShouldBeAccessibleOnlyOverHTTPSNamespaces": {"type": "Array", "defaultValue": [], "metadata": {"displayName": "Namespace inclusions", "description": "This parameter is deprecated. The policy is applied to all Kubernetes resources in all Kubernetes namespaces.", "deprecated": true}}, "windowsWebServersShouldBeConfiguredToUseSecureCommunicationProtocolsMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Windows web servers should be configured to use secure communication protocols", "description": "To protect the privacy of information communicated over the Internet, your web servers should use the latest version of the industry-standard cryptographic protocol, Transport Layer Security (TLS). TLS secures communications over a network by using security certificates to encrypt a connection between machines."}}, "windowsWebServersShouldBeConfiguredToUseSecureCommunicationProtocolsIncludeArcMachines": {"type": "string", "defaultValue": "true", "allowedValues": ["true", "false"], "metadata": {"displayName": "Include Arc connected servers", "description": "By selecting this option, you choose to include Arc connected servers in the scope of this policy."}}, "windowsWebServersShouldBeConfiguredToUseSecureCommunicationProtocolsMinimumTLSVersion": {"type": "string", "defaultValue": "1.2", "allowedValues": ["1.1", "1.2"], "metadata": {"displayName": "Minimum TLS version", "description": "The minimum TLS protocol version that should be enabled. Windows web servers with lower TLS versions will be marked as non-compliant."}}, "cognitiveServicesAccountsShouldRestrictNetworkAccessMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Cognitive Services accounts should restrict network access", "description": "Network access to Cognitive Services accounts should be restricted. Configure network rules so only applications from allowed networks can access the Cognitive Services account. To allow connections from specific internet or on-premises clients, access can be granted to traffic from specific Azure virtual networks or to public internet IP address ranges."}}, "cognitiveServicesAccountsShouldUseCustomerOwnedStorageOrEnableDataEncryptionMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Cognitive Services accounts should use customer owned storage or enable data encryption", "description": "This policy audits any Cognitive Services account not using customer owned storage nor data encryption. For each Cognitive Services account with storage, use either customer owned storage or enable data encryption.", "deprecated": true}}, "publicNetworkAccessShouldBeDisabledForCognitiveServicesAccountsMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Public network access should be disabled for Cognitive Services accounts", "description": "This policy audits any Cognitive Services account in your environment with public network access enabled. Public network access should be disabled so that only connections from private endpoints are allowed.", "deprecated": true}}, "cognitiveServicesAccountsShouldEnableDataEncryptionMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Cognitive Services accounts should enable data encryption", "description": "This policy audits any Cognitive Services account not using data encryption. For each Cognitive Services account with storage, should enable data encryption with either customer managed or Microsoft managed key.", "deprecated": true}}, "aPIManagementServicesShouldUseAVirtualNetworkMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "API Management services should use a virtual network", "description": "Azure Virtual Network deployment provides enhanced security, isolation and allows you to place your API Management service in a non-internet routable network that you control access to. These networks can then be connected to your on-premises networks using various VPN technologies, which enables access to your backend services within the network and/or on-premises. The developer portal and API gateway, can be configured to be accessible either from the Internet or only within the virtual network."}}, "aPIManagementServicesShouldUseAVirtualNetworkEvaluatedSkuNames": {"type": "Array", "defaultValue": ["Developer", "Premium"], "allowedValues": ["Developer", "Basic", "Standard", "Premium", "Consumption"], "metadata": {"displayName": "API Management SKU Names", "description": "List of API Management SKUs against which this policy will be evaluated."}}, "azureCosmosDBAccountsShouldHaveFirewallRulesMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Azure Cosmos DB accounts should have firewall rules", "description": "Firewall rules should be defined on your Azure Cosmos DB accounts to prevent traffic from unauthorized sources. Accounts that have at least one IP rule defined with the virtual network filter enabled are deemed compliant. Accounts disabling public access are also deemed compliant."}}, "networkWatcherShouldBeEnabledMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Network Watcher should be enabled", "description": "Network Watcher is a regional service that enables you to monitor and diagnose conditions at a network scenario level in, to, and from Azure. Scenario level monitoring enables you to diagnose problems at an end to end network level view. Network diagnostic and visualization tools available with Network Watcher help you understand, diagnose, and gain insights to your network in Azure."}}, "networkWatcherShouldBeEnabledListOfLocations": {"type": "Array", "defaultValue": [], "metadata": {"displayName": "List of regions where Network Watcher should be enabled", "description": "To see a complete list of regions, run the PowerShell command Get-AzLocation", "strongType": "location", "deprecated": true}}, "networkWatcherShouldBeEnabledResourceGroupName": {"type": "String", "defaultValue": "NetworkWatcherRG", "metadata": {"displayName": "Name of the resource group for Network Watcher", "description": "Name of the resource group where Network Watchers are located"}}, "AzureDefenderForResourceManagerShouldBeEnabledMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Azure Defender for Resource Manager should be enabled", "description": "Azure Defender for Resource Manager automatically monitors the resource management operations in your organization. Azure Defender detects threats and alerts you about suspicious activity. Learn more about the capabilities of Azure Defender for Resource Manager at https://aka.ms/defender-for-resource-manager . Enabling this Azure Defender plan results in charges. Learn about the pricing details per region on Security Center's pricing page: https://aka.ms/pricing-security-center ."}}, "AzureDefenderForDNSShouldBeEnabledMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Azure Defender for DNS should be enabled", "description": "Azure Defender for DNS provides an additional layer of protection for your cloud resources by continuously monitoring all DNS queries from your Azure resources. Azure Defender alerts you about suspicious activity at the DNS layer. Learn more about the capabilities of Azure Defender for DNS at https://aka.ms/defender-for-dns . Enabling this Azure Defender plan results in charges. Learn about the pricing details per region on Security Center's pricing page: https://aka.ms/pricing-security-center .", "deprecated": true}}, "AzureDefenderForOpenSourceRelationalDatabasesShouldBeEnabledMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Azure Defender for open-source relational databases should be enabled", "description": "Azure Defender for open-source relational databases detects anomalous activities indicating unusual and potentially harmful attempts to access or exploit databases. Learn more about the capabilities of Azure Defender for open-source relational databases at https://aka.ms/AzDforOpenSourceDBsDocu. Important: Enabling this plan will result in charges for protecting your open-source relational databases. Learn about the pricing on Security Center's pricing page: https://aka.ms/pricing-security-center."}}, "MicrosoftDefenderCSPMShouldBeEnabledMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Microsoft Defender CSPM should be enabled", "description": "Defender Cloud Security Posture Management (CSPM) provides enhanced posture capabilities and a new intelligent cloud security graph to help identify, prioritize, and reduce risk. Defender CSPM is available in addition to the free foundational security posture capabilities turned on by default in Defender for Cloud."}}, "KubernetesClustersShouldNotUseTheDefaultNamespaceMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"], "metadata": {"displayName": "Kubernetes clusters should not use the default namespace", "description": "Prevent usage of the default namespace in Kubernetes clusters to protect against unauthorized access for ConfigMap, Pod, Secret, Service, and ServiceAccount resource types. For more information, see https://aka.ms/kubepolicydoc."}}, "KubernetesClustersShouldNotUseTheDefaultNamespaceMonitoringLabelSelector": {"type": "object", "metadata": {"displayName": "Kubernetes label selector to select monitoring of block default namespace", "description": "Label query to select Kubernetes resources for policy evaluation. An empty label selector matches all Kubernetes resources."}, "defaultValue": {}, "schema": {"description": "A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all resources.", "type": "object", "properties": {"matchLabels": {"description": "matchLabels is a map of {key,value} pairs.", "type": "object", "additionalProperties": {"type": "string"}, "minProperties": 1}, "matchExpressions": {"description": "matchExpressions is a list of values, a key, and an operator.", "type": "array", "items": {"type": "object", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values.", "type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty.", "type": "array", "items": {"type": "string"}}}, "required": ["key", "operator"], "additionalProperties": false}, "minItems": 1}}, "additionalProperties": false}}, "KubernetesClustersShouldDisableAutomountingAPICredentialsMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"], "metadata": {"displayName": "Kubernetes clusters should disable automounting API credentials", "description": "Disable automounting API credentials to prevent a potentially compromised Pod resource to run API commands against Kubernetes clusters. For more information, see https://aka.ms/kubepolicydoc."}}, "KubernetesClustersShouldDisableAutomountingAPICredentialsMonitoringNamespaceExclusion": {"type": "Array", "defaultValue": ["kube-system", "gatekeeper-system", "azure-arc", "azuredefender", "mdc", "azure-extensions-usage-system"], "metadata": {"displayName": "Kubernetes namespaces to exclude from restricting automounting API credentials", "description": "List of Kubernetes namespaces to exclude from evaluation to restrict automounting API credentials. To list multiple namespaces, use semicolons (;) to separate them. System namespaces \"kube-system\", \"gatekeeper-system\" and \"azure-arc\" are always excluded by design."}}, "KubernetesClustersShouldDisableAutomountingAPICredentialsMonitoringLabelSelector": {"type": "object", "metadata": {"displayName": "Kubernetes label selector to select monitoring of automounting API credentials", "description": "Label query to select Kubernetes resources for policy evaluation. An empty label selector matches all Kubernetes resources."}, "defaultValue": {}, "schema": {"description": "A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all resources.", "type": "object", "properties": {"matchLabels": {"description": "matchLabels is a map of {key,value} pairs.", "type": "object", "additionalProperties": {"type": "string"}, "minProperties": 1}, "matchExpressions": {"description": "matchExpressions is a list of values, a key, and an operator.", "type": "array", "items": {"type": "object", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values.", "type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty.", "type": "array", "items": {"type": "string"}}}, "required": ["key", "operator"], "additionalProperties": false}, "minItems": 1}}, "additionalProperties": false}}, "KubernetesClustersShouldNotGrantCAPSYSADMINSecurityCapabilitiesMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"], "metadata": {"displayName": "Kubernetes clusters should not grant CAPSYSADMIN security capabilities", "description": "To reduce the attack surface of your containers, restrict CAP_SYS_ADMIN Linux capabilities. For more information, see https://aka.ms/kubepolicydoc."}}, "KubernetesClustersShouldNotGrantCAPSYSADMINSecurityCapabilitiesMonitoringNamespaceExclusion": {"type": "Array", "defaultValue": ["kube-system", "gatekeeper-system", "azure-arc", "azuredefender", "mdc", "azure-extensions-usage-system"], "metadata": {"displayName": "Kubernetes namespaces to exclude from restricting CAP_SYS_ADMIN Linux capabilities", "description": "List of Kubernetes namespaces to exclude from evaluation to restrict CAP_SYS_ADMIN Linux capabilities. To list multiple namespaces, use semicolons (;) to separate them. System namespaces \"kube-system\", \"gatekeeper-system\" and \"azure-arc\" are always excluded by design."}}, "KubernetesClustersShouldNotGrantCAPSYSADMINSecurityCapabilitiesMonitoringLabelSelector": {"type": "object", "metadata": {"displayName": "Kubernetes label selector to select monitoring of CAP_SYS_ADMIN Linux capabilities", "description": "Label query to select Kubernetes resources for policy evaluation. An empty label selector matches all Kubernetes resources."}, "defaultValue": {}, "schema": {"description": "A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all resources.", "type": "object", "properties": {"matchLabels": {"description": "matchLabels is a map of {key,value} pairs.", "type": "object", "additionalProperties": {"type": "string"}, "minProperties": 1}, "matchExpressions": {"description": "matchExpressions is a list of values, a key, and an operator.", "type": "array", "items": {"type": "object", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values.", "type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty.", "type": "array", "items": {"type": "string"}}}, "required": ["key", "operator"], "additionalProperties": false}, "minItems": 1}}, "additionalProperties": false}}, "VtpmShouldBeEnabledOnSupportedVirtualMachinesMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "vTPM should be enabled on supported virtual machines", "description": "Enable virtual TPM device on supported virtual machines to facilitate Measured Boot and other OS security features that require a TPM. Once enabled, vTPM can be used to attest boot integrity. This assessment only applies to trusted launch enabled virtual machines."}}, "SecureBootShouldBeEnabledOnSupportedWindowsVirtualMachinesMonitoringEffect": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"], "metadata": {"displayName": "Secure Boot should be enabled on supported Windows virtual machines", "description": "Enable Secure Boot on supported Windows virtual machines to mitigate against malicious and unauthorized changes to the boot chain. Once enabled, only trusted bootloaders, kernel and kernel drivers will be allowed to run. This assessment only applies to trusted launch enabled Windows virtual machines."}}, "GuestAttestationExtensionShouldBeInstalledOnSupportedLinuxVirtualMachinesMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Guest Attestation extension should be installed on supported Linux virtual machines", "description": "Install Guest Attestation extension on supported Linux virtual machines to allow Microsoft Defender for Cloud to proactively attest and monitor the boot integrity. Once installed, boot integrity will be attested via Remote Attestation. This assessment only applies to trusted launch enabled Linux virtual machines."}}, "GuestAttestationExtensionShouldBeInstalledOnSupportedLinuxVirtualMachinesScaleSetsMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Guest Attestation extension should be installed on supported Linux virtual machines scale sets", "description": "Install Guest Attestation extension on supported Linux virtual machines scale sets to allow Microsoft Defender for Cloud to proactively attest and monitor the boot integrity. Once installed, boot integrity will be attested via Remote Attestation. This assessment only applies to trusted launch enabled Linux virtual machine scale sets."}}, "GuestAttestationExtensionShouldBeInstalledOnSupportedWindowsVirtualMachinesMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Guest Attestation extension should be installed on supported Windows virtual machines", "description": "Install Guest Attestation extension on supported virtual machines to allow Microsoft Defender for Cloud to proactively attest and monitor the boot integrity. Once installed, boot integrity will be attested via Remote Attestation. This assessment only applies to trusted launch enabled virtual machines."}}, "GuestAttestationExtensionShouldBeInstalledOnSupportedWindowsVirtualMachinesScaleSetsMonitoringEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Guest Attestation extension should be installed on supported Windows virtual machines scale sets", "description": "Install Guest Attestation extension on supported virtual machines scale sets to allow Microsoft Defender for Cloud to proactively attest and monitor the boot integrity. Once installed, boot integrity will be attested via Remote Attestation. This assessment only applies to trusted launch enabled virtual machine scale sets."}}, "LinuxVirtualMachineShouldUseSignedAndTrustedBootComponentEffect": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Linux virtual machines should use only signed and trusted boot components", "description": "All OS boot components (boot loader, kernel, kernel drivers) must be signed by trusted publishers. Defender for Cloud has identified untrusted OS boot components on one or more of your Linux machines. To protect your machines from potentially malicious components, add them to your allow list or remove the identified components."}}, "installEndpointProtectionMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Endpoint protection should be installed on your machines", "description": "To protect your machines from threats and vulnerabilities, install a supported endpoint protection solution.", "deprecated": true}}, "endpointProtectionHealthIssuesMonitoringEffect": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Endpoint protection health issues should be resolved on your machines", "description": "Resolve endpoint protection health issues on your virtual machines to protect them from latest threats and vulnerabilities. Microsoft Defender for Cloud supported endpoint protection solutions are documented here - https://docs.microsoft.com/azure/security-center/security-center-services?tabs=features-windows#supported-endpoint-protection-solutions. Endpoint protection assessment is documented here - https://docs.microsoft.com/azure/security-center/security-center-endpoint-protection.", "deprecated": true}}}, "policyDefinitions": [{"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0a075868-4c26-42ef-914c-5bc007359560", "definitionVersion": "2.*.*-preview", "policyDefinitionReferenceId": "certificatesValidityPeriodMonitoring", "parameters": {"effect": {"value": "[parameters('certificatesValidityPeriodMonitoringEffect')]"}, "maximumValidityInMonths": {"value": "[parameters('certificatesValidityPeriodInMonths')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-7"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/98728c90-32c7-4049-8429-847dc0f4fe37", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "secretsExpirationSet", "parameters": {"effect": {"value": "[parameters('secretsExpirationSetEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-6"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/152b15f7-8e1f-4c1f-ab71-8c010ba5dbc0", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "keysExpirationSet", "parameters": {"effect": {"value": "[parameters('keysExpirationSetEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-6"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ae89ebca-1c92-4898-ac2c-9f63decb045c", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "gcExtOnVMMonitoring", "parameters": {"effect": {"value": "[parameters('azurePolicyforWindowsMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/d26f7642-7545-4e18-9b75-8c9bbdee3a9a", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "gcExtOnVMWithNoSAMIMonitoring", "parameters": {"effect": {"value": "[parameters('gcExtOnVMWithNoSAMIMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_IM-3", "Azure_Security_Benchmark_v3.0_PV-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/bed48b13-6647-468e-aa2f-1af1d3f4dd40", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "windowsDefenderExploitGuardMonitoring", "parameters": {"effect": {"value": "[parameters('windowsDefenderExploitGuardMonitoringEffect')]"}, "IncludeArcMachines": {"value": "[parameters('windowsWebServersShouldBeConfiguredToUseSecureCommunicationProtocolsIncludeArcMachines')]"}, "NotAvailableMachineState": {"value": "Compliant"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-1", "Azure_Security_Benchmark_v3.0_LT-2", "Azure_Security_Benchmark_v3.0_ES-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/72650e9f-97bc-4b2a-ab5f-9781a9fcecbc", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "windowsGuestConfigBaselinesMonitoring", "parameters": {"effect": {"value": "[parameters('windowsGuestConfigBaselinesMonitoringEffect')]"}, "IncludeArcMachines": {"value": "[parameters('windowsWebServersShouldBeConfiguredToUseSecureCommunicationProtocolsIncludeArcMachines')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/fc9b3da7-8347-4380-8e70-0a0361d8dedd", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "linuxGuestConfigBaselinesMonitoring", "parameters": {"effect": {"value": "[parameters('linuxGuestConfigBaselinesMonitoringEffect')]"}, "IncludeArcMachines": {"value": "[parameters('windowsWebServersShouldBeConfiguredToUseSecureCommunicationProtocolsIncludeArcMachines')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/383856f8-de7f-44a2-81fc-e5135b5c2aa4", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "diagnosticsLogsInIoTHubMonitoring", "parameters": {"effect": {"value": "[parameters('diagnosticsLogsInIoTHubMonitoringEffect')]"}, "requiredRetentionDays": {"value": "[parameters('diagnosticsLogsInIoTHubRetentionDays')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/34c877ad-507e-4c82-993e-3452a6e0ad3c", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "disableUnrestrictedNetworkToStorageAccountMonitoring", "parameters": {"effect": {"value": "[parameters('disableUnrestrictedNetworkToStorageAccountMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a451c1ef-c6ca-483d-87ed-f49761e3ffb5", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "useRbacRulesMonitoring", "parameters": {"effect": {"value": "[parameters('useRbacRulesMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PA-7"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/f9be5368-9bf5-4b84-9e0a-7850da98bb46", "definitionVersion": "5.*.*", "policyDefinitionReferenceId": "diagnosticsLogsInStreamAnalyticsMonitoring", "parameters": {"effect": {"value": "[parameters('diagnosticsLogsInStreamAnalyticsMonitoringEffect')]"}, "requiredRetentionDays": {"value": "[parameters('diagnosticsLogsInStreamAnalyticsRetentionDays')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/404c3081-a854-4457-ae30-26a93ef643f9", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "secureTransferToStorageAccountMonitoring", "parameters": {"effect": {"value": "[parameters('secureTransferToStorageAccountMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1f314764-cb73-4fc9-b863-8eca98ac36e9", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "aadAuthenticationInSqlServerMonitoring", "parameters": {"effect": {"value": "[parameters('aadAuthenticationInSqlServerMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_IM-1"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/f8d36e2f-389b-4ee4-898d-21aeb69a0f45", "definitionVersion": "5.*.*", "policyDefinitionReferenceId": "diagnosticsLogsInServiceBusMonitoring", "parameters": {"effect": {"value": "[parameters('diagnosticsLogsInServiceBusMonitoringEffect')]"}, "requiredRetentionDays": {"value": "[parameters('diagnosticsLogsInServiceBusRetentionDays')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/617c02be-7f02-4efd-8836-3180d47b6c68", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "clusterProtectionLevelInServiceFabricMonitoring", "parameters": {"effect": {"value": "[parameters('clusterProtectionLevelInServiceFabricMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b54ed75b-3e1a-44ac-a333-05ba39b99ff0", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "aadAuthenticationInServiceFabricMonitoring", "parameters": {"effect": {"value": "[parameters('aadAuthenticationInServiceFabricMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_IM-1"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b4330a05-a843-4bc8-bf9a-cacce50c67f4", "definitionVersion": "5.*.*", "policyDefinitionReferenceId": "diagnosticsLogsInSearchServiceMonitoring", "parameters": {"effect": {"value": "[parameters('diagnosticsLogsInSearchServiceMonitoringEffect')]"}, "requiredRetentionDays": {"value": "[parameters('diagnosticsLogsInSearchServiceRetentionDays')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/22bee202-a82f-4305-9a2a-6d7f44d4dedb", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "diagnosticsLogsInRedisCacheMonitoring", "parameters": {"effect": {"value": "[parameters('diagnosticsLogsInRedisCacheMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/34f95f76-5386-4de7-b824-0d8478470c9d", "definitionVersion": "5.*.*", "policyDefinitionReferenceId": "diagnosticsLogsInLogicAppsMonitoring", "parameters": {"effect": {"value": "[parameters('diagnosticsLogsInLogicAppsMonitoringEffect')]"}, "requiredRetentionDays": {"value": "[parameters('diagnosticsLogsInLogicAppsRetentionDays')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/cf820ca0-f99e-4f3e-84fb-66e913812d21", "definitionVersion": "5.*.*", "policyDefinitionReferenceId": "diagnosticsLogsInKeyVaultMonitoring", "parameters": {"effect": {"value": "[parameters('diagnosticsLogsInKeyVaultMonitoringEffect')]"}, "requiredRetentionDays": {"value": "[parameters('diagnosticsLogsInKeyVaultRetentionDays')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-8", "Azure_Security_Benchmark_v3.0_LT-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/83a214f7-d01a-484b-91a9-ed54470c9a6a", "definitionVersion": "5.*.*", "policyDefinitionReferenceId": "diagnosticsLogsInEventHubMonitoring", "parameters": {"effect": {"value": "[parameters('diagnosticsLogsInEventHubMonitoringEffect')]"}, "requiredRetentionDays": {"value": "[parameters('diagnosticsLogsInEventHubRetentionDays')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/057ef27e-665e-4328-8ea3-04b3122bd9fb", "definitionVersion": "5.*.*", "policyDefinitionReferenceId": "diagnosticsLogsInDataLakeStoreMonitoring", "parameters": {"effect": {"value": "[parameters('diagnosticsLogsInDataLakeStoreMonitoringEffect')]"}, "requiredRetentionDays": {"value": "[parameters('diagnosticsLogsInDataLakeStoreRetentionDays')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c95c74d9-38fe-4f0d-af86-0c7d626a315c", "definitionVersion": "5.*.*", "policyDefinitionReferenceId": "diagnosticsLogsInDataLakeAnalyticsMonitoring", "parameters": {"effect": {"value": "[parameters('diagnosticsLogsInDataLakeAnalyticsMonitoringEffect')]"}, "requiredRetentionDays": {"value": "[parameters('diagnosticsLogsInDataLakeAnalyticsRetentionDays')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/37e0d2fe-28a5-43d6-a273-67d37d1f5606", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "classicStorageAccountsMonitoring", "parameters": {"effect": {"value": "[parameters('classicStorageAccountsMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_AM-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1d84d5fb-01f6-4d12-ba4f-4a26081d403d", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "classicComputeVMsMonitoring", "parameters": {"effect": {"value": "[parameters('classicComputeVMsMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_AM-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/428256e6-1fac-4f48-a757-df34c2b3336d", "definitionVersion": "5.*.*", "policyDefinitionReferenceId": "diagnosticsLogsInBatchAccountMonitoring", "parameters": {"effect": {"value": "[parameters('diagnosticsLogsInBatchAccountMonitoringEffect')]"}, "requiredRetentionDays": {"value": "[parameters('diagnosticsLogsInBatchAccountRetentionDays')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/3657f5a0-770e-44a3-b44e-9431ba1e9735", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "encryptionOfAutomationAccountMonitoring", "parameters": {"effect": {"value": "[parameters('encryptionOfAutomationAccountMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/17k78e20-9358-41c9-923c-fb736d382a12", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "sqlDbEncryptionMonitoring", "parameters": {"effect": {"value": "[parameters('sqlDbEncryptionMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a6fb4358-5bf4-4ad7-ba82-2cd2f41ce5e9", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "sqlServerAuditingMonitoring", "parameters": {"effect": {"value": "[parameters('sqlServerAuditingMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/f85bf3e0-d513-442e-89c3-1784ad63382b", "definitionVersion": "1.*.*-preview", "policyDefinitionReferenceId": "systemUpdatesV2Monitoring", "parameters": {"effect": {"value": "[parameters('systemUpdatesV2MonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-6"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/bd876905-5b84-4f73-ab2d-2e7a7c4568d9", "definitionVersion": "3.*.*-preview", "policyDefinitionReferenceId": "systemUpdatesAutoAssessmentMode", "parameters": {"effect": {"value": "[parameters('systemUpdatesAutoAssessmentModeEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-6"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b0f33259-77d7-4c9e-aac6-3aabcfae693c", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "jitNetworkAccessMonitoring", "parameters": {"effect": {"value": "[parameters('jitNetworkAccessMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-3", "Azure_Security_Benchmark_v3.0_PA-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e71308d3-144b-4262-b144-efdc3cc90517", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "networkSecurityGroupsOnSubnetsMonitoring", "parameters": {"effect": {"value": "[parameters('networkSecurityGroupsOnSubnetsMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-1"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/f6de0be7-9a8a-4b8a-b349-43cf02d22f7c", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "networkSecurityGroupsOnVirtualMachinesMonitoring", "parameters": {"effect": {"value": "[parameters('networkSecurityGroupsOnVirtualMachinesMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-1"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/bb91dfba-c30d-4263-9add-9c2384e659a6", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "networkSecurityGroupsOnInternalVirtualMachinesMonitoring", "parameters": {"effect": {"value": "[parameters('networkSecurityGroupsOnInternalVirtualMachinesMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-1"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/501541f7-f7e7-4cd6-868c-4190fdad3ac9", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "serverVulnerabilityAssessment", "parameters": {"effect": {"value": "[parameters('serverVulnerabilityAssessmentEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/9daedab3-fb2d-461e-b861-71790eead4f6", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "nextGenerationFirewallMonitoring", "parameters": {"effect": {"value": "[parameters('nextGenerationFirewallMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-1"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/feedbf84-6b99-488c-acc2-71c829aa5ffc", "definitionVersion": "4.*.*", "policyDefinitionReferenceId": "sqlDbVulnerabilityAssesmentMonitoring", "parameters": {"effect": {"value": "[parameters('sqlDbVulnerabilityAssesmentMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-6"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6ba6d016-e7c3-4842-b8f2-4992ebc0d72d", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "serverSqlDbVulnerabilityAssesmentMonitoring", "parameters": {"effect": {"value": "[parameters('serverSqlDbVulnerabilityAssesmentMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-6"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/4f11b553-d42e-4e3a-89be-32ca364cad4c", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "identityDesignateLessThanOwnersMonitoring", "parameters": {"effect": {"value": "[parameters('identityDesignateLessThanOwnersMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PA-1"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/09024ccc-0c5f-475e-9457-b7c0d9ed487b", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "identityDesignateMoreThanOneOwnerMonitoring", "parameters": {"effect": {"value": "[parameters('identityDesignateMoreThanOneOwnerMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PA-1"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0cfea604-3201-4e14-88fc-fae4c427a6c5", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "identityRemoveDeprecatedAccountWithOwnerPermissionsMonitoringNew", "parameters": {"effect": {"value": "[parameters('identityRemoveDeprecatedAccountWithOwnerPermissionsMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PA-1", "Azure_Security_Benchmark_v3.0_PA-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/8d7e1fde-fe26-4b5f-8108-f8e432cbc2be", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "identityRemoveDeprecatedAccountMonitoringNew", "parameters": {"effect": {"value": "[parameters('identityRemoveDeprecatedAccountMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PA-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/339353f6-2387-4a45-abe4-7f529d121046", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "identityRemoveExternalAccountWithOwnerPermissionsMonitoringNew", "parameters": {"effect": {"value": "[parameters('identityRemoveExternalAccountWithOwnerPermissionsMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PA-1", "Azure_Security_Benchmark_v3.0_PA-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/94e1c2ac-cbbe-4cac-a2b5-389c812dee87", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "identityRemoveExternalAccountWithWritePermissionsMonitoringNew", "parameters": {"effect": {"value": "[parameters('identityRemoveExternalAccountWithWritePermissionsMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PA-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e9ac8f8e-ce22-4355-8f04-99b911d6be52", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "identityRemoveExternalAccountWithReadPermissionsMonitoringNew", "parameters": {"effect": {"value": "[parameters('identityRemoveExternalAccountWithReadPermissionsMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PA-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0e60b895-3786-45da-8377-9c6b4b6ac5f9", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "functionAppDisableRemoteDebuggingMonitoring", "parameters": {"effect": {"value": "[parameters('functionAppDisableRemoteDebuggingMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/cb510bfd-1cba-4d9f-a230-cb0976f4bb71", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "webAppDisableRemoteDebuggingMonitoring", "parameters": {"effect": {"value": "[parameters('webAppDisableRemoteDebuggingMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6d555dd1-86f2-4f1c-8ed7-5abae7c6cbab", "definitionVersion": "5.*.*", "policyDefinitionReferenceId": "functionAppEnforceHttpsMonitoring", "parameters": {"effect": {"value": "[parameters('functionAppEnforceHttpsMonitoringEffectV2')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a4af4a39-4135-47fb-b175-47fbdf85311d", "definitionVersion": "4.*.*", "policyDefinitionReferenceId": "webAppEnforceHttpsMonitoring", "parameters": {"effect": {"value": "[parameters('webAppEnforceHttpsMonitoringEffectV2')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0820b7b9-23aa-4725-a1ce-ae4558f718e5", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "functionAppRestrictCORSAccessMonitoring", "parameters": {"effect": {"value": "[parameters('functionAppRestrictCORSAccessMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/5744710e-cc2f-4ee8-8809-3b11e89f4bc9", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "webAppRestrictCORSAccessMonitoring", "parameters": {"effect": {"value": "[parameters('webAppRestrictCORSAccessMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a7aca53f-2ed4-4466-a25e-0b45ade68efd", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "vnetEnableDDoSProtectionMonitoring", "parameters": {"effect": {"value": "[parameters('vnetEnableDDoSProtectionMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/abfb4388-5bf4-4ad7-ba82-2cd2f41ceae9", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "sqlServerAdvancedDataSecurityMonitoring", "parameters": {"effect": {"value": "[parameters('sqlServerAdvancedDataSecurityMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-1", "Azure_Security_Benchmark_v3.0_LT-2", "Azure_Security_Benchmark_v3.0_IR-3", "Azure_Security_Benchmark_v3.0_IR-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/d31e5c31-63b2-4f12-887b-e49456834fa1", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "synapseWorkspaceAdvancedDataSecurityMonitoring", "groupNames": ["Azure_Security_Benchmark_v3.0_LT-1", "Azure_Security_Benchmark_v3.0_LT-2", "Azure_Security_Benchmark_v3.0_IR-3", "Azure_Security_Benchmark_v3.0_IR-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/d38668f5-d155-42c7-ab3d-9b57b50f8fbf", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "postgreSqlFlexibleServersAdvancedDataSecurityMonitoring", "groupNames": ["Azure_Security_Benchmark_v3.0_LT-1", "Azure_Security_Benchmark_v3.0_LT-2", "Azure_Security_Benchmark_v3.0_IR-3", "Azure_Security_Benchmark_v3.0_IR-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/3bc8a0d5-38e0-4a3d-a657-2cb64468fc34", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "mySqlFlexibleServersAdvancedDataSecurityMonitoring", "groupNames": ["Azure_Security_Benchmark_v3.0_LT-1", "Azure_Security_Benchmark_v3.0_LT-2", "Azure_Security_Benchmark_v3.0_IR-3", "Azure_Security_Benchmark_v3.0_IR-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/938c4981-c2c9-4168-9cd6-972b8675f906", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "arcEnabledSqlServerDefenderStatus", "parameters": {"effect": {"value": "[parameters('arcEnabledSqlServerDefenderStatusEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-1", "Azure_Security_Benchmark_v3.0_LT-2", "Azure_Security_Benchmark_v3.0_IR-3", "Azure_Security_Benchmark_v3.0_IR-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c6283572-73bb-4deb-bf2c-7a2b8f7462cb", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "SQLServerTargetedAutoprovisioningShouldBeEnabledForSQLServersOnMachinesPlan", "groupNames": ["Azure_Security_Benchmark_v3.0_LT-1", "Azure_Security_Benchmark_v3.0_LT-2", "Azure_Security_Benchmark_v3.0_IR-3", "Azure_Security_Benchmark_v3.0_IR-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/7926a6d1-b268-4586-8197-e8ae90c877d7", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "enableDefenderForApis", "groupNames": ["Azure_Security_Benchmark_v3.0_DP-1", "Azure_Security_Benchmark_v3.0_DP-2", "Azure_Security_Benchmark_v3.0_LT-1", "Azure_Security_Benchmark_v3.0_IR-3", "Azure_Security_Benchmark_v3.0_IR-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/abfb7388-5bf4-4ad7-ba99-2cd2f41cebb9", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "sqlManagedInstanceAdvancedDataSecurityMonitoring", "parameters": {"effect": {"value": "[parameters('sqlManagedInstanceAdvancedDataSecurityMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-2", "Azure_Security_Benchmark_v3.0_LT-1", "Azure_Security_Benchmark_v3.0_LT-2", "Azure_Security_Benchmark_v3.0_IR-3", "Azure_Security_Benchmark_v3.0_IR-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ac4a19c2-fa67-49b4-8ae5-0b2e78c49457", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "kubernetesServiceRbacEnabledMonitoring", "parameters": {"effect": {"value": "[parameters('kubernetesServiceRbacEnabledMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PA-7"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0e246bcf-5f6f-4f87-bc6f-775d4712c7ea", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "kubernetesServiceAuthorizedIPRangesEnabledMonitoring", "parameters": {"effect": {"value": "[parameters('kubernetesServiceAuthorizedIPRangesEnabledMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ef2a8f2a-b3d9-49cd-a8a8-9a3aaaf647d9", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "vulnerabilityAssessmentOnServerMonitoring", "parameters": {"effect": {"value": "[parameters('vulnerabilityAssessmentOnServerMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1b7aa243-30e4-4c9e-bca8-d0d3022b634a", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "vulnerabilityAssessmentOnManagedInstanceMonitoring", "parameters": {"effect": {"value": "[parameters('vulnerabilityAssessmentOnManagedInstanceMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/22730e10-96f6-4aac-ad84-9383d35b5917", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "restrictAccessToManagementPortsMonitoring", "parameters": {"effect": {"value": "[parameters('restrictAccessToManagementPortsMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/bd352bd5-2853-4985-bf0d-73806b4a5744", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "disableIPForwardingMonitoring", "parameters": {"effect": {"value": "[parameters('disableIPForwardingMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0a370ff3-6cab-4e85-8995-295fd854c5b8", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "ensureServerTDEIsEncryptedWithYourOwnKeyMonitoring", "parameters": {"effect": {"value": "[parameters('ensureServerTDEIsEncryptedWithYourOwnKeyWithDenyMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ac01ad65-10e5-46df-bdd9-6b0cad13e1d2", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "ensureManagedInstanceTDEIsEncryptedWithYourOwnKeyMonitoring", "parameters": {"effect": {"value": "[parameters('ensureManagedInstanceTDEIsEncryptedWithYourOwnKeyWithDenyMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/2f2ee1de-44aa-4762-b6bd-0893fc3f306d", "definitionVersion": "1.*.*-preview", "policyDefinitionReferenceId": "ASCDependencyAgentAuditWindowsEffect", "parameters": {"effect": {"value": "[parameters('ASCDependencyAgentAuditWindowsEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/04c4380f-3fae-46e8-96c9-30193528f602", "definitionVersion": "1.*.*-preview", "policyDefinitionReferenceId": "ASCDependencyAgentAuditLinuxEffect", "parameters": {"effect": {"value": "[parameters('ASCDependencyAgentAuditLinuxEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/fc5e4038-4584-4632-8c85-c0448d374b2c", "definitionVersion": "3.*.*-preview", "policyDefinitionReferenceId": "AzureFirewallEffect", "parameters": {"effect": {"value": "[parameters('AzureFirewallEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/d69b1763-b96d-40b8-a2d9-ca31e9fd0d3e", "definitionVersion": "1.*.*-preview", "policyDefinitionReferenceId": "ArcWindowsMonitoring", "parameters": {"effect": {"value": "[parameters('ArcWindowsMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/842c54e8-c2f9-4d79-ae8d-38d8b8019373", "definitionVersion": "1.*.*-preview", "policyDefinitionReferenceId": "ArcLinuxMonitoring", "parameters": {"effect": {"value": "[parameters('ArcLinuxMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0e6763cc-5078-4e64-889d-ff4d9a839047", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "keyVaultsAdvancedDataSecurityMonitoringEffect", "parameters": {"effect": {"value": "[parameters('keyVaultsAdvancedDataSecurityMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-8", "Azure_Security_Benchmark_v3.0_LT-1", "Azure_Security_Benchmark_v3.0_LT-2", "Azure_Security_Benchmark_v3.0_IR-3", "Azure_Security_Benchmark_v3.0_IR-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/7fe3b40f-802b-4cdd-8bd4-fd799c948cc2", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "sqlServersAdvancedDataSecurityMonitoringEffect", "parameters": {"effect": {"value": "[parameters('sqlServersAdvancedDataSecurityMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-2", "Azure_Security_Benchmark_v3.0_LT-1", "Azure_Security_Benchmark_v3.0_LT-2", "Azure_Security_Benchmark_v3.0_IR-3", "Azure_Security_Benchmark_v3.0_IR-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6581d072-105e-4418-827f-bd446d56421b", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "sqlServersVirtualMachinesAdvancedDataSecurityMonitoringEffect", "parameters": {"effect": {"value": "[parameters('sqlServersVirtualMachinesAdvancedDataSecurityMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-2", "Azure_Security_Benchmark_v3.0_LT-1", "Azure_Security_Benchmark_v3.0_LT-2", "Azure_Security_Benchmark_v3.0_IR-3", "Azure_Security_Benchmark_v3.0_IR-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/2913021d-f2fd-4f3d-b958-22354e2bdbcb", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "appServicesAdvancedThreatProtectionMonitoringEffect", "parameters": {"effect": {"value": "[parameters('appServicesAdvancedThreatProtectionMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-1", "Azure_Security_Benchmark_v3.0_LT-2", "Azure_Security_Benchmark_v3.0_IR-3", "Azure_Security_Benchmark_v3.0_IR-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1c988dd6-ade4-430f-a608-2a3e5b0a6d38", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "containersAdvancedThreatProtectionMonitoringEffect", "parameters": {"effect": {"value": "[parameters('containersAdvancedThreatProtectionMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-1", "Azure_Security_Benchmark_v3.0_LT-2", "Azure_Security_Benchmark_v3.0_IR-3", "Azure_Security_Benchmark_v3.0_IR-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/4da35fc9-c9e7-4960-aec9-797fe7d9051d", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "virtualMachinesAdvancedThreatProtectionMonitoringEffect", "parameters": {"effect": {"value": "[parameters('virtualMachinesAdvancedThreatProtectionMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-1", "Azure_Security_Benchmark_v3.0_LT-2", "Azure_Security_Benchmark_v3.0_ES-1", "Azure_Security_Benchmark_v3.0_IR-3", "Azure_Security_Benchmark_v3.0_IR-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0a15ec92-a229-4763-bb14-0ea34a568f8d", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "azurePolicyAddonStatus", "parameters": {"effect": {"value": "[parameters('azurePolicyAddonStatusEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6b2122c1-8120-4ff5-801b-17625a355590", "definitionVersion": "1.*.*-preview", "policyDefinitionReferenceId": "arcEnabledKubernetesClustersShouldHaveAzurePolicyExtensionInstalled", "parameters": {"effect": {"value": "[parameters('arcEnabledKubernetesClustersShouldHaveAzurePolicyExtensionInstalledEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/febd0533-8e55-448f-b837-bd0e06f16469", "definitionVersion": "9.*.*", "policyDefinitionReferenceId": "ensureAllowedContainerImagesInKubernetesCluster", "parameters": {"effect": {"value": "[parameters('allowedContainerImagesInKubernetesClusterEffect')]"}, "allowedContainerImagesRegex": {"value": "[parameters('allowedContainerImagesInKubernetesClusterRegex')]"}, "excludedNamespaces": {"value": "[parameters('allowedContainerImagesNamespaceExclusion')]"}, "labelSelector": {"value": "[parameters('allowedContainerImagesLabelSelector')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/95edb821-ddaf-4404-9732-666045e056b4", "definitionVersion": "9.*.*", "policyDefinitionReferenceId": "privilegedContainersShouldBeAvoided", "parameters": {"effect": {"value": "[parameters('privilegedContainersShouldBeAvoidedEffect')]"}, "excludedNamespaces": {"value": "[parameters('privilegedContainerNamespaceExclusion')]"}, "labelSelector": {"value": "[parameters('privilegedContainerLabelSelector')]"}, "excludedImages": {"value": "[parameters('excludedImagesInKubernetesCluster')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/233a2a17-77ca-4fb1-9b6b-69223d272a44", "definitionVersion": "8.*.*", "policyDefinitionReferenceId": "allowedServicePortsInKubernetesCluster", "parameters": {"effect": {"value": "[parameters('allowedServicePortsInKubernetesClusterEffect')]"}, "allowedServicePortsList": {"value": "[parameters('allowedservicePortsInKubernetesClusterPorts')]"}, "excludedNamespaces": {"value": "[parameters('allowedServicePortsInKubernetesClusterNamespaceExclusion')]"}, "labelSelector": {"value": "[parameters('allowedServicePortsInKubernetesClusterLabelSelector')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e345eecc-fa47-480f-9e88-67dcc122b164", "definitionVersion": "9.*.*", "policyDefinitionReferenceId": "memoryAndCPULimitsInKubernetesCluster", "parameters": {"effect": {"value": "[parameters('memoryAndCPULimitsInKubernetesClusterEffect')]"}, "cpuLimit": {"value": "[parameters('CPUInKubernetesClusterLimit')]"}, "memoryLimit": {"value": "[parameters('memoryInKubernetesClusterLimit')]"}, "excludedNamespaces": {"value": "[parameters('memoryAndCPULimitsInKubernetesClusterNamespaceExclusion')]"}, "labelSelector": {"value": "[parameters('memoryAndCPULimitsInKubernetesClusterLabelSelector')]"}, "excludedImages": {"value": "[parameters('excludedImagesInKubernetesCluster')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/f06ddb64-5fa3-4b77-b166-acb36f7f6042", "definitionVersion": "6.*.*", "policyDefinitionReferenceId": "MustRunAsNonRoot", "parameters": {"effect": {"value": "[parameters('MustRunAsNonRootNamespaceEffect')]"}, "runAsUserRule": {"value": "MustRunAsNonRoot"}, "runAsUserRanges": {"value": {"ranges": []}}, "runAsGroupRule": {"value": "[parameters('runAsGroupRule-f06ddb64-5fa3-4b77-b166-acb36f7f6042')]"}, "runAsGroupRanges": {"value": "[parameters('runAsGroupRanges-f06ddb64-5fa3-4b77-b166-acb36f7f6042')]"}, "supplementalGroupsRule": {"value": "[parameters('supplementalGroupsRule-f06ddb64-5fa3-4b77-b166-acb36f7f6042')]"}, "supplementalGroupsRanges": {"value": "[parameters('supplementalGroupsRanges-f06ddb64-5fa3-4b77-b166-acb36f7f6042')]"}, "fsGroupRule": {"value": "[parameters('fsGroupRule-f06ddb64-5fa3-4b77-b166-acb36f7f6042')]"}, "fsGroupRanges": {"value": "[parameters('fsGroupRanges-f06ddb64-5fa3-4b77-b166-acb36f7f6042')]"}, "excludedNamespaces": {"value": "[parameters('MustRunAsNonRootNamespaceExclusion')]"}, "labelSelector": {"value": "[parameters('MustRunAsNonRootLabelSelector')]"}, "excludedImages": {"value": "[parameters('excludedImagesInKubernetesCluster')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/8dfab9c4-fe7b-49ad-85e4-1e9be085358f", "definitionVersion": "6.*.*-preview", "policyDefinitionReferenceId": "arcEnabledKubernetesClustersShouldHaveAzureDefendersExtensionInstalled", "parameters": {"effect": {"value": "[parameters('arcEnabledKubernetesClustersShouldHaveAzureDefendersExtensionInstalled')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-1", "Azure_Security_Benchmark_v3.0_LT-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a1840de2-8088-4ea8-b153-b4c723e9cb01", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "azureKubernetesServiceClustersShouldHaveSecurityProfileEnabled", "parameters": {"effect": {"value": "[parameters('azureKubernetesServiceClustersShouldHaveSecurityProfileEnabled')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-1", "Azure_Security_Benchmark_v3.0_LT-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/090c7b07-b4ed-4561-ad20-e9075f3ccaff", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "azureContainerRegistryVulnerabilityAssessment", "parameters": {"effect": {"value": "[parameters('azureContainerRegistryVulnerabilityAssessmentEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-6", "Azure_Security_Benchmark_v3.0_DS-6"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/17f4b1cc-c55c-4d94-b1f9-2978f6ac2957", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "kubernetesRunningImagesVulnerabilityMDVMAssessment", "parameters": {"effect": {"value": "[parameters('kubernetesRunningImagesVulnerabilityMDVMAssessmentEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-6", "Azure_Security_Benchmark_v3.0_DS-6"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1c6e92c9-99f0-4e55-9cf2-0c234dc48f99", "definitionVersion": "7.*.*", "policyDefinitionReferenceId": "NoPrivilegeEscalationInKubernetesCluster", "parameters": {"effect": {"value": "[parameters('NoPrivilegeEscalationInKubernetesClusterEffect')]"}, "excludedNamespaces": {"value": "[parameters('NoPrivilegeEscalationInKubernetesClusterNamespaceExclusion')]"}, "labelSelector": {"value": "[parameters('NoPrivilegeEscalationInKubernetesClusterLabelSelector')]"}, "excludedImages": {"value": "[parameters('excludedImagesInKubernetesCluster')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/47a1ee2f-2a2a-4576-bf2a-e0e36709c2b8", "definitionVersion": "5.*.*", "policyDefinitionReferenceId": "NoSharingSensitiveHostNamespacesInKubernetes", "parameters": {"effect": {"value": "[parameters('NoSharingSensitiveHostNamespacesInKubernetesEffect')]"}, "excludedNamespaces": {"value": "[parameters('NoSharingSensitiveHostNamespacesInKubernetesNamespaceExclusion')]"}, "labelSelector": {"value": "[parameters('NoSharingSensitiveHostNamespacesInKubernetesLabelSelector')]"}, "excludedImages": {"value": "[parameters('excludedImagesInKubernetesCluster')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/df49d893-a74c-421d-bc95-c663042e5b80", "definitionVersion": "6.*.*", "policyDefinitionReferenceId": "ReadOnlyRootFileSystemInKubernetesCluster", "parameters": {"effect": {"value": "[parameters('ReadOnlyRootFileSystemInKubernetesClusterEffect')]"}, "excludedNamespaces": {"value": "[parameters('ReadOnlyRootFileSystemInKubernetesClusterNamespaceExclusion')]"}, "labelSelector": {"value": "[parameters('ReadOnlyRootFileSystemInKubernetesClusterLabelSelector')]"}, "excludedImages": {"value": "[parameters('excludedImagesInKubernetesCluster')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c26596ff-4d70-4e6a-9a30-c2506bd2f80c", "definitionVersion": "6.*.*", "policyDefinitionReferenceId": "AllowedCapabilitiesInKubernetesCluster", "parameters": {"effect": {"value": "[parameters('AllowedCapabilitiesInKubernetesClusterEffect')]"}, "excludedNamespaces": {"value": "[parameters('AllowedCapabilitiesInKubernetesClusterNamespaceExclusion')]"}, "labelSelector": {"value": "[parameters('AllowedCapabilitiesInKubernetesClusterLabelSelector')]"}, "allowedCapabilities": {"value": "[parameters('AllowedCapabilitiesInKubernetesClusterList')]"}, "requiredDropCapabilities": {"value": "[parameters('DropCapabilitiesInKubernetesClusterList')]"}, "excludedImages": {"value": "[parameters('excludedImagesInKubernetesCluster')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/511f5417-5d12-434d-ab2e-816901e72a5e", "definitionVersion": "6.*.*", "policyDefinitionReferenceId": "AllowedAppArmorProfilesInKubernetesCluster", "parameters": {"effect": {"value": "[parameters('AllowedAppArmorProfilesInKubernetesClusterEffect')]"}, "excludedNamespaces": {"value": "[parameters('AllowedAppArmorProfilesInKubernetesClusterNamespaceExclusion')]"}, "labelSelector": {"value": "[parameters('AllowedAppArmorProfilesInKubernetesClusterLabelSelector')]"}, "allowedProfiles": {"value": "[parameters('AllowedAppArmorProfilesInKubernetesClusterList')]"}, "excludedImages": {"value": "[parameters('excludedImagesInKubernetesCluster')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/82985f06-dc18-4a48-bc1c-b9f4f0098cfe", "definitionVersion": "6.*.*", "policyDefinitionReferenceId": "AllowedHostNetworkingAndPortsInKubernetesCluster", "parameters": {"effect": {"value": "[parameters('AllowedHostNetworkingAndPortsInKubernetesClusterEffect')]"}, "excludedNamespaces": {"value": "[parameters('AllowedHostNetworkingAndPortsInKubernetesClusterNamespaceExclusion')]"}, "labelSelector": {"value": "[parameters('AllowedHostNetworkingAndPortsInKubernetesClusterLabelSelector')]"}, "allowHostNetwork": {"value": "[parameters('AllowHostNetworkingInKubernetesCluster')]"}, "minPort": {"value": "[parameters('AllowedHostMinPortInKubernetesCluster')]"}, "maxPort": {"value": "[parameters('AllowedHostMaxPortInKubernetesCluster')]"}, "excludedImages": {"value": "[parameters('excludedImagesInKubernetesCluster')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/098fc59e-46c7-4d99-9b16-64990e543d75", "definitionVersion": "6.*.*", "policyDefinitionReferenceId": "AllowedHostPathVolumesInKubernetesCluster", "parameters": {"effect": {"value": "[parameters('AllowedHostPathVolumesInKubernetesClusterEffect')]"}, "excludedNamespaces": {"value": "[parameters('AllowedHostPathVolumesInKubernetesClusterNamespaceExclusion')]"}, "labelSelector": {"value": "[parameters('AllowedHostPathVolumesInKubernetesClusterLabelSelector')]"}, "allowedHostPaths": {"value": "[parameters('AllowedHostPathVolumesInKubernetesClusterList')]"}, "excludedImages": {"value": "[parameters('excludedImagesInKubernetesCluster')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/4fa4b6c0-31ca-4c0d-b10d-24b96f62a751", "definitionVersion": "3.*.*-preview", "policyDefinitionReferenceId": "StorageDisallowPublicAccess", "parameters": {"effect": {"value": "[parameters('disallowPublicBlobAccessEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/013e242c-8828-4970-87b3-ab247555486d", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "azureBackupShouldBeEnabledForVirtualMachinesMonitoringEffect", "parameters": {"effect": {"value": "[parameters('azureBackupShouldBeEnabledForVirtualMachinesMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_BR-1", "Azure_Security_Benchmark_v3.0_BR-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0da106f2-4ca3-48e8-bc85-c638fe6aea8f", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "managedIdentityShouldBeUsedInYourFunctionAppMonitoringEffect", "parameters": {"effect": {"value": "[parameters('managedIdentityShouldBeUsedInYourFunctionAppMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_IM-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0ec47710-77ff-4a3d-9181-6aa50af424d0", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "georedundantBackupShouldBeEnabledForAzureDatabaseForMariadbMonitoringEffect", "parameters": {"effect": {"value": "[parameters('georedundantBackupShouldBeEnabledForAzureDatabaseForMariadbMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_BR-1", "Azure_Security_Benchmark_v3.0_BR-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/2b9ad585-36bc-4615-b300-fd4435808332", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "managedIdentityShouldBeUsedInYourWebAppMonitoringEffect", "parameters": {"effect": {"value": "[parameters('managedIdentityShouldBeUsedInYourWebAppMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_IM-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/48af4db5-9b8b-401c-8e74-076be876a430", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "georedundantBackupShouldBeEnabledForAzureDatabaseForPostgresqlMonitoringEffect", "parameters": {"effect": {"value": "[parameters('georedundantBackupShouldBeEnabledForAzureDatabaseForPostgresqlMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_BR-1", "Azure_Security_Benchmark_v3.0_BR-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/19dd1db6-f442-49cf-a838-b0786b4401ef", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "ensureWebAppHasIncomingClientCertificatesSetToOnMonitoringEffect", "parameters": {}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/82339799-d096-41ae-8538-b108becf0970", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "georedundantBackupShouldBeEnabledForAzureDatabaseForMysqlMonitoringEffect", "parameters": {"effect": {"value": "[parameters('georedundantBackupShouldBeEnabledForAzureDatabaseForMysqlMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_BR-1", "Azure_Security_Benchmark_v3.0_BR-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/91a78b24-f231-4a8a-8da9-02c35b2b6510", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "diagnosticLogsInAppServicesShouldBeEnabledMonitoringEffect", "parameters": {"effect": {"value": "[parameters('diagnosticLogsInAppServicesShouldBeEnabledMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/d158790f-bfb0-486c-8631-2dc6b4e8e6af", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "enforceSSLConnectionShouldBeEnabledForPostgresqlDatabaseServersMonitoringEffect", "parameters": {"effect": {"value": "[parameters('enforceSSLConnectionShouldBeEnabledForPostgresqlDatabaseServersMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e802a67a-daf5-4436-9ea6-f6d821dd0c5d", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "enforceSSLConnectionShouldBeEnabledForMysqlDatabaseServersMonitoringEffect", "parameters": {"effect": {"value": "[parameters('enforceSSLConnectionShouldBeEnabledForMysqlDatabaseServersMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/f0e6e85b-9b9f-4a4b-b67b-f730d42f1b0b", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "latestTLSVersionShouldBeUsedInYourWebAppMonitoringEffect", "parameters": {"effect": {"value": "[parameters('latestTLSVersionShouldBeUsedInYourWebAppMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-8", "Azure_Security_Benchmark_v3.0_DP-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/f9d614c5-c173-4d56-95a7-b4437057d193", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "latestTLSVersionShouldBeUsedInYourFunctionAppMonitoringEffect", "parameters": {"effect": {"value": "[parameters('latestTLSVersionShouldBeUsedInYourFunctionAppMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-8", "Azure_Security_Benchmark_v3.0_DP-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0564d078-92f5-4f97-8398-b9f58a51f70b", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "privateEndpointShouldBeEnabledForPostgresqlServersMonitoringEffect", "parameters": {"effect": {"value": "[parameters('privateEndpointShouldBeEnabledForPostgresqlServersMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0a1302fb-a631-4106-9753-f3d494733990", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "privateEndpointShouldBeEnabledForMariadbServersMonitoringEffect", "parameters": {"effect": {"value": "[parameters('privateEndpointShouldBeEnabledForMariadbServersMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/7595c971-233d-4bcf-bd18-596129188c49", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "privateEndpointShouldBeEnabledForMysqlServersMonitoringEffect", "parameters": {"effect": {"value": "[parameters('privateEndpointShouldBeEnabledForMysqlServersMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/89099bee-89e0-4b26-a5f4-165451757743", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "sQLServersShouldBeConfiguredWithAuditingRetentionDaysGreaterThan90DaysMonitoringEffect", "parameters": {"effect": {"value": "[parameters('sQLServersShouldBeConfiguredWithAuditingRetentionDaysGreaterThan90DaysMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-6"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/399b2637-a50f-4f95-96f8-3a145476eb15", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "fTPSOnlyShouldBeRequiredInYourFunctionAppMonitoringEffect", "parameters": {"effect": {"value": "[parameters('fTPSOnlyShouldBeRequiredInYourFunctionAppMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/4d24b6d4-5e53-4a4f-a7f4-618fa573ee4b", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "fTPSShouldBeRequiredInYourWebAppMonitoringEffect", "parameters": {"effect": {"value": "[parameters('fTPSShouldBeRequiredInYourWebAppMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ab6a902f-9493-453b-928d-62c30b11b5a6", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "functionAppsShouldHaveClientCertificatesEnabledMonitoring", "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/67121cc7-ff39-4ab8-b7e3-95b84dab487d", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "cognitiveServicesAccountsShouldEnableDataEncryptionWithACustomerManagedKeyMonitoringEffect", "parameters": {"effect": {"value": "[parameters('cognitiveServicesAccountsShouldEnableDataEncryptionWithACustomerManagedKeyMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1f905d99-2ab7-462c-a6b0-f709acca6c8f", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "azureCosmosDbAccountsShouldUseCustomerManagedKeysToEncryptDataAtRestMonitoringEffect", "parameters": {"effect": {"value": "[parameters('azureCosmosDbAccountsShouldUseCustomerManagedKeysToEncryptDataAtRestMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/5450f5bd-9c72-4390-a9c4-a7aba4edfdd2", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "azureCosmosDbAccountsShouldHaveLocalAuthenticationMethodsDisabledMonitoringEffect", "parameters": {"effect": {"value": "[parameters('azureCosmosDbAccountsShouldHaveLocalAuthenticationMethodsDisabledMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_IM-1"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0b60c0b2-2dc2-4e1c-b5c9-abbed971de53", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "keyVaultsShouldHavePurgeProtectionEnabledMonitoringEffect", "parameters": {"effect": {"value": "[parameters('keyVaultsShouldHavePurgeProtectionEnabledMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-8"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1e66c121-a66a-4b1f-9b83-0fd99bf0fc2d", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "keyVaultsShouldHaveSoftDeleteEnabledMonitoringEffect", "parameters": {"effect": {"value": "[parameters('keyVaultsShouldHaveSoftDeleteEnabledMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-8"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/7803067c-7d34-46e3-8c79-0ca68fc4036d", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "azureCacheForRedisShouldUsePrivateEndpointMonitoringEffect", "parameters": {"effect": {"value": "[parameters('azureCacheForRedisShouldUsePrivateEndpointMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6fac406b-40ca-413b-bf8e-0bf964659c25", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "storageAccountsShouldUseCustomerManagedKeyForEncryptionMonitoringEffect", "parameters": {"effect": {"value": "[parameters('storageAccountsShouldUseCustomerManagedKeyForEncryptionMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/2a1a9cdf-e04d-429a-8416-3bfb72a1b26f", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "storageAccountsShouldRestrictNetworkAccessUsingVirtualNetworkRulesMonitoringEffect", "parameters": {"effect": {"value": "[parameters('storageAccountsShouldRestrictNetworkAccessUsingVirtualNetworkRulesMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/5b9159ae-1701-4a6f-9a7a-aa9c8ddd0580", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "containerRegistriesShouldBeEncryptedWithACustomerManagedKeyMonitoringEffect", "parameters": {"effect": {"value": "[parameters('containerRegistriesShouldBeEncryptedWithACustomerManagedKeyMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/d0793b48-0edc-4296-a390-4c75d1bdfd71", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "containerRegistriesShouldNotAllowUnrestrictedNetworkAccessMonitoringEffect", "parameters": {"effect": {"value": "[parameters('containerRegistriesShouldNotAllowUnrestrictedNetworkAccessMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e8eef0a8-67cf-4eb4-9386-14b0e78733d4", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "containerRegistriesShouldUsePrivateLinkMonitoringEffect", "parameters": {"effect": {"value": "[parameters('containerRegistriesShouldUsePrivateLinkMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ca610c1d-041c-4332-9d88-7ed3094967c7", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "appConfigurationShouldUsePrivateLinkMonitoringEffect", "parameters": {"effect": {"value": "[parameters('appConfigurationShouldUsePrivateLinkMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/9830b652-8523-49cc-b1b3-e17dce1127ca", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "azureEventGridDomainsShouldUsePrivateLinkMonitoringEffect", "parameters": {"effect": {"value": "[parameters('azureEventGridDomainsShouldUsePrivateLinkMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/4b90e17e-8448-49db-875e-bd83fb6f804f", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "azureEventGridTopicsShouldUsePrivateLinkMonitoringEffect", "parameters": {"effect": {"value": "[parameters('azureEventGridTopicsShouldUsePrivateLinkMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/2393d2cf-a342-44cd-a2e2-fe0188fd1234", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "azureSignalRServiceShouldUsePrivateLinkMonitoringEffect", "parameters": {"effect": {"value": "[parameters('azureSignalRServiceShouldUsePrivateLinkMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ba769a63-b8cc-4b2d-abf6-ac33c7204be8", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "azureMachineLearningWorkspacesShouldBeEncryptedWithACustomerManagedKeyMonitoringEffect", "parameters": {"effect": {"value": "[parameters('azureMachineLearningWorkspacesShouldBeEncryptedWithACustomerManagedKeyMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/45e05259-1eb5-4f70-9574-baf73e9d219b", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "azureMachineLearningWorkspacesShouldUsePrivateLinkMonitoringEffect", "parameters": {"effect": {"value": "[parameters('azureMachineLearningWorkspacesShouldUsePrivateLinkMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/055aa869-bc98-4af8-bafc-23f1ab6ffe2c", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "webApplicationFirewallShouldBeEnabledForAzureFrontDoorServiceServiceMonitoringEffect", "parameters": {"effect": {"value": "[parameters('webApplicationFirewallShouldBeEnabledForAzureFrontDoorServiceServiceMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-6"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/564feb30-bf6a-4854-b4bb-0d2d2d1e6c66", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "webApplicationFirewallShouldBeEnabledForApplicationGatewayMonitoringEffect", "parameters": {"effect": {"value": "[parameters('webApplicationFirewallShouldBeEnabledForApplicationGatewayMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-6"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/fdccbe47-f3e3-4213-ad5d-ea459b2fa077", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "publicNetworkAccessShouldBeDisabledForMariaDbServersMonitoringEffect", "parameters": {"effect": {"value": "[parameters('publicNetworkAccessShouldBeDisabledForMariaDbServersMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/d9844e8a-1437-4aeb-a32c-0c992f056095", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "publicNetworkAccessShouldBeDisabledForMySqlServersMonitoringEffect", "parameters": {"effect": {"value": "[parameters('publicNetworkAccessShouldBeDisabledForMySqlServersMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/83cef61d-dbd1-4b20-a4fc-5fbc7da10833", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "bringYourOwnKeyDataProtectionShouldBeEnabledForMySqlServersMonitoringEffect", "parameters": {"effect": {"value": "[parameters('bringYourOwnKeyDataProtectionShouldBeEnabledForMySqlServersMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b52376f7-9612-48a1-81cd-1ffe4b61032c", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "publicNetworkAccessShouldBeDisabledForPostgreSqlServersMonitoringEffect", "parameters": {"effect": {"value": "[parameters('publicNetworkAccessShouldBeDisabledForPostgreSqlServersMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/18adea5e-f416-4d0f-8aa8-d24321e3e274", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "bringYourOwnKeyDataProtectionShouldBeEnabledForPostgreSqlServersMonitoringEffect", "parameters": {"effect": {"value": "[parameters('bringYourOwnKeyDataProtectionShouldBeEnabledForPostgreSqlServersMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/2154edb9-244f-4741-9970-660785bccdaa", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "vmImageBuilderTemplatesShouldUsePrivateLinkMonitoringEffect", "parameters": {"effect": {"value": "[parameters('vmImageBuilderTemplatesShouldUsePrivateLinkMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/55615ac9-af46-4a59-874e-391cc3dfb490", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "firewallShouldBeEnabledOnKeyVaultMonitoringEffect", "parameters": {"effect": {"value": "[parameters('firewallShouldBeEnabledOnKeyVaultMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2", "Azure_Security_Benchmark_v3.0_DP-8"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a6abeaec-4d90-4a02-805f-6b26c4d3fbe9", "definitionVersion": "1.*.*-preview", "policyDefinitionReferenceId": "privateEndpointShouldBeConfiguredForKeyVaultMonitoringEffect", "parameters": {"audit_effect": {"value": "[parameters('privateEndpointShouldBeConfiguredForKeyVaultMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2", "Azure_Security_Benchmark_v3.0_DP-8"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/af35e2a4-ef96-44e7-a9ae-853dd97032c4", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "azureSpringCloudShouldUseNetworkInjectionMonitoringEffect", "parameters": {"effect": {"value": "[parameters('azureSpringCloudShouldUseNetworkInjectionMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/4f4f78b8-e367-4b10-a341-d9a4ad5cf1c7", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "subscriptionsShouldHaveAContactEmailAddressForSecurityIssuesMonitoringEffect", "parameters": {"effect": {"value": "[parameters('subscriptionsShouldHaveAContactEmailAddressForSecurityIssuesMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_IR-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6e2593d9-add6-4083-9c9b-4b7d2188c899", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "emailNotificationForHighSeverityAlertsShouldBeEnabledMonitoringEffect", "parameters": {"effect": {"value": "[parameters('emailNotificationForHighSeverityAlertsShouldBeEnabledMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_IR-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0b15565f-aa9e-48ba-8619-45960f2c314d", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "emailNotificationToSubscriptionOwnerForHighSeverityAlertsShouldBeEnabledMonitoringEffect", "parameters": {"effect": {"value": "[parameters('emailNotificationToSubscriptionOwnerForHighSeverityAlertsShouldBeEnabledMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_IR-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6edd7eda-6dd8-40f7-810d-67160c639cd9", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "storageAccountShouldUseAPrivateLinkConnectionMonitoringEffect", "parameters": {"effect": {"value": "[parameters('storageAccountShouldUseAPrivateLinkConnectionMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/630c64f9-8b6b-4c64-b511-6544ceff6fd6", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "authenticationToLinuxMachinesShouldRequireSSHKeysMonitoringEffect", "parameters": {"effect": {"value": "[parameters('authenticationToLinuxMachinesShouldRequireSSHKeysMonitoringEffect')]"}, "IncludeArcMachines": {"value": "[parameters('windowsWebServersShouldBeConfiguredToUseSecureCommunicationProtocolsIncludeArcMachines')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_IM-6"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/7698e800-9299-47a6-b3b6-5a0fee576eed", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "privateEndpointConnectionsOnAzureSQLDatabaseShouldBeEnabledMonitoringEffect", "parameters": {"effect": {"value": "[parameters('privateEndpointConnectionsOnAzureSQLDatabaseShouldBeEnabledMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1b8ca024-1d5c-4dec-8995-b1a932b41780", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "publicNetworkAccessOnAzureSQLDatabaseShouldBeDisabledMonitoringEffect", "parameters": {"effect": {"value": "[parameters('publicNetworkAccessOnAzureSQLDatabaseShouldBeDisabledMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1a5b4dca-0b6f-4cf5-907c-56316bc1bf3d", "definitionVersion": "8.*.*", "policyDefinitionReferenceId": "kubernetesClustersShouldBeAccessibleOnlyOverHTTPSMonitoringEffect", "parameters": {"effect": {"value": "[parameters('kubernetesClustersShouldBeAccessibleOnlyOverHTTPSMonitoringEffect')]"}, "excludedNamespaces": {"value": "[parameters('kubernetesClustersShouldBeAccessibleOnlyOverHTTPSExcludedNamespaces')]"}, "labelSelector": {"value": "[parameters('kubernetesClustersShouldBeAccessibleOnlyOverHTTPSLabelSelector')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/5752e6d6-1206-46d8-8ab1-ecc2f71a8112", "definitionVersion": "4.*.*", "policyDefinitionReferenceId": "windowsWebServersShouldBeConfiguredToUseSecureCommunicationProtocolsMonitoringEffect", "parameters": {"effect": {"value": "[parameters('windowsWebServersShouldBeConfiguredToUseSecureCommunicationProtocolsMonitoringEffect')]"}, "IncludeArcMachines": {"value": "[parameters('windowsWebServersShouldBeConfiguredToUseSecureCommunicationProtocolsIncludeArcMachines')]"}, "MinimumTLSVersion": {"value": "[parameters('windowsWebServersShouldBeConfiguredToUseSecureCommunicationProtocolsMinimumTLSVersion')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/037eea7a-bd0a-46c5-9a66-03aea78705d3", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "cognitiveServicesAccountsShouldRestrictNetworkAccessMonitoringEffect", "parameters": {"effect": {"value": "[parameters('cognitiveServicesAccountsShouldRestrictNetworkAccessMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1b4d1c4e-934c-4703-944c-27c82c06bebb", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "diagnosticLogsInAzureAIServicesResourcesShouldBeEnabledMonitoring", "groupNames": ["Azure_Security_Benchmark_v3.0_LT-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/d6759c02-b87f-42b7-892e-71b3f471d782", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "azureAIServicesResourcesShouldUseAzurePrivateLinkMonitoring", "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ef619a2c-cc4d-4d03-b2ba-8c94a834d85b", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "aPIManagementServicesShouldUseAVirtualNetworkMonitoringEffect", "parameters": {"effect": {"value": "[parameters('aPIManagementServicesShouldUseAVirtualNetworkMonitoringEffect')]"}, "evaluatedSkuNames": {"value": "[parameters('aPIManagementServicesShouldUseAVirtualNetworkEvaluatedSkuNames')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/862e97cf-49fc-4a5c-9de4-40d4e2e7c8eb", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "azureCosmosDBAccountsShouldHaveFirewallRulesMonitoringEffect", "parameters": {"effect": {"value": "[parameters('azureCosmosDBAccountsShouldHaveFirewallRulesMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b6e2945c-0b7b-40f5-9233-7a5323b5cdc6", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "networkWatcherShouldBeEnabledMonitoringEffect", "parameters": {"effect": {"value": "[parameters('networkWatcherShouldBeEnabledMonitoringEffect')]"}, "resourceGroupName": {"value": "[parameters('networkWatcherShouldBeEnabledResourceGroupName')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_IR-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/afe0c3be-ba3b-4544-ba52-0c99672a8ad6", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "resourceLogsInAzureMachineLearningWorkspacesShouldBeEnabled", "groupNames": ["Azure_Security_Benchmark_v3.0_LT-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/438c38d2-3772-465a-a9cc-7a6666a275ce", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "azureMachineLearningWorkspacesShouldDisablePublicNetworkAccess", "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/7804b5c7-01dc-4723-969b-ae300cc07ff1", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "azureMachineLearningComputesShouldBeInAVirtualNetwork", "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e96a9a5f-07ca-471b-9bc5-6a0f33cbd68f", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "azureMachineLearningComputesShouldHaveLocalAuthenticationMethodsDisabled", "groupNames": ["Azure_Security_Benchmark_v3.0_IM-1"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/f110a506-2dcb-422e-bcea-d533fc8c35e2", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "azureMachineLearningComputeInstancesshouldUseTheLatestOsImage", "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/138ff14d-b687-4faa-a81c-898c91a87fa2", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "resourceLogsInAzureDatabricksWorkspacesShouldBeEnabled", "groupNames": ["Azure_Security_Benchmark_v3.0_LT-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0e7849de-b939-4c50-ab48-fc6b0f5eeba2", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "azureDatabricksWorkspacesShouldDisablePublicNetworkAccess", "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/51c1490f-3319-459c-bbbc-7f391bbed753", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "azureDatabricksClustersShouldDisablePublicIp", "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/9c25c9e4-ee12-4882-afd2-11fb9d87893f", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "azureDatabricksWorkspacesShouldBeInAVirtualNetwork", "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/258823f2-4595-4b52-b333-cc96192710d8", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "azureDatabricksWorkspacesShouldUsePrivateLink", "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c3d20c29-b36d-48fe-808b-99a87530ad99", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "AzureDefenderForResourceManagerShouldBeEnabledMonitoringEffect", "parameters": {"effect": {"value": "[parameters('AzureDefenderForResourceManagerShouldBeEnabledMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-1", "Azure_Security_Benchmark_v3.0_LT-2", "Azure_Security_Benchmark_v3.0_IR-3", "Azure_Security_Benchmark_v3.0_IR-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/9f061a12-e40d-4183-a00e-171812443373", "definitionVersion": "4.*.*", "policyDefinitionReferenceId": "KubernetesClustersShouldNotUseTheDefaultNamespaceMonitoringEffect", "parameters": {"effect": {"value": "[parameters('KubernetesClustersShouldNotUseTheDefaultNamespaceMonitoringEffect')]"}, "labelSelector": {"value": "[parameters('KubernetesClustersShouldNotUseTheDefaultNamespaceMonitoringLabelSelector')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/423dd1ba-798e-40e4-9c4d-b6902674b423", "definitionVersion": "4.*.*", "policyDefinitionReferenceId": "KubernetesClustersShouldDisableAutomountingAPICredentialsMonitoringEffect", "parameters": {"effect": {"value": "[parameters('KubernetesClustersShouldDisableAutomountingAPICredentialsMonitoringEffect')]"}, "excludedNamespaces": {"value": "[parameters('KubernetesClustersShouldDisableAutomountingAPICredentialsMonitoringNamespaceExclusion')]"}, "labelSelector": {"value": "[parameters('KubernetesClustersShouldDisableAutomountingAPICredentialsMonitoringLabelSelector')]"}, "excludedImages": {"value": "[parameters('excludedImagesInKubernetesCluster')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/d2e7ea85-6b44-4317-a0be-1b951587f626", "definitionVersion": "5.*.*", "policyDefinitionReferenceId": "KubernetesClustersShouldNotGrantCAPSYSADMINSecurityCapabilitiesMonitoringEffect", "parameters": {"effect": {"value": "[parameters('KubernetesClustersShouldNotGrantCAPSYSADMINSecurityCapabilitiesMonitoringEffect')]"}, "excludedNamespaces": {"value": "[parameters('KubernetesClustersShouldNotGrantCAPSYSADMINSecurityCapabilitiesMonitoringNamespaceExclusion')]"}, "labelSelector": {"value": "[parameters('KubernetesClustersShouldNotGrantCAPSYSADMINSecurityCapabilitiesMonitoringLabelSelector')]"}, "excludedImages": {"value": "[parameters('excludedImagesInKubernetesCluster')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1c30f9cd-b84c-49cc-aa2c-9288447cc3b3", "definitionVersion": "2.*.*-preview", "policyDefinitionReferenceId": "VtpmShouldBeEnabledOnSupportedVirtualMachinesMonitoringEffect", "parameters": {"effect": {"value": "[parameters('VtpmShouldBeEnabledOnSupportedVirtualMachinesMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/97566dd7-78ae-4997-8b36-1c7bfe0d8121", "definitionVersion": "4.*.*-preview", "policyDefinitionReferenceId": "previewSecureBootShouldBeEnabledOnSupportedWindowsVirtualMachinesMonitoringEffect", "parameters": {"effect": {"value": "[parameters('SecureBootShouldBeEnabledOnSupportedWindowsVirtualMachinesMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/672fe5a1-2fcd-42d7-b85d-902b6e28c6ff", "definitionVersion": "6.*.*-preview", "policyDefinitionReferenceId": "GuestAttestationExtensionShouldBeInstalledOnSupportedLinuxVirtualMachinesMonitoringEffect", "parameters": {"effect": {"value": "[parameters('GuestAttestationExtensionShouldBeInstalledOnSupportedLinuxVirtualMachinesMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a21f8c92-9e22-4f09-b759-50500d1d2dda", "definitionVersion": "5.*.*-preview", "policyDefinitionReferenceId": "GuestAttestationExtensionShouldBeInstalledOnSupportedLinuxVirtualMachinesScaleSetsMonitoringEffect", "parameters": {"effect": {"value": "[parameters('GuestAttestationExtensionShouldBeInstalledOnSupportedLinuxVirtualMachinesScaleSetsMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1cb4d9c2-f88f-4069-bee0-dba239a57b09", "definitionVersion": "4.*.*-preview", "policyDefinitionReferenceId": "GuestAttestationExtensionShouldBeInstalledOnSupportedWindowsVirtualMachinesMonitoringEffect", "parameters": {"effect": {"value": "[parameters('GuestAttestationExtensionShouldBeInstalledOnSupportedWindowsVirtualMachinesMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/f655e522-adff-494d-95c2-52d4f6d56a42", "definitionVersion": "3.*.*-preview", "policyDefinitionReferenceId": "GuestAttestationExtensionShouldBeInstalledOnSupportedWindowsVirtualMachinesScaleSetsMonitoringEffect", "parameters": {"effect": {"value": "[parameters('GuestAttestationExtensionShouldBeInstalledOnSupportedWindowsVirtualMachinesScaleSetsMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/13a6c84f-49a5-410a-b5df-5b880c3fe009", "definitionVersion": "1.*.*-preview", "policyDefinitionReferenceId": "LinuxVirtualMachineShouldUseSignedAndTrustedBootComponentEffect", "parameters": {"effect": {"value": "[parameters('LinuxVirtualMachineShouldUseSignedAndTrustedBootComponentEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_PV-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/245fc9df-fa96-4414-9a0b-3738c2f7341c", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "diagnosticsLogsInKubernetesMonitoring", "parameters": {"effect": {"value": "[parameters('diagnosticsLogsInKubernetesMonitoringEffect')]"}, "requiredRetentionDays": {"value": "[parameters('diagnosticsLogsInKubernetesRetentionDays')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0a9fbe0d-c5c4-4da8-87d8-f4fd77338835", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "AzureDefenderForOpenSourceRelationalDatabasesShouldBeEnabledMonitoringEffect", "parameters": {"effect": {"value": "[parameters('AzureDefenderForOpenSourceRelationalDatabasesShouldBeEnabledMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-2", "Azure_Security_Benchmark_v3.0_LT-1", "Azure_Security_Benchmark_v3.0_LT-2", "Azure_Security_Benchmark_v3.0_IR-3", "Azure_Security_Benchmark_v3.0_IR-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1f90fc71-a595-4066-8974-d4d0802e8ef0", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "MicrosoftDefenderCSPMShouldBeEnabledMonitoringEffect", "parameters": {"effect": {"value": "[parameters('MicrosoftDefenderCSPMShouldBeEnabledMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_LT-1", "Azure_Security_Benchmark_v3.0_LT-2", "Azure_Security_Benchmark_v3.0_IR-3", "Azure_Security_Benchmark_v3.0_IR-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ca88aadc-6e2b-416c-9de2-5a0f01d1693f", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "gcLinuxDiskEncryptionMonitoring", "parameters": {"effect": {"value": "[parameters('gcLinuxDiskEncryptionMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/3dc5edcd-002d-444c-b216-e123bbfa37c0", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "gcWindowsDiskEncryptionMonitoring", "parameters": {"effect": {"value": "[parameters('gcWindowsDiskEncryptionMonitoringEffect')]"}}, "groupNames": ["Azure_Security_Benchmark_v3.0_DP-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/abda6d70-9778-44e7-84a8-06713e6db027", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "sqlServerADOnlyEnabledMonitoring", "groupNames": ["Azure_Security_Benchmark_v3.0_IM-1"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b3a22bc9-66de-45fb-98fa-00f5df42f41a", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "sqlServerDisableADOnlyAuthMonitoring", "groupNames": ["Azure_Security_Benchmark_v3.0_IM-1"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/146412e9-005c-472b-9e48-c87b72ac229e", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "mySqlServerADAdminisMonitoring", "groupNames": ["Azure_Security_Benchmark_v3.0_DP-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b4dec045-250a-48c2-b5cc-e0c4eec8b5b4", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "postgreSqlServerADAdminisMonitoring", "groupNames": ["Azure_Security_Benchmark_v3.0_IM-1"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/78215662-041e-49ed-a9dd-5385911b3a1f", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "sqlManagedInstanceADOnlyEnabledMonitoring", "groupNames": ["Azure_Security_Benchmark_v3.0_IM-1"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0c28c3fb-c244-42d5-a9bf-f35f2999577b", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "sqlManagedInstanceDisablingADOnlyAuthMonitoring", "groupNames": ["Azure_Security_Benchmark_v3.0_IM-1"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/2158ddbe-fefa-408e-b43f-d4faef8ff3b8", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "synapseWorkspaceADOnlyEnabledMonitoring", "groupNames": ["Azure_Security_Benchmark_v3.0_IM-1"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6ea81a52-5ca7-4575-9669-eaa910b7edf8", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "synapseWorkspaceDisablingADOnlyAuthMonitoring", "groupNames": ["Azure_Security_Benchmark_v3.0_IM-1"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/3aa03346-d8c5-4994-a5bc-7652c2a2aef1", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "aPIManagementServiceShouldNotHaveAllApisScopedSubscriptions", "groupNames": ["Azure_Security_Benchmark_v3.0_PA-7"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/92bb331d-ac71-416a-8c91-02f2cb734ce4", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "aPIManagementServiceShouldNotBypassCertificateValidation", "groupNames": ["Azure_Security_Benchmark_v3.0_IM-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ee7495e7-3ba7-40b6-bfee-c29e22cc75d4", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "aPIManagementServiceShouldUseEncryptedProtocols", "groupNames": ["Azure_Security_Benchmark_v3.0_DP-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/f1cc7827-022c-473e-836e-5a51cae0b249", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "aPIManagementServiceShouldUseKeyVaultForSecretNamedValues", "groupNames": ["Azure_Security_Benchmark_v3.0_IM-8", "Azure_Security_Benchmark_v3.0_DP-6"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b741306c-968e-4b67-b916-5675e5c709f4", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "aPIManagementServiceShouldHaveDirectManagementEndpointDisabled", "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/df73bd95-24da-4a4f-96b9-4e8b94b402bd", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "aPIManagementServiceShouldDisableServiceConfigurationEndpoints", "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/549814b6-3212-4203-bdc8-1548d342fb67", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "aPIManagementServiceShouldHaveMinimumAPIVersionSet", "groupNames": ["Azure_Security_Benchmark_v3.0_IM-8", "Azure_Security_Benchmark_v3.0_PV-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c15dcc82-b93c-4dcb-9332-fbf121685b54", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "aPIManagementServiceShouldHaveBackendCallsAuthenticated", "groupNames": ["Azure_Security_Benchmark_v3.0_IM-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/40e85574-ef33-47e8-a854-7a65c7500560", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "mySqlServerADOnlyEnabledMonitoring", "groupNames": ["Azure_Security_Benchmark_v3.0_DP-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/3ac7c827-eea2-4bde-acc7-9568cd320efa", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "serverSecretFindingsMonitoring", "groupNames": ["Azure_Security_Benchmark_v3.0_PV-5", "Azure_Security_Benchmark_v3.0_IM-8"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c8acafaf-3d23-44d1-9624-978ef0f8652c", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "unusedApisShouldbeDisabledAndRemovedFromApim", "groupNames": ["Azure_Security_Benchmark_v3.0_AM-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/8ac833bd-f505-48d5-887e-c993a1d3eea0", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "apimApiEndpointsShouldbeAuthenticated", "groupNames": ["Azure_Security_Benchmark_v3.0_IM-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/640d2586-54d2-465f-877f-9ffc1d2109f4", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "defenderForStorageShouldBeEnabledMonitoring", "groupNames": ["Azure_Security_Benchmark_v3.0_DP-2", "Azure_Security_Benchmark_v3.0_LT-1", "Azure_Security_Benchmark_v3.0_LT-2", "Azure_Security_Benchmark_v3.0_IR-3", "Azure_Security_Benchmark_v3.0_IR-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/71ef260a-8f18-47b7-abcb-62d0673d94dc", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "cognitiveServicesAccountsShouldHaveLocalAuthenticationMethodsDisabled", "groupNames": ["Azure_Security_Benchmark_v3.0_IM-1"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/cddd188c-4b82-4c48-a19d-ddf74ee66a01", "definitionVersion": "3.*.*", "policyDefinitionReferenceId": "cognitiveServicesShouldUsePrivateLink", "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/fc4d8e41-e223-45ea-9bf5-eada37891d87", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "virtualMachinesAndVirtualMachineScaleSetsShouldHaveEncryptionAtHostEnabled", "groupNames": ["Azure_Security_Benchmark_v3.0_DP-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/797b37f7-06b8-444c-b1ad-fc62867f335a", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "azureCosmosDBShouldDisablePublicNetworkAccess", "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/58440f8a-10c5-4151-bdce-dfbaad4a20b7", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "cosmosDBAaccountsShouldUsePrivateLink", "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/21a6bc25-125e-4d13-b82d-2e19b7208ab7", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "vPNGatewaysShouldUseOnlyAzureActiveDirectoryAzureADAuthenticationForPointtositeUsers", "groupNames": ["Azure_Security_Benchmark_v3.0_IM-1"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/32e6bbec-16b6-44c2-be37-c5b672d103cf", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "azureSQLDatabaseShouldBeRunningTLSVersion12OrNewer", "groupNames": ["Azure_Security_Benchmark_v3.0_DP-3", "Azure_Security_Benchmark_v3.0_IM-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/9dfea752-dd46-4766-aed1-c355fa93fb91", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "azureSQLManagedInstancesShouldDisablePublicNetworkAccess", "groupNames": ["Azure_Security_Benchmark_v3.0_NS-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/8c6a50c6-9ffd-4ae7-986f-5fa6111f9a54", "definitionVersion": "2.*.*", "policyDefinitionReferenceId": "storageAccountsShouldPreventSharedKeyAccess", "groupNames": ["Azure_Security_Benchmark_v3.0_IM-1"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1dc2fc00-2245-4143-99f4-874c937f13ef", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "aPIManagementServicePlatformVersionShouldBeStv2", "groupNames": ["Azure_Security_Benchmark_v3.0_PV-2", "Azure_Security_Benchmark_v3.0_AM-2"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/5e6bf724-0154-49bc-985f-27b2e07e636b", "definitionVersion": "1.*.*-preview", "policyDefinitionReferenceId": "hciSecuredCoreComplianceMonitoringAtCluster", "groupNames": ["Azure_Security_Benchmark_v3.0_PV-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/dad3a6b9-4451-492f-a95c-69efc6f3fada", "definitionVersion": "1.*.*-preview", "policyDefinitionReferenceId": "hciWdacComplianceMonitoringAtCluster", "groupNames": ["Azure_Security_Benchmark_v3.0_PV-4"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ee8ca833-1583-4d24-837e-96c2af9488a4", "definitionVersion": "1.*.*-preview", "policyDefinitionReferenceId": "hciDataAtRestEncryptedComplianceMonitoringAtCluster", "groupNames": ["Azure_Security_Benchmark_v3.0_DP-5"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/36f0d6bc-a253-4df8-b25b-c3a5023ff443", "definitionVersion": "1.*.*-preview", "policyDefinitionReferenceId": "hciDataInTransitComplianceMonitoringAtCluster", "groupNames": ["Azure_Security_Benchmark_v3.0_DP-3"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/fa498b91-8a7e-4710-9578-da944c68d1fe", "definitionVersion": "1.*.*-preview", "policyDefinitionReferenceId": "postgreSqlFlexibleServersEnableEntraOnlyAuthenticationMonitoring", "groupNames": ["Azure_Security_Benchmark_v3.0_IM-1"]}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/12d4fa5e-1f9f-4c21-97a9-b99b3c6611b5", "definitionVersion": "1.*.*", "policyDefinitionReferenceId": "keyvaultServiceRbacEnabledMonitoring", "groupNames": ["Azure_Security_Benchmark_v3.0_PA-7"]}], "versions": ["57.51.0", "57.50.0", "57.49.0", "57.48.0", "57.47.0", "57.46.0", "57.45.0", "57.44.0", "57.43.0", "57.42.0", "57.41.0", "57.40.0", "57.39.0", "57.38.0", "57.37.0", "57.36.0", "57.35.0", "57.34.0", "57.33.0", "57.32.0", "57.31.0", "57.30.0", "57.29.0", "57.28.1", "57.27.0", "57.26.0", "57.25.0", "57.24.0", "57.23.1"]}, "id": "/providers/Microsoft.Authorization/policySetDefinitions/1f3afdf9-d0c9-4c3d-847f-89da613e70a8", "name": "1f3afdf9-d0c9-4c3d-847f-89da613e70a8"}