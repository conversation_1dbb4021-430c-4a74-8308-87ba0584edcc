from security_pr_review import SecurityPRReviewer
import json

# Initialize the reviewer in local mode
reviewer = SecurityPRReviewer(local_folder="C:\\Users\\<USER>\\Downloads\\drop_build_main\\drop_build_main\\deployments\\servicegrouproot-Managed-SDP\\templates")

# Prepare the benchmark data
reviewer.prepare_benchmark()

# Run the analysis
findings = reviewer.analyze_folder("C:\\Users\\<USER>\\Downloads\\drop_build_main\\drop_build_main\\deployments\\servicegrouproot-Managed-SDP\\templates")

# Print a summary of findings
print(f"Found {len(findings)} security issues")
print(f"{findings}")
# Save findings to a file
with open("security_findings.json", "w") as f:
    json.dump(findings, f, indent=2)