[{"control_id": "IM-1", "severity": "HIGH", "line": 74, "description": "Authentication is not enforced for all users. The 'requireAuthentication' property is set to false, which means unauthenticated access is allowed to the App Service.", "remediation": "Set 'requireAuthentication' to true in the 'globalValidation' section to enforce authentication for all users accessing the App Service.", "file_path": "main.json"}, {"control_id": "DP-1", "severity": "MEDIUM", "line": 38, "description": "The App Service does not have 'clientCertEnabled' set, which means client certificate authentication is not enforced. This weakens transport security for sensitive applications.", "remediation": "Add 'clientCertEnabled': true to the App Service properties if client certificate authentication is required for your scenario.", "file_path": "main.json"}, {"control_id": "NS-2", "severity": "MEDIUM", "line": 38, "description": "The App Service is not configured with access restrictions (IP restrictions or service endpoints), which means it is potentially accessible from any network, including the public internet.", "remediation": "Configure 'ipSecurityRestrictions' in the App Service configuration to restrict access to only trusted IP addresses or networks.", "file_path": "main.json"}, {"control_id": "DP-3", "severity": "LOW", "line": 38, "description": "There is no evidence of HTTPS minimum TLS version enforcement. The App Service should enforce a minimum TLS version (e.g., 1.2) to ensure secure transport.", "remediation": "Set 'minTlsVersion' property (e.g., '1.2') in the App Service configuration to enforce a secure TLS version.", "file_path": "main.json"}]