Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
CRITICAL,NS-2,keyvault.bicep,17,"The Key Vault resource has 'networkAcls.defaultAction' set to 'Allow', meaning it will allow access from all public networks unless specifically denied via rules. This exposes the Key Vault to the public internet, violating the ASB requirement to secure all public endpoints.",Set 'networkAcls.defaultAction' to 'Deny' and explicitly whitelist only required IP addresses and vNets using 'ipRules' and 'virtualNetworkRules' to restrict access.,N/A,AI,Generic
CRITICAL,DP-3,operational-insights.bicep,79,"The template outputs 'appInsightsInstrumentationKey', which may be sensitive, as an output parameter. The Application Insights Instrumentation Key is considered sensitive, as exposure can allow unauthorized telemetry data ingestion and leakage of monitoring context.",Remove the 'appInsightsInstrumentationKey' output or use Azure Key Vault to securely store and reference instrumentation keys. Do not output or expose instrumentation keys in templates or logs.,N/A,AI,Generic
HIGH,NS-5,app-config.bicep,7,"The App Configuration resource (Microsoft.AppConfiguration/configurationStores) is deployed without Private Endpoint configuration. By default, it will be accessible over a public endpoint, increasing the risk of unauthorized access.",Configure a Private Endpoint for the App Configuration resource to ensure access is restricted to internal networks only and not exposed to the public internet. Add a 'privateEndpointConnections' resource referencing a Private Endpoint in the Bicep template.,N/A,AI,Generic
HIGH,NS-2,app-config.bicep,7,"The App Configuration resource is created without restricting public network access. This exposes public endpoints that may be accessible from the internet, violating secure access best practices.","Restrict public network access by setting the 'publicNetworkAccess' property to 'Disabled' in the App Configuration resource properties, and depend solely on private endpoints for connectivity.",N/A,AI,Generic
HIGH,DP-3,app-config.bicep,20,"Key-value pairs for App Configuration are parameterized, but the template does not integrate with Azure Key Vault for storing sensitive application settings (e.g., secrets, API keys). Storing secrets directly in configuration or parameters can cause sensitive information disclosure.","Store secrets and sensitive values in Azure Key Vault and reference them securely from your application code, instead of including them as App Configuration key-values. Avoid placing secrets in IaC parameters or in App Configuration unless Key Vault references are used.",N/A,AI,Generic
HIGH,NS-5,event-grid.bicep,15,"Event subscriptions are configured to deliver events directly to a storage queue via the 'StorageQueue' endpoint type, but there is no indication that Private Endpoints are being used for secure communication between Event Grid and the storage account. This may expose the storage queue endpoint to the public internet.","Update the target storage account to use Private Endpoints, and configure Event Grid system topics/subscriptions to send events via the private endpoint, ensuring all event traffic remains on the trusted Microsoft backbone.",N/A,AI,Generic
HIGH,DP-3,event-grid.bicep,2,"The storage account resource IDs (storageFuncId, storageFuzzId, storageCorpusIds[].id) and possibly queue names are accepted as plain string parameters. There is no indication these values are protected, nor whether secrets (such as connection strings, access keys) will be handled securely elsewhere.","Where sensitive data such as keys or connection strings are used, store them in Azure Key Vault and reference them securely. Ensure that the Bicep template does not require secrets as parameters, but retrieves them securely at deployment time.",N/A,AI,Generic
HIGH,NS-1,function-settings.bicep,28,"The Storage Account resource 'funcStorage' is declared as an existing resource, but there is no evidence in this template that appropriate network protections (such as NSGs or Azure Firewall rules) are applied. This potentially leaves the storage account exposed to the public internet.","Ensure the referenced Storage Account (funcStorage) is configured with network security controls. Restrict access using private endpoints, service endpoints, or firewall rules to limit exposure only to required services/networks.",N/A,AI,Generic
HIGH,NS-2,function-settings.bicep,53,"App Service function exposes endpoints by default and there is no evidence of access restriction (e.g., IP restrictions, VNet integration, or private endpoints) being configured. Public accessibility without restrictions increases the attack surface.","Configure access restrictions on the App Service to allow access only from trusted networks, IPs, or via private endpoints and service endpoints. Consider enabling Azure App Service Private Endpoint where possible.",N/A,AI,Generic
HIGH,DP-3,function-settings.bicep,49,Sensitive information such as 'APPINSIGHTS_INSTRUMENTATIONKEY' is set directly from parameters. There is no evidence that secrets such as instrumentation keys or connection strings are sourced from Azure Key Vault or another secure secret store.,"Move all sensitive values, such as App Insights keys and connection strings, to Azure Key Vault. Reference these secrets from the template using secure mechanisms to avoid exposure in configuration or code.",N/A,AI,Generic
HIGH,NS-5,function-settings.bicep,28,"Network access to the Storage Account ('funcStorage') is not protected by a Private Endpoint or restricted to specific networks. Without private endpoints, sensitive data in storage may be accessible via public networks.",Integrate the Storage Account with an Azure Private Endpoint and disable public network access to prevent data exposure.,N/A,AI,Generic
HIGH,NS-1,function.bicep,54,"The Azure Storage Account (logs_storage) is referenced as an existing resource, but there is no validation or enforcement ensuring that it is protected by Network Security Groups (NSGs) or Azure Firewall. Storage accounts are often public by default, which can expose sensitive log data if not properly restricted.",Ensure the referenced storage account has access restrictions enforced using NSGs or Azure Firewall and allow access only from trusted networks (such as the function app's subnet or private endpoints).,N/A,AI,Generic
HIGH,DP-3,function.bicep,87,"The storage account SAS token is generated and interpolated directly into the log configuration ('sasUrl') for diagnostic logs. There is no use of Azure Key Vault to manage or reference sensitive data like SAS tokens, increasing risk of sensitive information exposure.",Use Azure Key Vault to securely store and reference sensitive tokens and connection strings. Avoid direct interpolation of secrets in resource properties.,N/A,AI,Generic
HIGH,NS-5,function.bicep,62,The function app configures VNet integration but does not make use of Azure Private Endpoints to securely connect to the storage account or limit its exposure to the public internet.,Implement Private Endpoints for both the storage account and the function app to ensure that data flows privately within the Azure backbone and is not exposed to the public internet.,N/A,AI,Generic
HIGH,NS-1,hub-network.bicep,2,"The virtual network 'hub-vnet' and its subnet 'hub-subnet' are defined without any associated Network Security Group (NSG) or Azure Firewall to control network traffic, leaving resources unprotected from unauthorized access. This violates ASB control NS-1, which requires protecting resources using NSGs or Azure Firewall.","Associate an NSG with the subnet, configuring restrictive rules to permit only necessary inbound and outbound traffic. If advanced inspection is needed, deploy Azure Firewall and direct traffic through it using appropriate routes.",N/A,AI,Generic
HIGH,NS-3,hub-network.bicep,14,"The 'hub-subnet' is not associated with any Network Security Group (NSG), which is required to enforce explicit access controls on network traffic. This violates ASB control NS-3 requiring the use of NSGs to control traffic into and out of subnets.","Create and associate an NSG with the 'hub-subnet', defining inbound and outbound security rules to restrict traffic to only what is necessary for applications and services in this subnet.",N/A,AI,Generic
HIGH,DP-3,instance-config.bicep,9,"Sensitive information such as Azure AD tenant IDs, client IDs, allowed tenants, and domain details are passed into the template as parameters (specificConfig) and surfaced as plain object values or outputs, without guaranteed use of Azure Key Vault for secure storage or referencing. Storing or handling secrets and sensitive configuration data in plain text or output variables increases the risk of disclosure.","Ensure any sensitive values (e.g., tenant_id, cli_client_id, allowed_aad_tenants) are referenced from Azure Key Vault or securely injected at runtime (e.g., using Key Vault references or secure parameter mechanisms). Remove direct outputs of sensitive fields unless strictly necessary and protected.",N/A,AI,Generic
HIGH,NS-2,ip-rules.bicep,2,"The 'corpNetIps' variable includes multiple large public IP ranges (e.g., '*******/8', '********/8') that vastly exceed the intended principle of least privilege for network access. Allowing large public IP blocks increases attack surface for resources using these rules, violating the benchmark's mandate to minimize public exposure.","Restrict IP allow lists to the smallest possible range required for business needs. Instead of large public IPv4 ranges, specify only necessary, trusted IP addresses or tightly scoped ranges. Review and reduce allowed IP addresses to the minimum required.",N/A,AI,Generic
HIGH,NS-1,keyvault.bicep,17,"Key Vault is not sufficiently protected with network restrictions; 'defaultAction' is set to 'Allow', bypassing network controls and exposing the resource to the internet. Critical resources must be protected by network security controls such as NSGs or firewalls.",Use 'networkAcls.defaultAction' set to 'Deny' and ensure access is only allowed through specific vNet or IP rules. Implement further restrictions in associated subnets using NSGs where possible.,N/A,AI,Generic
HIGH,NS-1,scaleset-networks.bicep,1,No Network Security Groups (NSGs) or Azure Firewall resources are defined on the virtual network or subnets to protect resources. This exposes the subnet and its resources to potentially unfiltered network traffic.,"Define and associate an NSG with the scale set subnet to explicitly allow/deny required inbound and outbound traffic according to the least privilege principle. Alternatively, deploy and configure Azure Firewall to protect the network perimeter.",N/A,AI,Generic
HIGH,NS-3,scaleset-networks.bicep,1,"The template does not deploy or associate any Network Security Groups (NSGs) with the virtual network or subnet. NSGs are required to control and restrict inbound and outbound traffic to resources within the subnet (e.g., scale set VMs).","Associate an NSG resource with the 'scaleset' subnet, defining rules to permit only required traffic and deny all other traffic.",N/A,AI,Generic
HIGH,NS-2,scaleset-networks.bicep,7,"A public IP address is provisioned ('scaleset-outbound-ip') and attached to the NAT gateway for outbound communication. There is no reference to NSG or firewall controls, which could leave public endpoints unprotected.","Ensure that inbound traffic to the public IP is blocked via NSG or firewall rules unless explicitly required, and limit the exposure of public endpoints as much as possible.",N/A,AI,Generic
HIGH,NS-1,server-farms.bicep,1,There is no evidence of network security controls such as Network Security Groups (NSGs) or Azure Firewall being applied to protect the App Service or referenced Key Vault resources. This increases the attack surface and can allow unauthorized network access to sensitive resources.,"Implement appropriate NSGs or Azure Firewall rules to restrict access to the App Service and Key Vault resources to only required sources and services, in line with the principle of least privilege.",N/A,AI,Generic
HIGH,NS-2,server-farms.bicep,1,"The template does not enforce restriction of public endpoints or specify private networking for the App Service or referenced Key Vaults. App Services, by default, are publicly accessible unless configured otherwise. Exposing resources to the public internet unnecessarily increases exposure to threats.","Configure App Service access restrictions to explicitly deny all except trusted IP addresses or subnets. Use private endpoints or service endpoints for internal access where possible. Similarly, restrict Key Vault public access by enabling firewall rules or private endpoint connections.",N/A,AI,Generic
HIGH,DP-3,server-farms.bicep,98,"The 'serverFarms_AntMDS_CERTIFICATE_PASSWORD_GENEVACERT' setting is configured with an empty string as the certificate password. This could lead to misconfiguration where a password is expected for certificates, potentially resulting in weak protection or application/service failures.",Store sensitive certificate passwords securely in Azure Key Vault and reference them as secrets. Never use empty or hardcoded values for sensitive credential parameters.,N/A,AI,Generic
HIGH,IM-6,server-farms.bicep,1,There is no explicit assignment or enforcement of RBAC roles for either App Service or Key Vault within the template. Not limiting privileges according to least privilege may lead to excessive permissions and increased risk of misuse or compromise.,"Explicitly assign required RBAC roles (at a minimum, ""Key Vault Reader/Secrets User"" for the managed identity) either within this template or as a separate automation step, scoped as narrowly as possible.",N/A,AI,Generic
HIGH,NS-2,signalR.bicep,5,"The SignalR resource is deployed with the default configuration, which exposes a public endpoint by default. Not specifying network ACLs, private endpoints, or restricted access may leave the service accessible from the public internet, increasing the attack surface.","Restrict public access to the SignalR service by configuring IP ACLs to trusted ranges, enabling private endpoints, or integrating with Azure Virtual Network service endpoints. Explicitly define 'networkAcls' to restrict access as appropriate.",N/A,AI,Generic
HIGH,NS-2,storage-accounts.bicep,24,"Storage accounts are configured with `networkAcls.defaultAction = 'Allow'`, which allows access to the storage account from any network unless explicitly blocked. This creates a public endpoint for the storage account, violating the principle of securing public endpoints.","Set `networkAcls.defaultAction` to 'Deny', and only explicitly allow trusted IPs or virtual network rules. This will ensure that storage accounts are not exposed to the public internet.",N/A,AI,Generic
HIGH,NS-1,storage-accounts.bicep,24,"`networkAcls.bypass` includes 'AzureServices', 'Logging', and 'Metrics', which can allow privileged Azure service traffic to the storage account. Overuse of bypass options may permit excessive or unnecessary access, potentially exposing sensitive data.","Restrict the `bypass` setting to only the services necessary for your application. Remove 'AzureServices' unless there's a strict business requirement, or use managed identities to limit service access.",N/A,AI,Generic
MEDIUM,NS-6,event-grid.bicep,15,"Event subscription destination references a storage queue without any enforcement of Virtual Network Service Endpoints. Without service endpoints, access to the storage account could be exposed to the public internet.",Restrict access to the storage account using Virtual Network Service Endpoints so that only traffic from selected VNETs can access the queues.,N/A,AI,Generic
MEDIUM,DP-2,function-settings.bicep,53,"There is no explicit specification of minimum TLS version for the App Service, which may permit legacy and insecure protocols for data in transit.",Set the 'minTlsVersion' property on the App Service resource configuration to '1.2' or higher to enforce TLS 1.2+ for all client connections.,N/A,AI,Generic
MEDIUM,NS-2,function.bicep,62,"The function app enables VNet integration by setting 'virtualNetworkSubnetId', but there is no explicit restriction of public inbound access using access restrictions or firewall rules. This can expose public endpoints unnecessarily.","Explicitly configure access restrictions or Azure Firewall rules on the function app to restrict public inbound access, allowing only trusted network locations.",N/A,AI,Generic
MEDIUM,DP-2,function.bicep,40,"There is no explicit configuration of minimum TLS version for the function app, which should be set to at least TLS 1.2 to ensure encryption in transit.",Specify 'minTlsVersion' in the function app's siteConfig with a value of '1.2' or higher.,N/A,AI,Generic
MEDIUM,NS-2,ip-rules.bicep,34,"The 'ingestionServiceIps' array is present but empty, with a '// TODO' indicating placeholders for public IP authorization. This ambiguity may result in overly permissive rules or allow for insecure expansion in future. Any omission or later inclusion of broad/public ranges could expose endpoints in violation of secure access controls.","Explicitly define trusted, minimal IP addresses for 'ingestionServiceIps'. Remove the TODO and use least-privilege principles when authorizing IPs. Regularly audit and update IP allowlists based on current requirements.",N/A,AI,Generic
MEDIUM,DP-6,keyvault.bicep,10,The Key Vault resource does not define or enforce the use of a Key Vault encryption key with a customer-managed key (CMK). Relying solely on platform-managed keys may not be sufficient for sensitive or regulated data.,"Configure the Key Vault to use customer-managed keys by specifying the 'encryption' property with a user-assigned key, if organizational policy or compliance requires.",N/A,AI,Generic
MEDIUM,DP-3,keyvault.bicep,33,"Secrets are created directly from parameter input using the 'secrets' object, with potential risk of sensitive values being supplied inline rather than referenced securely (e.g., from Key Vault references or managed identities). This may expose secrets in deployment logs or histories.",Ensure 'secrets' values are sourced securely—avoid passing sensitive values directly via parameter files or pipelines. Use secure input mechanisms such as Azure Key Vault references or secure pipeline variables for secrets.,N/A,AI,Generic
MEDIUM,DP-3,operational-insights.bicep,78,"The template outputs 'appInsightsAppId', which could reveal potentially sensitive operational information. While AppId is less sensitive than keys, its exposure should be minimized to prevent information disclosure.","Avoid outputting 'appInsightsAppId' unless necessary for automation securely managed downstream. If required, consider limiting access to deployment outputs.",N/A,AI,Generic
MEDIUM,DP-3,operational-insights.bicep,80,"The template outputs 'workspaceId' and 'logAnalyticsWorkspaceId', which reveal operational workspace resource identifiers. Directly exposing resource IDs may facilitate information gathering for attackers.",Restrict access to deployment outputs via RBAC and avoid outputting resource IDs unless required for downstream automation that is securely managed.,N/A,AI,Generic
MEDIUM,NS-4,scaleset-networks.bicep,1,No Azure Firewall or third-party firewall has been deployed or referenced within the template to provide centralized traffic inspection and threat protection.,"Implement an Azure Firewall resource on critical points of the network topology, particularly if internet connectivity is allowed, and integrate with your network architecture, using UDRs to direct traffic through the firewall where necessary.",N/A,AI,Generic
MEDIUM,NS-5,scaleset-networks.bicep,31,"Private endpoint policies are set to 'Enabled', but no private endpoints are defined for accessing Azure resources (e.g., Storage, Key Vault, SQL), missing an opportunity to restrict data access to only within the VNet.","Define appropriate private endpoints for backend services (such as Azure Storage, SQL, or Key Vault) that should not be accessible from public networks.",N/A,AI,Generic
MEDIUM,NS-5,server-farms.bicep,1,The template does not configure private endpoints for App Service or referenced Key Vault deployments. This omits a stronger layer of network isolation for sensitive resources.,"Implement Azure Private Endpoints for App Service and Key Vault resources to ensure all traffic traverses secure, private Azure backbone network paths. Restrict public network access.",N/A,AI,Generic
MEDIUM,IM-8,server-farms.bicep,1,"There is no usage or assignment of Managed Identities for the App Service resource, nor evidence of Managed Identity being used to access Key Vault secrets. Omitting managed identities increases the risk of credential exposure and complicates secure resource-to-resource authentication.",Enable a system-assigned or user-assigned Managed Identity for the App Service and use this identity for accessing Key Vault and other Azure resources. Update access policies or RBAC as appropriate.,N/A,AI,Generic
MEDIUM,IM-1,signalR.bicep,13,"Azure SignalR authentication for clients is not specified, and by default, clients may use local authentication keys. Although 'disableLocalAuth' is set to true, making Azure AD authentication more likely, the template does not explicitly show the use of Azure AD for identity and access management.",Ensure that SignalR is configured to use Azure Active Directory authentication for all access. Document or automate Azure AD integration in the template when possible.,N/A,AI,Generic
MEDIUM,NS-3,storage-accounts.bicep,24,There is no evidence of Network Security Groups (NSGs) being used to protect access to the storage accounts. NSGs offer granular control over inbound and outbound traffic.,"Implement NSGs on the subnets where the storage accounts are accessed (for example, the subnet referenced by `hubSubnetId`) to restrict access to only trusted IP ranges or resources.",N/A,AI,Generic
MEDIUM,DP-1,storage-accounts.bicep,25,"Storage account resources do not specify customer-managed keys (CMKs) for encryption at rest. The default provider-managed keys are used, which may not meet stricter organizational or regulatory requirements.",Configure storage accounts with customer-managed keys from Azure Key Vault to enhance control over encryption at rest.,N/A,AI,Generic
MEDIUM,DP-2,storage-accounts.bicep,25,"`supportsHttpsTrafficOnly` is set to `true`, enforcing HTTPS. However, the minimum TLS version is not specified, and may default to a less secure version.",Explicitly set the `minimumTlsVersion` property to 'TLS1_2' or higher for all storage accounts to enforce strong encryption in transit.,N/A,AI,Generic
MEDIUM,DP-3,storage-accounts.bicep,90,"CORS configuration for blob services allows all headers (`allowedHeaders: ['*']` and `exposedHeaders: ['*']`) and origins (configurable via `cors_origins`). If `cors_origins` includes broad or wildcard origins, this could disclose sensitive information.",Limit allowed headers and exposed headers to only what is strictly needed. Do not use wildcards unless necessary. Restrict `cors_origins` to trusted domains only.,N/A,AI,Generic
LOW,DP-1,function.bicep,54,"The storage account (logs_storage) is referenced as existing, but there is no enforcement or validation of encryption at rest (such as requiring infrastructure or customer-managed keys).","Ensure that the storage account enabling diagnostic logs uses encryption at rest (and, if compliance requires, customer-managed keys).",N/A,AI,Generic
LOW,IM-8,function.bicep,70,The function app enables both SystemAssigned and UserAssigned managed identities but does not assign any RBAC roles or limit permissions to least privilege for these identities in the template.,"Explicitly assign least privilege RBAC roles to the managed identities, granting only the necessary access for the function app to resources such as Key Vault, Storage, etc.",N/A,AI,Generic
LOW,NS-6,scaleset-networks.bicep,35,"Service endpoints are deliberately left empty. While this may be intentional, it does not leverage service endpoints to secure traffic to Azure PaaS resources.","Add relevant service endpoints to the subnet (e.g., Microsoft.Storage, Microsoft.Sql) to secure connections from this subnet to those services, reducing the attack surface.",N/A,AI,Generic
LOW,DP-1,signalR.bicep,5,"There is no explicit configuration for encryption at rest for the SignalR resource. While Azure SignalR Service encryption at rest is enabled by default, the template does not enforce or document this, and customers may miss validating this essential security measure.","Add documentation or configuration to verify or enforce encryption at rest for the SignalR resource, and monitor compliance with encryption policies.",N/A,AI,Generic
LOW,DP-2,signalR.bicep,5,"The template does not specify a requirement for encryption in transit, such as enforcing TLS 1.2+ for client connections to SignalR. While the service defaults to secure protocols, the lack of explicit specification may cause ambiguity or compliance gaps.","Explicitly configure or document transport security requirements for SignalR, ensuring only TLS 1.2 or above is allowed for client and server communications.",N/A,AI,Generic
LOW,IM-1,storage-accounts.bicep,27,Storage accounts do not explicitly enable Azure Active Directory (Azure AD) authentication for data plane access. This means access may rely on legacy shared keys or SAS tokens.,"Enable Azure AD authentication for storage accounts, and migrate from using shared key/SAS access where possible.",N/A,AI,Generic
