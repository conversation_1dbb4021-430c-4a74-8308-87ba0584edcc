#!/usr/bin/env python3
"""
Quick validation script for IaC Guardian prompt optimizations.
Tests basic functionality without requiring full AI analysis.
"""

import os
import sys
import tempfile
from pathlib import Path

# Add current directory to path
sys.path.append('.')

def test_context_analysis():
    """Test the context analysis functionality."""
    print("🔍 Testing Context Analysis...")
    
    try:
        from security_opt import SecurityPRReviewer
        
        # Set environment variables
        os.environ.setdefault('ENABLE_DETERMINISTIC_ANALYSIS', 'true')
        
        reviewer = SecurityPRReviewer(local_folder='.')
        
        # Test content with false positive variables
        test_content = '''
variable "secret_mode" {
  description = "UI display mode for secrets"
  type        = bool
  default     = false
}

variable "show_secret_fields" {
  description = "Whether to show secret input fields"
  type        = bool
  default     = true
}

variable "api_key_name" {
  description = "Name of the API key configuration"
  type        = string
  default     = "primary-api-key"
}

resource "azurerm_storage_account" "example" {
  name = "test"
  enable_https_traffic_only = false
}
'''
        
        # Test context analysis
        context_analysis = reviewer._analyze_code_context(test_content)
        
        print(f"   ✅ Variables analyzed: {len(context_analysis.get('variables', {}))}")
        print(f"   ✅ Secret-like variables: {len(context_analysis.get('secrets_analysis', {}))}")
        print(f"   ✅ False positive indicators: {len(context_analysis.get('false_positive_indicators', []))}")
        
        # Check for expected false positive detection
        secrets_analysis = context_analysis.get('secrets_analysis', {})
        false_positives_detected = sum(1 for analysis in secrets_analysis.values() 
                                     if analysis.get('likely_false_positive', False))
        
        print(f"   ✅ False positives detected: {false_positives_detected}")
        
        if false_positives_detected >= 2:
            print("   ✅ PASSED: Context analysis detecting false positives")
            return True
        else:
            print("   ⚠️  WARNING: Expected more false positive detection")
            return False
            
    except Exception as e:
        print(f"   ❌ ERROR: {str(e)}")
        return False

def test_deterministic_analysis():
    """Test deterministic analysis for consistency."""
    print("\n🔄 Testing Deterministic Analysis...")
    
    try:
        from security_opt import SecurityPRReviewer
        
        os.environ.setdefault('ENABLE_DETERMINISTIC_ANALYSIS', 'true')
        
        reviewer = SecurityPRReviewer(local_folder='.')
        
        test_content = '''
variable "secret_enabled" {
  type = bool
  default = false
}
'''
        
        # Run analysis multiple times
        results = []
        for i in range(3):
            context_analysis = reviewer._analyze_code_context(test_content)
            results.append(context_analysis)
        
        # Check consistency
        first_result = results[0]
        consistent = all(
            result.get('secrets_analysis', {}).keys() == first_result.get('secrets_analysis', {}).keys()
            for result in results[1:]
        )
        
        if consistent:
            print("   ✅ PASSED: Deterministic analysis is consistent")
            return True
        else:
            print("   ❌ FAILED: Analysis results are inconsistent")
            return False
            
    except Exception as e:
        print(f"   ❌ ERROR: {str(e)}")
        return False

def test_context_validation():
    """Test pre-analysis context validation."""
    print("\n🛡️  Testing Context Validation...")
    
    try:
        from security_opt import SecurityPRReviewer
        
        reviewer = SecurityPRReviewer(local_folder='.')
        
        # Test content with high false positive probability
        high_fp_content = '''
variable "secret_mode" { default = false }
variable "secret_display" { default = "hidden" }
variable "secret_ui_enabled" { default = true }
variable "show_secret_form" { default = false }
'''
        
        file_info = {
            "content": high_fp_content,
            "path": "test.tf",
            "parameters_used": {}
        }
        
        validation_result = reviewer._validate_security_context(file_info, [])
        
        print(f"   ✅ Validation completed")
        print(f"   ✅ Skip analysis: {validation_result.get('skip_analysis', False)}")
        print(f"   ✅ Context flags: {len(validation_result.get('context_flags', []))}")
        
        # Check if high false positive content is properly flagged
        if validation_result.get('skip_analysis') or validation_result.get('context_flags'):
            print("   ✅ PASSED: Context validation working")
            return True
        else:
            print("   ⚠️  WARNING: Expected more validation flags")
            return False
            
    except Exception as e:
        print(f"   ❌ ERROR: {str(e)}")
        return False

def test_semantic_analysis():
    """Test semantic variable analysis."""
    print("\n🧠 Testing Semantic Analysis...")

    try:
        from security_opt import SecurityPRReviewer

        os.environ.setdefault('ENABLE_DETERMINISTIC_ANALYSIS', 'true')

        reviewer = SecurityPRReviewer(local_folder='.')

        # Test variables with different semantic patterns
        variables = {
            "secret_mode": {
                "value": "false",
                "is_boolean": True,
                "usage_pattern": "configuration"
            },
            "api_key_name": {
                "value": "primary-key-config",
                "is_boolean": False,
                "usage_pattern": "naming"
            },
            "database_password": {
                "value": "hardcoded123",
                "is_boolean": False,
                "usage_pattern": "unknown"
            }
        }

        # Apply semantic analysis
        semantic_analysis = reviewer._apply_consistent_semantic_analysis(variables, "test content")

        print(f"   ✅ Variables analyzed: {len(semantic_analysis)}")

        # Check for proper classification
        false_positives = sum(1 for analysis in semantic_analysis.values()
                            if analysis.get('likely_false_positive', False))

        print(f"   ✅ False positives identified: {false_positives}")

        if false_positives >= 1:  # Should identify at least secret_mode as false positive
            print("   ✅ PASSED: Semantic analysis working")
            return True
        else:
            print("   ⚠️  WARNING: Expected semantic analysis to identify false positives")
            return False

    except Exception as e:
        print(f"   ❌ ERROR: {str(e)}")
        return False

def test_azure_guidance_integration():
    """Test Azure guidance integration from CSV."""
    print("\n📋 Testing Azure Guidance Integration...")

    try:
        from security_opt import SecurityPRReviewer

        reviewer = SecurityPRReviewer(local_folder='.')

        # Test loading CSV controls data
        csv_controls = reviewer._load_csv_controls_data()

        print(f"   ✅ CSV controls loaded: {len(csv_controls)}")

        # Test getting Azure guidance for a known control
        test_control_ids = ['IM-1', 'NS-1', 'DP-1']
        guidance_found = 0

        for control_id in test_control_ids:
            guidance = reviewer._get_azure_guidance_for_control(control_id)
            if guidance and guidance.get('guidance'):
                guidance_found += 1
                print(f"   ✅ Found guidance for {control_id}: {guidance['guidance'][:50]}...")

        if guidance_found >= 2:
            print("   ✅ PASSED: Azure guidance integration working")
            return True
        else:
            print("   ⚠️  WARNING: Expected more Azure guidance data")
            return False

    except Exception as e:
        print(f"   ❌ ERROR: {str(e)}")
        return False

def test_control_id_isolation():
    """Test control ID isolation to prevent AI from mixing control IDs."""
    print("\n🔒 Testing Control ID Isolation...")

    try:
        from security_opt import SecurityPRReviewer

        reviewer = SecurityPRReviewer(local_folder='.')

        # Test control IDs
        valid_control_ids = ['IM-1', 'IM-2', 'NS-1', 'NS-2', 'DP-1', 'DP-2']

        # Create isolation map
        isolation_map = reviewer._create_control_id_isolation_map(valid_control_ids)

        print(f"   ✅ Isolation map created for {len(isolation_map)} controls")

        # Test valid control IDs
        valid_tests = 0
        for control_id in ['IM-1', 'NS-1', 'DP-1']:
            result = reviewer._validate_control_id_isolation(control_id, isolation_map)
            if result['is_valid'] and result['is_exact_match']:
                valid_tests += 1
                print(f"   ✅ Valid ID '{control_id}': {result['message']}")

        # Test forbidden variations
        forbidden_tests = 0
        test_variations = ['IM-1a', 'NS-1.1', 'DP-1-modified']
        for variation in test_variations:
            result = reviewer._validate_control_id_isolation(variation, isolation_map)
            if result['is_forbidden_variation'] and result['corrected_id']:
                forbidden_tests += 1
                print(f"   ✅ Caught variation '{variation}': {result['message']}")

        # Test hybrid IDs
        hybrid_tests = 0
        test_hybrids = ['IM-1+NS-1', 'IM-1,NS-1', 'IM-1/NS-1']
        for hybrid in test_hybrids:
            result = reviewer._validate_control_id_isolation(hybrid, isolation_map)
            if result['is_hybrid']:
                hybrid_tests += 1
                print(f"   ✅ Caught hybrid '{hybrid}': {result['message']}")

        # Evaluate results
        if valid_tests >= 2 and forbidden_tests >= 2 and hybrid_tests >= 2:
            print("   ✅ PASSED: Control ID isolation working")
            return True
        else:
            print(f"   ⚠️  WARNING: Isolation tests incomplete (valid: {valid_tests}, forbidden: {forbidden_tests}, hybrid: {hybrid_tests})")
            return False

    except Exception as e:
        print(f"   ❌ ERROR: {str(e)}")
        return False

def main():
    """Run validation tests."""
    print("🧪 Validating IaC Guardian Prompt Optimizations")
    print("=" * 60)
    
    tests = [
        ("Context Analysis", test_context_analysis),
        ("Deterministic Analysis", test_deterministic_analysis),
        ("Context Validation", test_context_validation),
        ("Semantic Analysis", test_semantic_analysis),
        ("Azure Guidance Integration", test_azure_guidance_integration),
        ("Control ID Isolation", test_control_id_isolation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with error: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 VALIDATION SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    success_rate = passed / total if total > 0 else 0
    print(f"\nSuccess Rate: {success_rate:.2%} ({passed}/{total})")
    
    if success_rate >= 0.75:
        print("🎉 VALIDATION SUCCESSFUL: Optimizations are working!")
    elif success_rate >= 0.5:
        print("⚠️  VALIDATION PARTIAL: Some optimizations working")
    else:
        print("❌ VALIDATION FAILED: Optimizations need work")
    
    return success_rate >= 0.75

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
