#!/usr/bin/env python3
"""
IaC Guardian Master Controls Database Integration System
Consolidates multiple security framework data sources with AI-driven conflict resolution
"""

import json
import logging
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import hashlib

@dataclass
class ControlConflict:
    """Represents a conflict between control definitions from different sources"""
    control_id: str
    field_name: str
    source1: str
    value1: Any
    source2: str
    value2: Any
    confidence_score: float
    resolution_strategy: str

class MasterControlsDatabase:
    """
    Consolidated security controls database with AI-driven data merging
    and conflict resolution capabilities
    """
    
    def __init__(self, data_dir: Path = None):
        self.data_dir = data_dir or Path(__file__).parent
        self.logger = logging.getLogger(__name__)
        self.master_controls = {}
        self.data_sources = {}
        self.conflicts = []
        self.validation_rules = {}
        
    def load_data_sources(self) -> Dict[str, Any]:
        """Load all available data sources"""
        sources = {
            'azure_security_benchmark_csv': self._load_csv_sources(),
            'azure_security_benchmark_json': self._load_json_benchmark(),
            'enhanced_security_controls': self._load_enhanced_controls(),
            'mitre_attack_mappings': self._load_mitre_mappings(),
            'false_positive_rules': self._load_false_positive_rules(),
            'remediation_guidance': self._load_remediation_guidance()
        }
        
        self.data_sources = sources
        return sources
    
    def _load_csv_sources(self) -> Dict[str, pd.DataFrame]:
        """Load CSV data sources with enhanced error handling"""
        csv_sources = {}
        csv_files = [
            'identity_management.csv',
            'data_protection.csv', 
            'network_security_with_urls.csv'  # Using preferred file
        ]
        
        for csv_file in csv_files:
            try:
                file_path = self.data_dir.parent / 'benchmarks' / csv_file
                if file_path.exists():
                    df = pd.read_csv(file_path)
                    domain = csv_file.replace('.csv', '').replace('_with_urls', '')
                    csv_sources[domain] = df
                    self.logger.info(f"Loaded {len(df)} controls from {csv_file}")
                else:
                    self.logger.warning(f"CSV file not found: {file_path}")
            except Exception as e:
                self.logger.error(f"Error loading {csv_file}: {e}")
        
        return csv_sources
    
    def _load_json_benchmark(self) -> Dict[str, Any]:
        """Load Azure Security Benchmark JSON data"""
        try:
            json_path = self.data_dir.parent / 'benchmarks' / 'Azure_Security_Benchmark_v3.json'
            if json_path.exists():
                with open(json_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                self.logger.warning(f"JSON benchmark file not found: {json_path}")
                return {}
        except Exception as e:
            self.logger.error(f"Error loading JSON benchmark: {e}")
            return {}
    
    def _load_enhanced_controls(self) -> Dict[str, Any]:
        """Load enhanced security controls"""
        try:
            enhanced_path = self.data_dir / 'enhanced_security_controls.json'
            if enhanced_path.exists():
                with open(enhanced_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                self.logger.warning(f"Enhanced controls file not found: {enhanced_path}")
                return {}
        except Exception as e:
            self.logger.error(f"Error loading enhanced controls: {e}")
            return {}
    
    def _load_mitre_mappings(self) -> Dict[str, Any]:
        """Load MITRE ATT&CK mappings"""
        try:
            mitre_path = self.data_dir / 'mitre_attack_mappings.json'
            if mitre_path.exists():
                with open(mitre_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                self.logger.warning(f"MITRE mappings file not found: {mitre_path}")
                return {}
        except Exception as e:
            self.logger.error(f"Error loading MITRE mappings: {e}")
            return {}
    
    def _load_false_positive_rules(self) -> Dict[str, Any]:
        """Load false positive reduction rules"""
        try:
            fp_path = self.data_dir / 'false_positive_reduction_rules.json'
            if fp_path.exists():
                with open(fp_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                self.logger.warning(f"False positive rules file not found: {fp_path}")
                return {}
        except Exception as e:
            self.logger.error(f"Error loading false positive rules: {e}")
            return {}
    
    def _load_remediation_guidance(self) -> Dict[str, Any]:
        """Load remediation guidance database"""
        try:
            remediation_path = self.data_dir / 'remediation_guidance_database.json'
            if remediation_path.exists():
                with open(remediation_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                self.logger.warning(f"Remediation guidance file not found: {remediation_path}")
                return {}
        except Exception as e:
            self.logger.error(f"Error loading remediation guidance: {e}")
            return {}
    
    def merge_control_data(self, control_id: str) -> Dict[str, Any]:
        """
        Merge control data from multiple sources with AI-driven conflict resolution
        """
        merged_control = {
            'control_id': control_id,
            'sources': [],
            'confidence_score': 0,
            'last_updated': datetime.now().isoformat(),
            'data_hash': None
        }
        
        # Collect data from all sources
        csv_data = self._extract_csv_control_data(control_id)
        json_data = self._extract_json_control_data(control_id)
        enhanced_data = self._extract_enhanced_control_data(control_id)
        mitre_data = self._extract_mitre_control_data(control_id)
        
        # Merge with conflict resolution
        merged_control.update(self._resolve_conflicts([
            ('csv', csv_data),
            ('json', json_data), 
            ('enhanced', enhanced_data),
            ('mitre', mitre_data)
        ]))
        
        # Calculate confidence score
        merged_control['confidence_score'] = self._calculate_confidence_score(merged_control)
        
        # Generate data hash for change detection
        merged_control['data_hash'] = self._generate_data_hash(merged_control)
        
        return merged_control
    
    def _extract_csv_control_data(self, control_id: str) -> Dict[str, Any]:
        """Extract control data from CSV sources"""
        control_data = {}
        
        for domain, df in self.data_sources.get('azure_security_benchmark_csv', {}).items():
            if 'ASB ID' in df.columns:
                matching_rows = df[df['ASB ID'] == control_id]
                if not matching_rows.empty:
                    row = matching_rows.iloc[0]
                    control_data.update({
                        'name': row.get('Recommendation', ''),
                        'domain': row.get('Control Domain', ''),
                        'security_principle': row.get('Security Principle', ''),
                        'azure_guidance': row.get('Azure Guidance', ''),
                        'implementation_context': row.get('Implementation and additional context', ''),
                        'stakeholders': row.get('Customer Security Stakeholders', ''),
                        'azure_policy_mapping': row.get('Azure Policy Mapping', ''),
                        'source': f'csv_{domain}'
                    })
        
        return control_data
    
    def _extract_json_control_data(self, control_id: str) -> Dict[str, Any]:
        """Extract control data from JSON benchmark"""
        json_benchmark = self.data_sources.get('azure_security_benchmark_json', {})
        controls = json_benchmark.get('controls', [])
        
        for control in controls:
            if control.get('id') == control_id:
                return {
                    'name': control.get('name', ''),
                    'description': control.get('description', ''),
                    'severity': control.get('severity', ''),
                    'resource_types': control.get('resource_types', []),
                    'related_controls': control.get('related_controls', []),
                    'source': 'json_benchmark'
                }
        
        return {}
    
    def _extract_enhanced_control_data(self, control_id: str) -> Dict[str, Any]:
        """Extract control data from enhanced controls"""
        enhanced = self.data_sources.get('enhanced_security_controls', {})
        
        # Search in different framework sections
        for framework_section in ['azure_security_benchmark', 'extended_security_frameworks']:
            section_data = enhanced.get(framework_section, {})
            for domain, controls in section_data.items():
                if isinstance(controls, list):
                    for control in controls:
                        if control.get('id') == control_id:
                            return {
                                **control,
                                'source': f'enhanced_{framework_section}_{domain}'
                            }
        
        return {}
    
    def _extract_mitre_control_data(self, control_id: str) -> Dict[str, Any]:
        """Extract MITRE ATT&CK mappings for control"""
        mitre_data = self.data_sources.get('mitre_attack_mappings', {})
        tactics = mitre_data.get('tactics_and_techniques', {})
        
        mitre_mappings = []
        for tactic_name, tactic_data in tactics.items():
            techniques = tactic_data.get('techniques', [])
            for technique in techniques:
                related_controls = technique.get('related_asb_controls', [])
                if control_id in related_controls:
                    mitre_mappings.append({
                        'technique_id': technique.get('technique_id'),
                        'technique_name': technique.get('technique_name'),
                        'tactic': tactic_name,
                        'severity_impact': technique.get('severity_impact')
                    })
        
        return {'mitre_attack_techniques': mitre_mappings, 'source': 'mitre_mappings'} if mitre_mappings else {}
    
    def _resolve_conflicts(self, source_data_pairs: List[Tuple[str, Dict[str, Any]]]) -> Dict[str, Any]:
        """
        AI-driven conflict resolution between multiple data sources
        """
        resolved_data = {}
        field_sources = {}
        
        # Collect all fields and their sources
        for source_name, data in source_data_pairs:
            if not data:
                continue
                
            for field, value in data.items():
                if field == 'source':
                    continue
                    
                if field not in field_sources:
                    field_sources[field] = []
                field_sources[field].append((source_name, value))
        
        # Resolve conflicts for each field
        for field, source_values in field_sources.items():
            if len(source_values) == 1:
                # No conflict
                resolved_data[field] = source_values[0][1]
            else:
                # Conflict resolution needed
                resolved_value = self._apply_conflict_resolution_strategy(field, source_values)
                resolved_data[field] = resolved_value
        
        return resolved_data
    
    def _apply_conflict_resolution_strategy(self, field: str, source_values: List[Tuple[str, Any]]) -> Any:
        """
        Apply intelligent conflict resolution strategies based on field type and source priority
        """
        # Source priority (higher number = higher priority)
        source_priority = {
            'enhanced': 4,
            'csv': 3,
            'json': 2,
            'mitre': 1
        }
        
        # Field-specific resolution strategies
        if field in ['severity', 'confidence_baseline']:
            # For severity and confidence, use highest priority source
            return max(source_values, key=lambda x: source_priority.get(x[0].split('_')[0], 0))[1]
        
        elif field in ['name', 'description']:
            # For text fields, prefer non-empty values from high-priority sources
            non_empty = [(s, v) for s, v in source_values if v and str(v).strip()]
            if non_empty:
                return max(non_empty, key=lambda x: source_priority.get(x[0].split('_')[0], 0))[1]
        
        elif field in ['mitre_attack_techniques', 'compliance_frameworks', 'azure_policy_definitions']:
            # For arrays, merge unique values
            merged_array = []
            for _, value in source_values:
                if isinstance(value, list):
                    merged_array.extend(value)
                elif value:
                    merged_array.append(value)
            return list(set(merged_array)) if merged_array else []
        
        # Default: use highest priority source
        return max(source_values, key=lambda x: source_priority.get(x[0].split('_')[0], 0))[1]
    
    def _calculate_confidence_score(self, control_data: Dict[str, Any]) -> int:
        """Calculate confidence score based on data completeness and source quality"""
        score = 50  # Base score
        
        # Boost for multiple sources
        source_count = len([k for k in control_data.keys() if k.endswith('_source')])
        score += min(source_count * 10, 30)
        
        # Boost for complete data
        required_fields = ['name', 'domain', 'severity']
        complete_fields = sum(1 for field in required_fields if control_data.get(field))
        score += (complete_fields / len(required_fields)) * 20
        
        return min(score, 100)
    
    def _generate_data_hash(self, control_data: Dict[str, Any]) -> str:
        """Generate hash for change detection"""
        # Remove dynamic fields
        static_data = {k: v for k, v in control_data.items() 
                      if k not in ['last_updated', 'data_hash', 'confidence_score']}
        
        data_string = json.dumps(static_data, sort_keys=True)
        return hashlib.sha256(data_string.encode()).hexdigest()[:16]
    
    def build_master_database(self) -> Dict[str, Any]:
        """Build the complete master controls database"""
        self.logger.info("Building master controls database...")
        
        # Load all data sources
        self.load_data_sources()
        
        # Get all unique control IDs
        all_control_ids = set()
        
        # From CSV sources
        for domain, df in self.data_sources.get('azure_security_benchmark_csv', {}).items():
            if 'ASB ID' in df.columns:
                all_control_ids.update(df['ASB ID'].dropna().unique())
        
        # From JSON benchmark
        json_controls = self.data_sources.get('azure_security_benchmark_json', {}).get('controls', [])
        all_control_ids.update(control.get('id') for control in json_controls if control.get('id'))
        
        # From enhanced controls
        enhanced = self.data_sources.get('enhanced_security_controls', {})
        for framework_section in enhanced.values():
            if isinstance(framework_section, dict):
                for domain_controls in framework_section.values():
                    if isinstance(domain_controls, list):
                        all_control_ids.update(control.get('id') for control in domain_controls if control.get('id'))
        
        # Build master database
        master_db = {
            'metadata': {
                'version': '4.0',
                'created_date': datetime.now().isoformat(),
                'total_controls': len(all_control_ids),
                'data_sources': list(self.data_sources.keys()),
                'conflicts_resolved': len(self.conflicts)
            },
            'controls': {}
        }
        
        for control_id in sorted(all_control_ids):
            if control_id:  # Skip empty control IDs
                master_db['controls'][control_id] = self.merge_control_data(control_id)
        
        self.master_controls = master_db
        self.logger.info(f"Master database built with {len(all_control_ids)} controls")
        
        return master_db
    
    def save_master_database(self, output_path: Path = None) -> Path:
        """Save the master database to file"""
        if not self.master_controls:
            self.build_master_database()
        
        output_path = output_path or (self.data_dir / 'master_controls_database.json')
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.master_controls, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Master database saved to {output_path}")
        return output_path

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    # Build and save master database
    db = MasterControlsDatabase()
    master_db = db.build_master_database()
    output_path = db.save_master_database()
    
    print(f"Master controls database created with {len(master_db['controls'])} controls")
    print(f"Saved to: {output_path}")
