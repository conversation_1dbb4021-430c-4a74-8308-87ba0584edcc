<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Assessment Report - IaC Guardian</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            /* Modern Color Palette */
            --primary-blue: #1e40af;
            --primary-blue-light: #3b82f6;
            --secondary-blue: #0ea5e9;
            --accent-purple: #8b5cf6;
            --success-green: #10b981;
            --warning-amber: #f59e0b;
            --danger-red: #ef4444;
            --info-cyan: #06b6d4;

            /* Neutral Colors */
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;

            /* Semantic Colors */
            --critical-bg: #fef2f2;
            --critical-border: #fecaca;
            --critical-text: #dc2626;
            --high-bg: #fffbeb;
            --high-border: #fed7aa;
            --high-text: #ea580c;
            --medium-bg: #fefce8;
            --medium-border: #fde68a;
            --medium-text: #ca8a04;
            --low-bg: #f0f9ff;
            --low-border: #bae6fd;
            --low-text: #0284c7;

            /* Layout */
            --max-width: 1400px;
            --border-radius: 12px;
            --border-radius-sm: 8px;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--gray-700);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-size: 14px;
        }

        .main-container {
            max-width: var(--max-width);
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        /* Header Section */
        .report-header {
            background: white;
            border-radius: var(--border-radius);
            padding: 3rem 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-lg);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .report-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue), var(--accent-purple));
        }

        .report-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
        }

        .report-subtitle {
            font-size: 1.125rem;
            color: var(--gray-600);
            font-weight: 400;
            margin-bottom: 1rem;
        }

        .report-meta {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 1.5rem;
            font-size: 0.875rem;
            color: var(--gray-500);
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* Controls Section */
        .controls-section {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }

        .controls-grid {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 1.5rem;
            align-items: center;
        }

        .search-container {
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius-sm);
            font-size: 0.875rem;
            transition: all 0.2s ease;
            background: var(--gray-50);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
        }

        .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
        }

        .filter-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 0.5rem 1rem;
            border: 2px solid transparent;
            border-radius: 2rem;
            background: var(--gray-100);
            color: var(--gray-600);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            white-space: nowrap;
        }

        .filter-btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .filter-btn.active {
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .filter-btn.all.active { background: var(--primary-blue); }
        .filter-btn.critical.active { background: var(--danger-red); }
        .filter-btn.high.active { background: var(--warning-amber); }
        .filter-btn.medium.active { background: var(--medium-text); }
        .filter-btn.low.active { background: var(--info-cyan); }

        /* Summary Section */
        .summary-section {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }

        .summary-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-blue);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--gray-600);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .severity-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .severity-badge {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .severity-badge:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .severity-badge.critical {
            background: var(--critical-bg);
            border: 1px solid var(--critical-border);
            color: var(--critical-text);
        }

        .severity-badge.high {
            background: var(--high-bg);
            border: 1px solid var(--high-border);
            color: var(--high-text);
        }

        .severity-badge.medium {
            background: var(--medium-bg);
            border: 1px solid var(--medium-border);
            color: var(--medium-text);
        }

        .severity-badge.low {
            background: var(--low-bg);
            border: 1px solid var(--low-border);
            color: var(--low-text);
        }

        .severity-count {
            font-size: 1.25rem;
            font-weight: 700;
        }

        .severity-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* Findings Section */
        .findings-container {
            margin-bottom: 2rem;
        }

        .severity-group {
            background: white;
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
            box-shadow: var(--shadow);
            overflow: hidden;
            border: 1px solid var(--gray-200);
        }

        .severity-group:last-child {
            margin-bottom: 0;
        }

        .severity-header {
            padding: 1.25rem 1.5rem;
            font-weight: 600;
            font-size: 1.125rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 1px solid var(--gray-200);
        }

        .severity-header:hover {
            background: var(--gray-50);
        }

        .severity-header-left {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .severity-icon {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.875rem;
        }

        .severity-header.critical {
            background: var(--critical-bg);
            color: var(--critical-text);
            border-left: 4px solid var(--critical-text);
        }

        .severity-header.critical .severity-icon {
            background: var(--critical-text);
        }

        .severity-header.high {
            background: var(--high-bg);
            color: var(--high-text);
            border-left: 4px solid var(--high-text);
        }

        .severity-header.high .severity-icon {
            background: var(--high-text);
        }

        .severity-header.medium {
            background: var(--medium-bg);
            color: var(--medium-text);
            border-left: 4px solid var(--medium-text);
        }

        .severity-header.medium .severity-icon {
            background: var(--medium-text);
        }

        .severity-header.low {
            background: var(--low-bg);
            color: var(--low-text);
            border-left: 4px solid var(--low-text);
        }

        .severity-header.low .severity-icon {
            background: var(--low-text);
        }

        .severity-title {
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .severity-count {
            background: rgba(255, 255, 255, 0.9);
            color: inherit;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 700;
            margin-left: 0.5rem;
        }

        .toggle-icon {
            transition: transform 0.2s ease;
            color: var(--gray-500);
        }

        .severity-header.collapsed .toggle-icon {
            transform: rotate(-90deg);
        }

        .findings-list {
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .findings-list.collapsed {
            max-height: 0;
        }

        .finding-item {
            border-bottom: 1px solid var(--gray-200);
            padding: 1.5rem;
            transition: all 0.2s ease;
            background: white;
        }

        .finding-item:last-child {
            border-bottom: none;
        }

        .finding-item:hover {
            background: var(--gray-50);
        }

        .finding-header {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .finding-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
            flex-shrink: 0;
        }

        .finding-icon.critical { background: var(--danger-red); }
        .finding-icon.high { background: var(--warning-amber); }
        .finding-icon.medium { background: var(--medium-text); }
        .finding-icon.low { background: var(--info-cyan); }

        .finding-content {
            flex: 1;
            min-width: 0;
        }

        .finding-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            flex-wrap: wrap;
        }

        .control-id {
            background: var(--primary-blue);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .finding-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1rem;
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.375rem;
        }

        .meta-icon {
            color: var(--gray-400);
            width: 1rem;
        }

        .finding-description {
            color: var(--gray-700);
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .remediation-section {
            background: var(--success-green);
            background: linear-gradient(135deg, var(--success-green) 0%, #059669 100%);
            color: white;
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            margin-top: 1rem;
        }

        .remediation-title {
            font-weight: 600;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .remediation-content {
            font-size: 0.875rem;
            line-height: 1.5;
            opacity: 0.95;
        }

        .code-snippet {
            background: var(--gray-900);
            color: var(--gray-100);
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 0.8125rem;
            line-height: 1.5;
            white-space: pre-wrap;
            margin: 1rem 0;
            overflow-x: auto;
            border: 1px solid var(--gray-700);
        }

        .no-findings {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--gray-500);
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }

        .no-findings-icon {
            font-size: 3rem;
            color: var(--gray-300);
            margin-bottom: 1rem;
        }

        .no-findings h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
        }

        /* Footer */
        .report-footer {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-top: 2rem;
            box-shadow: var(--shadow);
            text-align: center;
        }

        .export-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }

        .export-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius-sm);
            background: var(--primary-blue);
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.875rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .export-btn:hover {
            background: var(--primary-blue-light);
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .footer-info {
            color: var(--gray-600);
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .footer-info strong {
            color: var(--gray-900);
        }

        /* Responsive Design */

        /* Large Desktop (1200px+) */
        @media (min-width: 1200px) {
            .main-container {
                padding: 3rem 2rem;
            }

            .stats-grid {
                grid-template-columns: repeat(4, 1fr);
            }

            .severity-overview {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        /* Desktop (992px - 1199px) */
        @media (min-width: 992px) and (max-width: 1199px) {
            .main-container {
                padding: 2rem 1.5rem;
            }

            .report-title {
                font-size: 2.25rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .severity-overview {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Tablet (768px - 991px) */
        @media (min-width: 768px) and (max-width: 991px) {
            .main-container {
                padding: 1.5rem 1rem;
            }

            .report-header {
                padding: 2rem 1.5rem;
            }

            .report-title {
                font-size: 2rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .report-meta {
                flex-direction: column;
                gap: 0.5rem;
            }

            .controls-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .filter-buttons {
                justify-content: center;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .severity-overview {
                grid-template-columns: repeat(2, 1fr);
            }

            .finding-title {
                font-size: 1rem;
            }

            .finding-meta {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        /* Mobile Large (576px - 767px) */
        @media (min-width: 576px) and (max-width: 767px) {
            .main-container {
                padding: 1rem 0.75rem;
            }

            .report-header {
                padding: 1.5rem 1rem;
            }

            .report-title {
                font-size: 1.75rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .report-subtitle {
                font-size: 1rem;
            }

            .report-meta {
                flex-direction: column;
                gap: 0.25rem;
                font-size: 0.8125rem;
            }

            .controls-section {
                padding: 1rem;
            }

            .controls-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .filter-buttons {
                justify-content: center;
                gap: 0.375rem;
            }

            .filter-btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.6875rem;
            }

            .summary-section {
                padding: 1.5rem 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .severity-overview {
                grid-template-columns: 1fr;
            }

            .finding-item {
                padding: 1rem;
            }

            .finding-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.75rem;
            }

            .finding-title {
                font-size: 0.9375rem;
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .export-actions {
                flex-direction: column;
                align-items: center;
            }
        }

        /* Mobile Small (up to 575px) */
        @media (max-width: 575px) {
            .main-container {
                padding: 0.75rem 0.5rem;
            }

            .report-header {
                padding: 1.25rem 0.75rem;
                margin-bottom: 1rem;
            }

            .report-title {
                font-size: 1.5rem;
                flex-direction: column;
                gap: 0.375rem;
            }

            .report-subtitle {
                font-size: 0.9375rem;
            }

            .report-meta {
                flex-direction: column;
                gap: 0.25rem;
                font-size: 0.75rem;
            }

            .controls-section {
                padding: 0.75rem;
            }

            .search-input {
                font-size: 1rem; /* Prevents zoom on iOS */
                padding: 0.75rem 1rem 0.75rem 2.25rem;
            }

            .filter-buttons {
                gap: 0.25rem;
            }

            .filter-btn {
                padding: 0.375rem 0.625rem;
                font-size: 0.625rem;
                min-width: auto;
            }

            .summary-section {
                padding: 1.25rem 0.75rem;
            }

            .summary-title {
                font-size: 1.25rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            .stat-card {
                padding: 1rem;
            }

            .stat-number {
                font-size: 2rem;
            }

            .severity-overview {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .severity-header {
                padding: 1rem;
                font-size: 1rem;
            }

            .finding-item {
                padding: 0.75rem;
            }

            .finding-icon {
                width: 2rem;
                height: 2rem;
                font-size: 0.875rem;
            }

            .finding-title {
                font-size: 0.875rem;
            }

            .control-id {
                font-size: 0.6875rem;
                padding: 0.1875rem 0.5rem;
            }

            .finding-meta {
                font-size: 0.8125rem;
            }

            .finding-description {
                font-size: 0.875rem;
            }

            .code-snippet {
                font-size: 0.75rem;
                padding: 0.75rem;
            }

            .export-actions {
                flex-direction: column;
                gap: 0.5rem;
            }

            .export-btn {
                width: 100%;
                justify-content: center;
            }
        }

        /* Print Styles */
        @media print {
            body {
                background: white !important;
            }

            .main-container {
                max-width: none;
                padding: 0;
            }

            .report-header,
            .controls-section,
            .summary-section,
            .severity-group,
            .report-footer {
                box-shadow: none !important;
                break-inside: avoid;
            }

            .controls-section,
            .export-actions {
                display: none !important;
            }

            .findings-list {
                max-height: none !important;
            }
        }
    </style>

    <script>
        // Modern JavaScript for enhanced interactivity
        let searchTimeout;
        let allFindings = [];
        let currentFilter = 'all';

        document.addEventListener('DOMContentLoaded', function() {
            initializeReport();
            setupEventListeners();
            loadFindings();
        });

        function initializeReport() {
            // Initialize filter buttons
            const filterButtons = document.querySelectorAll('.filter-btn');
            filterButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    setActiveFilter(this.dataset.severity);
                });
            });

            // Initialize search
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.addEventListener('input', debounceSearch);
            }

            // Initialize collapsible sections
            const severityHeaders = document.querySelectorAll('.severity-header');
            severityHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    toggleSeverityGroup(this);
                });
            });
        }

        function setupEventListeners() {
            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    clearSearch();
                }
                if (e.ctrlKey && e.key === 'f') {
                    e.preventDefault();
                    focusSearch();
                }
            });
        }

        function debounceSearch() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch();
            }, 300);
        }

        function performSearch() {
            const searchTerm = document.querySelector('.search-input').value.toLowerCase();
            const findings = document.querySelectorAll('.finding-item');
            let visibleCount = 0;

            findings.forEach(finding => {
                const text = finding.textContent.toLowerCase();
                const isVisible = text.includes(searchTerm);
                finding.style.display = isVisible ? 'block' : 'none';
                if (isVisible) visibleCount++;
            });

            updateNoResultsMessage(visibleCount === 0 && searchTerm.length > 0);
        }

        function setActiveFilter(severity) {
            currentFilter = severity;

            // Update button states
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-severity="${severity}"]`).classList.add('active');

            // Filter findings
            const severityGroups = document.querySelectorAll('.severity-group');
            severityGroups.forEach(group => {
                const groupSeverity = group.dataset.severity;
                if (severity === 'all' || groupSeverity === severity) {
                    group.style.display = 'block';
                } else {
                    group.style.display = 'none';
                }
            });

            // Update URL hash for bookmarking
            window.location.hash = severity === 'all' ? '' : severity;
        }

        function toggleSeverityGroup(header) {
            const group = header.parentElement;
            const findingsList = group.querySelector('.findings-list');
            const isCollapsed = header.classList.contains('collapsed');

            if (isCollapsed) {
                header.classList.remove('collapsed');
                findingsList.classList.remove('collapsed');
                findingsList.style.maxHeight = findingsList.scrollHeight + 'px';
            } else {
                header.classList.add('collapsed');
                findingsList.classList.add('collapsed');
                findingsList.style.maxHeight = '0';
            }
        }

        function clearSearch() {
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.value = '';
                performSearch();
            }
        }

        function focusSearch() {
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }

        function updateNoResultsMessage(show) {
            const noResults = document.querySelector('.no-findings');
            if (noResults) {
                noResults.style.display = show ? 'block' : 'none';
            }
        }

        function exportToJson() {
            const data = {
                timestamp: new Date().toISOString(),
                findings: allFindings,
                summary: {
                    total: allFindings.length,
                    critical: allFindings.filter(f => f.severity === 'CRITICAL').length,
                    high: allFindings.filter(f => f.severity === 'HIGH').length,
                    medium: allFindings.filter(f => f.severity === 'MEDIUM').length,
                    low: allFindings.filter(f => f.severity === 'LOW').length
                }
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], {
                type: 'application/json'
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `security-findings-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function loadFindings() {
            // This would be populated with actual findings data
            allFindings = [];
        }

        // Initialize filter from URL hash
        window.addEventListener('load', function() {
            const hash = window.location.hash.substring(1);
            if (hash && ['critical', 'high', 'medium', 'low'].includes(hash)) {
                setActiveFilter(hash);
            }
        });
    </script>
</head>

<body>
    <div class="main-container">
        <!-- Header Section -->
        <header class="report-header">
            <h1 class="report-title">
                <i class="fas fa-shield-alt"></i>
                Security Assessment Report
            </h1>
            <p class="report-subtitle">Infrastructure as Code Security Analysis</p>
            <div class="report-meta">
                <div class="meta-item">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Generated: {timestamp}</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-robot"></i>
                    <span>IaC Guardian</span>
                </div>
            </div>
        </header>

        <!-- Controls Section -->
        <section class="controls-section">
            <div class="controls-grid">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search findings..." aria-label="Search findings">
                    <i class="fas fa-search search-icon"></i>
                </div>
                <div class="filter-buttons">
                    <button class="filter-btn all active" data-severity="all">All</button>
                    <button class="filter-btn critical" data-severity="critical">Critical</button>
                    <button class="filter-btn high" data-severity="high">High</button>
                    <button class="filter-btn medium" data-severity="medium">Medium</button>
                    <button class="filter-btn low" data-severity="low">Low</button>
                </div>
            </div>
        </section>

        
        <!-- Summary Section -->
        <section class="summary-section">
            <h2 class="summary-title">
                <i class="fas fa-chart-bar"></i>
                Executive Summary
            </h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">91</div>
                    <div class="stat-label">Total Findings</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">13</div>
                    <div class="stat-label">Files Affected</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">46</div>
                    <div class="stat-label">High Priority Issues</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">28</div>
                    <div class="stat-label">Security Controls</div>
                </div>
            </div>
            <div class="severity-overview">
                <div class="severity-badge critical">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        Critical
                    </div>
                    <div class="severity-count">9</div>
                </div>
                <div class="severity-badge high">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        High
                    </div>
                    <div class="severity-count">37</div>
                </div>
                <div class="severity-badge medium">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        Medium
                    </div>
                    <div class="severity-count">31</div>
                </div>
                <div class="severity-badge low">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        Low
                    </div>
                    <div class="severity-count">14</div>
                </div>
            </div>
        </section>

        <div class="findings-container">
            
        <section class="severity-group" data-severity="critical">
            <header class="severity-header critical">
                <div class="severity-header-left">
                    <div class="severity-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="severity-title">Critical Severity</div>
                    <div class="severity-count">9</div>
                </div>
                <i class="fas fa-chevron-down toggle-icon"></i>
            </header>
            <div class="findings-list">
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon critical">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>IngestionStorageAccount.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 37</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The template does not restrict public endpoints for the storage accounts. When network rules are not defined, storage accounts may be accessible via public endpoints, increasing risk of exposure.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Explicitly configure &#x27;networkAcls&#x27; to deny public network access by setting &#x27;defaultAction&#x27; to &#x27;Deny&#x27;, and limit access to private endpoints or specific IPs.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon critical">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGeo.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 160</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The CosmosDB resource (Microsoft.DocumentDB/databaseAccounts) has &quot;publicNetworkAccess&quot; set to &quot;Enabled&quot; and &quot;isVirtualNetworkFilterEnabled&quot; set to false. No virtual network rules or NSGs are defined to limit access. This exposes the CosmosDB instance to the public internet, violating requirements for protecting critical resources.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Set &quot;publicNetworkAccess&quot; to &quot;Disabled&quot; or enable virtual network filtering (&quot;isVirtualNetworkFilterEnabled&quot;: true) and define appropriate virtualNetworkRules restricting access to trusted subnets. Implement NSGs where appropriate to further restrict access.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon critical">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGeo.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 160</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        CosmosDB is deployed with public network access enabled (&quot;publicNetworkAccess&quot;: &quot;Enabled&quot;) and no IP allowlist (&quot;ipRules&quot;: []). This exposes the database to all public IPs with no restriction, increasing risk of network-based attacks or unauthorized access.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Configure CosmosDB to use private endpoints or at minimum restrict public access via IP allowlist. Set &quot;publicNetworkAccess&quot; to &quot;Disabled&quot; and define private endpoints and/or IP rules to allow only known trusted sources.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon critical">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-13</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGeo.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 160</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        No private endpoints (Microsoft.Network/privateEndpoints) are provisioned for CosmosDB or Key Vault. Relying solely on public endpoints exposes these sensitive resources to broader Azure network attack surface, violating the principle that access to such critical services should be restricted via private networking.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Provision Azure Private Endpoints for CosmosDB and Key Vault, associate them with the correct VNets/subnets, and enforce that access is only allowed through these endpoints.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon critical">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGlobal.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 49</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The CosmosDB account enables &#x27;publicNetworkAccess&#x27; (&quot;publicNetworkAccess&quot;: &quot;Enabled&quot;) and explicitly disables virtual network filters (&#x27;isVirtualNetworkFilterEnabled&#x27;: false), meaning the database is exposed to the public internet without restriction. This poses a high risk of unauthorized access.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Set &#x27;publicNetworkAccess&#x27; to &#x27;Disabled&#x27; and &#x27;isVirtualNetworkFilterEnabled&#x27; to true to restrict access to private endpoints or trusted networks only. Use Azure Private Endpoints where possible.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon critical">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGlobal.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 133</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Key Vault resource does not specify &#x27;networkAcls&#x27; or private endpoint settings and leaves default network access posture, allowing public access by default. This exposes secrets to potential breaches from the public network.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Set &#x27;networkAcls.bypass&#x27; to &#x27;AzureServices&#x27; or &#x27;None&#x27;, add explicit &#x27;networkAcls&#x27; to restrict access to trusted VNets, and/or configure Private Endpoints for Key Vault.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon critical">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpRegion.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1070</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The CosmosDB account (&#x27;Microsoft.DocumentDB/databaseAccounts&#x27;) is configured with &#x27;publicNetworkAccess&#x27;: &#x27;Enabled&#x27; and &#x27;isVirtualNetworkFilterEnabled&#x27;: false, exposing the database to the public internet without any network restrictions or private endpoints, in violation of network security best practices.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Set &#x27;isVirtualNetworkFilterEnabled&#x27; to true, restrict access with virtual network rules, and/or disable public network access. Consider using private endpoints to ensure only trusted networks can reach the CosmosDB resource.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon critical">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpRegion.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1070</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        CosmosDB account is exposed with public network access (&#x27;publicNetworkAccess&#x27;: &#x27;Enabled&#x27;), making it accessible from the public internet, which increases the risk of data exposure or exploitation.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Set &#x27;publicNetworkAccess&#x27; to &#x27;Disabled&#x27; to block all public network access. Use private endpoints or configured IP rules to explicitly authorize trusted sources.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon critical">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-14</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpRegion.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1070</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        No &#x27;privateEndpointConnections&#x27; or private endpoint configuration is present on the CosmosDB resource, which means connectivity is not limited to private Azure networks.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Configure a private endpoint for the CosmosDB account to restrict network access to Azure virtual networks only.
                        </div>
                    </div>
                </article>
            </div>
        </section>
        <section class="severity-group" data-severity="high">
            <header class="severity-header high">
                <div class="severity-header-left">
                    <div class="severity-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="severity-title">High Severity</div>
                    <div class="severity-count">37</div>
                </div>
                <i class="fas fa-chevron-down toggle-icon"></i>
            </header>
            <div class="findings-list">
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>IngestionStorageAccount.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 37</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The storage account resources lack network rules restricting access (e.g., no &#x27;networkAcls&#x27; property is specified to restrict access to specific virtual networks or selected IP ranges). Without such rules, the account could be exposed to the public internet, violating the need for network security boundaries.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Add the &#x27;networkAcls&#x27; property to each storage account resource to restrict access to trusted virtual networks and/or specific IP addresses. Consider leveraging private endpoints for additional protection.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpBilling.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The Storage Account resources do not have any network security controls such as Virtual Network (VNet) integration or private endpoints. No NSGs, service endpoints, or firewall rules restricting access are defined, potentially exposing storage accounts to the public internet.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Use network rules to restrict Storage Account access only to required networks and trusted services. Configure Storage Account firewall settings to allow only selected VNets/subnets or private endpoints, and deny public network access. Apply NSGs at the subnet level where storage endpoints reside.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpBilling.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Storage Accounts may be exposed via public endpoints because the template does not explicitly restrict public network access, nor does it provision private endpoints. This increases the attack surface.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Set the Storage Account property &#x27;publicNetworkAccess&#x27; to &#x27;Disabled&#x27; to prevent public access to storage. Implement private endpoints to access Storage Accounts securely over Azure&#x27;s backbone.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">IM-13</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpBilling.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Role assignments for both the Usage Billing Account and Storage Accounts are created and directly reference a user-assigned managed identity (UAMI) and use hard-coded role definition IDs. There is no process or parameterization ensuring that only necessary permissions are granted for these roles. Overbroad role assignments may result in unnecessary privilege escalation.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Carefully review and enforce least-privilege RBAC role assignments. Where possible, restrict the scope to only what is required (resource or resource group-level), and parameterize/validate role assignment IDs and principals. Regularly audit and attest assigned roles.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpBillingExhaust.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 28</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The Kusto (Azure Data Explorer) cluster is deployed without any network restrictions such as private endpoints, IP allowlists, VNet integration, or restricted firewall rules. This can result in the cluster being accessible from the public internet, violating the requirement to protect public endpoints.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Restrict public access to the Kusto cluster by enabling VNet integration or Private endpoints, setting up firewall rules to allow access only from trusted IP ranges, and/or disabling public network access completely.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpBillingExhaust.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 28</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        There are no network security groups (NSGs) or Azure Firewall configured for the Kusto cluster or related resources. This exposes the resource to unfiltered inbound connections and potential unauthorized access over the network.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Deploy the Kusto cluster within a subnet protected by NSGs or behind an Azure Firewall. Define rules to allow only required traffic from trusted sources.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpBillingExhaustExport.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 12</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Sensitive information such as &#x27;adxExhaustDataIngestionUri&#x27; and &#x27;adxExhaustUri&#x27; are passed as plain string parameters. These may contain secrets, connection strings, or sensitive endpoints, stored in plaintext within the deployment template or parameter file. This violates the requirement to manage sensitive information disclosure.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Store sensitive parameters containing secrets, connection strings, or URIs in an Azure Key Vault and reference them using secure parameters/Key Vault references in the ARM template. Avoid including sensitive values directly or as plain type parameters.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpBillingExhaustExport.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 56</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Potential public exposure of Azure Data Explorer (ADX) endpoints (&#x27;adxExhaustUri&#x27; and &#x27;adxExhaustDataIngestionUri&#x27;) as there are no restrictions, private endpoint configuration, or firewall rules specified. This increases the risk of data exfiltration or unauthorized access to sensitive data.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Ensure that ADX endpoints used in the template are restricted to private endpoints or virtual networks, and configure firewall rules to allow only trusted connections. Do not expose ingestion or data query URIs to public networks unless required and mitigate risk with additional controls.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-14</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGeo.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 160</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        CosmosDB is deployed without virtual network service endpoints (&quot;isVirtualNetworkFilterEnabled&quot;: false) and no virtualNetworkRules are defined. Service endpoints help secure traffic between VNets and Azure services.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Enable virtual network filtering (&quot;isVirtualNetworkFilterEnabled&quot;: true) for CosmosDB and define appropriate &quot;virtualNetworkRules&quot; that allow access only from trusted and required subnets.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-6</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGeo.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 160</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The CosmosDB configuration does not reference or enable customer-managed keys (CMK) for encryption at rest. Without CMK, the organization cannot control or rotate its own encryption keys for this sensitive database.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Update the CosmosDB resource properties to reference a Key Vault and configure customer-managed keys (CMK) for at-rest encryption.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-8</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGeo.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 48</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The Key Vault is deployed without any network-level restrictions. By default, this allows access from any source, including the public internet, unless explicitly restricted.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Enable Key Vault firewall and virtual network rules to restrict access to trusted networks or set up Private Endpoints for the Key Vault.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGeo.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 162</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        CosmosDB resource defers &quot;minimalTlsVersion&quot; to a parameter (&quot;REQUIRED_PARAM_minimalCosmosDbTlsVersion&quot;). If this is not explicitly set to at least &#x27;Tls1_2&#x27;, encryption in transit may not meet benchmark requirements.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Explicitly set the &quot;minimalTlsVersion&quot; property to at least &#x27;Tls1_2&#x27; for CosmosDB. Validate the parameter at deployment time to disallow insecure protocols.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-5</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGlobal.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 49</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        CosmosDB public exposure without Network Security Group or Private Endpoint. The resource does not use Private Endpoints, leaving traffic exposed to the public internet.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Configure and require a Private Endpoint for the CosmosDB account to ensure all access occurs over the Azure backbone, not the public internet.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-6</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGlobal.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 49</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        CosmosDB is not configured with service endpoints or virtual network filtering. This exposes CosmosDB to traffic from any network.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Enable virtual network filtering and configure Azure Virtual Network Service Endpoints to restrict access to trusted VNets only.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-5</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGlobal.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 133</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Key Vault is accessible without a Private Endpoint or networkAcls restrictions, making it reachable from any public endpoint.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Configure a Private Endpoint and/or restrict public network access to Key Vault. Apply &#x27;networkAcls&#x27; to permit only necessary traffic from trusted subnets.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGlobal.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 110</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Storage Accounts do not specify &#x27;networkAcls&#x27; or private endpoints and have default network access (no restriction), which makes the accounts accessible from the public internet.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Restrict public network access to the storage account by configuring &#x27;networkAcls&#x27; to allow only trusted virtual network subnets or by requiring Private Endpoints.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpRegion.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1097</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Key Vault (&#x27;Microsoft.KeyVault/vaults&#x27;) does not specify &#x27;networkAcls&#x27; to restrict access and defaults to being accessible from any address (public internet). By default, without network ACLs or private endpoint configuration, Key Vault can be reached over the internet.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Add &#x27;networkAcls&#x27; to restrict access to specific virtual networks/subnets and consider adding private endpoints to remove public internet exposure.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpRegion.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1097</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Azure Key Vault is not network restricted (no &#x27;networkAcls&#x27;) and may be accessible publicly. Sensitive secrets and keys could be exposed to the public internet.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Configure &#x27;networkAcls&#x27; on the Key Vault to only permit traffic from trusted VNets/subnets and set &#x27;defaultAction&#x27; to &#x27;Deny&#x27;. Deploy a private endpoint and disable public network access.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-14</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpRegion.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1097</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Azure Key Vault is missing a private endpoint configuration, risking exposure over the public internet.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Implement a private endpoint for the Key Vault to ensure it is only accessible over private Azure networks.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpRegion.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 672</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Storage accounts are deployed without explicit network restrictions (such as &#x27;networkAcls&#x27;, &#x27;virtualNetworkRules&#x27;, or private endpoint resources). By default, storage accounts allow access from all networks unless explicitly restricted.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Add &#x27;networkAcls&#x27; to explicitly restrict storage account access to trusted networks. Use private endpoints to prevent public access.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpRegion.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 672</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Storage accounts do not have &#x27;networkAcls&#x27; defined and there are no private endpoints, meaning they may be publicly accessible if not otherwise restricted at account or tenant level.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Configure &#x27;networkAcls&#x27; to block public access, only allowing approved Azure services/networks, and require the use of private endpoints for all storage account access.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-14</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpRegion.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 672</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Storage accounts lack private endpoint configurations, which is recommended for eliminating public exposure for storage services.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Add private endpoints for each storage account to ensure all access occurs over private connections.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-12</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpRegion.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1070</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The CosmosDB databaseAccount does not specify &#x27;keyVaultKeyUri&#x27; or any parameterization for Customer-Managed Key (CMK) encryption, defaulting to Microsoft-managed keys. This does not meet the requirement for customer-controlled encryption for highly sensitive data.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Configure CosmosDB to use customer-managed keys (CMK) by referencing a Key Vault key in the &#x27;keyVaultKeyUri&#x27; property.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-12</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpRegion.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 672</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Storage Accounts do not specify customer-managed keys (CMK) but instead default to Microsoft-managed encryption keys. Highly sensitive storage data should allow for customer control over encryption keys.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Enable and configure CMK encryption using Azure Key Vault by setting the appropriate properties in the storage accounts.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpStamp.Parameters-LacpStampResources.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 64</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The parameter &#x27;dasStorageAccountKey&#x27; is populated using a cross-template reference, which may result in the raw storage account key being exposed in clear text in parameters. Storing or passing sensitive credentials (such as keys) outside of a secure secret store like Azure Key Vault increases risk of exposure.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Store and reference storage account keys securely from Azure Key Vault. Avoid passing sensitive credentials as plain parameters. Use Key Vault references instead.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpStamp.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The template does not deploy or reference any Network Security Groups (NSGs) or Azure Firewall resources to protect Storage Accounts, Key Vaults, or other sensitive components. This violates the requirement to protect sensitive resources with NSGs or firewalls.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Add NSGs and/or Azure Firewall to control network traffic to and from critical resources, and associate them with relevant subnets or resources. For example, ensure Storage Accounts and Key Vaults can be accessed only from trusted sources.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpStamp.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Multiple Storage Accounts and Key Vaults are created without any explicit restriction of their public endpoints (no &#x27;networkAcls&#x27; property, no private endpoint configuration, and no service endpoints are defined). This can potentially expose critical services to the public internet.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Restrict public access to Storage Accounts and Key Vaults by configuring their &#x27;networkAcls&#x27; property to allow traffic only from selected virtual networks or use private endpoints.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-5</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpStamp.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        None of the Storage Accounts or Key Vaults use private endpoints, which are required for secure access to these resources without traversing the public internet.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Define and associate private endpoints with all Storage Account and Key Vault resources to ensure private, secured access.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-7</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpStamp.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        There is no evidence of any Network Security Groups being created or assigned to resources or subnets, so inbound and outbound traffic control is not enforced.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Implement and associate appropriate NSGs with subnets or directly with resources to allow only required traffic.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">AM-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpStamp.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Several role assignments grant broad or powerful permissions (e.g., &#x27;Data Owner&#x27; on Redis, Key Vault access policies with extensive permissions) to managed identities. This risks violating least privilege and increases the attack surface.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Review all role assignments and access policies. Limit permissions strictly to those required by applications/processes. Avoid granting owner/contributor/data owner roles unless absolutely necessary.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">IM-8</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpStamp.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Some managed identities are used, but not all principal assignments are managed identities. Some principal assignments rely on AAD objects/service principals; these should be transitioned to managed identities for resource-to-resource authentication wherever feasible.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Adopt managed identities in place of direct service principal/AAD assignments for all resource-to-resource authentication scenarios.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>ReadAdxExhaust.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 21</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The Microsoft.Kusto/cluster resource does not specify any network restrictions such as network security groups (NSGs), private endpoints, or integration with Azure Firewall. By default, Kusto clusters are deployed with a public endpoint, potentially exposing the resource to the internet.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Restrict cluster access by enabling private endpoints, configuring virtual network integration, or associating the cluster with properly scoped NSGs or Azure Firewall rules to limit public exposure.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>ReadAdxExhaust.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 21</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        No configuration is provided to secure or restrict public endpoints for the Microsoft.Kusto/cluster resource. This may expose the cluster&#x27;s endpoint to the public internet and increase the risk of unauthorized access.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Configure the cluster to use private endpoints and disable the public endpoint unless explicitly required. If the public endpoint is required, restrict access to trusted IP addresses only.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">IM-8</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>ReadUsageAccount.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 13</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The UsageBilling account resource does not have a system-assigned managed identity configured, despite the variable &#x27;enableSystemAssignedIdentity&#x27; being set to true. The properties or identity block for enabling managed identity is missing from the resource definition, contrary to best practices for secure resource-to-resource authentication.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Add the &#x27;identity&#x27; block with type &#x27;SystemAssigned&#x27; to the UsageBilling account resource definition to enable a managed identity, as recommended by ASB IM-8.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">IM-6</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>RoleAssignment.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 52</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The template assigns the &#x27;Contributor&#x27; role to the Ev2 Buildout Service Principal at the subscription scope. The Contributor role grants broad permissions, including management of all resources (except access control), which increases the attack surface and does not follow the principle of least privilege.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Assign a more restrictive, custom role tailored to the minimum permissions required for the Ev2 Buildout Service Principal. Limit scope to the specific resources or resource groups needed, rather than the entire subscription.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">AM-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>RoleAssignment.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 52</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The template grants Contributor access to a service principal over the subscription. Granting such broad permissions to a service principal presents a significant risk if the identity or its credentials are compromised.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Review and reduce the scope of the role assignment. Use a least privilege, custom RBAC role with only the required permissions, and assign at the most granular resource scope possible (e.g., resource group or specific resource), not the subscription.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>TrafficManagerEndpoints.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 46</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The template defines external Traffic Manager endpoints using &#x27;Microsoft.Network/trafficManagerProfiles/externalEndpoints&#x27;, which are by nature public. There is no evidence of access restrictions, IP allowlists, or security controls to minimize the exposure of these public endpoints as recommended by ASB NS-2.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Restrict access to public endpoints by implementing client IP allowlists, traffic authentication, or using private endpoints wherever possible. Leverage Traffic Manager features such as endpoint monitoring, and evaluate if these endpoints can be internal or require network restrictions via fronting services or firewalls.
                        </div>
                    </div>
                </article>
            </div>
        </section>
        <section class="severity-group" data-severity="medium">
            <header class="severity-header medium">
                <div class="severity-header-left">
                    <div class="severity-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="severity-title">Medium Severity</div>
                    <div class="severity-count">31</div>
                </div>
                <i class="fas fa-chevron-down toggle-icon"></i>
            </header>
            <div class="findings-list">
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>IngestionStorageAccount.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 37</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        No mention or configuration of Network Security Groups (NSGs) is present for securing traffic to services that may interact with these storage accounts.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            When integrating storage with virtual networks, ensure relevant subnets have appropriate Network Security Groups (NSGs) attached to restrict and monitor traffic.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">IM-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>IngestionStorageAccount.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 37</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The template does not specify or enforce Azure Active Directory (AAD) integration for storage account authentication (e.g., Azure AD-DS integration for blob/file access, or Azure AD RBAC for management).
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Enable Azure AD integration for the storage accounts to enforce identity-based access control and reduce reliance on account keys.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>IngestionStorageAccount.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 37</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        No configuration for secure storage of sensitive secrets, such as access keys or connection strings, in Azure Key Vault. While no secrets appear directly in the template, there is no evidence that access keys are being securely managed.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Use Azure Key Vault to securely manage and reference storage account keys and connection strings. Never embed secrets in templates or parameter files.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-7</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpBilling.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        There are no Network Security Groups (NSGs) applied—neither at subnet level nor referenced in any resource—in the template. NSGs help control and restrict traffic even when resources are only accessible from within the VNet.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Implement NSGs on the relevant subnets (if using VNets) that might access the Storage Accounts or any deployed VMs/services to restrict traffic to what is necessary.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">IM-8</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpBillingExhaust.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 61</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Principal assignments for database access use explicit principal IDs with &#x27;principalType&#x27; set to &#x27;App&#x27;, but there is no evidence of managed identities being referenced as principals. Directly assigning access to applications without using managed identities is less secure as it may involve managing secrets or certificates externally.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Assign access to the database resources using managed identities where possible, which enables secure, credential-free authentication for Azure resources.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">AM-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpBillingExhaust.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 61</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Principal assignments grant &#x27;User&#x27; and &#x27;Ingestor&#x27; roles at the database level to entire application principals without clear evidence of the principle of least privilege being followed. Over-permissioned assignments may allow broader access than necessary.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Review the assigned roles and ensure that each principal is granted only the minimum permissions required for their intended operation within the cluster.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpBillingExhaust.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 28</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        There is no explicit configuration for encryption at rest for the Kusto cluster or database in the template. While encryption at rest is enabled by default for Azure Data Explorer, the absence of customer-managed keys (CMK) configuration means data is protected only with platform-managed keys.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Configure customer-managed keys (CMK) for the cluster to enhance encryption-at-rest controls, ensuring compliance with stricter security policies if required by your organization.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpBillingExhaustExport.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 56</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The template does not specify any network security controls, access restrictions, or private endpoints for the Data Export resources, potentially exposing sensitive billing or diagnostic data. There is no evidence of NSG, firewall, or network isolation applied to the data flows.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Explicitly define network security groups (NSGs) or Azure Firewall to limit access to the deployed resources and their endpoints. Where supported, restrict access to private networks or leverage Private Link/Private Endpoints for data connectivity.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpBillingExhaustExport.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 56</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        There is no evidence in the template that transport layer encryption (TLS &gt;= 1.2) is enforced for connections to the ADX endpoints or between resources, leaving possible risk of unencrypted data-in-transit.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Enforce connections to ADX and related data export endpoints using at least TLS 1.2. When specifying endpoints, use &#x27;https&#x27; and ensure that service configurations restrict or reject insecure protocols.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">IM-6</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGeo.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 48</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Key Vault access policies grant extensive permissions (e.g., &quot;Get&quot;/&quot;List&quot; for keys, secrets, certificates) to service principals and groups, without indications of necessity per least privilege. Granting broad permissions risks misuse or compromise.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Review and minimize Key Vault access policies to only those operations required by each principal. Use Azure RBAC for fine-grained, least-privilege access management.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">AM-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGeo.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 48</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Key Vault access policies for &quot;lacpAadServicePrincipal&quot; and &quot;managedIdentitiesSecurityGroupId&quot; allow both &quot;Get&quot; and &quot;List&quot; on keys, secrets, and certificates. The &quot;List&quot; permission can reveal all names, which may aid reconnaissance.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Restrict permissions to minimum required (e.g., remove &quot;List&quot; if not strictly needed). Audit why each principal needs each permission, and remove any not justified by their business function.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">IM-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGeo.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        There is no indication that conditional access policies are used for any identity principal interacting with resources like Key Vault or CosmosDB.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Implement and enforce Azure AD conditional access policies requiring users and administrators to authenticate under defined secure conditions (e.g., trusted devices, MFA, location-based access).
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-6</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGlobal.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 133</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Key Vault is set to use &#x27;Standard&#x27; SKU with no explicit configuration for customer-managed keys (CMK) on dependent resources such as Storage or CosmosDB, missing out on full customer control of encryption keys.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Where regulatory requirements apply, enable and enforce Customer Managed Keys (CMK) on all sensitive resources, referencing a Key Vault key.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-5</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGlobal.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 110</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Storage Accounts lack configuration for Private Endpoints, exposing data transfers to the public internet.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Implement Azure Private Endpoints for storage accounts to limit their exposure to the public internet.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-6</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGlobal.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 110</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Storage Accounts do not leverage Azure Virtual Network Service Endpoints, which could provide additional network layer controls.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Configure Service Endpoints on the storage accounts to restrict traffic to trusted VNets only.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-6</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGlobal.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 110</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Storage Accounts do not have customer-managed keys configured for encryption at rest (default platform-managed keys are likely used). This weakens data control and compliance posture.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Enable encryption at rest with customer-managed keys (CMK) stored in Azure Key Vault for all storage accounts if compliance and regulatory requirements dictate.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGlobal.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 110</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Storage, CosmosDB, and Key Vault resources lack any reference to Network Security Groups or Azure Firewall, missing an important line of defense for access control.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Deploy NSGs or Azure Firewall in the relevant subnets or resource configurations to protect these resources with explicit traffic filtering.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGlobal.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 110</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        There are no NSG (Network Security Group) attachments or rules applied to secure access to Storage or dependent resources.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Apply appropriate Network Security Groups to the subnets of these resources to enforce allowed/denied inbound and outbound traffic.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">AM-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpRegion.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1362</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Data Factory pipelines and resources assign broad &#x27;Data Contributor&#x27; roles and potentially other permissions to managed identities and security groups. There is no evidence of enforcing least privilege or reviewing permissions.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Review all RBAC and data permissions in role assignments and access policies, ensuring managed identities and security groups receive only the minimum set of privileges required for their operations.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">IM-11</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpRegion.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1097</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Key Vault access policies grant broad secrets/certificates/keys &#x27;Get&#x27; and &#x27;List&#x27; permissions to groups and identities. Excessive permissions can result in unnecessary risk if accounts are compromised.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Limit Key Vault permissions to only those that are necessary per principal, and avoid granting highly privileged access broadly.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpRegion.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1411</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Secrets such as storage account connection strings and CosmosDB keys are stored in Key Vault, but there is no evidence these are referenced using Key Vault references in linked services or other consuming resources, increasing operational risk if secrets are rotated or leaked.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Consume secrets securely using Key Vault references in consuming services (e.g., App Service or Data Factory linked services), and rotate them regularly.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-9</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpRegion.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 672</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        All storage accounts and CosmosDB instances have minimum TLS version set to &#x27;TLS1_2&#x27;, which is correct, but ensure all consumers enforce TLS1.2+ as well. Some DataFactory linked services or connections may not enforce a minimum TLS version.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Ensure all consuming applications and linked services enforce TLS1.2+ for connections to data sources and sinks.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-6</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpStamp.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        There are no Azure Virtual Network service endpoints configured for Storage Accounts or Key Vaults, which would limit access to these resources to traffic from selected virtual networks.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Enable and configure Virtual Network Service Endpoints on the required subnets and associate the Storage Accounts/Key Vaults to accept traffic only from those endpoints.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpStamp.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Encryption at rest is not explicitly configured for Storage Accounts or Redis Cache. Absence of explicit configuration can lead to potentially unencrypted or weak encryption defaults.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Explicitly enable encryption at rest using platform-managed or customer-managed keys (CMK) for all data storage resources like Storage Accounts and Redis. Review and set the encryption section where supported.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-6</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpStamp.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Customer-Managed Key (CMK) encryption is not configured for Storage Accounts, Redis, or Key Vaults, which is recommended for sensitive data and regulated workloads.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Implement CMK encryption by linking resources to Azure Key Vault-managed encryption keys and updating resource definitions with necessary CMK configuration.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpStamp.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Although Storage Accounts specify &#x27;minimumTlsVersion&#x27; as &#x27;TLS1_2&#x27; and &#x27;supportsHttpsTrafficOnly&#x27; is true, App Service endpoints and traffic manager endpoints are exposed via HTTPS but without custom DNS/hostnames or explicit TLS certificate references. This may lead to weak server identity assurance if default certificates are used.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Use custom domain names and upload trusted TLS certificates where possible for external endpoints and App Services to ensure strong in-transit encryption and proper identity validation.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">IM-11</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpStamp.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        RBAC is used for some resources but not comprehensively enforced across all resources, and coarse-grained roles are seen. More granular RBAC and scope definition can further restrict access.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Enforce granular RBAC for all resources, with least-privilege assignments and appropriate scope limitations.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">IM-8</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>ReadAdxExhaust.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 21</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The deployment does not define or reference a system-assigned or user-assigned managed identity for the Kusto cluster. Managed identities should be used for secure resource-to-resource authentication.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Add a managed identity property to the Microsoft.Kusto/cluster resource to enable secure authentication to other Azure services without managing credentials.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">IM-7</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>RoleAssignment.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 60</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        A service principal (KeyVaultPrincipalId) is assigned the &#x27;Storage Account Key Operator Service Role&#x27; at the subscription scope. Application identities should be limited in permission and assignment scope to reduce risk in case of compromise.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Assign this role at the minimal required scope (e.g., only to the relevant storage accounts, not the entire subscription) and use managed identities where possible.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>TrafficManagerEndpoints.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 46</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        There is no reference to Network Security Groups (NSGs) or Azure Firewall rules being used to protect the resources associated with the Traffic Manager endpoints. This may expose backend resources to unauthorized access.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Ensure appropriate network security controls like NSGs or Azure Firewall are applied to backend resources referenced by these endpoints. Document or enforce network layer protections for all traffic flows associated with Traffic Manager endpoints to mitigate unauthorized access.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-5</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>TrafficManagerEndpoints.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 46</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The usage of external endpoints in Traffic Manager suggests public exposure. There is no indication of Private Endpoints being implemented to securely access the resources behind the Traffic Manager, missing the ASB recommendation to prefer private connectivity for sensitive services.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Where possible, use Azure Private Endpoints for backend services and configure Traffic Manager profiles to use internal or private endpoints, minimizing direct public access to critical infrastructure.
                        </div>
                    </div>
                </article>
            </div>
        </section>
        <section class="severity-group" data-severity="low">
            <header class="severity-header low">
                <div class="severity-header-left">
                    <div class="severity-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="severity-title">Low Severity</div>
                    <div class="severity-count">14</div>
                </div>
                <i class="fas fa-chevron-down toggle-icon"></i>
            </header>
            <div class="findings-list">
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>IngestionStorageAccount.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 45</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        &#x27;minimumTlsVersion&#x27; is set to &#x27;TLS1_2&#x27;, which meets baseline, but best practice is to regularly review and apply the highest supported TLS version (e.g., TLS1_3 when available).
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Monitor for updates and configure storage accounts to use &#x27;TLS1_3&#x27; (or higher) when available to maintain strong transport encryption.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpBilling.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The Storage Accounts do not specify customer-managed keys (CMK) for encryption at rest, relying on platform-managed keys (default behavior). While Azure encrypts data at rest by default, using CMK provides an additional layer of control and compliance.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Configure Storage Accounts to use customer-managed keys (CMK) stored in Azure Key Vault for encryption at rest where regulatory or compliance requirements exist.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpBilling.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The Storage Accounts set &#x27;minimumTlsVersion&#x27; to &#x27;TLS1_2&#x27; and &#x27;supportsHttpsTrafficOnly&#x27; to &#x27;true&#x27;, which meets the requirement for encryption in transit. No finding of violation; this is flagged for completeness.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            None required. Ensure continued enforcement of TLS 1.2+ and HTTPS-only traffic on Storage Accounts.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpBilling.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        No sensitive information such as access keys, connection strings, or secrets are hardcoded in this template, and &#x27;allowSharedKeyAccess&#x27; is set &#x27;false&#x27;. However, Storage Account identities and access might be managed manually elsewhere, and key management details (such as Key Vault integration) are not present.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Use Azure Key Vault to manage secrets and keys for applications. Integrate Storage Account with Key Vault for managing keys if customer-managed keys are used.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">IM-15</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpBilling.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        User-assigned and system-assigned managed identities are used for the service, which meets the requirement for using managed identities. No violation; noted for transparency.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Continue to use managed identities for internal authentication between Azure resources. Regularly audit assigned privileges to managed identities.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpBillingExhaust.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 103</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        An AAD group (<EMAIL>) is granted database viewer permissions via a script, but group membership and access governance are not controlled or referenced in the template. Improper governance risks unauthorized disclosure of sensitive data.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Ensure the group assigned as database viewer is governed through Azure AD with proper access reviews, and avoid granting group-level access unless necessary and tightly controlled.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGeo.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 87</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        A CosmosDB account&#x27;s primary key is retrieved via ARM listKeys and stored as a Key Vault secret. Exposure risk is minimized by using Key Vault, but the primary key is highly sensitive and should not be programmatically or broadly accessible.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Regularly rotate CosmosDB keys and audit Key Vault access. Restrict which identities can retrieve and use these secrets, removing broad &#x27;List&#x27;/&#x27;Get&#x27; access.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">IM-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGeo.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The template utilizes objectIds and service principals, but does not explicitly define the use of Azure AD groups or managed identities for all access, leaving possible gaps in leveraging Azure AD&#x27;s full capabilities.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Ensure all resource-to-resource and user access leverages Azure AD, and prefer managed identities for resource access rather than application IDs and secrets.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-5</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpGlobal.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 49</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        CosmosDB is configured with a backup policy of &#x27;Continuous30Days,&#x27; which typically meets backup requirements; however, there is a comment indicating potential changes are needed in test environments, suggesting backup policies may not be consistent.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Ensure that backup policy is enforced in all environments and not only in production, and that compliance and recovery requirements are met for all critical data.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-18</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpRegion.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line None</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        There are no explicit logging or Azure Monitor diagnostic settings applied to storage accounts, Key Vaults, Data Factory, or Cosmos DB. Without diagnostics, issues and access anomalies may go undetected.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Configure diagnostic settings and enable logging for all key data resources and identity management planes.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-9</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpStamp.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Network traffic logging/monitoring is not enabled for any resource. There are no &#x27;diagnosticSettings&#x27; or log collection policies in place for Storage, Network, or Key Vault resources.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Enable diagnostic logging for Storage Accounts, Key Vaults, and network resources. Stream logs to a Log Analytics Workspace, Storage Account, or Event Hub as appropriate.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpStamp.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The parameter &#x27;dasStorageAccountKey&#x27; is passed as a string parameter and stored directly in Key Vault as-is. If this value is ever set inline (potentially via parameter file), there is risk of secret exposure at deployment time.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Secure input of secrets via Azure Key Vault references, and avoid passing secrets as parameters in templates. Always inject secrets using secure methods.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-5</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>LacpStamp.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        No explicit backup or recovery configuration is provided for Storage Accounts or Redis instances.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Implement backup policies and strategies for all critical data repositories (enable soft delete, point-in-time restore, or geo-redundant backup where available).
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-4</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>TrafficManagerEndpoints.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 46</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The template does not show use of Azure Firewall or any third-party firewall to further protect the resources exposed through Traffic Manager, which may increase risk, especially if endpoints must remain public.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Implement Azure Firewall (or a third-party firewall) between public-facing Traffic Manager endpoints and backend services to provide additional threat mitigation such as intrusion detection and threat intelligence filtering.
                        </div>
                    </div>
                </article>
            </div>
        </section>
            <div class="no-findings" style="display: none;">
                <i class="fas fa-search no-findings-icon"></i>
                <h3>No findings match your search criteria</h3>
                <p>Try adjusting your search terms or filters</p>
            </div>
        </div>

        <!-- Footer Section -->
        <footer class="report-footer">
            <div class="export-actions">
                <button class="export-btn" onclick="window.print()">
                    <i class="fas fa-print"></i>
                    Print Report
                </button>
                <button class="export-btn" onclick="exportToJson()">
                    <i class="fas fa-download"></i>
                    Export JSON
                </button>
            </div>
            <div class="footer-info">
                <p><strong>Generated by IaC Guardian GPT</strong> • {timestamp}</p>
                <p><strong>Disclaimer:</strong> This report is AI-generated and should be reviewed by security professionals.</p>
            </div>
        </footer>
    </div>
</body>
</html>