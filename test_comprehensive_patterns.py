#!/usr/bin/env python3
"""
Comprehensive test for all supported Template/Parameters naming patterns
"""

import os
import sys
import logging
from pathlib import Path

# Add src/core to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'core'))

from template_parameter_expander import TemplateParameterExpander

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_comprehensive_patterns():
    """Test all supported Template/Parameters naming patterns"""
    
    # Create test directory
    test_dir = Path("test_comprehensive")
    test_dir.mkdir(exist_ok=True)
    
    # Test cases: (template_file, expected_parameter_file, description)
    test_cases = [
        # Your specific pattern: deploymentTemplate → deploymentParameters
        ("Grafana.deploymentTemplate.json", "Grafana.deploymentParameters.json", "deploymentTemplate → deploymentParameters"),
        ("webapp.deploymentTemplate.json", "webapp.deploymentParameters.json", "deploymentTemplate → deploymentParameters"),
        
        # Standard pattern: Template → Parameters  
        ("storage.Template.json", "storage.Parameters.json", "Template → Parameters"),
        ("network.Template.json", "network.Parameters.json", "Template → Parameters"),
        
        # Lowercase pattern: template → parameters
        ("database.template.json", "database.parameters.json", "template → parameters"),
        ("KustoScripts.template.json", "KustoScripts.parameters.json", "template → parameters"),
        
        # Mixed case patterns
        ("keyvault.Template.json", "keyvault.Parameter.json", "Template → Parameter (singular)"),
        ("cosmosdb.template.json", "cosmosdb.params.json", "template → params"),
        
        # Main/deploy patterns
        ("main.json", "main.parameters.json", "main → main.parameters"),
        ("deploy.json", "deploy.params.json", "deploy → deploy.params"),
    ]
    
    # Create all test files
    template_content = '''
{
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
    "contentVersion": "*******",
    "parameters": {
        "testParam": {
            "type": "string"
        }
    },
    "resources": []
}'''
    
    parameter_content = '''
{
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#",
    "contentVersion": "*******",
    "parameters": {
        "testParam": {
            "value": "test-value"
        }
    }
}'''
    
    # Write all test files
    for template_file, param_file, _ in test_cases:
        (test_dir / template_file).write_text(template_content)
        (test_dir / param_file).write_text(parameter_content)
    
    # Initialize expander
    expander = TemplateParameterExpander()
    
    print("🧪 Comprehensive Template/Parameters Pattern Test")
    print("=" * 70)
    
    # Test each pattern
    success_count = 0
    total_count = len(test_cases)
    
    for template_file, expected_param_file, description in test_cases:
        template_path = str(test_dir / template_file)
        
        print(f"\n📄 Testing: {description}")
        print(f"   Template: {template_file}")
        print(f"   Expected: {expected_param_file}")
        
        # Find matching parameter files
        found_params = expander._find_matching_parameter_files(template_path)
        found_basenames = [os.path.basename(p) for p in found_params]
        
        if expected_param_file in found_basenames:
            print(f"   ✅ SUCCESS: Found {expected_param_file}")
            success_count += 1
            
            # Show base name analysis
            template_base = template_file.replace('.json', '')
            param_base = expected_param_file.replace('.json', '')
            
            # Extract root names by removing common suffixes
            template_root = template_base
            for suffix in ['deploymentTemplate', 'Template', 'template', 'main', 'deploy']:
                if template_base.endswith(suffix):
                    template_root = template_base[:-len(suffix)]
                    break
            
            param_root = param_base
            for suffix in ['deploymentParameters', 'Parameters', 'parameters', 'Parameter', 'params', 'param']:
                if param_base.endswith(suffix):
                    param_root = param_base[:-len(suffix)]
                    break
            
            print(f"   📋 Root match: '{template_root}' == '{param_root}' → {template_root == param_root}")
        else:
            print(f"   ❌ FAILED: Expected {expected_param_file}, found {found_basenames}")
    
    # Summary
    print(f"\n📊 Test Summary:")
    print("=" * 70)
    print(f"✅ Successful matches: {success_count}/{total_count}")
    print(f"❌ Failed matches: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print(f"🎉 ALL TESTS PASSED! Template/Parameters pattern matching is working perfectly!")
    else:
        print(f"⚠️  Some tests failed. Pattern matching needs improvement.")
    
    # Test parameter loading for successful matches
    if success_count > 0:
        print(f"\n📥 Parameter Loading Verification:")
        print("-" * 40)
        
        for template_file, expected_param_file, description in test_cases[:3]:  # Test first 3
            template_path = str(test_dir / template_file)
            param_values = expander._load_parameter_values(template_path)
            
            if param_values and 'testParam' in param_values:
                print(f"✅ {template_file}: Loaded parameter 'testParam' = '{param_values['testParam']}'")
            else:
                print(f"❌ {template_file}: Failed to load parameters")
    
    # Cleanup
    import shutil
    shutil.rmtree(test_dir)
    
    print(f"\n✨ Comprehensive test completed!")

if __name__ == "__main__":
    test_comprehensive_patterns()
