File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
Parameters\LacpBillingExhaust.Parameters-LacpBillingExhaust.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'usageAccountSystemAssignedIdentityPrincipalId' flows across template boundary,Secure parameter 'usageAccountSystemAssignedIdentityPrincipalId' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyVaultSuffix' flows across template boundary,Secure parameter 'keyVaultSuffix' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'regionalCosmosAccountLocation' flows across template boundary,Secure parameter 'regionalCosmosAccountLocation' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'dasStorageAccountKey' flows across template boundary,Secure parameter 'dasStorageAccountKey' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'globalKeyVaultName' flows across template boundary,Secure parameter 'globalKeyVaultName' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'createSharedKeyVault' flows across template boundary,Secure parameter 'createSharedKeyVault' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,40.0,"The parameter 'isBoundariesRestricted' is explicitly set to 'false' (Line 041), indicating that boundary restrictions are disabled. This configuration can allow public network access to Logic Apps and related resources, increasing the risk of initial access by external attackers and expanding the blast radius for lateral movement and data exfiltration. Disabling network boundaries directly contradicts Azure Security Benchmark guidance to restrict public access and enforce private endpoints.","Set 'isBoundariesRestricted' to 'true' to enforce network boundary restrictions. Additionally, ensure all Logic Apps and associated resources are configured to use private endpoints and have public network access disabled. Review and update network security group (NSG) and firewall rules to restrict access to only trusted networks, following Azure Security Benchmark NS-2 guidance.",,,,ai_analysis,,Validated
Templates\IngestionStorageAccount.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,44.0,"The 'allowBlobPublicAccess' property is explicitly set to false on Line 044, which is correct. However, there is no configuration present for 'publicNetworkAccess' or private endpoint usage for the Microsoft.Storage/storageAccounts resources. Without explicitly disabling public network access or enabling private endpoints, the storage accounts may still be accessible over the public internet, enabling initial access and data exfiltration attack vectors. The blast radius includes all data stored in these accounts, potentially exposing sensitive ingestion data to unauthorized parties.",Explicitly set 'publicNetworkAccess' to 'Disabled' and configure private endpoints for all storage accounts to ensure they are only accessible from trusted networks. Example: add 'publicNetworkAccess': 'Disabled' to the storage account resource properties and deploy a private endpoint resource referencing the storage account. Reference ASB control NS-2.,,,,ai_analysis,,Validated
Templates\LacpGeo.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,225.0,"The CosmosDB resource 'publicNetworkAccess' property is set to 'Enabled' on line 225. This allows the database account to be accessed from the public internet, creating an initial access vector for attackers. If authentication or network controls are misconfigured, attackers could exploit this exposure to access or exfiltrate sensitive data, increasing the blast radius to all data in the CosmosDB account.","Set 'publicNetworkAccess' to 'Disabled' to restrict access to the CosmosDB account to private endpoints only. Implement Azure Private Link and ensure only trusted VNets can access the resource. Example: ""publicNetworkAccess"": ""Disabled"". Reference: NS-2 (Secure cloud services with network controls).",,,,ai_analysis,,Validated
Templates\LacpGeo.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,228.0,"The CosmosDB resource 'isVirtualNetworkFilterEnabled' property is set to 'false' on line 228. This disables VNet-based access controls, allowing connections from any network, including the public internet. Attackers can exploit this to access the database from untrusted networks, enabling lateral movement and data exfiltration.","Set 'isVirtualNetworkFilterEnabled' to 'true' to enforce VNet-based access controls. Define 'virtualNetworkRules' to specify allowed subnets. Example: ""isVirtualNetworkFilterEnabled"": true. Reference: NS-2 (Secure cloud services with network controls).",,,,ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,105.0,"CosmosDB account 'publicNetworkAccess' is set to 'Enabled' on line 105, allowing public network access. This exposes the database to the internet, enabling attackers to attempt initial access, brute force, or exploit vulnerabilities, significantly increasing the blast radius for data exfiltration and lateral movement.","Set 'publicNetworkAccess' to 'Disabled' in the CosmosDB account properties to restrict access to private endpoints only. Implement Azure Private Link and ensure only trusted networks can access the database. Example: ""publicNetworkAccess"": ""Disabled"".",,,,ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,108.0,"CosmosDB account 'isVirtualNetworkFilterEnabled' is set to 'false' on line 108, disabling VNet-based access controls. This allows access from any network, increasing the risk of unauthorized access and lateral movement by attackers.","Set 'isVirtualNetworkFilterEnabled' to 'true' to enforce VNet-based access controls. Define 'virtualNetworkRules' to specify allowed subnets. Example: ""isVirtualNetworkFilterEnabled"": true.",,,,ai_analysis,,Validated
Templates\LacpRegion.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,561.0,"The CosmosDB account resource at line 558 has 'publicNetworkAccess' explicitly set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false. This exposes the CosmosDB account to the public internet, allowing attackers to attempt direct access, brute force, or exploit vulnerabilities, significantly increasing the risk of data exfiltration and lateral movement. The blast radius includes all data in the CosmosDB account and any downstream services relying on it.","Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to true in the CosmosDB account resource. Configure 'virtualNetworkRules' to allow only trusted subnets. Deploy a private endpoint for CosmosDB to restrict access to private networks only, following Azure guidance for Private Link and network security.",,,,ai_analysis,,Validated
Templates\LacpRegion.Template.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,561.0,"The CosmosDB account resource at line 561 has 'isVirtualNetworkFilterEnabled' set to false, which means network traffic is not restricted to secure, private networks. This allows unencrypted or weakly encrypted traffic from any source, increasing the risk of data interception and man-in-the-middle attacks. Attackers can exploit this to read or modify data in transit.",Set 'isVirtualNetworkFilterEnabled' to true and configure 'virtualNetworkRules' to restrict access to trusted subnets. Ensure all client connections enforce TLS 1.2 or higher by setting 'minimalTlsVersion' appropriately. This will ensure data in transit is encrypted and only accessible from secure networks.,,,,ai_analysis,,Validated
Templates\LacpStamp.Template.json,DP-8,Data Protection,Ensure security of key and certificate repository,MEDIUM,798.0,"The Key Vault resource at line 800 (property: 'enabledForDeployment', 'enabledForTemplateDeployment') is missing explicit network access controls such as private endpoints or public network access restrictions. Without these, the Key Vault may be accessible from public networks, increasing the risk of unauthorized access to cryptographic keys and secrets. Attackers could exploit this exposure to exfiltrate secrets or escalate privileges, impacting all resources relying on this Key Vault.",Restrict public network access to the Key Vault by setting 'publicNetworkAccess' to 'Disabled' and configure a private endpoint. Review and limit access policies to the minimum required. Reference: Azure Security Benchmark DP-8.,,,,ai_analysis,,Validated
Templates\LacpStamp.Template.json,DP-8,Data Protection,Ensure security of key and certificate repository,MEDIUM,798.0,"The shared Key Vault resource at line 932 (property: 'enabledForDeployment', 'enabledForTemplateDeployment') is missing explicit network access controls such as private endpoints or public network access restrictions. This increases the attack surface for secrets and keys stored in the shared Key Vault, enabling potential lateral movement and data exfiltration if compromised.",Set 'publicNetworkAccess' to 'Disabled' for the shared Key Vault and configure a private endpoint. Limit access policies to only required identities. Reference: Azure Security Benchmark DP-8.,,,,ai_analysis,,Validated
Templates\ReadAdxExhaust.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,23.0,"The Microsoft.Kusto/clusters resource defined at line 023 does not specify any network controls such as private endpoints or restrictions on public network access. By default, Azure Data Explorer (Kusto) clusters are accessible over the public internet unless explicitly restricted. This configuration enables an initial access attack vector, allowing attackers to attempt unauthorized access, brute force, or exploit vulnerabilities over the public endpoint. The blast radius includes potential exposure of all data and services within the cluster, and could allow lateral movement to other resources if the cluster is compromised.",Restrict public network access by setting the 'publicNetworkAccess' property to 'Disabled' and configure a private endpoint for the Kusto cluster. Update the resource definition to include 'publicNetworkAccess': 'Disabled' and deploy a private endpoint resource to ensure only trusted networks can access the cluster. Reference Azure guidance for Private Link and network access restrictions.,,,,ai_analysis,,Validated
Templates\RoleAssignment.Template.json,IM-2,Identity Management,Protect identity and authentication systems,HIGH,5.0,"The 'roleDefinitionId' property on line 72 assigns the built-in 'Contributor' role to the principal specified by 'Ev2BuildoutServicePrincipalId'. Assigning the Contributor role to a service principal grants broad permissions, including the ability to modify resources, escalate privileges, and access sensitive data. If this service principal is compromised, an attacker could gain extensive control over the subscription, enabling privilege escalation, lateral movement, and data exfiltration. This configuration increases the blast radius and exposes the environment to significant risk if the identity is not tightly controlled and monitored.","Restrict the permissions granted to 'Ev2BuildoutServicePrincipalId' by assigning only the minimum required role instead of 'Contributor'. Use custom roles with least privilege, enable Azure AD Privileged Identity Management (PIM) for just-in-time access, and enforce strong authentication (MFA) for all privileged identities. Regularly review and monitor role assignments for excessive permissions. Reference: Azure Security Benchmark IM-2.",,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 18,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 6,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T11:22:38.181738,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
