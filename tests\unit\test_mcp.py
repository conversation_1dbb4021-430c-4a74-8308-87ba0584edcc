#!/usr/bin/env python3
"""
Test script for IaC Guardian MCP Server
Tests the MCP server functionality locally.
"""

import asyncio
import json
import tempfile
from pathlib import Path
from mcp_server import handle_call_tool, security_reviewer
from security_opt import SecurityPRReviewer

async def test_analyze_file():
    """Test file analysis functionality."""
    print("🧪 Testing file analysis...")
    
    # Create a test ARM template with security issues
    test_template = {
        "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
        "contentVersion": "*******",
        "resources": [
            {
                "type": "Microsoft.Storage/storageAccounts",
                "apiVersion": "2021-04-01",
                "name": "teststorage",
                "location": "[resourceGroup().location]",
                "properties": {
                    "supportsHttpsTrafficOnly": False,  # Security issue
                    "minimumTlsVersion": "TLS1_0"       # Security issue
                }
            }
        ]
    }
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(test_template, f, indent=2)
        temp_file = f.name
    
    try:
        # Initialize global security reviewer
        global security_reviewer
        security_reviewer = SecurityPRReviewer(local_folder=".")
        
        # Test the analyze_iac_file tool
        result = await handle_call_tool("analyze_iac_file", {
            "file_path": temp_file,
            "format": "markdown"
        })
        
        print("✅ File analysis test completed")
        print("📄 Result preview:")
        print(result[0].text[:200] + "..." if len(result[0].text) > 200 else result[0].text)
        
    finally:
        Path(temp_file).unlink(missing_ok=True)

async def test_get_controls():
    """Test security controls retrieval."""
    print("🧪 Testing security controls retrieval...")
    
    global security_reviewer
    if security_reviewer is None:
        security_reviewer = SecurityPRReviewer(local_folder=".")
    
    result = await handle_call_tool("get_security_controls", {
        "resource_type": "Storage",
        "domain": "Data Protection"
    })
    
    print("✅ Security controls test completed")
    print("📄 Result preview:")
    print(result[0].text[:200] + "..." if len(result[0].text) > 200 else result[0].text)

async def main():
    """Run all tests."""
    print("🚀 Starting IaC Guardian MCP Server Tests")
    print("=" * 60)
    
    try:
        await test_analyze_file()
        print()
        await test_get_controls()
        print()
        print("🎉 All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
