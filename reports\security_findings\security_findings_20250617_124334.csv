Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,163,"App Service 'onefuzz-daily-ui' has 'ipSecurityRestrictions' with 'ipAddress' set to 'Any' and 'action' set to 'Allow', which exposes the application to the public internet without restriction. This violates the requirement to secure all public endpoints.",Restrict 'ipSecurityRestrictions' to only allow trusted IP ranges and remove 'Allow all' rules to minimize public exposure.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,171,"App Service 'onefuzz-daily-ui' has 'scmIpSecurityRestrictions' with 'ipAddress' set to 'Any' and 'action' set to 'Allow', which exposes the SCM endpoint to the public internet without restriction. This violates the requirement to secure all public endpoints.",Restrict 'scmIpSecurityRestrictions' to only allow trusted IP ranges and remove 'Allow all' rules to minimize public exposure.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,120,"App Service 'onefuzz-daily-ui' is configured with 'phpVersion' set to '5.6', which is deprecated and may not support modern encryption at rest or in transit. This violates the requirement to use secure, supported runtimes for data protection.","Upgrade 'phpVersion' to a supported version (e.g., 8.0 or later) to ensure compatibility with encryption and security best practices.",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,54,"App Service 'onefuzz-daily-ui' has 'hostNameSslStates' with 'sslState' set to 'Disabled' for 'onefuzz-daily-ui.azurewebsites.net', which allows unencrypted HTTP connections. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates to enforce HTTPS and ensure only TLS 1.2+ is used.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,60,"App Service 'onefuzz-daily-ui' has 'hostNameSslStates' with 'sslState' set to 'Disabled' for 'onefuzz-daily-ui.scm.azurewebsites.net', which allows unencrypted HTTP connections to the SCM endpoint. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all SCM hostNameSslStates to enforce HTTPS and ensure only TLS 1.2+ is used.,N/A,AI,Generic
