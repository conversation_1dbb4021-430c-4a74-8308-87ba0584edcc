"""
Template Validator for IaC Guardian

This module provides comprehensive template validation including syntax checking,
security validation, and structural integrity verification.
"""

import re
import json
import html
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class ValidationSeverity(Enum):
    """Validation issue severity levels"""
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"

@dataclass
class ValidationIssue:
    """Represents a template validation issue"""
    severity: ValidationSeverity
    message: str
    line_number: Optional[int] = None
    column: Optional[int] = None
    rule: Optional[str] = None
    suggestion: Optional[str] = None

@dataclass
class ValidationResult:
    """Results of template validation"""
    is_valid: bool
    issues: List[ValidationIssue]
    template_type: str
    file_path: str
    validation_time_ms: float
    
    @property
    def error_count(self) -> int:
        return len([i for i in self.issues if i.severity == ValidationSeverity.ERROR])
    
    @property
    def warning_count(self) -> int:
        return len([i for i in self.issues if i.severity == ValidationSeverity.WARNING])

class TemplateValidator:
    """Comprehensive template validation system"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the template validator
        
        Args:
            config: Validation configuration dictionary
        """
        self.config = config
        self.validation_rules = self._load_validation_rules()
        self._setup_security_patterns()
        
    def _load_validation_rules(self) -> Dict[str, Any]:
        """Load validation rules from configuration"""
        return self.config.get('validation', {})
    
    def _setup_security_patterns(self):
        """Setup security validation patterns"""
        forbidden = self.validation_rules.get('forbidden_patterns', [])
        self.security_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in forbidden]
        
    def validate_template(self, file_path: Path, content: str, template_type: str) -> ValidationResult:
        """
        Validate a template file
        
        Args:
            file_path: Path to the template file
            content: Template content
            template_type: Type of template (html, css, js, prompts)
            
        Returns:
            ValidationResult with validation status and issues
        """
        import time
        start_time = time.time()
        
        issues = []
        
        # Basic validation
        issues.extend(self._validate_basic_structure(content, template_type))
        
        # Security validation
        if self.validation_rules.get('validate_security', True):
            issues.extend(self._validate_security(content))
        
        # Variable validation
        if self.validation_rules.get('validate_variables', True):
            issues.extend(self._validate_variables(content, template_type))
        
        # Type-specific validation
        issues.extend(self._validate_by_type(content, template_type))
        
        # Size validation
        issues.extend(self._validate_size(content))
        
        validation_time = (time.time() - start_time) * 1000
        
        # Determine if template is valid (no errors)
        is_valid = not any(issue.severity == ValidationSeverity.ERROR for issue in issues)
        
        return ValidationResult(
            is_valid=is_valid,
            issues=issues,
            template_type=template_type,
            file_path=str(file_path),
            validation_time_ms=validation_time
        )
    
    def _validate_basic_structure(self, content: str, template_type: str) -> List[ValidationIssue]:
        """Validate basic template structure"""
        issues = []
        
        # Check for empty content
        if not content.strip():
            issues.append(ValidationIssue(
                severity=ValidationSeverity.ERROR,
                message="Template content is empty",
                rule="empty_content"
            ))
            return issues
        
        # Check encoding
        try:
            content.encode('utf-8')
        except UnicodeEncodeError as e:
            issues.append(ValidationIssue(
                severity=ValidationSeverity.ERROR,
                message=f"Invalid UTF-8 encoding: {e}",
                rule="encoding_validation"
            ))
        
        return issues
    
    def _validate_security(self, content: str) -> List[ValidationIssue]:
        """Validate template for security issues"""
        issues = []
        
        for pattern in self.security_patterns:
            matches = pattern.finditer(content)
            for match in matches:
                line_num = content[:match.start()].count('\n') + 1
                issues.append(ValidationIssue(
                    severity=ValidationSeverity.ERROR,
                    message=f"Forbidden pattern detected: {match.group()}",
                    line_number=line_num,
                    rule="security_pattern",
                    suggestion="Remove or replace the forbidden pattern"
                ))
        
        return issues
    
    def _validate_variables(self, content: str, template_type: str) -> List[ValidationIssue]:
        """Validate template variables and placeholders"""
        issues = []
        
        # Find all placeholders
        placeholder_pattern = re.compile(r'\{([^}]+)\}')
        placeholders = set(placeholder_pattern.findall(content))
        
        # Check required placeholders
        required = self.validation_rules.get('required_placeholders', {}).get(template_type, [])
        for req_placeholder in required:
            if req_placeholder not in placeholders:
                issues.append(ValidationIssue(
                    severity=ValidationSeverity.WARNING,
                    message=f"Missing required placeholder: {{{req_placeholder}}}",
                    rule="required_placeholder",
                    suggestion=f"Add {{{req_placeholder}}} placeholder to template"
                ))
        
        # Check for malformed placeholders
        malformed_pattern = re.compile(r'\{[^}]*$|\{[^}]*\n')
        for match in malformed_pattern.finditer(content):
            line_num = content[:match.start()].count('\n') + 1
            issues.append(ValidationIssue(
                severity=ValidationSeverity.ERROR,
                message="Malformed placeholder detected",
                line_number=line_num,
                rule="malformed_placeholder",
                suggestion="Ensure all placeholders are properly closed with }"
            ))
        
        return issues
    
    def _validate_by_type(self, content: str, template_type: str) -> List[ValidationIssue]:
        """Perform type-specific validation"""
        issues = []
        
        if template_type == 'html':
            issues.extend(self._validate_html(content))
        elif template_type == 'css':
            issues.extend(self._validate_css(content))
        elif template_type == 'js':
            issues.extend(self._validate_javascript(content))
        elif template_type == 'prompts':
            issues.extend(self._validate_prompt(content))
        
        return issues
    
    def _validate_html(self, content: str) -> List[ValidationIssue]:
        """Validate HTML template"""
        issues = []
        
        # Check for DOCTYPE
        if not re.search(r'<!DOCTYPE\s+html>', content, re.IGNORECASE):
            issues.append(ValidationIssue(
                severity=ValidationSeverity.WARNING,
                message="Missing HTML5 DOCTYPE declaration",
                rule="html_doctype",
                suggestion="Add <!DOCTYPE html> at the beginning"
            ))
        
        # Check for charset
        if not re.search(r'<meta\s+charset=', content, re.IGNORECASE):
            issues.append(ValidationIssue(
                severity=ValidationSeverity.WARNING,
                message="Missing charset meta tag",
                rule="html_charset",
                suggestion="Add <meta charset=\"UTF-8\"> in head section"
            ))
        
        # Check for unclosed tags (basic check)
        tag_pattern = re.compile(r'<(\w+)(?:\s[^>]*)?>|</(\w+)>')
        tags = []
        for match in tag_pattern.finditer(content):
            if match.group(1):  # Opening tag
                tags.append(match.group(1).lower())
            elif match.group(2):  # Closing tag
                if tags and tags[-1] == match.group(2).lower():
                    tags.pop()
        
        if tags:
            issues.append(ValidationIssue(
                severity=ValidationSeverity.WARNING,
                message=f"Potentially unclosed HTML tags: {', '.join(tags)}",
                rule="html_unclosed_tags",
                suggestion="Ensure all HTML tags are properly closed"
            ))
        
        return issues
    
    def _validate_css(self, content: str) -> List[ValidationIssue]:
        """Validate CSS template"""
        issues = []
        
        # Basic CSS syntax check
        brace_count = content.count('{') - content.count('}')
        if brace_count != 0:
            issues.append(ValidationIssue(
                severity=ValidationSeverity.ERROR,
                message="Mismatched CSS braces",
                rule="css_braces",
                suggestion="Ensure all CSS rules have matching opening and closing braces"
            ))
        
        return issues
    
    def _validate_javascript(self, content: str) -> List[ValidationIssue]:
        """Validate JavaScript template"""
        issues = []
        
        # Basic syntax checks
        paren_count = content.count('(') - content.count(')')
        brace_count = content.count('{') - content.count('}')
        bracket_count = content.count('[') - content.count(']')
        
        if paren_count != 0:
            issues.append(ValidationIssue(
                severity=ValidationSeverity.ERROR,
                message="Mismatched parentheses in JavaScript",
                rule="js_parentheses"
            ))
        
        if brace_count != 0:
            issues.append(ValidationIssue(
                severity=ValidationSeverity.ERROR,
                message="Mismatched braces in JavaScript",
                rule="js_braces"
            ))
        
        if bracket_count != 0:
            issues.append(ValidationIssue(
                severity=ValidationSeverity.ERROR,
                message="Mismatched brackets in JavaScript",
                rule="js_brackets"
            ))
        
        return issues

    def validate_iac_template(self, file_path: Path, content: str, template_type: str) -> ValidationResult:
        """
        Validate Infrastructure as Code templates (ARM, Bicep, Terraform)

        Args:
            file_path: Path to the IaC template file
            content: Template content
            template_type: Type of template (arm, bicep, terraform)

        Returns:
            ValidationResult with validation status and issues
        """
        import time
        start_time = time.time()

        issues = []

        # Basic validation
        issues.extend(self._validate_basic_structure(content, template_type))

        # Security validation
        if self.validation_rules.get('validate_security', True):
            issues.extend(self._validate_security(content))

        # IaC-specific validation
        if template_type.lower() == 'arm':
            issues.extend(self._validate_arm_template(content))
        elif template_type.lower() == 'bicep':
            issues.extend(self._validate_bicep_template(content))
        elif template_type.lower() == 'terraform':
            issues.extend(self._validate_terraform_template(content))

        # Size validation
        issues.extend(self._validate_size(content))

        validation_time = (time.time() - start_time) * 1000

        # Determine if template is valid (no errors)
        is_valid = not any(issue.severity == ValidationSeverity.ERROR for issue in issues)

        return ValidationResult(
            is_valid=is_valid,
            issues=issues,
            template_type=template_type,
            file_path=str(file_path),
            validation_time_ms=validation_time
        )

    def _validate_arm_template(self, content: str) -> List[ValidationIssue]:
        """Validate ARM template structure and syntax"""
        issues = []

        try:
            template = json.loads(content)

            # Check required ARM template sections
            required_sections = ['$schema', 'contentVersion']
            for section in required_sections:
                if section not in template:
                    issues.append(ValidationIssue(
                        severity=ValidationSeverity.ERROR,
                        message=f"Missing required ARM template section: {section}",
                        rule="arm_required_sections"
                    ))

            # Validate schema URL
            if '$schema' in template:
                schema = template['$schema']
                if 'deploymenttemplate.json' not in schema.lower():
                    issues.append(ValidationIssue(
                        severity=ValidationSeverity.WARNING,
                        message="ARM template schema may not be correct",
                        rule="arm_schema_validation"
                    ))

            # Check for resources section
            if 'resources' not in template:
                issues.append(ValidationIssue(
                    severity=ValidationSeverity.WARNING,
                    message="ARM template has no resources section",
                    rule="arm_no_resources"
                ))
            elif not isinstance(template['resources'], list):
                issues.append(ValidationIssue(
                    severity=ValidationSeverity.ERROR,
                    message="ARM template resources section must be an array",
                    rule="arm_resources_type"
                ))

            # Validate parameters section
            if 'parameters' in template and not isinstance(template['parameters'], dict):
                issues.append(ValidationIssue(
                    severity=ValidationSeverity.ERROR,
                    message="ARM template parameters section must be an object",
                    rule="arm_parameters_type"
                ))

        except json.JSONDecodeError as e:
            issues.append(ValidationIssue(
                severity=ValidationSeverity.ERROR,
                message=f"Invalid JSON in ARM template: {e}",
                rule="arm_json_syntax"
            ))

        return issues

    def _validate_bicep_template(self, content: str) -> List[ValidationIssue]:
        """Validate Bicep template structure and syntax"""
        issues = []

        # Check for basic Bicep syntax elements
        bicep_keywords = ['param', 'var', 'resource', 'output', 'module']
        found_keywords = []

        for keyword in bicep_keywords:
            if re.search(rf'\b{keyword}\s+', content):
                found_keywords.append(keyword)

        if not found_keywords:
            issues.append(ValidationIssue(
                severity=ValidationSeverity.WARNING,
                message="No Bicep keywords found - may not be a valid Bicep template",
                rule="bicep_keywords"
            ))

        # Check for resource declarations
        resource_pattern = r'resource\s+\w+\s+\'[^\']+\'\s*='
        if not re.search(resource_pattern, content):
            issues.append(ValidationIssue(
                severity=ValidationSeverity.INFO,
                message="No resource declarations found in Bicep template",
                rule="bicep_no_resources"
            ))

        # Check for proper string syntax
        string_issues = re.findall(r"'[^']*\n[^']*'", content)
        if string_issues:
            issues.append(ValidationIssue(
                severity=ValidationSeverity.WARNING,
                message="Potential multiline string issues found",
                rule="bicep_string_syntax"
            ))

        return issues

    def _validate_terraform_template(self, content: str) -> List[ValidationIssue]:
        """Validate Terraform template structure and syntax"""
        issues = []

        # Check for basic Terraform syntax elements
        tf_blocks = ['resource', 'data', 'variable', 'output', 'provider', 'terraform']
        found_blocks = []

        for block in tf_blocks:
            if re.search(rf'\b{block}\s+"', content):
                found_blocks.append(block)

        if not found_blocks:
            issues.append(ValidationIssue(
                severity=ValidationSeverity.WARNING,
                message="No Terraform blocks found - may not be a valid Terraform template",
                rule="terraform_blocks"
            ))

        # Check for resource blocks
        if 'resource' not in found_blocks:
            issues.append(ValidationIssue(
                severity=ValidationSeverity.INFO,
                message="No resource blocks found in Terraform template",
                rule="terraform_no_resources"
            ))

        # Basic HCL syntax validation
        brace_count = content.count('{') - content.count('}')
        if brace_count != 0:
            issues.append(ValidationIssue(
                severity=ValidationSeverity.ERROR,
                message="Mismatched braces in Terraform template",
                rule="terraform_braces"
            ))

        return issues
    
    def _validate_prompt(self, content: str) -> List[ValidationIssue]:
        """Validate prompt template"""
        issues = []
        
        # Check for proper formatting markers
        if not re.search(r'===.*===', content):
            issues.append(ValidationIssue(
                severity=ValidationSeverity.INFO,
                message="No section markers found in prompt template",
                rule="prompt_formatting",
                suggestion="Consider using === markers for section organization"
            ))
        
        return issues
    
    def _validate_size(self, content: str) -> List[ValidationIssue]:
        """Validate template size"""
        issues = []
        
        max_size_mb = self.validation_rules.get('max_template_size_mb', 10)
        size_mb = len(content.encode('utf-8')) / (1024 * 1024)
        
        if size_mb > max_size_mb:
            issues.append(ValidationIssue(
                severity=ValidationSeverity.WARNING,
                message=f"Template size ({size_mb:.2f}MB) exceeds recommended limit ({max_size_mb}MB)",
                rule="template_size",
                suggestion="Consider splitting large templates into smaller components"
            ))
        
        return issues
