Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
CRITICAL,DP-3,function-settings.bicep,66,"The Application Insights instrumentation key ('app_insights_key') is being set as an app setting in plain text, albeit as a @secure() parameter. According to ASB DP-3, sensitive secrets such as instrumentation keys should be stored in Azure Key Vault and referenced securely, not included inline in app settings.",Store the Application Insights instrumentation key in Azure Key Vault and reference it using a Key Vault reference in the App Service app settings.,N/A,AI,Generic
CRITICAL,NS-1,keyvault.bicep,18,"The Key Vault 'networkAcls' configuration sets 'defaultAction' to 'Allow', which makes the vault accessible from any public IP unless explicitly denied. This violates network security best practices for restricting sensitive resources to only trusted networks.","Set 'defaultAction' to 'Deny' in the Key Vault 'networkAcls' configuration. Explicitly permit access only from approved networks, IP addresses, or Azure services that require it.",N/A,AI,Generic
CRITICAL,NS-2,keyvault.bicep,18,"The Key Vault's public endpoint is exposed due to 'defaultAction' being 'Allow', enabling access over the Internet. This increases risk of unauthorized access/exploitation.","Configure 'networkAcls.defaultAction' to 'Deny', use 'ipRules' and 'virtualNetworkRules' to restrict access, and consider disabling the public endpoint if not needed.",N/A,AI,Generic
CRITICAL,NS-2,storage-accounts.bicep,22,"The 'networkAcls' configuration for all storage accounts sets 'defaultAction' to 'Allow'. This permits access from all public networks except for explicitly denied addresses, exposing the storage account to the internet or unintended sources. This violates the control requiring the minimization of public endpoint exposure.",Set 'networkAcls.defaultAction' to 'Deny' to ensure that only explicitly allowed IP ranges and subnets can access the storage account. Review and restrict 'bypass' settings as well.,N/A,AI,Generic
HIGH,NS-5,app-config.bicep,7,"The Azure App Configuration instance is not configured with a private endpoint, leaving the service accessible over public internet by default. This increases the risk of unauthorized or malicious access.",Configure a private endpoint (Microsoft.Network/privateEndpoints) for the App Configuration resource. This will ensure traffic to the resource remains on the Azure backbone and is accessible only within your virtual network.,N/A,AI,Generic
HIGH,NS-2,app-config.bicep,7,"There is no restriction or secured access on the public endpoint of Azure App Configuration. Without IP restrictions or firewall configuration, the endpoint is publicly accessible, violating best practices for minimizing exposure.","Restrict public access by enabling public network access to 'Disabled', using firewall rules to allow only trusted sources, or enforce private endpoint use.",N/A,AI,Generic
HIGH,DP-3,app-config.bicep,15,"App Configuration key-value pairs ('keyValues') may contain sensitive configuration data, but the template permits direct inline assignment of secrets or sensitive values. This risks accidental disclosure if secrets are hardcoded or improperly referenced.","Ensure that sensitive values (such as secrets, keys, or connection strings) are stored in Azure Key Vault and referenced securely via Key Vault references rather than being set directly or hardcoded in parameter files or templates.",N/A,AI,Generic
HIGH,NS-2,autoscale-settings.bicep,1,"The template references a storage account (func_storage_account_id) and possibly other Azure resources (server_farm_id) by resource ID, but there are no controls in place to restrict public endpoint exposure. Storage Accounts, by default, expose public endpoints, which can increase the risk of unauthorized access or data leaks.","Verify that the referenced storage account and other service resources deny public network access unless specifically required. For Storage Accounts, configure the 'networkAcls' property to allow only trusted subnets or private endpoints.",N/A,AI,Generic
HIGH,NS-5,event-grid.bicep,19,"The Event Grid System Topics are configured to receive events from storage accounts, but there is no evidence of private endpoints being used for the associated storage account(s) or Event Grid topic(s). This increases the risk of exposure to public network traffic.","Configure Azure Private Endpoints for all associated storage accounts and, if possible, for Event Grid endpoints. Ensure access to these resources is only possible from authorized virtual networks.",N/A,AI,Generic
HIGH,NS-1,event-grid.bicep,19,"There is no evidence that network security measures such as Network Security Groups (NSGs) or Azure Firewall are implemented to restrict access to the underlying storage accounts or Event Grid endpoints. Without these protections, services could be exposed to unrestricted network traffic.","Apply NSGs to restrict inbound and outbound access to the storage accounts referenced by 'storageFuzzId', 'storageCorpusIds', and monitor for access anomalies. Consider using Azure Firewall for additional network layer protection.",N/A,AI,Generic
HIGH,NS-2,event-grid.bicep,19,The template does not show any configuration to restrict or protect public endpoints for the storage accounts or Event Grid system topics. Unsecured public endpoints may allow unauthorized access.,"Ensure that public network access is disabled or restricted for the storage accounts and Event Grid endpoints, and implement access controls and private endpoints to minimize exposure.",N/A,AI,Generic
HIGH,NS-1,function-settings.bicep,40,"The Storage Account resource 'funcStorage' is defined as 'existing', but there is no evidence in this file of any network security measures such as Network Security Groups (NSGs) or Firewall rules protecting the storage account, as required by ASB NS-1.",Ensure that the referenced storage account is protected with NSGs and/or Azure Firewall by restricting access to only required networks and clients. Consider adding private endpoint configuration or explicitly setting network rules.,N/A,AI,Generic
HIGH,NS-2,function-settings.bicep,40,"The Storage Account resource 'funcStorage' does not have any indication of public endpoint restrictions or private endpoints. By default, storage accounts have public endpoints enabled, risking exposure to the internet.",Update the storage account configuration to disable public network access or configure a Private Endpoint to restrict access to only trusted networks.,N/A,AI,Generic
HIGH,NS-5,function-settings.bicep,40,"The Storage Account resource 'funcStorage' is not configured with a Private Endpoint, nor are there settings present to enforce access over private networks. This leaves the resource potentially accessible over public networks.",Implement an Azure Private Endpoint for the storage account to ensure all access is performed securely over private networks only.,N/A,AI,Generic
HIGH,NS-1,function.bicep,85,"The storage account resource 'funcStorage' is referenced as 'existing', but there is no evidence in this template that it is protected by a Network Security Group (NSG), Azure Firewall, nor does it use Private Endpoints. This leaves the storage account potentially exposed to public network access, violating the requirement to protect sensitive resources.","Ensure that the referenced storage account restricts public network access using NSGs, Azure Firewall, or private endpoints. If not managed within this template, verify configurations in the referenced resource.",N/A,AI,Generic
HIGH,NS-2,function.bicep,85,"The referenced storage account 'funcStorage' may have a publicly accessible endpoint, as there is no configuration to disable public network access or use private endpoints. Application logs are uploaded using a SAS token to a potentially public endpoint, increasing risk of unauthorized access.",Configure the storage account to deny public network access and require access via private endpoints. Regenerate or rotate SAS tokens if exposure is suspected.,N/A,AI,Generic
HIGH,DP-3,function.bicep,109,"A shared access signature (SAS) token is generated programmatically and inlined into the application logging configuration ('sasUrl'). This exposes sensitive SAS tokens within the deployment, increasing the risk of secret leakage and unauthorized access to storage data.","Avoid inlining SAS tokens within code or IaC. Store secrets in Azure Key Vault and reference them securely. Also, use managed identity and RBAC for storage access where possible.",N/A,AI,Generic
HIGH,NS-5,function.bicep,85,The storage account used for logging does not use private endpoints in the context of this deployment. Using SAS tokens to write logs to public storage endpoints increases exposure to data exfiltration risks.,Integrate the storage account with Azure Private Endpoint to restrict access to trusted networks only. Update the function to use the private endpoint URL for uploads.,N/A,AI,Generic
HIGH,NS-1,hub-network.bicep,2,"The virtual network 'hub-vnet' and its subnet 'hub-subnet' are defined without any associated Network Security Group (NSG) or Azure Firewall, leaving all network traffic unfiltered at the subnet level. This exposes connected resources to potential unauthorized network access and traffic, violating network protection standards.","Associate an appropriate NSG to the 'hub-subnet' to define and enforce allowed inbound and outbound traffic. Where appropriate, consider deploying an Azure Firewall or a similar firewall solution to centrally control and monitor network traffic.",N/A,AI,Generic
HIGH,NS-3,hub-network.bicep,2,"No Network Security Group (NSG) is applied to the 'hub-subnet', meaning there are no access control rules restricting network access to resources deployed in this subnet.",Create an NSG with least privilege rules and associate it with the 'hub-subnet' to ensure that only approved traffic is allowed into and out of the subnet.,N/A,AI,Generic
HIGH,DP-3,instance-config.bicep,10,"The parameter 'specificConfig' and its subfields, such as 'Admins', 'cli_client_id', and possible others, are ingested directly from external JSON files and handled in clear text in both variables and output objects. Keys, secrets, administrative identifiers, or sensitive configuration parameters may be exposed in source or output, rather than being stored in Azure Key Vault as per ASB DP-3.","Ensure that any sensitive values or secrets referenced in the external JSON files or Bicep parameters (such as client IDs, admin usernames, or any credentials) are instead stored securely in Azure Key Vault, and use Key Vault references or secret pull at deploy time. Do not expose sensitive data as plain parameters or outputs in storage or logs.",N/A,AI,Generic
HIGH,NS-1,instance-config.bicep,23,"The network configuration directly defines address spaces and subnets (address_space: '10.0.0.0/8', subnet: '10.0.0.0/16'), but does not reference, create, or enforce any Network Security Groups (NSGs), firewalls, or other network-level controls on these subnets/resources. ASB NS-1 requires protecting resources using NSGs or Azure Firewall to restrict network access.",Explicitly define and associate NSGs with relevant subnets and/or compute resources to control inbound and outbound traffic per the principle of least privilege. Define firewall rules as appropriate to segment and secure workloads in these networks.,N/A,AI,Generic
HIGH,NS-3,instance-config.bicep,23,There are no references to Network Security Groups (NSGs) or implementation of network filtering on the defined subnets or resources. ASB NS-3 recommends the use of NSGs to restrict both inbound and outbound traffic to and from VMs/subnets.,"Create and attach NSGs to all subnets and/or VM network interfaces with rules limiting traffic to only what is strictly required (administration, application communication) and deny everything else by default.",N/A,AI,Generic
HIGH,NS-2,ip-rules.bicep,3,"The 'corpNetIps' variable grants 'Allow' access to very large public IP ranges (e.g., '*******/8', '********/8'), which encompass millions of potentially untrusted addresses. Allowing access from such broad public IP ranges exposes resources to a wide attack surface and does not properly restrict public endpoint access.","Restrict allowed IP addresses to specific, trusted public IPs or ranges that are actually required for business needs. Avoid using broad ranges like /8. Where possible, use private network connections (VPN, ExpressRoute, Private Endpoints) and lock down public endpoints to only need-to-access addresses.",N/A,AI,Generic
HIGH,AM-1,keyvault.bicep,29,"No access policies (accessPolicies is empty) are defined for the Key Vault, and RBAC authorization is enabled. Without assignment of least-privilege roles, users or services may end up with inappropriate permissions.","Define and assign least-privilege RBAC roles to users/applications that require access to the Key Vault, limiting permissions to the minimum necessary. Regularly review role assignments to remove excess privileges.",N/A,AI,Generic
HIGH,DP-3,operational-insights.bicep,70,"The output 'appInsightsInstrumentationKey' exposes the Application Insights instrumentation key, which is a sensitive credential that could allow unauthorized data ingestion or leakage if accessed by untrusted parties.",Do not output sensitive values like instrumentation keys. Store secrets and sensitive keys in Azure Key Vault and access them securely from trusted applications only.,N/A,AI,Generic
HIGH,DP-3,operational-insights.bicep,69,"The output 'appInsightsAppId' exposes the Application Insights App ID. While this is less sensitive than the instrumentation key, it can still assist attackers in identifying targets or performing information gathering if outputs are broadly accessible.",Restrict the exposure of identifiers such as App IDs to only trusted users and systems. Avoid outputting such values unless necessary and ensure output scopes are limited.,N/A,AI,Generic
HIGH,NS-1,scaleset-networks.bicep,1,"No Network Security Group (NSG) is associated with the scaleset subnet or any part of the network. Without NSGs, critical resources such as VMs in the subnet are unprotected from unauthorized or malicious network access.",Deploy and associate a Network Security Group to the 'scaleset' subnet to restrict and control inbound and outbound network traffic according to least privilege.,N/A,AI,Generic
HIGH,NS-3,scaleset-networks.bicep,1,"Network Security Groups (NSGs) are not defined or applied to any subnet in this template, leaving the virtual network and its resources exposed to unrestricted network communication.","Define and attach NSGs to restrict inbound and outbound access to only required ports and protocols for each subnet, especially the scaleset subnet.",N/A,AI,Generic
HIGH,NS-1,server-farms.bicep,1,"No network security measures (NSGs or Azure Firewall) are defined for the App Service or referenced Key Vaults. App Service plans by default allow public network access, which may expose resources to external threats if not properly restricted.","Use service endpoints, access restrictions for the App Service, or deploy within a subnet protected by NSGs or an Azure Firewall. For Key Vaults, ensure network ACLs restrict access to only required addresses or subnets.",N/A,AI,Generic
HIGH,NS-2,server-farms.bicep,1,"App Service and referenced Key Vaults are deployed without explicit restrictions on public endpoints, potentially exposing them to the public internet if default settings remain.","Implement App Service Access Restrictions to only allow trusted IPs or vNET integration, and configure Key Vault firewalls to only allow required access.",N/A,AI,Generic
HIGH,NS-2,signalR.bicep,4,"The Azure SignalR Service is deployed without specifying any network ACLs or private endpoint configuration. By default, SignalR Service exposes a public endpoint, which increases the risk of unauthorized access and exposure to the internet.",Restrict public network access by configuring the SignalR resource with private endpoint connectivity or service endpoints. Use `networkACLs` property to limit allowed IP address ranges or set to 'Private' network mode where possible. Reference: https://learn.microsoft.com/en-us/azure/azure-signalr/signalr-overview-security#restrict-public-network-access,N/A,AI,Generic
HIGH,NS-2,storage-accounts.bicep,25,"The 'bypass' property is set to 'AzureServices, Logging, Metrics', which allows Azure services to bypass network restrictions. This broad bypass may unnecessarily expose storage accounts if not justified by a business need.",Minimize the 'bypass' scope. Only include 'AzureServices' if required for functionality. Prefer restricting bypass where possible and document the justification for each allowed bypass.,N/A,AI,Generic
HIGH,DP-2,storage-accounts.bicep,82,"The CORS rules on the storage account blob services specify 'allowedHeaders' and 'exposedHeaders' as ['*'], which allows all headers to be sent and exposed, and 'allowedOrigins' is driven by the parameter 'cors_origins'. This could expose sensitive data during transit and enables broader client access than necessary, raising the risk of downgrade or data leak.","Restrict 'allowedHeaders' and 'exposedHeaders' to only those specifically needed. Ensure 'allowedOrigins' only includes trusted, business-required domains and not '*'.",N/A,AI,Generic
MEDIUM,NS-1,autoscale-settings.bicep,1,"There is no evidence in the template of any network security controls (such as Network Security Groups or Azure Firewall) being applied to the resources defined or referenced. This means that the autoscale operation target (e.g., an App Service Plan or related server farm) and the associated Storage Account could potentially be left unprotected from unauthorized network access.",Review the network architecture and ensure that the target resources (including server farms and storage accounts) are protected with appropriate NSG (for VMs or subnets) or integrated with Azure Firewall/perimeter controls to restrict unnecessary traffic.,N/A,AI,Generic
MEDIUM,DP-3,autoscale-settings.bicep,1,"Sensitive values (such as 'server_farm_id', 'func_storage_account_id', and 'workspaceId') are provided as plain parameters, but it is unclear from the template if any Key Vault reference or secure parameter mechanism is used to protect these identifiers and potential secrets. While no direct secrets are exposed, best practices require storage of sensitive values in Azure Key Vault.","Parameterize sensitive identifiers and reference them securely from Azure Key Vault wherever possible, especially if these values could be used to escalate access or enumerate the environment.",N/A,AI,Generic
MEDIUM,DP-3,event-grid.bicep,2,"The parameters referencing resource IDs ('storageFuncId', 'storageFuzzId', etc.) are passed into the template potentially from upstream sources. There is no evidence that sensitive or secret values (such as keys or connection strings) are included here, but if they are, they must be stored in Azure Key Vault and never hardcoded or passed as plain parameters.","Store any sensitive information such as keys, secrets, or connection strings in Azure Key Vault. Ensure template parameters do not include sensitive data directly.",N/A,AI,Generic
MEDIUM,NS-3,function-settings.bicep,40,"There is no evidence of Network Security Groups (NSGs) being associated with the Storage Account (funcStorage) or the App Service resources. Without NSGs, there are no granular controls over inbound/outbound traffic.","Create and associate appropriate Network Security Groups (NSGs) to control inbound and outbound traffic to related subnets or VMs. For Storage, also consider using service endpoints or private endpoints together with NSGs.",N/A,AI,Generic
MEDIUM,DP-2,function.bicep,85,"The storage account resource configuration does not specify whether encryption at rest is enabled. Azure Storage enables encryption by default, but this should be explicitly set or verified, especially for existing resources.","Explicitly enable and monitor encryption at rest for the storage account. If the resource is managed outside this template, ensure encryption is configured accordingly.",N/A,AI,Generic
MEDIUM,DP-1,function.bicep,85,"Encryption at rest is not defined for the referenced storage account 'funcStorage'. Although encryption is enabled by default on Azure Storage, explicit configuration is recommended to ensure compliance and transparency.",Explicitly configure and enforce encryption at rest for the storage account via the template or by validating the existing resource's configuration.,N/A,AI,Generic
MEDIUM,NS-4,hub-network.bicep,2,There is no indication of an Azure Firewall or third-party firewall protecting the resources within the virtual network. This leaves the network lacking robust centralized traffic inspection and threat protection.,"Deploy an Azure Firewall or third-party firewall appliance to the hub network to provide centralized policy enforcement, monitoring, and protection against known threats. Integrate it in the network topology as required.",N/A,AI,Generic
MEDIUM,NS-8,hub-network.bicep,2,"The configuration does not explicitly enable or reference Azure DDoS Protection for the virtual network. Without this, resources may be vulnerable to distributed denial-of-service attacks.",Enable Azure DDoS Protection Standard on the virtual network to provide enhanced mitigation against DDoS attacks targeting cloud resources.,N/A,AI,Generic
MEDIUM,NS-9,hub-network.bicep,2,"There is no configuration for network traffic monitoring, such as enabling Network Watcher or diagnostic settings for flow logs. Lack of monitoring limits threat detection and forensic capability.",Enable Azure Network Watcher and configure diagnostic settings to collect and analyze flow logs and other relevant network events for the virtual network and subnets.,N/A,AI,Generic
MEDIUM,NS-10,instance-config.bicep,23,"The configuration does not mention use of Azure Bastion for secure VM management, leaving a risk that RDP/SSH might be exposed or managed insecurely. ASB NS-10 requires the use of Bastion hosts to avoid direct exposure of management ports.","Use Azure Bastion for remote management of all VMs. Do not expose SSH/RDP ports directly to the internet, and document Bastion deployment in your IaC if VMs need management access.",N/A,AI,Generic
MEDIUM,NS-7,instance-config.bicep,23,There is no implementation of Just-in-Time (JIT) VM access or a mechanism to reduce exposure time of management ports (SSH/RDP). ASB NS-7 requires enabling JIT VM access.,Enable JIT VM access via Azure Security Center or corresponding policy in your pipeline to ensure management ports are accessible only for limited time to authorized users on demand.,N/A,AI,Generic
MEDIUM,DP-1,instance-config.bicep,32,"VM image definitions and compute resources are referenced but there is no explicit configuration for encryption at rest on VM disks, as required by ASB DP-1.",Explicitly enable encryption at rest for all managed disks attached to VMs. This can be achieved by setting encryption-enabled properties on VM/VMSS disk resources or attaching encrypted disks by default.,N/A,AI,Generic
MEDIUM,DP-4,instance-config.bicep,32,"The template omits controlled configuration or enforcement of managed disk encryption (e.g., no 'encryptionSettings' for VM disks or no mention of managed disks at all in staticConfig), violating ASB DP-4.",Ensure all VM disks are Azure managed disks with platform-managed or customer-managed encryption keys enabled by default for any VM created by this configuration.,N/A,AI,Generic
MEDIUM,NS-5,ip-rules.bicep,3,"The template only defines IP allow rules for network connection but does not leverage Private Endpoints for resource access, missing an opportunity to fully isolate resources from the public internet as recommended for sensitive resources.","Implement Azure Private Endpoints to ensure sensitive resources are only accessible within the private network, eliminating exposure to the public internet. Combine with restrictive IP rules for additional defense.",N/A,AI,Generic
MEDIUM,NS-6,ip-rules.bicep,3,No usage of Virtual Network Service Endpoints is apparent in the template. Relying solely on IP-based rules does not provide the strongest binding of resources to designated VNets.,"Consider implementing Virtual Network Service Endpoints to tie Azure services (such as Storage or SQL) directly to your VNets, reducing exposure to other Azure tenants and additional risks from IP-based rules.",N/A,AI,Generic
MEDIUM,NS-1,ip-rules.bicep,3,No use of Network Security Groups (NSGs) or Azure Firewall is specified to enforce granular traffic restrictions beyond IP allow-lists. Relying solely on IP rules may not provide sufficient layer-4/layer-7 protection for critical resources.,Deploy Network Security Groups or Azure Firewall in conjunction with existing IP rules to provide deeper traffic inspection and enforce granular security policies at both subnet and resource levels.,N/A,AI,Generic
MEDIUM,NS-2,scaleset-networks.bicep,7,A public IP address ('scalesetOutboundIp') is being provisioned without any restriction or mention of NSG/firewall controls. Exposing public endpoints without sufficient protection increases the risk of unauthorized access and attacks.,"Ensure all public-facing IPs are protected by NSGs and/or an Azure Firewall, and restrict access to necessary sources only.",N/A,AI,Generic
MEDIUM,NS-3,server-farms.bicep,1,No Network Security Groups (NSGs) are defined or referenced for associated resources. This may lead to insufficient isolation or protection for network traffic related to the App Service or Key Vault.,"Deploy your App Service within a vNET subnet governed by an NSG with least privilege rules, and ensure any supporting resources (like the Key Vault) are similarly protected, where possible.",N/A,AI,Generic
MEDIUM,NS-5,server-farms.bicep,1,"There is no configuration for private endpoints for the App Service or referenced Key Vaults, risking exposure of sensitive operations over public infrastructure.",Enable Private Endpoints for the referenced Key Vaults and consider App Service vNET integration with private endpoints or access only.,N/A,AI,Generic
MEDIUM,IM-8,server-farms.bicep,1,No managed identities are configured for the App Service or referenced in the template. This may result in use of less secure credential management practices when accessing Key Vault secrets (used for certificates).,Configure a user-assigned or system-assigned managed identity for the App Service and assign it appropriate Key Vault access policies.,N/A,AI,Generic
MEDIUM,DP-6,server-farms.bicep,81,"There is no indication of Customer-Managed Key (CMK) encryption for resources such as App Service or Key Vault, which is recommended for strong data protection in sensitive environments.","Wherever CMK is available, configure your App Service and Key Vault to use Customer-Managed Keys for encryption at rest.",N/A,AI,Generic
MEDIUM,DP-3,storage-accounts.bicep,82,"The CORS configuration allows any header ('*') and origin is parameterized. While parameters are used, this may inadvertently disclose sensitive data through headers or enable data exfiltration if misconfigured.","Review and minimize the list of allowed origins. Limit allowed and exposed headers to those explicitly required for application functionality, and avoid using '*'.",N/A,AI,Generic
LOW,NS-8,ip-rules.bicep,0,"The template does not reference enabling Azure DDoS Protection, which is recommended to safeguard resources exposed to the internet, especially when public-facing IP rules are used.",Enable Azure DDoS Protection on the relevant virtual networks to mitigate the risk of distributed denial-of-service attacks.,N/A,AI,Generic
LOW,NS-6,scaleset-networks.bicep,38,"Service endpoints are not configured for the scaleset subnet, missing an opportunity to secure Azure service traffic directly within the Azure backbone network.","If this subnet will communicate with Azure PaaS services (e.g., Storage, SQL), explicitly define serviceEndpoints for those services in the subnet's configuration.",N/A,AI,Generic
LOW,DP-3,server-farms.bicep,98,"The property 'settingValue' in resource 'serverFarms_AntMDS_CERTIFICATE_PASSWORD_GENEVACERT' is explicitly set as an empty string, which indicates certificate password might not be set or could be handled insecurely in the future.",Never store certificate passwords in plaintext or as empty values. Securely reference such values from Azure Key Vault and ensure password protection is enforced for private keys when exporting or handling certificates.,N/A,AI,Generic
LOW,DP-2,server-farms.bicep,1,No explicit configuration for enforcing TLS 1.2+ for encryption in transit on the App Service is provided. This may allow weaker protocols by default.,Set 'minTlsVersion' property in App Service configuration to '1.2' or higher to enforce TLS 1.2+ for all inbound connections.,N/A,AI,Generic
LOW,DP-1,storage-accounts.bicep,16,"Encryption at rest is not explicitly configured for any storage account. Although Azure Storage is encrypted at rest by default, best practices recommend explicit configuration, such as customer-managed keys, for greater control and compliance.","Specify explicit encryption configuration under the 'properties.encryption' section, and consider using customer-managed keys if compliance requires.",N/A,AI,Generic
