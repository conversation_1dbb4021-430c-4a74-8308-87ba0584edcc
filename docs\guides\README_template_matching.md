# Configuration-Driven Template-Parameter Matching

This feature enables flexible matching of Azure ARM templates with their parameter files based on configurable patterns and strategies.

## Configuration Options

Add these settings to your `.env` file:

```bash
# Template and Parameter File Matching Configuration
TEMPLATE_IDENTIFIERS=Template,template,main,deploy,ArmTemplate
PARAMETER_IDENTIFIERS=Param,Parameter,params,parameters,ArmParam

# File naming patterns (comma-separated)
TEMPLATE_PATTERNS=*Template*,*template*,main.json,deploy*.json
PARAMETER_PATTERNS=*Param*,*Parameter*,*params*,*parameters*

# Matching strategy (strict, fuzzy, smart)
MATCHING_STRATEGY=smart

# Enable parameter expansion (true/false)
ENABLE_PARAMETER_EXPANSION=true

# Parameter file search depth (how many folders up/down to search)
PARAM_SEARCH_DEPTH=3

# Advanced matching configuration
MATCH_BY_PREFIX=true
MATCH_BY_SUFFIX=true
MATCH_BY_SUBSTRING=true
MIN_SIMILARITY_SCORE=0.7
```

## Matching Strategies

### 1. Strict Matching
- Only matches files with exact naming conventions
- Example: `template.json` → `template.parameters.json`

### 2. Fuzzy Matching
- Uses similarity scoring to match files
- Configurable similarity threshold
- Example: `deploy-storage.json` → `storage-params.json`

### 3. Smart Matching (Default)
- Combines multiple strategies with fallback:
  1. Tries suffix matching first (e.g., `-FabricAppA`)
  2. Tries prefix matching if enabled
  3. Uses substring matching if enabled
  4. Falls back to fuzzy matching if needed

## Example File Pairs

```
Template Files:
- deploy-fabric-app.ArmTemplate-FabricAppA.json
- storage-account.Template.json
- main.bicep

Parameter Files:
- deploy-fabric-app-params.ArmParam-FabricAppA.json
- storage-account.parameters.json
- main.bicepparam
```

## Usage

1. **Basic Usage**
   ```python
   reviewer = SecurityPRReviewer(local_folder="path/to/files")
   files = reviewer.analyze_folder("path/to/files")
   findings = reviewer.analyze_files(files)
   ```

2. **Enable Parameter Expansion**
   ```python
   os.environ["ENABLE_PARAMETER_EXPANSION"] = "true"
   reviewer = SecurityPRReviewer(local_folder="path/to/files")
   ```

3. **Running Tests**
   ```bash
   python run_tests.py
   ```

## Features

1. **Flexible Matching**
   - Multiple matching strategies
   - Configurable patterns
   - Cross-folder parameter file search

2. **Parameter Expansion**
   - Expands templates with parameter values
   - Analyzes actual deployed configurations
   - Detects security issues in parameter values

3. **Security Analysis**
   - Pattern-based detection
   - AI-powered analysis with Azure OpenAI
   - Enhanced findings with parameter context

## Best Practices

1. Use consistent naming conventions
2. Keep parameter files close to their templates
3. Use descriptive suffixes for related files
4. Enable parameter expansion for thorough analysis

## Troubleshooting

- If matches aren't found:
  - Check file naming patterns
  - Try different matching strategies
  - Increase PARAM_SEARCH_DEPTH
  - Enable debug logging

- If parameter expansion fails:
  - Verify parameter file format
  - Check for required parameters
  - Look for JSON syntax errors
