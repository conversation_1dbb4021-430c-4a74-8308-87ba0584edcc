# Current Data Flow Analysis

## Overview
This document maps the complete data flow from loading security benchmark data through generating recommendations in the IaC Guardian system.

## Data Flow Architecture

### 1. Initialization Phase

#### SecurityPRReviewer.__init__()
```python
# Entry points:
- SecurityPRReviewer(local_folder="path")  # Local analysis mode
- SecurityPRReviewer(repo_id, pr_id)       # PR analysis mode

# Key initialization steps:
1. _load_environment()                     # Load .env configuration
2. _init_openai_client()                   # Initialize Azure OpenAI client
3. benchmark_data = None                   # Defer benchmark loading
```

#### Environment Configuration Loading
```python
# Priority configuration from .env:
BENCHMARK_SOURCE_PRIORITY=csv,json,excel,fallback
ENFORCE_DOMAIN_PRIORITY=true
DOMAIN_PRIORITY_ORDER=Identity Management,Network Security,Data Protection,Access Management,Monitoring and Logging
```

### 2. Benchmark Preparation Phase

#### prepare_benchmark() - Main Entry Point
```python
# Two implementations:
1. security_pr_review.py:prepare_benchmark()     # Legacy implementation
2. security_opt.py:prepare_benchmark()           # Optimized implementation

# Optimized flow (security_opt.py):
for source in benchmark_source_priority:
    if source == 'csv':
        controls = _load_optimized_csv_benchmark()
    elif source == 'json':
        controls = _load_json_benchmark()
    elif source == 'excel':
        controls = _load_excel_benchmark()
    elif source == 'fallback':
        controls = _get_optimized_fallback_controls()
```

#### CSV Loading Pipeline (_load_optimized_csv_benchmark)
```python
# Priority-ordered CSV files:
prioritized_csv_files = [
    ("identity_management.csv", "Identity Management", 1),
    ("network_security.csv", "Network Security", 2),
    ("data_protection.csv", "Data Protection", 3),
    ("access_management.csv", "Access Management", 4),
    ("monitoring_logging.csv", "Monitoring and Logging", 5)
]

# Processing steps:
1. Load CSV with pandas.read_csv()
2. Map columns using field_mappings dictionary
3. Extract control data: ID, domain, name, description, guidance
4. Determine severity with _determine_optimized_control_severity()
5. Extract resource types from guidance text
6. Validate essential fields (name, description)
7. Add to controls list with source attribution
```

#### JSON Loading Pipeline (_load_json_benchmark)
```python
# Fallback when CSV unavailable:
json_path = benchmark_dir / "Azure_Security_Benchmark_v3.json"
with open(json_path, "r") as f:
    data = json.load(f)
controls = data.get("controls", [])
```

#### Excel Loading Pipeline (_load_excel_benchmark)
```python
# Official Microsoft source processing:
1. Check if Excel file exists, download if needed
2. _convert_benchmark_to_json() - Convert Excel to structured JSON
3. Cache result for future use
4. Return controls array
```

### 3. Data Processing & Indexing Phase

#### Control Indexing (_build_control_id_index)
```python
# Build fast lookup index:
for control in controls:
    control_id = control.get("id", "")
    if control_id:
        self.control_id_index[control_id] = {
            "id": control_id,
            "domain": control.get("domain", ""),
            "name": control.get("name", ""),
            "description": control.get("description", ""),
            "implementation": control.get("implementation", ""),
            "azure_guidance": control.get("azure_guidance", "")
        }
```

#### Resource-Control Correlation (_build_resource_control_correlations)
```python
# Dynamic mapping generation:
1. _get_dynamic_resource_mappings() - Generate resource-to-domain relationships
2. Filter controls that exist in benchmark
3. _sort_controls_by_domain_priority() - Apply domain ordering
4. Cache in self.resource_control_cache for performance

# Resource mapping structure:
resource_control_cache[resource_type] = {
    "primary_domains": ["Identity Management", "Network Security"],
    "relevant_controls": [control_objects],
    "control_ids": ["IM-1", "IM-2", "NS-1"]
}
```

#### Domain Priority Sorting (_sort_controls_by_domain_priority)
```python
# Priority order enforcement:
domain_priority_map = {
    "Identity Management": 1,
    "Network Security": 2,
    "Data Protection": 3,
    "Privileged Access": 4,
    "Logging and Threat Detection": 5
}

# Sort controls by domain priority, then by control ID
```

### 4. Analysis Execution Phase

#### File Analysis Entry Points
```python
# Main analysis methods:
1. analyze_folder(folder_path) -> List[Dict]     # Scan directory recursively
2. analyze_files(files) -> List[Dict]            # Analyze file list
3. run() -> None                                 # Full PR workflow
```

#### Resource Type Detection (_determine_resource_type)
```python
# Multi-strategy detection:
1. Check ARM template "type" field
2. Check Terraform resource blocks
3. Check Bicep resource declarations
4. Keyword matching against azure_resource_mappings.json
5. Fallback to "Generic"
```

#### Security Analysis Pipeline (analyze_files)
```python
for file_info in files:
    # Step 1: Resource type detection
    resource_type = _determine_resource_type(file_path, content)
    
    # Step 2: Pattern-based detection (fast)
    pattern_findings = _detect_common_issues(file_info)
    
    # Step 3: AI-powered analysis (comprehensive)
    ai_findings = _analyze_with_openai(file_info, resource_type)
    
    # Step 4: Combine and validate findings
    findings.extend(pattern_findings + ai_findings)
```

#### AI Analysis Pipeline (_analyze_with_openai)
```python
# Context preparation:
1. Get relevant controls: _get_relevant_controls(resource_type)
2. Build analysis prompt with control context
3. Call Azure OpenAI with structured prompt
4. Parse and validate AI response
5. Apply control ID validation and mapping
6. Return structured findings
```

### 5. Control Selection & Validation Phase

#### Relevant Control Selection (_get_relevant_controls)
```python
# Multi-tier selection strategy:
1. Check resource_control_cache first (fastest)
2. Fallback to keyword-based matching
3. Apply domain priority ordering
4. Limit to prevent AI overwhelming (max 15 controls)
5. Log control IDs provided to AI for validation
```

#### Control ID Validation (_validate_and_map_control_ids)
```python
# Validation pipeline:
1. Extract control IDs from AI response
2. Check against valid control ID set
3. Apply intelligent mapping for invalid IDs:
   - Map fictional IDs to valid ones (NS-22 → NS-1)
   - Use domain prefix matching
   - Find closest valid number
4. Log validation results and corrections
```

### 6. Output Generation Phase

#### Finding Structure
```python
finding = {
    "file": file_path,
    "line": line_number,
    "severity": "HIGH|MEDIUM|LOW",
    "control_id": "validated_control_id",
    "control_domain": "domain_name",
    "issue": "description",
    "recommendation": "guidance",
    "resource_type": "detected_type"
}
```

#### Export Formats
```python
# Multiple output formats:
1. JSON - Structured data for programmatic use
2. Markdown - Human-readable reports
3. HTML - Interactive reports with Glass UI
4. CSV - Spreadsheet-compatible with tooltips
```

## Key Performance Optimizations

### 1. Caching Strategy
- **Control ID Index**: O(1) control lookup by ID
- **Resource Control Cache**: Pre-computed resource-to-control mappings
- **Benchmark Data Cache**: Avoid re-loading benchmark data

### 2. Prioritized Loading
- **Source Priority**: CSV → JSON → Excel → Fallback
- **Domain Priority**: Identity → Network → Data → Access → Monitoring
- **Early Exit**: Stop at first successful source load

### 3. Intelligent Filtering
- **Control Limiting**: Max 15 controls per resource type
- **Relevance Scoring**: Keyword-based control selection
- **Domain Focusing**: Primary domains per resource type

## Integration Points

### 1. External Data Sources
- **SecurityBenchmarks/**: Static data files directory
- **azure_resource_mappings.json**: Resource type definitions
- **AzureSecurityCenter.json**: Policy mappings
- **.env**: Configuration and priorities

### 2. AI Integration
- **Azure OpenAI**: GPT-4 for analysis
- **Structured Prompts**: Control-constrained analysis
- **Response Validation**: Control ID verification

### 3. Output Integration
- **Azure DevOps**: PR comment posting
- **File System**: Local report generation
- **MCP Server**: VS Code integration

## Critical Dependencies

### 1. Data Dependencies
- CSV files must follow exact column schema
- Control IDs must be consistent across sources
- Resource mappings must be comprehensive

### 2. Configuration Dependencies
- Environment variables for source priority
- Domain priority order configuration
- Azure OpenAI credentials and endpoints

### 3. Runtime Dependencies
- pandas for CSV processing
- Azure OpenAI SDK for AI analysis
- Azure DevOps SDK for PR integration

## Identified Bottlenecks

### 1. Data Loading
- Multiple file reads on each initialization
- Excel-to-JSON conversion overhead
- No persistent caching across runs

### 2. Control Selection
- Keyword matching inefficiency
- Repeated domain priority sorting
- No semantic similarity matching

### 3. AI Analysis
- Sequential file processing (no parallelization)
- Large prompt sizes with many controls
- No response caching for similar files

## Opportunities for Master Database

### 1. Unified Data Access
- Single database query instead of multiple file reads
- Consistent schema across all control sources
- Real-time data updates without file management

### 2. Intelligent Control Selection
- Semantic similarity matching for better relevance
- Machine learning-based control ranking
- Context-aware control filtering

### 3. Performance Improvements
- Persistent caching and indexing
- Parallel processing capabilities
- Incremental analysis for changed files only
