Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P1-Identity-HIGH,Identity Management,HIGH,IM-1,template.json,120,App Service 'onefuzz-daily-ui' does not explicitly integrate with Azure Active Directory for identity management. This weakens secure identity and access management.,Integrate the App Service with Azure Active Directory for authentication and access control.,N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-2,template.json,120,App Service 'onefuzz-daily-ui' does not enforce Multi-Factor Authentication (MFA) for users or administrators. This weakens secure access.,Enable and enforce Multi-Factor Authentication (MFA) for all users and administrators accessing the App Service.,N/A,AI,Generic
P1-Identity-MEDIUM,Identity Management,MEDIUM,IM-3,template.json,120,App Service 'onefuzz-daily-ui' does not specify the use of Conditional Access Policies. This is a recommended best practice for secure access.,Implement Conditional Access Policies in Azure AD to enforce secure access to the App Service.,N/A,AI,Generic
P1-Identity-MEDIUM,Identity Management,MEDIUM,IM-6,template.json,120,App Service 'onefuzz-daily-ui' does not specify the use of Role-Based Access Control (RBAC) for access management. This is a recommended best practice.,Assign access rights to the App Service using Azure RBAC to ensure least privilege.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,120,"App Service 'onefuzz-daily-ui' allows public network access ('publicNetworkAccess': 'Enabled') and has 'ipSecurityRestrictions' with 'ipAddress': 'Any' and 'action': 'Allow', exposing the app to the public internet. This violates the requirement to secure all public endpoints.",Restrict 'ipSecurityRestrictions' to only allow trusted IP ranges. Set 'publicNetworkAccess' to 'Disabled' or use private endpoints to minimize public exposure.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,128,"App Service 'onefuzz-daily-ui' allows public network access to the SCM endpoint ('scmIpSecurityRestrictions' with 'ipAddress': 'Any' and 'action': 'Allow'), exposing the SCM site to the public internet. This violates the requirement to secure all public endpoints.",Restrict 'scmIpSecurityRestrictions' to only allow trusted IP ranges. Set 'publicNetworkAccess' to 'Disabled' or use private endpoints for SCM access.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,120,"App Service 'onefuzz-daily-ui' does not use private endpoints ('publicNetworkAccess': 'Enabled'), increasing exposure to the public internet. This violates the recommendation to use private endpoints for secure access.",Configure a private endpoint for the App Service and set 'publicNetworkAccess' to 'Disabled' to restrict access to internal networks only.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,120,App Service 'onefuzz-daily-ui' does not specify the use of customer-managed keys (CMK) or explicit encryption at rest settings. This may violate the requirement to enable encryption at rest for all data storage.,Configure encryption at rest for all associated storage and enable customer-managed keys (CMK) if required for compliance.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,54,"App Service 'onefuzz-daily-ui' has 'sslState' set to 'Disabled' for hostName 'onefuzz-daily-ui.azurewebsites.net', which allows unencrypted HTTP traffic. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates entries to enforce HTTPS. Ensure all endpoints require TLS 1.2 or higher.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,61,"App Service 'onefuzz-daily-ui' has 'sslState' set to 'Disabled' for hostName 'onefuzz-daily-ui.scm.azurewebsites.net', which allows unencrypted HTTP traffic. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates entries to enforce HTTPS. Ensure all endpoints require TLS 1.2 or higher.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,120,"App Service 'onefuzz-daily-ui' configuration includes a 'publishingUsername' property, which may expose sensitive information if not securely managed. Sensitive data should be stored in Azure Key Vault.",Remove sensitive information from the template and reference secrets from Azure Key Vault using secure parameters or managed identities.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-5,template.json,120,App Service 'onefuzz-daily-ui' does not specify backup and recovery settings. This is a recommended best practice for critical data.,Configure regular backups for the App Service and ensure recovery procedures are in place.,N/A,AI,Generic
