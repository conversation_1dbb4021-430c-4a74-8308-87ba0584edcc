#!/usr/bin/env python3
"""
Debug script to specifically examine NS-1 control data and understand why URLs are not being extracted.
"""

import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from security_opt import SecurityPRReviewer
    print("✅ Successfully imported SecurityPRReviewer")
except ImportError as e:
    print(f"❌ Failed to import SecurityPRReviewer: {e}")
    sys.exit(1)

def debug_ns1_control():
    """Debug NS-1 control specifically."""
    print("\n🔍 DEBUGGING NS-1 CONTROL DATA")
    print("=" * 60)
    
    try:
        # Create reviewer instance
        reviewer = SecurityPRReviewer(local_folder=".")
        
        # Find NS-1 control in the benchmark data
        ns1_control = None
        
        if reviewer.benchmark_data and "controls" in reviewer.benchmark_data:
            for control in reviewer.benchmark_data["controls"]:
                if control.get("id") == "NS-1":
                    ns1_control = control
                    break
        
        if ns1_control:
            print("📋 Found NS-1 control in benchmark data:")
            for key, value in ns1_control.items():
                if isinstance(value, str) and len(value) > 100:
                    print(f"  {key}: {repr(value[:100])}...")
                else:
                    print(f"  {key}: {repr(value)}")
        else:
            print("❌ NS-1 control not found in benchmark data")
            return False
        
        # Test link extraction specifically for NS-1
        print(f"\n🔗 Testing link extraction for NS-1:")
        links_info = reviewer._extract_control_links("NS-1")
        
        print(f"  Raw links: {links_info.get('raw_links', [])}")
        print(f"  Azure guidance: {repr(links_info.get('azure_guidance', ''))}")
        print(f"  Implementation context: {repr(links_info.get('implementation_context', ''))}")
        print(f"  Formatted links: {repr(links_info.get('formatted_links', ''))}")
        
        # Check if the implementation field contains URLs
        implementation = ns1_control.get("implementation", "")
        print(f"\n📖 Implementation field content:")
        print(f"  Length: {len(implementation)} characters")
        print(f"  Content: {repr(implementation)}")
        
        # Check for URLs in implementation field
        import re
        url_pattern = r'https?://[^\s\n\r]+'
        urls_in_implementation = re.findall(url_pattern, implementation)
        print(f"  URLs found in implementation: {len(urls_in_implementation)}")
        for i, url in enumerate(urls_in_implementation, 1):
            print(f"    {i}. {url}")
        
        # Check other fields that might contain URLs
        for field_name in ["azure_guidance", "description", "name"]:
            field_value = ns1_control.get(field_name, "")
            if field_value:
                urls_in_field = re.findall(url_pattern, str(field_value))
                if urls_in_field:
                    print(f"  URLs found in {field_name}: {urls_in_field}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during NS-1 debugging: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run NS-1 specific debugging."""
    print("🐛 NS-1 CONTROL DEBUGGING")
    print("=" * 70)
    
    success = debug_ns1_control()
    
    if success:
        print("\n✅ NS-1 debugging completed successfully!")
    else:
        print("\n❌ NS-1 debugging failed!")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
