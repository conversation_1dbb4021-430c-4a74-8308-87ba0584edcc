# IaC Guardian MCP Server Setup Guide

This guide will help you set up the IaC Guardian MCP Server for VS Code Copilot integration, allowing you to use security analysis tools directly in your development environment.

## Prerequisites

- **Python 3.8+** installed
- **VS Code** with **GitHub Copilot** extension
- **Azure OpenAI** credentials configured (in `.env` file)
- **IaC Guardian GPT** repository cloned

## Quick Setup (Recommended)

### Step 1: Run Automated Setup

```bash
cd c:\Users\<USER>\REPOS\IaCGuardianGPT
python setup_mcp.py
```

This script will:
- ✅ Install all required dependencies
- ✅ Configure VS Code settings for MCP integration
- ✅ Create launch and test scripts
- ✅ Set up proper environment variables

### Step 2: Restart VS Code

**Important:** Restart VS Code completely to load the new MCP server configuration.

### Step 3: Test the Setup

```bash
python test_mcp.py
```

Expected output:
```
🚀 Starting IaC Guardian MCP Server Tests
🧪 Testing file analysis...
✅ File analysis test completed
🧪 Testing security controls retrieval...
✅ Security controls test completed
🎉 All tests completed successfully!
```

## Manual Setup (Alternative)

If the automated setup doesn't work, follow these manual steps:

### Step 1: Install Dependencies

```bash
pip install -r requirements-mcp.txt
```

### Step 2: Configure VS Code Settings

Create or update `.vscode/settings.json`:

```json
{
  "github.copilot.chat.experimental.mcp.enabled": true,
  "github.copilot.chat.experimental.mcp.servers": {
    "iac-guardian": {
      "command": "python",
      "args": ["mcp_server.py"],
      "cwd": "c:\\Users\\<USER>\\REPOS\\IaCGuardianGPT",
      "env": {
        "ENFORCE_DOMAIN_PRIORITY": "true",
        "USE_OPTIMIZED_PROMPTS": "true",
        "ANALYSIS_SEED": "42",
        "PYTHONPATH": "c:\\Users\\<USER>\\REPOS\\IaCGuardianGPT"
      }
    }
  }
}
```

### Step 3: Verify Environment

Ensure your `.env` file contains Azure OpenAI credentials:

```env
AZURE_OPENAI_ENDPOINT=https://your-openai-resource.openai.azure.com/
AZURE_OPENAI_API_KEY=your_api_key_here
AZURE_OPENAI_DEPLOYMENT=gpt-4
AZURE_OPENAI_API_VERSION=2024-02-01
```

## Usage in VS Code

### Opening Copilot Chat

1. Open VS Code
2. Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
3. Type "GitHub Copilot: Open Chat"
4. Or use the chat icon in the sidebar

### Available Tools

#### 1. Analyze Single File

```
@iac-guardian analyze_iac_file file_path="./templates/storage.json" format="markdown"
```

**Parameters:**
- `file_path`: Path to your IaC file
- `format`: `json`, `markdown`, or `summary`

#### 2. Analyze Entire Folder

```
@iac-guardian analyze_iac_folder folder_path="./infrastructure" format="summary" export_report=true
```

**Parameters:**
- `folder_path`: Path to folder with IaC files
- `format`: `json`, `markdown`, `summary`, or `html`
- `export_report`: Generate detailed HTML/CSV reports

#### 3. Get Security Controls

```
@iac-guardian get_security_controls resource_type="Storage" domain="Data Protection"
```

**Parameters:**
- `resource_type`: Azure resource type (Storage, Network, Compute, etc.)
- `domain`: Optional filter (Identity Management, Network Security, Data Protection, etc.)

#### 4. Validate Configuration

```
@iac-guardian validate_security_config resource_type="Microsoft.Storage/storageAccounts" resource_config='{"properties":{"supportsHttpsTrafficOnly":false}}'
```

**Parameters:**
- `resource_type`: Full Azure resource type
- `resource_config`: JSON configuration to validate
- `control_ids`: Optional array of specific controls to check

## Example Workflows

### Workflow 1: Quick Security Check

1. Open a file in VS Code
2. Open Copilot Chat
3. Run: `@iac-guardian analyze_iac_file file_path="./current-file.json"`
4. Review security findings with prioritized recommendations

### Workflow 2: Comprehensive Project Analysis

1. Open your IaC project in VS Code
2. Run: `@iac-guardian analyze_iac_folder folder_path="." export_report=true`
3. Get summary of all security issues
4. Export detailed HTML report for leadership

### Workflow 3: Resource-Specific Guidance

1. Working on storage configuration
2. Run: `@iac-guardian get_security_controls resource_type="Storage"`
3. Get relevant Azure Security Benchmark controls
4. Apply recommendations to your templates

### Workflow 4: Configuration Validation

1. Have a specific resource configuration
2. Run: `@iac-guardian validate_security_config` with your config
3. Get immediate feedback on security compliance
4. Fix issues before deployment

## Expected Output Examples

### Markdown Analysis Report

```markdown
# Security Analysis Report for storage.json

## 🛡️ Data Protection

### 🔴 CRITICAL Issues

#### DP-1
**File:** `storage.json` (Line 15)
**Issue:** HTTPS traffic not enforced for storage account
**Remediation:** Set supportsHttpsTrafficOnly to true

### 🟠 HIGH Issues

#### DP-2
**File:** `storage.json` (Line 18)
**Issue:** Minimum TLS version not configured
**Remediation:** Set minimumTlsVersion to TLS1_2
```

### Summary Report

```
# Security Analysis Summary for ./infrastructure
**Total Issues Found:** 5
**Severity Breakdown:**
- 🔴 CRITICAL: 1 issues
- 🟠 HIGH: 2 issues
- 🟡 MEDIUM: 2 issues
```

## Troubleshooting

### Issue: MCP Server Not Found

**Symptoms:** Tools not available in Copilot Chat

**Solutions:**
1. Restart VS Code completely
2. Check `.vscode/settings.json` configuration
3. Verify the `cwd` path is correct
4. Run `python test_mcp.py` to test server

### Issue: Import Errors

**Symptoms:** Python import errors when running server

**Solutions:**
1. Install dependencies: `pip install -r requirements-mcp.txt`
2. Check Python path in VS Code settings
3. Ensure you're in the correct directory

### Issue: Analysis Errors

**Symptoms:** Tools run but return errors

**Solutions:**
1. Check `.env` file has Azure OpenAI credentials
2. Verify file paths are accessible
3. Ensure files are valid JSON/Bicep format

### Issue: No Security Issues Found

**Symptoms:** Analysis returns no findings

**Solutions:**
1. Check if files contain actual Azure resources
2. Verify the benchmark data is loaded
3. Try with a known problematic template

## Advanced Configuration

### Custom Environment Variables

Add to your VS Code settings:

```json
"env": {
  "ENFORCE_DOMAIN_PRIORITY": "true",
  "USE_OPTIMIZED_PROMPTS": "true",
  "ANALYSIS_SEED": "42",
  "LOG_LEVEL": "DEBUG",
  "BENCHMARK_SOURCE_PRIORITY": "csv,json,excel,fallback"
}
```

### Custom Working Directory

Change the `cwd` in settings to analyze files in different locations:

```json
"cwd": "c:\\path\\to\\your\\iac\\project"
```

## Performance Tips

1. **Use Summary Format** for quick checks
2. **Export Reports** only when needed (they take longer)
3. **Analyze Specific Files** rather than large folders when possible
4. **Cache Results** by keeping VS Code open

## Security Considerations

- The MCP server runs locally and doesn't send data externally
- Azure OpenAI API calls are made only for analysis
- File contents are processed locally
- No data is stored permanently by the server

## Next Steps

1. ✅ Complete setup and testing
2. 🔍 Try analyzing your existing IaC files
3. 📊 Generate reports for your team
4. 🛡️ Integrate security checks into your development workflow
5. 📚 Learn about Azure Security Benchmark controls

## Support

If you encounter issues:

1. **Check the logs** in VS Code Developer Tools
2. **Run the test script** to verify functionality
3. **Review the troubleshooting section** above
4. **Ensure all prerequisites** are met

The MCP server provides powerful security analysis capabilities directly in your development environment, making it easy to catch and fix security issues early in the development process!
