Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P1-Identity-HIGH,Identity Management,HIGH,IM-1,template.json,143,App Service 'onefuzz-daily-ui' does not explicitly integrate with Azure Active Directory for identity management.,Integrate App Service authentication with Azure Active Directory to enforce secure identity management.,N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-2,template.json,143,App Service 'onefuzz-daily-ui' does not enforce Multi-Factor Authentication (MFA) for users or administrators.,Enable MFA for all users and administrators accessing the App Service.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,template.json,143,App Service 'onefuzz-daily-ui' is not protected by a network security group (NSG) or Azure Firewall. This exposes the resource to potential network-based attacks.,Protect the App Service with an NSG or Azure Firewall to restrict and monitor network traffic.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,143,"App Service 'onefuzz-daily-ui' allows public network access ('publicNetworkAccess': 'Enabled') and has 'ipSecurityRestrictions' with 'ipAddress': 'Any', 'action': 'Allow', which exposes the app to the public internet. This violates the requirement to secure all public endpoints.",Restrict 'ipSecurityRestrictions' to only allow trusted IP ranges and set 'publicNetworkAccess' to 'Disabled' if public access is not required.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,154,"App Service 'onefuzz-daily-ui' allows public network access to the SCM endpoint ('scmIpSecurityRestrictions' with 'ipAddress': 'Any', 'action': 'Allow'), exposing the SCM endpoint to the public internet. This violates the requirement to secure all public endpoints.",Restrict 'scmIpSecurityRestrictions' to only allow trusted IP ranges and set 'publicNetworkAccess' to 'Disabled' if public access is not required.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-3,template.json,143,App Service 'onefuzz-daily-ui' does not implement Network Security Groups (NSGs) to control inbound and outbound traffic.,Apply NSGs to the subnet hosting the App Service Environment or use access restrictions to control traffic.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,143,"App Service 'onefuzz-daily-ui' does not use private endpoints ('publicNetworkAccess': 'Enabled'), increasing exposure to the public internet. This violates the recommendation to use private endpoints for secure access.",Configure a private endpoint for the App Service and set 'publicNetworkAccess' to 'Disabled' to restrict access to private networks only.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,143,App Service 'onefuzz-daily-ui' does not specify encryption at rest settings. All data storage must be encrypted at rest.,"Ensure App Service uses encrypted storage for all data at rest. For App Service, this is typically enabled by default, but verify and document encryption status.",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,54,"App Service 'onefuzz-daily-ui' has 'sslState' set to 'Disabled' for hostName 'onefuzz-daily-ui.azurewebsites.net', which allows unencrypted HTTP traffic. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates entries to enforce HTTPS. Ensure HTTP is disabled for all endpoints.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,62,"App Service 'onefuzz-daily-ui' has 'sslState' set to 'Disabled' for hostName 'onefuzz-daily-ui.scm.azurewebsites.net', which allows unencrypted HTTP traffic. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates entries to enforce HTTPS. Ensure HTTP is disabled for all endpoints.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,143,"App Service 'onefuzz-daily-ui' configuration includes a 'publishingUsername' property, which may expose sensitive information if not securely managed. Sensitive data should be stored in Azure Key Vault.",Remove hardcoded sensitive information from the template and reference secrets from Azure Key Vault using Key Vault references.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,143,"App Service 'onefuzz-daily-ui' has 'phpVersion' set to '5.6', which is deprecated and may expose sensitive data due to unpatched vulnerabilities.",Upgrade 'phpVersion' to a supported and secure version to reduce risk of sensitive data exposure.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,143,"App Service 'onefuzz-daily-ui' has 'httpLoggingEnabled', 'requestTracingEnabled', and 'detailedErrorLoggingEnabled' all set to false, which may hinder detection of sensitive data exposure or security incidents.",Enable logging and tracing to monitor for sensitive data exposure and security incidents.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-5,template.json,143,App Service 'onefuzz-daily-ui' does not specify backup and recovery settings. Implementing backup and recovery is recommended for critical data.,Configure regular backups for the App Service and document recovery procedures.,N/A,AI,Generic
