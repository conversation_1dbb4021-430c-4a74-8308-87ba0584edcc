#!/usr/bin/env python3
"""
Generate HTML security report for the workspace.
Usage: python generate_html_report.py [folder_path]
"""

import sys
import os
import webbrowser
from pathlib import Path

# Add current directory to path
sys.path.append('.')

def generate_report(folder_path=None):
    """Generate HTML security report."""
    try:
        from security_opt import SecurityPRReviewer
        
        # Set environment variables for optimal analysis
        os.environ.setdefault('ENFORCE_DOMAIN_PRIORITY', 'true')
        os.environ.setdefault('USE_OPTIMIZED_PROMPTS', 'true')
        os.environ.setdefault('ANALYSIS_SEED', '42')
        
        # Use current directory if no path provided
        if folder_path is None:
            folder_path = '.'
        
        # Validate folder exists
        if not Path(folder_path).exists():
            print(f'❌ Folder not found: {folder_path}')
            return
        
        print(f'🔍 Analyzing folder: {Path(folder_path).absolute()}')
        print('⏳ This may take a few moments...')
        print('='*60)
        
        # Analyze the folder
        reviewer = SecurityPRReviewer(local_folder=folder_path)
        files = reviewer.analyze_folder(folder_path)
        
        if not files:
            print('❌ No IaC files found to analyze')
            print('💡 Supported files: .json, .bicep, .parameters.json')
            return
        
        print(f'📁 Found {len(files)} files to analyze:')
        for file_info in files:
            # Handle both dict format (with 'path' key) and string format
            if isinstance(file_info, dict):
                file_path = file_info.get('path', str(file_info))
            else:
                file_path = str(file_info)
            print(f'   • {Path(file_path).name}')
        print()
        
        # Perform analysis
        findings = reviewer.analyze_files(files)
        
        if not findings:
            print('✅ No security issues found!')
            print('🎉 Your Infrastructure-as-Code follows security best practices.')
            return
        
        # Export HTML report
        print(f'📊 Found {len(findings)} security issues')
        print('📄 Generating HTML report...')
        
        reviewer.export_findings(findings, 'html', 'security_findings')
        
        # Find the generated HTML file
        security_dir = Path('security_findings')
        if security_dir.exists():
            html_files = list(security_dir.glob('*.html'))
            if html_files:
                # Get the most recent HTML file
                latest_html = max(html_files, key=lambda x: x.stat().st_mtime)
                
                print(f'✅ HTML report generated: {latest_html}')
                print(f'📁 Report location: {latest_html.absolute()}')
                
                # Display summary
                print('\n📊 SECURITY ANALYSIS SUMMARY')
                print('='*40)
                
                # Count by severity
                severity_counts = {}
                for finding in findings:
                    severity = finding.get("severity", "UNKNOWN")
                    severity_counts[severity] = severity_counts.get(severity, 0) + 1
                
                for severity in ["CRITICAL", "HIGH", "MEDIUM", "LOW"]:
                    count = severity_counts.get(severity, 0)
                    if count > 0:
                        severity_emoji = {
                            "CRITICAL": "🔴",
                            "HIGH": "🟠", 
                            "MEDIUM": "🟡",
                            "LOW": "🔵"
                        }.get(severity, "⚪")
                        print(f'{severity_emoji} {severity}: {count} issues')
                
                # Count by domain
                domains = {}
                for f in findings:
                    domain = reviewer._get_finding_domain(f)
                    domains[domain] = domains.get(domain, 0) + 1
                
                if len(domains) > 1:
                    print(f'\n📈 Issues by Security Domain:')
                    for domain, count in domains.items():
                        print(f'   • {domain}: {count} issues')
                
                print(f'\n💡 Domain prioritization: Identity → Network → Data Protection')
                
                # Try to open in browser
                try:
                    file_url = f'file:///{latest_html.absolute().as_posix()}'
                    webbrowser.open(file_url)
                    print(f'🌐 Opening report in browser...')
                except Exception as e:
                    print(f'⚠️ Could not auto-open browser: {e}')
                    print(f'📂 Manually open: {latest_html.absolute()}')
                
            else:
                print('❌ No HTML files found in security_findings directory')
        else:
            print('❌ security_findings directory not created')
        
    except ImportError as e:
        print(f'❌ Import error: {e}')
        print('Please ensure you are in the IaC Guardian directory')
    except Exception as e:
        print(f'❌ Error generating report: {e}')
        import traceback
        traceback.print_exc()

def main():
    """Main function."""
    folder_path = sys.argv[1] if len(sys.argv) > 1 else None
    generate_report(folder_path)

if __name__ == "__main__":
    main()
