Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P6-Other-CRITICAL,Unknown,CRITICAL,CONTROL-13,demo_files\storage_demo.bicep,16,"Storage account 'allowBlobPublicAccess' is set to true, enabling public access to blobs. This exposes data to the public internet.",Set 'allowBlobPublicAccess' to false to disable public blob access and prevent unauthorized data exposure.,N/A,AI,Generic
P6-Other-CRITICAL,Unknown,CRITICAL,CONTROL-13,demo_files\storage_demo.bicep,37,"Storage account 'networkAcls.defaultAction' is set to 'Allow', permitting unrestricted network access. This allows public access to the storage account.",Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only trusted networks or services to access the storage account.,N/A,AI,Generic
P6-Other-CRITICAL,Unknown,CRITICAL,CONTROL-13,test_code_snippets\azure_storage.bicep,29,"Public blob access is enabled on the storage account (allowBlobPublicAccess: true), which exposes data to the public internet.",Set 'allowBlobPublicAccess' to false in the storage account properties to disable public blob access.,N/A,AI,Generic
P6-Other-CRITICAL,Unknown,CRITICAL,CONTROL-13,test_code_snippets\azure_storage.bicep,35,"The storage account allows HTTP traffic (supportsHttpsTrafficOnly: false), which exposes data in transit to interception.",Set 'supportsHttpsTrafficOnly' to true in the storage account properties to enforce HTTPS-only traffic.,N/A,AI,Generic
P6-Other-CRITICAL,Unknown,CRITICAL,CONTROL-13,test_code_snippets\azure_storage.bicep,59,"The storage account network ACLs allow default network access (defaultAction: 'Allow'), permitting traffic from all networks.",Set 'networkAcls.defaultAction' to 'Deny' and explicitly define allowed virtual networks or IP rules to restrict access.,N/A,AI,Generic
P6-Other-CRITICAL,Unknown,CRITICAL,CONTROL-14,demo_files\storage_demo.bicep,22,"Storage account 'supportsHttpsTrafficOnly' is set to false, allowing HTTP traffic. This permits unencrypted data transmission.",Set 'supportsHttpsTrafficOnly' to true to enforce HTTPS-only traffic and ensure data is encrypted in transit.,N/A,AI,Generic
P6-Other-CRITICAL,Unknown,CRITICAL,DP-2,test_code_snippets\network_security.tf,95,"Storage account 'securityteststorage' has 'allow_blob_public_access' set to true, allowing public access to blobs and exposing data.",Set 'allow_blob_public_access' to false to prevent public access to blob data.,N/A,AI,Generic
P6-Other-CRITICAL,Unknown,CRITICAL,DP-3,demo_files\network_demo.tf,43,"Storage account 'demostorageaccount' has 'https_traffic_only' set to false, allowing unencrypted HTTP connections and exposing data in transit.",Set 'https_traffic_only' to true to enforce encrypted connections to the storage account.,N/A,AI,Generic
P6-Other-CRITICAL,Unknown,CRITICAL,DP-3,test_code_snippets\network_security.tf,98,"Storage account 'securityteststorage' has 'https_traffic_only' set to false, allowing unencrypted HTTP traffic.",Set 'https_traffic_only' to true to enforce encrypted connections to the storage account.,N/A,AI,Generic
P6-Other-CRITICAL,Unknown,CRITICAL,DP-6,demo_files\network_demo.tf,40,"Storage account 'demostorageaccount' has 'allow_blob_public_access' set to true, enabling public access to blobs and exposing data to the internet.",Set 'allow_blob_public_access' to false to prevent public access to blob data in the storage account.,N/A,AI,Generic
P6-Other-CRITICAL,Unknown,CRITICAL,NS-2,demo_files\network_demo.tf,16,"Network Security Group 'nsg-demo-security' allows inbound SSH (port 22) from any source (0.0.0.0/0), exposing the resource to the public internet.",Restrict the source_address_prefix for the SSH rule to specific trusted IP ranges instead of 0.0.0.0/0 to limit public exposure.,N/A,AI,Generic
P6-Other-CRITICAL,Unknown,CRITICAL,NS-2,test_code_snippets\network_security.tf,52,"Network Security Group 'nsg-security-test' allows inbound SSH (port 22) from any source (0.0.0.0/0), exposing the resource to the public internet.","Restrict the source_address_prefix for the SSH rule to specific trusted IP ranges instead of 0.0.0.0/0, or remove the rule if not required.",N/A,AI,Generic
P6-Other-CRITICAL,Unknown,CRITICAL,NS-2,test_code_snippets\network_security.tf,63,"Network Security Group 'nsg-security-test' allows inbound RDP (port 3389) from any source (0.0.0.0/0), exposing the resource to the public internet.","Restrict the source_address_prefix for the RDP rule to specific trusted IP ranges instead of 0.0.0.0/0, or remove the rule if not required.",N/A,AI,Generic
P6-Other-CRITICAL,Unknown,CRITICAL,NS-3,demo_files\network_demo.tf,16,"Network Security Group 'nsg-demo-security' has an inbound rule permitting SSH (port 22) from any source (0.0.0.0/0), which allows unrestricted public access.","Modify the security rule to only allow SSH access from specific, trusted IP addresses or ranges.",N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,AM-1,missing_controls.json,7,"AM-1 control is missing for resource types AppService, Container, LogicApps as indicated in the 'missing_controls' array.","Implement asset management controls for AppService, Container, and LogicApps to ensure all assets are inventoried and managed according to Azure Security Benchmark AM-1.",N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,AM-2,missing_controls.json,19,"AM-2 control is missing for resource types AppService, Container, LogicApps as indicated in the 'missing_controls' array.","Implement application allowlisting and inventory controls for AppService, Container, and LogicApps to comply with Azure Security Benchmark AM-2.",N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,AM-3,missing_controls.json,31,"AM-3 control is missing for resource types AppService, Container, LogicApps as indicated in the 'missing_controls' array.","Implement software inventory and management controls for AppService, Container, and LogicApps to comply with Azure Security Benchmark AM-3.",N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,CONTROL-13,test_code_snippets\azure_storage.bicep,32,"The storage account is configured to allow TLS1_0 (minimumTlsVersion: 'TLS1_0'), which is a deprecated and insecure protocol.",Set 'minimumTlsVersion' to 'TLS1_2' or higher in the storage account properties to enforce strong encryption protocols.,N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,CONTROL-14,demo_files\storage_demo.bicep,19,"Storage account 'minimumTlsVersion' is set to 'TLS1_0', which is a deprecated and insecure protocol.",Set 'minimumTlsVersion' to 'TLS1_2' or higher to enforce strong encryption protocols for data in transit.,N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,DP-1,missing_controls.json,91,"DP-1 control is missing for resource types AppService, Container, LogicApps as indicated in the 'missing_controls' array.","Implement sensitive data inventory and classification for AppService, Container, and LogicApps to comply with Azure Security Benchmark DP-1.",N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,DP-2,test_analysis_output.json,10,"The 'min_tls_version' in 'test_storage.tf' is set to 'TLS1_0', which is an outdated and insecure protocol version vulnerable to multiple attacks.",Set 'min_tls_version' to 'TLS1_2' or higher to ensure secure communication.,N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,DP-3,test_analysis_output.json,9,"The 'enable_https_traffic_only' property in 'test_storage.tf' is set to false, allowing unencrypted HTTP connections to the storage account. This exposes data in transit to interception and tampering.",Set 'enable_https_traffic_only' to true to enforce encrypted connections to the storage account.,N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,DP-3,test_analysis_output.json,9,"Soft delete is not enabled for the Key Vault in 'test_keyvault.bicep'. Without soft delete, deleted secrets, keys, and certificates cannot be recovered, increasing the risk of accidental or malicious data loss.",Set 'enableSoftDelete' to true to ensure deleted items can be recovered.,N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,DP-3,test_analysis_output.json,10,"Purge protection is not enabled for the Key Vault in 'test_keyvault.bicep'. Without purge protection, a malicious actor could permanently delete (purge) secrets, keys, or certificates, even if soft delete is enabled.",Set 'enablePurgeProtection' to true to prevent permanent deletion of Key Vault objects.,N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,DP-3,test_code_snippets\network_security.tf,101,"Storage account 'securityteststorage' has 'min_tls_version' set to 'TLS1_0', which is a deprecated and insecure protocol.",Set 'min_tls_version' to 'TLS1_2' or higher to enforce strong encryption protocols.,N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,DP-6,missing_controls.json,127,"DP-6 control is missing for resource types AppService, Container, LogicApps as indicated in the 'missing_controls' array.","Implement cryptographic key management and expiration controls for AppService, Container, and LogicApps to comply with Azure Security Benchmark DP-6.",N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,IM-1,missing_controls.json,211,"IM-1 control is missing for resource types AppService, Container, LogicApps as indicated in the 'missing_controls' array.","Implement centralized authentication and account management for AppService, Container, and LogicApps to comply with Azure Security Benchmark IM-1.",N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,IM-1,security_findings.json,74,"Authentication is not enforced for all users. The 'requireAuthentication' property is set to false, which means unauthenticated access is allowed to the App Service. This violates ASB control IM-1.",Set 'requireAuthentication' to true in the 'globalValidation' section to enforce authentication for all users accessing the App Service.,N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,IM-2,missing_controls.json,235,"IM-2 control is missing for resource types AppService, Container, LogicApps as indicated in the 'missing_controls' array.","Implement dedicated administrative accounts and require MFA for AppService, Container, and LogicApps to comply with Azure Security Benchmark IM-2.",N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,IM-3,missing_controls.json,259,"IM-3 control is missing for resource types AppService, Container, LogicApps as indicated in the 'missing_controls' array.","Implement managed identities and service principals for AppService, Container, and LogicApps to comply with Azure Security Benchmark IM-3.",N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,IM-6,missing_controls.json,307,"IM-6 control is missing for resource types AppService, Container, LogicApps as indicated in the 'missing_controls' array.","Require multi-factor authentication for all administrative and remote access to AppService, Container, and LogicApps to comply with Azure Security Benchmark IM-6.",N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,IM-8,missing_controls.json,355,"IM-8 control is missing for resource types AppService, Container, LogicApps as indicated in the 'missing_controls' array.","Ensure secure coding practices and developer training for AppService, Container, and LogicApps to comply with Azure Security Benchmark IM-8.",N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,NS-2,AzureSecurityCenter.json,564,"The 'disableUnrestrictedNetworkToStorageAccountMonitoringEffect' parameter is set to 'Disabled' (line 564), which means unrestricted network access to storage accounts is not audited or denied. This weakens network security posture and may allow public access to sensitive data.",Set 'disableUnrestrictedNetworkToStorageAccountMonitoringEffect' to 'Audit' or 'Deny' to monitor and restrict unrestricted network access to storage accounts.,N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,NS-2,missing_controls.json,0,"NS-2 control is not present in the template for resource types AppService, Container, LogicApps. No explicit configuration found.","Implement network security controls as required by Azure Security Benchmark NS-2 for AppService, Container, and LogicApps.",N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,NS-2,test_analysis_output.json,12,"The 'network_rules' block in 'test_storage.tf' has 'default_action' set to 'Allow', which permits all network traffic (including from the public internet) to access the storage account. This increases the risk of unauthorized access.",Set 'default_action' to 'Deny' and explicitly allow only trusted networks or services to access the storage account.,N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,NS-2,test_analysis_output.json,13,"The 'networkAcls.defaultAction' property in 'test_keyvault.bicep' is set to 'Allow', which permits public network access to the Key Vault. This exposes the Key Vault to potential unauthorized access from the internet.",Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only trusted networks or private endpoints.,N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,NS-3,missing_controls.json,451,"NS-3 control is missing for resource types AppService, Container, LogicApps as indicated in the 'missing_controls' array.","Implement firewall and network segmentation controls for AppService, Container, and LogicApps to comply with Azure Security Benchmark NS-3.",N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,NS-5,missing_controls.json,499,"NS-5 control is missing for resource types AppService, Container, LogicApps as indicated in the 'missing_controls' array.","Enable DDoS protection and application layer filtering for AppService, Container, and LogicApps to comply with Azure Security Benchmark NS-5.",N/A,AI,Generic
P6-Other-MEDIUM,Unknown,MEDIUM,CONTROL-06,test_code_snippets\azure_storage.bicep,77,"Blob service delete retention policy is disabled (deleteRetentionPolicy.enabled: false), which may result in permanent data loss if blobs are deleted accidentally.",Set 'deleteRetentionPolicy.enabled' to true and specify an appropriate retention period in the blob service properties.,N/A,AI,Generic
P6-Other-MEDIUM,Unknown,MEDIUM,CONTROL-06,test_code_snippets\azure_storage.bicep,79,"Blob service versioning is disabled (isVersioningEnabled: false), reducing protection against accidental or malicious data modification or deletion.",Set 'isVersioningEnabled' to true in the blob service properties to enable blob versioning.,N/A,AI,Generic
P6-Other-MEDIUM,Unknown,MEDIUM,DP-1,security_findings.json,38,"The App Service does not have 'clientCertEnabled' set, which means client certificate authentication is not enforced. This weakens transport security for sensitive applications and violates ASB control DP-1.",Add 'clientCertEnabled': true to the App Service properties if client certificate authentication is required for your scenario.,N/A,AI,Generic
P6-Other-MEDIUM,Unknown,MEDIUM,DP-6,AzureSecurityCenter.json,462,"Key Vault secrets and keys expiration policies are set to 'Disabled' (lines 462 and 471), which means expiration dates are not enforced. This reduces the effectiveness of secrets management and increases risk of using stale or compromised secrets.",Set 'secretsExpirationSetEffect' and 'keysExpirationSetEffect' parameters to 'Audit' or 'Deny' to enforce expiration dates for Key Vault secrets and keys.,N/A,AI,Generic
P6-Other-MEDIUM,Unknown,MEDIUM,NS-2,security_findings.json,38,"The App Service is not configured with access restrictions (IP restrictions or service endpoints), which means it is potentially accessible from any network, including the public internet. This violates ASB control NS-2.",Configure 'ipSecurityRestrictions' in the App Service configuration to restrict access to only trusted IP addresses or networks.,N/A,AI,Generic
P6-Other-LOW,Unknown,LOW,CONTROL-06,test_code_snippets\azure_storage.bicep,81,"Blob service change feed is disabled (changeFeed.enabled: false), which limits the ability to track changes to blob data.",Set 'changeFeed.enabled' to true in the blob service properties to enable change feed for tracking data changes.,N/A,AI,Generic
P6-Other-LOW,Unknown,LOW,CONTROL-06,test_code_snippets\azure_storage.bicep,83,"Blob service restore policy is disabled (restorePolicy.enabled: false), which limits the ability to recover from accidental deletions or modifications.",Set 'restorePolicy.enabled' to true and configure the restore policy in the blob service properties.,N/A,AI,Generic
P6-Other-LOW,Unknown,LOW,CONTROL-06,test_code_snippets\azure_storage.bicep,85,"Blob service container delete retention policy is disabled (containerDeleteRetentionPolicy.enabled: false), which may result in permanent loss of deleted containers.",Set 'containerDeleteRetentionPolicy.enabled' to true and specify a retention period in the blob service properties.,N/A,AI,Generic
P6-Other-LOW,Unknown,LOW,CONTROL-06,test_code_snippets\azure_storage.bicep,94,"File service share delete retention policy is disabled (shareDeleteRetentionPolicy.enabled: false), which may result in permanent loss of deleted file shares.",Set 'shareDeleteRetentionPolicy.enabled' to true and specify a retention period in the file service properties.,N/A,AI,Generic
P6-Other-LOW,Unknown,LOW,DP-3,security_findings.json,38,"There is no evidence of HTTPS minimum TLS version enforcement. The App Service should enforce a minimum TLS version (e.g., 1.2) to ensure secure transport. This violates ASB control DP-3.","Set 'minTlsVersion' property (e.g., '1.2') in the App Service configuration to enforce a secure TLS version.",N/A,AI,Generic
