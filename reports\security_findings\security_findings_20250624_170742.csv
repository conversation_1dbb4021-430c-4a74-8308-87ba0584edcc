File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
Grafana.deploymentTemplate.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,38.0,"The 'publicNetworkAccess' property is set to 'Enabled' for the Microsoft.Dashboard/grafana resource. This exposes the Grafana instance to the public internet, creating an initial access vector for attackers. If compromised, attackers could gain access to monitoring data, credentials, or use the service as a pivot point for lateral movement within the environment. The blast radius includes potential data exposure and compromise of connected Azure resources.","Set 'publicNetworkAccess' to 'Disabled' to restrict access to private endpoints only. Implement Azure Private Link and ensure only trusted networks can access the Grafana instance. Example: ""publicNetworkAccess"": ""Disabled"". Reference: Azure Security Benchmark NS-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
Grafana.deploymentTemplate.json,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,38.0,"The Grafana instance is configured with 'publicNetworkAccess' set to 'Enabled', which allows unrestricted inbound and outbound network traffic from the public internet. This violates network segmentation and deny-by-default principles, increasing the risk of network compromise and unauthorized access.","Restrict network access by disabling public network access ('publicNetworkAccess': 'Disabled') and associating the resource with a virtual network and appropriate Network Security Groups (NSGs) to enforce least privilege network segmentation. Reference: Azure Security Benchmark NS-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
Grafana.deploymentTemplate.json,NS-3,Network Security,Deploy firewall at the edge of enterprise network,CRITICAL,38.0,"No Azure Firewall or equivalent network filtering is defined for the Grafana instance with 'publicNetworkAccess' enabled. This allows direct access from the internet without advanced filtering, increasing the risk of exploitation via known vulnerabilities or brute-force attacks.","Deploy Azure Firewall or a similar solution to filter and inspect traffic to the Grafana instance. Route all inbound and outbound traffic through the firewall and configure rules to allow only necessary traffic. Reference: Azure Security Benchmark NS-3.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal) | [Virtual network traffic routing](https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview) | [Azure Firewall Manager](https://docs.microsoft.com/azure/firewall-manager/overview) | [Hub-spoke topology](https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Use Azure Firewall for stateful application layer traffic restriction and central management. Create user-defined routes (UDR) for complex topologies. Block known bad IPs and high-risk protocols.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal) | [Virtual network traffic routing](https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview) | [Azure Firewall Manager](https://docs.microsoft.com/azure/firewall-manager/overview) | [Hub-spoke topology](https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Use Azure Firewall for stateful application layer traffic restriction and central management. Create user-defined routes (UDR) for complex topologies. Block known bad IPs and high-risk protocols.,"Enhanced Implementation Context:
• Azure Firewall deployment: https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal
• Virtual network traffic routing: https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview
• Azure Firewall Manager: https://docs.microsoft.com/azure/firewall-manager/overview
• Hub-spoke topology: https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 4.4, 4.8, 13.10
• NIST SP800-53 r4: AC-4, SC-7, CM-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Management ports should be closed on your virtual machines
• Management ports of virtual machines should be protected with just-in-time network access control
• IP Forwarding on your virtual machine should be disabled
• All Internet traffic should be routed via your deployed Azure Firewall",ai_analysis,,Validated
Grafana.deploymentTemplate.json,NS-5,Network Security,Deploy DDoS protection,HIGH,38.0,"With 'publicNetworkAccess' enabled, the Grafana instance is exposed to potential Distributed Denial of Service (DDoS) attacks. No DDoS protection is specified, increasing the risk of service disruption and potential exploitation during an attack.","Enable Azure DDoS Protection Standard on the virtual network hosting the Grafana instance. Monitor DDoS metrics and configure alerts for attack detection. Reference: Azure Security Benchmark NS-5.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/manage-ddos-protection) | [DDoS response strategy](https://docs.microsoft.com/azure/ddos-protection/ddos-response-strategy) | [DDoS monitoring and alerting](https://docs.microsoft.com/azure/ddos-protection/telemetry-monitoring-alerting) | [DDoS best practices](https://docs.microsoft.com/azure/security/fundamentals/ddos-best-practices) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Enable DDoS Standard protection plan on your VNet to protect resources exposed to public networks. Configure DDoS policies and monitoring.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/manage-ddos-protection) | [DDoS response strategy](https://docs.microsoft.com/azure/ddos-protection/ddos-response-strategy) | [DDoS monitoring and alerting](https://docs.microsoft.com/azure/ddos-protection/telemetry-monitoring-alerting) | [DDoS best practices](https://docs.microsoft.com/azure/security/fundamentals/ddos-best-practices) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Enable DDoS Standard protection plan on your VNet to protect resources exposed to public networks. Configure DDoS policies and monitoring.,"Enhanced Implementation Context:
• Azure DDoS Protection Standard: https://docs.microsoft.com/azure/virtual-network/manage-ddos-protection
• DDoS response strategy: https://docs.microsoft.com/azure/ddos-protection/ddos-response-strategy
• DDoS monitoring and alerting: https://docs.microsoft.com/azure/ddos-protection/telemetry-monitoring-alerting
• DDoS best practices: https://docs.microsoft.com/azure/security/fundamentals/ddos-best-practices
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 13.10
• NIST SP800-53 r4: SC-5, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3, 6.6

Azure Policy Examples:
• Azure DDoS Protection Standard should be enabled
• Monitor DDoS attack metrics and configure alerts
• Implement DDoS response procedures",ai_analysis,,Validated
Grafana.deploymentTemplate.json,NS-8,Network Security,Detect and disable insecure services and protocols,HIGH,38.0,"The template does not specify enforcement of secure protocols (e.g., TLS 1.2+) for the public endpoint. With 'publicNetworkAccess' enabled, this omission could allow insecure protocol usage, exposing the service to downgrade and man-in-the-middle attacks.","Explicitly enforce the latest TLS version (1.2 or higher) for all public endpoints. Disable legacy and insecure protocols. Reference: Azure Security Benchmark NS-8.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Secure protocol configuration](https://docs.microsoft.com/azure/security/fundamentals/network-best-practices) | [Network security monitoring](https://docs.microsoft.com/azure/security/fundamentals/network-monitoring) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Use Azure Sentinel's Insecure Protocol Workbook to discover insecure services like SSL/TLSv1 SSHv1 SMBv1 LM/NTLMv1. Disable insecure protocols or use compensating controls like NSG/Firewall blocking.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Secure protocol configuration](https://docs.microsoft.com/azure/security/fundamentals/network-best-practices) | [Network security monitoring](https://docs.microsoft.com/azure/security/fundamentals/network-monitoring) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Use Azure Sentinel's Insecure Protocol Workbook to discover insecure services like SSL/TLSv1 SSHv1 SMBv1 LM/NTLMv1. Disable insecure protocols or use compensating controls like NSG/Firewall blocking.,"Enhanced Implementation Context:
• Azure Sentinel insecure protocols workbook: https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks
• TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem
• Secure protocol configuration: https://docs.microsoft.com/azure/security/fundamentals/network-best-practices
• Network security monitoring: https://docs.microsoft.com/azure/security/fundamentals/network-monitoring
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 4.4, 4.8
• NIST SP800-53 r4: CM-2, CM-6, CM-7
• PCI-DSS v3.2.1: 4.1, A2.1, A2.2, A2.3

Azure Policy Examples:
• Latest TLS version should be used in your API App
• Latest TLS version should be used in your Web App
• Latest TLS version should be used in your Function App
• Secure transfer to storage accounts should be enabled",ai_analysis,,Validated
KustoScripts.template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,55.0,"The property 'continueOnErrors' is set to true for the Kusto script deployment. This setting allows the deployment to proceed even if errors occur during script execution, which can mask unauthorized data access or exfiltration attempts. Attackers could exploit this by injecting or triggering errors to bypass detection, leading to potential data loss or exposure. The blast radius includes undetected data exfiltration or corruption in the Kusto database.","Set 'continueOnErrors' to false to ensure that any error during script execution halts the deployment and triggers alerts. Implement monitoring and alerting for failed script executions to detect and respond to anomalous activities. Reference: Azure Security Benchmark DP-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
KustoScripts.template.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,105.0,"The property 'continueOnErrors' is set to true for the Kusto script resource. This setting allows the deployment to proceed even if the script fails, which can result in incomplete or inconsistent data processing. Attackers could exploit this by causing intentional script failures to bypass security controls or data validation steps, increasing the risk of data corruption or unauthorized data exposure. The blast radius includes potential data integrity loss and undetected misconfigurations in production environments.","Set 'continueOnErrors' to false for all critical Kusto script deployments to ensure that any failure halts the deployment process. This enforces strict error handling and prevents partial or insecure configurations from being applied. Review all script resources and update the configuration as follows: ""continueOnErrors"": false. Reference: Azure Security Benchmark DP-3.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account)

🔵 Azure Guidance: Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windo...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account),Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windows remote management.,"Enhanced Implementation Context:
• Double encryption for Azure data in transit: https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit
• Encryption in transit overview: https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit
• TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem
• Azure Storage secure transfer: https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account

Compliance Mappings:
• CIS Controls v8: 3.10
• NIST SP800-53 r4: SC-8
• PCI-DSS v3.2.1: 3.5, 3.6, 4.1

Azure Policy Examples:
• Kubernetes clusters should be accessible only over HTTPS
• Only secure connections to your Azure Cache for Redis should be enabled
• FTPS only should be required in your Function App
• Secure transfer to storage accounts should be enabled
• Function App should only be accessible over HTTPS
• Latest TLS version should be used in your API App
• Web Application should only be accessible over HTTPS
• Enforce SSL connection should be enabled for PostgreSQL database servers
• Latest TLS version should be used in your Web App",ai_analysis,,Validated
KustoScripts.template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,176.0,"The property 'continueOnErrors' is set to true for the Microsoft.Kusto/clusters/databases/scripts resource. This configuration allows the deployment to proceed even if script execution fails, which can result in incomplete or inconsistent data processing. Attackers could exploit this by introducing errors or tampering with scripts, causing data exfiltration or bypassing critical data validation steps. The blast radius includes potential unauthorized data access, data integrity compromise, and undetected data exfiltration.","Set 'continueOnErrors' to false to ensure that any script execution failure halts the deployment process. Implement monitoring and alerting for script execution failures. Regularly review and validate all scripts for integrity and security. Reference: Azure Security Benchmark DP-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
KustoScripts.template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,225.0,"The use of 'mv-expand' on '_input_monikers' at line 232 lacks monitoring for anomalous or unauthorized data access. Without monitoring, attackers could exploit this function to exfiltrate large volumes of data or perform unauthorized queries, bypassing detection and increasing the risk of data breaches.","Enable Azure Defender for SQL/Data Explorer and configure monitoring for anomalous data access patterns on the Kusto cluster. Set up alerts for unusual query volumes or access to sensitive data. Reference: ASB DP-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
KustoScripts.template.json,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,225.0,"The script at line 232 uses 'mv-expand' on a dynamic input '_input_monikers' without any data classification or validation. If sensitive or unclassified data is passed in '_input_monikers', it could be exposed or processed without proper controls, enabling data exfiltration or unauthorized access. Attackers could exploit this to enumerate or extract sensitive data, increasing the blast radius of a compromise.","Implement Azure Purview or Azure Information Protection to classify and label all data processed by '_input_monikers'. Ensure that only non-sensitive, classified data is accepted as input. Add validation logic to reject or mask sensitive data before expansion. Reference: ASB DP-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources)

🔵 Azure Guidance: Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other loca...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources),Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other locations.,"Enhanced Implementation Context:
• Data classification overview: https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification
• Azure Purview sensitivity labels: https://docs.microsoft.com/azure/purview/create-sensitivity-label
• Azure Information Protection: https://docs.microsoft.com/azure/information-protection/what-is-information-protection
• Azure SQL Data Discovery: https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification
• Azure Purview data sources: https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources

Compliance Mappings:
• CIS Controls v8: 3.2, 3.7, 3.13
• NIST SP800-53 r4: RA-2, SC-28
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Sensitive data in your SQL databases should be classified
• Implement data discovery and classification across all data stores
• Deploy Azure Purview for enterprise data governance
• Configure sensitivity labels for all data assets",ai_analysis,,Validated
KustoScripts.template.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,237.0,"The function call 'fun_LookupByUniqueAttribute_Region' at line 237 does not specify any authentication or access control mechanism. If this function is exposed without Azure AD authentication, attackers could gain unauthorized access to region metadata, enabling lateral movement or privilege escalation within the environment.","Enforce Azure AD authentication for all Kusto functions, including 'fun_LookupByUniqueAttribute_Region'. Restrict function access to authorized identities only. Reference: ASB IM-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applicati...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),"Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applications to Azure AD when technically feasible.","Enhanced Implementation Context:
• Azure AD tenancy concepts: https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps
• Create and configure Azure AD: https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant
• Define Azure AD tenants: https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/
• External identity providers: https://docs.microsoft.com/azure/active-directory/b2b/identity-providers
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 6.7, 12.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8
• PCI-DSS v3.2.1: 7.2, 8.3

Azure Policy Examples:
• An Azure Active Directory administrator should be provisioned for SQL servers
• Service Fabric clusters should only use Azure Active Directory for client authentication
• Standardize identity provider across all applications and services",ai_analysis,,Validated
KustoScripts.template.json,IM-3,Identity Management,Manage application identities securely and automatically,HIGH,237.0,"The script at line 237 does not indicate the use of managed identities or service principals for function execution. Absence of managed identities increases the risk of credential exposure and unauthorized access, as attackers could exploit hardcoded or weak credentials to execute privileged operations.","Configure the Logic App and Kusto functions to use Azure Managed Identities for all service-to-service authentication. Eliminate any use of hardcoded credentials or shared accounts. Reference: ASB IM-3.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview) | [Services supporting managed identities](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities) | [Azure service principal creation](https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps) | [Service principal with certificates](https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Use Azure managed identities for services supporting Azure AD authentication. For services without managed identity support use Azure AD service principals with certificate credentials and restricted ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview) | [Services supporting managed identities](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities) | [Azure service principal creation](https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps) | [Service principal with certificates](https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),Use Azure managed identities for services supporting Azure AD authentication. For services without managed identity support use Azure AD service principals with certificate credentials and restricted permissions.,"Enhanced Implementation Context:
• Azure managed identities overview: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview
• Services supporting managed identities: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities
• Azure service principal creation: https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps
• Service principal with certificates: https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: Not specified
• NIST SP800-53 r4: AC-2, AC-3, IA-4, IA-5, IA-9
• PCI-DSS v3.2.1: Not applicable

Azure Policy Examples:
• Managed identity should be used in your Function App
• Managed identity should be used in your Web App
• Service principals should be used to protect your subscriptions instead of management certificates
• Managed identity should be used in your API App
• Virtual machines' Guest Configuration extension should be deployed with system-assigned managed identity",ai_analysis,,Validated
KustoScripts.template.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,291.0,"The property 'continueOnErrors' is set to true, which may allow the deployment to proceed even if critical errors occur in script execution. This can result in incomplete or inconsistent data processing, potentially exposing sensitive data in transit to attackers if validation or security steps are skipped. Attackers could exploit this to inject malicious data or bypass security controls, increasing the risk of data exposure or manipulation.","Set 'continueOnErrors' to false to ensure that any error during script execution halts the deployment process. This enforces strict validation and prevents the risk of data-in-transit exposure due to incomplete or insecure operations. Review all deployment scripts to ensure secure error handling and data validation in accordance with Azure Security Benchmark DP-3.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account)

🔵 Azure Guidance: Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windo...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account),Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windows remote management.,"Enhanced Implementation Context:
• Double encryption for Azure data in transit: https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit
• Encryption in transit overview: https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit
• TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem
• Azure Storage secure transfer: https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account

Compliance Mappings:
• CIS Controls v8: 3.10
• NIST SP800-53 r4: SC-8
• PCI-DSS v3.2.1: 3.5, 3.6, 4.1

Azure Policy Examples:
• Kubernetes clusters should be accessible only over HTTPS
• Only secure connections to your Azure Cache for Redis should be enabled
• FTPS only should be required in your Function App
• Secure transfer to storage accounts should be enabled
• Function App should only be accessible over HTTPS
• Latest TLS version should be used in your API App
• Web Application should only be accessible over HTTPS
• Enforce SSL connection should be enabled for PostgreSQL database servers
• Latest TLS version should be used in your Web App",ai_analysis,,Validated
KustoScripts.template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,319.0,"The property 'continueOnErrors' is set to true, which allows the Logic App to proceed even if errors occur during script execution. This can enable attackers to suppress error signals during data operations, potentially masking unauthorized data access or exfiltration attempts. The blast radius includes undetected data exfiltration or manipulation, as monitoring and alerting systems may not be triggered by failed operations.","Set 'continueOnErrors' to false to ensure that any error in the script execution halts the process and triggers appropriate monitoring and alerting. Implement robust error handling and ensure that all failed operations are logged and reviewed. Reference: Azure Security Benchmark DP-2 (Monitor and alert on anomalous data activities).

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
roleAssignment.deploymentTemplate.json,IM-2,Identity Management,Protect identity and authentication systems,HIGH,11.0,"The 'builtInRoleType' parameter has a default value of 'Owner', which grants full administrative privileges to the assigned principal. Assigning the Owner role by default enables an attacker who compromises the principalId to gain unrestricted access to all resources within the subscription, facilitating privilege escalation, lateral movement, and potential full environment compromise. The blast radius includes all resources under the subscription, making this a critical access control weakness.","Change the 'builtInRoleType' default value to the minimum required privilege (e.g., 'Reader' or a custom least-privilege role). Require explicit assignment of high-privilege roles and implement approval workflows for Owner or Contributor assignments. Reference: Azure Security Benchmark v3.0, Control IM-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score) | [Active Directory security best practices](https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory) | [Azure AD security baseline](https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline) | [Privileged Identity Management](https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Use Azure AD security baseline and Identity Secure Score to evaluate security posture. Implement limited administrative roles user risk policies MFA for administrative roles and block legacy authentic...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score) | [Active Directory security best practices](https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory) | [Azure AD security baseline](https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline) | [Privileged Identity Management](https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),Use Azure AD security baseline and Identity Secure Score to evaluate security posture. Implement limited administrative roles user risk policies MFA for administrative roles and block legacy authentication.,"Enhanced Implementation Context:
• Azure AD Identity Secure Score: https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score
• Active Directory security best practices: https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory
• Azure AD security baseline: https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline
• Privileged Identity Management: https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 5.4, 6.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8, SI-4
• PCI-DSS v3.2.1: 8.2, 8.3

Azure Policy Examples:
• No applicable built-in policy (requires configuration-based implementation)
• Use Azure AD Identity Secure Score recommendations
• Implement Azure AD security baseline configurations
• Monitor privileged account activities through Azure AD logs",ai_analysis,,Validated
roleAssignment.deploymentTemplate.json,IM-2,Identity Management,Protect identity and authentication systems,HIGH,13.0,"The 'roleDefinitionId' property is set to the GUID for the 'Owner' role, which grants full administrative rights over the subscription. Assigning this role to a principal (especially via automation or default configuration) creates a significant attack vector: if the principalId is compromised, an attacker can take full control of all Azure resources, escalate privileges, and disable security controls. The blast radius is the entire subscription.","Restrict assignment of the 'Owner' role to only those principals that require it for operational necessity. Use least-privilege roles wherever possible and implement Privileged Identity Management (PIM) to require just-in-time elevation for Owner access. Reference: Azure Security Benchmark v3.0, Control IM-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score) | [Active Directory security best practices](https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory) | [Azure AD security baseline](https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline) | [Privileged Identity Management](https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Use Azure AD security baseline and Identity Secure Score to evaluate security posture. Implement limited administrative roles user risk policies MFA for administrative roles and block legacy authentic...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score) | [Active Directory security best practices](https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory) | [Azure AD security baseline](https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline) | [Privileged Identity Management](https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),Use Azure AD security baseline and Identity Secure Score to evaluate security posture. Implement limited administrative roles user risk policies MFA for administrative roles and block legacy authentication.,"Enhanced Implementation Context:
• Azure AD Identity Secure Score: https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score
• Active Directory security best practices: https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory
• Azure AD security baseline: https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline
• Privileged Identity Management: https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 5.4, 6.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8, SI-4
• PCI-DSS v3.2.1: 8.2, 8.3

Azure Policy Examples:
• No applicable built-in policy (requires configuration-based implementation)
• Use Azure AD Identity Secure Score recommendations
• Implement Azure AD security baseline configurations
• Monitor privileged account activities through Azure AD logs",ai_analysis,,Validated
roleAssignment.deploymentTemplate.json,IM-6,Identity Management,Use strong authentication controls,HIGH,28.0,"The 'roleDefinitionName' property is set to 'Owner', which grants the highest level of permissions in Azure. Assigning this role without enforcing strong authentication (such as MFA) for the associated principalId exposes the environment to account takeover and privilege escalation attacks. Attackers can exploit weak authentication to gain full control over the subscription.","Enforce multi-factor authentication (MFA) for all accounts assigned the 'Owner' role. Use Conditional Access policies to require strong authentication and monitor for suspicious sign-in activity. Reference: Azure Security Benchmark v3.0, Control IM-6.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/authentication/howto-mfa-getstarted) | [Passwordless authentication options](https://docs.microsoft.com/azure/active-directory/authentication/concept-authentication-passwordless) | [Azure AD password policies](https://docs.microsoft.com/azure/active-directory/authentication/concept-sspr-policy#password-policies-that-only-apply-to-cloud-user-accounts) | [Azure AD Password Protection](https://docs.microsoft.com/azure/active-directory/authentication/concept-password-ban-bad) | [Block legacy authentication](https://docs.microsoft.com/azure/active-directory/conditional-access/block-legacy-authentication)

🔵 Azure Guidance: Use Azure AD passwordless authentication as default method (Windows Hello Microsoft Authenticator FIDO2 Keys). Enable Azure MFA for all users with conditional access policies. Block legacy authenticat...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/authentication/howto-mfa-getstarted) | [Passwordless authentication options](https://docs.microsoft.com/azure/active-directory/authentication/concept-authentication-passwordless) | [Azure AD password policies](https://docs.microsoft.com/azure/active-directory/authentication/concept-sspr-policy#password-policies-that-only-apply-to-cloud-user-accounts) | [Azure AD Password Protection](https://docs.microsoft.com/azure/active-directory/authentication/concept-password-ban-bad) | [Block legacy authentication](https://docs.microsoft.com/azure/active-directory/conditional-access/block-legacy-authentication),Use Azure AD passwordless authentication as default method (Windows Hello Microsoft Authenticator FIDO2 Keys). Enable Azure MFA for all users with conditional access policies. Block legacy authentication.,"Enhanced Implementation Context:
• Azure MFA deployment: https://docs.microsoft.com/azure/active-directory/authentication/howto-mfa-getstarted
• Passwordless authentication options: https://docs.microsoft.com/azure/active-directory/authentication/concept-authentication-passwordless
• Azure AD password policies: https://docs.microsoft.com/azure/active-directory/authentication/concept-sspr-policy#password-policies-that-only-apply-to-cloud-user-accounts
• Azure AD Password Protection: https://docs.microsoft.com/azure/active-directory/authentication/concept-password-ban-bad
• Block legacy authentication: https://docs.microsoft.com/azure/active-directory/conditional-access/block-legacy-authentication

Compliance Mappings:
• CIS Controls v8: 6.3, 6.4
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-5, IA-8
• PCI-DSS v3.2.1: 7.2, 8.2, 8.3, 8.4

Azure Policy Examples:
• Authentication to Linux machines should require SSH keys
• MFA should be enabled on accounts with write permissions on your subscription
• MFA should be enabled on accounts with owner permissions on your subscription
• MFA should be enabled on accounts with read permissions on your subscription",ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 17,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 0,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-24T17:07:42.611095,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
