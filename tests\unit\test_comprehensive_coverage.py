#!/usr/bin/env python3
"""
Test script to verify ALL resources get ALL 27 controls for comprehensive security coverage
"""

import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_comprehensive_control_coverage():
    """Test that ALL resource types get ALL 27 controls."""
    print("🔒 Testing Comprehensive Control Coverage")
    print("=" * 60)
    print("Verifying that EVERY resource type gets ALL 27 controls")
    print("Rationale: Any resource can be compromised through unexpected attack vectors")
    print()
    
    try:
        from enhanced_resource_control_mappings import EnhancedResourceControlMapper
        
        mapper = EnhancedResourceControlMapper()
        
        # Get total available controls
        total_controls = len(mapper.control_database)
        print(f"📊 Total available controls: {total_controls}")
        
        # Test various Azure resource types
        test_resources = [
            "Microsoft.Storage/storageAccounts",
            "Microsoft.KeyVault/vaults", 
            "Microsoft.Network/networkSecurityGroups",
            "Microsoft.Sql/servers",
            "Microsoft.Compute/virtualMachines",
            "Microsoft.Web/sites",
            "Microsoft.ContainerService/managedClusters",
            "Microsoft.DocumentDB/databaseAccounts",
            "Microsoft.Network/virtualNetworks",
            "Microsoft.Storage/storageAccounts/blobServices",
            "Microsoft.Insights/components",
            "Microsoft.Resources/resourceGroups"  # Test unknown resource type
        ]
        
        print(f"\n🧪 Testing {len(test_resources)} resource types:")
        print("-" * 60)
        
        all_comprehensive = True
        coverage_results = {}
        
        for resource_type in test_resources:
            controls = mapper.get_controls_for_resource_type(resource_type)
            control_count = len(controls)
            
            # Check if we have comprehensive coverage (all 27 controls)
            is_comprehensive = control_count == total_controls
            
            if not is_comprehensive:
                all_comprehensive = False
            
            # Get domain breakdown
            domain_counts = {}
            control_ids = []
            for control in controls:
                domain = control.get('domain', 'Unknown')
                control_id = control.get('id', 'UNKNOWN')
                domain_counts[domain] = domain_counts.get(domain, 0) + 1
                control_ids.append(control_id)
            
            coverage_results[resource_type] = {
                'control_count': control_count,
                'is_comprehensive': is_comprehensive,
                'domain_counts': domain_counts,
                'control_ids': sorted(control_ids)
            }
            
            # Display results
            status = "✅ COMPREHENSIVE" if is_comprehensive else f"❌ LIMITED ({control_count}/{total_controls})"
            print(f"{status} {resource_type}")
            
            # Show domain breakdown
            domain_summary = []
            for domain, count in sorted(domain_counts.items()):
                domain_summary.append(f"{domain}: {count}")
            print(f"   📋 Domains: {' | '.join(domain_summary)}")
            
            # Show first few control IDs
            if control_ids:
                sample_controls = ', '.join(sorted(control_ids)[:8])
                if len(control_ids) > 8:
                    sample_controls += f", ... (+{len(control_ids)-8} more)"
                print(f"   🔍 Controls: {sample_controls}")
            
            print()
        
        # Summary
        print("=" * 60)
        print("📊 COMPREHENSIVE COVERAGE SUMMARY")
        print("=" * 60)
        
        comprehensive_count = sum(1 for result in coverage_results.values() if result['is_comprehensive'])
        total_tested = len(coverage_results)
        
        print(f"Resources with comprehensive coverage: {comprehensive_count}/{total_tested}")
        
        if all_comprehensive:
            print("🎉 SUCCESS: ALL resource types have comprehensive control coverage!")
            print("🔒 Every resource will be evaluated against all 27 security controls")
            print("🛡️ Maximum security coverage achieved - no attack vector missed")
        else:
            print("⚠️ WARNING: Some resource types have limited coverage")
            print("\nResources with limited coverage:")
            for resource_type, result in coverage_results.items():
                if not result['is_comprehensive']:
                    missing = total_controls - result['control_count']
                    print(f"   ❌ {resource_type}: Missing {missing} controls")
        
        # Test specific security scenarios
        print(f"\n🔍 SECURITY SCENARIO TESTING")
        print("-" * 40)
        
        # Scenario 1: KeyVault should have Network Security controls
        keyvault_controls = mapper.get_controls_for_resource_type("Microsoft.KeyVault/vaults")
        keyvault_control_ids = [c.get('id', '') for c in keyvault_controls]
        
        ns_controls_in_keyvault = [cid for cid in keyvault_control_ids if cid.startswith('NS-')]
        print(f"KeyVault Network Security controls: {len(ns_controls_in_keyvault)}")
        print(f"   Controls: {', '.join(sorted(ns_controls_in_keyvault))}")
        
        if len(ns_controls_in_keyvault) >= 8:  # Should have most/all NS controls
            print("   ✅ KeyVault can be protected against network-based attacks")
        else:
            print("   ❌ KeyVault missing network security controls - vulnerability!")
        
        # Scenario 2: Network resources should have Data Protection controls  
        nsg_controls = mapper.get_controls_for_resource_type("Microsoft.Network/networkSecurityGroups")
        nsg_control_ids = [c.get('id', '') for c in nsg_controls]
        
        dp_controls_in_nsg = [cid for cid in nsg_control_ids if cid.startswith('DP-')]
        print(f"\nNetwork Security Group Data Protection controls: {len(dp_controls_in_nsg)}")
        print(f"   Controls: {', '.join(sorted(dp_controls_in_nsg))}")
        
        if len(dp_controls_in_nsg) >= 6:  # Should have most/all DP controls
            print("   ✅ Network resources can protect data in transit")
        else:
            print("   ❌ Network resources missing data protection controls - vulnerability!")
        
        return all_comprehensive
        
    except Exception as e:
        print(f"❌ Error: {e}")
        logger.exception("Detailed error:")
        return False

def test_integration_with_security_opt():
    """Test that security_opt.py will use the comprehensive coverage."""
    print(f"\n🔧 Testing Integration with security_opt.py")
    print("=" * 60)
    
    try:
        from security_opt import SecurityPRReviewer
        
        reviewer = SecurityPRReviewer(local_folder=".")
        
        # Test that enhanced mapper is working
        if hasattr(reviewer, 'enhanced_mapper') and reviewer.enhanced_mapper:
            print("✅ Enhanced mapper integrated in security_opt.py")
            
            # Test comprehensive coverage through security_opt
            test_resource = "Microsoft.KeyVault/vaults"
            controls = reviewer._find_relevant_controls(test_resource)
            
            print(f"📋 Controls returned by security_opt for {test_resource}: {len(controls)}")
            
            control_ids = [c.get('id', 'UNKNOWN') for c in controls]
            
            # Check for comprehensive coverage
            has_im = any(cid.startswith('IM-') for cid in control_ids)
            has_ns = any(cid.startswith('NS-') for cid in control_ids)  
            has_dp = any(cid.startswith('DP-') for cid in control_ids)
            
            print(f"   🔍 Identity Management controls: {'✅' if has_im else '❌'}")
            print(f"   🔍 Network Security controls: {'✅' if has_ns else '❌'}")
            print(f"   🔍 Data Protection controls: {'✅' if has_dp else '❌'}")
            
            if has_im and has_ns and has_dp:
                print("   🎉 Comprehensive multi-domain coverage confirmed!")
                return True
            else:
                print("   ❌ Missing domain coverage in security_opt integration")
                return False
        else:
            print("❌ Enhanced mapper not integrated in security_opt.py")
            return False
            
    except Exception as e:
        print(f"❌ Error testing security_opt integration: {e}")
        return False

def main():
    """Main test function."""
    print("🔒 COMPREHENSIVE SECURITY CONTROL COVERAGE TEST")
    print("=" * 80)
    print("Testing that EVERY resource gets EVERY control for maximum security")
    print("=" * 80)
    
    success = True
    
    # Test comprehensive coverage
    if not test_comprehensive_control_coverage():
        print("❌ Comprehensive coverage test failed")
        success = False
    
    # Test integration
    if not test_integration_with_security_opt():
        print("❌ Security_opt integration test failed")
        success = False
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("🔒 COMPREHENSIVE SECURITY COVERAGE ACHIEVED")
        print()
        print("✅ Every resource type gets ALL 27 security controls")
        print("✅ No attack vector will be missed")
        print("✅ KeyVault gets Network Security controls")
        print("✅ Network resources get Data Protection controls")
        print("✅ Maximum security posture implemented")
        print()
        print("Your next security analysis will have COMPREHENSIVE coverage!")
    else:
        print("❌ TESTS FAILED - Security coverage is incomplete")
        print("Please review and fix the issues above")

if __name__ == "__main__":
    main()
