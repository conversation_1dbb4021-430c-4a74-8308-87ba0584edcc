{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "1.0.0.0", "parameters": {"storageAccountName": {"type": "string", "defaultValue": "mystorageaccount"}, "keyVaultName": {"type": "string", "defaultValue": "my<PERSON><PERSON><PERSON>"}}, "resources": [{"type": "Microsoft.Storage/storageAccounts", "apiVersion": "2021-04-01", "name": "[parameters('storageAccountName')]", "location": "[resourceGroup().location]", "sku": {"name": "Standard_LRS"}, "kind": "StorageV2", "properties": {"supportsHttpsTrafficOnly": false, "allowBlobPublicAccess": true, "minimumTlsVersion": "TLS1_0", "networkAcls": {"defaultAction": "Allow"}}}, {"type": "Microsoft.KeyVault/vaults", "apiVersion": "2021-04-01-preview", "name": "[parameters('keyVaultName')]", "location": "[resourceGroup().location]", "dependsOn": ["[resourceId('Microsoft.Storage/storageAccounts', parameters('storageAccountName'))]"], "properties": {"sku": {"family": "A", "name": "standard"}, "tenantId": "[subscription().tenantId]", "enabledForDeployment": true, "enabledForTemplateDeployment": true, "enabledForDiskEncryption": true, "enableSoftDelete": false, "enablePurgeProtection": false, "networkAcls": {"defaultAction": "Allow"}, "accessPolicies": []}}, {"type": "Microsoft.Network/virtualNetworks", "apiVersion": "2021-02-01", "name": "myVNet", "location": "[resourceGroup().location]", "properties": {"addressSpace": {"addressPrefixes": ["10.0.0.0/16"]}, "subnets": [{"name": "default", "properties": {"addressPrefix": "10.0.0.0/24"}}]}}]}