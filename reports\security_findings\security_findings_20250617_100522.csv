Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,template.json,120,"App Service 'onefuzz-daily-ui' is not protected by a network security group (NSG) or Azure Firewall. 'ipSecurityRestrictions' allow all traffic, which does not meet the requirement to protect sensitive resources using NSGs or firewalls.",Apply network security groups (NSGs) or Azure Firewall to restrict access to the App Service. Update 'ipSecurityRestrictions' to allow only trusted IP ranges.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,120,"App Service 'onefuzz-daily-ui' allows public network access ('publicNetworkAccess': 'Enabled') and has 'ipSecurityRestrictions' with 'ipAddress': 'Any' and 'action': 'Allow', exposing the app to the public internet. This violates the requirement to secure all public endpoints.",Restrict 'ipSecurityRestrictions' to only allow trusted IP ranges. Set 'publicNetworkAccess' to 'Disabled' or use private endpoints to minimize public exposure.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,128,"App Service 'onefuzz-daily-ui' allows public network access to the SCM endpoint ('scmIpSecurityRestrictions' with 'ipAddress': 'Any' and 'action': 'Allow'), exposing the SCM endpoint to the public internet. This violates the requirement to secure all public endpoints.",Restrict 'scmIpSecurityRestrictions' to only allow trusted IP ranges. Consider disabling public SCM access or using private endpoints.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-3,template.json,120,App Service 'onefuzz-daily-ui' does not implement network security groups (NSGs) to control inbound and outbound traffic. 'ipSecurityRestrictions' allow all traffic.,Implement NSGs to control traffic to and from the App Service. Restrict 'ipSecurityRestrictions' to only trusted IP addresses.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,120,"App Service 'onefuzz-daily-ui' does not use private endpoints ('publicNetworkAccess': 'Enabled'), increasing exposure to the public internet. This violates the recommendation to use private endpoints for secure access.",Configure a private endpoint for the App Service and set 'publicNetworkAccess' to 'Disabled' to restrict access to internal networks only.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,120,App Service 'onefuzz-daily-ui' does not specify customer-managed keys (CMK) or explicit encryption at rest settings. This may violate the requirement to enable encryption at rest for all data storage.,Configure encryption at rest using Azure-managed or customer-managed keys for all App Service data storage.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,44,"App Service 'onefuzz-daily-ui' has 'sslState' set to 'Disabled' for hostName 'onefuzz-daily-ui.azurewebsites.net', which allows unencrypted HTTP traffic. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates entries to enforce HTTPS. Ensure all endpoints require TLS 1.2 or higher.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,51,"App Service 'onefuzz-daily-ui' has 'sslState' set to 'Disabled' for hostName 'onefuzz-daily-ui.scm.azurewebsites.net', which allows unencrypted HTTP traffic. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates entries to enforce HTTPS. Ensure all endpoints require TLS 1.2 or higher.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,120,"App Service 'onefuzz-daily-ui' includes a 'publishingUsername' property in plain text in the configuration, which may expose sensitive information. This violates the requirement to store sensitive data like credentials in Azure Key Vault.",Remove plain text credentials from the configuration and reference secrets securely from Azure Key Vault using Key Vault references.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-6,template.json,120,"App Service 'onefuzz-daily-ui' does not specify the use of customer-managed keys (CMK) for encryption, which is required for sensitive data.",Configure the App Service to use customer-managed keys (CMK) for encryption of sensitive data at rest.,N/A,AI,Generic
