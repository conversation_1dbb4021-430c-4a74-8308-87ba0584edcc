/* Glass UI Framework - IaC Guardian Security Reports */

:root {
    /* 🎨 Primary Color Palette (Blue tone) */
    --hue-primary: 223;
    --primary500: hsl(var(--hue-primary), 90%, 50%);
    --primary600: hsl(var(--hue-primary), 90%, 60%);
    --primary700: hsl(var(--hue-primary), 90%, 70%);

    /* 🟢 Secondary Color Palette (Teal tone) */
    --hue-secondary: 178;
    --secondary800: hsl(var(--hue-secondary), 90%, 80%);

    /* 🌑 Dark Grays (used for dark backgrounds) */
    --dark-gray50: hsl(var(--hue-primary), 90%, 5%);
    --dark-gray100: hsl(var(--hue-primary), 90%, 10%);

    /* ⚪ White Transparency Palette (used for glass effects, overlays) */
    --white0: hsla(0, 0%, 100%, 0);
    --white50: hsla(0, 0%, 100%, 0.05);
    --white100: hsla(0, 0%, 100%, 0.1);
    --white200: hsla(0, 0%, 100%, 0.2);
    --white300: hsla(0, 0%, 100%, 0.3);
    --white400: hsla(0, 0%, 100%, 0.4);
    --white500: hsla(0, 0%, 100%, 0.5);
    --white: hsl(0, 0%, 100%);

    /* 🧮 Base font scaling */
    font-size: clamp(0.75rem, 0.65rem + 0.5vw, 1.25rem);

    /* Glass UI Semantic Colors with New Palette */
    --success-green: hsl(142, 76%, 36%);
    --warning-amber: hsl(38, 92%, 50%);
    --danger-red: hsl(0, 84%, 60%);
    --info-cyan: var(--secondary800);

    /* Glass UI Components using White Transparency */
    --glass-white: var(--white200);
    --glass-white-light: var(--white100);
    --glass-white-strong: var(--white400);
    --glass-border: var(--white200);

    /* 📝 Text Color Palette - Optimized for Glass UI */
    --text-primary: var(--white);
    --text-secondary: hsla(0, 0%, 100%, 0.85);
    --text-muted: hsla(0, 0%, 100%, 0.65);
    --text-accent: hsl(var(--hue-secondary), 90%, 85%);
    --text-on-glass: hsla(0, 0%, 100%, 0.95);
    --text-on-dark: var(--white);
    --text-on-light: hsl(var(--hue-primary), 90%, 15%);
    --text-interactive: hsl(var(--hue-primary), 90%, 85%);
    --text-hover: hsl(var(--hue-secondary), 90%, 90%);

    /* Semantic Colors with Glass Effects */
    --critical-glass: hsla(0, 84%, 60%, 0.2);
    --critical-border: hsla(0, 84%, 60%, 0.3);
    --critical-text: hsl(0, 84%, 80%);
    --high-glass: hsla(38, 92%, 50%, 0.2);
    --high-border: hsla(38, 92%, 50%, 0.3);
    --high-text: hsl(38, 92%, 75%);
    --medium-glass: hsla(45, 93%, 47%, 0.2);
    --medium-border: hsla(45, 93%, 47%, 0.3);
    --medium-text: hsl(45, 93%, 75%);
    --low-glass: hsla(var(--hue-secondary), 90%, 80%, 0.2);
    --low-border: hsla(var(--hue-secondary), 90%, 80%, 0.3);
    --low-text: var(--secondary800);

    /* Glass UI Layout */
    --max-width: 1400px;
    --border-radius: 16px;
    --border-radius-sm: 12px;
    --border-radius-lg: 24px;
    --glass-blur: blur(16px);
    --glass-blur-strong: blur(24px);
    --glass-shadow: 0 8px 32px hsla(var(--hue-primary), 90%, 5%, 0.12);
    --glass-shadow-lg: 0 16px 64px hsla(var(--hue-primary), 90%, 5%, 0.16);
    --glass-shadow-xl: 0 24px 96px hsla(var(--hue-primary), 90%, 5%, 0.2);
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-on-glass);
    background: linear-gradient(135deg,
        var(--dark-gray50) 0%,
        var(--dark-gray100) 30%,
        hsl(var(--hue-primary), 60%, 15%) 70%,
        hsl(var(--hue-secondary), 40%, 20%) 100%);
    background-attachment: fixed;
    min-height: 100vh;
    overflow-x: hidden;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, hsla(var(--hue-primary), 90%, 50%, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, hsla(var(--hue-secondary), 90%, 60%, 0.12) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, var(--white50) 0%, transparent 70%);
    pointer-events: none;
    z-index: -1;
}

.main-container {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 2rem 1rem;
    position: relative;
    z-index: 1;
}

/* Glass UI Header Section */
.report-header {
    background: var(--glass-white);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    padding: 3rem 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--glass-shadow-lg);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.report-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary500), var(--primary600), var(--secondary800));
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.report-header::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
    pointer-events: none;
    border-radius: inherit;
}

.report-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-on-glass);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    text-shadow: 0 2px 8px hsla(var(--hue-primary), 90%, 5%, 0.3);
    position: relative;
    z-index: 2;
}

.report-subtitle {
    font-size: 1.125rem;
    color: var(--text-accent);
    font-weight: 400;
    margin-bottom: 1rem;
    position: relative;
    z-index: 2;
    text-shadow: 0 1px 4px hsla(var(--hue-primary), 90%, 5%, 0.2);
}

.report-meta {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 1.5rem;
    font-size: 0.875rem;
    color: var(--text-interactive);
    position: relative;
    z-index: 2;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--glass-white-light);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

/* Glass UI Controls Section */
.controls-section {
    background: var(--glass-white);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--glass-shadow);
}

.controls-grid {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 1.5rem;
    align-items: center;
}

.search-container {
    position: relative;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-sm);
    font-size: 0.875rem;
    transition: all 0.3s ease;
    background: var(--glass-white-light);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    color: var(--text-on-glass);
}

.search-input::placeholder {
    color: var(--text-interactive);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary500);
    background: var(--glass-white-strong);
    box-shadow: 0 0 0 3px hsla(var(--hue-primary), 90%, 50%, 0.2);
    transform: translateY(-1px);
    color: var(--text-on-glass);
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-interactive);
}

.filter-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--glass-border);
    border-radius: 2rem;
    background: var(--glass-white-light);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    color: var(--text-interactive);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    white-space: nowrap;
}

.filter-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--glass-shadow);
    background: var(--glass-white-strong);
    color: var(--text-hover);
}

.filter-btn.active {
    color: var(--text-on-dark);
    transform: translateY(-2px);
    box-shadow: var(--glass-shadow-lg);
    border-color: transparent;
}

.filter-btn.all.active {
    background: linear-gradient(135deg, var(--primary500), var(--primary600));
}
.filter-btn.critical.active {
    background: linear-gradient(135deg, var(--danger-red), var(--critical-text));
}
.filter-btn.high.active {
    background: linear-gradient(135deg, var(--warning-amber), var(--high-text));
}
.filter-btn.medium.active {
    background: linear-gradient(135deg, hsl(45, 93%, 47%), var(--medium-text));
}
.filter-btn.low.active {
    background: linear-gradient(135deg, var(--info-cyan), var(--secondary800));
}

/* Multi-select filter enhancements */
.filter-btn.active {
    position: relative;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.multi-select-info {
    text-align: center;
    margin-top: 0.5rem;
}

.filter-summary {
    text-align: center;
    font-weight: 500;
}

/* Animation for filter changes */
.severity-group {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.severity-group[style*="display: none"] {
    opacity: 0;
    transform: translateY(-10px);
}

.finding-item {
    transition: opacity 0.2s ease;
}

.finding-item[style*="display: none"] {
    opacity: 0;
}

/* Glass UI Summary Section */
.summary-section {
    background: var(--glass-white);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--glass-shadow);
}

.summary-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-on-glass);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-shadow: 0 2px 8px hsla(var(--hue-primary), 90%, 5%, 0.3);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--glass-white-light);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--glass-shadow-lg);
    background: var(--glass-white-strong);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary500), var(--secondary800));
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.stat-card::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.06) 0%, transparent 60%);
    pointer-events: none;
    border-radius: inherit;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-on-glass);
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 8px hsla(var(--hue-primary), 90%, 5%, 0.3);
    position: relative;
    z-index: 2;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-accent);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: relative;
    z-index: 2;
}

.severity-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1.5rem;
}

.severity-badge {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    transition: all 0.3s ease;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid var(--glass-border);
    position: relative;
    overflow: hidden;
}

.severity-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: inherit;
    opacity: 0.1;
    z-index: -1;
}

.severity-badge:hover {
    transform: translateY(-2px);
    box-shadow: var(--glass-shadow);
}

.severity-badge.critical {
    background: var(--critical-glass);
    border-color: var(--critical-border);
    color: var(--critical-text);
}

.severity-badge.high {
    background: var(--high-glass);
    border-color: var(--high-border);
    color: var(--high-text);
}

.severity-badge.medium {
    background: var(--medium-glass);
    border-color: var(--medium-border);
    color: var(--medium-text);
}

.severity-badge.low {
    background: var(--low-glass);
    border-color: var(--low-border);
    color: var(--low-text);
}

/* Glass UI Domain Section Styles */
.domain-section {
    margin-bottom: 2rem;
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    background: var(--glass-white);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    box-shadow: var(--glass-shadow);
    transition: all 0.3s ease;
}

.domain-section:hover {
    transform: translateY(-2px);
    box-shadow: var(--glass-shadow-lg);
}

.domain-header {
    background: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 50%, #03A9F4 100%);
    color: var(--white);
    padding: 1.5rem 2rem;
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(79, 195, 247, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;
}

.domain-header:hover {
    background: linear-gradient(135deg, #29B6F6 0%, #03A9F4 50%, #0288D1 100%);
    transform: translateY(-1px);
}

.domain-header-left {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.domain-toggle-icon {
    transition: transform 0.3s ease;
    font-size: 1rem;
    opacity: 0.8;
}

.domain-header.collapsed .domain-toggle-icon {
    transform: rotate(-90deg);
}

.domain-header.collapsed {
    border-radius: var(--border-radius);
}

/* Domain-specific color schemes */
.domain-section[data-domain="identity-management"] .domain-header {
    background: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 50%, #03A9F4 100%);
    box-shadow: 0 4px 12px rgba(79, 195, 247, 0.3);
}

.domain-section[data-domain="identity-management"] .domain-header:hover {
    background: linear-gradient(135deg, #29B6F6 0%, #03A9F4 50%, #0288D1 100%);
}

.domain-section[data-domain="network-security"] .domain-header {
    background: linear-gradient(135deg, #66BB6A 0%, #4CAF50 50%, #388E3C 100%);
    box-shadow: 0 4px 12px rgba(102, 187, 106, 0.3);
}

.domain-section[data-domain="network-security"] .domain-header:hover {
    background: linear-gradient(135deg, #4CAF50 0%, #388E3C 50%, #2E7D32 100%);
}

.domain-section[data-domain="data-protection"] .domain-header {
    background: linear-gradient(135deg, #FF7043 0%, #FF5722 50%, #D84315 100%);
    box-shadow: 0 4px 12px rgba(255, 112, 67, 0.3);
}

.domain-section[data-domain="data-protection"] .domain-header:hover {
    background: linear-gradient(135deg, #FF5722 0%, #D84315 50%, #BF360C 100%);
}

/* Glass UI Severity Group Styles */
.severity-group {
    background: var(--glass-white-light);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    border-radius: 0;
    margin: 0;
    box-shadow: none;
    transition: all 0.3s ease;
    overflow: hidden;
}

.domain-section .severity-group {
    border-radius: 0;
    border: none;
    border-bottom: 1px solid var(--glass-border);
    box-shadow: none;
    background: var(--glass-white-light);
}

.domain-section .severity-group:last-child {
    border-bottom: none;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* Severity Header Styles */
.severity-header {
    padding: 1.25rem 1.5rem;
    font-weight: 600;
    font-size: 1.125rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 1px solid var(--glass-border);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.severity-header:hover {
    background: var(--glass-white-strong);
    transform: translateY(-1px);
}

.severity-header-left {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.severity-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.severity-header.critical {
    background: var(--critical-glass);
    color: var(--critical-text);
    border-left: 4px solid var(--critical-text);
}

.severity-header.critical .severity-icon {
    background: linear-gradient(135deg, var(--danger-red), var(--critical-text));
}

.severity-header.high {
    background: var(--high-glass);
    color: var(--high-text);
    border-left: 4px solid var(--high-text);
}

.severity-header.high .severity-icon {
    background: linear-gradient(135deg, var(--warning-amber), var(--high-text));
}

.severity-header.medium {
    background: var(--medium-glass);
    color: var(--medium-text);
    border-left: 4px solid var(--medium-text);
}

.severity-header.medium .severity-icon {
    background: linear-gradient(135deg, hsl(45, 93%, 47%), var(--medium-text));
}

.severity-header.low {
    background: var(--low-glass);
    color: var(--low-text);
    border-left: 4px solid var(--low-text);
}

.severity-header.low .severity-icon {
    background: linear-gradient(135deg, var(--info-cyan), var(--secondary800));
}

.severity-title {
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.severity-count {
    background: rgba(255, 255, 255, 0.9);
    color: inherit;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 700;
    margin-left: 0.5rem;
}

/* Toggle Icon Styles */
.toggle-icon {
    transition: transform 0.3s ease;
    color: var(--text-interactive);
    opacity: 0.8;
}

.severity-header.collapsed .toggle-icon {
    transform: rotate(-90deg);
}

/* Findings List Styles */
.findings-list {
    transition: all 0.3s ease;
    overflow: hidden;
    max-height: none;
}

.findings-list.collapsed {
    max-height: 0;
}

.finding-item {
    border-bottom: 1px solid var(--glass-border);
    padding: 1.5rem;
    transition: all 0.3s ease;
    background: var(--glass-white-light);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.finding-item:last-child {
    border-bottom: none;
}

.finding-item:hover {
    background: var(--glass-white-strong);
    transform: translateY(-1px);
    box-shadow: var(--glass-shadow);
}

/* Finding Header and Content Styles */
.finding-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
}

.finding-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.finding-icon.critical {
    background: linear-gradient(135deg, var(--danger-red), var(--critical-text));
}

.finding-icon.high {
    background: linear-gradient(135deg, var(--warning-amber), var(--high-text));
}

.finding-icon.medium {
    background: linear-gradient(135deg, hsl(45, 93%, 47%), var(--medium-text));
}

.finding-icon.low {
    background: linear-gradient(135deg, var(--info-cyan), var(--secondary800));
}

.finding-content {
    flex: 1;
    min-width: 0;
}

.finding-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-on-glass);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.control-id {
    background: linear-gradient(135deg, var(--primary500), var(--primary600));
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.finding-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    color: var(--text-interactive);
    flex-wrap: wrap;
}

.finding-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.finding-recommendation {
    background: var(--glass-white);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-sm);
    padding: 1rem;
    margin-top: 1rem;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.finding-recommendation h4 {
    color: var(--success-green);
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.finding-recommendation p {
    color: var(--text-secondary);
    line-height: 1.5;
    margin: 0;
}

/* Remediation Section Styles */
.remediation-section {
    background: linear-gradient(135deg, var(--success-green) 0%, #059669 100%);
    color: var(--white);
    padding: 1rem;
    border-radius: var(--border-radius-sm);
    margin-top: 1rem;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(16, 185, 129, 0.3);
    box-shadow: var(--glass-shadow);
    position: relative;
    overflow: hidden;
}

.remediation-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, transparent 60%);
    border-radius: inherit;
    pointer-events: none;
}

.remediation-title {
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--white);
    position: relative;
    z-index: 1;
}

.remediation-content {
    font-size: 0.875rem;
    line-height: 1.5;
    opacity: 0.95;
    position: relative;
    z-index: 1;
}

/* Action Buttons */
.finding-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

/* Footer Styles */
.report-footer {
    background: var(--glass-white);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-top: 2rem;
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    text-align: center;
    box-shadow: var(--glass-shadow);
}

.export-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.export-btn {
    padding: 0.75rem 1.5rem;
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-sm);
    background: linear-gradient(135deg, var(--primary500), var(--primary600));
    color: var(--white);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.export-btn:hover {
    background: linear-gradient(135deg, var(--primary600), var(--primary700));
    transform: translateY(-2px);
    box-shadow: var(--glass-shadow);
}

.footer-info {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.6;
    text-align: center;
}

.footer-info p {
    margin: 0.5rem 0;
}

.footer-info strong {
    color: var(--text-primary);
    font-weight: 600;
}

.action-btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-sm);
    background: var(--glass-white-light);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    color: var(--text-interactive);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.75rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.action-btn:hover {
    background: var(--glass-white-strong);
    transform: translateY(-1px);
    box-shadow: var(--glass-shadow);
    color: var(--text-hover);
}

.action-btn.primary {
    background: linear-gradient(135deg, var(--primary500), var(--primary600));
    color: var(--white);
    border-color: transparent;
}

.action-btn.primary:hover {
    background: linear-gradient(135deg, var(--primary600), var(--primary700));
    color: var(--white);
}
