File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Source,Cross Reference Type,Validation Status
app-config.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyValues' flows across template boundary,Secure parameter 'keyValues' and validate its usage across template boundaries,cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'app_insights_key' flows across template boundary,Secure parameter 'app_insights_key' and validate its usage across template boundaries,cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyvault_name' flows across template boundary,Secure parameter 'keyvault_name' and validate its usage across template boundaries,cross_reference_analysis,privilege_escalation,Validated
keyvault.bicep,NS-1,Network Security,Protect resources using network security groups,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,Review template dependencies and ensure secure communication between templates,cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'secrets' flows across template boundary,Secure parameter 'secrets' and validate its usage across template boundaries,cross_reference_analysis,privilege_escalation,Validated
storage-accounts.bicep,NS-1,Network Security,Protect resources using network security groups,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,Review template dependencies and ensure secure communication between templates,cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,DP-6,Data Protection,Secure Data with Customer-Managed Keys (CMK),HIGH,1.0,Parameter flow security risk: Sensitive parameters may be exposed through template dependencies,Use Key Vault references for sensitive parameters and validate parameter flow security,cross_reference_analysis,parameter_flow,Validated
app-config.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,7.0,"The App Configuration resource 'Microsoft.AppConfiguration/configurationStores' does not restrict public network access. By default, endpoints are public unless 'publicNetworkAccess' is explicitly set to 'Disabled' or network ACLs are configured.",Add the 'publicNetworkAccess' property set to 'Disabled' or configure 'networkAcls' to restrict access to required IPs only in the App Configuration resource definition to comply with ASB NS-2.,ai_analysis,,Validated
autoscale-settings.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,9.0,The 'Microsoft.Insights/autoscalesettings' resource does not specify the use of Azure Active Directory (Azure AD) or managed identities for identity management. This is required by ASB IM-1.,Configure the autoscaleSettings resource to use a managed identity by adding an 'identity' block with type 'SystemAssigned' or 'UserAssigned'. Ensure all access to Azure resources is performed using managed identities instead of credentials stored in code.,ai_analysis,,Validated
function-settings.bicep,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,24.0,"The parameter 'app_insights_key' is marked as @secure(), but is directly assigned to the 'APPINSIGHTS_INSTRUMENTATIONKEY' property in application settings (line 54). Storing secrets such as instrumentation keys in app settings, even if passed as secure parameters, can lead to sensitive information disclosure if not referenced via Key Vault.","Use Azure Key Vault references for 'APPINSIGHTS_INSTRUMENTATIONKEY' instead of passing the key directly. Update the 'APPINSIGHTS_INSTRUMENTATIONKEY' property to reference a Key Vault secret, and ensure the function app has access to the Key Vault.",ai_analysis,,Validated
function.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,49.0,"The App Service resource 'function' (Microsoft.Web/sites) does not restrict public access or IPs. By default, App Service endpoints are public unless access restrictions are explicitly configured. This exposes the function app to the public internet, violating NS-2 (Protect public endpoints).","Configure access restrictions for the App Service by adding 'ipSecurityRestrictions' in the 'siteConfig' property to allow only required IPs or subnets. Alternatively, use Azure Front Door, Application Gateway, or Private Endpoints to restrict public access.",ai_analysis,,Validated
function.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,38.0,"The Storage Account resource 'funcStorage' (Microsoft.Storage/storageAccounts) is referenced for logs, but there is no configuration for a Private Endpoint. This means the storage account may be accessible over the public internet, violating NS-5 (Use Private Endpoints).",Configure a Private Endpoint for the storage account to ensure access is only possible from within your virtual network. Add a 'Microsoft.Network/privateEndpoints' resource targeting the storage account.,ai_analysis,,Validated
hub-network.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,7.0,The subnet 'hub-subnet' defined at line 7 does not have a Network Security Group (NSG) associated. NSGs are required to segment and protect network resources.,Associate a Network Security Group (NSG) with the 'hub-subnet' by adding the 'networkSecurityGroup' property referencing an NSG resource.,ai_analysis,,Validated
hub-network.bicep,NS-3,Network Security,Use Network Security Groups (NSGs),CRITICAL,7.0,No Network Security Group (NSG) is configured for the 'hub-subnet' at line 7. NSGs are required to provide network-level access control and deny all inbound traffic by default.,Create an NSG resource and associate it with the 'hub-subnet' to restrict inbound and outbound traffic according to least privilege.,ai_analysis,,Validated
hub-network.bicep,NS-4,Network Security,Use Azure Firewall or third-party firewall,HIGH,1.0,No Azure Firewall or third-party firewall is defined in the template. Advanced network protection is essential for monitoring and controlling traffic.,Deploy an Azure Firewall or a supported third-party firewall in the virtual network and configure appropriate firewall policies.,ai_analysis,,Validated
hub-network.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,7.0,"Private Endpoints are not configured for services such as Storage or Key Vault, even though service endpoints are present in the subnet at line 7. Private Endpoints provide enhanced security by enabling private connectivity.",Configure Private Endpoints for Azure Storage and Key Vault services to ensure traffic remains on the Microsoft backbone network.,ai_analysis,,Validated
instance-config.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,22.0,"The network_config object defines a wide address_space ('10.0.0.0/8') and subnet ('10.0.0.0/16') but does not reference or configure any Network Security Groups (NSGs) to segment or restrict network traffic. This violates NS-1, which requires NSGs at subnet and NIC levels.",Define and associate appropriate Network Security Groups (NSGs) for the subnet and any NICs. Restrict inbound and outbound traffic to only required ports and protocols. Example: Add an NSG resource and link it to the subnet in your Bicep template.,ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,3.0,"IP rules include broad address ranges such as '*******/8' (line 3), which may allow excessive public access and violate the requirement to restrict public endpoints to required IPs only.","Restrict allowed IP ranges to only those that are strictly necessary. Replace broad ranges like '*******/8' with more specific, minimal IP ranges that correspond to trusted sources only, in accordance with NS-2.",ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,4.0,"IP rules include broad address ranges such as '********/8' (line 4), which may allow excessive public access and violate the requirement to restrict public endpoints to required IPs only.","Restrict allowed IP ranges to only those that are strictly necessary. Replace broad ranges like '********/8' with more specific, minimal IP ranges that correspond to trusted sources only, in accordance with NS-2.",ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,5.0,"IP rules include broad address ranges such as '20.0.0.0/8' (line 5), which may allow excessive public access and violate the requirement to restrict public endpoints to required IPs only.","Restrict allowed IP ranges to only those that are strictly necessary. Replace broad ranges like '20.0.0.0/8' with more specific, minimal IP ranges that correspond to trusted sources only, in accordance with NS-2.",ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,6.0,"IP rules include broad address ranges such as '40.0.0.0/8' (line 6), which may allow excessive public access and violate the requirement to restrict public endpoints to required IPs only.","Restrict allowed IP ranges to only those that are strictly necessary. Replace broad ranges like '40.0.0.0/8' with more specific, minimal IP ranges that correspond to trusted sources only, in accordance with NS-2.",ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,7.0,"IP rules include broad address ranges such as '********/8' (line 7), which may allow excessive public access and violate the requirement to restrict public endpoints to required IPs only.","Restrict allowed IP ranges to only those that are strictly necessary. Replace broad ranges like '********/8' with more specific, minimal IP ranges that correspond to trusted sources only, in accordance with NS-2.",ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,8.0,"IP rules include broad address ranges such as '********/8' (line 8), which may allow excessive public access and violate the requirement to restrict public endpoints to required IPs only.","Restrict allowed IP ranges to only those that are strictly necessary. Replace broad ranges like '********/8' with more specific, minimal IP ranges that correspond to trusted sources only, in accordance with NS-2.",ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,9.0,"IP rules include broad address ranges such as '********/8' (line 9), which may allow excessive public access and violate the requirement to restrict public endpoints to required IPs only.","Restrict allowed IP ranges to only those that are strictly necessary. Replace broad ranges like '********/8' with more specific, minimal IP ranges that correspond to trusted sources only, in accordance with NS-2.",ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,10.0,"IP rules include broad address ranges such as '70.0.0.0/8' (line 10), which may allow excessive public access and violate the requirement to restrict public endpoints to required IPs only.","Restrict allowed IP ranges to only those that are strictly necessary. Replace broad ranges like '70.0.0.0/8' with more specific, minimal IP ranges that correspond to trusted sources only, in accordance with NS-2.",ai_analysis,,Validated
keyvault.bicep,DP-6,Data Protection,Secure Data with Customer-Managed Keys (CMK),HIGH,18.0,"Key Vault 'networkAcls.defaultAction' is set to 'Allow', which permits public network access. This does not enforce secure access to encryption keys as required by DP-6.",Set 'networkAcls.defaultAction' to 'Deny' in the Key Vault resource to restrict public network access and only allow trusted networks.,ai_analysis,,Validated
operational-insights.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,"No use of Azure Active Directory or managed identities is specified for any resource. The template does not configure identity management for Synapse or related resources, violating IM-1.","Configure Azure Active Directory integration and use managed identities for all resources. For Synapse, enable managed identity and assign appropriate roles.",ai_analysis,,Validated
scaleset-networks.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,38.0,The subnet 'scaleset' defined at line 38 does not have a Network Security Group (NSG) associated. NSGs are required to segment and protect network resources.,Associate a Network Security Group (NSG) with the 'scaleset' subnet to restrict and control network traffic. Define an NSG resource and reference its id in the subnet's 'networkSecurityGroup' property.,ai_analysis,,Validated
scaleset-networks.bicep,NS-3,Network Security,Use Network Security Groups (NSGs),CRITICAL,38.0,The subnet 'scaleset' at line 38 lacks an explicit NSG configuration. NSGs are required to deny all inbound traffic by default and only allow necessary traffic.,Create and associate a Network Security Group (NSG) with the 'scaleset' subnet. Configure the NSG to deny all inbound traffic by default and only allow required ports and protocols.,ai_analysis,,Validated
server-farms.bicep,DP-2,Data Protection,Enable encryption in transit,CRITICAL,74.0,"App Service resource 'serverFarms' at line 74 does not explicitly enforce HTTPS-only or specify a minimum TLS version, violating DP-2 (Enable encryption in transit).","Add 'httpsOnly: true' and 'minimumTlsVersion: ""1.2""' to the 'properties' block of the App Service resource to enforce encryption in transit.",ai_analysis,,Validated
server-farms.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,74.0,"App Service resource 'serverFarms' at line 74 does not restrict public endpoints or limit access to required IPs, violating NS-2 (Protect public endpoints).","Restrict public access to the App Service by configuring access restrictions (e.g., 'ipSecurityRestrictions') to allow only required IP addresses or subnets.",ai_analysis,,Validated
signalR.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,5.0,"The Microsoft.SignalRService/signalR resource does not explicitly restrict public network access or limit allowed IPs. By default, SignalR Service endpoints are public unless network ACLs are configured.",Configure the 'networkACLs' property for the SignalR resource to restrict public access. Limit allowed IPs or use private endpoints. Example: add 'networkACLs' with 'defaultAction' set to 'Deny' and specify allowed IP rules.,ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,19.0,"The 'networkAcls' configuration for 'storageAccountFunc' sets 'defaultAction' to 'Allow', which permits public network access to the storage account. This violates ASB NS-2, which requires public endpoints to be strictly controlled.","Set 'networkAcls.defaultAction' to 'Deny' for 'storageAccountFunc' and explicitly allow only required IPs or subnets. Example: change 'defaultAction: ""Allow""' to 'defaultAction: ""Deny""' and ensure only necessary 'ipRules' and 'virtualNetworkRules' are present.",ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,49.0,"The 'networkAcls' configuration in 'fuzzStorageProperties' (used by 'storageAccountFuzz' and 'storageAccountsCorpus') sets 'defaultAction' to 'Allow', which permits public network access to the storage accounts. This violates ASB NS-2, which requires public endpoints to be strictly controlled.","Set 'networkAcls.defaultAction' to 'Deny' in 'fuzzStorageProperties' and explicitly allow only required IPs or subnets. Example: change 'defaultAction: ""Allow""' to 'defaultAction: ""Deny""' and ensure only necessary 'ipRules' and 'virtualNetworkRules' are present.",ai_analysis,,Validated
,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,
Total Findings: 34,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,
Cross-Reference Findings: 7,,,,,,,,,,
Analysis Timestamp: 2025-06-17T13:44:17.918103,,,,,,,,,,
