File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
simple_test.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,25.0,"The 'supportsHttpsTrafficOnly' property for the Microsoft.Storage/storageAccounts resource is set to false, allowing unencrypted HTTP connections. This enables attackers to intercept or modify data in transit, exposing sensitive storage account data to man-in-the-middle attacks. The blast radius includes all data transferred to and from the storage account, potentially impacting all connected resources.","Set 'supportsHttpsTrafficOnly' to true in the storage account properties to enforce HTTPS for all data transfers. This ensures data in transit is encrypted and protected from interception. Example: ""supportsHttpsTrafficOnly"": true",,,,ai_analysis,,Validated
simple_test.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,27.0,"The 'minimumTlsVersion' property for the Microsoft.Storage/storageAccounts resource is set to 'TLS1_0', which is an outdated and insecure protocol. Attackers can exploit known vulnerabilities in TLS 1.0 to decrypt or tamper with data in transit, increasing the risk of data exfiltration and compromise of connected resources.","Set 'minimumTlsVersion' to 'TLS1_2' or higher in the storage account properties to enforce strong encryption for data in transit. Example: ""minimumTlsVersion"": ""TLS1_2""",,,,ai_analysis,,Validated
simple_test.json,NS-2,Network Security,Protect public endpoints,CRITICAL,26.0,"The 'allowBlobPublicAccess' property for the Microsoft.Storage/storageAccounts resource is set to true, enabling public anonymous access to blob data. This creates an initial access vector for attackers to exfiltrate sensitive data without authentication, significantly increasing the blast radius to include all public internet users.","Set 'allowBlobPublicAccess' to false in the storage account properties to prevent anonymous public access to blob data. Example: ""allowBlobPublicAccess"": false",,,,ai_analysis,,Validated
simple_test.json,NS-2,Network Security,Protect public endpoints,CRITICAL,29.0,"The 'networkAcls.defaultAction' property for the Microsoft.Storage/storageAccounts resource is set to 'Allow', which permits all network traffic by default. This exposes the storage account to the public internet, enabling attackers to access or brute-force the resource from any location, increasing the risk of data compromise and lateral movement.","Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only trusted networks or private endpoints. Example: ""defaultAction"": ""Deny""",,,,ai_analysis,,Validated
simple_test.json,DP-8,Data Protection,Ensure security of key and certificate repository,CRITICAL,29.0,"The 'networkAcls.defaultAction' property for the Microsoft.KeyVault/vaults resource is set to 'Allow', exposing the Key Vault to the public internet. This enables attackers to attempt unauthorized access to cryptographic keys and secrets, increasing the risk of credential theft and compromise of all resources dependent on this Key Vault.","Set 'networkAcls.defaultAction' to 'Deny' and configure explicit access for trusted networks or private endpoints. Example: ""defaultAction"": ""Deny""",,,,ai_analysis,,Validated
simple_test.json,DP-8,Data Protection,Ensure security of key and certificate repository,CRITICAL,50.0,"The 'enableSoftDelete' property for the Microsoft.KeyVault/vaults resource is set to false, disabling soft delete protection. Attackers or malicious insiders can permanently delete keys and secrets, making recovery impossible and enabling denial-of-service or destructive attacks on dependent applications.","Set 'enableSoftDelete' to true in the Key Vault properties to enable soft delete protection. Example: ""enableSoftDelete"": true",,,,ai_analysis,,Validated
simple_test.json,DP-8,Data Protection,Ensure security of key and certificate repository,CRITICAL,51.0,"The 'enablePurgeProtection' property for the Microsoft.KeyVault/vaults resource is set to false, disabling purge protection. Attackers can permanently remove deleted keys and secrets, preventing recovery and increasing the risk of irreversible data loss or service disruption.","Set 'enablePurgeProtection' to true in the Key Vault properties to enable purge protection. Example: ""enablePurgeProtection"": true",,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 7,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 0,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T10:24:44.127897,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
