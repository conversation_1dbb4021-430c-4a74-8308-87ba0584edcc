<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Assessment Report - IaC Guardian</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            /* 🎨 Primary Color Palette (Blue tone) */
            --hue-primary: 223;
            --primary500: hsl(var(--hue-primary), 90%, 50%);
            --primary600: hsl(var(--hue-primary), 90%, 60%);
            --primary700: hsl(var(--hue-primary), 90%, 70%);

            /* 🟢 Secondary Color Palette (Teal tone) */
            --hue-secondary: 178;
            --secondary800: hsl(var(--hue-secondary), 90%, 80%);

            /* 🌑 Dark Grays (used for dark backgrounds) */
            --dark-gray50: hsl(var(--hue-primary), 90%, 5%);
            --dark-gray100: hsl(var(--hue-primary), 90%, 10%);

            /* ⚪ White Transparency Palette (used for glass effects, overlays) */
            --white0: hsla(0, 0%, 100%, 0);
            --white50: hsla(0, 0%, 100%, 0.05);
            --white100: hsla(0, 0%, 100%, 0.1);
            --white200: hsla(0, 0%, 100%, 0.2);
            --white300: hsla(0, 0%, 100%, 0.3);
            --white400: hsla(0, 0%, 100%, 0.4);
            --white500: hsla(0, 0%, 100%, 0.5);
            --white: hsl(0, 0%, 100%);

            /* 🧮 Base font scaling */
            font-size: clamp(0.75rem, 0.65rem + 0.5vw, 1.25rem);

            /* Glass UI Semantic Colors with New Palette */
            --success-green: hsl(142, 76%, 36%);
            --warning-amber: hsl(38, 92%, 50%);
            --danger-red: hsl(0, 84%, 60%);
            --info-cyan: var(--secondary800);

            /* Glass UI Components using White Transparency */
            --glass-white: var(--white200);
            --glass-white-light: var(--white100);
            --glass-white-strong: var(--white400);
            --glass-border: var(--white200);

            /* 📝 Text Color Palette - Optimized for Glass UI */
            --text-primary: var(--white);
            --text-secondary: hsla(0, 0%, 100%, 0.85);
            --text-muted: hsla(0, 0%, 100%, 0.65);
            --text-accent: hsl(var(--hue-secondary), 90%, 85%);
            --text-on-glass: hsla(0, 0%, 100%, 0.95);
            --text-on-dark: var(--white);
            --text-on-light: hsl(var(--hue-primary), 90%, 15%);
            --text-interactive: hsl(var(--hue-primary), 90%, 85%);
            --text-hover: hsl(var(--hue-secondary), 90%, 90%);

            /* Semantic Colors with Glass Effects */
            --critical-glass: hsla(0, 84%, 60%, 0.2);
            --critical-border: hsla(0, 84%, 60%, 0.3);
            --critical-text: hsl(0, 84%, 80%);
            --high-glass: hsla(38, 92%, 50%, 0.2);
            --high-border: hsla(38, 92%, 50%, 0.3);
            --high-text: hsl(38, 92%, 75%);
            --medium-glass: hsla(45, 93%, 47%, 0.2);
            --medium-border: hsla(45, 93%, 47%, 0.3);
            --medium-text: hsl(45, 93%, 75%);
            --low-glass: hsla(var(--hue-secondary), 90%, 80%, 0.2);
            --low-border: hsla(var(--hue-secondary), 90%, 80%, 0.3);
            --low-text: var(--secondary800);

            /* Glass UI Layout */
            --max-width: 1400px;
            --border-radius: 16px;
            --border-radius-sm: 12px;
            --border-radius-lg: 24px;
            --glass-blur: blur(16px);
            --glass-blur-strong: blur(24px);
            --glass-shadow: 0 8px 32px hsla(var(--hue-primary), 90%, 5%, 0.12);
            --glass-shadow-lg: 0 16px 64px hsla(var(--hue-primary), 90%, 5%, 0.16);
            --glass-shadow-xl: 0 24px 96px hsla(var(--hue-primary), 90%, 5%, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-on-glass);
            background: linear-gradient(135deg,
                var(--dark-gray50) 0%,
                var(--dark-gray100) 30%,
                hsl(var(--hue-primary), 60%, 15%) 70%,
                hsl(var(--hue-secondary), 40%, 20%) 100%);
            background-attachment: fixed;
            min-height: 100vh;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, hsla(var(--hue-primary), 90%, 50%, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, hsla(var(--hue-secondary), 90%, 60%, 0.12) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, var(--white50) 0%, transparent 70%);
            pointer-events: none;
            z-index: -1;
        }

        .main-container {
            max-width: var(--max-width);
            margin: 0 auto;
            padding: 2rem 1rem;
            position: relative;
            z-index: 1;
        }

        /* Glass UI Header Section */
        .report-header {
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-lg);
            padding: 3rem 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--glass-shadow-lg);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .report-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary500), var(--primary600), var(--secondary800));
            border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
        }

        .report-header::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
            pointer-events: none;
            border-radius: inherit;
        }

        .report-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-on-glass);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            text-shadow: 0 2px 8px hsla(var(--hue-primary), 90%, 5%, 0.3);
            position: relative;
            z-index: 2;
        }

        .report-subtitle {
            font-size: 1.125rem;
            color: var(--text-accent);
            font-weight: 400;
            margin-bottom: 1rem;
            position: relative;
            z-index: 2;
            text-shadow: 0 1px 4px hsla(var(--hue-primary), 90%, 5%, 0.2);
        }

        .report-meta {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 1.5rem;
            font-size: 0.875rem;
            color: var(--text-interactive);
            position: relative;
            z-index: 2;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: var(--glass-white-light);
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius-sm);
            border: 1px solid var(--glass-border);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }

        /* Glass UI Controls Section */
        .controls-section {
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--glass-shadow);
        }

        .controls-grid {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 1.5rem;
            align-items: center;
        }

        .search-container {
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-sm);
            font-size: 0.875rem;
            transition: all 0.3s ease;
            background: var(--glass-white-light);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            color: var(--text-on-glass);
        }

        .search-input::placeholder {
            color: var(--text-interactive);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary500);
            background: var(--glass-white-strong);
            box-shadow: 0 0 0 3px hsla(var(--hue-primary), 90%, 50%, 0.2);
            transform: translateY(-1px);
            color: var(--text-on-glass);
        }

        .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-interactive);
        }

        .filter-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 0.5rem 1rem;
            border: 1px solid var(--glass-border);
            border-radius: 2rem;
            background: var(--glass-white-light);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            color: var(--text-interactive);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            white-space: nowrap;
        }

        .filter-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--glass-shadow);
            background: var(--glass-white-strong);
            color: var(--text-hover);
        }

        .filter-btn.active {
            color: var(--text-on-dark);
            transform: translateY(-2px);
            box-shadow: var(--glass-shadow-lg);
            border-color: transparent;
        }

        .filter-btn.all.active {
            background: linear-gradient(135deg, var(--primary500), var(--primary600));
        }
        .filter-btn.critical.active {
            background: linear-gradient(135deg, var(--danger-red), var(--critical-text));
        }
        .filter-btn.high.active {
            background: linear-gradient(135deg, var(--warning-amber), var(--high-text));
        }
        .filter-btn.medium.active {
            background: linear-gradient(135deg, hsl(45, 93%, 47%), var(--medium-text));
        }
        .filter-btn.low.active {
            background: linear-gradient(135deg, var(--info-cyan), var(--secondary800));
        }

        /* Multi-select filter enhancements */
        .filter-btn.active {
            position: relative;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .multi-select-info {
            text-align: center;
            margin-top: 0.5rem;
        }

        .filter-summary {
            text-align: center;
            font-weight: 500;
        }

        /* Animation for filter changes */
        .severity-group {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        .severity-group[style*="display: none"] {
            opacity: 0;
            transform: translateY(-10px);
        }

        .finding-item {
            transition: opacity 0.2s ease;
        }

        .finding-item[style*="display: none"] {
            opacity: 0;
        }

        /* Glass UI Summary Section */
        .summary-section {
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--glass-shadow);
        }

        .summary-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-on-glass);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-shadow: 0 2px 8px hsla(var(--hue-primary), 90%, 5%, 0.3);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--glass-white-light);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--glass-shadow-lg);
            background: var(--glass-white-strong);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary500), var(--secondary800));
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        .stat-card::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 50%;
            height: 100%;
            background: radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.06) 0%, transparent 60%);
            pointer-events: none;
            border-radius: inherit;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-on-glass);
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 8px hsla(var(--hue-primary), 90%, 5%, 0.3);
            position: relative;
            z-index: 2;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-accent);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            position: relative;
            z-index: 2;
        }

        .severity-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .severity-badge {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border: 1px solid var(--glass-border);
            position: relative;
            overflow: hidden;
        }

        .severity-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: inherit;
            opacity: 0.1;
            z-index: -1;
        }

        .severity-badge:hover {
            transform: translateY(-2px);
            box-shadow: var(--glass-shadow);
        }

        .severity-badge.critical {
            background: var(--critical-glass);
            border-color: var(--critical-border);
            color: var(--critical-text);
        }

        .severity-badge.high {
            background: var(--high-glass);
            border-color: var(--high-border);
            color: var(--high-text);
        }

        .severity-badge.medium {
            background: var(--medium-glass);
            border-color: var(--medium-border);
            color: var(--medium-text);
        }

        .severity-badge.low {
            background: var(--low-glass);
            border-color: var(--low-border);
            color: var(--low-text);
        }

        .severity-count {
            font-size: 1.25rem;
            font-weight: 700;
            text-shadow: 0 1px 4px hsla(var(--hue-primary), 90%, 5%, 0.2);
        }

        .severity-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* Glass UI Findings Section */
        .findings-container {
            margin-bottom: 2rem;
        }

        .severity-group {
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
            box-shadow: var(--glass-shadow);
            overflow: hidden;
            border: 1px solid var(--glass-border);
            transition: all 0.3s ease;
        }

        .severity-group:hover {
            transform: translateY(-2px);
            box-shadow: var(--glass-shadow-lg);
        }

        .severity-group:last-child {
            margin-bottom: 0;
        }

        /* Glass UI Domain Section Styles */
        .domain-section {
            margin-bottom: 2rem;
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            box-shadow: var(--glass-shadow);
            transition: all 0.3s ease;
        }

        .domain-section:hover {
            transform: translateY(-2px);
            box-shadow: var(--glass-shadow-lg);
        }

        .domain-header {
            background: linear-gradient(135deg, var(--primary500) 0%, var(--secondary800) 100%);
            color: var(--white);
            padding: 1.5rem 2rem;
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            position: relative;
            overflow: hidden;
        }

        .domain-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            border-radius: inherit;
        }

        .domain-header i {
            font-size: 1.5rem;
            position: relative;
            z-index: 2;
        }

        .domain-section .severity-group {
            margin: 0;
            border-radius: 0;
            border: none;
            border-bottom: 1px solid var(--glass-border);
            box-shadow: none;
            background: var(--glass-white-light);
        }

        .domain-section .severity-group:last-child {
            border-bottom: none;
            border-radius: 0 0 var(--border-radius) var(--border-radius);
        }

        .severity-header {
            padding: 1.25rem 1.5rem;
            font-weight: 600;
            font-size: 1.125rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 1px solid var(--glass-border);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }

        .severity-header:hover {
            background: var(--glass-white-strong);
            transform: translateX(4px);
        }

        .severity-header-left {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .severity-icon {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.875rem;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .severity-header.critical {
            background: var(--critical-glass);
            color: var(--critical-text);
            border-left: 4px solid var(--critical-text);
        }

        .severity-header.critical .severity-icon {
            background: linear-gradient(135deg, var(--danger-red), var(--critical-text));
        }

        .severity-header.high {
            background: var(--high-glass);
            color: var(--high-text);
            border-left: 4px solid var(--high-text);
        }

        .severity-header.high .severity-icon {
            background: linear-gradient(135deg, var(--warning-amber), var(--high-text));
        }

        .severity-header.medium {
            background: var(--medium-glass);
            color: var(--medium-text);
            border-left: 4px solid var(--medium-text);
        }

        .severity-header.medium .severity-icon {
            background: linear-gradient(135deg, hsl(45, 93%, 47%), var(--medium-text));
        }

        .severity-header.low {
            background: var(--low-glass);
            color: var(--low-text);
            border-left: 4px solid var(--low-text);
        }

        .severity-header.low .severity-icon {
            background: linear-gradient(135deg, var(--info-cyan), var(--secondary800));
        }

        .severity-title {
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .severity-count {
            background: rgba(255, 255, 255, 0.9);
            color: inherit;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 700;
            margin-left: 0.5rem;
        }

        .toggle-icon {
            transition: transform 0.2s ease;
            color: var(--gray-500);
        }

        .severity-header.collapsed .toggle-icon {
            transform: rotate(-90deg);
        }

        .findings-list {
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .findings-list.collapsed {
            max-height: 0;
        }

        .finding-item {
            border-bottom: 1px solid var(--glass-border);
            padding: 1.5rem;
            transition: all 0.3s ease;
            background: var(--glass-white-light);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }

        .finding-item:last-child {
            border-bottom: none;
        }

        .finding-item:hover {
            background: var(--glass-white-strong);
            transform: translateX(4px);
        }

        .finding-header {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .finding-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
            flex-shrink: 0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
        }

        .finding-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, transparent 60%);
            border-radius: inherit;
        }

        .finding-icon.critical {
            background: linear-gradient(135deg, var(--danger-red), var(--critical-text));
        }
        .finding-icon.high {
            background: linear-gradient(135deg, var(--warning-amber), var(--high-text));
        }
        .finding-icon.medium {
            background: linear-gradient(135deg, hsl(45, 93%, 47%), var(--medium-text));
        }
        .finding-icon.low {
            background: linear-gradient(135deg, var(--info-cyan), var(--secondary800));
        }

        .finding-content {
            flex: 1;
            min-width: 0;
        }

        .finding-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-on-glass);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            flex-wrap: wrap;
            text-shadow: 0 1px 4px hsla(var(--hue-primary), 90%, 5%, 0.2);
        }

        .control-id {
            background: linear-gradient(135deg, var(--primary500), var(--primary600));
            color: var(--white);
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            box-shadow: 0 2px 4px var(--dark-gray50);
        }

        .finding-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 0.375rem;
            margin-bottom: 1rem;
            font-size: 0.875rem;
            color: var(--text-interactive);
        }

        .meta-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.25rem;
            background: var(--glass-white-light);
            padding: 0.4rem 0.8rem;
            border-radius: 0.5rem;
            border: 1px solid var(--glass-border);
            color: var(--text-accent);
            font-size: 0.8125rem;
            font-weight: 500;
            min-height: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            white-space: nowrap;
            overflow: visible;
        }

        /* Line Number Highlighting - Base Styles */
        .meta-item.line-number {
            color: var(--text-on-dark);
            font-weight: 600;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .meta-item.line-number::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            border-radius: inherit;
        }

        /* Severity-Specific Line Number Colors */
        .finding-item[data-severity="critical"] .meta-item.line-number {
            background: linear-gradient(135deg, var(--danger-red), var(--critical-text));
            border: 2px solid var(--danger-red);
            box-shadow: 0 2px 8px hsla(0, 84%, 60%, 0.4);
            border-radius: 0.5rem;
        }

        .finding-item[data-severity="high"] .meta-item.line-number {
            background: linear-gradient(135deg, var(--warning-amber), var(--high-text));
            border: 2px solid var(--warning-amber);
            box-shadow: 0 2px 8px hsla(38, 92%, 50%, 0.4);
            border-radius: 0.5rem;
        }

        .finding-item[data-severity="medium"] .meta-item.line-number {
            background: linear-gradient(135deg, hsl(45, 93%, 47%), var(--medium-text));
            border: 2px solid hsl(45, 93%, 47%);
            box-shadow: 0 2px 8px hsla(45, 93%, 47%, 0.4);
            border-radius: 0.5rem;
        }

        .finding-item[data-severity="low"] .meta-item.line-number {
            background: linear-gradient(135deg, var(--info-cyan), var(--secondary800));
            border: 2px solid var(--info-cyan);
            box-shadow: 0 2px 8px hsla(var(--hue-secondary), 90%, 80%, 0.4);
            border-radius: 0.5rem;
        }

        .meta-item.line-number .meta-icon {
            color: var(--text-on-dark);
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
        }

        /* Severity-Specific Line Number Badges */
        .line-number-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.4rem;
            color: var(--text-on-dark);
            padding: 0.4rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.8125rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.03em;
            position: relative;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
            min-height: 2rem;
            min-width: 5rem;
            line-height: 1.2;
            white-space: nowrap;
            overflow: visible;
        }

        /* Critical Severity Line Numbers - Red Theme */
        .finding-item[data-severity="critical"] .line-number-badge {
            background: linear-gradient(135deg, hsl(0, 84%, 70%), hsl(0, 84%, 80%));
            box-shadow: 0 2px 6px hsla(0, 84%, 60%, 0.5);
            border: 1px solid hsl(0, 84%, 85%);
            font-size: 0.8125rem !important;
            font-weight: 700 !important;
            padding: 0.4rem 1rem !important;
            min-width: 5rem !important;
            min-height: 2rem !important;
            white-space: nowrap !important;
            overflow: visible !important;
        }

        /* High Severity Line Numbers - Orange Theme */
        .finding-item[data-severity="high"] .line-number-badge {
            background: linear-gradient(135deg, hsl(38, 92%, 60%), hsl(38, 92%, 70%));
            box-shadow: 0 2px 6px hsla(38, 92%, 50%, 0.5);
            border: 1px solid hsl(38, 92%, 75%);
            font-size: 0.8125rem !important;
            font-weight: 700 !important;
            padding: 0.4rem 1rem !important;
            min-width: 5rem !important;
            min-height: 2rem !important;
            white-space: nowrap !important;
            overflow: visible !important;
        }

        /* Medium Severity Line Numbers - Yellow Theme */
        .finding-item[data-severity="medium"] .line-number-badge {
            background: linear-gradient(135deg, hsl(45, 93%, 57%), hsl(45, 93%, 67%));
            box-shadow: 0 2px 6px hsla(45, 93%, 47%, 0.5);
            border: 1px solid hsl(45, 93%, 72%);
            font-size: 0.8125rem !important;
            font-weight: 700 !important;
            padding: 0.4rem 1rem !important;
            min-width: 5rem !important;
            min-height: 2rem !important;
            white-space: nowrap !important;
            overflow: visible !important;
        }

        /* Low Severity Line Numbers - Teal Theme */
        .finding-item[data-severity="low"] .line-number-badge {
            background: linear-gradient(135deg, hsl(var(--hue-secondary), 90%, 75%), hsl(var(--hue-secondary), 90%, 85%));
            box-shadow: 0 2px 6px hsla(var(--hue-secondary), 90%, 70%, 0.5);
            border: 1px solid hsl(var(--hue-secondary), 90%, 90%);
            font-size: 0.8125rem !important;
            font-weight: 700 !important;
            padding: 0.4rem 1rem !important;
            min-width: 5rem !important;
            min-height: 2rem !important;
            white-space: nowrap !important;
            overflow: visible !important;
        }

        /* Code Preview Button Styling */
        .meta-item.code-preview {
            background: linear-gradient(135deg, var(--primary500), var(--primary600));
            color: var(--text-on-dark);
            font-weight: 600;
            border: 2px solid var(--primary500);
            box-shadow: 0 2px 8px hsla(var(--hue-primary), 90%, 50%, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 0.5rem;
        }

        .meta-item.code-preview:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px hsla(var(--hue-primary), 90%, 50%, 0.5);
            background: linear-gradient(135deg, var(--primary600), var(--primary700));
        }

        .meta-item.code-preview .meta-icon {
            color: var(--text-on-dark);
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
        }

        .code-preview-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.4rem;
            color: var(--text-on-dark);
            padding: 0.4rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.8125rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.03em;
            background: linear-gradient(135deg, hsl(var(--hue-primary), 90%, 60%), hsl(var(--hue-primary), 90%, 70%));
            box-shadow: 0 2px 6px hsla(var(--hue-primary), 90%, 50%, 0.4);
            border: 1px solid hsl(var(--hue-primary), 90%, 75%);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
            min-height: 2rem;
            min-width: 7rem;
            line-height: 1.2;
            white-space: nowrap;
            overflow: visible;
        }

        /* Line Number Tooltip */
        .meta-item.line-number:hover::after {
            content: 'Click to jump to line in code editor';
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--dark-gray100);
            color: var(--text-on-dark);
            padding: 0.5rem 0.75rem;
            border-radius: var(--border-radius-sm);
            font-size: 0.75rem;
            white-space: nowrap;
            box-shadow: var(--glass-shadow-lg);
            border: 1px solid var(--glass-border);
            z-index: 1000;
            margin-bottom: 0.5rem;
            opacity: 0;
            animation: tooltip-fade-in 0.3s ease forwards;
        }

        @keyframes tooltip-fade-in {
            from { opacity: 0; transform: translateX(-50%) translateY(5px); }
            to { opacity: 1; transform: translateX(-50%) translateY(0); }
        }

        /* Subtle Professional Hover Effects */
        .finding-item[data-severity="critical"] .meta-item.line-number:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px hsla(0, 84%, 60%, 0.7);
        }

        .finding-item[data-severity="high"] .meta-item.line-number:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px hsla(38, 92%, 50%, 0.7);
        }

        .finding-item[data-severity="medium"] .meta-item.line-number:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px hsla(45, 93%, 47%, 0.7);
        }

        .finding-item[data-severity="low"] .meta-item.line-number:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px hsla(var(--hue-secondary), 90%, 70%, 0.7);
        }

        /* Line Number Copy Functionality */
        .meta-item.line-number {
            cursor: pointer;
            user-select: none;
        }

        .meta-item.line-number:active {
            transform: scale(0.95);
        }

        /* Professional Static Styling - No Animations */
        .finding-item[data-severity="critical"] .line-number-badge {
            box-shadow: 0 3px 8px hsla(0, 84%, 60%, 0.6);
            border: 2px solid hsl(0, 84%, 85%);
        }

        .finding-item[data-severity="high"] .line-number-badge {
            box-shadow: 0 3px 8px hsla(38, 92%, 50%, 0.6);
            border: 2px solid hsl(38, 92%, 75%);
        }

        .finding-item[data-severity="medium"] .line-number-badge {
            box-shadow: 0 2px 6px hsla(45, 93%, 47%, 0.5);
            border: 2px solid hsl(45, 93%, 72%);
        }

        .finding-item[data-severity="low"] .line-number-badge {
            box-shadow: 0 2px 6px hsla(var(--hue-secondary), 90%, 70%, 0.5);
            border: 2px solid hsl(var(--hue-secondary), 90%, 90%);
        }

        .meta-icon {
            color: var(--text-interactive);
            width: 0.75rem;
            font-size: 0.75rem;
        }

        .finding-description {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .remediation-section {
            background: linear-gradient(135deg, var(--success-green) 0%, #059669 100%);
            color: white;
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            margin-top: 1rem;
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border: 1px solid rgba(16, 185, 129, 0.3);
            box-shadow: var(--glass-shadow);
            position: relative;
            overflow: hidden;
        }

        .remediation-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, transparent 60%);
            border-radius: inherit;
        }

        .remediation-title {
            font-weight: 600;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            position: relative;
            z-index: 2;
            color: var(--text-on-dark);
            text-shadow: 0 1px 2px hsla(var(--hue-primary), 90%, 5%, 0.3);
        }

        .remediation-content {
            font-size: 0.875rem;
            line-height: 1.5;
            opacity: 0.95;
            position: relative;
            z-index: 2;
            color: var(--text-on-dark);
        }

        .code-snippet {
            background: hsla(var(--hue-primary), 90%, 5%, 0.9);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            color: var(--text-accent);
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 0.8125rem;
            line-height: 1.5;
            white-space: pre-wrap;
            margin: 1rem 0;
            overflow-x: auto;
            border: 1px solid var(--glass-border);
            box-shadow: var(--glass-shadow);
        }

        .no-findings {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--text-interactive);
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            box-shadow: var(--glass-shadow);
        }

        .no-findings-icon {
            font-size: 3rem;
            color: var(--text-accent);
            margin-bottom: 1rem;
        }

        .no-findings h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-on-glass);
            margin-bottom: 0.5rem;
        }

        /* Glass UI Footer */
        .report-footer {
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-top: 2rem;
            box-shadow: var(--glass-shadow);
            text-align: center;
        }

        .export-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }

        .export-btn {
            padding: 0.75rem 1.5rem;
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-sm);
            background: linear-gradient(135deg, var(--primary500), var(--primary600));
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            color: var(--white);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.875rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            box-shadow: var(--glass-shadow);
            position: relative;
            overflow: hidden;
        }

        .export-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .export-btn:hover::before {
            left: 100%;
        }

        .export-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--glass-shadow-lg);
        }

        .footer-info {
            color: var(--text-interactive);
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .footer-info strong {
            color: var(--text-on-glass);
        }

        /* Glass UI Responsive Design */

        /* Large Desktop (1200px+) - Enhanced Glass Effects */
        @media (min-width: 1200px) {
            .main-container {
                padding: 3rem 2rem;
            }

            .stats-grid {
                grid-template-columns: repeat(4, 1fr);
            }

            .severity-overview {
                grid-template-columns: repeat(4, 1fr);
            }

            /* Enhanced glass blur for larger screens */
            .report-header,
            .controls-section,
            .summary-section,
            .severity-group,
            .domain-section {
                backdrop-filter: var(--glass-blur-strong);
                -webkit-backdrop-filter: var(--glass-blur-strong);
            }
        }

        /* Desktop (992px - 1199px) */
        @media (min-width: 992px) and (max-width: 1199px) {
            .main-container {
                padding: 2rem 1.5rem;
            }

            .report-title {
                font-size: 2.25rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .severity-overview {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Tablet (768px - 991px) */
        @media (min-width: 768px) and (max-width: 991px) {
            .main-container {
                padding: 1.5rem 1rem;
            }

            .report-header {
                padding: 2rem 1.5rem;
            }

            .report-title {
                font-size: 2rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .report-meta {
                flex-direction: column;
                gap: 0.5rem;
            }

            .controls-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .filter-buttons {
                justify-content: center;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .severity-overview {
                grid-template-columns: repeat(2, 1fr);
            }

            .finding-title {
                font-size: 1rem;
            }

            .finding-meta {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        /* Mobile Large (576px - 767px) */
        @media (min-width: 576px) and (max-width: 767px) {
            .main-container {
                padding: 1rem 0.75rem;
            }

            .report-header {
                padding: 1.5rem 1rem;
            }

            .report-title {
                font-size: 1.75rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .report-subtitle {
                font-size: 1rem;
            }

            .report-meta {
                flex-direction: column;
                gap: 0.25rem;
                font-size: 0.8125rem;
            }

            .controls-section {
                padding: 1rem;
            }

            .controls-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .filter-buttons {
                justify-content: center;
                gap: 0.375rem;
            }

            .filter-btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.6875rem;
            }

            .summary-section {
                padding: 1.5rem 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .severity-overview {
                grid-template-columns: 1fr;
            }

            .finding-item {
                padding: 1rem;
            }

            .finding-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.75rem;
            }

            .finding-title {
                font-size: 0.9375rem;
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .export-actions {
                flex-direction: column;
                align-items: center;
            }
        }

        /* Mobile Small (up to 575px) */
        @media (max-width: 575px) {
            .main-container {
                padding: 0.75rem 0.5rem;
            }

            .report-header {
                padding: 1.25rem 0.75rem;
                margin-bottom: 1rem;
            }

            .report-title {
                font-size: 1.5rem;
                flex-direction: column;
                gap: 0.375rem;
            }

            .report-subtitle {
                font-size: 0.9375rem;
            }

            .report-meta {
                flex-direction: column;
                gap: 0.25rem;
                font-size: 0.75rem;
            }

            .controls-section {
                padding: 0.75rem;
            }

            .search-input {
                font-size: 1rem; /* Prevents zoom on iOS */
                padding: 0.75rem 1rem 0.75rem 2.25rem;
            }

            .filter-buttons {
                gap: 0.25rem;
            }

            .filter-btn {
                padding: 0.375rem 0.625rem;
                font-size: 0.625rem;
                min-width: auto;
            }

            .summary-section {
                padding: 1.25rem 0.75rem;
            }

            .summary-title {
                font-size: 1.25rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            .stat-card {
                padding: 1rem;
            }

            .stat-number {
                font-size: 2rem;
            }

            .severity-overview {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .severity-header {
                padding: 1rem;
                font-size: 1rem;
            }

            .finding-item {
                padding: 0.75rem;
            }

            .finding-icon {
                width: 2rem;
                height: 2rem;
                font-size: 0.875rem;
            }

            .finding-title {
                font-size: 0.875rem;
            }

            .control-id {
                font-size: 0.6875rem;
                padding: 0.1875rem 0.5rem;
            }

            .finding-meta {
                font-size: 0.8125rem;
            }

            .finding-description {
                font-size: 0.875rem;
            }

            .code-snippet {
                font-size: 0.75rem;
                padding: 0.75rem;
            }

            .export-actions {
                flex-direction: column;
                gap: 0.5rem;
            }

            .export-btn {
                width: 100%;
                justify-content: center;
            }
        }

        /* Print Styles */
        @media print {
            body {
                background: white !important;
            }

            .main-container {
                max-width: none;
                padding: 0;
            }

            .report-header,
            .controls-section,
            .summary-section,
            .severity-group,
            .report-footer {
                box-shadow: none !important;
                break-inside: avoid;
            }

            .controls-section,
            .export-actions {
                display: none !important;
            }

            .findings-list {
                max-height: none !important;
            }
        }
    </style>

    <script>
        // Modern JavaScript for enhanced interactivity with multi-select filtering
        let searchTimeout;
        let allFindings = [];
        let activeFilters = new Set(['all']); // Support multiple active filters

        document.addEventListener('DOMContentLoaded', function() {
            initializeReport();
            setupEventListeners();
            loadFindings();
        });

        function initializeReport() {
            // Initialize filter buttons with multi-select support
            const filterButtons = document.querySelectorAll('.filter-btn');
            filterButtons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    // Allow Ctrl/Cmd + click for multi-select
                    const isMultiSelect = e.ctrlKey || e.metaKey;
                    toggleFilter(this.dataset.severity, isMultiSelect);
                });
            });

            // Initialize search
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.addEventListener('input', debounceSearch);
            }

            // Initialize collapsible sections
            const severityHeaders = document.querySelectorAll('.severity-header');
            severityHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    toggleSeverityGroup(this);
                });
            });

            // Add instructions for multi-select
            addMultiSelectInstructions();
        }

        function addMultiSelectInstructions() {
            const controlsSection = document.querySelector('.controls-section');
            if (controlsSection) {
                const instructions = document.createElement('div');
                instructions.className = 'multi-select-info';
                instructions.innerHTML = `
                    <small style="color: var(--gray-500); font-size: 0.75rem; margin-top: 0.5rem; display: block;">
                        <i class="fas fa-info-circle"></i>
                        Tip: Hold Ctrl/Cmd and click to select multiple severity levels
                    </small>
                `;
                controlsSection.appendChild(instructions);
            }
        }

        function setupEventListeners() {
            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    clearSearch();
                    resetFilters();
                }
                if (e.ctrlKey && e.key === 'f') {
                    e.preventDefault();
                    focusSearch();
                }
            });
        }

        function debounceSearch() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch();
            }, 300);
        }

        function performSearch() {
            const searchTerm = document.querySelector('.search-input').value.toLowerCase();
            applyFilters(searchTerm);
        }

        function toggleFilter(severity, isMultiSelect = false) {
            if (severity === 'all') {
                // If "All" is clicked, reset to show all
                activeFilters.clear();
                activeFilters.add('all');
            } else {
                if (isMultiSelect) {
                    // Multi-select mode
                    if (activeFilters.has('all')) {
                        activeFilters.clear();
                    }

                    if (activeFilters.has(severity)) {
                        activeFilters.delete(severity);
                    } else {
                        activeFilters.add(severity);
                    }

                    // If no filters selected, default to "all"
                    if (activeFilters.size === 0) {
                        activeFilters.add('all');
                    }
                } else {
                    // Single select mode (default behavior)
                    activeFilters.clear();
                    activeFilters.add(severity);
                }
            }

            updateFilterButtons();
            applyFilters();
            updateUrlHash();
        }

        function updateFilterButtons() {
            const filterButtons = document.querySelectorAll('.filter-btn');
            filterButtons.forEach(btn => {
                const severity = btn.dataset.severity;
                const isActive = activeFilters.has(severity);
                btn.classList.toggle('active', isActive);

                // Add visual indicator for multi-select
                if (activeFilters.size > 1 && !activeFilters.has('all')) {
                    btn.style.position = 'relative';
                    if (isActive && !btn.querySelector('.multi-indicator')) {
                        const indicator = document.createElement('span');
                        indicator.className = 'multi-indicator';
                        indicator.innerHTML = '✓';
                        indicator.style.cssText = `
                            position: absolute;
                            top: -2px;
                            right: -2px;
                            background: var(--success-green);
                            color: white;
                            border-radius: 50%;
                            width: 16px;
                            height: 16px;
                            font-size: 10px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-weight: bold;
                        `;
                        btn.appendChild(indicator);
                    }
                } else {
                    // Remove multi-select indicators
                    const indicator = btn.querySelector('.multi-indicator');
                    if (indicator) {
                        indicator.remove();
                    }
                }
            });
        }

        function applyFilters(searchTerm = '') {
            if (!searchTerm) {
                searchTerm = document.querySelector('.search-input').value.toLowerCase();
            }

            const severityGroups = document.querySelectorAll('.severity-group');
            let totalVisibleCount = 0;

            severityGroups.forEach(group => {
                const groupSeverity = group.dataset.severity;
                const findings = group.querySelectorAll('.finding-item');
                let groupVisibleCount = 0;

                // Check if this severity group should be visible
                const severityMatches = activeFilters.has('all') || activeFilters.has(groupSeverity);

                findings.forEach(finding => {
                    const text = finding.textContent.toLowerCase();
                    const searchMatches = searchTerm === '' || text.includes(searchTerm);
                    const isVisible = severityMatches && searchMatches;

                    finding.style.display = isVisible ? 'block' : 'none';
                    if (isVisible) {
                        groupVisibleCount++;
                        totalVisibleCount++;
                    }
                });

                // Show/hide the entire severity group
                group.style.display = groupVisibleCount > 0 ? 'block' : 'none';
            });

            updateNoResultsMessage(totalVisibleCount === 0);
            updateFilterSummary();
        }

        function updateFilterSummary() {
            // Update or create filter summary
            let summary = document.querySelector('.filter-summary');
            if (!summary) {
                summary = document.createElement('div');
                summary.className = 'filter-summary';
                summary.style.cssText = `
                    margin-top: 0.5rem;
                    padding: 0.5rem;
                    background: var(--gray-100);
                    border-radius: var(--border-radius-sm);
                    font-size: 0.8125rem;
                    color: var(--gray-600);
                `;
                document.querySelector('.controls-section').appendChild(summary);
            }

            if (activeFilters.has('all')) {
                summary.textContent = 'Showing all severity levels';
            } else {
                const filterList = Array.from(activeFilters).map(f => f.charAt(0).toUpperCase() + f.slice(1)).join(', ');
                summary.textContent = `Showing: ${filterList} severity levels`;
            }
        }

        function resetFilters() {
            activeFilters.clear();
            activeFilters.add('all');
            updateFilterButtons();
            applyFilters();
            updateUrlHash();
        }

        function updateUrlHash() {
            const params = new URLSearchParams();
            if (!activeFilters.has('all')) {
                params.set('filters', Array.from(activeFilters).join(','));
            }
            const searchTerm = document.querySelector('.search-input').value;
            if (searchTerm) {
                params.set('search', searchTerm);
            }

            const hash = params.toString();
            if (hash) {
                window.location.hash = hash;
            } else {
                window.history.replaceState(null, null, window.location.pathname);
            }
        }

        function loadFromUrlHash() {
            const hash = window.location.hash.substring(1);
            if (hash) {
                const params = new URLSearchParams(hash);
                const filters = params.get('filters');
                const search = params.get('search');

                if (filters) {
                    activeFilters.clear();
                    filters.split(',').forEach(filter => activeFilters.add(filter.trim()));
                    updateFilterButtons();
                }

                if (search) {
                    document.querySelector('.search-input').value = search;
                }

                applyFilters();
            }
        }

        function toggleSeverityGroup(header) {
            const group = header.parentElement;
            const findingsList = group.querySelector('.findings-list');
            const isCollapsed = header.classList.contains('collapsed');

            if (isCollapsed) {
                header.classList.remove('collapsed');
                findingsList.classList.remove('collapsed');
                findingsList.style.maxHeight = findingsList.scrollHeight + 'px';
            } else {
                header.classList.add('collapsed');
                findingsList.classList.add('collapsed');
                findingsList.style.maxHeight = '0';
            }
        }

        function clearSearch() {
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.value = '';
                applyFilters();
            }
        }

        function focusSearch() {
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }

        function updateNoResultsMessage(show) {
            const noResults = document.querySelector('.no-findings');
            if (noResults) {
                noResults.style.display = show ? 'block' : 'none';
                if (show) {
                    // Update message based on active filters
                    const message = noResults.querySelector('p');
                    if (activeFilters.has('all')) {
                        message.textContent = 'Try adjusting your search terms';
                    } else {
                        const filterList = Array.from(activeFilters).join(', ');
                        message.textContent = `No ${filterList} severity findings match your search. Try different filters or search terms.`;
                    }
                }
            }
        }

        function exportToJson() {
            // Get currently visible findings for export
            const visibleFindings = [];
            document.querySelectorAll('.finding-item').forEach(finding => {
                if (finding.style.display !== 'none') {
                    const severityGroup = finding.closest('.severity-group');
                    const severity = severityGroup ? severityGroup.dataset.severity.toUpperCase() : 'UNKNOWN';
                    const controlId = finding.querySelector('.control-id')?.textContent || 'UNKNOWN';
                    const description = finding.querySelector('.finding-description')?.textContent || '';
                    const remediation = finding.querySelector('.remediation-content')?.textContent || '';
                    const filePath = finding.querySelector('.meta-item:first-child span')?.textContent || '';
                    const lineText = finding.querySelector('.meta-item:last-child span')?.textContent || '';
                    const line = lineText.replace('Line ', '') || '0';

                    visibleFindings.push({
                        control_id: controlId,
                        severity: severity,
                        file_path: filePath,
                        line: parseInt(line) || 0,
                        description: description.trim(),
                        remediation: remediation.trim()
                    });
                }
            });

            const data = {
                timestamp: new Date().toISOString(),
                filters_applied: Array.from(activeFilters),
                total_findings: visibleFindings.length,
                findings: visibleFindings,
                summary: {
                    critical: visibleFindings.filter(f => f.severity === 'CRITICAL').length,
                    high: visibleFindings.filter(f => f.severity === 'HIGH').length,
                    medium: visibleFindings.filter(f => f.severity === 'MEDIUM').length,
                    low: visibleFindings.filter(f => f.severity === 'LOW').length
                }
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], {
                type: 'application/json'
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `security-findings-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function loadFindings() {
            // Initialize findings data - this would be populated with actual findings
            allFindings = [];
        }

        // Glass UI Enhancement Functions
        function addGlassEffects() {
            // Add parallax scrolling effect to background elements
            window.addEventListener('scroll', function() {
                const scrolled = window.pageYOffset;
                const parallax = document.querySelector('body::before');
                if (parallax) {
                    parallax.style.transform = `translateY(${scrolled * 0.5}px)`;
                }
            });

            // Add hover effects to glass cards
            document.querySelectorAll('.stat-card, .severity-badge, .finding-item').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px) scale(1.02)';
                    this.style.boxShadow = 'var(--glass-shadow-xl)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                    this.style.boxShadow = 'var(--glass-shadow)';
                });
            });

            // Add glass ripple effect to buttons
            document.querySelectorAll('.filter-btn, .export-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple');

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        }

        // Line Number Interaction Functions
        function initLineNumberHighlighting() {
            document.querySelectorAll('.meta-item.line-number').forEach(lineItem => {
                lineItem.addEventListener('click', function() {
                    const lineText = this.querySelector('.line-number-badge').textContent;
                    const lineNumber = lineText.replace(/[^0-9]/g, '');
                    const fileName = this.closest('.finding-item').querySelector('.meta-item:first-child span').textContent;

                    // Copy line reference to clipboard
                    const lineReference = `${fileName}:${lineNumber}`;
                    navigator.clipboard.writeText(lineReference).then(() => {
                        showLineNumberFeedback(this, 'Copied to clipboard!');
                    }).catch(() => {
                        showLineNumberFeedback(this, 'Line: ' + lineNumber);
                    });
                });

                // Add hover effect for line numbers
                lineItem.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05) translateY(-2px)';
                });

                lineItem.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1) translateY(0)';
                });
            });
        }

        function showLineNumberFeedback(element, message) {
            const feedback = document.createElement('div');
            feedback.textContent = message;
            feedback.style.cssText = `
                position: absolute;
                top: -40px;
                left: 50%;
                transform: translateX(-50%);
                background: var(--success-green);
                color: white;
                padding: 0.5rem 1rem;
                border-radius: var(--border-radius-sm);
                font-size: 0.75rem;
                font-weight: 600;
                box-shadow: var(--glass-shadow-lg);
                z-index: 1001;
                animation: feedback-bounce 2s ease forwards;
                pointer-events: none;
            `;

            element.style.position = 'relative';
            element.appendChild(feedback);

            setTimeout(() => {
                if (feedback.parentNode) {
                    feedback.parentNode.removeChild(feedback);
                }
            }, 2000);
        }

        // Code Dialog Functions
        function showCodeDialog(filePath, lineNumber, controlId, severity) {
            // Create dialog if it doesn't exist
            let dialog = document.getElementById('code-dialog-overlay');
            if (!dialog) {
                createCodeDialog();
                dialog = document.getElementById('code-dialog-overlay');
            }

            // Update dialog content
            updateCodeDialog(filePath, lineNumber, controlId, severity);

            // Show dialog
            dialog.style.display = 'flex';
            document.body.style.overflow = 'hidden';

            // Focus trap
            dialog.querySelector('.code-dialog-close').focus();
        }

        function createCodeDialog() {
            const dialogHTML = `
                <div id="code-dialog-overlay" class="code-dialog-overlay" onclick="closeCodeDialog(event)">
                    <div class="code-dialog" onclick="event.stopPropagation()">
                        <div class="code-dialog-header">
                            <div class="code-dialog-title">
                                <i class="fas fa-file-code"></i>
                                <span id="dialog-title">Code Snippet</span>
                            </div>
                            <button class="code-dialog-close" onclick="closeCodeDialog()" title="Close dialog">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="code-dialog-content">
                            <div class="code-snippet-container">
                                <div class="code-snippet-with-lines">
                                    <div class="line-numbers" id="line-numbers"></div>
                                    <div class="code-content" id="code-content"></div>
                                </div>
                            </div>
                        </div>
                        <div class="code-dialog-footer">
                            <div class="code-dialog-info">
                                <span id="dialog-file-info"></span>
                                <span id="dialog-severity-info"></span>
                            </div>
                            <div class="code-dialog-actions">
                                <button class="code-dialog-btn" onclick="copyCodeSnippet()" title="Copy code to clipboard">
                                    <i class="fas fa-copy"></i>
                                    Copy Code
                                </button>
                                <button class="code-dialog-btn primary" onclick="closeCodeDialog()">
                                    <i class="fas fa-check"></i>
                                    Close
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', dialogHTML);

            // Add keyboard event listener
            document.addEventListener('keydown', handleDialogKeydown);
        }

        function updateCodeDialog(filePath, lineNumber, controlId, severity) {
            // Update title and info
            document.getElementById('dialog-title').textContent = `${controlId} - Code Snippet`;
            document.getElementById('dialog-file-info').innerHTML = `<i class="fas fa-file"></i> ${filePath}`;
            document.getElementById('dialog-severity-info').innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${severity.toUpperCase()} Severity`;

            // Fetch real file content with ±100 lines context
            fetchRealFileContentWithContext(filePath, lineNumber).then(content => {
                if (content.success) {
                    displayRealCodeWithLineNumbers(content, lineNumber);
                    console.log('✅ Successfully displayed real file content');
                } else {
                    // Fallback to sample code if real content unavailable
                    const sampleCode = generateSampleCode(filePath, lineNumber, severity);
                    displayCodeWithLineNumbers(sampleCode, lineNumber);

                    // Show detailed warning about fallback content
                    const errorMsg = content.error || 'File content not embedded in report';
                    showContentWarning(`Real file content unavailable: ${errorMsg}. Showing representative example.`);
                    console.warn('⚠️ Using fallback sample code:', errorMsg);
                }
            }).catch(error => {
                console.error('❌ Failed to fetch real file content:', error);
                const sampleCode = generateSampleCode(filePath, lineNumber, severity);
                displayCodeWithLineNumbers(sampleCode, lineNumber);
                showContentWarning(`Error loading file content: ${error.message || error}. Showing representative example.`);
            });
        }

        async function fetchRealFileContentWithContext(filePath, lineNumber) {
            try {
                console.log('🔍 Fetching real file content with context for:', filePath, 'line:', lineNumber);

                // Create finding ID to match embedded context data - try multiple normalization approaches
                const findingId = `${filePath}:${lineNumber}`;

                // Try different normalization patterns that might be used during embedding
                const normalizedIds = [
                    // Original approach
                    findingId.split('\\').join('/').split(':').join('_').split('/').join('_'),
                    // Alternative approaches
                    findingId.replace(/\\\\/g, '/').replace(/:/g, '_').replace(/\//g, '_'),
                    findingId.replace(/\\\\/g, '_').replace(/:/g, '_').replace(/\//g, '_'),
                    // Keep original separators
                    findingId.replace(/\\\\/g, '/'),
                    // Just replace backslashes
                    findingId.split('\\').join('/'),
                    // URL-encoded version
                    encodeURIComponent(findingId),
                    // Base64 encoded version
                    btoa(findingId).replace(/[+\/=]/g, '_')
                ];

                console.log('🔧 Trying normalized IDs:', normalizedIds);

                // Try to find embedded context data with any of the normalized IDs
                let contextElement = null;
                let usedId = null;

                for (const normalizedId of normalizedIds) {
                    contextElement = document.querySelector(`[data-finding-context="${normalizedId}"]`);
                    if (contextElement) {
                        usedId = normalizedId;
                        break;
                    }
                }

                console.log('🎯 Found embedded context element:', !!contextElement, 'using ID:', usedId);

                if (contextElement) {
                    const contextJson = contextElement.textContent || contextElement.innerText;
                    console.log('📝 Context JSON length:', contextJson.length);

                    try {
                        const contextData = JSON.parse(contextJson);
                        console.log('✅ Parsed context data successfully:', contextData.success);

                        if (contextData.success) {
                            console.log('📊 Context info:', {
                                total_lines: contextData.total_lines,
                                start_line: contextData.start_line,
                                end_line: contextData.end_line,
                                content_lines_count: contextData.content_lines?.length
                            });
                            return contextData;
                        }
                    } catch (parseError) {
                        console.error('❌ Error parsing context JSON:', parseError);
                    }
                }

                console.log('⚠️ No embedded context found, trying fallback approaches...');

                // Enhanced fallback: try multiple path normalization approaches
                const pathVariations = [
                    filePath,
                    filePath.split('\\').join('/'),
                    filePath.replace(/\\\\/g, '/'),
                    filePath.replace(/\//g, '\\\\'),
                    // Try relative paths
                    filePath.split('/').pop(),
                    filePath.split('\\').pop()
                ];

                const allEmbeddedFiles = document.querySelectorAll('[data-file-path]');
                console.log('📄 Available embedded files:', Array.from(allEmbeddedFiles).map(el => el.getAttribute('data-file-path')));

                for (const pathVariation of pathVariations) {
                    for (const element of allEmbeddedFiles) {
                        const embeddedPath = element.getAttribute('data-file-path');
                        if (embeddedPath === pathVariation || embeddedPath.endsWith(pathVariation) || pathVariation.endsWith(embeddedPath)) {
                            const content = element.textContent || element.innerText;
                            if (content && content.trim()) {
                                const result = parseFileContentWithAccurateLines(content, lineNumber, 100);
                                console.log('✅ Used fallback file content with path variation:', pathVariation);
                                return result;
                            }
                        }
                    }
                }

                console.log('❌ No content available from any source');
                return { success: false, error: 'File content not available - no embedded data found' };

            } catch (error) {
                console.error('❌ Error fetching file content with context:', error);
                return { success: false, error: error.message };
            }
        }

        function parseFileContentWithAccurateLines(content, targetLine, contextLines) {
            // Split content into lines (preserving empty lines)
            const lines = content.split('\n');
            const totalLines = lines.length;
            const targetLineNum = parseInt(targetLine);

            // Validate target line
            if (targetLineNum < 1 || targetLineNum > totalLines) {
                return {
                    success: false,
                    error: `Line ${targetLineNum} out of range (1-${totalLines})`
                };
            }

            // Calculate context range with ±100 lines
            const startLine = Math.max(1, targetLineNum - contextLines);
            const endLine = Math.min(totalLines, targetLineNum + contextLines);

            // Build content lines with accurate numbering
            const contentLines = [];
            for (let lineNum = startLine; lineNum <= endLine; lineNum++) {
                const lineIndex = lineNum - 1; // Convert to 0-based index
                contentLines.push({
                    number: lineNum,
                    content: lines[lineIndex] || '',
                    highlighted: lineNum === targetLineNum
                });
            }

            return {
                success: true,
                file_path: filePath,
                total_lines: totalLines,
                highlighted_line: targetLineNum,
                start_line: startLine,
                end_line: endLine,
                context_size: contextLines,
                content_lines: contentLines
            };
        }

        function displayRealCodeWithLineNumbers(fileData, highlightLine) {
            const lineNumbersEl = document.getElementById('line-numbers');
            const codeContentEl = document.getElementById('code-content');

            let lineNumbersHTML = '';
            let codeHTML = '';

            // Add context information at the top
            if (fileData.start_line && fileData.end_line) {
                const contextInfo = `Showing lines ${fileData.start_line}-${fileData.end_line} of ${fileData.total_lines} (±${fileData.context_size || 100} lines around line ${fileData.line_number || highlightLine})`;
                codeHTML += `<div class="context-info">${contextInfo}</div>\n`;
                lineNumbersHTML += `<div class="context-info-spacer"></div>\n`;
            }

            // Handle both old and new data formats
            const contentLines = fileData.content_lines || [];
            const targetLine = fileData.line_number || fileData.highlighted_line || highlightLine;

            contentLines.forEach(lineInfo => {
                const isHighlighted = lineInfo.highlighted || lineInfo.number === targetLine;

                if (isHighlighted) {
                    lineNumbersHTML += `<div class="highlighted-line-number">${lineInfo.number}</div>\n`;
                    codeHTML += `<div class="highlighted-line">${escapeHtml(lineInfo.content)} <span class="security-marker">← Security Issue</span></div>\n`;
                } else {
                    lineNumbersHTML += `<div>${lineInfo.number}</div>\n`;
                    codeHTML += `<div>${escapeHtml(lineInfo.content)}</div>\n`;
                }
            });

            lineNumbersEl.innerHTML = lineNumbersHTML;
            codeContentEl.innerHTML = codeHTML;

            // Scroll to highlighted line with better positioning
            setTimeout(() => {
                const highlightedLine = codeContentEl.querySelector('.highlighted-line');
                if (highlightedLine) {
                    // Scroll to center the highlighted line
                    const container = document.querySelector('.code-snippet-container');
                    if (container) {
                        const containerHeight = container.clientHeight;
                        const lineTop = highlightedLine.offsetTop;
                        const lineHeight = highlightedLine.clientHeight;

                        // Calculate scroll position to center the line
                        const scrollTop = lineTop - (containerHeight / 2) + (lineHeight / 2);
                        container.scrollTop = Math.max(0, scrollTop);
                    }
                }
            }, 100);

            // Update dialog info with comprehensive file stats
            const dialogInfo = document.querySelector('.code-dialog-info');
            if (dialogInfo && fileData.total_lines) {
                const contextSize = fileData.context_size || 100;
                const linesShown = contentLines.length;
                const filePath = fileData.file_path || 'Unknown file';
                dialogInfo.innerHTML = `
                    <span><i class="fas fa-file"></i> ${filePath}</span>
                    <span><i class="fas fa-list-ol"></i> ${fileData.total_lines} total lines</span>
                    <span><i class="fas fa-eye"></i> Showing ${linesShown} lines (±${contextSize})</span>
                    <span><i class="fas fa-crosshairs"></i> Line ${targetLine} highlighted</span>
                `;
            }
        }

        function showContentWarning(message) {
            console.log('⚠️ Showing content warning:', message);
            const dialogContent = document.querySelector('.code-dialog-content');

            // Remove existing warning
            const existingWarning = dialogContent.querySelector('.content-warning');
            if (existingWarning) {
                existingWarning.remove();
            }

            // Add new warning with less prominent styling
            const warning = document.createElement('div');
            warning.className = 'content-warning';
            warning.innerHTML = `
                <div style="
                    background: linear-gradient(135deg, hsl(45, 93%, 47%), hsl(45, 93%, 40%));
                    color: var(--text-on-dark);
                    padding: 0.5rem 1rem;
                    margin: 0;
                    font-size: 0.8125rem;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    border-bottom: 1px solid var(--glass-border);
                    font-weight: 500;
                    opacity: 0.95;
                ">
                    <i class="fas fa-info-circle" style="font-size: 0.875rem;"></i>
                    <span>ℹ️ SAMPLE CONTENT: ${message}</span>
                </div>
            `;

            dialogContent.insertBefore(warning, dialogContent.firstChild);
        }

        function generateSampleCode(filePath, lineNumber, severity) {
            // Fallback sample code generator when real content is unavailable
            const fileExtension = filePath.split('.').pop().toLowerCase();

            if (fileExtension === 'bicep') {
                return `// Azure Bicep Template
param location string = resourceGroup().location
param storageAccountName string

resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {
  name: storageAccountName
  location: location
  kind: 'StorageV2'
  sku: {
    name: 'Standard_LRS'  // ← Security issue on this line
  }
  properties: {
    allowBlobPublicAccess: true  // ← Potential security risk
    minimumTlsVersion: 'TLS1_0'  // ← Outdated TLS version
    supportsHttpsTrafficOnly: false
  }
}

output storageAccountId string = storageAccount.id`;
            } else if (fileExtension === 'tf') {
                return `# Terraform Configuration
resource "azurerm_storage_account" "example" {
  name                     = var.storage_account_name
  resource_group_name      = azurerm_resource_group.example.name
  location                 = azurerm_resource_group.example.location
  account_tier             = "Standard"
  account_replication_type = "LRS"

  # Security Configuration Issues
  allow_blob_public_access = true    # ← Line ${lineNumber}: Public access enabled
  min_tls_version         = "TLS1_0" # ← Outdated TLS version
  https_traffic_only      = false    # ← HTTP traffic allowed

  tags = {
    Environment = "Development"
  }
}`;
            } else {
                return `// Generic code example
function processData(input) {
    // Security issue: No input validation
    const result = eval(input);  // ← Line ${lineNumber}: Dangerous eval usage

    // Additional security concerns
    localStorage.setItem('data', result);  // ← Storing sensitive data
    console.log('Debug info:', result);    // ← Information disclosure

    return result;
}

// Usage example
const userInput = getUrlParameter('code');
const output = processData(userInput);`;
            }
        }

        function displayCodeWithLineNumbers(code, highlightLine) {
            const lines = code.split('\n');
            const lineNumbersEl = document.getElementById('line-numbers');
            const codeContentEl = document.getElementById('code-content');

            // Generate line numbers
            let lineNumbersHTML = '';
            let codeHTML = '';

            lines.forEach((line, index) => {
                const lineNum = index + 1;
                const isHighlighted = lineNum === parseInt(highlightLine);

                if (isHighlighted) {
                    lineNumbersHTML += `<div class="highlighted-line-number">${lineNum}</div>\n`;
                    codeHTML += `<div class="highlighted-line">${escapeHtml(line)}</div>\n`;
                } else {
                    lineNumbersHTML += `<div>${lineNum}</div>\n`;
                    codeHTML += `<div>${escapeHtml(line)}</div>\n`;
                }
            });

            lineNumbersEl.innerHTML = lineNumbersHTML;
            codeContentEl.innerHTML = codeHTML;

            // Scroll to highlighted line
            setTimeout(() => {
                const highlightedLine = codeContentEl.querySelector('.highlighted-line');
                if (highlightedLine) {
                    highlightedLine.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }, 100);
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function copyCodeSnippet() {
            const codeContent = document.getElementById('code-content').textContent;
            navigator.clipboard.writeText(codeContent).then(() => {
                const btn = event.target.closest('.code-dialog-btn');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
                btn.style.background = 'var(--success-green)';

                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.style.background = '';
                }, 2000);
            }).catch(() => {
                alert('Failed to copy code to clipboard');
            });
        }

        function closeCodeDialog(event) {
            if (event && event.target !== event.currentTarget) return;

            const dialog = document.getElementById('code-dialog-overlay');
            if (dialog) {
                dialog.style.display = 'none';
                document.body.style.overflow = '';
            }
        }

        function handleDialogKeydown(event) {
            if (event.key === 'Escape') {
                closeCodeDialog();
            }
        }

        // Initialize Glass UI effects
        function initGlassUI() {
            addGlassEffects();
            initLineNumberHighlighting();

            // Add smooth scrolling
            document.documentElement.style.scrollBehavior = 'smooth';

            // Add intersection observer for fade-in animations
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, { threshold: 0.1 });

            document.querySelectorAll('.severity-group, .stat-card').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(el);
            });
        }

        // Initialize from URL hash on page load
        window.addEventListener('load', function() {
            loadFromUrlHash();
            initGlassUI();
        });

        // Handle browser back/forward
        window.addEventListener('hashchange', function() {
            loadFromUrlHash();
        });

        // Copy code snippet function
        function copyCodeSnippet(button) {
            const codeSection = button.closest('.code-snippet-section');
            const codeLines = codeSection.querySelectorAll('.code-line, .highlighted-code-line');

            let codeText = '';
            codeLines.forEach(line => {
                const lineNumber = line.querySelector('.line-number').textContent.trim();
                const lineContent = line.querySelector('.line-content').textContent;
                codeText += `${lineNumber.padStart(4, ' ')}: ${lineContent}\n`;
            });

            navigator.clipboard.writeText(codeText).then(() => {
                // Show success feedback
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i> Copied!';
                button.style.background = 'var(--success-green)';

                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.style.background = 'var(--primary500)';
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy code:', err);
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = codeText;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i> Copied!';
                button.style.background = 'var(--success-green)';

                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.style.background = 'var(--primary500)';
                }, 2000);
            });
        }
    </script>

    <!-- Embedded File Content for Code Dialog -->
    
                <script type="application/json" data-finding-context="template.json_61" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;template.json&quot;, &quot;line_number&quot;: 61, &quot;total_lines&quot;: 234, &quot;start_line&quot;: 1, &quot;end_line&quot;: 161, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;{&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;    \&quot;$schema\&quot;: \&quot;https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;    \&quot;contentVersion\&quot;: \&quot;*******\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;    \&quot;parameters\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;        \&quot;sites_onefuzz_daily_ui_name\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;            \&quot;defaultValue\&quot;: \&quot;onefuzz-daily-ui\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;            \&quot;type\&quot;: \&quot;String\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;        },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;        \&quot;serverfarms_SP_DEV_WEB_WESTUS_externalid\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;            \&quot;defaultValue\&quot;: \&quot;/subscriptions/82e97bef-45a0-4ed7-ac69-82c0501a15dc/resourceGroups/onefuzz-daily-ui/providers/Microsoft.Web/serverfarms/SP-DEV-WEB-WESTUS\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;            \&quot;type\&quot;: \&quot;String\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;    \&quot;variables\&quot;: {},&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;    \&quot;resources\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;        {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;            \&quot;type\&quot;: \&quot;Microsoft.Web/sites\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;            \&quot;apiVersion\&quot;: \&quot;2024-04-01\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;            \&quot;name\&quot;: \&quot;[parameters('sites_onefuzz_daily_ui_name')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;            \&quot;location\&quot;: \&quot;West US 2\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;            \&quot;tags\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;                \&quot;hidden-related:/subscriptions/82e97bef-45a0-4ed7-ac69-82c0501a15dc/resourceGroups/onefuzz-daily-ui/providers/Microsoft.Web/serverfarms/SP-DEV-WEB-WESTUS\&quot;: \&quot;empty\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;            },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;            \&quot;kind\&quot;: \&quot;app\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;            \&quot;identity\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;                \&quot;type\&quot;: \&quot;SystemAssigned\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;            },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;            \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;                \&quot;enabled\&quot;: true,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;                \&quot;hostNameSslStates\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;                    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;                        \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '.azurewebsites.net')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;                        \&quot;sslState\&quot;: \&quot;Disabled\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;                        \&quot;hostType\&quot;: \&quot;Standard\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;                    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;                    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;                        \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '.microsoft.com')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;                        \&quot;sslState\&quot;: \&quot;SniEnabled\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;                        \&quot;thumbprint\&quot;: \&quot;3A511380E775FC0057C96B50046A338343912F5C\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;                        \&quot;hostType\&quot;: \&quot;Standard\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;                    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;                    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;                        \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '.scm.azurewebsites.net')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 44, &quot;content&quot;: &quot;                        \&quot;sslState\&quot;: \&quot;Disabled\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 45, &quot;content&quot;: &quot;                        \&quot;hostType\&quot;: \&quot;Repository\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 46, &quot;content&quot;: &quot;                    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 47, &quot;content&quot;: &quot;                ],&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 48, &quot;content&quot;: &quot;                \&quot;serverFarmId\&quot;: \&quot;[parameters('serverfarms_SP_DEV_WEB_WESTUS_externalid')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 49, &quot;content&quot;: &quot;                \&quot;reserved\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 50, &quot;content&quot;: &quot;                \&quot;isXenon\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 51, &quot;content&quot;: &quot;                \&quot;hyperV\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 52, &quot;content&quot;: &quot;                \&quot;dnsConfiguration\&quot;: {},&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 53, &quot;content&quot;: &quot;                \&quot;vnetRouteAllEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 54, &quot;content&quot;: &quot;                \&quot;vnetImagePullEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 55, &quot;content&quot;: &quot;                \&quot;vnetContentShareEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 56, &quot;content&quot;: &quot;                \&quot;siteConfig\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 57, &quot;content&quot;: &quot;                    \&quot;numberOfWorkers\&quot;: 1,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 58, &quot;content&quot;: &quot;                    \&quot;acrUseManagedIdentityCreds\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 59, &quot;content&quot;: &quot;                    \&quot;alwaysOn\&quot;: true,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 60, &quot;content&quot;: &quot;                    \&quot;http20Enabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 61, &quot;content&quot;: &quot;                    \&quot;functionAppScaleLimit\&quot;: 0,&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 62, &quot;content&quot;: &quot;                    \&quot;minimumElasticInstanceCount\&quot;: 1&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 63, &quot;content&quot;: &quot;                },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 64, &quot;content&quot;: &quot;                \&quot;scmSiteAlsoStopped\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 65, &quot;content&quot;: &quot;                \&quot;clientAffinityEnabled\&quot;: true,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 66, &quot;content&quot;: &quot;                \&quot;clientCertEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 67, &quot;content&quot;: &quot;                \&quot;clientCertMode\&quot;: \&quot;Required\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 68, &quot;content&quot;: &quot;                \&quot;hostNamesDisabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 69, &quot;content&quot;: &quot;                \&quot;ipMode\&quot;: \&quot;IPv4\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 70, &quot;content&quot;: &quot;                \&quot;vnetBackupRestoreEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 71, &quot;content&quot;: &quot;                \&quot;customDomainVerificationId\&quot;: \&quot;413529405199EC9F7D39BBA0DDC7700B623267BC9F3F342F680996FCB5AA1C24\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 72, &quot;content&quot;: &quot;                \&quot;containerSize\&quot;: 0,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 73, &quot;content&quot;: &quot;                \&quot;dailyMemoryTimeQuota\&quot;: 0,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 74, &quot;content&quot;: &quot;                \&quot;httpsOnly\&quot;: true,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 75, &quot;content&quot;: &quot;                \&quot;endToEndEncryptionEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 76, &quot;content&quot;: &quot;                \&quot;redundancyMode\&quot;: \&quot;None\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 77, &quot;content&quot;: &quot;                \&quot;publicNetworkAccess\&quot;: \&quot;Enabled\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 78, &quot;content&quot;: &quot;                \&quot;storageAccountRequired\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 79, &quot;content&quot;: &quot;                \&quot;keyVaultReferenceIdentity\&quot;: \&quot;SystemAssigned\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 80, &quot;content&quot;: &quot;            }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 81, &quot;content&quot;: &quot;        },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 82, &quot;content&quot;: &quot;        {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 83, &quot;content&quot;: &quot;            \&quot;type\&quot;: \&quot;Microsoft.Web/sites/basicPublishingCredentialsPolicies\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 84, &quot;content&quot;: &quot;            \&quot;apiVersion\&quot;: \&quot;2024-04-01\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 85, &quot;content&quot;: &quot;            \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '/ftp')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 86, &quot;content&quot;: &quot;            \&quot;location\&quot;: \&quot;West US 2\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 87, &quot;content&quot;: &quot;            \&quot;dependsOn\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 88, &quot;content&quot;: &quot;                \&quot;[resourceId('Microsoft.Web/sites', parameters('sites_onefuzz_daily_ui_name'))]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 89, &quot;content&quot;: &quot;            ],&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 90, &quot;content&quot;: &quot;            \&quot;tags\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 91, &quot;content&quot;: &quot;                \&quot;hidden-related:/subscriptions/82e97bef-45a0-4ed7-ac69-82c0501a15dc/resourceGroups/onefuzz-daily-ui/providers/Microsoft.Web/serverfarms/SP-DEV-WEB-WESTUS\&quot;: \&quot;empty\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 92, &quot;content&quot;: &quot;            },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 93, &quot;content&quot;: &quot;            \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 94, &quot;content&quot;: &quot;                \&quot;allow\&quot;: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 95, &quot;content&quot;: &quot;            }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 96, &quot;content&quot;: &quot;        },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 97, &quot;content&quot;: &quot;        {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 98, &quot;content&quot;: &quot;            \&quot;type\&quot;: \&quot;Microsoft.Web/sites/basicPublishingCredentialsPolicies\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 99, &quot;content&quot;: &quot;            \&quot;apiVersion\&quot;: \&quot;2024-04-01\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 100, &quot;content&quot;: &quot;            \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '/scm')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 101, &quot;content&quot;: &quot;            \&quot;location\&quot;: \&quot;West US 2\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 102, &quot;content&quot;: &quot;            \&quot;dependsOn\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 103, &quot;content&quot;: &quot;                \&quot;[resourceId('Microsoft.Web/sites', parameters('sites_onefuzz_daily_ui_name'))]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 104, &quot;content&quot;: &quot;            ],&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 105, &quot;content&quot;: &quot;            \&quot;tags\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 106, &quot;content&quot;: &quot;                \&quot;hidden-related:/subscriptions/82e97bef-45a0-4ed7-ac69-82c0501a15dc/resourceGroups/onefuzz-daily-ui/providers/Microsoft.Web/serverfarms/SP-DEV-WEB-WESTUS\&quot;: \&quot;empty\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 107, &quot;content&quot;: &quot;            },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 108, &quot;content&quot;: &quot;            \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 109, &quot;content&quot;: &quot;                \&quot;allow\&quot;: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 110, &quot;content&quot;: &quot;            }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 111, &quot;content&quot;: &quot;        },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 112, &quot;content&quot;: &quot;        {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 113, &quot;content&quot;: &quot;            \&quot;type\&quot;: \&quot;Microsoft.Web/sites/config\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 114, &quot;content&quot;: &quot;            \&quot;apiVersion\&quot;: \&quot;2024-04-01\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 115, &quot;content&quot;: &quot;            \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '/web')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 116, &quot;content&quot;: &quot;            \&quot;location\&quot;: \&quot;West US 2\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 117, &quot;content&quot;: &quot;            \&quot;dependsOn\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 118, &quot;content&quot;: &quot;                \&quot;[resourceId('Microsoft.Web/sites', parameters('sites_onefuzz_daily_ui_name'))]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 119, &quot;content&quot;: &quot;            ],&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 120, &quot;content&quot;: &quot;            \&quot;tags\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 121, &quot;content&quot;: &quot;                \&quot;hidden-related:/subscriptions/82e97bef-45a0-4ed7-ac69-82c0501a15dc/resourceGroups/onefuzz-daily-ui/providers/Microsoft.Web/serverfarms/SP-DEV-WEB-WESTUS\&quot;: \&quot;empty\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 122, &quot;content&quot;: &quot;            },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 123, &quot;content&quot;: &quot;            \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 124, &quot;content&quot;: &quot;                \&quot;numberOfWorkers\&quot;: 1,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 125, &quot;content&quot;: &quot;                \&quot;defaultDocuments\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 126, &quot;content&quot;: &quot;                    \&quot;Default.htm\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 127, &quot;content&quot;: &quot;                    \&quot;Default.html\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 128, &quot;content&quot;: &quot;                    \&quot;Default.asp\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 129, &quot;content&quot;: &quot;                    \&quot;index.htm\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 130, &quot;content&quot;: &quot;                    \&quot;index.html\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 131, &quot;content&quot;: &quot;                    \&quot;iisstart.htm\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 132, &quot;content&quot;: &quot;                    \&quot;default.aspx\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 133, &quot;content&quot;: &quot;                    \&quot;index.php\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 134, &quot;content&quot;: &quot;                    \&quot;hostingstart.html\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 135, &quot;content&quot;: &quot;                ],&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 136, &quot;content&quot;: &quot;                \&quot;netFrameworkVersion\&quot;: \&quot;v4.0\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 137, &quot;content&quot;: &quot;                \&quot;phpVersion\&quot;: \&quot;5.6\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 138, &quot;content&quot;: &quot;                \&quot;nodeVersion\&quot;: \&quot;~20\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 139, &quot;content&quot;: &quot;                \&quot;requestTracingEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 140, &quot;content&quot;: &quot;                \&quot;remoteDebuggingEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 141, &quot;content&quot;: &quot;                \&quot;httpLoggingEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 142, &quot;content&quot;: &quot;                \&quot;acrUseManagedIdentityCreds\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 143, &quot;content&quot;: &quot;                \&quot;logsDirectorySizeLimit\&quot;: 35,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 144, &quot;content&quot;: &quot;                \&quot;detailedErrorLoggingEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 145, &quot;content&quot;: &quot;                \&quot;publishingUsername\&quot;: \&quot;REDACTED\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 146, &quot;content&quot;: &quot;                \&quot;scmType\&quot;: \&quot;None\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 147, &quot;content&quot;: &quot;                \&quot;use32BitWorkerProcess\&quot;: true,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 148, &quot;content&quot;: &quot;                \&quot;webSocketsEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 149, &quot;content&quot;: &quot;                \&quot;alwaysOn\&quot;: true,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 150, &quot;content&quot;: &quot;                \&quot;managedPipelineMode\&quot;: \&quot;Integrated\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 151, &quot;content&quot;: &quot;                \&quot;virtualApplications\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 152, &quot;content&quot;: &quot;                    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 153, &quot;content&quot;: &quot;                        \&quot;virtualPath\&quot;: \&quot;/\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 154, &quot;content&quot;: &quot;                        \&quot;physicalPath\&quot;: \&quot;site\\\\wwwroot\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 155, &quot;content&quot;: &quot;                        \&quot;preloadEnabled\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 156, &quot;content&quot;: &quot;                    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 157, &quot;content&quot;: &quot;                ],&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 158, &quot;content&quot;: &quot;                \&quot;loadBalancing\&quot;: \&quot;LeastRequests\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 159, &quot;content&quot;: &quot;                \&quot;experiments\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 160, &quot;content&quot;: &quot;                    \&quot;rampUpRules\&quot;: []&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 161, &quot;content&quot;: &quot;                },&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: {\n       2:     \&quot;$schema\&quot;: \&quot;https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#\&quot;,\n       3:     \&quot;contentVersion\&quot;: \&quot;*******\&quot;,\n       4:     \&quot;parameters\&quot;: {\n       5:         \&quot;sites_onefuzz_daily_ui_name\&quot;: {\n       6:             \&quot;defaultValue\&quot;: \&quot;onefuzz-daily-ui\&quot;,\n       7:             \&quot;type\&quot;: \&quot;String\&quot;\n       8:         },\n       9:         \&quot;serverfarms_SP_DEV_WEB_WESTUS_externalid\&quot;: {\n      10:             \&quot;defaultValue\&quot;: \&quot;/subscriptions/82e97bef-45a0-4ed7-ac69-82c0501a15dc/resourceGroups/onefuzz-daily-ui/providers/Microsoft.Web/serverfarms/SP-DEV-WEB-WESTUS\&quot;,\n      11:             \&quot;type\&quot;: \&quot;String\&quot;\n      12:         }\n      13:     },\n      14:     \&quot;variables\&quot;: {},\n      15:     \&quot;resources\&quot;: [\n      16:         {\n      17:             \&quot;type\&quot;: \&quot;Microsoft.Web/sites\&quot;,\n      18:             \&quot;apiVersion\&quot;: \&quot;2024-04-01\&quot;,\n      19:             \&quot;name\&quot;: \&quot;[parameters('sites_onefuzz_daily_ui_name')]\&quot;,\n      20:             \&quot;location\&quot;: \&quot;West US 2\&quot;,\n      21:             \&quot;tags\&quot;: {\n      22:                 \&quot;hidden-related:/subscriptions/82e97bef-45a0-4ed7-ac69-82c0501a15dc/resourceGroups/onefuzz-daily-ui/providers/Microsoft.Web/serverfarms/SP-DEV-WEB-WESTUS\&quot;: \&quot;empty\&quot;\n      23:             },\n      24:             \&quot;kind\&quot;: \&quot;app\&quot;,\n      25:             \&quot;identity\&quot;: {\n      26:                 \&quot;type\&quot;: \&quot;SystemAssigned\&quot;\n      27:             },\n      28:             \&quot;properties\&quot;: {\n      29:                 \&quot;enabled\&quot;: true,\n      30:                 \&quot;hostNameSslStates\&quot;: [\n      31:                     {\n      32:                         \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '.azurewebsites.net')]\&quot;,\n      33:                         \&quot;sslState\&quot;: \&quot;Disabled\&quot;,\n      34:                         \&quot;hostType\&quot;: \&quot;Standard\&quot;\n      35:                     },\n      36:                     {\n      37:                         \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '.microsoft.com')]\&quot;,\n      38:                         \&quot;sslState\&quot;: \&quot;SniEnabled\&quot;,\n      39:                         \&quot;thumbprint\&quot;: \&quot;3A511380E775FC0057C96B50046A338343912F5C\&quot;,\n      40:                         \&quot;hostType\&quot;: \&quot;Standard\&quot;\n      41:                     },\n      42:                     {\n      43:                         \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '.scm.azurewebsites.net')]\&quot;,\n      44:                         \&quot;sslState\&quot;: \&quot;Disabled\&quot;,\n      45:                         \&quot;hostType\&quot;: \&quot;Repository\&quot;\n      46:                     }\n      47:                 ],\n      48:                 \&quot;serverFarmId\&quot;: \&quot;[parameters('serverfarms_SP_DEV_WEB_WESTUS_externalid')]\&quot;,\n      49:                 \&quot;reserved\&quot;: false,\n      50:                 \&quot;isXenon\&quot;: false,\n      51:                 \&quot;hyperV\&quot;: false,\n      52:                 \&quot;dnsConfiguration\&quot;: {},\n      53:                 \&quot;vnetRouteAllEnabled\&quot;: false,\n      54:                 \&quot;vnetImagePullEnabled\&quot;: false,\n      55:                 \&quot;vnetContentShareEnabled\&quot;: false,\n      56:                 \&quot;siteConfig\&quot;: {\n      57:                     \&quot;numberOfWorkers\&quot;: 1,\n      58:                     \&quot;acrUseManagedIdentityCreds\&quot;: false,\n      59:                     \&quot;alwaysOn\&quot;: true,\n      60:                     \&quot;http20Enabled\&quot;: false,\n&gt;&gt;&gt;   61:                     \&quot;functionAppScaleLimit\&quot;: 0,\n      62:                     \&quot;minimumElasticInstanceCount\&quot;: 1\n      63:                 },\n      64:                 \&quot;scmSiteAlsoStopped\&quot;: false,\n      65:                 \&quot;clientAffinityEnabled\&quot;: true,\n      66:                 \&quot;clientCertEnabled\&quot;: false,\n      67:                 \&quot;clientCertMode\&quot;: \&quot;Required\&quot;,\n      68:                 \&quot;hostNamesDisabled\&quot;: false,\n      69:                 \&quot;ipMode\&quot;: \&quot;IPv4\&quot;,\n      70:                 \&quot;vnetBackupRestoreEnabled\&quot;: false,\n      71:                 \&quot;customDomainVerificationId\&quot;: \&quot;413529405199EC9F7D39BBA0DDC7700B623267BC9F3F342F680996FCB5AA1C24\&quot;,\n      72:                 \&quot;containerSize\&quot;: 0,\n      73:                 \&quot;dailyMemoryTimeQuota\&quot;: 0,\n      74:                 \&quot;httpsOnly\&quot;: true,\n      75:                 \&quot;endToEndEncryptionEnabled\&quot;: false,\n      76:                 \&quot;redundancyMode\&quot;: \&quot;None\&quot;,\n      77:                 \&quot;publicNetworkAccess\&quot;: \&quot;Enabled\&quot;,\n      78:                 \&quot;storageAccountRequired\&quot;: false,\n      79:                 \&quot;keyVaultReferenceIdentity\&quot;: \&quot;SystemAssigned\&quot;\n      80:             }\n      81:         },\n      82:         {\n      83:             \&quot;type\&quot;: \&quot;Microsoft.Web/sites/basicPublishingCredentialsPolicies\&quot;,\n      84:             \&quot;apiVersion\&quot;: \&quot;2024-04-01\&quot;,\n      85:             \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '/ftp')]\&quot;,\n      86:             \&quot;location\&quot;: \&quot;West US 2\&quot;,\n      87:             \&quot;dependsOn\&quot;: [\n      88:                 \&quot;[resourceId('Microsoft.Web/sites', parameters('sites_onefuzz_daily_ui_name'))]\&quot;\n      89:             ],\n      90:             \&quot;tags\&quot;: {\n      91:                 \&quot;hidden-related:/subscriptions/82e97bef-45a0-4ed7-ac69-82c0501a15dc/resourceGroups/onefuzz-daily-ui/providers/Microsoft.Web/serverfarms/SP-DEV-WEB-WESTUS\&quot;: \&quot;empty\&quot;\n      92:             },\n      93:             \&quot;properties\&quot;: {\n      94:                 \&quot;allow\&quot;: false\n      95:             }\n      96:         },\n      97:         {\n      98:             \&quot;type\&quot;: \&quot;Microsoft.Web/sites/basicPublishingCredentialsPolicies\&quot;,\n      99:             \&quot;apiVersion\&quot;: \&quot;2024-04-01\&quot;,\n     100:             \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '/scm')]\&quot;,\n     101:             \&quot;location\&quot;: \&quot;West US 2\&quot;,\n     102:             \&quot;dependsOn\&quot;: [\n     103:                 \&quot;[resourceId('Microsoft.Web/sites', parameters('sites_onefuzz_daily_ui_name'))]\&quot;\n     104:             ],\n     105:             \&quot;tags\&quot;: {\n     106:                 \&quot;hidden-related:/subscriptions/82e97bef-45a0-4ed7-ac69-82c0501a15dc/resourceGroups/onefuzz-daily-ui/providers/Microsoft.Web/serverfarms/SP-DEV-WEB-WESTUS\&quot;: \&quot;empty\&quot;\n     107:             },\n     108:             \&quot;properties\&quot;: {\n     109:                 \&quot;allow\&quot;: false\n     110:             }\n     111:         },\n     112:         {\n     113:             \&quot;type\&quot;: \&quot;Microsoft.Web/sites/config\&quot;,\n     114:             \&quot;apiVersion\&quot;: \&quot;2024-04-01\&quot;,\n     115:             \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '/web')]\&quot;,\n     116:             \&quot;location\&quot;: \&quot;West US 2\&quot;,\n     117:             \&quot;dependsOn\&quot;: [\n     118:                 \&quot;[resourceId('Microsoft.Web/sites', parameters('sites_onefuzz_daily_ui_name'))]\&quot;\n     119:             ],\n     120:             \&quot;tags\&quot;: {\n     121:                 \&quot;hidden-related:/subscriptions/82e97bef-45a0-4ed7-ac69-82c0501a15dc/resourceGroups/onefuzz-daily-ui/providers/Microsoft.Web/serverfarms/SP-DEV-WEB-WESTUS\&quot;: \&quot;empty\&quot;\n     122:             },\n     123:             \&quot;properties\&quot;: {\n     124:                 \&quot;numberOfWorkers\&quot;: 1,\n     125:                 \&quot;defaultDocuments\&quot;: [\n     126:                     \&quot;Default.htm\&quot;,\n     127:                     \&quot;Default.html\&quot;,\n     128:                     \&quot;Default.asp\&quot;,\n     129:                     \&quot;index.htm\&quot;,\n     130:                     \&quot;index.html\&quot;,\n     131:                     \&quot;iisstart.htm\&quot;,\n     132:                     \&quot;default.aspx\&quot;,\n     133:                     \&quot;index.php\&quot;,\n     134:                     \&quot;hostingstart.html\&quot;\n     135:                 ],\n     136:                 \&quot;netFrameworkVersion\&quot;: \&quot;v4.0\&quot;,\n     137:                 \&quot;phpVersion\&quot;: \&quot;5.6\&quot;,\n     138:                 \&quot;nodeVersion\&quot;: \&quot;~20\&quot;,\n     139:                 \&quot;requestTracingEnabled\&quot;: false,\n     140:                 \&quot;remoteDebuggingEnabled\&quot;: false,\n     141:                 \&quot;httpLoggingEnabled\&quot;: false,\n     142:                 \&quot;acrUseManagedIdentityCreds\&quot;: false,\n     143:                 \&quot;logsDirectorySizeLimit\&quot;: 35,\n     144:                 \&quot;detailedErrorLoggingEnabled\&quot;: false,\n     145:                 \&quot;publishingUsername\&quot;: \&quot;REDACTED\&quot;,\n     146:                 \&quot;scmType\&quot;: \&quot;None\&quot;,\n     147:                 \&quot;use32BitWorkerProcess\&quot;: true,\n     148:                 \&quot;webSocketsEnabled\&quot;: false,\n     149:                 \&quot;alwaysOn\&quot;: true,\n     150:                 \&quot;managedPipelineMode\&quot;: \&quot;Integrated\&quot;,\n     151:                 \&quot;virtualApplications\&quot;: [\n     152:                     {\n     153:                         \&quot;virtualPath\&quot;: \&quot;/\&quot;,\n     154:                         \&quot;physicalPath\&quot;: \&quot;site\\\\wwwroot\&quot;,\n     155:                         \&quot;preloadEnabled\&quot;: true\n     156:                     }\n     157:                 ],\n     158:                 \&quot;loadBalancing\&quot;: \&quot;LeastRequests\&quot;,\n     159:                 \&quot;experiments\&quot;: {\n     160:                     \&quot;rampUpRules\&quot;: []\n     161:                 },&quot;, &quot;highlighted_line_content&quot;: &quot;\&quot;functionAppScaleLimit\&quot;: 0,&quot;}
                </script>
                <script type="application/json" data-finding-context="template.json_191" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;template.json&quot;, &quot;line_number&quot;: 191, &quot;total_lines&quot;: 234, &quot;start_line&quot;: 91, &quot;end_line&quot;: 234, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 91, &quot;content&quot;: &quot;                \&quot;hidden-related:/subscriptions/82e97bef-45a0-4ed7-ac69-82c0501a15dc/resourceGroups/onefuzz-daily-ui/providers/Microsoft.Web/serverfarms/SP-DEV-WEB-WESTUS\&quot;: \&quot;empty\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 92, &quot;content&quot;: &quot;            },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 93, &quot;content&quot;: &quot;            \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 94, &quot;content&quot;: &quot;                \&quot;allow\&quot;: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 95, &quot;content&quot;: &quot;            }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 96, &quot;content&quot;: &quot;        },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 97, &quot;content&quot;: &quot;        {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 98, &quot;content&quot;: &quot;            \&quot;type\&quot;: \&quot;Microsoft.Web/sites/basicPublishingCredentialsPolicies\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 99, &quot;content&quot;: &quot;            \&quot;apiVersion\&quot;: \&quot;2024-04-01\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 100, &quot;content&quot;: &quot;            \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '/scm')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 101, &quot;content&quot;: &quot;            \&quot;location\&quot;: \&quot;West US 2\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 102, &quot;content&quot;: &quot;            \&quot;dependsOn\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 103, &quot;content&quot;: &quot;                \&quot;[resourceId('Microsoft.Web/sites', parameters('sites_onefuzz_daily_ui_name'))]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 104, &quot;content&quot;: &quot;            ],&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 105, &quot;content&quot;: &quot;            \&quot;tags\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 106, &quot;content&quot;: &quot;                \&quot;hidden-related:/subscriptions/82e97bef-45a0-4ed7-ac69-82c0501a15dc/resourceGroups/onefuzz-daily-ui/providers/Microsoft.Web/serverfarms/SP-DEV-WEB-WESTUS\&quot;: \&quot;empty\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 107, &quot;content&quot;: &quot;            },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 108, &quot;content&quot;: &quot;            \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 109, &quot;content&quot;: &quot;                \&quot;allow\&quot;: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 110, &quot;content&quot;: &quot;            }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 111, &quot;content&quot;: &quot;        },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 112, &quot;content&quot;: &quot;        {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 113, &quot;content&quot;: &quot;            \&quot;type\&quot;: \&quot;Microsoft.Web/sites/config\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 114, &quot;content&quot;: &quot;            \&quot;apiVersion\&quot;: \&quot;2024-04-01\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 115, &quot;content&quot;: &quot;            \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '/web')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 116, &quot;content&quot;: &quot;            \&quot;location\&quot;: \&quot;West US 2\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 117, &quot;content&quot;: &quot;            \&quot;dependsOn\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 118, &quot;content&quot;: &quot;                \&quot;[resourceId('Microsoft.Web/sites', parameters('sites_onefuzz_daily_ui_name'))]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 119, &quot;content&quot;: &quot;            ],&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 120, &quot;content&quot;: &quot;            \&quot;tags\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 121, &quot;content&quot;: &quot;                \&quot;hidden-related:/subscriptions/82e97bef-45a0-4ed7-ac69-82c0501a15dc/resourceGroups/onefuzz-daily-ui/providers/Microsoft.Web/serverfarms/SP-DEV-WEB-WESTUS\&quot;: \&quot;empty\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 122, &quot;content&quot;: &quot;            },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 123, &quot;content&quot;: &quot;            \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 124, &quot;content&quot;: &quot;                \&quot;numberOfWorkers\&quot;: 1,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 125, &quot;content&quot;: &quot;                \&quot;defaultDocuments\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 126, &quot;content&quot;: &quot;                    \&quot;Default.htm\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 127, &quot;content&quot;: &quot;                    \&quot;Default.html\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 128, &quot;content&quot;: &quot;                    \&quot;Default.asp\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 129, &quot;content&quot;: &quot;                    \&quot;index.htm\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 130, &quot;content&quot;: &quot;                    \&quot;index.html\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 131, &quot;content&quot;: &quot;                    \&quot;iisstart.htm\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 132, &quot;content&quot;: &quot;                    \&quot;default.aspx\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 133, &quot;content&quot;: &quot;                    \&quot;index.php\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 134, &quot;content&quot;: &quot;                    \&quot;hostingstart.html\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 135, &quot;content&quot;: &quot;                ],&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 136, &quot;content&quot;: &quot;                \&quot;netFrameworkVersion\&quot;: \&quot;v4.0\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 137, &quot;content&quot;: &quot;                \&quot;phpVersion\&quot;: \&quot;5.6\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 138, &quot;content&quot;: &quot;                \&quot;nodeVersion\&quot;: \&quot;~20\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 139, &quot;content&quot;: &quot;                \&quot;requestTracingEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 140, &quot;content&quot;: &quot;                \&quot;remoteDebuggingEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 141, &quot;content&quot;: &quot;                \&quot;httpLoggingEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 142, &quot;content&quot;: &quot;                \&quot;acrUseManagedIdentityCreds\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 143, &quot;content&quot;: &quot;                \&quot;logsDirectorySizeLimit\&quot;: 35,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 144, &quot;content&quot;: &quot;                \&quot;detailedErrorLoggingEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 145, &quot;content&quot;: &quot;                \&quot;publishingUsername\&quot;: \&quot;REDACTED\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 146, &quot;content&quot;: &quot;                \&quot;scmType\&quot;: \&quot;None\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 147, &quot;content&quot;: &quot;                \&quot;use32BitWorkerProcess\&quot;: true,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 148, &quot;content&quot;: &quot;                \&quot;webSocketsEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 149, &quot;content&quot;: &quot;                \&quot;alwaysOn\&quot;: true,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 150, &quot;content&quot;: &quot;                \&quot;managedPipelineMode\&quot;: \&quot;Integrated\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 151, &quot;content&quot;: &quot;                \&quot;virtualApplications\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 152, &quot;content&quot;: &quot;                    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 153, &quot;content&quot;: &quot;                        \&quot;virtualPath\&quot;: \&quot;/\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 154, &quot;content&quot;: &quot;                        \&quot;physicalPath\&quot;: \&quot;site\\\\wwwroot\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 155, &quot;content&quot;: &quot;                        \&quot;preloadEnabled\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 156, &quot;content&quot;: &quot;                    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 157, &quot;content&quot;: &quot;                ],&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 158, &quot;content&quot;: &quot;                \&quot;loadBalancing\&quot;: \&quot;LeastRequests\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 159, &quot;content&quot;: &quot;                \&quot;experiments\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 160, &quot;content&quot;: &quot;                    \&quot;rampUpRules\&quot;: []&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 161, &quot;content&quot;: &quot;                },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 162, &quot;content&quot;: &quot;                \&quot;autoHealEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 163, &quot;content&quot;: &quot;                \&quot;vnetRouteAllEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 164, &quot;content&quot;: &quot;                \&quot;vnetPrivatePortsCount\&quot;: 0,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 165, &quot;content&quot;: &quot;                \&quot;publicNetworkAccess\&quot;: \&quot;Enabled\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 166, &quot;content&quot;: &quot;                \&quot;cors\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 167, &quot;content&quot;: &quot;                    \&quot;allowedOrigins\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 168, &quot;content&quot;: &quot;                        \&quot;https://onefuzz-daily-ui.microsoft.com\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 169, &quot;content&quot;: &quot;                        \&quot;https://login.microsoftonline.com\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 170, &quot;content&quot;: &quot;                    ],&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 171, &quot;content&quot;: &quot;                    \&quot;supportCredentials\&quot;: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 172, &quot;content&quot;: &quot;                },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 173, &quot;content&quot;: &quot;                \&quot;localMySqlEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 174, &quot;content&quot;: &quot;                \&quot;managedServiceIdentityId\&quot;: 49453,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 175, &quot;content&quot;: &quot;                \&quot;ipSecurityRestrictions\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 176, &quot;content&quot;: &quot;                    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 177, &quot;content&quot;: &quot;                        \&quot;ipAddress\&quot;: \&quot;Any\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 178, &quot;content&quot;: &quot;                        \&quot;action\&quot;: \&quot;Allow\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 179, &quot;content&quot;: &quot;                        \&quot;priority\&quot;: 2147483647,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 180, &quot;content&quot;: &quot;                        \&quot;name\&quot;: \&quot;Allow all\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 181, &quot;content&quot;: &quot;                        \&quot;description\&quot;: \&quot;Allow all access\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 182, &quot;content&quot;: &quot;                    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 183, &quot;content&quot;: &quot;                ],&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 184, &quot;content&quot;: &quot;                \&quot;scmIpSecurityRestrictions\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 185, &quot;content&quot;: &quot;                    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 186, &quot;content&quot;: &quot;                        \&quot;ipAddress\&quot;: \&quot;Any\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 187, &quot;content&quot;: &quot;                        \&quot;action\&quot;: \&quot;Allow\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 188, &quot;content&quot;: &quot;                        \&quot;priority\&quot;: 2147483647,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 189, &quot;content&quot;: &quot;                        \&quot;name\&quot;: \&quot;Allow all\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 190, &quot;content&quot;: &quot;                        \&quot;description\&quot;: \&quot;Allow all access\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 191, &quot;content&quot;: &quot;                    }&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 192, &quot;content&quot;: &quot;                ],&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 193, &quot;content&quot;: &quot;                \&quot;scmIpSecurityRestrictionsUseMain\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 194, &quot;content&quot;: &quot;                \&quot;http20Enabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 195, &quot;content&quot;: &quot;                \&quot;minTlsVersion\&quot;: \&quot;1.2\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 196, &quot;content&quot;: &quot;                \&quot;scmMinTlsVersion\&quot;: \&quot;1.2\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 197, &quot;content&quot;: &quot;                \&quot;ftpsState\&quot;: \&quot;FtpsOnly\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 198, &quot;content&quot;: &quot;                \&quot;preWarmedInstanceCount\&quot;: 0,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 199, &quot;content&quot;: &quot;                \&quot;elasticWebAppScaleLimit\&quot;: 0,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 200, &quot;content&quot;: &quot;                \&quot;functionsRuntimeScaleMonitoringEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 201, &quot;content&quot;: &quot;                \&quot;minimumElasticInstanceCount\&quot;: 1,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 202, &quot;content&quot;: &quot;                \&quot;azureStorageAccounts\&quot;: {}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 203, &quot;content&quot;: &quot;            }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 204, &quot;content&quot;: &quot;        },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 205, &quot;content&quot;: &quot;        {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 206, &quot;content&quot;: &quot;            \&quot;type\&quot;: \&quot;Microsoft.Web/sites/hostNameBindings\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 207, &quot;content&quot;: &quot;            \&quot;apiVersion\&quot;: \&quot;2024-04-01\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 208, &quot;content&quot;: &quot;            \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '/', parameters('sites_onefuzz_daily_ui_name'), '.azurewebsites.net')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 209, &quot;content&quot;: &quot;            \&quot;location\&quot;: \&quot;West US 2\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 210, &quot;content&quot;: &quot;            \&quot;dependsOn\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 211, &quot;content&quot;: &quot;                \&quot;[resourceId('Microsoft.Web/sites', parameters('sites_onefuzz_daily_ui_name'))]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 212, &quot;content&quot;: &quot;            ],&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 213, &quot;content&quot;: &quot;            \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 214, &quot;content&quot;: &quot;                \&quot;siteName\&quot;: \&quot;onefuzz-daily-ui\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 215, &quot;content&quot;: &quot;                \&quot;hostNameType\&quot;: \&quot;Verified\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 216, &quot;content&quot;: &quot;            }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 217, &quot;content&quot;: &quot;        },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 218, &quot;content&quot;: &quot;        {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 219, &quot;content&quot;: &quot;            \&quot;type\&quot;: \&quot;Microsoft.Web/sites/hostNameBindings\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 220, &quot;content&quot;: &quot;            \&quot;apiVersion\&quot;: \&quot;2024-04-01\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 221, &quot;content&quot;: &quot;            \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '/', parameters('sites_onefuzz_daily_ui_name'), '.microsoft.com')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 222, &quot;content&quot;: &quot;            \&quot;location\&quot;: \&quot;West US 2\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 223, &quot;content&quot;: &quot;            \&quot;dependsOn\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 224, &quot;content&quot;: &quot;                \&quot;[resourceId('Microsoft.Web/sites', parameters('sites_onefuzz_daily_ui_name'))]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 225, &quot;content&quot;: &quot;            ],&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 226, &quot;content&quot;: &quot;            \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 227, &quot;content&quot;: &quot;                \&quot;siteName\&quot;: \&quot;onefuzz-daily-ui\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 228, &quot;content&quot;: &quot;                \&quot;hostNameType\&quot;: \&quot;Verified\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 229, &quot;content&quot;: &quot;                \&quot;sslState\&quot;: \&quot;SniEnabled\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 230, &quot;content&quot;: &quot;                \&quot;thumbprint\&quot;: \&quot;3A511380E775FC0057C96B50046A338343912F5C\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 231, &quot;content&quot;: &quot;            }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 232, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 233, &quot;content&quot;: &quot;    ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 234, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;      91:                 \&quot;hidden-related:/subscriptions/82e97bef-45a0-4ed7-ac69-82c0501a15dc/resourceGroups/onefuzz-daily-ui/providers/Microsoft.Web/serverfarms/SP-DEV-WEB-WESTUS\&quot;: \&quot;empty\&quot;\n      92:             },\n      93:             \&quot;properties\&quot;: {\n      94:                 \&quot;allow\&quot;: false\n      95:             }\n      96:         },\n      97:         {\n      98:             \&quot;type\&quot;: \&quot;Microsoft.Web/sites/basicPublishingCredentialsPolicies\&quot;,\n      99:             \&quot;apiVersion\&quot;: \&quot;2024-04-01\&quot;,\n     100:             \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '/scm')]\&quot;,\n     101:             \&quot;location\&quot;: \&quot;West US 2\&quot;,\n     102:             \&quot;dependsOn\&quot;: [\n     103:                 \&quot;[resourceId('Microsoft.Web/sites', parameters('sites_onefuzz_daily_ui_name'))]\&quot;\n     104:             ],\n     105:             \&quot;tags\&quot;: {\n     106:                 \&quot;hidden-related:/subscriptions/82e97bef-45a0-4ed7-ac69-82c0501a15dc/resourceGroups/onefuzz-daily-ui/providers/Microsoft.Web/serverfarms/SP-DEV-WEB-WESTUS\&quot;: \&quot;empty\&quot;\n     107:             },\n     108:             \&quot;properties\&quot;: {\n     109:                 \&quot;allow\&quot;: false\n     110:             }\n     111:         },\n     112:         {\n     113:             \&quot;type\&quot;: \&quot;Microsoft.Web/sites/config\&quot;,\n     114:             \&quot;apiVersion\&quot;: \&quot;2024-04-01\&quot;,\n     115:             \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '/web')]\&quot;,\n     116:             \&quot;location\&quot;: \&quot;West US 2\&quot;,\n     117:             \&quot;dependsOn\&quot;: [\n     118:                 \&quot;[resourceId('Microsoft.Web/sites', parameters('sites_onefuzz_daily_ui_name'))]\&quot;\n     119:             ],\n     120:             \&quot;tags\&quot;: {\n     121:                 \&quot;hidden-related:/subscriptions/82e97bef-45a0-4ed7-ac69-82c0501a15dc/resourceGroups/onefuzz-daily-ui/providers/Microsoft.Web/serverfarms/SP-DEV-WEB-WESTUS\&quot;: \&quot;empty\&quot;\n     122:             },\n     123:             \&quot;properties\&quot;: {\n     124:                 \&quot;numberOfWorkers\&quot;: 1,\n     125:                 \&quot;defaultDocuments\&quot;: [\n     126:                     \&quot;Default.htm\&quot;,\n     127:                     \&quot;Default.html\&quot;,\n     128:                     \&quot;Default.asp\&quot;,\n     129:                     \&quot;index.htm\&quot;,\n     130:                     \&quot;index.html\&quot;,\n     131:                     \&quot;iisstart.htm\&quot;,\n     132:                     \&quot;default.aspx\&quot;,\n     133:                     \&quot;index.php\&quot;,\n     134:                     \&quot;hostingstart.html\&quot;\n     135:                 ],\n     136:                 \&quot;netFrameworkVersion\&quot;: \&quot;v4.0\&quot;,\n     137:                 \&quot;phpVersion\&quot;: \&quot;5.6\&quot;,\n     138:                 \&quot;nodeVersion\&quot;: \&quot;~20\&quot;,\n     139:                 \&quot;requestTracingEnabled\&quot;: false,\n     140:                 \&quot;remoteDebuggingEnabled\&quot;: false,\n     141:                 \&quot;httpLoggingEnabled\&quot;: false,\n     142:                 \&quot;acrUseManagedIdentityCreds\&quot;: false,\n     143:                 \&quot;logsDirectorySizeLimit\&quot;: 35,\n     144:                 \&quot;detailedErrorLoggingEnabled\&quot;: false,\n     145:                 \&quot;publishingUsername\&quot;: \&quot;REDACTED\&quot;,\n     146:                 \&quot;scmType\&quot;: \&quot;None\&quot;,\n     147:                 \&quot;use32BitWorkerProcess\&quot;: true,\n     148:                 \&quot;webSocketsEnabled\&quot;: false,\n     149:                 \&quot;alwaysOn\&quot;: true,\n     150:                 \&quot;managedPipelineMode\&quot;: \&quot;Integrated\&quot;,\n     151:                 \&quot;virtualApplications\&quot;: [\n     152:                     {\n     153:                         \&quot;virtualPath\&quot;: \&quot;/\&quot;,\n     154:                         \&quot;physicalPath\&quot;: \&quot;site\\\\wwwroot\&quot;,\n     155:                         \&quot;preloadEnabled\&quot;: true\n     156:                     }\n     157:                 ],\n     158:                 \&quot;loadBalancing\&quot;: \&quot;LeastRequests\&quot;,\n     159:                 \&quot;experiments\&quot;: {\n     160:                     \&quot;rampUpRules\&quot;: []\n     161:                 },\n     162:                 \&quot;autoHealEnabled\&quot;: false,\n     163:                 \&quot;vnetRouteAllEnabled\&quot;: false,\n     164:                 \&quot;vnetPrivatePortsCount\&quot;: 0,\n     165:                 \&quot;publicNetworkAccess\&quot;: \&quot;Enabled\&quot;,\n     166:                 \&quot;cors\&quot;: {\n     167:                     \&quot;allowedOrigins\&quot;: [\n     168:                         \&quot;https://onefuzz-daily-ui.microsoft.com\&quot;,\n     169:                         \&quot;https://login.microsoftonline.com\&quot;\n     170:                     ],\n     171:                     \&quot;supportCredentials\&quot;: false\n     172:                 },\n     173:                 \&quot;localMySqlEnabled\&quot;: false,\n     174:                 \&quot;managedServiceIdentityId\&quot;: 49453,\n     175:                 \&quot;ipSecurityRestrictions\&quot;: [\n     176:                     {\n     177:                         \&quot;ipAddress\&quot;: \&quot;Any\&quot;,\n     178:                         \&quot;action\&quot;: \&quot;Allow\&quot;,\n     179:                         \&quot;priority\&quot;: 2147483647,\n     180:                         \&quot;name\&quot;: \&quot;Allow all\&quot;,\n     181:                         \&quot;description\&quot;: \&quot;Allow all access\&quot;\n     182:                     }\n     183:                 ],\n     184:                 \&quot;scmIpSecurityRestrictions\&quot;: [\n     185:                     {\n     186:                         \&quot;ipAddress\&quot;: \&quot;Any\&quot;,\n     187:                         \&quot;action\&quot;: \&quot;Allow\&quot;,\n     188:                         \&quot;priority\&quot;: 2147483647,\n     189:                         \&quot;name\&quot;: \&quot;Allow all\&quot;,\n     190:                         \&quot;description\&quot;: \&quot;Allow all access\&quot;\n&gt;&gt;&gt;  191:                     }\n     192:                 ],\n     193:                 \&quot;scmIpSecurityRestrictionsUseMain\&quot;: false,\n     194:                 \&quot;http20Enabled\&quot;: false,\n     195:                 \&quot;minTlsVersion\&quot;: \&quot;1.2\&quot;,\n     196:                 \&quot;scmMinTlsVersion\&quot;: \&quot;1.2\&quot;,\n     197:                 \&quot;ftpsState\&quot;: \&quot;FtpsOnly\&quot;,\n     198:                 \&quot;preWarmedInstanceCount\&quot;: 0,\n     199:                 \&quot;elasticWebAppScaleLimit\&quot;: 0,\n     200:                 \&quot;functionsRuntimeScaleMonitoringEnabled\&quot;: false,\n     201:                 \&quot;minimumElasticInstanceCount\&quot;: 1,\n     202:                 \&quot;azureStorageAccounts\&quot;: {}\n     203:             }\n     204:         },\n     205:         {\n     206:             \&quot;type\&quot;: \&quot;Microsoft.Web/sites/hostNameBindings\&quot;,\n     207:             \&quot;apiVersion\&quot;: \&quot;2024-04-01\&quot;,\n     208:             \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '/', parameters('sites_onefuzz_daily_ui_name'), '.azurewebsites.net')]\&quot;,\n     209:             \&quot;location\&quot;: \&quot;West US 2\&quot;,\n     210:             \&quot;dependsOn\&quot;: [\n     211:                 \&quot;[resourceId('Microsoft.Web/sites', parameters('sites_onefuzz_daily_ui_name'))]\&quot;\n     212:             ],\n     213:             \&quot;properties\&quot;: {\n     214:                 \&quot;siteName\&quot;: \&quot;onefuzz-daily-ui\&quot;,\n     215:                 \&quot;hostNameType\&quot;: \&quot;Verified\&quot;\n     216:             }\n     217:         },\n     218:         {\n     219:             \&quot;type\&quot;: \&quot;Microsoft.Web/sites/hostNameBindings\&quot;,\n     220:             \&quot;apiVersion\&quot;: \&quot;2024-04-01\&quot;,\n     221:             \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '/', parameters('sites_onefuzz_daily_ui_name'), '.microsoft.com')]\&quot;,\n     222:             \&quot;location\&quot;: \&quot;West US 2\&quot;,\n     223:             \&quot;dependsOn\&quot;: [\n     224:                 \&quot;[resourceId('Microsoft.Web/sites', parameters('sites_onefuzz_daily_ui_name'))]\&quot;\n     225:             ],\n     226:             \&quot;properties\&quot;: {\n     227:                 \&quot;siteName\&quot;: \&quot;onefuzz-daily-ui\&quot;,\n     228:                 \&quot;hostNameType\&quot;: \&quot;Verified\&quot;,\n     229:                 \&quot;sslState\&quot;: \&quot;SniEnabled\&quot;,\n     230:                 \&quot;thumbprint\&quot;: \&quot;3A511380E775FC0057C96B50046A338343912F5C\&quot;\n     231:             }\n     232:         }\n     233:     ]\n     234: }&quot;, &quot;highlighted_line_content&quot;: &quot;}&quot;}
                </script>
                <script type="application/json" data-finding-context="template.json_200" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;template.json&quot;, &quot;line_number&quot;: 200, &quot;total_lines&quot;: 234, &quot;start_line&quot;: 100, &quot;end_line&quot;: 234, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 100, &quot;content&quot;: &quot;            \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '/scm')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 101, &quot;content&quot;: &quot;            \&quot;location\&quot;: \&quot;West US 2\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 102, &quot;content&quot;: &quot;            \&quot;dependsOn\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 103, &quot;content&quot;: &quot;                \&quot;[resourceId('Microsoft.Web/sites', parameters('sites_onefuzz_daily_ui_name'))]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 104, &quot;content&quot;: &quot;            ],&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 105, &quot;content&quot;: &quot;            \&quot;tags\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 106, &quot;content&quot;: &quot;                \&quot;hidden-related:/subscriptions/82e97bef-45a0-4ed7-ac69-82c0501a15dc/resourceGroups/onefuzz-daily-ui/providers/Microsoft.Web/serverfarms/SP-DEV-WEB-WESTUS\&quot;: \&quot;empty\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 107, &quot;content&quot;: &quot;            },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 108, &quot;content&quot;: &quot;            \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 109, &quot;content&quot;: &quot;                \&quot;allow\&quot;: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 110, &quot;content&quot;: &quot;            }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 111, &quot;content&quot;: &quot;        },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 112, &quot;content&quot;: &quot;        {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 113, &quot;content&quot;: &quot;            \&quot;type\&quot;: \&quot;Microsoft.Web/sites/config\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 114, &quot;content&quot;: &quot;            \&quot;apiVersion\&quot;: \&quot;2024-04-01\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 115, &quot;content&quot;: &quot;            \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '/web')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 116, &quot;content&quot;: &quot;            \&quot;location\&quot;: \&quot;West US 2\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 117, &quot;content&quot;: &quot;            \&quot;dependsOn\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 118, &quot;content&quot;: &quot;                \&quot;[resourceId('Microsoft.Web/sites', parameters('sites_onefuzz_daily_ui_name'))]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 119, &quot;content&quot;: &quot;            ],&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 120, &quot;content&quot;: &quot;            \&quot;tags\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 121, &quot;content&quot;: &quot;                \&quot;hidden-related:/subscriptions/82e97bef-45a0-4ed7-ac69-82c0501a15dc/resourceGroups/onefuzz-daily-ui/providers/Microsoft.Web/serverfarms/SP-DEV-WEB-WESTUS\&quot;: \&quot;empty\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 122, &quot;content&quot;: &quot;            },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 123, &quot;content&quot;: &quot;            \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 124, &quot;content&quot;: &quot;                \&quot;numberOfWorkers\&quot;: 1,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 125, &quot;content&quot;: &quot;                \&quot;defaultDocuments\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 126, &quot;content&quot;: &quot;                    \&quot;Default.htm\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 127, &quot;content&quot;: &quot;                    \&quot;Default.html\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 128, &quot;content&quot;: &quot;                    \&quot;Default.asp\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 129, &quot;content&quot;: &quot;                    \&quot;index.htm\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 130, &quot;content&quot;: &quot;                    \&quot;index.html\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 131, &quot;content&quot;: &quot;                    \&quot;iisstart.htm\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 132, &quot;content&quot;: &quot;                    \&quot;default.aspx\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 133, &quot;content&quot;: &quot;                    \&quot;index.php\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 134, &quot;content&quot;: &quot;                    \&quot;hostingstart.html\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 135, &quot;content&quot;: &quot;                ],&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 136, &quot;content&quot;: &quot;                \&quot;netFrameworkVersion\&quot;: \&quot;v4.0\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 137, &quot;content&quot;: &quot;                \&quot;phpVersion\&quot;: \&quot;5.6\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 138, &quot;content&quot;: &quot;                \&quot;nodeVersion\&quot;: \&quot;~20\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 139, &quot;content&quot;: &quot;                \&quot;requestTracingEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 140, &quot;content&quot;: &quot;                \&quot;remoteDebuggingEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 141, &quot;content&quot;: &quot;                \&quot;httpLoggingEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 142, &quot;content&quot;: &quot;                \&quot;acrUseManagedIdentityCreds\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 143, &quot;content&quot;: &quot;                \&quot;logsDirectorySizeLimit\&quot;: 35,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 144, &quot;content&quot;: &quot;                \&quot;detailedErrorLoggingEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 145, &quot;content&quot;: &quot;                \&quot;publishingUsername\&quot;: \&quot;REDACTED\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 146, &quot;content&quot;: &quot;                \&quot;scmType\&quot;: \&quot;None\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 147, &quot;content&quot;: &quot;                \&quot;use32BitWorkerProcess\&quot;: true,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 148, &quot;content&quot;: &quot;                \&quot;webSocketsEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 149, &quot;content&quot;: &quot;                \&quot;alwaysOn\&quot;: true,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 150, &quot;content&quot;: &quot;                \&quot;managedPipelineMode\&quot;: \&quot;Integrated\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 151, &quot;content&quot;: &quot;                \&quot;virtualApplications\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 152, &quot;content&quot;: &quot;                    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 153, &quot;content&quot;: &quot;                        \&quot;virtualPath\&quot;: \&quot;/\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 154, &quot;content&quot;: &quot;                        \&quot;physicalPath\&quot;: \&quot;site\\\\wwwroot\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 155, &quot;content&quot;: &quot;                        \&quot;preloadEnabled\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 156, &quot;content&quot;: &quot;                    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 157, &quot;content&quot;: &quot;                ],&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 158, &quot;content&quot;: &quot;                \&quot;loadBalancing\&quot;: \&quot;LeastRequests\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 159, &quot;content&quot;: &quot;                \&quot;experiments\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 160, &quot;content&quot;: &quot;                    \&quot;rampUpRules\&quot;: []&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 161, &quot;content&quot;: &quot;                },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 162, &quot;content&quot;: &quot;                \&quot;autoHealEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 163, &quot;content&quot;: &quot;                \&quot;vnetRouteAllEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 164, &quot;content&quot;: &quot;                \&quot;vnetPrivatePortsCount\&quot;: 0,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 165, &quot;content&quot;: &quot;                \&quot;publicNetworkAccess\&quot;: \&quot;Enabled\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 166, &quot;content&quot;: &quot;                \&quot;cors\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 167, &quot;content&quot;: &quot;                    \&quot;allowedOrigins\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 168, &quot;content&quot;: &quot;                        \&quot;https://onefuzz-daily-ui.microsoft.com\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 169, &quot;content&quot;: &quot;                        \&quot;https://login.microsoftonline.com\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 170, &quot;content&quot;: &quot;                    ],&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 171, &quot;content&quot;: &quot;                    \&quot;supportCredentials\&quot;: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 172, &quot;content&quot;: &quot;                },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 173, &quot;content&quot;: &quot;                \&quot;localMySqlEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 174, &quot;content&quot;: &quot;                \&quot;managedServiceIdentityId\&quot;: 49453,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 175, &quot;content&quot;: &quot;                \&quot;ipSecurityRestrictions\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 176, &quot;content&quot;: &quot;                    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 177, &quot;content&quot;: &quot;                        \&quot;ipAddress\&quot;: \&quot;Any\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 178, &quot;content&quot;: &quot;                        \&quot;action\&quot;: \&quot;Allow\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 179, &quot;content&quot;: &quot;                        \&quot;priority\&quot;: 2147483647,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 180, &quot;content&quot;: &quot;                        \&quot;name\&quot;: \&quot;Allow all\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 181, &quot;content&quot;: &quot;                        \&quot;description\&quot;: \&quot;Allow all access\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 182, &quot;content&quot;: &quot;                    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 183, &quot;content&quot;: &quot;                ],&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 184, &quot;content&quot;: &quot;                \&quot;scmIpSecurityRestrictions\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 185, &quot;content&quot;: &quot;                    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 186, &quot;content&quot;: &quot;                        \&quot;ipAddress\&quot;: \&quot;Any\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 187, &quot;content&quot;: &quot;                        \&quot;action\&quot;: \&quot;Allow\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 188, &quot;content&quot;: &quot;                        \&quot;priority\&quot;: 2147483647,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 189, &quot;content&quot;: &quot;                        \&quot;name\&quot;: \&quot;Allow all\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 190, &quot;content&quot;: &quot;                        \&quot;description\&quot;: \&quot;Allow all access\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 191, &quot;content&quot;: &quot;                    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 192, &quot;content&quot;: &quot;                ],&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 193, &quot;content&quot;: &quot;                \&quot;scmIpSecurityRestrictionsUseMain\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 194, &quot;content&quot;: &quot;                \&quot;http20Enabled\&quot;: false,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 195, &quot;content&quot;: &quot;                \&quot;minTlsVersion\&quot;: \&quot;1.2\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 196, &quot;content&quot;: &quot;                \&quot;scmMinTlsVersion\&quot;: \&quot;1.2\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 197, &quot;content&quot;: &quot;                \&quot;ftpsState\&quot;: \&quot;FtpsOnly\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 198, &quot;content&quot;: &quot;                \&quot;preWarmedInstanceCount\&quot;: 0,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 199, &quot;content&quot;: &quot;                \&quot;elasticWebAppScaleLimit\&quot;: 0,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 200, &quot;content&quot;: &quot;                \&quot;functionsRuntimeScaleMonitoringEnabled\&quot;: false,&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 201, &quot;content&quot;: &quot;                \&quot;minimumElasticInstanceCount\&quot;: 1,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 202, &quot;content&quot;: &quot;                \&quot;azureStorageAccounts\&quot;: {}&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 203, &quot;content&quot;: &quot;            }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 204, &quot;content&quot;: &quot;        },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 205, &quot;content&quot;: &quot;        {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 206, &quot;content&quot;: &quot;            \&quot;type\&quot;: \&quot;Microsoft.Web/sites/hostNameBindings\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 207, &quot;content&quot;: &quot;            \&quot;apiVersion\&quot;: \&quot;2024-04-01\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 208, &quot;content&quot;: &quot;            \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '/', parameters('sites_onefuzz_daily_ui_name'), '.azurewebsites.net')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 209, &quot;content&quot;: &quot;            \&quot;location\&quot;: \&quot;West US 2\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 210, &quot;content&quot;: &quot;            \&quot;dependsOn\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 211, &quot;content&quot;: &quot;                \&quot;[resourceId('Microsoft.Web/sites', parameters('sites_onefuzz_daily_ui_name'))]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 212, &quot;content&quot;: &quot;            ],&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 213, &quot;content&quot;: &quot;            \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 214, &quot;content&quot;: &quot;                \&quot;siteName\&quot;: \&quot;onefuzz-daily-ui\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 215, &quot;content&quot;: &quot;                \&quot;hostNameType\&quot;: \&quot;Verified\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 216, &quot;content&quot;: &quot;            }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 217, &quot;content&quot;: &quot;        },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 218, &quot;content&quot;: &quot;        {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 219, &quot;content&quot;: &quot;            \&quot;type\&quot;: \&quot;Microsoft.Web/sites/hostNameBindings\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 220, &quot;content&quot;: &quot;            \&quot;apiVersion\&quot;: \&quot;2024-04-01\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 221, &quot;content&quot;: &quot;            \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '/', parameters('sites_onefuzz_daily_ui_name'), '.microsoft.com')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 222, &quot;content&quot;: &quot;            \&quot;location\&quot;: \&quot;West US 2\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 223, &quot;content&quot;: &quot;            \&quot;dependsOn\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 224, &quot;content&quot;: &quot;                \&quot;[resourceId('Microsoft.Web/sites', parameters('sites_onefuzz_daily_ui_name'))]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 225, &quot;content&quot;: &quot;            ],&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 226, &quot;content&quot;: &quot;            \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 227, &quot;content&quot;: &quot;                \&quot;siteName\&quot;: \&quot;onefuzz-daily-ui\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 228, &quot;content&quot;: &quot;                \&quot;hostNameType\&quot;: \&quot;Verified\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 229, &quot;content&quot;: &quot;                \&quot;sslState\&quot;: \&quot;SniEnabled\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 230, &quot;content&quot;: &quot;                \&quot;thumbprint\&quot;: \&quot;3A511380E775FC0057C96B50046A338343912F5C\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 231, &quot;content&quot;: &quot;            }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 232, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 233, &quot;content&quot;: &quot;    ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 234, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;     100:             \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '/scm')]\&quot;,\n     101:             \&quot;location\&quot;: \&quot;West US 2\&quot;,\n     102:             \&quot;dependsOn\&quot;: [\n     103:                 \&quot;[resourceId('Microsoft.Web/sites', parameters('sites_onefuzz_daily_ui_name'))]\&quot;\n     104:             ],\n     105:             \&quot;tags\&quot;: {\n     106:                 \&quot;hidden-related:/subscriptions/82e97bef-45a0-4ed7-ac69-82c0501a15dc/resourceGroups/onefuzz-daily-ui/providers/Microsoft.Web/serverfarms/SP-DEV-WEB-WESTUS\&quot;: \&quot;empty\&quot;\n     107:             },\n     108:             \&quot;properties\&quot;: {\n     109:                 \&quot;allow\&quot;: false\n     110:             }\n     111:         },\n     112:         {\n     113:             \&quot;type\&quot;: \&quot;Microsoft.Web/sites/config\&quot;,\n     114:             \&quot;apiVersion\&quot;: \&quot;2024-04-01\&quot;,\n     115:             \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '/web')]\&quot;,\n     116:             \&quot;location\&quot;: \&quot;West US 2\&quot;,\n     117:             \&quot;dependsOn\&quot;: [\n     118:                 \&quot;[resourceId('Microsoft.Web/sites', parameters('sites_onefuzz_daily_ui_name'))]\&quot;\n     119:             ],\n     120:             \&quot;tags\&quot;: {\n     121:                 \&quot;hidden-related:/subscriptions/82e97bef-45a0-4ed7-ac69-82c0501a15dc/resourceGroups/onefuzz-daily-ui/providers/Microsoft.Web/serverfarms/SP-DEV-WEB-WESTUS\&quot;: \&quot;empty\&quot;\n     122:             },\n     123:             \&quot;properties\&quot;: {\n     124:                 \&quot;numberOfWorkers\&quot;: 1,\n     125:                 \&quot;defaultDocuments\&quot;: [\n     126:                     \&quot;Default.htm\&quot;,\n     127:                     \&quot;Default.html\&quot;,\n     128:                     \&quot;Default.asp\&quot;,\n     129:                     \&quot;index.htm\&quot;,\n     130:                     \&quot;index.html\&quot;,\n     131:                     \&quot;iisstart.htm\&quot;,\n     132:                     \&quot;default.aspx\&quot;,\n     133:                     \&quot;index.php\&quot;,\n     134:                     \&quot;hostingstart.html\&quot;\n     135:                 ],\n     136:                 \&quot;netFrameworkVersion\&quot;: \&quot;v4.0\&quot;,\n     137:                 \&quot;phpVersion\&quot;: \&quot;5.6\&quot;,\n     138:                 \&quot;nodeVersion\&quot;: \&quot;~20\&quot;,\n     139:                 \&quot;requestTracingEnabled\&quot;: false,\n     140:                 \&quot;remoteDebuggingEnabled\&quot;: false,\n     141:                 \&quot;httpLoggingEnabled\&quot;: false,\n     142:                 \&quot;acrUseManagedIdentityCreds\&quot;: false,\n     143:                 \&quot;logsDirectorySizeLimit\&quot;: 35,\n     144:                 \&quot;detailedErrorLoggingEnabled\&quot;: false,\n     145:                 \&quot;publishingUsername\&quot;: \&quot;REDACTED\&quot;,\n     146:                 \&quot;scmType\&quot;: \&quot;None\&quot;,\n     147:                 \&quot;use32BitWorkerProcess\&quot;: true,\n     148:                 \&quot;webSocketsEnabled\&quot;: false,\n     149:                 \&quot;alwaysOn\&quot;: true,\n     150:                 \&quot;managedPipelineMode\&quot;: \&quot;Integrated\&quot;,\n     151:                 \&quot;virtualApplications\&quot;: [\n     152:                     {\n     153:                         \&quot;virtualPath\&quot;: \&quot;/\&quot;,\n     154:                         \&quot;physicalPath\&quot;: \&quot;site\\\\wwwroot\&quot;,\n     155:                         \&quot;preloadEnabled\&quot;: true\n     156:                     }\n     157:                 ],\n     158:                 \&quot;loadBalancing\&quot;: \&quot;LeastRequests\&quot;,\n     159:                 \&quot;experiments\&quot;: {\n     160:                     \&quot;rampUpRules\&quot;: []\n     161:                 },\n     162:                 \&quot;autoHealEnabled\&quot;: false,\n     163:                 \&quot;vnetRouteAllEnabled\&quot;: false,\n     164:                 \&quot;vnetPrivatePortsCount\&quot;: 0,\n     165:                 \&quot;publicNetworkAccess\&quot;: \&quot;Enabled\&quot;,\n     166:                 \&quot;cors\&quot;: {\n     167:                     \&quot;allowedOrigins\&quot;: [\n     168:                         \&quot;https://onefuzz-daily-ui.microsoft.com\&quot;,\n     169:                         \&quot;https://login.microsoftonline.com\&quot;\n     170:                     ],\n     171:                     \&quot;supportCredentials\&quot;: false\n     172:                 },\n     173:                 \&quot;localMySqlEnabled\&quot;: false,\n     174:                 \&quot;managedServiceIdentityId\&quot;: 49453,\n     175:                 \&quot;ipSecurityRestrictions\&quot;: [\n     176:                     {\n     177:                         \&quot;ipAddress\&quot;: \&quot;Any\&quot;,\n     178:                         \&quot;action\&quot;: \&quot;Allow\&quot;,\n     179:                         \&quot;priority\&quot;: 2147483647,\n     180:                         \&quot;name\&quot;: \&quot;Allow all\&quot;,\n     181:                         \&quot;description\&quot;: \&quot;Allow all access\&quot;\n     182:                     }\n     183:                 ],\n     184:                 \&quot;scmIpSecurityRestrictions\&quot;: [\n     185:                     {\n     186:                         \&quot;ipAddress\&quot;: \&quot;Any\&quot;,\n     187:                         \&quot;action\&quot;: \&quot;Allow\&quot;,\n     188:                         \&quot;priority\&quot;: 2147483647,\n     189:                         \&quot;name\&quot;: \&quot;Allow all\&quot;,\n     190:                         \&quot;description\&quot;: \&quot;Allow all access\&quot;\n     191:                     }\n     192:                 ],\n     193:                 \&quot;scmIpSecurityRestrictionsUseMain\&quot;: false,\n     194:                 \&quot;http20Enabled\&quot;: false,\n     195:                 \&quot;minTlsVersion\&quot;: \&quot;1.2\&quot;,\n     196:                 \&quot;scmMinTlsVersion\&quot;: \&quot;1.2\&quot;,\n     197:                 \&quot;ftpsState\&quot;: \&quot;FtpsOnly\&quot;,\n     198:                 \&quot;preWarmedInstanceCount\&quot;: 0,\n     199:                 \&quot;elasticWebAppScaleLimit\&quot;: 0,\n&gt;&gt;&gt;  200:                 \&quot;functionsRuntimeScaleMonitoringEnabled\&quot;: false,\n     201:                 \&quot;minimumElasticInstanceCount\&quot;: 1,\n     202:                 \&quot;azureStorageAccounts\&quot;: {}\n     203:             }\n     204:         },\n     205:         {\n     206:             \&quot;type\&quot;: \&quot;Microsoft.Web/sites/hostNameBindings\&quot;,\n     207:             \&quot;apiVersion\&quot;: \&quot;2024-04-01\&quot;,\n     208:             \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '/', parameters('sites_onefuzz_daily_ui_name'), '.azurewebsites.net')]\&quot;,\n     209:             \&quot;location\&quot;: \&quot;West US 2\&quot;,\n     210:             \&quot;dependsOn\&quot;: [\n     211:                 \&quot;[resourceId('Microsoft.Web/sites', parameters('sites_onefuzz_daily_ui_name'))]\&quot;\n     212:             ],\n     213:             \&quot;properties\&quot;: {\n     214:                 \&quot;siteName\&quot;: \&quot;onefuzz-daily-ui\&quot;,\n     215:                 \&quot;hostNameType\&quot;: \&quot;Verified\&quot;\n     216:             }\n     217:         },\n     218:         {\n     219:             \&quot;type\&quot;: \&quot;Microsoft.Web/sites/hostNameBindings\&quot;,\n     220:             \&quot;apiVersion\&quot;: \&quot;2024-04-01\&quot;,\n     221:             \&quot;name\&quot;: \&quot;[concat(parameters('sites_onefuzz_daily_ui_name'), '/', parameters('sites_onefuzz_daily_ui_name'), '.microsoft.com')]\&quot;,\n     222:             \&quot;location\&quot;: \&quot;West US 2\&quot;,\n     223:             \&quot;dependsOn\&quot;: [\n     224:                 \&quot;[resourceId('Microsoft.Web/sites', parameters('sites_onefuzz_daily_ui_name'))]\&quot;\n     225:             ],\n     226:             \&quot;properties\&quot;: {\n     227:                 \&quot;siteName\&quot;: \&quot;onefuzz-daily-ui\&quot;,\n     228:                 \&quot;hostNameType\&quot;: \&quot;Verified\&quot;,\n     229:                 \&quot;sslState\&quot;: \&quot;SniEnabled\&quot;,\n     230:                 \&quot;thumbprint\&quot;: \&quot;3A511380E775FC0057C96B50046A338343912F5C\&quot;\n     231:             }\n     232:         }\n     233:     ]\n     234: }&quot;, &quot;highlighted_line_content&quot;: &quot;\&quot;functionsRuntimeScaleMonitoringEnabled\&quot;: false,&quot;}
                </script>


    <style>
        /* Glass UI Ripple Effect */
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* Code Dialog Modal Styling */
        .code-dialog-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: hsla(var(--hue-primary), 90%, 5%, 0.8);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 10000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .code-dialog {
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur-strong);
            -webkit-backdrop-filter: var(--glass-blur-strong);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--glass-shadow-xl);
            max-width: 90vw;
            max-height: 80vh;
            width: 1000px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .code-dialog-header {
            background: linear-gradient(135deg, var(--primary500), var(--primary600));
            color: var(--text-on-dark);
            padding: 1.5rem 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
        }

        .code-dialog-title {
            font-size: 1.25rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .code-dialog-close {
            background: none;
            border: none;
            color: var(--text-on-dark);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 2.5rem;
            height: 2.5rem;
        }

        .code-dialog-close:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        .code-dialog-content {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .code-snippet-container {
            flex: 1;
            overflow: auto;
            background: var(--dark-gray100);
            margin: 0;
            position: relative;
        }

        .code-snippet-with-lines {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 0.875rem;
            line-height: 1.6;
            color: var(--text-accent);
            padding: 0;
            margin: 0;
            display: flex;
            min-height: 100%;
        }

        .line-numbers {
            background: hsla(var(--hue-primary), 90%, 8%, 0.85);
            color: var(--text-primary);
            padding: 1rem 0.75rem;
            text-align: right;
            user-select: none;
            border-right: 2px solid var(--primary500);
            min-width: 4rem;
            font-weight: 600;
            font-size: 0.9rem;
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
        }

        .code-content {
            flex: 1;
            padding: 1rem;
            white-space: pre;
            overflow-x: auto;
        }

        .highlighted-line {
            background: hsla(var(--hue-primary), 90%, 50%, 0.2);
            border-left: 3px solid var(--primary500);
            margin-left: -1rem;
            padding-left: calc(1rem - 3px);
            position: relative;
        }

        .highlighted-line::before {
            content: '← Security Issue';
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: var(--danger-red);
            color: var(--text-on-dark);
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
            opacity: 0.9;
        }

        .line-numbers .highlighted-line-number {
            background: var(--primary500);
            color: var(--white);
            padding: 0.25rem 0.6rem;
            border-radius: 0.375rem;
            font-weight: 700;
            font-size: 0.95rem;
            box-shadow: 0 3px 8px hsla(var(--hue-primary), 90%, 50%, 0.4);
            position: relative;
            border: 1px solid var(--primary600);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .line-numbers .highlighted-line-number::after {
            content: '●';
            position: absolute;
            right: -8px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--danger-red);
            font-size: 0.75rem;
        }

        /* Enhanced line number visibility */
        .line-numbers div {
            padding: 0.125rem 0.25rem;
            margin: 0.125rem 0;
            border-radius: 0.25rem;
            transition: all 0.2s ease;
        }

        .line-numbers div:hover {
            background: hsla(var(--hue-primary), 90%, 15%, 0.6);
            color: var(--text-primary);
        }

        /* Enhanced line number visibility */
        .line-numbers div {
            padding: 0.125rem 0.25rem;
            margin: 0.125rem 0;
            border-radius: 0.25rem;
            transition: all 0.2s ease;
        }

        .line-numbers div:hover {
            background: hsla(var(--hue-primary), 90%, 15%, 0.6);
            color: var(--text-primary);
        }

        .context-info {
            background: var(--glass-white-strong);
            color: var(--text-interactive);
            padding: 0.75rem 1rem;
            margin: 0 -1rem 1rem -1rem;
            border-bottom: 1px solid var(--glass-border);
            font-size: 0.875rem;
            font-weight: 500;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .context-info-spacer {
            height: 2.5rem;
            background: var(--glass-white-strong);
            border-bottom: 1px solid var(--glass-border);
        }

        /* Code Snippet Section Styles */
        .code-snippet-section {
            margin-top: 1.5rem;
            background: var(--glass-white-light);
            border: 1px solid var(--glass-border);
            border-radius: 0.75rem;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .code-snippet-section.fallback {
            background: var(--glass-white-medium);
            border-color: var(--warning-amber);
        }

        .code-snippet-header {
            background: var(--glass-white-strong);
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--glass-border);
        }

        .code-snippet-header h4 {
            margin: 0 0 0.5rem 0;
            color: var(--text-primary);
            font-size: 1rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .code-info {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .code-info span {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .code-snippet-content {
            max-height: 600px;
            overflow-y: auto;
            background: var(--glass-dark);
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .code-line, .highlighted-code-line {
            display: flex;
            align-items: flex-start;
            padding: 0.25rem 0;
            border-bottom: 1px solid hsla(var(--hue-primary), 20%, 80%, 0.1);
        }

        .highlighted-code-line {
            background: hsla(var(--hue-primary), 90%, 50%, 0.15);
            border-left: 3px solid var(--danger-red);
            position: relative;
        }

        .line-number {
            display: inline-block;
            width: 4rem;
            text-align: right;
            color: var(--text-primary);
            background: var(--glass-white-light);
            padding: 0.25rem 0.75rem;
            border-right: 2px solid var(--glass-border);
            user-select: none;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .highlighted-code-line .line-number {
            background: var(--danger-red);
            color: var(--text-on-dark);
            font-weight: 700;
        }

        .line-content {
            flex: 1;
            padding: 0.25rem 1rem;
            color: var(--text-primary);
            white-space: pre-wrap;
            word-break: break-word;
        }

        .security-marker {
            color: var(--danger-red);
            font-weight: 600;
            font-size: 0.75rem;
            margin-left: 1rem;
            opacity: 0.9;
        }

        .code-snippet-footer {
            background: var(--glass-white-strong);
            padding: 1rem 1.5rem;
            border-top: 1px solid var(--glass-border);
            display: flex;
            justify-content: flex-end;
        }

        .copy-code-btn {
            background: var(--primary500);
            color: var(--text-on-dark);
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;
        }

        .copy-code-btn:hover {
            background: var(--primary600);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px hsla(var(--hue-primary), 90%, 50%, 0.3);
        }

        .fallback-content {
            padding: 1.5rem;
            color: var(--text-secondary);
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            background: var(--glass-white-medium);
        }

        .fallback-content pre {
            margin: 0;
            white-space: pre-wrap;
            word-break: break-word;
        }

        .code-dialog-footer {
            background: var(--glass-white-light);
            padding: 1rem 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-top: 1px solid var(--glass-border);
            border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
        }

        .code-dialog-info {
            color: var(--text-secondary);
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .code-dialog-actions {
            display: flex;
            gap: 0.75rem;
        }

        .code-dialog-btn {
            padding: 0.375rem 0.75rem;
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-sm);
            background: var(--glass-white-light);
            color: var(--text-interactive);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.8125rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.375rem;
        }

        .code-dialog-btn:hover {
            background: var(--glass-white-strong);
            transform: translateY(-1px);
            box-shadow: var(--glass-shadow);
        }

        .code-dialog-btn.primary {
            background: linear-gradient(135deg, var(--primary500), var(--primary600));
            color: var(--text-on-dark);
            border-color: var(--primary500);
        }

        .code-dialog-btn.primary:hover {
            box-shadow: 0 4px 12px hsla(var(--hue-primary), 90%, 50%, 0.4);
        }

        /* Professional Static Styling - No Distracting Animations */

        /* Code Dialog Responsive Design */
        @media (max-width: 768px) {
            .code-dialog-overlay {
                padding: 1rem;
            }

            .code-dialog {
                width: 100%;
                max-height: 90vh;
            }

            .code-dialog-header {
                padding: 1rem 1.5rem;
            }

            .code-dialog-title {
                font-size: 1.125rem;
            }

            .code-snippet-with-lines {
                font-size: 0.75rem;
            }

            .line-numbers {
                min-width: 3rem;
                padding: 1rem 0.5rem;
                font-size: 0.85rem;
            }

            .code-dialog-footer {
                padding: 1rem 1.5rem;
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }

            .code-dialog-actions {
                justify-content: stretch;
            }

            .code-dialog-btn {
                flex: 1;
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .code-dialog-overlay {
                padding: 0.5rem;
            }

            .code-dialog {
                max-height: 95vh;
            }

            .code-dialog-header {
                padding: 0.75rem 1rem;
            }

            .code-dialog-title {
                font-size: 1rem;
            }

            .code-snippet-with-lines {
                font-size: 0.6875rem;
            }

            .code-dialog-footer {
                padding: 0.75rem 1rem;
            }
        }

        /* Glass UI Print Styles */
        @media print {
            body::before {
                display: none;
            }

            .report-header,
            .controls-section,
            .summary-section,
            .severity-group,
            .domain-section {
                background: white !important;
                backdrop-filter: none !important;
                -webkit-backdrop-filter: none !important;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
            }

            .export-actions,
            .code-dialog-overlay {
                display: none !important;
            }
        }
    </style>
</head>

<body>
    <div class="main-container">
        <!-- Header Section -->
        <header class="report-header">
            <h1 class="report-title">
                <i class="fas fa-shield-alt"></i>
                Security Assessment Report
            </h1>
            <p class="report-subtitle">Infrastructure as Code Security Analysis</p>
            <div class="report-meta">
                <div class="meta-item">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Generated: June 17, 2025 at 09:57 AM</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-robot"></i>
                    <span>IaC Guardian</span>
                </div>
            </div>
        </header>

        <!-- Controls Section -->
        <section class="controls-section">
            <div class="controls-grid">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search findings..." aria-label="Search findings">
                    <i class="fas fa-search search-icon"></i>
                </div>
                <div class="filter-buttons">
                    <button class="filter-btn all active" data-severity="all">All</button>
                    <button class="filter-btn critical" data-severity="critical">Critical</button>
                    <button class="filter-btn high" data-severity="high">High</button>
                    <button class="filter-btn medium" data-severity="medium">Medium</button>
                    <button class="filter-btn low" data-severity="low">Low</button>
                </div>
            </div>
        </section>

        
        <!-- Summary Section -->
        <section class="summary-section">
            <h2 class="summary-title">
                <i class="fas fa-chart-bar"></i>
                Executive Summary
            </h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">12</div>
                    <div class="stat-label">Total Findings</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">1</div>
                    <div class="stat-label">Files Affected</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">9</div>
                    <div class="stat-label">High Priority Issues</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">9</div>
                    <div class="stat-label">Security Controls</div>
                </div>
            </div>
            <div class="severity-overview">
                <div class="severity-badge critical">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        Critical
                    </div>
                    <div class="severity-count">6</div>
                </div>
                <div class="severity-badge high">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        High
                    </div>
                    <div class="severity-count">3</div>
                </div>
                <div class="severity-badge medium">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        Medium
                    </div>
                    <div class="severity-count">3</div>
                </div>
            </div>
        </section>

        <div class="findings-container">
            
            <div class="domain-section">
                <h3 class="domain-header">
                    <i class="fas fa-shield-alt"></i>
                    Identity Management (3 findings)
                </h3>
                <section class="severity-group" data-severity="high" data-domain="identity-management">
                    <header class="severity-header high">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">High Severity</div>
                            <div class="severity-count">1</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item" data-severity="high">
                        <header class="finding-header">
                            <div class="finding-icon high">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">IM-2</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>template.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-crosshairs meta-icon"></i>
                                        <span class="line-number-badge">
                                            <i class="fas fa-code"></i>
                                            Line 191
                                        </span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('template.json', 191, 'IM-2', 'high')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">App Service configuration does not enforce or reference Multi-Factor Authentication (MFA) for users or administrators, weakening access security.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Enforce MFA for all users and administrators accessing the App Service through Azure Active Directory.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    </div>
                </section>
                <section class="severity-group" data-severity="medium" data-domain="identity-management">
                    <header class="severity-header medium">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">Medium Severity</div>
                            <div class="severity-count">2</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item" data-severity="medium">
                        <header class="finding-header">
                            <div class="finding-icon medium">
                                <i class="fas fa-exclamation"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">IM-3</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>template.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-crosshairs meta-icon"></i>
                                        <span class="line-number-badge">
                                            <i class="fas fa-code"></i>
                                            Line 191
                                        </span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('template.json', 191, 'IM-3', 'medium')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">App Service configuration does not reference use of Privileged Identity Management (PIM) for privileged access.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Implement Azure AD Privileged Identity Management (PIM) to manage and control privileged access to the App Service.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="medium">
                        <header class="finding-header">
                            <div class="finding-icon medium">
                                <i class="fas fa-exclamation"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">IM-6</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>template.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-crosshairs meta-icon"></i>
                                        <span class="line-number-badge">
                                            <i class="fas fa-code"></i>
                                            Line 191
                                        </span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('template.json', 191, 'IM-6', 'medium')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">App Service configuration does not specify use of Role-Based Access Control (RBAC) for access assignments.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Assign access rights to the App Service using Azure RBAC to ensure least privilege.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    </div>
                </section>
            </div>
            <div class="domain-section">
                <h3 class="domain-header">
                    <i class="fas fa-shield-alt"></i>
                    Network Security (4 findings)
                </h3>
                <section class="severity-group" data-severity="critical" data-domain="network-security">
                    <header class="severity-header critical">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">Critical Severity</div>
                            <div class="severity-count">2</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-2</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>template.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-crosshairs meta-icon"></i>
                                        <span class="line-number-badge">
                                            <i class="fas fa-code"></i>
                                            Line 191
                                        </span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('template.json', 191, 'NS-2', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">App Service config 'ipSecurityRestrictions' allows access from 'Any' IP address with action 'Allow', exposing the application to the public internet. This violates the requirement to secure all public endpoints.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Restrict 'ipSecurityRestrictions' to only allow trusted IP ranges and deny public (Any) access to minimize exposure.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-2</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>template.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-crosshairs meta-icon"></i>
                                        <span class="line-number-badge">
                                            <i class="fas fa-code"></i>
                                            Line 200
                                        </span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('template.json', 200, 'NS-2', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">App Service config 'scmIpSecurityRestrictions' allows access from 'Any' IP address with action 'Allow', exposing the SCM endpoint to the public internet. This violates the requirement to secure all public endpoints.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Restrict 'scmIpSecurityRestrictions' to only allow trusted IP ranges and deny public (Any) access to minimize exposure.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    </div>
                </section>
                <section class="severity-group" data-severity="high" data-domain="network-security">
                    <header class="severity-header high">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">High Severity</div>
                            <div class="severity-count">2</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item" data-severity="high">
                        <header class="finding-header">
                            <div class="finding-icon high">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-5</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>template.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-crosshairs meta-icon"></i>
                                        <span class="line-number-badge">
                                            <i class="fas fa-code"></i>
                                            Line 191
                                        </span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('template.json', 191, 'NS-5', 'high')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">App Service config 'publicNetworkAccess' is set to 'Enabled', allowing public network access. This does not use private endpoints as recommended.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Set 'publicNetworkAccess' to 'Disabled' and configure a private endpoint for the App Service to restrict access to private networks only.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="high">
                        <header class="finding-header">
                            <div class="finding-icon high">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-5</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>template.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-crosshairs meta-icon"></i>
                                        <span class="line-number-badge">
                                            <i class="fas fa-code"></i>
                                            Line 200
                                        </span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('template.json', 200, 'NS-5', 'high')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">App Service config 'publicNetworkAccess' is set to 'Enabled' for SCM endpoint, allowing public network access. This does not use private endpoints as recommended.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Set 'publicNetworkAccess' to 'Disabled' for the SCM endpoint and configure a private endpoint to restrict access to private networks only.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    </div>
                </section>
            </div>
            <div class="domain-section">
                <h3 class="domain-header">
                    <i class="fas fa-shield-alt"></i>
                    Data Protection (5 findings)
                </h3>
                <section class="severity-group" data-severity="critical" data-domain="data-protection">
                    <header class="severity-header critical">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">Critical Severity</div>
                            <div class="severity-count">4</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-1</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>template.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-crosshairs meta-icon"></i>
                                        <span class="line-number-badge">
                                            <i class="fas fa-code"></i>
                                            Line 191
                                        </span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('template.json', 191, 'DP-1', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">App Service config does not specify use of customer-managed keys (CMK) or explicit encryption at rest settings. This may violate the requirement to enable encryption at rest for all data storage.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Configure the App Service to use encryption at rest, preferably with customer-managed keys (CMK) if handling sensitive data.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-2</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>template.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-crosshairs meta-icon"></i>
                                        <span class="line-number-badge">
                                            <i class="fas fa-code"></i>
                                            Line 61
                                        </span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('template.json', 61, 'DP-2', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">App Service hostNameSslStates for 'onefuzz-daily-ui.azurewebsites.net' and 'onefuzz-daily-ui.scm.azurewebsites.net' have 'sslState' set to 'Disabled', which allows unencrypted HTTP connections. This violates the requirement to use TLS 1.2+ for all data transfers.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates to enforce HTTPS and ensure encryption in transit.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-3</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>template.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-crosshairs meta-icon"></i>
                                        <span class="line-number-badge">
                                            <i class="fas fa-code"></i>
                                            Line 191
                                        </span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('template.json', 191, 'DP-3', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">App Service config includes 'publishingUsername' in plain text, which is sensitive information. This violates the requirement to store sensitive data like credentials in Azure Key Vault.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Remove 'publishingUsername' from the template and reference it securely from Azure Key Vault or use secure parameters.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-3</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>template.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-crosshairs meta-icon"></i>
                                        <span class="line-number-badge">
                                            <i class="fas fa-code"></i>
                                            Line 191
                                        </span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('template.json', 191, 'DP-3', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">App Service config does not reference Azure Key Vault for application secrets or sensitive configuration, violating the requirement to store sensitive data in Azure Key Vault.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Store all application secrets and sensitive configuration in Azure Key Vault and reference them securely in the App Service configuration.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    </div>
                </section>
                <section class="severity-group" data-severity="medium" data-domain="data-protection">
                    <header class="severity-header medium">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">Medium Severity</div>
                            <div class="severity-count">1</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item" data-severity="medium">
                        <header class="finding-header">
                            <div class="finding-icon medium">
                                <i class="fas fa-exclamation"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-6</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>template.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-crosshairs meta-icon"></i>
                                        <span class="line-number-badge">
                                            <i class="fas fa-code"></i>
                                            Line 191
                                        </span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('template.json', 191, 'DP-6', 'medium')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">App Service config does not specify use of customer-managed keys (CMK) for encryption, reducing control over data protection.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Configure the App Service to use customer-managed keys (CMK) for encryption at rest to enhance data protection.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    </div>
                </section>
            </div>
            <div class="no-findings" style="display: none;">
                <i class="fas fa-search no-findings-icon"></i>
                <h3>No findings match your search criteria</h3>
                <p>Try adjusting your search terms or filters</p>
            </div>
        </div>

        <!-- Footer Section -->
        <footer class="report-footer">
            <div class="export-actions">
                <button class="export-btn" onclick="window.print()">
                    <i class="fas fa-print"></i>
                    Print Report
                </button>
                <button class="export-btn" onclick="exportToJson()">
                    <i class="fas fa-download"></i>
                    Export JSON
                </button>
            </div>
            <div class="footer-info">
                <p><strong>Generated by IaC Guardian</strong> • June 17, 2025 at 09:57 AM</p>
                <p><strong>Disclaimer:</strong> This report is AI-generated and should be reviewed by security professionals.</p>
            </div>
        </footer>
    </div>
</body>
</html>