"""
Enhanced methods for security_opt.py integration
"""

import logging
import re
from typing import Dict, List

logger = logging.getLogger(__name__)

def get_controls_for_resource_enhanced(self, resource_type: str) -> List[Dict]:
    """Get comprehensive ASB controls for Azure resource type using enhanced mappings."""
    try:
        if hasattr(self, 'enhanced_mapper') and self.enhanced_mapper:
            # Use enhanced mapper for comprehensive control coverage
            controls = self.enhanced_mapper.get_controls_for_resource_type(resource_type)
            
            # Convert to expected format and add additional details
            formatted_controls = []
            for control in controls:
                control_details = {
                    "id": control['id'],
                    "domain": control['domain'],
                    "name": control['name'],
                    "security_principle": control.get('security_principle', ''),
                    "azure_guidance": control.get('azure_guidance', ''),
                    "implementation_context": control.get('implementation_context', ''),
                    "stakeholders": control.get('stakeholders', ''),
                    "urls": control.get('urls', [])
                }
                formatted_controls.append(control_details)
            
            logger.debug(f"Enhanced mapper returned {len(formatted_controls)} controls for {resource_type}")
            return formatted_controls
        else:
            logger.warning("Enhanced mapper not available, falling back to legacy mappings")
            return self._get_legacy_controls_for_resource(resource_type)
            
    except Exception as e:
        logger.error(f"Error getting controls for resource {resource_type}: {e}")
        return self._get_legacy_controls_for_resource(resource_type)

def extract_control_links_enhanced(self, control_id: str) -> Dict:
    """Extract links and references from enhanced CSV data for a specific control."""
    try:
        if hasattr(self, 'enhanced_mapper') and self.enhanced_mapper:
            # Use enhanced mapper for comprehensive link extraction
            links_info = self.enhanced_mapper.get_control_links(control_id)
            
            # Ensure all required fields are present
            return {
                "formatted_links": links_info.get("formatted_links", ""),
                "azure_guidance": links_info.get("azure_guidance", ""),
                "implementation_context": links_info.get("implementation_context", ""),
                "raw_links": links_info.get("raw_links", [])
            }
        else:
            logger.warning("Enhanced mapper not available for link extraction")
            return self._legacy_extract_control_links(control_id)
            
    except Exception as e:
        logger.error(f"Error extracting links for control {control_id}: {e}")
        return {
            "formatted_links": "",
            "azure_guidance": "",
            "implementation_context": "",
            "raw_links": []
        }
