Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P1-Identity-HIGH,Identity Management,HIGH,IM-2,template.json,191,"App Service configuration does not enforce or reference Multi-Factor Authentication (MFA) for users or administrators, weakening access security.",Enforce MFA for all users and administrators accessing the App Service through Azure Active Directory.,N/A,AI,Generic
P1-Identity-MEDIUM,Identity Management,MEDIUM,IM-3,template.json,191,App Service configuration does not reference use of Privileged Identity Management (PIM) for privileged access.,Implement Azure AD Privileged Identity Management (PIM) to manage and control privileged access to the App Service.,N/A,AI,Generic
P1-Identity-MEDIUM,Identity Management,MEDIUM,IM-6,template.json,191,App Service configuration does not specify use of Role-Based Access Control (RBAC) for access assignments.,Assign access rights to the App Service using Azure RBAC to ensure least privilege.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,191,"App Service config 'ipSecurityRestrictions' allows access from 'Any' IP address with action 'Allow', exposing the application to the public internet. This violates the requirement to secure all public endpoints.",Restrict 'ipSecurityRestrictions' to only allow trusted IP ranges and deny public (Any) access to minimize exposure.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,200,"App Service config 'scmIpSecurityRestrictions' allows access from 'Any' IP address with action 'Allow', exposing the SCM endpoint to the public internet. This violates the requirement to secure all public endpoints.",Restrict 'scmIpSecurityRestrictions' to only allow trusted IP ranges and deny public (Any) access to minimize exposure.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,191,"App Service config 'publicNetworkAccess' is set to 'Enabled', allowing public network access. This does not use private endpoints as recommended.",Set 'publicNetworkAccess' to 'Disabled' and configure a private endpoint for the App Service to restrict access to private networks only.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,200,"App Service config 'publicNetworkAccess' is set to 'Enabled' for SCM endpoint, allowing public network access. This does not use private endpoints as recommended.",Set 'publicNetworkAccess' to 'Disabled' for the SCM endpoint and configure a private endpoint to restrict access to private networks only.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,191,App Service config does not specify use of customer-managed keys (CMK) or explicit encryption at rest settings. This may violate the requirement to enable encryption at rest for all data storage.,"Configure the App Service to use encryption at rest, preferably with customer-managed keys (CMK) if handling sensitive data.",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,61,"App Service hostNameSslStates for 'onefuzz-daily-ui.azurewebsites.net' and 'onefuzz-daily-ui.scm.azurewebsites.net' have 'sslState' set to 'Disabled', which allows unencrypted HTTP connections. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates to enforce HTTPS and ensure encryption in transit.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,191,"App Service config includes 'publishingUsername' in plain text, which is sensitive information. This violates the requirement to store sensitive data like credentials in Azure Key Vault.",Remove 'publishingUsername' from the template and reference it securely from Azure Key Vault or use secure parameters.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,191,"App Service config does not reference Azure Key Vault for application secrets or sensitive configuration, violating the requirement to store sensitive data in Azure Key Vault.",Store all application secrets and sensitive configuration in Azure Key Vault and reference them securely in the App Service configuration.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-6,template.json,191,"App Service config does not specify use of customer-managed keys (CMK) for encryption, reducing control over data protection.",Configure the App Service to use customer-managed keys (CMK) for encryption at rest to enhance data protection.,N/A,AI,Generic
