File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
network_demo.tf,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,24.0,"The 'source_address_prefix' property in the security_rule of azurerm_network_security_group.demo is set to '0.0.0.0/0' (Line 24), allowing inbound SSH (port 22) from any IP address. This exposes the resource to the entire internet, enabling initial access, brute-force attacks, and lateral movement. The blast radius includes any VM or resource associated with this NSG, potentially compromising the entire subnet or resource group.","Restrict 'source_address_prefix' to trusted IP ranges (e.g., corporate office IPs) or use a jump host with just-in-time access. Change 'source_address_prefix' from '0.0.0.0/0' to a specific IP or CIDR block. Example: source_address_prefix = ""***********/24"". Apply deny-by-default and only allow necessary traffic.",,,,ai_analysis,,Validated
network_demo.tf,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,43.0,"The 'allow_blob_public_access' property in azurerm_storage_account.demo is set to 'true' (Line 43), enabling public anonymous access to blob data. This allows any unauthenticated user on the internet to read data from the storage account, creating a direct data exfiltration vector and significantly increasing the blast radius to all data stored in public containers.",Set 'allow_blob_public_access' to 'false' to disable anonymous public access. Example: allow_blob_public_access = false. Use private endpoints and restrict access to trusted networks only.,,,,ai_analysis,,Validated
network_demo.tf,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,46.0,"The 'https_traffic_only' property in azurerm_storage_account.demo is set to 'false' (Line 46), allowing unencrypted HTTP connections. This exposes data in transit to interception and man-in-the-middle attacks, enabling attackers to read or modify sensitive data as it traverses the network.",Set 'https_traffic_only' to 'true' to enforce encryption for all data in transit. Example: https_traffic_only = true. Ensure all clients and applications use HTTPS endpoints.,,,,ai_analysis,,Validated
storage_demo.bicep,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,17.0,"The 'allowBlobPublicAccess' property is set to true, enabling public anonymous access to blobs in the storage account. This exposes all data in public containers to the internet, allowing attackers to enumerate, read, and exfiltrate sensitive data without authentication. The blast radius includes all data stored in public containers, potentially leading to data breaches and regulatory violations.",Set 'allowBlobPublicAccess' to false to disable anonymous public access to blobs. Review all containers and ensure none are configured for public access. Example: allowBlobPublicAccess: false,,,,ai_analysis,,Validated
storage_demo.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,26.0,"The 'minimumTlsVersion' property is set to 'TLS1_0', which is a deprecated and insecure protocol. Attackers can exploit known vulnerabilities in TLS 1.0 to intercept or modify data in transit, leading to credential theft or data tampering. The blast radius includes all clients connecting to the storage account, as weak encryption undermines the confidentiality and integrity of data transfers.",Set 'minimumTlsVersion' to 'TLS1_2' or higher to enforce strong encryption for all connections. Example: minimumTlsVersion: 'TLS1_2',,,,ai_analysis,,Validated
storage_demo.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,23.0,"The 'supportsHttpsTrafficOnly' property is set to false, allowing unencrypted HTTP connections to the storage account. This enables attackers to intercept data in transit using man-in-the-middle attacks, exposing sensitive information and authentication tokens. The blast radius includes all data transferred to and from the storage account over HTTP.",Set 'supportsHttpsTrafficOnly' to true to enforce encrypted HTTPS connections for all storage account traffic. Example: supportsHttpsTrafficOnly: true,,,,ai_analysis,,Validated
storage_demo.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,40.0,"The 'networkAcls.defaultAction' property is set to 'Allow', which permits all network traffic (including from the public internet) to access the storage account unless explicitly denied. This creates a broad attack surface, enabling attackers to attempt brute force, enumeration, or exploitation from any network location. The blast radius is the entire storage account, as all data and services are exposed to unauthorized access.","Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only trusted networks or private endpoints. Example: defaultAction: 'Deny'. Additionally, configure 'bypass' and 'ipRules' or 'virtualNetworkRules' as needed for legitimate access.",,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 7,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 0,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T11:52:42.379030,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
