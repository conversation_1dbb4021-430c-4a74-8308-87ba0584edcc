File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
Parameters\LacpBillingExhaust.Parameters-LacpBillingExhaust.json,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'usageAccountSystemAssignedIdentityPrincipalId' flows across template boundary,"Secure parameter 'usageAccountSystemAssignedIdentityPrincipalId' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyVaultSuffix' flows across template boundary,"Secure parameter 'keyVaultSuffix' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'regionalCosmosAccountLocation' flows across template boundary,"Secure parameter 'regionalCosmosAccountLocation' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'dasStorageAccountKey' flows across template boundary,"Secure parameter 'dasStorageAccountKey' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'globalKeyVaultName' flows across template boundary,"Secure parameter 'globalKeyVaultName' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'createSharedKeyVault' flows across template boundary,"Secure parameter 'createSharedKeyVault' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,18.0,"The parameter 'lacpAadServicePrincipal' at line 18 contains a GUID value directly in the template, which may represent a sensitive identifier. Storing sensitive information such as service principal IDs directly in parameters can lead to information disclosure. ASB DP-3 requires that secrets and sensitive identifiers are not stored in code or parameter files.","Remove sensitive identifiers from the template and use Azure Key Vault references for secret or sensitive values. Update the template to reference the value securely via Key Vault.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",[Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references),Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.
Key Vault overview: https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Secret management: https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets
App Service Key Vault integration: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,30.0,"The parameter 'tenantId' at line 30 contains a GUID value directly in the template, which may represent a sensitive tenant identifier. Storing sensitive information such as tenant IDs directly in parameters can lead to information disclosure. ASB DP-3 requires that secrets and sensitive identifiers are not stored in code or parameter files.","Remove sensitive identifiers from the template and use Azure Key Vault references for secret or sensitive values. Update the template to reference the value securely via Key Vault.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",[Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references),Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.
Key Vault overview: https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Secret management: https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets
App Service Key Vault integration: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,34.0,"The parameter 'ev2BuildoutAppObjectId' at line 34 contains a GUID value directly in the template, which may represent a sensitive application object identifier. Storing sensitive information such as object IDs directly in parameters can lead to information disclosure. ASB DP-3 requires that secrets and sensitive identifiers are not stored in code or parameter files.","Remove sensitive identifiers from the template and use Azure Key Vault references for secret or sensitive values. Update the template to reference the value securely via Key Vault.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",[Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references),Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.
Key Vault overview: https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Secret management: https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets
App Service Key Vault integration: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,38.0,"The parameter 'lacpSecurityGroup' at line 38 contains a GUID value directly in the template, which may represent a sensitive security group identifier. Storing sensitive information such as security group IDs directly in parameters can lead to information disclosure. ASB DP-3 requires that secrets and sensitive identifiers are not stored in code or parameter files.","Remove sensitive identifiers from the template and use Azure Key Vault references for secret or sensitive values. Update the template to reference the value securely via Key Vault.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",[Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references),Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.
Key Vault overview: https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Secret management: https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets
App Service Key Vault integration: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,42.0,"The parameter 'azureDeployAppId' at line 42 contains a GUID value directly in the template, which may represent a sensitive application ID. Storing sensitive information such as application IDs directly in parameters can lead to information disclosure. ASB DP-3 requires that secrets and sensitive identifiers are not stored in code or parameter files.","Remove sensitive identifiers from the template and use Azure Key Vault references for secret or sensitive values. Update the template to reference the value securely via Key Vault.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",[Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references),Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.
Key Vault overview: https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Secret management: https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets
App Service Key Vault integration: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,66.0,"The parameter 'managedIdentitiesSecurityGroupId' at line 66 contains a GUID value directly in the template, which may represent a sensitive security group identifier. Storing sensitive information such as security group IDs directly in parameters can lead to information disclosure. ASB DP-3 requires that secrets and sensitive identifiers are not stored in code or parameter files.","Remove sensitive identifiers from the template and use Azure Key Vault references for secret or sensitive values. Update the template to reference the value securely via Key Vault.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",[Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references),Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.
Key Vault overview: https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Secret management: https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets
App Service Key Vault integration: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,78.0,"The parameter 'cosmosDbEsgPrincipalId' at line 78 contains a GUID value directly in the template, which may represent a sensitive principal identifier. Storing sensitive information such as principal IDs directly in parameters can lead to information disclosure. ASB DP-3 requires that secrets and sensitive identifiers are not stored in code or parameter files.","Remove sensitive identifiers from the template and use Azure Key Vault references for secret or sensitive values. Update the template to reference the value securely via Key Vault.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",[Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references),Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.
Key Vault overview: https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Secret management: https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets
App Service Key Vault integration: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,61.0,"The parameter 'dasStorageAccountKey' at line 61 contains a reference to a storage account key, which is a sensitive secret. Storing or passing secrets directly in parameters violates ASB DP-3: Manage sensitive information disclosure.","Remove the storage account key from the parameters file. Use Azure Key Vault references to securely retrieve secrets at deployment/runtime. Update your template to reference the Key Vault secret instead of passing the key directly.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",[Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references),Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.
Key Vault overview: https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Secret management: https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets
App Service Key Vault integration: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\IngestionStorageAccount.Template.json,DP-1,Data Protection,Enable encryption at rest,CRITICAL,32.0,Storage account resource at line 32 does not specify 'encryption' or 'encryption.services' properties. Azure Storage accounts must have encryption at rest explicitly enabled to comply with DP-1.,"Add the 'encryption' property with 'services' (blob, file) and 'keySource' set to 'Microsoft.Storage' or 'Microsoft.Keyvault' to the storage account resource definition.

📚 References: [Storage Service Encryption](https://docs.microsoft.com/en-us/azure/storage/common/storage-service-encryption) | [Transparent Data Encryption Tde Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-tde-overview) | [Database Encryption At Rest](https://docs.microsoft.com/en-us/azure/cosmos-db/database-encryption-at-rest)

🔵 Azure Guidance: Enable encryption for all storage accounts and databases.",[Storage Service Encryption](https://docs.microsoft.com/en-us/azure/storage/common/storage-service-encryption) | [Transparent Data Encryption Tde Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-tde-overview) | [Database Encryption At Rest](https://docs.microsoft.com/en-us/azure/cosmos-db/database-encryption-at-rest),Enable encryption for all storage accounts and databases.,"Use Azure Storage Service Encryption. Enable TDE for SQL databases.
Azure Storage encryption: https://docs.microsoft.com/en-us/azure/storage/common/storage-service-encryption
SQL TDE documentation: https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-tde-overview
Cosmos DB encryption: https://docs.microsoft.com/en-us/azure/cosmos-db/database-encryption-at-rest

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\IngestionStorageAccount.Template.json,DP-1,Data Protection,Enable encryption at rest,CRITICAL,54.0,Storage account resource at line 54 does not specify 'encryption' or 'encryption.services' properties. Azure Storage accounts must have encryption at rest explicitly enabled to comply with DP-1.,"Add the 'encryption' property with 'services' (blob, file) and 'keySource' set to 'Microsoft.Storage' or 'Microsoft.Keyvault' to the storage account resource definition.

📚 References: [Storage Service Encryption](https://docs.microsoft.com/en-us/azure/storage/common/storage-service-encryption) | [Transparent Data Encryption Tde Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-tde-overview) | [Database Encryption At Rest](https://docs.microsoft.com/en-us/azure/cosmos-db/database-encryption-at-rest)

🔵 Azure Guidance: Enable encryption for all storage accounts and databases.",[Storage Service Encryption](https://docs.microsoft.com/en-us/azure/storage/common/storage-service-encryption) | [Transparent Data Encryption Tde Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-tde-overview) | [Database Encryption At Rest](https://docs.microsoft.com/en-us/azure/cosmos-db/database-encryption-at-rest),Enable encryption for all storage accounts and databases.,"Use Azure Storage Service Encryption. Enable TDE for SQL databases.
Azure Storage encryption: https://docs.microsoft.com/en-us/azure/storage/common/storage-service-encryption
SQL TDE documentation: https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-tde-overview
Cosmos DB encryption: https://docs.microsoft.com/en-us/azure/cosmos-db/database-encryption-at-rest

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\IngestionStorageAccount.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,32.0,"Storage account resource at line 32 does not configure a private endpoint. To reduce attack surface, private endpoints should be used for all storage accounts.","Add a 'Microsoft.Network/privateEndpoints' resource referencing this storage account to restrict access to private networks only.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\IngestionStorageAccount.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,54.0,"Storage account resource at line 54 does not configure a private endpoint. To reduce attack surface, private endpoints should be used for all storage accounts.","Add a 'Microsoft.Network/privateEndpoints' resource referencing this storage account to restrict access to private networks only.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpBilling.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,393.0,"The Microsoft.Storage/storageAccounts resource does not configure a private endpoint. According to ASB NS-5, storage accounts should use Private Endpoints to reduce attack surface.","Add a Microsoft.Network/privateEndpoints resource referencing the storage account to ensure all access is via private endpoints. See: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpBillingExhaust.Template.json,DP-1,Data Protection,Enable encryption at rest,CRITICAL,34.0,Microsoft.Kusto/clusters resource at line 34 does not explicitly enable encryption at rest or specify customer-managed keys. Azure Security Benchmark DP-1 requires all sensitive data to be encrypted at rest.,"Configure the Microsoft.Kusto cluster to use encryption at rest by specifying the 'keyVaultProperties' property with a valid Key Vault URI and key name for customer-managed keys, or ensure platform-managed encryption is explicitly enabled.

📚 References: [Storage Service Encryption](https://docs.microsoft.com/en-us/azure/storage/common/storage-service-encryption) | [Transparent Data Encryption Tde Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-tde-overview) | [Database Encryption At Rest](https://docs.microsoft.com/en-us/azure/cosmos-db/database-encryption-at-rest)

🔵 Azure Guidance: Enable encryption for all storage accounts and databases.",[Storage Service Encryption](https://docs.microsoft.com/en-us/azure/storage/common/storage-service-encryption) | [Transparent Data Encryption Tde Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-tde-overview) | [Database Encryption At Rest](https://docs.microsoft.com/en-us/azure/cosmos-db/database-encryption-at-rest),Enable encryption for all storage accounts and databases.,"Use Azure Storage Service Encryption. Enable TDE for SQL databases.
Azure Storage encryption: https://docs.microsoft.com/en-us/azure/storage/common/storage-service-encryption
SQL TDE documentation: https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-tde-overview
Cosmos DB encryption: https://docs.microsoft.com/en-us/azure/cosmos-db/database-encryption-at-rest

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpBillingExhaust.Template.json,DP-2,Data Protection,Enable encryption in transit,CRITICAL,34.0,"Microsoft.Kusto/clusters resource at line 34 does not explicitly enforce encryption in transit (e.g., minimum TLS version or HTTPS-only). Azure Security Benchmark DP-2 requires data to be protected during transmission.","Set the 'enablePurge' property to true and configure the minimum TLS version (e.g., 'minimumTlsVersion': '1.2') in the Microsoft.Kusto cluster properties to enforce encryption in transit.

📚 References: [Configure Ssl Bindings](https://docs.microsoft.com/en-us/azure/app-service/configure-ssl-bindings) | [Tls Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/tls-best-practices) | [Storage Require Secure Transfer](https://docs.microsoft.com/en-us/azure/storage/common/storage-require-secure-transfer)

🔵 Azure Guidance: Use TLS 1.2+ for all data transfers.",[Configure Ssl Bindings](https://docs.microsoft.com/en-us/azure/app-service/configure-ssl-bindings) | [Tls Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/tls-best-practices) | [Storage Require Secure Transfer](https://docs.microsoft.com/en-us/azure/storage/common/storage-require-secure-transfer),Use TLS 1.2+ for all data transfers.,"Enable HTTPS-only. Configure minimum TLS version. Use secure protocols.
HTTPS enforcement: https://docs.microsoft.com/en-us/azure/app-service/configure-ssl-bindings
TLS best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/tls-best-practices
Storage secure transfer: https://docs.microsoft.com/en-us/azure/storage/common/storage-require-secure-transfer

This control is implemented through 6 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpBillingExhaust.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,34.0,Microsoft.Kusto/clusters resource at line 34 does not restrict public network access or configure private endpoints. Azure Security Benchmark NS-2 requires strict access control for public endpoints.,"Restrict public network access by setting 'publicNetworkAccess' to 'Disabled' and configure private endpoints for the Microsoft.Kusto cluster to limit exposure.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpBillingExhaust.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,34.0,Microsoft.Kusto/clusters resource at line 34 does not configure private endpoints. Azure Security Benchmark NS-5 recommends using private endpoints to reduce the attack surface.,"Add a private endpoint configuration to the Microsoft.Kusto cluster to ensure all access is via private IP addresses.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpBillingExhaustExport.Template.json,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,61.0,"The template defines sensitive connection URIs (adxExhaustUri, adxExhaustDataIngestionUri) as plain string parameters at line 10 and uses them directly in resource properties at line 61, which may expose secrets or sensitive information in code or deployment history. This violates DP-3: Manage sensitive information disclosure.","Store sensitive connection URIs in Azure Key Vault and reference them securely in the template using Key Vault references. Remove plain string parameters for secrets and update the template to retrieve these values from Key Vault.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",[Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references),Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.
Key Vault overview: https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Secret management: https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets
App Service Key Vault integration: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpGeo.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,143.0,"CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to 'false', exposing the database to the public internet without network restrictions.","Set 'publicNetworkAccess' to 'Disabled' or 'isVirtualNetworkFilterEnabled' to 'true' and define appropriate 'virtualNetworkRules' to restrict public access. Reference: ASB NS-2.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpGeo.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,143.0,"CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' does not configure a private endpoint, increasing the attack surface by allowing public access.","Configure a private endpoint for the CosmosDB account to restrict access to private networks only. Reference: ASB NS-5.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,54.0,"CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to 'false', exposing the database to the public internet without network restrictions.","Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to 'true'. Restrict access to required IPs or use Private Link to limit public exposure.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,54.0,"CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' does not configure a private endpoint, increasing the attack surface by allowing public access.","Configure a Private Endpoint for the CosmosDB account to ensure access is only available from within your private network.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,163.0,"Storage Account resource 'Microsoft.Storage/storageAccounts' does not restrict public network access or use network rules, potentially exposing data to the public internet.","Set 'networkAcls' with 'defaultAction' as 'Deny' and specify allowed subnets or IPs. Consider using Private Endpoints to restrict access.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,163.0,"Storage Account resource 'Microsoft.Storage/storageAccounts' does not configure a private endpoint, increasing the risk of unauthorized public access.","Configure a Private Endpoint for the Storage Account to restrict access to resources within your private network.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,210.0,"Key Vault resource 'Microsoft.KeyVault/vaults' does not restrict public network access or use network rules, potentially exposing secrets to the public internet.","Configure 'networkAcls' for the Key Vault with 'defaultAction' set to 'Deny' and specify allowed subnets or IPs. Consider using Private Endpoints to restrict access.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,210.0,"Key Vault resource 'Microsoft.KeyVault/vaults' does not configure a private endpoint, increasing the risk of unauthorized public access.","Configure a Private Endpoint for the Key Vault to restrict access to resources within your private network.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpRegion.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,1007.0,"CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' at line 1007 has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false, exposing the database to the public internet. This violates ASB NS-2: Protect public endpoints.","Set 'publicNetworkAccess' to 'Disabled' and enable 'isVirtualNetworkFilterEnabled' with appropriate 'virtualNetworkRules' to restrict access to only required networks. Reference: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpRegion.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,1007.0,"CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' at line 1007 has 'ipRules' set to an empty array, which does not restrict public access. This violates ASB NS-2: Protect public endpoints.","Configure 'ipRules' to allow only required IP addresses or ranges, or use Private Endpoints to eliminate public exposure. Reference: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpRegion.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,1007.0,"CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' at line 1007 does not use Private Endpoints, increasing the attack surface. This violates ASB NS-5: Use Private Endpoints.","Configure a Private Endpoint for the CosmosDB account to restrict access to private network traffic only. Reference: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpStamp.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,2223.0,"Microsoft.Network/trafficManagerProfiles resources at line 2223 expose public DNS endpoints without IP restrictions or WAF/App Gateway/Front Door. This exposes critical services to the public internet, violating NS-2 (Protect public endpoints).","Restrict public access to the Traffic Manager endpoints by using IP whitelisting, Azure Front Door, Application Gateway, or Private Link. Review endpoint exposure and apply access controls as per NS-2.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpStamp.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,2257.0,"Microsoft.Network/trafficManagerProfiles resources at line 2257 expose public DNS endpoints without IP restrictions or WAF/App Gateway/Front Door. This exposes critical services to the public internet, violating NS-2 (Protect public endpoints).","Restrict public access to the Traffic Manager endpoints by using IP whitelisting, Azure Front Door, Application Gateway, or Private Link. Review endpoint exposure and apply access controls as per NS-2.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpStamp.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,2462.0,"Microsoft.Storage/storageAccounts resources at line 2462 do not configure private endpoints, exposing storage accounts to public network access. This violates NS-5 (Use Private Endpoints).","Configure Private Endpoints for all storage accounts to restrict access to private networks only. Refer to Azure documentation for setting up Private Endpoints.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpStamp.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,2512.0,"Microsoft.Storage/storageAccounts resources at line 2512 do not configure private endpoints, exposing storage accounts to public network access. This violates NS-5 (Use Private Endpoints).","Configure Private Endpoints for all storage accounts to restrict access to private networks only. Refer to Azure documentation for setting up Private Endpoints.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpStamp.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,2577.0,"Microsoft.Storage/storageAccounts resources at line 2577 do not configure private endpoints, exposing storage accounts to public network access. This violates NS-5 (Use Private Endpoints).","Configure Private Endpoints for all storage accounts to restrict access to private networks only. Refer to Azure documentation for setting up Private Endpoints.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\ReadAdxExhaust.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,32.0,"The Microsoft.Kusto/clusters resource does not restrict public endpoints or specify access controls for public network exposure. By default, Kusto clusters are accessible over the public internet unless explicitly restricted.","Restrict public network access by configuring the 'publicNetworkAccess' property to 'Disabled' or by using IP-based access controls. Consider using Private Link or service endpoints to limit exposure. Reference: ASB NS-2.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\ReadAdxExhaust.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,32.0,"The Microsoft.Kusto/clusters resource does not configure a private endpoint, leaving the cluster accessible over the public network.","Configure a private endpoint for the Kusto cluster to ensure access is only available from within your private network. Reference: ASB NS-5.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\ReadIdentity.Template.json,IM-8,Identity Management,Use Managed Identities for Azure Resources,HIGH,15.0,"The variable 'userAssignedIdentityName' is set to an invalid value (""FUNCTION_ERROR_toLower""). This may prevent the correct configuration of a user-assigned managed identity, violating the requirement to use managed identities for Azure resources and avoid credential storage.","Correct the 'userAssignedIdentityName' variable to use a valid value or expression that resolves to the intended managed identity name. Ensure the managed identity is properly created and referenced in the template.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview#managed-identity-types) | [Qs Configure Portal Windows Vm](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/qs-configure-portal-windows-vm)

🔵 Azure Guidance: Enable system/user-assigned managed identities for Azure services.",[Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview#managed-identity-types) | [Qs Configure Portal Windows Vm](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/qs-configure-portal-windows-vm),Enable system/user-assigned managed identities for Azure services.,"Configure managed identities for VMs apps and Azure Functions. Use for Key Vault access.
Managed identities overview: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
System vs user-assigned: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview#managed-identity-types
VM managed identity setup: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/qs-configure-portal-windows-vm

This control is implemented through 3 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\ReadUsageAccount.Template.json,IM-8,Identity Management,Use Managed Identities for Azure Resources,HIGH,19.0,"The resource 'Microsoft.UsageBilling/accounts' at line 19 does not explicitly configure a managed identity. According to ASB IM-8, managed identities should be used to avoid storing credentials in code or configuration.","Add the 'identity' property to the resource definition to enable a system-assigned managed identity. Example: ""identity"": { ""type"": ""SystemAssigned"" }

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview#managed-identity-types) | [Qs Configure Portal Windows Vm](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/qs-configure-portal-windows-vm)

🔵 Azure Guidance: Enable system/user-assigned managed identities for Azure services.",[Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview#managed-identity-types) | [Qs Configure Portal Windows Vm](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/qs-configure-portal-windows-vm),Enable system/user-assigned managed identities for Azure services.,"Configure managed identities for VMs apps and Azure Functions. Use for Key Vault access.
Managed identities overview: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
System vs user-assigned: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview#managed-identity-types
VM managed identity setup: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/qs-configure-portal-windows-vm

This control is implemented through 3 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\RoleAssignment.Template.json,IM-8,Identity Management,Use Managed Identities for Azure Resources,MEDIUM,44.0,"Role assignment for 'Ev2BuildoutServicePrincipalId' grants Contributor access using a service principal. The template does not use a managed identity for this application identity, which is recommended to eliminate credential storage and improve security (IM-8).","Replace the service principal with a managed identity for the resource requiring Contributor access. Update the role assignment to use the managed identity's principalId.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview#managed-identity-types) | [Qs Configure Portal Windows Vm](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/qs-configure-portal-windows-vm)

🔵 Azure Guidance: Enable system/user-assigned managed identities for Azure services.",[Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview#managed-identity-types) | [Qs Configure Portal Windows Vm](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/qs-configure-portal-windows-vm),Enable system/user-assigned managed identities for Azure services.,"Configure managed identities for VMs apps and Azure Functions. Use for Key Vault access.
Managed identities overview: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
System vs user-assigned: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview#managed-identity-types
VM managed identity setup: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/qs-configure-portal-windows-vm

This control is implemented through 3 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\RoleAssignment.Template.json,IM-8,Identity Management,Use Managed Identities for Azure Resources,MEDIUM,52.0,"Role assignment for 'KeyVaultPrincipalId' grants Storage Account Key Operator Service Role access using a service principal. The template does not use a managed identity for this application identity, which is recommended to eliminate credential storage and improve security (IM-8).","Replace the service principal with a managed identity for the resource requiring Storage Account Key Operator Service Role access. Update the role assignment to use the managed identity's principalId.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview#managed-identity-types) | [Qs Configure Portal Windows Vm](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/qs-configure-portal-windows-vm)

🔵 Azure Guidance: Enable system/user-assigned managed identities for Azure services.",[Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview#managed-identity-types) | [Qs Configure Portal Windows Vm](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/qs-configure-portal-windows-vm),Enable system/user-assigned managed identities for Azure services.,"Configure managed identities for VMs apps and Azure Functions. Use for Key Vault access.
Managed identities overview: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
System vs user-assigned: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview#managed-identity-types
VM managed identity setup: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/qs-configure-portal-windows-vm

This control is implemented through 3 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\TrafficManagerEndpoints.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,38.0,"The resource 'Microsoft.Network/trafficManagerProfiles/externalEndpoints' at line 38 is configured as a public endpoint ('location': 'global') without any explicit access restrictions. This exposes the endpoint to the public internet, violating NS-2 which requires strict access control for public endpoints.","Restrict public access to the Traffic Manager external endpoint by configuring endpoint access controls, using IP whitelisting, or integrating with Azure Front Door or Application Gateway. Consider using Private Link or Service Endpoints to limit exposure. Reference: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 47,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 6,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-17T16:20:29.509291,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
