Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
CRITICAL,DP-1,IngestionStorageAccount.Template.json,38,Storage account resource at line 38 does not explicitly enable encryption at rest. The 'encryption' property is missing in the resource definition.,"Add the 'encryption' property to the storage account resource at line 38, specifying 'services', 'keySource', and optionally 'keyVaultProperties' to ensure encryption at rest is enabled.",N/A,AI,Generic
CRITICAL,DP-1,IngestionStorageAccount.Template.json,59,Storage account resource at line 59 does not explicitly enable encryption at rest. The 'encryption' property is missing in the resource definition.,"Add the 'encryption' property to the storage account resource at line 59, specifying 'services', 'keySource', and optionally 'keyVaultProperties' to ensure encryption at rest is enabled.",N/A,AI,Generic
CRITICAL,NS-1,IngestionStorageAccount.Template.json,38,Storage account resource at line 38 does not restrict network access using network security groups (NSGs) or Azure Firewall. No 'networkAcls' property is defined.,Add the 'networkAcls' property to the storage account resource at line 38 to restrict access to trusted networks and subnets using NSGs or Azure Firewall.,N/A,AI,Generic
CRITICAL,NS-1,IngestionStorageAccount.Template.json,59,Storage account resource at line 59 does not restrict network access using network security groups (NSGs) or Azure Firewall. No 'networkAcls' property is defined.,Add the 'networkAcls' property to the storage account resource at line 59 to restrict access to trusted networks and subnets using NSGs or Azure Firewall.,N/A,AI,Generic
CRITICAL,NS-2,IngestionStorageAccount.Template.json,38,"Storage account resource at line 38 does not restrict public network access. The 'networkAcls' property is missing, which may allow public endpoints.",Add the 'networkAcls' property to the storage account resource at line 38 and set 'defaultAction' to 'Deny' to restrict public access.,N/A,AI,Generic
CRITICAL,NS-2,IngestionStorageAccount.Template.json,59,"Storage account resource at line 59 does not restrict public network access. The 'networkAcls' property is missing, which may allow public endpoints.",Add the 'networkAcls' property to the storage account resource at line 59 and set 'defaultAction' to 'Deny' to restrict public access.,N/A,AI,Generic
CRITICAL,NS-3,IngestionStorageAccount.Template.json,38,Storage account resource at line 38 does not implement Network Security Groups (NSGs) to control inbound and outbound traffic. No 'networkAcls' property is defined.,Add the 'networkAcls' property to the storage account resource at line 38 and configure appropriate virtual network rules to enforce NSG controls.,N/A,AI,Generic
CRITICAL,NS-3,IngestionStorageAccount.Template.json,59,Storage account resource at line 59 does not implement Network Security Groups (NSGs) to control inbound and outbound traffic. No 'networkAcls' property is defined.,Add the 'networkAcls' property to the storage account resource at line 59 and configure appropriate virtual network rules to enforce NSG controls.,N/A,AI,Generic
CRITICAL,NS-1,LacpBilling.Template.json,3932,"Microsoft.Storage/storageAccounts resource at line 3932 does not specify network rules (such as networkAcls, private endpoints, or service endpoints). This exposes the storage account to potential unrestricted network access, violating ASB NS-1.","Add the 'networkAcls' property to the storage account resource to restrict access to selected networks and subnets, or configure private endpoints to limit exposure.",N/A,AI,Generic
CRITICAL,NS-2,LacpBilling.Template.json,3932,"Microsoft.Storage/storageAccounts resource at line 3932 does not restrict public network access. Absence of explicit networkAcls or publicNetworkAccess settings may allow public endpoints, violating ASB NS-2.",Set 'publicNetworkAccess' to 'Disabled' and configure 'networkAcls' to restrict access to trusted networks only.,N/A,AI,Generic
CRITICAL,NS-1,LacpBillingExhaust.Template.json,27,"Microsoft.Kusto/clusters resource at line 27 does not specify any network security group (NSG) or Azure Firewall configuration to restrict access. This exposes the cluster to potential unauthorized network access, violating ASB NS-1.",Configure the Microsoft.Kusto cluster to use private endpoints and associate it with a subnet protected by a Network Security Group (NSG) or Azure Firewall. Restrict inbound and outbound traffic to only required sources and destinations.,N/A,AI,Generic
CRITICAL,NS-2,LacpBillingExhaust.Template.json,27,"Microsoft.Kusto/clusters resource at line 27 does not restrict public endpoints. No configuration is present to disable or secure public network access, violating ASB NS-2.",Disable public network access for the Microsoft.Kusto cluster by setting 'publicNetworkAccess' to 'Disabled' and use private endpoints for all access.,N/A,AI,Generic
CRITICAL,NS-3,LacpBillingExhaust.Template.json,27,"Microsoft.Kusto/clusters resource at line 27 is not associated with any Network Security Group (NSG), violating ASB NS-3.",Deploy the Microsoft.Kusto cluster within a subnet that is protected by a Network Security Group (NSG) with rules that restrict access to only trusted IP ranges.,N/A,AI,Generic
CRITICAL,DP-1,LacpBillingExhaust.Template.json,27,"Microsoft.Kusto/clusters resource at line 27 does not explicitly enable encryption at rest. No 'encryption' property or key vault integration is specified, violating ASB DP-1.","Enable encryption at rest for the Microsoft.Kusto cluster by specifying the 'encryption' property and, if required, integrating with a customer-managed key in Azure Key Vault.",N/A,AI,Generic
CRITICAL,DP-2,LacpBillingExhaust.Template.json,27,"Microsoft.Kusto/clusters resource at line 27 does not explicitly enforce encryption in transit (TLS 1.2+). No configuration is present to require secure transport, violating ASB DP-2.",Configure the Microsoft.Kusto cluster to require TLS 1.2 or higher for all data in transit by setting the appropriate 'enablePurge' and 'enableDoubleEncryption' properties or equivalent.,N/A,AI,Generic
CRITICAL,DP-3,LacpBillingExhaust.Template.json,27,"Microsoft.Kusto/clusters resource at line 27 does not specify secure storage of sensitive information such as keys or secrets. No Azure Key Vault integration is present, violating ASB DP-3.","Store all sensitive information, such as keys and secrets, in Azure Key Vault and reference them securely in the template using Key Vault references.",N/A,AI,Generic
CRITICAL,DP-3,LacpBillingExhaustExport.Template.json,74,"The template references sensitive connection URIs (adxExhaustUri, adxExhaustDataIngestionUri) directly as parameters in the 'adxParameters' object for the 'Microsoft.UsageBilling/accounts/dataExports' resource. These sensitive values are not integrated with Azure Key Vault, violating DP-3: Manage sensitive information disclosure.",Store sensitive connection URIs in Azure Key Vault and reference them securely in the template using Key Vault references. Update the template to retrieve these values from Key Vault instead of passing them as plain parameters.,N/A,AI,Generic
CRITICAL,NS-1,LacpGeo.Template.json,74,"Key Vault resource 'Microsoft.KeyVault/vaults' at line 74 does not restrict network access using network security groups (NSGs) or Azure Firewall. No network rules are defined, which may expose the Key Vault to public access.","Configure the Key Vault to use private endpoints or set network ACLs to restrict access to trusted networks only. Add 'networkAcls' property with appropriate 'virtualNetworkRules' and 'ipRules', and set 'defaultAction' to 'Deny'.",N/A,AI,Generic
CRITICAL,NS-2,LacpGeo.Template.json,153,"Cosmos DB account 'Microsoft.DocumentDB/databaseAccounts' at line 153 has 'publicNetworkAccess' set to 'Enabled', exposing the database account to the public internet.",Set the 'publicNetworkAccess' property to 'Disabled' to prevent public access. Use private endpoints to securely access the Cosmos DB account.,N/A,AI,Generic
CRITICAL,NS-1,LacpGeo.Template.json,153,"Cosmos DB account 'Microsoft.DocumentDB/databaseAccounts' at line 153 does not have virtual network filters enabled ('isVirtualNetworkFilterEnabled' is false), which allows unrestricted network access.",Set 'isVirtualNetworkFilterEnabled' to true and define 'virtualNetworkRules' to restrict access to trusted subnets only.,N/A,AI,Generic
CRITICAL,NS-1,LacpGlobal.Template.json,54,"CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' at line 54 has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false, exposing the database to the public internet without network security groups or Azure Firewall.","Set 'publicNetworkAccess' to 'Disabled' or 'isVirtualNetworkFilterEnabled' to true, and restrict access using network security groups or Azure Firewall as per ASB NS-1.",N/A,AI,Generic
CRITICAL,NS-2,LacpGlobal.Template.json,54,CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' at line 54 exposes a public endpoint by setting 'publicNetworkAccess' to 'Enabled' and not restricting access via 'ipRules' or virtual network rules.,Disable public network access by setting 'publicNetworkAccess' to 'Disabled' or configure 'ipRules' and 'virtualNetworkRules' to restrict access as per ASB NS-2.,N/A,AI,Generic
CRITICAL,NS-3,LacpGlobal.Template.json,54,CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' at line 54 does not implement Network Security Groups (NSGs) to control inbound and outbound traffic.,Apply NSGs to the subnet where CosmosDB is deployed or use virtual network service endpoints to restrict traffic as per ASB NS-3.,N/A,AI,Generic
CRITICAL,NS-1,LacpGlobal.Template.json,143,"Storage Account resource 'Microsoft.Storage/storageAccounts' at line 143 does not specify network rules, NSGs, or Azure Firewall, leaving it potentially exposed to public access.","Restrict network access to the storage account using network rules, NSGs, or Azure Firewall as per ASB NS-1.",N/A,AI,Generic
CRITICAL,NS-2,LacpGlobal.Template.json,143,"Storage Account resource 'Microsoft.Storage/storageAccounts' at line 143 does not restrict public endpoints, which may allow public access.","Restrict public access by configuring 'networkAcls' with appropriate 'ipRules' or 'virtualNetworkRules', and consider using private endpoints as per ASB NS-2.",N/A,AI,Generic
CRITICAL,NS-3,LacpGlobal.Template.json,143,Storage Account resource 'Microsoft.Storage/storageAccounts' at line 143 does not implement Network Security Groups (NSGs) to control traffic.,Apply NSGs to the subnet where the storage account is accessed or use service endpoints/private endpoints as per ASB NS-3.,N/A,AI,Generic
CRITICAL,NS-1,LacpGlobal.Template.json,180,"Key Vault resource 'Microsoft.KeyVault/vaults' at line 180 does not specify network rules, NSGs, or Azure Firewall, leaving it potentially exposed to public access.","Restrict network access to the Key Vault using network rules, NSGs, or Azure Firewall as per ASB NS-1.",N/A,AI,Generic
CRITICAL,NS-2,LacpGlobal.Template.json,180,"Key Vault resource 'Microsoft.KeyVault/vaults' at line 180 does not restrict public endpoints, which may allow public access.","Restrict public access by configuring 'networkAcls' with appropriate 'ipRules' or 'virtualNetworkRules', and consider using private endpoints as per ASB NS-2.",N/A,AI,Generic
CRITICAL,NS-3,LacpGlobal.Template.json,180,Key Vault resource 'Microsoft.KeyVault/vaults' at line 180 does not implement Network Security Groups (NSGs) to control traffic.,Apply NSGs to the subnet where the Key Vault is accessed or use service endpoints/private endpoints as per ASB NS-3.,N/A,AI,Generic
CRITICAL,NS-1,LacpRegion.Template.json,1042,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1042 has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false, exposing the database to the public internet without network security group or firewall protection.","Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to true. Add appropriate 'virtualNetworkRules' to restrict access to trusted networks only, and use NSGs or Azure Firewall to protect the resource.",N/A,AI,Generic
CRITICAL,NS-2,LacpRegion.Template.json,1042,CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1042 exposes a public endpoint by setting 'publicNetworkAccess' to 'Enabled' and not enabling virtual network filtering.,Set 'publicNetworkAccess' to 'Disabled' and enable 'isVirtualNetworkFilterEnabled'. Use private endpoints to restrict access to the CosmosDB account.,N/A,AI,Generic
CRITICAL,DP-3,LacpStamp.Parameters-LacpStampResources.json,65,"The parameter 'dasStorageAccountKey' at line 65 contains a storage account key value reference, which is sensitive information. Storing sensitive data such as storage account keys directly in parameters violates ASB DP-3, which requires sensitive information to be stored in Azure Key Vault.",Store the storage account key in Azure Key Vault and reference it securely in the template using a Key Vault reference. Remove direct storage of sensitive keys in parameters.,N/A,AI,Generic
CRITICAL,NS-1,LacpStamp.Template.json,563,"Microsoft.Storage/storageAccounts resource at line 563 does not specify any network security group (NSG), firewall rules, or network rules to restrict access. Storage accounts should be protected using NSGs or Azure Firewall to prevent unauthorized access.","Add networkAcls property to the storage account resource to restrict access to trusted networks and subnets, or associate the storage account with an NSG or Azure Firewall.",N/A,AI,Generic
CRITICAL,NS-1,LacpStamp.Template.json,601,"Microsoft.Storage/storageAccounts resource at line 601 does not specify any network security group (NSG), firewall rules, or network rules to restrict access. Storage accounts should be protected using NSGs or Azure Firewall to prevent unauthorized access.","Add networkAcls property to the storage account resource to restrict access to trusted networks and subnets, or associate the storage account with an NSG or Azure Firewall.",N/A,AI,Generic
CRITICAL,NS-1,LacpStamp.Template.json,639,"Microsoft.Storage/storageAccounts resource at line 639 does not specify any network security group (NSG), firewall rules, or network rules to restrict access. Storage accounts should be protected using NSGs or Azure Firewall to prevent unauthorized access.","Add networkAcls property to the storage account resource to restrict access to trusted networks and subnets, or associate the storage account with an NSG or Azure Firewall.",N/A,AI,Generic
CRITICAL,NS-20,LacpStamp.Template.json,563,"Microsoft.Storage/storageAccounts resource at line 563 does not configure private endpoints or restrict public network access. Public endpoints are exposed by default, increasing risk of unauthorized access.",Configure the storage account to disable public network access and use private endpoints for secure access.,N/A,AI,Generic
CRITICAL,NS-20,LacpStamp.Template.json,601,"Microsoft.Storage/storageAccounts resource at line 601 does not configure private endpoints or restrict public network access. Public endpoints are exposed by default, increasing risk of unauthorized access.",Configure the storage account to disable public network access and use private endpoints for secure access.,N/A,AI,Generic
CRITICAL,NS-20,LacpStamp.Template.json,639,"Microsoft.Storage/storageAccounts resource at line 639 does not configure private endpoints or restrict public network access. Public endpoints are exposed by default, increasing risk of unauthorized access.",Configure the storage account to disable public network access and use private endpoints for secure access.,N/A,AI,Generic
CRITICAL,NS-1,LacpStamp.Template.json,823,"Microsoft.KeyVault/vaults resource at line 823 does not specify any network security group (NSG), firewall rules, or network rules to restrict access. Key Vaults should be protected using NSGs or Azure Firewall to prevent unauthorized access.","Add networkAcls property to the Key Vault resource to restrict access to trusted networks and subnets, or associate the Key Vault with an NSG or Azure Firewall.",N/A,AI,Generic
CRITICAL,NS-20,LacpStamp.Template.json,823,"Microsoft.KeyVault/vaults resource at line 823 does not configure private endpoints or restrict public network access. Public endpoints are exposed by default, increasing risk of unauthorized access.",Configure the Key Vault to disable public network access and use private endpoints for secure access.,N/A,AI,Generic
CRITICAL,NS-1,LacpStamp.Template.json,900,"Microsoft.KeyVault/vaults resource at line 900 does not specify any network security group (NSG), firewall rules, or network rules to restrict access. Key Vaults should be protected using NSGs or Azure Firewall to prevent unauthorized access.","Add networkAcls property to the Key Vault resource to restrict access to trusted networks and subnets, or associate the Key Vault with an NSG or Azure Firewall.",N/A,AI,Generic
CRITICAL,NS-20,LacpStamp.Template.json,900,"Microsoft.KeyVault/vaults resource at line 900 does not configure private endpoints or restrict public network access. Public endpoints are exposed by default, increasing risk of unauthorized access.",Configure the Key Vault to disable public network access and use private endpoints for secure access.,N/A,AI,Generic
CRITICAL,NS-1,ReadAdxExhaust.Template.json,23,Microsoft.Kusto/clusters resource does not specify any network security group (NSG) or Azure Firewall configuration to restrict access. This exposes the resource to potential unauthorized network access.,Configure the Microsoft.Kusto/clusters resource to use a private endpoint or associate it with a subnet protected by a Network Security Group (NSG) or Azure Firewall. Restrict inbound and outbound traffic to only required sources and destinations.,N/A,AI,Generic
CRITICAL,NS-2,ReadAdxExhaust.Template.json,23,"Microsoft.Kusto/clusters resource does not restrict public network access or specify publicNetworkAccess property. By default, the cluster may be accessible from the public internet.",Set the 'publicNetworkAccess' property to 'Disabled' or configure private endpoints to ensure the cluster is not accessible from the public internet.,N/A,AI,Generic
CRITICAL,NS-3,ReadAdxExhaust.Template.json,23,"Microsoft.Kusto/clusters resource is not associated with any Network Security Group (NSG), leaving network traffic unfiltered.",Deploy the cluster within a subnet that is protected by a Network Security Group (NSG) and define rules to restrict access to only trusted sources.,N/A,AI,Generic
CRITICAL,NS-10,TrafficManagerEndpoints.Template.json,38,The resource 'Microsoft.Network/trafficManagerProfiles/externalEndpoints' defines a public endpoint without explicit security controls to minimize exposure. Public endpoints should be secured to prevent unauthorized access.,"Restrict access to the external endpoint by implementing IP whitelisting, authentication, or moving to private endpoints where possible. Review and secure the 'target' property to ensure only authorized traffic is allowed.",N/A,AI,Generic
HIGH,NS-5,LacpGeo.Template.json,153,Cosmos DB account 'Microsoft.DocumentDB/databaseAccounts' at line 153 does not use private endpoints. 'publicNetworkAccess' is enabled and no private endpoint configuration is present.,"Implement a private endpoint for the Cosmos DB account to ensure secure, private connectivity from within your virtual network.",N/A,AI,Generic
HIGH,NS-5,LacpGlobal.Template.json,54,"CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' at line 54 does not use private endpoints, increasing exposure risk.",Configure a private endpoint for the CosmosDB account to restrict access to private networks as per ASB NS-5.,N/A,AI,Generic
HIGH,NS-5,LacpGlobal.Template.json,143,"Storage Account resource 'Microsoft.Storage/storageAccounts' at line 143 does not use private endpoints, increasing exposure risk.",Configure a private endpoint for the storage account to restrict access to private networks as per ASB NS-5.,N/A,AI,Generic
HIGH,NS-5,LacpGlobal.Template.json,180,"Key Vault resource 'Microsoft.KeyVault/vaults' at line 180 does not use private endpoints, increasing exposure risk.",Configure a private endpoint for the Key Vault to restrict access to private networks as per ASB NS-5.,N/A,AI,Generic
HIGH,NS-22,LacpRegion.Template.json,1042,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1042 does not use private endpoints, increasing the risk of unauthorized access.","Configure a private endpoint for the CosmosDB account to ensure secure, private connectivity from within your virtual network.",N/A,AI,Generic
HIGH,NS-23,LacpStamp.Template.json,563,Microsoft.Storage/storageAccounts resource at line 563 does not implement private endpoints. Access to the storage account is not restricted to private networks.,Add a private endpoint resource for the storage account to restrict access to resources within a virtual network.,N/A,AI,Generic
HIGH,NS-23,LacpStamp.Template.json,601,Microsoft.Storage/storageAccounts resource at line 601 does not implement private endpoints. Access to the storage account is not restricted to private networks.,Add a private endpoint resource for the storage account to restrict access to resources within a virtual network.,N/A,AI,Generic
HIGH,NS-23,LacpStamp.Template.json,639,Microsoft.Storage/storageAccounts resource at line 639 does not implement private endpoints. Access to the storage account is not restricted to private networks.,Add a private endpoint resource for the storage account to restrict access to resources within a virtual network.,N/A,AI,Generic
HIGH,NS-23,LacpStamp.Template.json,823,Microsoft.KeyVault/vaults resource at line 823 does not implement private endpoints. Access to the Key Vault is not restricted to private networks.,Add a private endpoint resource for the Key Vault to restrict access to resources within a virtual network.,N/A,AI,Generic
HIGH,NS-23,LacpStamp.Template.json,900,Microsoft.KeyVault/vaults resource at line 900 does not implement private endpoints. Access to the Key Vault is not restricted to private networks.,Add a private endpoint resource for the Key Vault to restrict access to resources within a virtual network.,N/A,AI,Generic
HIGH,AM-1,RoleAssignment.Template.json,49,"Role assignment at line 49 grants 'Contributor' role to the 'Ev2BuildoutServicePrincipalId' principal. The Contributor role provides broad permissions, which may exceed least privilege requirements.","Review the permissions required by 'Ev2BuildoutServicePrincipalId' and assign a more restrictive, custom role with only the necessary permissions as per the principle of least privilege.",N/A,AI,Generic
