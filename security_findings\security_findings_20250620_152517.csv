File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
app-config.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyValues' flows across template boundary,Secure parameter 'keyValues' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'app_insights_key' flows across template boundary,Secure parameter 'app_insights_key' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyvault_name' flows across template boundary,Secure parameter 'keyvault_name' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
keyvault.bicep,NS-1,Network Security,Establish network segmentation boundaries,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,Review template dependencies and ensure secure communication between templates,,,,cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'secrets' flows across template boundary,Secure parameter 'secrets' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
storage-accounts.bicep,NS-1,Network Security,Establish network segmentation boundaries,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,Review template dependencies and ensure secure communication between templates,,,,cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,DP-6,Data Protection,Use a secure key management process,HIGH,1.0,Parameter flow security risk: Sensitive parameters may be exposed through template dependencies,Use Key Vault references for sensitive parameters and validate parameter flow security,,,,cross_reference_analysis,parameter_flow,Validated
function.bicep,NS-8,Network Security,Detect and disable insecure services and protocols,HIGH,48.0,"The 'ftpsState' property is set to 'Disabled' in the App Service site configuration (Line 048). Disabling FTPS allows only unencrypted FTP connections, which exposes credentials and data in transit to interception and manipulation by attackers. This enables credential theft, lateral movement, and data exfiltration, significantly increasing the blast radius if an attacker gains network access.","Set 'ftpsState' to 'FtpsOnly' in the site configuration to enforce encrypted FTPS connections. This ensures all file transfers are protected with TLS, mitigating credential theft and data interception risks. Example: ftpsState: 'FtpsOnly'.",,,,ai_analysis,,Validated
hub-network.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,14.0,"The subnet 'hub-subnet' defined at line 14 does not specify a networkSecurityGroup property. Without an associated Network Security Group (NSG), the subnet lacks explicit traffic filtering, enabling potential attack vectors such as unrestricted lateral movement, initial access via open ports, and increased blast radius if a resource is compromised. Attackers could exploit this to move laterally within the VNet or access resources without restriction.",Associate a Network Security Group (NSG) with the 'hub-subnet' by adding the 'networkSecurityGroup' property to the subnet definition. Define restrictive inbound and outbound rules to enforce a deny-by-default policy and only allow required traffic. Example: 'networkSecurityGroup: { id: <resourceId of NSG> }'.,,,,ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,4.0,"The 'corpNetIps' variable on line 4 includes the IP range '*******/8', which is an extremely broad public IP range. Allowing such a wide range in network rules can enable initial access for attackers from a vast portion of the public internet, bypassing segmentation boundaries and increasing the blast radius for lateral movement and data exfiltration. This configuration directly violates network segmentation and isolation principles.","Restrict allowed IP ranges in 'corpNetIps' to only trusted, well-defined corporate or private network ranges. Remove or replace '*******/8' with specific, minimal IP ranges that are under organizational control. Review all entries in 'corpNetIps' to ensure they do not include broad or public IP ranges. Apply network security groups (NSGs) and deny-by-default policies to enforce segmentation as per ASB NS-1.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,5.0,"The 'corpNetIps' variable on line 5 includes the IP range '********/8', which is a large public IP block. Allowing this range in network rules exposes resources to a significant portion of the public internet, enabling attackers to bypass segmentation and increasing the risk of unauthorized access, lateral movement, and data compromise.","Limit allowed IP ranges in 'corpNetIps' to only trusted, organization-controlled addresses. Remove '********/8' and replace with specific, minimal IP ranges. Ensure all network rules enforce least privilege and segmentation boundaries as required by ASB NS-1.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,6.0,"The 'corpNetIps' variable on line 6 includes the IP range '20.0.0.0/8', which is a large public IP block. This configuration allows access from a vast range of public addresses, significantly increasing the attack surface and enabling potential initial access and lateral movement by threat actors.","Remove '20.0.0.0/8' from 'corpNetIps' and restrict allowed IPs to only those under direct organizational control. Use network segmentation and deny-by-default policies to minimize exposure, as required by ASB NS-1.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,7.0,"The 'corpNetIps' variable on line 7 includes the IP range '40.0.0.0/8', which is a large public IP block. Allowing this range in network rules exposes resources to a wide range of public IPs, increasing the risk of unauthorized access and lateral movement.","Remove '40.0.0.0/8' from 'corpNetIps' and replace with specific, trusted IP ranges. Enforce network segmentation and least privilege access as per ASB NS-1.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,8.0,"The 'corpNetIps' variable on line 8 includes the IP range '********/8', which is a large public IP block. This configuration increases the attack surface by allowing access from a broad range of public addresses, violating segmentation and isolation requirements.",Remove '********/8' from 'corpNetIps' and restrict allowed IPs to only those under organizational control. Apply deny-by-default and segmentation policies as required by ASB NS-1.,,,,ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,9.0,"The 'corpNetIps' variable on line 9 includes the IP range '********/8', which is a large public IP block. Allowing this range in network rules exposes resources to a significant portion of the public internet, enabling potential initial access and lateral movement.","Remove '********/8' from 'corpNetIps' and replace with specific, minimal IP ranges under organizational control. Enforce network segmentation and least privilege as per ASB NS-1.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,10.0,"The 'corpNetIps' variable on line 10 includes the IP range '********/8', which is a large public IP block. This configuration allows access from a vast range of public addresses, increasing the risk of unauthorized access and lateral movement.",Remove '********/8' from 'corpNetIps' and restrict allowed IPs to only those under direct organizational control. Apply network segmentation and deny-by-default policies as required by ASB NS-1.,,,,ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,11.0,"The 'corpNetIps' variable on line 11 includes the IP range '70.0.0.0/8', which is a large public IP block. Allowing this range in network rules exposes resources to a wide range of public IPs, increasing the risk of unauthorized access and lateral movement.","Remove '70.0.0.0/8' from 'corpNetIps' and replace with specific, trusted IP ranges. Enforce network segmentation and least privilege access as per ASB NS-1.",,,,ai_analysis,,Validated
keyvault.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,28.0,"The 'networkAcls.defaultAction' property is set to 'Allow' on the Key Vault resource. This configuration allows all network traffic to access the Key Vault unless explicitly denied, enabling attackers to access sensitive secrets and keys over the network. Without enforced network boundaries and secure transfer requirements, data in transit is at risk of interception or unauthorized access, increasing the blast radius to any entity with network access.",Set 'networkAcls.defaultAction' to 'Deny' to restrict access to only explicitly allowed networks. Ensure that only trusted IPs and subnets are permitted via 'ipRules' and 'virtualNetworkRules'. This enforces network boundaries and reduces the risk of unauthorized data access in transit.,,,,ai_analysis,,Validated
keyvault.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,29.0,"The 'networkAcls.bypass' property is set to 'AzureServices', which allows all Azure services to bypass network restrictions and access the Key Vault. This configuration enables lateral movement and data exfiltration by any Azure service, expanding the attack surface and increasing the potential blast radius if any Azure service is compromised.","Set 'networkAcls.bypass' to 'None' to prevent all Azure services from bypassing network restrictions. Only allow specific, trusted services or identities access to the Key Vault through explicit access policies or RBAC assignments.",,,,ai_analysis,,Validated
keyvault.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,20.0,"The 'enabledForDeployment' property is set to 'true', allowing Azure Resource Manager (ARM) deployments to access the Key Vault. This can enable privilege escalation or data exfiltration if an attacker gains access to deployment pipelines or automation accounts, as they could retrieve secrets during deployments.","Set 'enabledForDeployment' to 'false' unless there is a strict business requirement. If enabled, ensure that only trusted deployment identities have access to the Key Vault and that all deployment activities are monitored and logged.",,,,ai_analysis,,Validated
keyvault.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,21.0,"The 'enabledForTemplateDeployment' property is set to 'true', allowing Azure Resource Manager (ARM) template deployments to access the Key Vault. This increases the risk of unauthorized access to secrets if deployment pipelines or templates are compromised, enabling attackers to retrieve sensitive data during infrastructure provisioning.","Set 'enabledForTemplateDeployment' to 'false' unless absolutely necessary. If required, restrict access to trusted deployment identities and implement strict monitoring and logging of all template deployment activities.",,,,ai_analysis,,Validated
scaleset-networks.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,66.0,"The 'defaultOutboundAccess' property is set to true on the subnet configuration (Line 066: defaultOutboundAccess: true). This enables default outbound internet connectivity for the subnet, which can be exploited by attackers for data exfiltration, command and control, or lateral movement. This configuration increases the blast radius by allowing all resources in the subnet to initiate outbound connections to the internet without restriction, bypassing network segmentation and deny-by-default principles.","Set 'defaultOutboundAccess' to false in the subnet configuration to disable unrestricted outbound internet access. Instead, explicitly define required outbound connectivity using NAT Gateway or controlled firewall rules, and associate a Network Security Group (NSG) with the subnet to enforce a deny-by-default policy. Example: defaultOutboundAccess: false",,,,ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,168.0,"The 'defaultAction' property in 'networkAcls' for the 'storageAccountFunc' resource is set to 'Allow' (Line 031). This configuration allows public network access to the storage account, enabling attackers to access storage resources from any network not explicitly denied. This significantly increases the attack surface for initial access, data exfiltration, and lateral movement, as the storage account is not restricted to private endpoints or specific networks.","Set 'networkAcls.defaultAction' to 'Deny' on Line 031 to block public network access. Only allow access via explicit 'ipRules' or 'virtualNetworkRules'. Additionally, consider enabling private endpoints for the storage account to further restrict access to trusted networks, following Azure Security Benchmark NS-2 guidance.",,,,ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,29.0,"The 'defaultAction' property in 'networkAcls' for the 'fuzzStorageProperties' object is set to 'Allow' (Line 065), which is used by multiple storage accounts. This configuration allows public network access to these storage accounts, enabling attackers to access storage resources from any network not explicitly denied. This exposes all storage accounts using 'fuzzStorageProperties' to potential data exfiltration and unauthorized access.","Set 'networkAcls.defaultAction' to 'Deny' on Line 065 to block public network access. Only allow access via explicit 'ipRules' or 'virtualNetworkRules'. Additionally, implement private endpoints for all storage accounts using 'fuzzStorageProperties' to restrict access to trusted networks, as per Azure Security Benchmark NS-2.",,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 24,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 7,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T15:25:17.116726,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
