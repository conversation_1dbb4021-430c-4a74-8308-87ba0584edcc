Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P1-Identity-HIGH,Identity Management,HIGH,IM-1,template.json,180,"App Service does not explicitly integrate with Azure Active Directory for identity management, violating IM-1 (Use Azure Active Directory for Identity Management).",Enable Azure Active Directory authentication for the App Service.,N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-2,template.json,180,"App Service does not enforce Multi-Factor Authentication (MFA) for users or administrators, violating IM-2 (Enable Multi-Factor Authentication).",Configure Azure Active Directory authentication with MFA enforcement for the App Service.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,180,"App Service config 'ipSecurityRestrictions' allows 'Any' IP with action 'Allow', exposing the app publicly and violating NS-2 (Protect public endpoints).",Restrict 'ipSecurityRestrictions' to only allow trusted IP ranges. Remove or replace the 'Any' rule with specific IPs or ranges.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,189,"App Service config 'scmIpSecurityRestrictions' allows 'Any' IP with action 'Allow', exposing the SCM endpoint publicly and violating NS-2 (Protect public endpoints).",Restrict 'scmIpSecurityRestrictions' to only allow trusted IP ranges. Remove or replace the 'Any' rule with specific IPs or ranges.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,180,"App Service config 'publicNetworkAccess' is set to 'Enabled', not using private endpoints, violating NS-5 (Use Private Endpoints).",Set 'publicNetworkAccess' to 'Disabled' and configure a private endpoint for the App Service.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,180,"App Service config 'azureStorageAccounts' is empty and there is no explicit configuration for encryption at rest, violating DP-1 (Enable encryption at rest).",Ensure all storage accounts and data stores used by the App Service have encryption at rest enabled. Configure 'azureStorageAccounts' with encryption settings if used.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,54,"App Service hostNameSslStates: SSL is disabled for 'onefuzz-daily-ui.azurewebsites.net' and 'onefuzz-daily-ui.scm.azurewebsites.net', violating DP-2 (Enable encryption in transit).",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates to enforce TLS for all endpoints.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,180,"App Service config includes 'publishingUsername' in plain text, violating DP-3 (Manage sensitive information disclosure).",Remove 'publishingUsername' from the template and use Azure Key Vault references or secure parameters for sensitive information.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-5,template.json,180,"No backup or recovery configuration is present for the App Service, violating DP-5 (Backup and Recovery).",Configure regular backups for the App Service and ensure recovery procedures are in place.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-6,template.json,180,"App Service does not specify use of customer-managed keys (CMK) for encryption, violating DP-6 (Secure Data with Customer-Managed Keys).",Configure the App Service to use customer-managed keys for encryption at rest where supported.,N/A,AI,Generic
P4-Access-HIGH,Access Management,HIGH,AM-1,template.json,180,"No explicit RBAC or access control configuration is present, potentially violating AM-1 (Assign Least Privilege Access).",Assign least privilege access to the App Service using Azure RBAC and restrict permissions to only what is necessary.,N/A,AI,Generic
P4-Access-MEDIUM,Access Management,MEDIUM,AM-2,template.json,180,"No configuration for periodic access reviews is present, violating AM-2 (Regularly Review Access Rights).",Implement periodic access reviews for all roles and assignments related to the App Service.,N/A,AI,Generic
P4-Access-MEDIUM,Access Management,MEDIUM,AM-4,template.json,180,"No access review configuration is present for the App Service, violating AM-4 (Use Access Reviews).",Configure access reviews for the App Service to ensure only necessary access is retained.,N/A,AI,Generic
