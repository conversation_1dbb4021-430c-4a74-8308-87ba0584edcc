ASB ID,Control Domain,Recommendation,Security Principle,Azure Guidance,Implementation and additional context,Customer Security Stakeholders,Azure Policy Mapping
DP-1,Data Protection,Discover classify and label sensitive data,Establish and maintain an inventory of sensitive data based on defined scope. Use tools to discover classify and label in-scope sensitive data.,"Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other locations.","Enhanced Implementation Context:
• Data classification overview: https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification
• Azure Purview sensitivity labels: https://docs.microsoft.com/azure/purview/create-sensitivity-label
• Azure Information Protection: https://docs.microsoft.com/azure/information-protection/what-is-information-protection
• Azure SQL Data Discovery: https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification
• Azure Purview data sources: https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources

Compliance Mappings:
• CIS Controls v8: 3.2, 3.7, 3.13
• NIST SP800-53 r4: RA-2, SC-28
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Sensitive data in your SQL databases should be classified
• Implement data discovery and classification across all data stores
• Deploy Azure Purview for enterprise data governance
• Configure sensitivity labels for all data assets",Application Security; Data Security; Infrastructure and Endpoint Security,ImplementDataClassification;DeployPurview;EnableSensitivityLabels
DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,Monitor for anomalies around sensitive data such as unauthorized transfer to locations outside enterprise visibility and control. Monitor for anomalous activities that could indicate unauthorized data exfiltration.,"Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.","Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",Security Operations; Application Security; Infrastructure and Endpoint Security,EnableDefenderForSQL;EnableDefenderForStorage;MonitorDataExfiltration
DP-3,Data Protection,Encrypt sensitive data in transit,Protect data in transit against out-of-band attacks using encryption to ensure attackers cannot easily read or modify data. Set network boundaries where data-in-transit encryption is mandatory.,"Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windows remote management.","Enhanced Implementation Context:
• Double encryption for Azure data in transit: https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit
• Encryption in transit overview: https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit
• TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem
• Azure Storage secure transfer: https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account

Compliance Mappings:
• CIS Controls v8: 3.10
• NIST SP800-53 r4: SC-8
• PCI-DSS v3.2.1: 3.5, 3.6, 4.1

Azure Policy Examples:
• Kubernetes clusters should be accessible only over HTTPS
• Only secure connections to your Azure Cache for Redis should be enabled
• FTPS only should be required in your Function App
• Secure transfer to storage accounts should be enabled
• Function App should only be accessible over HTTPS
• Latest TLS version should be used in your API App
• Web Application should only be accessible over HTTPS
• Enforce SSL connection should be enabled for PostgreSQL database servers
• Latest TLS version should be used in your Web App",Security Architecture; Infrastructure and Endpoint Security; Application Security; Data Security,RequireHttps;RequireTLS12;EnforceSecureTransfer
DP-4,Data Protection,Enable data at rest encryption by default,Protect data at rest against out-of-band attacks using encryption. This helps ensure attackers cannot easily read or modify data by accessing underlying storage.,"Many Azure services have data-at-rest encryption enabled by default using service-managed keys. Where not enabled by default enable encryption in Azure services or VMs for storage-level file-level or database-level encryption.","Enhanced Implementation Context:
• Encryption at rest in Azure: https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services
• Data at rest double encryption: https://docs.microsoft.com/azure/security/fundamentals/encryption-models
• Encryption model and key management: https://docs.microsoft.com/azure/security/fundamentals/encryption-models
• Azure Disk Encryption: https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview
• SQL Transparent Data Encryption: https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview

Compliance Mappings:
• CIS Controls v8: 3.11
• NIST SP800-53 r4: SC-28
• PCI-DSS v3.2.1: 3.4, 3.5

Azure Policy Examples:
• Virtual machines should encrypt temp disks caches and data flows between Compute and Storage resources
• Transparent Data Encryption on SQL databases should be enabled
• Automation account variables should be encrypted
• Service Fabric clusters should have the ClusterProtectionLevel property set to EncryptAndSign
• Azure Cosmos DB accounts should use customer-managed keys to encrypt data at rest",Security Architecture; Infrastructure and Endpoint Security; Application Security; Data Security,RequireEncryptionAtRest;EnableTDE;RequireDiskEncryption
DP-5,Data Protection,Use customer-managed key option in data at rest encryption when required,Define use cases and service scope where customer-managed keys are needed for regulatory compliance. Enable data-at-rest encryption using customer-managed keys in applicable services.,"Azure provides customer-managed key options for certain services requiring additional operational efforts for key lifecycle management including generation rotation revocation and access control.","Enhanced Implementation Context:
• Encryption model and key management: https://docs.microsoft.com/azure/security/fundamentals/encryption-models
• Services supporting customer-managed keys: https://docs.microsoft.com/azure/security/fundamentals/encryption-models#supporting-services
• Azure Storage customer-managed keys: https://docs.microsoft.com/azure/storage/common/storage-encryption-keys-portal
• SQL customer-managed keys: https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-byok-overview
• Key Vault integration: https://docs.microsoft.com/azure/key-vault/general/overview

Compliance Mappings:
• CIS Controls v8: 3.11
• NIST SP800-53 r4: SC-12, SC-28
• PCI-DSS v3.2.1: 3.4, 3.5, 3.6

Azure Policy Examples:
• SQL managed instances should use customer-managed keys to encrypt data at rest
• SQL servers should use customer-managed keys to encrypt data at rest
• PostgreSQL servers should use customer-managed keys to encrypt data at rest
• Azure Cosmos DB accounts should use customer-managed keys to encrypt data at rest
• Container registries should be encrypted with a customer-managed key
• Storage accounts should use customer-managed key for encryption
• MySQL servers should use customer-managed keys to encrypt data at rest",Security Architecture; Infrastructure and Endpoint Security; Application Security; Data Security,RequireCMK;EnableKeyRotation;ImplementKeyLifecycle
DP-6,Data Protection,Use a secure key management process,Document and implement enterprise cryptographic key management standards processes and procedures to control key lifecycle. Use secured key vault service for key generation distribution and storage when using customer-managed keys.,"Use Azure Key Vault to create and control encryption key lifecycle including generation distribution storage rotation and revocation. Follow best practices for key hierarchy and BYOK scenarios. Ensure FIPS compliance levels meet requirements.","Enhanced Implementation Context:
• Azure Key Vault overview: https://docs.microsoft.com/azure/key-vault/general/overview
• Azure data encryption key hierarchy: https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#key-hierarchy
• BYOK specification: https://docs.microsoft.com/azure/key-vault/keys/byok-specification
• Key Vault security best practices: https://docs.microsoft.com/azure/key-vault/general/best-practices
• FIPS 140-2 compliance levels: Software-protected keys (Level 1) HSM-protected keys in vaults (Level 2) HSM-protected keys in Managed HSM (Level 3)

Compliance Mappings:
• CIS Controls v8: Not specified
• NIST SP800-53 r4: IA-5, SC-12, SC-28
• PCI-DSS v3.2.1: 3.6

Azure Policy Examples:
• Key Vault keys should have an expiration date
• Key Vault secrets should have an expiration date
• Implement key rotation policies
• Use separate data encryption keys (DEK) with key encryption keys (KEK)
• Register keys with Azure Key Vault using key IDs",Identity and Key Management; Security Architecture; Application Security; Data Security,ImplementKeyManagement;RequireKeyExpiration;EnableKeyRotation
DP-7,Data Protection,Use a secure certificate management process,Document and implement enterprise certificate management standards processes and procedures including certificate lifecycle control and certificate policies. Ensure certificates are inventoried tracked monitored and renewed timely.,"Use Azure Key Vault to create and control certificate lifecycle including creation import rotation revocation storage and purge. Setup automatic rotation where supported. Use approved Certificate Authorities and avoid self-signed or wildcard certificates.","Enhanced Implementation Context:
• Key Vault certificates overview: https://docs.microsoft.com/azure/key-vault/certificates/certificate-scenarios
• Certificate access control: https://docs.microsoft.com/azure/key-vault/certificates/certificate-access-control
• Automatic certificate rotation: https://docs.microsoft.com/azure/key-vault/certificates/tutorial-rotate-certificates
• Approved Certificate Authorities: DigiCert and GlobalSign are partnered providers with Azure Key Vault
• Certificate security best practices: Avoid insufficient key size overly long validity periods and insecure cryptography

Compliance Mappings:
• CIS Controls v8: Not specified
• NIST SP800-53 r4: IA-5, SC-12, SC-17
• PCI-DSS v3.2.1: 3.6

Azure Policy Examples:
• Certificates should have the specified maximum validity period
• Implement certificate lifecycle management
• Use only approved Certificate Authority providers
• Monitor certificate expiration and renewal
• Disable known bad CA root/intermediate certificates",Identity and Key Management; Security Architecture; Application Security; Data Security,ImplementCertificateManagement;RequireCertificateExpiration;UseApprovedCAs
DP-8,Data Protection,Ensure security of key and certificate repository,Ensure security of key vault service used for cryptographic key and certificate lifecycle management. Harden key vault through access control network security logging monitoring and backup.,"Secure Azure Key Vault through access policies or Azure RBAC for least privilege. Use Private Link and Azure Firewall for minimal exposure. Implement separation of duties managed identity access and comprehensive logging.","Enhanced Implementation Context:
• Azure Key Vault overview: https://docs.microsoft.com/azure/key-vault/general/overview
• Key Vault security best practices: https://docs.microsoft.com/azure/key-vault/general/best-practices
• Managed identity Key Vault access: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad
• Key Vault Private Link: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Key Vault logging and monitoring: https://docs.microsoft.com/azure/key-vault/general/logging

Compliance Mappings:
• CIS Controls v8: Not specified
• NIST SP800-53 r4: IA-5, SC-12, SC-17
• PCI-DSS v3.2.1: 3.6

Azure Policy Examples:
• Key vaults should have purge protection enabled
• Azure Defender for Key Vault should be enabled
• Key vaults should have soft delete enabled
• Azure Key Vault should disable public network access
• Private endpoint should be configured for Key Vault
• Resource logs in Key Vault should be enabled
• Implement separation of duties for key management and data access",Identity and Key Management; Security Architecture; Application Security; Data Security,SecureKeyVault;EnablePurgeProtection;RequirePrivateEndpoint;EnableKeyVaultLogging
