# Multiple Resource Types Detection Fix

## Problem Identified

The original `_determine_resource_type` method in `security_opt.py` had a critical issue where it would return early after finding the first exact ARM type match, causing subsequent resource types in the same template to be omitted from analysis.

### Original Issue

```python
# Tier 1: Exact ARM type match - highest priority
for rt_category, mappings in self.azure_resource_mappings.items():
    for arm_type in mappings.get("arm_types", []):
        if arm_type.lower() in content_lower:
            # PROBLEM: Early return only returns first match
            logger.info(f"Exact ARM type match found: {arm_type} -> {rt_category}")
            return rt_category  # ❌ Only returns single string, misses other types
```

### Impact

1. **Incomplete Analysis**: Templates with multiple resource types (e.g., Storage + KeyVault) would only analyze the first detected type
2. **Missed Security Issues**: Security patterns for subsequent resource types would not be applied
3. **Inconsistent Results**: Analysis results would vary depending on the order of resource detection

## Solution Implemented

### 1. Collect All Exact Matches

```python
# Tier 1: Exact ARM type match - highest priority
exact_matches = []
for rt_category, mappings in self.azure_resource_mappings.items():
    for arm_type in mappings.get("arm_types", []):
        if arm_type.lower() in content_lower:
            logger.info(f"Exact ARM type match found: {arm_type} -> {rt_category}")
            if rt_category not in exact_matches:
                exact_matches.append(rt_category)  # ✅ Collect all matches

# Continue processing to find additional resource types
if exact_matches:
    logger.info(f"Found {len(exact_matches)} exact ARM type matches: {exact_matches}")
```

### 2. Enhanced ARM Resource Processing

```python
# Find matching resource category - collect ALL matches, not just first
matched_categories = set()
for rt_category, mappings in self.azure_resource_mappings.items():
    # Check exact type match
    if any(arm_type.lower() == resource_type for arm_type in mappings.get("arm_types", [])):
        matched_categories.add(rt_category)  # ✅ Collect all categories
        logger.info(f"Found ARM resource type: {resource_type} -> {rt_category}")
    
    # Additional matching logic...

# Add all matched categories to detected_types
for category in matched_categories:
    if category not in detected_types:
        detected_types.append(category)  # ✅ Add all matches
```

### 3. Improved Final Type Combination

```python
# Combine exact matches with detected types from ARM analysis
if exact_matches:
    detected_types.extend(exact_matches)

# Use scoring system to supplement ARM analysis
if not detected_types or len(detected_types) < 2:
    non_zero_scores = {k: v for k,v in resource_scores.items() if v > 0}
    if non_zero_scores:
        max_score = max(non_zero_scores.values())
        # Include all types with high scores (not just the maximum)
        high_score_threshold = max_score * 0.7  # 70% threshold
        high_score_types = [rt for rt, score in non_zero_scores.items() 
                          if score >= high_score_threshold and rt not in detected_types]
        detected_types.extend(high_score_types)

# Remove duplicates while preserving order
detected_types = list(dict.fromkeys(detected_types))
```

## Key Improvements

### 1. Comprehensive Detection

- **Before**: Only first resource type detected
- **After**: All resource types in template detected

### 2. Better ARM Analysis

- **Before**: Early return missed subsequent resources
- **After**: Complete analysis of all resources in template

### 3. Enhanced Scoring

- **Before**: Only maximum score types included
- **After**: All high-scoring types included (70% threshold)

### 4. Nested Resource Support

- **Before**: Limited nested resource detection
- **After**: Complete nested resource analysis with category collection

## Test Cases

### Example 1: ARM Template with Storage + KeyVault

```json
{
  "resources": [
    {
      "type": "Microsoft.Storage/storageAccounts",
      "properties": {
        "enableHttpsTrafficOnly": false  // Storage security issue
      }
    },
    {
      "type": "Microsoft.KeyVault/vaults", 
      "properties": {
        "enablePurgeProtection": false   // KeyVault security issue
      }
    }
  ]
}
```

**Before Fix**: Only `["Storage"]` detected
**After Fix**: `["Storage", "KeyVault"]` detected

### Example 2: Bicep with Multiple Resources

```bicep
resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {
  properties: {
    enableHttpsTrafficOnly: false
  }
}

resource keyVault 'Microsoft.KeyVault/vaults@2021-10-01' = {
  properties: {
    enablePurgeProtection: false
  }
}
```

**Before Fix**: Only first resource type detected
**After Fix**: Both `Storage` and `KeyVault` detected

## Benefits

### 1. Complete Security Analysis

- All resource types in template are now analyzed
- Security patterns applied to all relevant resources
- No missed security issues due to incomplete detection

### 2. Accurate Resource Mapping

- Templates with mixed resource types properly categorized
- Better control selection for AI analysis
- More comprehensive security recommendations

### 3. Consistent Results

- Analysis results no longer depend on resource order
- Deterministic resource type detection
- Reliable security coverage

### 4. Enhanced Logging

- Better visibility into resource detection process
- Debug information for troubleshooting
- Clear indication of all detected types

## Backward Compatibility

- **Existing functionality preserved**: Single resource type templates work as before
- **Enhanced capability**: Multi-resource templates now work correctly
- **No breaking changes**: All existing patterns and logic maintained
- **Improved accuracy**: Better results for complex templates

## Usage

The fix is automatically applied when using the existing API:

```python
# No changes needed in usage
reviewer = SecurityPRReviewer(local_folder="./templates")
files = reviewer.analyze_folder("./templates")
findings = reviewer.analyze_files(files)

# Now correctly detects all resource types in each template
for file_info in files:
    resource_types = file_info.get("resource_types", [])
    print(f"File: {file_info['path']}")
    print(f"Resource types: {resource_types}")  # Now shows all types
```

## Testing

Run the test suite to verify the fix:

```bash
python test_multiple_resource_types.py
```

The test cases verify:
- Multiple resource type detection in ARM templates
- Multiple resource type detection in Bicep templates  
- Security analysis coverage for all detected types
- Nested resource type detection
- Proper handling of complex template structures

## Conclusion

This fix ensures that templates with multiple resource types are properly analyzed for security issues across all resource types, providing comprehensive security coverage and more accurate analysis results.
