#!/usr/bin/env python3
"""
Test script to generate an improved HTML report with consistent box sizing
"""

from security_opt import SecurityPRReviewer

def test_improved_report():
    # Create a test reviewer
    reviewer = SecurityPRReviewer(local_folder='.')

    # Create some sample findings for testing
    sample_findings = [
        {
            'control_id': 'TEST-001',
            'severity': 'CRITICAL',
            'description': 'This is a critical security finding with a longer description to test box sizing consistency and ensure that all boxes have uniform heights.',
            'remediation': 'Apply security patches and update configurations immediately.',
            'file_path': 'test/sample.tf',
            'line_number': 10,
            'code_snippet': 'resource "aws_s3_bucket" "example" {\n  bucket = "my-bucket"\n}'
        },
        {
            'control_id': 'TEST-002', 
            'severity': 'HIGH',
            'description': 'Short description.',
            'remediation': 'Quick fix.',
            'file_path': 'test/another.tf',
            'line_number': 5,
            'code_snippet': 'variable "test" {}'
        },
        {
            'control_id': 'TEST-003',
            'severity': 'MEDIUM', 
            'description': 'Medium length description that should test the box sizing and ensure consistent heights across different content lengths in the report.',
            'remediation': 'Medium length remediation that provides detailed steps for fixing this issue and improving security posture.',
            'file_path': 'test/config.tf',
            'line_number': 15,
            'code_snippet': 'locals {\n  environment = "test"\n  region = "us-east-1"\n}'
        },
        {
            'control_id': 'TEST-004',
            'severity': 'LOW',
            'description': 'Low severity finding.',
            'remediation': 'Optional improvement.',
            'file_path': 'test/vars.tf',
            'line_number': 1,
            'code_snippet': 'variable "name" { type = string }'
        },
        {
            'control_id': 'TEST-005',
            'severity': 'CRITICAL',
            'description': 'Another critical finding with different content length to test consistency.',
            'remediation': 'Immediate action required.',
            'file_path': 'test/security.tf',
            'line_number': 20,
            'code_snippet': 'resource "aws_security_group" "test" {}'
        },
        {
            'control_id': 'TEST-006',
            'severity': 'HIGH',
            'description': 'High severity finding with extensive description that spans multiple lines and contains detailed information about the security vulnerability and its potential impact on the system.',
            'remediation': 'Comprehensive remediation steps that include multiple actions, configuration changes, and security best practices to address this high-severity finding.',
            'file_path': 'test/network.tf',
            'line_number': 35,
            'code_snippet': 'resource "aws_vpc" "main" {\n  cidr_block = "10.0.0.0/16"\n  enable_dns_hostnames = true\n}'
        },
        {
            'control_id': 'TEST-007',
            'severity': 'CRITICAL',
            'description': 'Another critical finding to test boxing.',
            'remediation': 'Fix immediately.',
            'file_path': 'test/critical2.tf',
            'line_number': 42,
            'code_snippet': 'resource "aws_s3_bucket_public_access_block" "example" { block_public_acls = false }'
        },
        {
            'control_id': 'TEST-008',
            'severity': 'HIGH',
            'description': 'Additional high severity finding.',
            'remediation': 'Apply security patches.',
            'file_path': 'test/high2.tf',
            'line_number': 18,
            'code_snippet': 'resource "aws_security_group_rule" "example" { type = "ingress", from_port = 0, to_port = 65535 }'
        },
        {
            'control_id': 'TEST-009',
            'severity': 'MEDIUM',
            'description': 'Medium severity finding for testing.',
            'remediation': 'Consider implementing best practices.',
            'file_path': 'test/medium2.tf',
            'line_number': 25,
            'code_snippet': 'resource "aws_instance" "example" { instance_type = "t2.micro" }'
        },
        {
            'control_id': 'TEST-010',
            'severity': 'LOW',
            'description': 'Low severity finding for completeness.',
            'remediation': 'Optional improvement when time permits.',
            'file_path': 'test/low2.tf',
            'line_number': 8,
            'code_snippet': 'variable "environment" { default = "dev" }'
        }
    ]

    # Generate HTML report
    output_file = 'test_improved_report.html'
    reviewer._export_findings_to_html(sample_findings, output_file)
    print(f'Generated improved report: {output_file}')
    print('The report now includes:')
    print('- Consistent box heights for stat cards')
    print('- Uniform finding box sizes')
    print('- Better responsive design')
    print('- Improved scrolling experience')
    print('- Enhanced visual consistency')

if __name__ == '__main__':
    test_improved_report()
