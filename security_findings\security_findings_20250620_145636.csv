File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
network_demo.tf,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,24.0,"The 'source_address_prefix' property in the 'azurerm_network_security_group.demo' resource is set to '0.0.0.0/0' for an inbound rule allowing TCP port 22 (SSH) (Line 24). This exposes SSH access from any IP address on the internet, enabling attackers to attempt brute-force attacks, gain initial access, and move laterally within the network. The blast radius includes all resources protected by this NSG, potentially compromising the entire subnet or virtual network.","Restrict 'source_address_prefix' to trusted IP ranges (e.g., corporate office IPs or jump hosts) instead of '0.0.0.0/0'. Implement a deny-by-default approach and only allow SSH from specific, controlled sources. Example: source_address_prefix = ""***********/24"". Review all NSG rules to ensure no unnecessary exposure to the internet.",,,,ai_analysis,,Validated
network_demo.tf,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,43.0,"The 'allow_blob_public_access' property in the 'azurerm_storage_account.demo' resource is set to 'true' (Line 43), enabling public anonymous access to blobs. This allows any unauthenticated user on the internet to read data from the storage account, creating a direct data exfiltration vector and significantly increasing the blast radius of potential data exposure.","Set 'allow_blob_public_access' to 'false' to disable anonymous public access. Example: allow_blob_public_access = false. Additionally, use private endpoints and restrict network access to the storage account to trusted VNets or IPs only.",,,,ai_analysis,,Validated
network_demo.tf,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,46.0,"The 'https_traffic_only' property in the 'azurerm_storage_account.demo' resource is set to 'false' (Line 46), allowing unencrypted HTTP connections. This exposes data in transit to interception and man-in-the-middle attacks, enabling attackers to read or modify sensitive data as it traverses the network.",Set 'https_traffic_only' to 'true' to enforce encryption for all data in transit. Example: https_traffic_only = true. Ensure all clients and applications accessing the storage account use HTTPS endpoints.,,,,ai_analysis,,Validated
storage_demo.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,26.0,"The 'minimumTlsVersion' property is set to 'TLS1_0', which enables weak encryption for data in transit. Attackers can exploit known vulnerabilities in TLS 1.0 to intercept or modify data between clients and the storage account, leading to data exfiltration or tampering. The blast radius includes all data transferred to and from this storage account.",Set 'minimumTlsVersion' to 'TLS1_2' or higher in the storage account properties to enforce strong encryption for data in transit. Example: minimumTlsVersion: 'TLS1_2'.,,,,ai_analysis,,Validated
storage_demo.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,23.0,"The 'supportsHttpsTrafficOnly' property is set to false, allowing unencrypted HTTP traffic to the storage account. This exposes data in transit to interception and man-in-the-middle attacks, enabling attackers to steal or manipulate sensitive information. The blast radius includes all data accessed or uploaded via HTTP.",Set 'supportsHttpsTrafficOnly' to true in the storage account properties to enforce HTTPS-only access. Example: supportsHttpsTrafficOnly: true.,,,,ai_analysis,,Validated
storage_demo.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,17.0,"The 'allowBlobPublicAccess' property is set to true, enabling public anonymous access to blobs in the storage account. This allows any unauthenticated user on the internet to read data, creating a direct data exfiltration vector. The blast radius is all blob data in the storage account.",Set 'allowBlobPublicAccess' to false in the storage account properties to disable anonymous public access. Example: allowBlobPublicAccess: false.,,,,ai_analysis,,Validated
storage_demo.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,40.0,"The 'networkAcls.defaultAction' property is set to 'Allow', which permits all network traffic (including from the public internet) to access the storage account unless explicitly denied. This exposes the storage account to unauthorized access, lateral movement, and data exfiltration. The blast radius is the entire storage account and its data.","Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only trusted networks or private endpoints. Example: networkAcls: { defaultAction: 'Deny', ... }.",,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 7,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 0,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T14:56:36.892616,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
