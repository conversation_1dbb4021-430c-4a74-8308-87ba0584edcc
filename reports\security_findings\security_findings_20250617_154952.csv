File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
Parameters\LacpBillingExhaust.Parameters-LacpBillingExhaust.json,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'usageAccountSystemAssignedIdentityPrincipalId' flows across template boundary,"Secure parameter 'usageAccountSystemAssignedIdentityPrincipalId' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyVaultSuffix' flows across template boundary,"Secure parameter 'keyVaultSuffix' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'regionalCosmosAccountLocation' flows across template boundary,"Secure parameter 'regionalCosmosAccountLocation' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'dasStorageAccountKey' flows across template boundary,"Secure parameter 'dasStorageAccountKey' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'globalKeyVaultName' flows across template boundary,"Secure parameter 'globalKeyVaultName' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'createSharedKeyVault' flows across template boundary,"Secure parameter 'createSharedKeyVault' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,61.0,"The parameter 'dasStorageAccountKey' at line 61 contains a reference to a storage account key, which is a sensitive secret. Storing secrets directly in parameters or code violates DP-3: Manage sensitive information disclosure.","Remove the storage account key from the parameters file. Use Azure Key Vault references to securely retrieve secrets at deployment/runtime. Update your template to reference the key from Key Vault instead.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",[Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references),Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.
Key Vault overview: https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Secret management: https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets
App Service Key Vault integration: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\IngestionStorageAccount.Template.json,DP-1,Data Protection,Enable encryption at rest,CRITICAL,32.0,Storage account resource at line 32 does not specify 'encryption' property. Azure Security Benchmark DP-1 requires all sensitive data to be encrypted at rest.,"Add the 'encryption' property to the storage account resource with 'services', 'keySource', and 'enabled' set appropriately to enable Storage Service Encryption. See: https://docs.microsoft.com/en-us/azure/storage/common/storage-service-encryption

📚 References: [Storage Service Encryption](https://docs.microsoft.com/en-us/azure/storage/common/storage-service-encryption) | [Transparent Data Encryption Tde Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-tde-overview) | [Database Encryption At Rest](https://docs.microsoft.com/en-us/azure/cosmos-db/database-encryption-at-rest)

🔵 Azure Guidance: Enable encryption for all storage accounts and databases.",[Storage Service Encryption](https://docs.microsoft.com/en-us/azure/storage/common/storage-service-encryption) | [Transparent Data Encryption Tde Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-tde-overview) | [Database Encryption At Rest](https://docs.microsoft.com/en-us/azure/cosmos-db/database-encryption-at-rest),Enable encryption for all storage accounts and databases.,"Use Azure Storage Service Encryption. Enable TDE for SQL databases.
Azure Storage encryption: https://docs.microsoft.com/en-us/azure/storage/common/storage-service-encryption
SQL TDE documentation: https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-tde-overview
Cosmos DB encryption: https://docs.microsoft.com/en-us/azure/cosmos-db/database-encryption-at-rest

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\IngestionStorageAccount.Template.json,DP-1,Data Protection,Enable encryption at rest,CRITICAL,54.0,Storage account resource at line 54 does not specify 'encryption' property. Azure Security Benchmark DP-1 requires all sensitive data to be encrypted at rest.,"Add the 'encryption' property to the storage account resource with 'services', 'keySource', and 'enabled' set appropriately to enable Storage Service Encryption. See: https://docs.microsoft.com/en-us/azure/storage/common/storage-service-encryption

📚 References: [Storage Service Encryption](https://docs.microsoft.com/en-us/azure/storage/common/storage-service-encryption) | [Transparent Data Encryption Tde Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-tde-overview) | [Database Encryption At Rest](https://docs.microsoft.com/en-us/azure/cosmos-db/database-encryption-at-rest)

🔵 Azure Guidance: Enable encryption for all storage accounts and databases.",[Storage Service Encryption](https://docs.microsoft.com/en-us/azure/storage/common/storage-service-encryption) | [Transparent Data Encryption Tde Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-tde-overview) | [Database Encryption At Rest](https://docs.microsoft.com/en-us/azure/cosmos-db/database-encryption-at-rest),Enable encryption for all storage accounts and databases.,"Use Azure Storage Service Encryption. Enable TDE for SQL databases.
Azure Storage encryption: https://docs.microsoft.com/en-us/azure/storage/common/storage-service-encryption
SQL TDE documentation: https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-tde-overview
Cosmos DB encryption: https://docs.microsoft.com/en-us/azure/cosmos-db/database-encryption-at-rest

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\IngestionStorageAccount.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,32.0,Storage account resource at line 32 does not configure a private endpoint. Azure Security Benchmark NS-5 requires use of Private Endpoints to reduce attack surface.,"Add a 'Microsoft.Network/privateEndpoints' resource referencing this storage account to restrict access to private networks only. See: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\IngestionStorageAccount.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,54.0,Storage account resource at line 54 does not configure a private endpoint. Azure Security Benchmark NS-5 requires use of Private Endpoints to reduce attack surface.,"Add a 'Microsoft.Network/privateEndpoints' resource referencing this storage account to restrict access to private networks only. See: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpBilling.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,393.0,"The Microsoft.Storage/storageAccounts resource at line 393 does not configure a private endpoint. According to ASB NS-5, storage accounts should use Private Endpoints to reduce public exposure.","Add a Microsoft.Network/privateEndpoints resource targeting the storage account and configure the storage account to only allow traffic from the private endpoint. See: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpBillingExhaust.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,32.0,"The Microsoft.Kusto/clusters resource at line 32 does not restrict public network access or configure private endpoints, potentially exposing a public endpoint. No explicit network ACLs, IP allowlists, or private endpoint configurations are present.","Restrict public network access for the Kusto cluster by configuring network ACLs to allow only required IPs, or use Private Endpoints. Refer to https://docs.microsoft.com/en-us/azure/private-link/private-link-overview and update the resource with 'publicNetworkAccess': 'Disabled' or configure 'virtualNetworkConfiguration'.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpBillingExhaust.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,32.0,"The Microsoft.Kusto/clusters resource at line 32 does not use Private Endpoints, increasing the attack surface for the service.","Configure a Private Endpoint for the Kusto cluster to ensure access is only available from within your private network. See https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview for implementation guidance.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpBillingExhaustExport.Template.json,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,54.0,"The template contains a hardcoded 'sourceId' value with a placeholder subscription and resource group (""/subscriptions/placeholder/resourceGroups/placeholder/providers/Microsoft.UsageBilling/accounts/pipelines/variables('usageAccountName')""). This may expose sensitive information or secrets in code, violating DP-3.","Remove hardcoded sensitive values from the template. Use Azure Key Vault references or parameterize sensitive data such as subscription IDs and resource group names. Ensure no secrets or sensitive identifiers are stored directly in the template.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",[Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references),Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.
Key Vault overview: https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Secret management: https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets
App Service Key Vault integration: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpGeo.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,153.0,"CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' has 'publicNetworkAccess' set to 'Enabled', exposing the database account to the public internet. This violates ASB NS-2, which requires strict access control for public endpoints.","Set 'publicNetworkAccess' to 'Disabled' in the CosmosDB account resource to restrict public access. Use Private Link or service endpoints for secure access.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpGeo.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,156.0,"CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' has 'isVirtualNetworkFilterEnabled' set to 'false', which means no virtual network restrictions are applied. This violates ASB NS-2, which requires strict access control for public endpoints.","Set 'isVirtualNetworkFilterEnabled' to 'true' and configure 'virtualNetworkRules' to restrict access to only required subnets.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpGeo.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,153.0,CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' does not configure a private endpoint. ASB NS-5 requires use of Private Endpoints to reduce attack surface for PaaS services.,"Add a 'Microsoft.Network/privateEndpoints' resource targeting the CosmosDB account to ensure all access is via private endpoints.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,54.0,"CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' at line 54 has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false, exposing the database to the public internet without network restrictions. This violates NS-2, which requires strict access control for public endpoints.","Set 'publicNetworkAccess' to 'Disabled' or 'isVirtualNetworkFilterEnabled' to true and define appropriate 'virtualNetworkRules' to restrict access to only required IPs or networks. Consider using Private Link for CosmosDB.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,54.0,CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' at line 54 does not configure a private endpoint. NS-5 requires use of Private Endpoints for PaaS services to reduce attack surface.,"Add a 'privateEndpointConnections' property to the CosmosDB resource to configure a Private Endpoint, ensuring all access is via private IP addresses.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,151.0,Storage Account resource 'Microsoft.Storage/storageAccounts' at line 151 does not configure a private endpoint. NS-5 requires use of Private Endpoints for Storage to reduce attack surface.,"Add a 'privateEndpointConnections' property to the Storage Account resource to configure a Private Endpoint, ensuring all access is via private IP addresses.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,184.0,Key Vault resource 'Microsoft.KeyVault/vaults' at line 184 does not configure a private endpoint. NS-5 requires use of Private Endpoints for Key Vault to reduce attack surface.,"Add a 'privateEndpointConnections' property to the Key Vault resource to configure a Private Endpoint, ensuring all access is via private IP addresses.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpRegion.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,1002.0,"CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' at line 1002 has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to 'false', exposing the database to the public internet. This violates NS-2: Protect public endpoints.","Set 'publicNetworkAccess' to 'Disabled' and enable 'isVirtualNetworkFilterEnabled' with appropriate 'virtualNetworkRules' to restrict access to only required networks. Reference: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpStamp.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,1007.0,Microsoft.Storage/storageAccounts resource at line 1007 does not configure a private endpoint. Storage accounts should use private endpoints to reduce public exposure.,"Add a Microsoft.Network/privateEndpoints resource for this storage account and configure the storage account to only allow traffic from the private endpoint. Reference: ASB NS-5.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpStamp.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,1042.0,Microsoft.Storage/storageAccounts resource at line 1042 does not configure a private endpoint. Storage accounts should use private endpoints to reduce public exposure.,"Add a Microsoft.Network/privateEndpoints resource for this storage account and configure the storage account to only allow traffic from the private endpoint. Reference: ASB NS-5.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpStamp.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,1087.0,Microsoft.Storage/storageAccounts resource at line 1087 does not configure a private endpoint. Storage accounts should use private endpoints to reduce public exposure.,"Add a Microsoft.Network/privateEndpoints resource for this storage account and configure the storage account to only allow traffic from the private endpoint. Reference: ASB NS-5.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpStamp.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,1007.0,"Microsoft.Storage/storageAccounts resource at line 1007 does not explicitly set 'networkAcls' to restrict public network access. Without this, the storage account may be accessible over the public internet, violating encryption in transit requirements.","Set the 'networkAcls' property to restrict public network access and allow only trusted subnets or private endpoints. Reference: ASB DP-2.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpStamp.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,1042.0,"Microsoft.Storage/storageAccounts resource at line 1042 does not explicitly set 'networkAcls' to restrict public network access. Without this, the storage account may be accessible over the public internet, violating encryption in transit requirements.","Set the 'networkAcls' property to restrict public network access and allow only trusted subnets or private endpoints. Reference: ASB DP-2.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpStamp.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,1087.0,"Microsoft.Storage/storageAccounts resource at line 1087 does not explicitly set 'networkAcls' to restrict public network access. Without this, the storage account may be accessible over the public internet, violating encryption in transit requirements.","Set the 'networkAcls' property to restrict public network access and allow only trusted subnets or private endpoints. Reference: ASB DP-2.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpStamp.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,1132.0,"Microsoft.Storage/storageAccounts resource at line 1132 does not explicitly set 'networkAcls' to restrict public network access. Without this, the storage account may be accessible over the public internet, violating encryption in transit requirements.","Set the 'networkAcls' property to restrict public network access and allow only trusted subnets or private endpoints. Reference: ASB DP-2.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpStamp.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,1177.0,"Microsoft.Storage/storageAccounts resource at line 1177 does not explicitly set 'networkAcls' to restrict public network access. Without this, the storage account may be accessible over the public internet, violating encryption in transit requirements.","Set the 'networkAcls' property to restrict public network access and allow only trusted subnets or private endpoints. Reference: ASB DP-2.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\LacpStamp.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,1222.0,"Microsoft.Storage/storageAccounts resource at line 1222 does not explicitly set 'networkAcls' to restrict public network access. Without this, the storage account may be accessible over the public internet, violating encryption in transit requirements.","Set the 'networkAcls' property to restrict public network access and allow only trusted subnets or private endpoints. Reference: ASB DP-2.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\ReadAdxExhaust.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,28.0,"The Microsoft.Kusto/clusters resource at line 28 does not specify any network restrictions or private endpoints, potentially exposing the cluster to public network access. This violates NS-2, which requires strict access control for public endpoints.","Restrict public network access to the Kusto cluster by configuring allowed IP ranges, using Private Link, or integrating with Application Gateway or Azure Front Door. Add 'publicNetworkAccess': 'Disabled' or configure 'virtualNetworkConfiguration' to limit exposure.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\ReadAdxExhaust.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,28.0,"The Microsoft.Kusto/clusters resource at line 28 does not use Private Endpoints, increasing the attack surface. NS-5 requires the use of Private Endpoints for PaaS services to reduce exposure.","Configure a Private Endpoint for the Kusto cluster by adding a 'privateEndpointConnections' property and associating it with the appropriate virtual network and subnet.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\ReadIdentity.Template.json,IM-8,Identity Management,Use Managed Identities for Azure Resources,HIGH,13.0,"The variable 'userAssignedIdentityName' is set to an invalid value (""FUNCTION_ERROR_toLower""). This may prevent the correct configuration of a user-assigned managed identity, violating the requirement to use managed identities for Azure resources (IM-8).","Correct the 'userAssignedIdentityName' variable to use a valid value or expression that resolves to the intended managed identity name. Ensure the template properly configures a user-assigned managed identity for secure identity management.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview#managed-identity-types) | [Qs Configure Portal Windows Vm](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/qs-configure-portal-windows-vm)

🔵 Azure Guidance: Enable system/user-assigned managed identities for Azure services.",[Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview#managed-identity-types) | [Qs Configure Portal Windows Vm](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/qs-configure-portal-windows-vm),Enable system/user-assigned managed identities for Azure services.,"Configure managed identities for VMs apps and Azure Functions. Use for Key Vault access.
Managed identities overview: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
System vs user-assigned: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview#managed-identity-types
VM managed identity setup: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/qs-configure-portal-windows-vm

This control is implemented through 3 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\ReadUsageAccount.Template.json,IM-8,Identity Management,Use Managed Identities for Azure Resources,HIGH,18.0,"The resource 'Microsoft.UsageBilling/accounts' at line 18 does not explicitly configure a managed identity. According to ASB IM-8, managed identities should be used to avoid credential storage in code or configuration.","Add the 'identity' property to the resource definition at line 18 to enable a system-assigned managed identity. Example: ""identity"": { ""type"": ""SystemAssigned"" }

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview#managed-identity-types) | [Qs Configure Portal Windows Vm](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/qs-configure-portal-windows-vm)

🔵 Azure Guidance: Enable system/user-assigned managed identities for Azure services.",[Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview#managed-identity-types) | [Qs Configure Portal Windows Vm](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/qs-configure-portal-windows-vm),Enable system/user-assigned managed identities for Azure services.,"Configure managed identities for VMs apps and Azure Functions. Use for Key Vault access.
Managed identities overview: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
System vs user-assigned: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview#managed-identity-types
VM managed identity setup: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/qs-configure-portal-windows-vm

This control is implemented through 3 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\RoleAssignment.Template.json,IM-8,Identity Management,Use Managed Identities for Azure Resources,MEDIUM,44.0,"Role assignment for 'Ev2BuildoutServicePrincipalId' grants Contributor access using a service principal, but does not use a managed identity. This does not align with the recommendation to use managed identities for Azure resources.","Replace the service principal with a managed identity for the resource, and assign the Contributor role to the managed identity instead. Refer to https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview for implementation guidance.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview#managed-identity-types) | [Qs Configure Portal Windows Vm](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/qs-configure-portal-windows-vm)

🔵 Azure Guidance: Enable system/user-assigned managed identities for Azure services.",[Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview#managed-identity-types) | [Qs Configure Portal Windows Vm](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/qs-configure-portal-windows-vm),Enable system/user-assigned managed identities for Azure services.,"Configure managed identities for VMs apps and Azure Functions. Use for Key Vault access.
Managed identities overview: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
System vs user-assigned: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview#managed-identity-types
VM managed identity setup: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/qs-configure-portal-windows-vm

This control is implemented through 3 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\RoleAssignment.Template.json,IM-8,Identity Management,Use Managed Identities for Azure Resources,MEDIUM,51.0,"Role assignment for 'KeyVaultPrincipalId' grants Storage Account Key Operator Service Role access using a service principal, but does not use a managed identity. This does not align with the recommendation to use managed identities for Azure resources.","Replace the service principal with a managed identity for the resource, and assign the Storage Account Key Operator Service Role to the managed identity instead. Refer to https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview for implementation guidance.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview#managed-identity-types) | [Qs Configure Portal Windows Vm](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/qs-configure-portal-windows-vm)

🔵 Azure Guidance: Enable system/user-assigned managed identities for Azure services.",[Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview#managed-identity-types) | [Qs Configure Portal Windows Vm](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/qs-configure-portal-windows-vm),Enable system/user-assigned managed identities for Azure services.,"Configure managed identities for VMs apps and Azure Functions. Use for Key Vault access.
Managed identities overview: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
System vs user-assigned: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview#managed-identity-types
VM managed identity setup: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/qs-configure-portal-windows-vm

This control is implemented through 3 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
Templates\TrafficManagerEndpoints.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,38.0,"The resource 'Microsoft.Network/trafficManagerProfiles/externalEndpoints' at line 38 is configured as a public endpoint (location: 'global') without any explicit access restrictions or IP allowlist. This exposes the endpoint to the public internet, violating ASB NS-2 requirements for strict access control on public endpoints.","Restrict public access to the Traffic Manager external endpoint by configuring endpoint access controls, using IP allowlists, or integrating with Azure Front Door or Application Gateway. Consider using Private Link or service endpoints to limit exposure. Reference: ASB NS-2.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 39,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 6,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-17T15:49:52.250965,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
