#!/usr/bin/env python3
"""
Test script to validate that all control IDs now have URLs in the CSV files.
"""

import sys
import json
import tempfile
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from security_opt import SecurityPRReviewer
    print("✅ Successfully imported SecurityPRReviewer")
except ImportError as e:
    print(f"❌ Failed to import SecurityPRReviewer: {e}")
    sys.exit(1)

def test_all_control_urls():
    """Test that all control IDs have URLs."""
    print("\n🔗 Testing URL Coverage for All Control IDs")
    print("=" * 60)
    
    try:
        # Initialize reviewer
        reviewer = SecurityPRReviewer(local_folder=".")
        
        # Get all control IDs from the benchmark
        all_controls = []
        if reviewer.benchmark_data and "controls" in reviewer.benchmark_data:
            all_controls = reviewer.benchmark_data["controls"]
        
        print(f"📊 Found {len(all_controls)} total controls in benchmark")
        
        # Test link extraction for each control
        controls_with_urls = 0
        controls_without_urls = 0
        total_urls = 0
        
        for control in all_controls:
            control_id = control.get("id", "UNKNOWN")
            domain = control.get("domain", "Unknown")
            
            # Extract links for this control
            links_info = reviewer._extract_control_links(control_id)
            
            urls_count = len(links_info.get('raw_links', []))
            has_guidance = bool(links_info.get('azure_guidance'))
            has_context = bool(links_info.get('implementation_context'))
            
            if urls_count > 0:
                controls_with_urls += 1
                total_urls += urls_count
                status = "✅"
            else:
                controls_without_urls += 1
                status = "❌"
            
            print(f"   {status} {control_id} ({domain}): {urls_count} URLs, guidance: {'Yes' if has_guidance else 'No'}")
            
            # Show first URL as example for controls with URLs
            if urls_count > 0:
                first_url = links_info['raw_links'][0]
                print(f"      Example: {first_url}")
        
        # Summary statistics
        print(f"\n📈 URL Coverage Summary:")
        print(f"   Controls with URLs: {controls_with_urls}")
        print(f"   Controls without URLs: {controls_without_urls}")
        print(f"   Total URLs found: {total_urls}")
        print(f"   Coverage percentage: {(controls_with_urls / len(all_controls) * 100):.1f}%")
        
        # Test HTML generation with comprehensive URLs
        if controls_with_urls > 0:
            print(f"\n🌐 Testing HTML generation with comprehensive URLs...")
            
            # Create temporary directory for test files
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                # Create sample Bicep file
                bicep_content = """
resource storageAccount 'Microsoft.Storage/storageAccounts@2023-01-01' = {
  name: 'teststorage'
  properties: {
    publicNetworkAccess: 'Enabled'
    supportsHttpsTrafficOnly: false
    minimumTlsVersion: 'TLS1_0'
  }
}

resource keyVault 'Microsoft.KeyVault/vaults@2023-07-01' = {
  name: 'test-keyvault'
  properties: {
    enableSoftDelete: false
    enableRbacAuthorization: false
  }
}
"""
                bicep_file = temp_path / "comprehensive-test.bicep"
                bicep_file.write_text(bicep_content)
                
                # Create sample findings covering multiple domains
                sample_findings = [
                    {
                        "file_path": str(bicep_file),
                        "control_id": "IM-1",
                        "severity": "HIGH",
                        "line": 1,
                        "description": "Azure AD not configured as identity provider",
                        "remediation": "Configure Azure AD for identity management",
                        "source": "ai_analysis"
                    },
                    {
                        "file_path": str(bicep_file),
                        "control_id": "NS-2",
                        "severity": "CRITICAL",
                        "line": 4,
                        "description": "Public network access enabled",
                        "remediation": "Configure private endpoints",
                        "source": "ai_analysis"
                    },
                    {
                        "file_path": str(bicep_file),
                        "control_id": "DP-2",
                        "severity": "HIGH",
                        "line": 5,
                        "description": "HTTPS not enforced",
                        "remediation": "Enable HTTPS-only access",
                        "source": "ai_analysis"
                    },
                    {
                        "file_path": str(bicep_file),
                        "control_id": "DP-3",
                        "severity": "MEDIUM",
                        "line": 12,
                        "description": "Key Vault not properly configured",
                        "remediation": "Enable soft delete and RBAC",
                        "source": "ai_analysis"
                    }
                ]
                
                # Create output directory
                output_dir = Path("comprehensive_url_reports")
                output_dir.mkdir(exist_ok=True)
                
                print(f"📊 Generating comprehensive reports with {len(sample_findings)} findings...")
                
                # Export both HTML and CSV
                reviewer.export_findings(sample_findings, format="html", output_dir=str(output_dir))
                reviewer.export_findings(sample_findings, format="csv", output_dir=str(output_dir))
                
                # Check generated files
                html_files = list(output_dir.glob("*.html"))
                csv_files = list(output_dir.glob("*.csv"))
                
                if html_files and csv_files:
                    html_file = html_files[0]
                    csv_file = csv_files[0]
                    
                    print(f"✅ Generated HTML report: {html_file}")
                    print(f"✅ Generated CSV report: {csv_file}")
                    
                    # Check HTML tooltip data
                    with open(html_file, 'r', encoding='utf-8') as f:
                        html_content = f.read()
                    
                    if 'window.tooltipLinks = ' in html_content:
                        start = html_content.find('window.tooltipLinks = ') + len('window.tooltipLinks = ')
                        end = html_content.find('};', start) + 1
                        tooltip_data_str = html_content[start:end]
                        
                        try:
                            tooltip_data = json.loads(tooltip_data_str)
                            tooltip_controls = list(tooltip_data.keys())
                            tooltip_total_links = sum(len(data.get('raw_links', [])) for data in tooltip_data.values())
                            
                            print(f"   🔗 HTML tooltip data: {len(tooltip_controls)} controls, {tooltip_total_links} total links")
                            
                            for control_id in tooltip_controls:
                                links_count = len(tooltip_data[control_id].get('raw_links', []))
                                print(f"      {control_id}: {links_count} links")
                                
                        except json.JSONDecodeError:
                            print(f"   ⚠️ Could not parse HTML tooltip data")
                    
                    # Check CSV reference links
                    with open(csv_file, 'r', encoding='utf-8') as f:
                        csv_content = f.read()
                    
                    reference_links_count = csv_content.count('[')  # Count formatted links
                    print(f"   📄 CSV reference links: {reference_links_count} formatted links found")
                    
                    return True
                else:
                    print(f"❌ Failed to generate reports")
                    return False
        
        return controls_with_urls > 0
        
    except Exception as e:
        print(f"❌ Error during URL testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run comprehensive URL testing."""
    print("🧪 COMPREHENSIVE URL COVERAGE TESTING")
    print("=" * 70)
    
    success = test_all_control_urls()
    
    if success:
        print("\n🎉 COMPREHENSIVE URL TESTING SUCCESSFUL!")
        print("\n📋 What was validated:")
        print("   ✅ URL coverage across all control domains")
        print("   ✅ Link extraction from updated CSV files")
        print("   ✅ HTML tooltip generation with comprehensive data")
        print("   ✅ CSV reference links formatting")
        print("   ✅ Multi-domain security findings coverage")
        
        print("\n💡 All control IDs now have comprehensive documentation links!")
        print("📁 Check the 'comprehensive_url_reports' directory for generated reports")
        
        return True
    else:
        print("\n❌ Comprehensive URL testing failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
