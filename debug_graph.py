#!/usr/bin/env python3
"""
Debug script for graph analysis functionality.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src" / "core"))

def main():
    folder_path = project_root / "test_graph"

    print(f"🔍 Debug: Testing graph analysis with folder: {folder_path}")

    try:
        # Change to the src/core directory for imports
        os.chdir(project_root / "src" / "core")
        from security_opt import SecurityPRReviewer

        # Initialize the security reviewer in local mode
        reviewer = SecurityPRReviewer(local_folder=str(folder_path))
        
        # Test file detection
        print("🔍 Debug: Testing file detection...")
        files = reviewer.analyze_folder(str(folder_path))
        print(f"📁 Debug: Found {len(files)} files")

        for i, file_info in enumerate(files):
            print(f"  File {i+1}: {file_info.get('path', 'Unknown')}")
            print(f"    - Is ARM file: {file_info.get('is_arm_file', False)}")
            print(f"    - Content length: {len(file_info.get('content', ''))}")

        if not files:
            print("❌ Debug: No files detected - checking folder contents...")
            for root, dirs, filenames in os.walk(str(folder_path)):
                for fname in filenames:
                    print(f"  Found file: {fname}")
            return
        
        # Test graph building
        print("\n🔗 Debug: Testing graph building...")
        print("📋 Debug: File structure analysis:")
        for i, file_info in enumerate(files):
            print(f"  File {i+1} structure:")
            for key, value in file_info.items():
                if key == 'content':
                    print(f"    {key}: {len(str(value))} characters")
                else:
                    print(f"    {key}: {value}")

        graph = reviewer.build_resource_dependency_graph(files)
        print(f"📊 Debug: Graph has {len(graph['nodes'])} nodes and {len(graph['edges'])} edges")

        if len(graph['nodes']) == 0:
            print("❌ Debug: No nodes found! Let's check what's happening...")
            # Let's manually test the ARM parsing
            test_file = files[0] if files else None
            if test_file:
                print(f"🔍 Debug: Testing ARM parsing on {test_file.get('path', 'unknown')}")
                content = test_file.get('content', '{}')
                try:
                    import json
                    template = json.loads(content)
                    resources = template.get("resources", [])
                    print(f"📋 Debug: Found {len(resources)} resources in template")
                    for i, resource in enumerate(resources):
                        print(f"  Resource {i}: {resource.get('type', 'Unknown')} - {resource.get('name', 'Unknown')}")
                except Exception as e:
                    print(f"❌ Debug: Error parsing JSON: {e}")

        for i, node in enumerate(graph['nodes']):
            print(f"  Node {i}: {node['type']} - {node['name']}")

        for i, edge in enumerate(graph['edges']):
            print(f"  Edge {i}: {edge['source']} -> {edge['target']} ({edge['type']})")
        
        # Test Mermaid generation
        print("\n📊 Debug: Testing Mermaid diagram generation...")
        mermaid = reviewer.generate_mermaid_diagram(graph)
        print(f"📄 Debug: Generated Mermaid diagram ({len(mermaid)} characters)")
        print("First 200 characters:")
        print(mermaid[:200])
        
        # Test graph-enhanced analysis
        print("\n🔗 Debug: Testing graph-enhanced analysis...")
        findings = reviewer.analyze_files_with_graph(files)
        print(f"📊 Debug: Found {len(findings)} findings with graph context")
        
        for i, finding in enumerate(findings):
            cascade_risk = finding.get('cascade_risk', 'UNKNOWN')
            connected_count = len(finding.get('connected_resources', []))
            print(f"  Finding {i+1}: {finding.get('severity', 'UNKNOWN')} - Cascade Risk: {cascade_risk} - Connected: {connected_count}")
        
        print("✅ Debug: All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Debug: Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
