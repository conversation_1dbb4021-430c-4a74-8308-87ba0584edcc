Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,hub-network.bicep,4,The virtual network 'hub-vnet' (line 4) and its subnet 'hub-subnet' (line 13) do not have any Network Security Group (NSG) associated. This violates the requirement to protect resources using NSGs for network segmentation and access control.,Associate a Network Security Group (NSG) with the 'hub-subnet' by adding a 'networkSecurityGroup' property referencing an NSG resource. Define the NSG resource in the template and configure rules to allow only required traffic.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,scaleset-networks.bicep,38,Subnet 'scaleset' in virtual network 'vnet-${location}-v2' (line 38) does not have a Network Security Group (NSG) associated. NSGs are required to segment and protect network resources.,Associate a Network Security Group (NSG) with the 'scaleset' subnet in the virtual network. Define NSG rules to allow only required traffic.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,keyvault.bicep,18,"Key Vault 'networkAcls.defaultAction' is set to 'Allow' (line 18), which permits public network access to the Key Vault. This exposes the Key Vault to the public internet, violating ASB NS-2: Protect public endpoints.",Set 'networkAcls.defaultAction' to 'Deny' to restrict public access. Only allow required IPs or virtual networks via 'ipRules' and 'virtualNetworkRules'.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,storage-accounts.bicep,22,"storageAccountFunc: 'networkAcls.defaultAction' is set to 'Allow', which permits public network access to the storage account. This exposes the storage account to the public internet, violating ASB NS-2 (Protect public endpoints).",Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only required IPs or virtual networks. Example: properties.networkAcls.defaultAction = 'Deny'.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,storage-accounts.bicep,49,"fuzzStorageProperties: 'networkAcls.defaultAction' is set to 'Allow', which permits public network access to the storage account. This exposes the storage account to the public internet, violating ASB NS-2 (Protect public endpoints).",Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only required IPs or virtual networks. Example: fuzzStorageProperties.networkAcls.defaultAction = 'Deny'.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,storage-accounts.bicep,62,"storageAccountFuzz: 'properties' uses 'fuzzStorageProperties' where 'networkAcls.defaultAction' is set to 'Allow', which permits public network access to the storage account. This exposes the storage account to the public internet, violating ASB NS-2 (Protect public endpoints).",Set 'networkAcls.defaultAction' in 'fuzzStorageProperties' to 'Deny' and explicitly allow only required IPs or virtual networks.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,storage-accounts.bicep,85,"storageAccountsCorpus: 'properties' uses 'fuzzStorageProperties' where 'networkAcls.defaultAction' is set to 'Allow', which permits public network access to the storage accounts. This exposes the storage accounts to the public internet, violating ASB NS-2 (Protect public endpoints).",Set 'networkAcls.defaultAction' in 'fuzzStorageProperties' to 'Deny' and explicitly allow only required IPs or virtual networks.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-3,hub-network.bicep,4,"The virtual network 'hub-vnet' (line 4) and its subnet 'hub-subnet' (line 13) lack a Network Security Group (NSG), which is required to provide network-level access control and deny all inbound traffic by default.",Create and associate a Network Security Group (NSG) with the 'hub-subnet'. Ensure the NSG denies all inbound traffic by default and only allows necessary traffic.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-3,scaleset-networks.bicep,38,Subnet 'scaleset' in virtual network 'vnet-${location}-v2' (line 38) is missing a Network Security Group (NSG). NSGs are required to deny all inbound traffic by default and allow only necessary traffic.,Attach a Network Security Group (NSG) to the 'scaleset' subnet and configure rules to deny all inbound traffic except for explicitly allowed ports and protocols.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-4,hub-network.bicep,4,No Azure Firewall or third-party firewall is configured for the virtual network 'hub-vnet' (line 4). Advanced network protection is not present.,Deploy an Azure Firewall or a supported third-party firewall in the virtual network. Configure firewall policies and enable threat intelligence for advanced network protection.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,hub-network.bicep,4,Private Endpoints are not configured for any PaaS services in the virtual network 'hub-vnet' (line 4). This increases the attack surface for services like Storage and Key Vault.,"Configure Private Endpoints for all supported PaaS services (e.g., Storage, Key Vault) to ensure private access within the virtual network.",N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,keyvault.bicep,18,"Key Vault does not use a private endpoint. 'networkAcls.defaultAction' is 'Allow' (line 18), and there is no configuration for a private endpoint, violating ASB NS-5: Use Private Endpoints.",Configure a private endpoint for the Key Vault resource to ensure access is only available via private network connectivity.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,signalR.bicep,5,"Microsoft.SignalRService/signalR resource does not configure a private endpoint. Public access is not explicitly restricted, increasing the attack surface (line 5).",Configure a private endpoint for the SignalR resource by adding a Microsoft.Network/privateEndpoints resource and associating it with the SignalR instance.,N/A,AI,Generic
P2-Network-MEDIUM,Network Security,MEDIUM,NS-2,scaleset-networks.bicep,7,"Resource 'scalesetOutboundIp' (line 7) creates a public IP address, which exposes a public endpoint. Public endpoints should be restricted to required IPs only.",Restrict access to the public IP address by associating it with a firewall or load balancer that limits allowed source IPs. Consider using Azure Front Door or Application Gateway for additional protection.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,44,Sensitive information (APPINSIGHTS_INSTRUMENTATIONKEY) is assigned directly from the parameter 'app_insights_key' in the function app settings. Storing secrets in app settings instead of referencing Azure Key Vault exposes sensitive data.,Use a Key Vault reference for 'APPINSIGHTS_INSTRUMENTATIONKEY' in the app settings. Store the instrumentation key in Azure Key Vault and reference it using the '@Microsoft.KeyVault(SecretUri=...)' syntax.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,45,Sensitive information (APPINSIGHTS_APPID) is assigned directly from the parameter 'app_insights_app_id' in the function app settings. Storing secrets in app settings instead of referencing Azure Key Vault exposes sensitive data.,Use a Key Vault reference for 'APPINSIGHTS_APPID' in the app settings. Store the App Insights App ID in Azure Key Vault and reference it using the '@Microsoft.KeyVault(SecretUri=...)' syntax.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,46,Sensitive information (LOG_ANALYTICS_WORKSPACE_ID) is assigned directly from the parameter 'log_analytics_workspace_id' in the function app settings. Storing secrets in app settings instead of referencing Azure Key Vault exposes sensitive data.,Use a Key Vault reference for 'LOG_ANALYTICS_WORKSPACE_ID' in the app settings. Store the Log Analytics Workspace ID in Azure Key Vault and reference it using the '@Microsoft.KeyVault(SecretUri=...)' syntax.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,52,Sensitive information (CLI_APP_ID) is assigned directly from the parameter 'cli_app_id' in the function app settings. Storing secrets in app settings instead of referencing Azure Key Vault exposes sensitive data.,Use a Key Vault reference for 'CLI_APP_ID' in the app settings. Store the CLI App ID in Azure Key Vault and reference it using the '@Microsoft.KeyVault(SecretUri=...)' syntax.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,53,Sensitive information (AUTHORITY) is assigned directly from the parameter 'authority' in the function app settings. Storing secrets in app settings instead of referencing Azure Key Vault exposes sensitive data.,Use a Key Vault reference for 'AUTHORITY' in the app settings. Store the authority value in Azure Key Vault and reference it using the '@Microsoft.KeyVault(SecretUri=...)' syntax.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,59,Sensitive information (APPCONFIGURATION_ENDPOINT) is assigned directly from the parameter 'app_config_endpoint' in the function app settings. Storing secrets in app settings instead of referencing Azure Key Vault exposes sensitive data.,Use a Key Vault reference for 'APPCONFIGURATION_ENDPOINT' in the app settings. Store the App Configuration endpoint in Azure Key Vault and reference it using the '@Microsoft.KeyVault(SecretUri=...)' syntax.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,66,Sensitive information (ONEFUZZ_DATA_STORAGE) is assigned directly from the parameter 'fuzz_storage_resource_id' in the function app settings. Storing secrets in app settings instead of referencing Azure Key Vault exposes sensitive data.,Use a Key Vault reference for 'ONEFUZZ_DATA_STORAGE' in the app settings. Store the storage resource ID in Azure Key Vault and reference it using the '@Microsoft.KeyVault(SecretUri=...)' syntax.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,67,Sensitive information (ONEFUZZ_FUNC_STORAGE) is assigned directly from the parameter 'func_storage_resource_id' in the function app settings. Storing secrets in app settings instead of referencing Azure Key Vault exposes sensitive data.,Use a Key Vault reference for 'ONEFUZZ_FUNC_STORAGE' in the app settings. Store the storage resource ID in Azure Key Vault and reference it using the '@Microsoft.KeyVault(SecretUri=...)' syntax.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,68,Sensitive information (ONEFUZZ_MONITOR) is assigned directly from the parameter 'monitor_account_name' in the function app settings. Storing secrets in app settings instead of referencing Azure Key Vault exposes sensitive data.,Use a Key Vault reference for 'ONEFUZZ_MONITOR' in the app settings. Store the monitor account name in Azure Key Vault and reference it using the '@Microsoft.KeyVault(SecretUri=...)' syntax.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,69,Sensitive information (ONEFUZZ_KEYVAULT) is assigned directly from the parameter 'keyvault_name' in the function app settings. Storing secrets in app settings instead of referencing Azure Key Vault exposes sensitive data.,Use a Key Vault reference for 'ONEFUZZ_KEYVAULT' in the app settings. Store the Key Vault name in Azure Key Vault and reference it using the '@Microsoft.KeyVault(SecretUri=...)' syntax.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,70,Sensitive information (ONEFUZZ_OWNER) is assigned directly from the parameter 'owner' in the function app settings. Storing secrets in app settings instead of referencing Azure Key Vault exposes sensitive data.,Use a Key Vault reference for 'ONEFUZZ_OWNER' in the app settings. Store the owner value in Azure Key Vault and reference it using the '@Microsoft.KeyVault(SecretUri=...)' syntax.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,71,Sensitive information (ONEFUZZ_EVIDENCE_STORE_URL) is assigned directly from the parameter 'evidence_store_url' in the function app settings. Storing secrets in app settings instead of referencing Azure Key Vault exposes sensitive data.,Use a Key Vault reference for 'ONEFUZZ_EVIDENCE_STORE_URL' in the app settings. Store the evidence store URL in Azure Key Vault and reference it using the '@Microsoft.KeyVault(SecretUri=...)' syntax.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,72,Sensitive information (ONEFUZZ_LIQUID_REQUIREMENT_URL) is assigned directly from the parameter 'liquid_requirement_url' in the function app settings. Storing secrets in app settings instead of referencing Azure Key Vault exposes sensitive data.,Use a Key Vault reference for 'ONEFUZZ_LIQUID_REQUIREMENT_URL' in the app settings. Store the liquid requirement URL in Azure Key Vault and reference it using the '@Microsoft.KeyVault(SecretUri=...)' syntax.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,80,Sensitive information (REGISTRATION_APP_ID) is assigned directly from the parameter 'registration_app_id' in the function app settings. Storing secrets in app settings instead of referencing Azure Key Vault exposes sensitive data.,Use a Key Vault reference for 'REGISTRATION_APP_ID' in the app settings. Store the registration app ID in Azure Key Vault and reference it using the '@Microsoft.KeyVault(SecretUri=...)' syntax.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,function-settings.bicep,85,Sensitive information (ADO_ACCESS_CLIENT_ID) is assigned directly from the parameter 'ado_access_client_id' in the function app settings. Storing secrets in app settings instead of referencing Azure Key Vault exposes sensitive data.,Use a Key Vault reference for 'ADO_ACCESS_CLIENT_ID' in the app settings. Store the ADO access client ID in Azure Key Vault and reference it using the '@Microsoft.KeyVault(SecretUri=...)' syntax.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,instance-config.bicep,7,"Parameter 'specificConfig' is an object passed into the template, and its fields (such as tenant_id, cli_client_id, allowed_aad_tenants) are directly mapped into configuration variables. This may result in sensitive information (such as secrets or credentials) being stored in code or configuration, violating the requirement to never store secrets in code and to use Key Vault references.","Refactor the template to ensure all sensitive values (such as tenant IDs, client IDs, and any secrets) are referenced from Azure Key Vault using secure references, and not passed directly as parameters or stored in configuration objects.",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,operational-insights.bicep,97,"The output 'appInsightsInstrumentationKey' at line 97 exposes the Application Insights Instrumentation Key, which is a sensitive secret. This violates DP-3: Manage sensitive information disclosure.","Remove the output of the Instrumentation Key from the template. Instead, store secrets in Azure Key Vault and reference them securely. Do not expose sensitive keys in outputs.",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,server-farms.bicep,109,"The resource 'serverFarms_AntMDS_CERTIFICATE_PASSWORD_GENEVACERT' (line 109) sets 'settingValue' to an empty string for a certificate password, which may indicate a placeholder for a sensitive secret directly in the template. Storing secrets in code or templates violates ASB DP-3.",Remove any hardcoded or placeholder secrets from the template. Use Azure Key Vault references to securely retrieve secrets at deployment/runtime. Ensure 'settingValue' is not set directly in the template.,N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,NS-18,app-config.bicep,8,"The Microsoft.AppConfiguration/configurationStores resource does not configure a private endpoint. Public network access is not explicitly restricted, increasing the attack surface.",Configure a private endpoint for the App Configuration resource to restrict access to only resources within your virtual network. Add a 'privateEndpointConnections' child resource and set 'publicNetworkAccess' to 'Disabled'.,N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,NS-21,function.bicep,1,"The Storage Account 'funcStorage' is referenced as an existing resource, but there is no evidence of a Private Endpoint configuration for the storage account. Without a Private Endpoint, the storage account may be accessible over the public internet.",Configure a Private Endpoint for the referenced Storage Account to ensure access is only available from within the virtual network. Update the storage account resource to include a 'privateEndpointConnections' property or deploy a 'Microsoft.Network/privateEndpoints' resource targeting the storage account.,N/A,AI,Generic
P6-Other-MEDIUM,Unknown,MEDIUM,NS-18,function.bicep,44,"The App Service 'Microsoft.Web/sites@2021-03-01' resource does not explicitly restrict public access. By default, App Services are internet-accessible unless access restrictions are configured.",Configure access restrictions for the App Service to allow only required IP addresses or subnets. Use the 'ipSecurityRestrictions' property in the siteConfig or deploy an App Service Access Restriction rule to limit public exposure.,N/A,AI,Generic
P6-Other-MEDIUM,Unknown,MEDIUM,NS-22,hub-network.bicep,4,Network monitoring is not enabled for the virtual network 'hub-vnet' (line 4). NSG flow logs and Network Watcher are not configured.,Enable NSG flow logs and configure Azure Network Watcher for the virtual network. Set up alerts for suspicious network activity.,N/A,AI,Generic
