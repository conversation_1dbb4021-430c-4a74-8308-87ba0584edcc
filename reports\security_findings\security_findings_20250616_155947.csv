Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
CRITICAL,NS-2,LacpBillingExhaust.Template.json,36,"The Kusto cluster resource is created with no explicit network access controls, meaning its public endpoint is potentially unprotected. There is no evidence of access restrictions (e.g., IP allow list, trusted VNet, or private link), exposing sensitive data to the internet.","Enable private endpoint access for the Kusto cluster and disable or restrict the public endpoint. If public endpoint is required, set up firewall rules to allow only trusted IP ranges and block all others.",N/A,AI,Generic
CRITICAL,NS-2,LacpGeo.Template.json,222,"The Cosmos DB account ('Microsoft.DocumentDB/databaseAccounts') is configured with 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false, exposing the database to the public internet. This violates ASB's requirement to secure public endpoints for databases.",Set 'publicNetworkAccess' to 'Disabled' and enable 'isVirtualNetworkFilterEnabled' to restrict network access to trusted subnets or private endpoints.,N/A,AI,Generic
CRITICAL,NS-1,LacpGeo.Template.json,222,The Cosmos DB account is not protected by network security controls (both 'virtualNetworkRules' and 'ipRules' are empty and 'isVirtualNetworkFilterEnabled' is false). This leaves the database unprotected from unauthorized direct access.,Enable 'isVirtualNetworkFilterEnabled': true and define 'virtualNetworkRules' or 'ipRules' to restrict network access to only authorized networks or subnets.,N/A,AI,Generic
CRITICAL,NS-1,LacpGlobal.Template.json,82,"Cosmos DB account is deployed with 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false. This allows unrestricted public network access to the Cosmos DB instance, which exposes it directly to the internet without any network-level access restrictions.",Set 'publicNetworkAccess' to 'Disabled' or 'isVirtualNetworkFilterEnabled' to true. Restrict access by adding specific 'virtualNetworkRules' and/or 'ipRules' to limit exposure to only trusted networks.,N/A,AI,Generic
CRITICAL,NS-2,LacpGlobal.Template.json,82,Cosmos DB account does not implement any IP or VNet-based restrictions ('ipRules': [] and 'virtualNetworkRules': []). The service is directly accessible over the public internet with no access controls.,Restrict public access by specifying trusted IP addresses in 'ipRules' or enable 'isVirtualNetworkFilterEnabled' and specify 'virtualNetworkRules' for private access only.,N/A,AI,Generic
CRITICAL,NS-1,LacpRegion.Template.json,1056,"The CosmosDB account ('Microsoft.DocumentDB/databaseAccounts') has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to 'false', resulting in unrestricted public access. This violates ASB NS-1: resources must be protected using NSGs/Azure Firewall or private endpoints.",Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to 'true'. Explicitly configure 'virtualNetworkRules' or private endpoints to allow only trusted networks.,N/A,AI,Generic
CRITICAL,NS-2,LacpRegion.Template.json,1056,"CosmosDB account exposes public endpoints without virtual network rules, increasing the risk of unauthorized external access (NS-2).",Restrict public endpoints by setting 'publicNetworkAccess' to 'Disabled' and configure 'virtualNetworkRules' for explicitly allowed subnets/networks.,N/A,AI,Generic
CRITICAL,DP-3,LacpStamp.Parameters-LacpStampResources.json,78,"Sensitive storage account key is referenced as an ARM parameter ('dasStorageAccountKey'), which exposes secrets in deployment files and parameters, risking disclosure to those with access to ARM templates, deployment logs, or version control.","Do not store or pass storage account keys as parameters. Instead, store sensitive secrets like storage keys in Azure Key Vault and use managed identities to access them securely at runtime.",N/A,AI,Generic
HIGH,NS-1,IngestionStorageAccount.Template.json,43,The storage accounts being deployed do not restrict network access to trusted networks or subnets and allow default access from all networks by not specifying 'networkAcls'. This exposes the storage account endpoints over the public internet.,Add the 'networkAcls' property for each storage account resource to restrict access to selected virtual networks and IP address ranges only. Set 'defaultAction' to 'Deny' and explicitly specify allowed networks/subnets.,N/A,AI,Generic
HIGH,NS-2,IngestionStorageAccount.Template.json,43,"By not configuring network rules ('networkAcls'), storage account endpoints are exposed as public endpoints by default, increasing risk of unauthorized access.","Explicitly define private endpoints or restrict public network access for the storage accounts by setting up 'networkAcls'. If public access is required, limit it to specific trusted sources only.",N/A,AI,Generic
HIGH,NS-1,LacpBilling.Template.json,296,"The storage accounts defined in the template do not implement any network security controls such as network rules, virtual network service endpoints, or private endpoints. By default, this leaves the storage account accessible from all networks, which is against ASB NS-1 (Protect resources using network security groups or Azure Firewall).","Add networkAcls property to the storage account resource to restrict access. Deny public network access and only allow selected networks (e.g., via virtual network rules or private endpoints). Example: { ""networkAcls"": { ""defaultAction"": ""Deny"", ""bypass"": ""AzureServices"", ""virtualNetworkRules"": [...] } }.",N/A,AI,Generic
HIGH,NS-2,LacpBilling.Template.json,296,"Public endpoint protection is not configured for storage accounts. Without explicit networkAcls restrictions, the storage account endpoints are exposed to the public internet.",Explicitly configure networkAcls on the storage account to deny public network access. Consider enabling 'privateEndpointConnections' and set 'publicNetworkAccess' to 'Disabled'.,N/A,AI,Generic
HIGH,NS-1,LacpBillingExhaust.Template.json,36,"The Microsoft.Kusto (Azure Data Explorer) cluster is deployed without specifying any private endpoints or network security restrictions. By default, this means the resource may be accessible via public endpoints, increasing exposure to external threats. ASB requires sensitive resources to be protected using NSGs or Azure Firewall.","Configure the Azure Data Explorer cluster with a private endpoint or restrict access using a Virtual Network (VNet) and appropriate NSG rules. If public endpoint access is required, specify allowed client IP ranges or configure the cluster for limited external access.",N/A,AI,Generic
HIGH,DP-1,LacpBillingExhaust.Template.json,36,"No explicit encryption at rest configuration is defined for the Kusto cluster or its databases. By default, Azure Kusto uses platform-managed keys for encryption at rest, but ASB recommends explicitly configuring and documenting encryption, and if necessary, using customer-managed keys for higher assurance.","Configure encryption at rest explicitly for the Azure Data Explorer (Kusto) cluster, and consider enabling customer-managed keys (CMK) for the highest assurance. Document your encryption posture as required by organizational policy.",N/A,AI,Generic
HIGH,DP-3,LacpBillingExhaustExport.Template.json,16,"Sensitive information such as Kusto ingestion and query endpoint URIs (adxExhaustUri, adxExhaustDataIngestionUri) are provided as standard parameters and could be visible in deployment logs or templates. Azure Security Benchmark recommends sensitive connection information be protected using Azure Key Vault.","Refactor the deployment template to source these sensitive configuration parameters from Azure Key Vault references within Azure Resource Manager parameters, using secure reference functionality. This limits exposure in deployment pipelines and templates.",N/A,AI,Generic
HIGH,DP-1,LacpBillingExhaustExport.Template.json,49,"There is no property or enforcement for encryption at rest for the data exports to Azure Data Explorer (ADX) or other storage locations. While underlying services may encrypt by default, omission in the template means encryption is not explicitly ensured or configured.",Explicitly add or document encryption settings for any target resource (such as enabling customer-managed keys or confirming/ensuring encryption in the storage/data destination resource definitions referenced by the export pipeline).,N/A,AI,Generic
HIGH,NS-2,LacpBillingExhaustExport.Template.json,54,"There is no validation or restriction to protect possible public endpoints defined in 'adxExhaustUri' and 'adxExhaustDataIngestionUri' parameters. If these URIs point to publicly accessible ADX endpoints or ingestion endpoints, data export could be exposed to public networks.","Restrict the target endpoints to private endpoints or those in trusted virtual networks only. Use Azure Private Link for secure, non-public connectivity and include validation or explicit configuration for private network enforcement in the deployment.",N/A,AI,Generic
HIGH,NS-8,LacpGeo.Template.json,222,Cosmos DB is accessible from any IP address due to 'publicNetworkAccess' being enabled and network rules being absent. Public access endpoints increase exposure to brute force and data exfiltration attacks.,Disable public network access and utilize private endpoints or restrict with 'ipRules' and 'virtualNetworkRules' for legitimate sources.,N/A,AI,Generic
HIGH,AM-1,LacpGeo.Template.json,54,"Some Key Vault access policies (particularly those for 'managedIdentitiesSecurityGroupId' and 'lacpAadServicePrincipal') include broad permissions ('Get' and 'List' for keys, secrets, and certificates), potentially granting more privilege than necessary.","Review and assign access policies or RBAC roles following the 'least privilege principle', granting only those permissions specifically required for the application or principal functionality.",N/A,AI,Generic
HIGH,NS-1,LacpGlobal.Template.json,124,Storage accounts are deployed with no network rules—public access is not explicitly denied and no 'networkAcls' block public traffic. This means storage accounts could be exposed to the public internet.,"Add 'networkAcls' to the storage account definition to allow only necessary subnets or trusted IPs, and set 'defaultAction' to 'Deny' to block public access by default.",N/A,AI,Generic
HIGH,NS-2,LacpGlobal.Template.json,124,"Storage accounts have no restrictions on public network access; default settings expose endpoints to the internet, which may lead to unauthorized access or data leakage.",Configure 'networkAcls' with 'defaultAction' set to 'Deny' and add only necessary virtual network/subnet rules and/or trusted IP ranges to permit legitimate access.,N/A,AI,Generic
HIGH,NS-2,LacpRegion.Parameters-LacpRegionResources.json,33,"The parameter 'isBoundariesRestricted' is set to 'false'. This suggests that network boundaries protection is not enabled, potentially exposing Logic App or related resources to public endpoints. This can increase the risk of unwanted or malicious access.","Set 'isBoundariesRestricted' to 'true' and implement private endpoints or IP restrictions for Logic Apps and other critical resources to limit public exposure, in line with ASB NS-2.",N/A,AI,Generic
HIGH,DP-3,LacpRegion.Parameters-LacpRegionResources.json,35,"Sensitive information such as 'lacpAadServicePrincipal', 'ev2BuildoutAppObjectId', 'cosmosDbEsgPrincipalId', and other IDs are specified inline as plain parameter values. Storing sensitive identities or connection information directly in parameter files increases the risk of disclosure.","Store sensitive values (such as service principal IDs or secrets) in Azure Key Vault and reference them securely in your templates, using ARM's key vault reference pattern.",N/A,AI,Generic
HIGH,NS-1,LacpRegion.Template.json,582,"Storage accounts ('Microsoft.Storage/storageAccounts') do not specify network rules or service endpoints, allowing default public network access.","Restrict access to storage accounts using network rules. Under 'properties', add 'networkAcls' specifying allowed 'virtualNetworkRules', 'ipRules', and set 'defaultAction': 'Deny'.",N/A,AI,Generic
HIGH,NS-2,LacpRegion.Template.json,582,"Storage account resources are deployed without public access controls. Without 'networkAcls', these accounts are accessible over the public internet unless blocked.","Under each storage account's 'properties', configure 'networkAcls' to deny public network traffic by default and whitelist only necessary Azure subnets or specific IPs.",N/A,AI,Generic
HIGH,NS-1,LacpRegion.Template.json,1437,"Key Vault ('Microsoft.KeyVault/vaults') resource does not restrict network access with firewall rules or private endpoints. By default, it will be accessible from all networks.","Add the 'networkAcls' property to the Key Vault resource definition, set 'defaultAction' to 'Deny', and list allowed 'virtualNetworkRules' or 'ipRules'. Consider enforcing private endpoint access only for high sensitivity workloads.",N/A,AI,Generic
HIGH,DP-2,LacpRegion.Template.json,1756,"Blob versioning is explicitly set to 'false' on the AMS backup storage account Blob service ('isVersioningEnabled': false), limiting recoverability in the event of accidental deletion or ransomware.",Enable blob versioning by setting 'isVersioningEnabled' to 'true' for all backup and sensitive storage accounts.,N/A,AI,Generic
HIGH,DP-3,LacpRegion.Template.json,2547,Storage account access keys are extracted via ARM template expressions and placed into Azure Key Vault secrets. The keys are then potentially exposed as deployment outputs and may be accessible if outputs are not sufficiently protected or if Key Vault is not network restricted.,Avoid outputting sensitive keys in ARM outputs. Ensure Key Vault is network protected and that only necessary roles have access to secrets. Prefer using Azure AD identities/Managed Identities over key-based access where possible.,N/A,AI,Generic
HIGH,DP-3,LacpRegion.Template.json,3769,"Outputs section exposes storage account key ('dasStorageAccountKey') as an output value. This disclosure increases risk of secrets being logged, intercepted, or accessible in deployment history.","Never output or surface sensitive information such as storage account keys in template outputs. Instead, reference secrets securely at runtime or use managed identities.",N/A,AI,Generic
HIGH,DP-3,LacpStamp.Parameters-LacpStampResources.json,83,"Key Vault is referenced ('globalKeyVaultName'), but 'createSharedKeyVault' is set to 'false', potentially indicating that a secure key vault for managing secrets will not be created in this deployment, risking secret sprawl or insecure secret management.","Ensure all secrets and keys are stored only in Azure Key Vault, and set 'createSharedKeyVault' to 'true' (or ensure a secure existing key vault is integrated) so all sensitive data is managed securely.",N/A,AI,Generic
HIGH,IM-8,LacpStamp.Parameters-LacpStampResources.json,52,"The deployment uses several service principals passed as parameters (e.g., 'lacpAadServicePrincipal', 'infraServicePrincipalGlobal'), indicating the likely use of application secrets/keys for automation or inter-service authentication instead of managed identities.","Replace service principal credentials with Azure Managed Identities for Logic Apps and downstream resources to enable secure, credential-less authentication between resources.",N/A,AI,Generic
HIGH,NS-2,LacpStamp.Parameters-LacpStampResources.json,78,"The storage account appears to be deployed with a default DNS suffix ('core.windows.net'), but there is no indication of public network access being disabled, which risks exposure of storage endpoints to the public internet.","Ensure the storage account disables public network access by setting 'publicNetworkAccess' to 'Disabled', using private endpoints, or restricting access to allowed networks/subnets only.",N/A,AI,Generic
HIGH,NS-1,LacpStamp.Template.json,589,"Storage accounts are deployed without specifying any network rules to restrict access (e.g., no use of `networkAcls` or private endpoints). By default, storage accounts are accessible from all networks except when restricted. This violates the control to protect resources using NSGs or Azure Firewall.","Add networkAcls to all storage account resources to restrict access to specific virtual networks, IP ranges, or require the use of private endpoints. Consider disabling public network access completely where possible.",N/A,AI,Generic
HIGH,NS-2,LacpStamp.Template.json,589,"Storage accounts do not have explicit configuration to deny public network access. This means that, unless a default policy blocks it elsewhere, these storage accounts may be exposed to public endpoints which increases risk of unauthorized access.",Explicitly set the storageAccounts' 'publicNetworkAccess' property to 'Disabled' and leverage private endpoints for access. Only allow exceptions if required and document justification.,N/A,AI,Generic
HIGH,NS-3,LacpStamp.Template.json,589,There is no evidence of Network Security Groups (NSGs) being associated with storage accounts or subnets for restricting access. Critical Azure resources should be behind proper network segmentation.,Associate storage accounts with subnets protected by properly configured NSGs that allow only necessary network traffic from trusted resources/services.,N/A,AI,Generic
HIGH,AM-1,LacpStamp.Template.json,157,"Key Vault 'accessPolicies' grant broad permissions (including full admin rights - e.g., 'Set', 'Delete', 'Recover', 'Backup', 'Restore') to multiple principals. This could violate the principle of least privilege, increasing the risk if the identity is compromised.","Review and limit Key Vault access policies so that each identity has only the permissions absolutely required for its function. Remove admin-level permissions (like 'Delete', 'Recover', etc.) from identities and groups not needing such access.",N/A,AI,Generic
HIGH,DP-3,LacpStamp.Template.json,996,"The template assigns the value of 'dasStorageAccountKey' (a parameter) directly as a secret in Key Vault. If 'dasStorageAccountKey' is provided via parameters, it may be exposed in deployment logs or parameter files, risking sensitive data leakage.",Do not pass secrets as parameters in ARM/Bicep templates. Retrieve sensitive values such as storage keys directly from Azure Key Vault in consuming services or use managed identities with granular RBAC.,N/A,AI,Generic
HIGH,NS-1,ReadAdxExhaust.Template.json,17,"The Kusto cluster resource (Microsoft.Kusto/clusters) has no network security configuration present. There is no specification of virtual network integration, private endpoints, Network Security Groups (NSGs), or Azure Firewall to restrict network access. By default, this leaves the resource potentially accessible from public internet or other unwanted subnets, violating network security best practices.","Integrate the Kusto cluster with a virtual network (using the 'virtualNetworkConfiguration' property), enable private endpoints, and/or apply NSGs to restrict inbound and outbound access to only trusted networks. Refer to Kusto network hardening guidance and ASB NS-1.",N/A,AI,Generic
HIGH,NS-2,ReadAdxExhaust.Template.json,17,No measures are observed to protect public endpoints for the Kusto cluster. Exposing cluster endpoints publicly increases the risk of unauthorized data access or exploitation over the internet.,"Enable private endpoints for the Kusto cluster to eliminate public exposure and restrict all endpoint access to selected private networks (VNets). If public endpoints are required, limit access via IP firewall rules to known/trusted IPs only.",N/A,AI,Generic
HIGH,NS-3,ReadAdxExhaust.Template.json,17,"There are no Network Security Groups (NSGs) associated with the Kusto cluster or its subnet. Without NSGs, there is no layer-4 network-level traffic restriction, increasing the risk of unauthorized access.","Deploy the Kusto cluster in a subnet protected by an NSG, configuring the NSG to restrict traffic only to necessary ports and trusted IP ranges/subnets.",N/A,AI,Generic
HIGH,IM-6,RoleAssignment.Template.json,56,"The Contributor role ('b24988ac-6180-42a0-ab88-20f7382dd24c') is being assigned to the Ev2 Buildout service principal, granting broad permissions across the subscription. This violates the principle of least privilege.","Review the required permissions for the Ev2 Buildout service principal and assign a custom role or a narrower built-in role with only the necessary permissions, minimizing the risk of excessive privilege escalation.",N/A,AI,Generic
HIGH,AM-1,RoleAssignment.Template.json,56,"Assignment of the 'Contributor' role to an application/service principal provides full management access to all resources, increasing attack surface and potential for abuse if credentials are compromised.",Restrict role assignments to the minimum permissions required for the application to function. Implement Just-in-Time (JIT) access if feasible and perform regular access reviews.,N/A,AI,Generic
HIGH,NS-2,TrafficManagerEndpoints.Template.json,53,"The Traffic Manager external endpoints are defined with DNS targets that are publicly accessible (constructed from cluster naming and regional parameters) but there is no configuration of IP whitelisting, firewall, or access restriction. This exposes the endpoints to the public internet without explicit safeguards, contravening the requirement to secure all public endpoints.","Restrict access to external endpoints by implementing firewall rules, allow-listing, or moving to private endpoints where feasible. Ensure Traffic Manager profiles have appropriate endpoint monitoring and access controls. Document and justify any intentional public exposure.",N/A,AI,Generic
MEDIUM,DP-1,LacpBilling.Template.json,296,"The storage account resource does not explicitly specify encryption settings. While Azure Storage enables encryption at rest by default, best practice is to explicitly configure these settings to avoid potential misconfigurations or drift.","Add the encryption property in the storage account resource to specify encryption services (e.g., for 'blob', 'file', 'queue', 'table') and enforce keySource as 'Microsoft.Managed' or 'Microsoft.Keyvault' as appropriate for compliance.",N/A,AI,Generic
MEDIUM,DP-2,LacpBilling.Template.json,300,"Although 'minimumTlsVersion' is set to 'TLS1_2' and 'supportsHttpsTrafficOnly' is true, it is important to ensure these remain enforced and never lowered in future changes to maintain secure transit.","No remediation required currently, but enforce change management so these settings remain at or above TLS1_2 and HTTPS-only. Add policy assignments or template validation rules to ensure compliance.",N/A,AI,Generic
MEDIUM,AM-1,LacpBilling.Template.json,393,"Role assignment for Storage Queue Data Contributor is created over each storage account, but the principal ID is derived from a managed identity with unclear scoping in this template. Without further scoping or conditions, this could violate least privilege.","Document and periodically review all principal assignments to confirm only the required managed identities have the least privilege roles necessary for the application to function (e.g., Storage Queue Data Contributor only if queue operations are needed). Remove all other unnecessary role assignments. Use management group or policy to audit and enforce least privilege.",N/A,AI,Generic
MEDIUM,IM-8,LacpBillingExhaust.Template.json,3,"No managed identities are assigned to the resource. The template uses parameters for 'usageAccountSystemAssignedIdentityPrincipalId' and 'podIdentityObjectID' to assign principal roles, but there is no evidence that the Microsoft.Kusto resource has an identity (system-assigned or user-assigned). ASB recommends using managed identities for secure authentication between Azure resources.",Assign a system-assigned or user-assigned managed identity to the Microsoft.Kusto cluster by including the 'identity' property in its resource definition.,N/A,AI,Generic
MEDIUM,AM-1,LacpBillingExhaust.Template.json,66,Principal assignments for 'User' and 'Ingestor' roles are provided to identities as parameters. There is no evidence of least privilege enforcement or scoping. These roles could potentially grant unnecessary permissions if parameterized incorrectly.,Review role assignments and ensure only the minimum required permissions are granted to each principal. Regularly audit these assignments and validate that roles follow the principle of least privilege.,N/A,AI,Generic
MEDIUM,DP-3,LacpBillingExhaust.Template.json,0,"Sensitive information such as principal IDs are passed directly as parameters, but there is no mention of integration with Azure Key Vault or other secure secret storage mechanisms for sensitive values. Storing secrets or sensitive IDs in parameter files or templates can lead to information disclosure.","Store sensitive parameters (e.g., principal IDs, secrets, keys) in Azure Key Vault and reference them securely from the template using linked templates or ARM/Bicep Key Vault integration features.",N/A,AI,Generic
MEDIUM,IM-2,LacpBillingExhaust.Template.json,0,"There is no evidence that Multi-Factor Authentication (MFA) is enforced for users or administrators who will have access to the Kusto cluster or associated resources. While this may be partially enforced outside of the template through Azure AD policies, lack of explicit mention is a potential gap.",Ensure Azure AD Conditional Access policies are in place to enforce MFA for all users and administrators that access resources deployed by this template.,N/A,AI,Generic
MEDIUM,IM-3,LacpBillingExhaust.Template.json,0,"Conditional Access Policies are not referenced or enforced in the deployment. Without these, there is risk of unauthorized or unsafe access to the resources.","Implement and audit Conditional Access Policies in Azure AD to restrict access to the Kusto cluster and any associated data based on user, device, location, risk, etc.",N/A,AI,Generic
MEDIUM,DP-2,LacpBillingExhaustExport.Template.json,54,"There is no enforcement or configuration ensuring the use of TLS 1.2+ when communicating with the 'kustoUri' and 'dataIngestionUri' endpoints. Data could potentially be sent to endpoints using insecure protocols, violating data-in-transit encryption best practices.","Ensure that parameter values for these endpoints require HTTPS (TLS 1.2+) URIs and do not allow unsecured endpoint configurations. Add parameter validation, or document this as a strict requirement for pipeline operation.",N/A,AI,Generic
MEDIUM,IM-1,LacpBillingExhaustExport.Template.json,49,The template does not specify the use of managed identities or Azure Active Directory authentication for accessing destination services (such as ADX). This could result in use of less-secure identity or access methods.,Use system-assigned or user-assigned managed identities for Data Factory/accessing ADX and configure all resources to utilize Azure AD authentication. Parameterize or enforce managed identity usage in the deployment and subsequent pipeline configuration.,N/A,AI,Generic
MEDIUM,DP-1,LacpGeo.Template.json,37,"The Key Vault uses the 'Standard' SKU, which does not natively offer dedicated HSM-backed keys (customer-managed keys with elevated assurances). If sensitive/regulated data is being stored, lack of HSM-backed keys could be a compliance issue.","If compliance requirements demand HSM-backed keys, use the 'Premium' Key Vault SKU for dedicated HSM support.",N/A,AI,Generic
MEDIUM,IM-6,LacpGeo.Template.json,52,"Key Vault access policies are assigned using access policy objects in ARM, not Azure RBAC, which does not leverage Azure RBAC's granular controls and auditing for Key Vault access management as recommended by ASB.",Transition Key Vault access management to Azure RBAC by enabling the 'RBAC Authorization' model for the Key Vault and assigning permissions via Azure RBAC roles instead of access policies.,N/A,AI,Generic
MEDIUM,DP-2,LacpGlobal.Template.json,132,"'minimumTlsVersion' for Storage Account is set to 'TLS1_2', which is correct for data encryption in transit. However, there is no enforcement for Cosmos DB minimal TLS version, as the template accepts it as a parameter ('minimalCosmosDbTlsVersion') without validation.",Validate that the 'minimalCosmosDbTlsVersion' parameter is always set to at least 'TLS1_2'. Consider hardcoding or restricting the value to prevent use of legacy versions.,N/A,AI,Generic
MEDIUM,IM-1,LacpGlobal.Template.json,83,"The Cosmos DB account disables local authentication ('disableLocalAuth': true), enforcing Azure AD-based identity, which is positive. However, storage account SAS keys are generated and stored as plain secrets, and there are no explicit references to Azure AD identity-based authentication for storage.","Enforce Azure AD-based authentication for all storage operations when possible, and limit or monitor usage of storage access keys.",N/A,AI,Generic
MEDIUM,IM-7,LacpMsiStamp.Teamplate.json,17,The deployment creates a user-assigned managed identity but does not define or limit the permissions/role assignments for this identity. Assigning broad or excessive permissions in subsequent operations may result in privilege escalation or data exposure if not properly managed. ASB recommends restricting application identities' privileges.,"Ensure that when assigning roles to this managed identity, only the minimum required permissions are granted using role-based access control (RBAC). Periodically review the assigned roles to confirm alignment with least privilege principles.",N/A,AI,Generic
MEDIUM,DP-2,LacpRegion.Parameters-LacpRegionResources.json,56,"The parameter 'minimalCosmosDbTlsVersion' is explicitly set to 'Tls12', which is compliant with ASB DP-2. However, other resources—such as Logic Apps' HTTP triggers, connections, or storage accounts—do not have explicit enforcement of TLS 1.2+ required, and this parameter does not guarantee all resources enforce this protocol.","Ensure all Logic Apps connections, storage accounts, and other resources (not just Cosmos DB) explicitly require and enforce TLS 1.2 or higher for all data in transit.",N/A,AI,Generic
MEDIUM,AM-1,LacpRegion.Template.json,1437,"Key Vault access policies grant 'Get' and 'List' for keys, secrets, and certificates to several identities, including groups and service principals, which may not be required for all use cases. This can lead to standing privileged access.",Review and reduce access policy permissions to align with the least privilege principle. Only grant necessary access to those identities which need specific secrets/keys.,N/A,AI,Generic
MEDIUM,NS-1,LacpStamp.Parameters-LacpStampResources.json,0,"Parameters for Network Security Groups (NSGs) or network restrictions are missing in the template, which may result in Logic Apps, storage accounts, and other sensitive resources being accessible without proper network segmentation.","Explicitly define and implement network security groups, Azure Firewall, or appropriate networking controls for Logic Apps and supporting resources to restrict access only to required subnets/IPs and block public access where unnecessary.",N/A,AI,Generic
MEDIUM,DP-1,LacpStamp.Template.json,589,"Storage accounts do not explicitly set the 'encryption' property. While encryption is default enabled in Azure, it is best practice to specify and enforce encryption settings explicitly in IaC to prevent accidental configuration drift.","Explicitly add and configure the 'encryption' property for each Microsoft.Storage/storageAccounts resource. Define the keySource (e.g., 'Microsoft.Storage' or customer-managed keys) and ensure encryption for both blob and file services.",N/A,AI,Generic
MEDIUM,DP-3,LacpStamp.Template.json,868,"The ARM template concatenates and stores storage account connection strings (including keys) as Key Vault secrets, but the format '[...];AccountKey=...' leaks connection strings in template evaluation (e.g., in deployment logs), if not handled securely.",Ensure sensitive expressions such as connection strings are securely handled using Azure Key Vault references or by deploying them securely post-deployment. Avoid constructing secrets in ARM expressions where possible. Ensure no secrets are exposed in deployment outputs or logs.,N/A,AI,Generic
MEDIUM,IM-8,ReadAdxExhaust.Template.json,17,"There is no configuration to enable managed identities for the Kusto cluster resource. Managed identities support secure authentication for resource-to-resource communications, reducing risks related to secret management and lateral movement.",Enable a system-assigned or user-assigned managed identity on the Kusto cluster to facilitate secure authentication to downstream Azure resources without embedding credentials.,N/A,AI,Generic
MEDIUM,DP-1,ReadAdxExhaust.Template.json,17,"The deployment does not explicitly enable encryption at rest for the Kusto cluster. Although Azure Kusto clusters are encrypted by default, specifying encryption settings (such as customer-managed keys) is a security best practice if stricter compliance requirements exist.","Configure the 'keyVaultProperties' property in the cluster resource to enable customer-managed keys for encryption at rest, especially if compliance requires you to manage your own keys.",N/A,AI,Generic
MEDIUM,IM-8,ReadUsageAccount.Template.json,15,"The template references system-assigned managed identity output, but the resource definition for 'Microsoft.UsageBilling/accounts' does not include the 'identity' property to enable a managed identity. This fails to ensure secure resource-to-resource authentication.",Add the 'identity' block with 'type': 'SystemAssigned' to the 'Microsoft.UsageBilling/accounts' resource to enable a system-assigned managed identity.,N/A,AI,Generic
MEDIUM,IM-7,RoleAssignment.Template.json,56,"Application identity (Ev2 Buildout service principal) is granted highly privileged access. No indication of secure secrets management, credential rotation, or monitoring of this identity's activities.","Ensure application identities are managed via Azure AD, credentials are rotated and stored in Azure Key Vault, and their usage is monitored. Limit the scope of privilege wherever possible.",N/A,AI,Generic
MEDIUM,IM-5,RoleAssignment.Template.json,0,"The template does not include (or reference) activity logging or monitoring configuration for these privileged role assignments, making it harder to detect misuse or anomalous activity.",Configure Azure Activity Logs and Azure AD Sign-In Logs to monitor for role assignments and any high-privilege actions. Consider integrating with Microsoft Sentinel or Defender for Cloud for alerting.,N/A,AI,Generic
MEDIUM,AM-3,RoleAssignment.Template.json,56,"Privileged role assignments (Contributor, Storage Account Key Operator Service Role) are not managed through Azure Privileged Identity Management (PIM), exposing the environment to continuous exposure of high privilege accounts.","Manage high-privilege roles using Azure AD Privileged Identity Management to enforce just-in-time access, approval workflows, and audit trails.",N/A,AI,Generic
LOW,IM-1,LacpBilling.Template.json,304,"Azure Active Directory integration is partially enforced via 'defaultToOAuthAuthentication': true, and 'allowSharedKeyAccess': false, but does not explicitly detail Azure AD RBAC or external user restrictions at the storage account level.","Review role assignments to ensure only authorized Azure AD identities (users, groups, managed identities) have access with least-privilege roles (preferably Storage Blob Data Contributor/Reader). Consider explicitly documenting or outputting the assigned principal IDs in the deployment.",N/A,AI,Generic
LOW,DP-3,LacpGeo.Template.json,143,"The Cosmos DB primary master key is stored as a plaintext value in an Azure Key Vault secret. Storing sensitive database keys in Key Vault is generally appropriate, but ensure secret permissions are tightly limited and audit logging is enabled.",Ensure only essential identities have 'Get' and 'List' secret permissions. Regularly audit Key Vault access logs and consider rotating keys and secrets on a scheduled basis.,N/A,AI,Generic
LOW,DP-3,LacpGlobal.Template.json,216,"Primary Cosmos DB master key and storage account connection strings (including keys) are stored in Azure Key Vault, aligning with the benchmark. However, access policies should be regularly reviewed to ensure least privilege.","Periodically review and audit Key Vault access policies to ensure that only necessary principals have access to secrets and keys, and consider implementing RBAC-based access management for improved tracking and control.",N/A,AI,Generic
LOW,IM-5,LacpMsiStamp.Teamplate.json,17,"This template does not include diagnostic settings or logging for the managed identity resource. Visibility into identity operations is necessary for detecting misuse or compromise, as recommended by ASB.","Enable diagnostic settings and send logs to a Log Analytics workspace, Storage Account, or Event Hub to monitor activities involving this managed identity.",N/A,AI,Generic
LOW,DP-2,ReadAdxExhaust.Template.json,17,"There is no explicit enforcement of encryption in transit (TLS 1.2+) for the Kusto cluster endpoints. While Azure services typically enforce TLS for service endpoints, an explicit policy is recommended for highly sensitive or regulated environments.","If available, configure the resource to require TLS 1.2+ for all connections. Document/verify this requirement in operational guidance and enforcement.",N/A,AI,Generic
LOW,IM-4,RoleAssignment.Template.json,0,"No evidence that role assignments are subject to review or automated expiration, risking persistent elevated access if not periodically re-evaluated.","Implement Azure AD Access Reviews and configure role assignment expirations, especially for privileged accounts and service principals.",N/A,AI,Generic
