#!/usr/bin/env python3
"""
Launch script for IaC Guardian MCP Server
Use this to test the MCP server locally.
"""

import asyncio
import sys
from mcp_server import main

if __name__ == "__main__":
    print("🚀 Starting IaC Guardian MCP Server...")
    print("📝 Use Ctrl+C to stop the server")
    print("🔗 Server will be available for VS Code Copilot integration")
    print("-" * 60)
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")
        sys.exit(1)
