"""
Refactored resource type detection module demonstrating improved architecture.
This shows how to implement the suggested improvements for the _determine_resource_type method.
"""

import os
import re
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass, field
from functools import lru_cache
from enum import Enum
import hashlib

logger = logging.getLogger(__name__)

class MatchType(Enum):
    """Types of matches for resource detection."""
    EXACT = 10.0
    PROVIDER_AND_RESOURCE = 8.0
    PROVIDER_ONLY = 5.0
    KEYWORD = 0.5
    BICEP_TYPE = 1.0
    TERRAFORM_TYPE = 3.0
    ARM_TYPE = 3.0

@dataclass
class ResourceScore:
    """Represents a resource type score with details."""
    resource_type: str
    score: float = 0.0
    matches: List[Tuple[str, MatchType]] = field(default_factory=list)
    
    def add_match(self, match_text: str, match_type: MatchType):
        """Add a match and update score."""
        self.matches.append((match_text, match_type))
        self.score += match_type.value

@dataclass
class ResourceDetectionResult:
    """Result of resource type detection."""
    detected_type: str
    confidence: float
    all_scores: Dict[str, float]
    reasoning: List[str]

class ResourceTypeDetector(ABC):
    """Abstract base class for resource type detection strategies."""
    
    def __init__(self, resource_mappings: Dict):
        self.resource_mappings = resource_mappings
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @abstractmethod
    def detect(self, file_path: str, content: str) -> Dict[str, ResourceScore]:
        """Detect resource types and return scores."""
        pass
    
    def _initialize_scores(self) -> Dict[str, ResourceScore]:
        """Initialize score tracking for all resource types."""
        return {rt: ResourceScore(rt) for rt in self.resource_mappings.keys()}

class BicepResourceDetector(ResourceTypeDetector):
    """Detector specifically for Bicep files."""
    
    def __init__(self, resource_mappings: Dict):
        super().__init__(resource_mappings)
        self.arm_declaration_pattern = re.compile(r"resource\s+\w+\s+'([^']+)'")
    
    def detect(self, file_path: str, content: str) -> Dict[str, ResourceScore]:
        """Detect resource types in Bicep files."""
        scores = self._initialize_scores()
        
        # Extract ARM resource type declarations
        declarations = self.arm_declaration_pattern.findall(content)
        self.logger.debug(f"Found {len(declarations)} ARM resource declarations in {file_path}")
        
        # Track resource providers for penalty calculation
        found_providers = set()
        
        for declaration in declarations:
            resource_type_only = declaration.split('@')[0] if '@' in declaration else declaration
            provider = resource_type_only.split('/')[0].lower()
            found_providers.add(provider)
            
            self._score_arm_declaration(resource_type_only, scores)
        
        # Apply penalties for mismatched providers
        if found_providers:
            self._apply_provider_penalties(found_providers, scores)
        
        # Also check for Bicep-specific type patterns
        self._score_bicep_patterns(content, scores)
        
        return scores
    
    def _score_arm_declaration(self, arm_type: str, scores: Dict[str, ResourceScore]):
        """Score based on ARM type declaration."""
        arm_type_lower = arm_type.lower()
        parts = arm_type_lower.split('/')
        provider = parts[0]
        resource_path = '/'.join(parts[1:]) if len(parts) > 1 else ''
        
        for rt, mappings in self.resource_mappings.items():
            for mapped_arm_type in mappings.get('arm_types', []):
                mapped_lower = mapped_arm_type.lower()
                
                # Exact match
                if arm_type_lower == mapped_lower:
                    scores[rt].add_match(arm_type, MatchType.EXACT)
                    self.logger.debug(f"Exact ARM match for {rt}: {arm_type}")
                    continue
                
                # Provider and partial resource match
                mapped_parts = mapped_lower.split('/')
                if len(mapped_parts) > 1:
                    mapped_provider = mapped_parts[0]
                    mapped_resource = '/'.join(mapped_parts[1:])
                    
                    if provider == mapped_provider:
                        if resource_path and mapped_resource and resource_path.startswith(mapped_resource):
                            scores[rt].add_match(arm_type, MatchType.PROVIDER_AND_RESOURCE)
                        else:
                            scores[rt].add_match(arm_type, MatchType.PROVIDER_ONLY)
    
    def _score_bicep_patterns(self, content: str, scores: Dict[str, ResourceScore]):
        """Score based on Bicep-specific patterns."""
        content_lower = content.lower()
        
        for rt, mappings in self.resource_mappings.items():
            for bicep_type in mappings.get('bicep_types', []):
                if bicep_type.lower() in content_lower:
                    scores[rt].add_match(bicep_type, MatchType.BICEP_TYPE)
    
    def _apply_provider_penalties(self, found_providers: Set[str], scores: Dict[str, ResourceScore]):
        """Apply penalties for resource types with mismatched providers."""
        for rt, mappings in self.resource_mappings.items():
            rt_providers = set()
            for arm_type in mappings.get('arm_types', []):
                provider = arm_type.split('/')[0].lower()
                rt_providers.add(provider)
            
            # If none of the resource type's providers match found providers, apply penalty
            if rt_providers and not rt_providers.intersection(found_providers):
                scores[rt].score = max(0, scores[rt].score - 5)

class TerraformResourceDetector(ResourceTypeDetector):
    """Detector for Terraform files."""
    
    def detect(self, file_path: str, content: str) -> Dict[str, ResourceScore]:
        """Detect resource types in Terraform files."""
        scores = self._initialize_scores()
        content_lower = content.lower()
        
        for rt, mappings in self.resource_mappings.items():
            for tf_type in mappings.get('terraform_types', []):
                if tf_type.lower() in content_lower:
                    scores[rt].add_match(tf_type, MatchType.TERRAFORM_TYPE)
                    self.logger.debug(f"Found Terraform type {tf_type} for {rt}")
        
        return scores

class KeywordResourceDetector(ResourceTypeDetector):
    """Detector based on keywords - works for any file type."""
    
    def detect(self, file_path: str, content: str) -> Dict[str, ResourceScore]:
        """Detect resource types based on keywords."""
        scores = self._initialize_scores()
        content_lower = content.lower()
        
        for rt, mappings in self.resource_mappings.items():
            for keyword in mappings.get('keywords', []):
                if keyword.lower() in content_lower:
                    scores[rt].add_match(keyword, MatchType.KEYWORD)
        
        return scores

class CompositeResourceDetector(ResourceTypeDetector):
    """Combines multiple detection strategies."""
    
    def __init__(self, resource_mappings: Dict, detectors: List[ResourceTypeDetector]):
        super().__init__(resource_mappings)
        self.detectors = detectors
    
    def detect(self, file_path: str, content: str) -> Dict[str, ResourceScore]:
        """Combine results from multiple detectors."""
        combined_scores = self._initialize_scores()
        
        for detector in self.detectors:
            detector_scores = detector.detect(file_path, content)
            
            # Merge scores
            for rt, score_data in detector_scores.items():
                combined_scores[rt].score += score_data.score
                combined_scores[rt].matches.extend(score_data.matches)
        
        return combined_scores

class ResourceTypeDetectorFactory:
    """Factory to create appropriate detector based on file type."""
    
    @staticmethod
    def create_detector(file_path: str, resource_mappings: Dict) -> ResourceTypeDetector:
        """Create a detector based on file extension."""
        file_extension = os.path.splitext(file_path)[1].lower()
        
        # Always include keyword detector as a base
        keyword_detector = KeywordResourceDetector(resource_mappings)
        
        # Add file-specific detector
        specific_detectors = {
            '.bicep': BicepResourceDetector,
            '.tf': TerraformResourceDetector,
        }
        
        if file_extension in specific_detectors:
            specific_detector = specific_detectors[file_extension](resource_mappings)
            return CompositeResourceDetector(resource_mappings, [specific_detector, keyword_detector])
        
        # For unknown file types, use keyword detector only
        return keyword_detector

class CachedResourceTypeAnalyzer:
    """Main analyzer with caching and improved architecture."""
    
    def __init__(self, resource_mappings: Dict):
        self.resource_mappings = resource_mappings
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @lru_cache(maxsize=1000)
    def _analyze_cached(self, file_hash: str, file_path: str) -> ResourceDetectionResult:
        """Cached analysis based on file content hash."""
        # This would contain the actual analysis logic
        # For now, returning a placeholder
        return ResourceDetectionResult(
            detected_type="Generic",
            confidence=0.0,
            all_scores={},
            reasoning=[]
        )
    
    def analyze(self, file_path: str, content: str) -> ResourceDetectionResult:
        """Analyze file and determine resource type."""
        # Create content hash for caching
        content_hash = hashlib.md5(content.encode()).hexdigest()
        
        # Check cache first
        cached_result = self._analyze_cached(content_hash, file_path)
        if cached_result.detected_type != "Generic" or cached_result.confidence > 0:
            self.logger.info(f"Using cached result for {file_path}")
            return cached_result
        
        # Create detector and perform analysis
        detector = ResourceTypeDetectorFactory.create_detector(file_path, self.resource_mappings)
        scores = detector.detect(file_path, content)
        
        # Convert scores to final result
        return self._scores_to_result(scores, file_path)
    
    def _scores_to_result(self, scores: Dict[str, ResourceScore], file_path: str) -> ResourceDetectionResult:
        """Convert scores to final detection result."""
        # Find highest scoring resource type
        max_score = 0.0
        detected_type = "Generic"
        all_scores = {}
        reasoning = []
        
        for rt, score_data in scores.items():
            all_scores[rt] = score_data.score
            if score_data.score > max_score:
                max_score = score_data.score
                detected_type = rt
        
        # Handle ties
        tied_types = [rt for rt, score in all_scores.items() if score == max_score and score > 0]
        if len(tied_types) > 1:
            # Try to break tie using filename
            file_name_lower = os.path.basename(file_path).lower()
            for rt in tied_types:
                if rt.lower() in file_name_lower:
                    detected_type = rt
                    reasoning.append(f"Tie broken by filename match: {rt}")
                    break
            else:
                # If still tied, prefer more specific types over generic ones
                if "Generic" in tied_types:
                    tied_types.remove("Generic")
                if tied_types:
                    detected_type = tied_types[0]
                    reasoning.append(f"Tie between {tied_types}, selected {detected_type}")
        
        # Calculate confidence (0-1 scale)
        confidence = min(1.0, max_score / 20.0)  # Normalize to 0-1 range
        
        # Add match details to reasoning
        if detected_type != "Generic" and detected_type in scores:
            for match_text, match_type in scores[detected_type].matches[:5]:  # Top 5 matches
                reasoning.append(f"{match_type.name}: {match_text}")
        
        return ResourceDetectionResult(
            detected_type=detected_type,
            confidence=confidence,
            all_scores=all_scores,
            reasoning=reasoning
        )

# Example usage
if __name__ == "__main__":
    # Example resource mappings (simplified)
    example_mappings = {
        "Storage": {
            "arm_types": ["Microsoft.Storage/storageAccounts"],
            "terraform_types": ["azurerm_storage_account"],
            "bicep_types": ["storageAccount"],
            "keywords": ["storage", "blob"]
        },
        "KeyVault": {
            "arm_types": ["Microsoft.KeyVault/vaults"],
            "terraform_types": ["azurerm_key_vault"],
            "bicep_types": ["keyVault"],
            "keywords": ["key vault", "secrets"]
        }
    }
    
    # Example Bicep content
    bicep_content = """
    resource stg 'Microsoft.Storage/storageAccounts@2021-06-01' = {
      name: 'mystorageaccount'
      location: 'eastus'
    }
    """
    
    # Create analyzer and detect resource type
    analyzer = CachedResourceTypeAnalyzer(example_mappings)
    result = analyzer.analyze("example.bicep", bicep_content)
    
    print(f"Detected Type: {result.detected_type}")
    print(f"Confidence: {result.confidence:.2f}")
    print(f"Reasoning: {result.reasoning}")
