Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
CRITICAL,DP-1,IngestionStorageAccount.Template.json,32,"Encryption at rest is not explicitly enabled for the Microsoft.Storage/storageAccounts resource. The template does not specify 'encryption' properties, which is required by ASB DP-1.","Add the 'encryption' property to the storage account resource definition to ensure encryption at rest is enabled. Example: 'encryption': { 'services': { 'blob': { 'enabled': true } }, 'keySource': 'Microsoft.Storage' }.",N/A,AI,Generic
CRITICAL,DP-1,IngestionStorageAccount.Template.json,54,"Encryption at rest is not explicitly enabled for the Microsoft.Storage/storageAccounts resource. The template does not specify 'encryption' properties, which is required by ASB DP-1.","Add the 'encryption' property to the storage account resource definition to ensure encryption at rest is enabled. Example: 'encryption': { 'services': { 'blob': { 'enabled': true } }, 'keySource': 'Microsoft.Storage' }.",N/A,AI,Generic
CRITICAL,NS-1,IngestionStorageAccount.Template.json,32,"Network security controls (such as network rules, NSGs, or Azure Firewall) are not configured for the Microsoft.Storage/storageAccounts resource. The template does not restrict network access, violating ASB NS-1.","Configure 'networkAcls' on the storage account to restrict access to trusted networks and subnets. Example: 'networkAcls': { 'defaultAction': 'Deny', 'bypass': 'AzureServices', 'ipRules': [], 'virtualNetworkRules': [] }.",N/A,AI,Generic
CRITICAL,NS-1,IngestionStorageAccount.Template.json,54,"Network security controls (such as network rules, NSGs, or Azure Firewall) are not configured for the Microsoft.Storage/storageAccounts resource. The template does not restrict network access, violating ASB NS-1.","Configure 'networkAcls' on the storage account to restrict access to trusted networks and subnets. Example: 'networkAcls': { 'defaultAction': 'Deny', 'bypass': 'AzureServices', 'ipRules': [], 'virtualNetworkRules': [] }.",N/A,AI,Generic
CRITICAL,NS-2,IngestionStorageAccount.Template.json,32,"Public endpoint protection is not configured for the Microsoft.Storage/storageAccounts resource. The template does not specify 'networkAcls' to restrict public access, violating ASB NS-2.",Set 'networkAcls.defaultAction' to 'Deny' and allow only required IPs or VNets to minimize public exposure.,N/A,AI,Generic
CRITICAL,NS-2,IngestionStorageAccount.Template.json,54,"Public endpoint protection is not configured for the Microsoft.Storage/storageAccounts resource. The template does not specify 'networkAcls' to restrict public access, violating ASB NS-2.",Set 'networkAcls.defaultAction' to 'Deny' and allow only required IPs or VNets to minimize public exposure.,N/A,AI,Generic
CRITICAL,NS-3,IngestionStorageAccount.Template.json,32,"No Network Security Groups (NSGs) are associated with the Microsoft.Storage/storageAccounts resource. The template does not implement NSGs to control traffic, violating ASB NS-3.","Associate the storage account with a subnet protected by an NSG, or configure network rules to restrict access.",N/A,AI,Generic
CRITICAL,NS-3,IngestionStorageAccount.Template.json,54,"No Network Security Groups (NSGs) are associated with the Microsoft.Storage/storageAccounts resource. The template does not implement NSGs to control traffic, violating ASB NS-3.","Associate the storage account with a subnet protected by an NSG, or configure network rules to restrict access.",N/A,AI,Generic
CRITICAL,NS-1,LacpBilling.Template.json,3932,"Microsoft.Storage/storageAccounts resource at line 3932 does not specify network rules (such as networkAcls, private endpoints, or service endpoints) to restrict access. According to ASB NS-1, storage accounts must be protected using network security groups or Azure Firewall.",Add the 'networkAcls' property to the storage account resource to restrict access to selected networks and subnets. Consider integrating with private endpoints or service endpoints to limit exposure.,N/A,AI,Generic
CRITICAL,NS-1,LacpBillingExhaust.Template.json,27,Microsoft.Kusto/clusters resource at line 27 does not specify any network security group (NSG) or Azure Firewall configuration to restrict access. This exposes the cluster to potential unauthorized network access.,Configure the Microsoft.Kusto cluster to use private endpoints and associate it with a network security group (NSG) or protect it behind Azure Firewall. Update the resource to include 'virtualNetworkConfiguration' with appropriate subnet and NSG references.,N/A,AI,Generic
CRITICAL,NS-2,LacpBillingExhaust.Template.json,27,"Microsoft.Kusto/clusters resource at line 27 does not restrict public endpoints. By default, the cluster may be accessible from the public internet, increasing exposure risk.",Disable public network access for the Microsoft.Kusto cluster by setting 'publicNetworkAccess' to 'Disabled' and use private endpoints for all access.,N/A,AI,Generic
CRITICAL,NS-3,LacpBillingExhaust.Template.json,27,Microsoft.Kusto/clusters resource at line 27 does not implement Network Security Groups (NSGs) to control inbound and outbound traffic.,Associate the Microsoft.Kusto cluster's subnet with a Network Security Group (NSG) that restricts traffic to only required sources and destinations.,N/A,AI,Generic
CRITICAL,DP-1,LacpBillingExhaustExport.Template.json,61,The resource 'Microsoft.UsageBilling/accounts/dataExports' at line 61 does not specify encryption at rest for exported data. Azure Security Benchmark DP-1 requires all data storage to be encrypted at rest.,Enable encryption at rest for the dataExports resource by specifying encryption settings in the resource properties or ensuring the underlying storage destination is encrypted.,N/A,AI,Generic
CRITICAL,DP-2,LacpBillingExhaustExport.Template.json,61,The resource 'Microsoft.UsageBilling/accounts/dataExports' at line 61 does not explicitly enforce encryption in transit (TLS 1.2+) for data exports. Azure Security Benchmark DP-2 requires all data transfers to use TLS 1.2 or higher.,"Configure the data export process to enforce TLS 1.2+ for all data transfers, and document this enforcement in the resource properties or linked service configuration.",N/A,AI,Generic
CRITICAL,DP-3,LacpBillingExhaustExport.Template.json,61,"Sensitive connection information such as 'adxExhaustUri' and 'adxExhaustDataIngestionUri' are passed as plain string parameters at line 61, rather than being securely referenced from Azure Key Vault. Azure Security Benchmark DP-3 requires sensitive data like keys and connection strings to be stored in Azure Key Vault.",Store sensitive parameters such as 'adxExhaustUri' and 'adxExhaustDataIngestionUri' in Azure Key Vault and reference them securely in the template using Key Vault references.,N/A,AI,Generic
CRITICAL,NS-1,LacpGeo.Template.json,109,"CosmosDB account 'publicNetworkAccess' is set to 'Enabled' and 'isVirtualNetworkFilterEnabled' is false, exposing the resource to the public internet without network security groups or Azure Firewall protection.","Set 'publicNetworkAccess' to 'Disabled' and enable 'isVirtualNetworkFilterEnabled' with appropriate virtual network rules to restrict access to trusted networks only, or protect the resource using NSGs or Azure Firewall as per ASB NS-1.",N/A,AI,Generic
CRITICAL,NS-2,LacpGeo.Template.json,109,"CosmosDB account allows public network access ('publicNetworkAccess': 'Enabled'), creating a public endpoint that is not secured.","Set 'publicNetworkAccess' to 'Disabled' to remove the public endpoint, or implement private endpoints to restrict access as per ASB NS-2.",N/A,AI,Generic
CRITICAL,NS-20,LacpGeo.Template.json,109,CosmosDB account does not implement Network Security Groups (NSGs); 'isVirtualNetworkFilterEnabled' is false and 'virtualNetworkRules' is empty.,Enable 'isVirtualNetworkFilterEnabled' and define 'virtualNetworkRules' to restrict access to the CosmosDB account from only trusted subnets as per ASB NS-3.,N/A,AI,Generic
CRITICAL,NS-1,LacpGlobal.Template.json,54,"CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' at line 54 does not use network security groups (NSGs) or Azure Firewall. 'publicNetworkAccess' is set to 'Enabled' and 'isVirtualNetworkFilterEnabled' is false, exposing the resource to the public internet.",Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to true. Configure virtual network rules to restrict access to trusted networks only.,N/A,AI,Generic
CRITICAL,NS-2,LacpGlobal.Template.json,54,"CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' at line 54 has 'publicNetworkAccess' set to 'Enabled', exposing a public endpoint.",Set 'publicNetworkAccess' to 'Disabled' to remove the public endpoint and use private endpoints for secure access.,N/A,AI,Generic
CRITICAL,NS-3,LacpGlobal.Template.json,54,CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' at line 54 does not implement Network Security Groups (NSGs) to control inbound and outbound traffic. 'isVirtualNetworkFilterEnabled' is false and 'virtualNetworkRules' is empty.,Enable 'isVirtualNetworkFilterEnabled' and define 'virtualNetworkRules' to restrict access to approved subnets.,N/A,AI,Generic
CRITICAL,NS-1,LacpGlobal.Template.json,151,"Storage Account resource 'Microsoft.Storage/storageAccounts' at line 151 does not use network security groups (NSGs) or Azure Firewall. No network rules are defined, potentially exposing the storage account to the public internet.",Configure network rules to restrict access to trusted networks only. Use NSGs or Azure Firewall to protect the storage account.,N/A,AI,Generic
CRITICAL,NS-2,LacpGlobal.Template.json,151,Storage Account resource 'Microsoft.Storage/storageAccounts' at line 151 does not restrict public network access. No network rules or private endpoints are configured.,Restrict public network access by configuring network rules and using private endpoints for the storage account.,N/A,AI,Generic
CRITICAL,NS-3,LacpGlobal.Template.json,151,Storage Account resource 'Microsoft.Storage/storageAccounts' at line 151 does not implement Network Security Groups (NSGs) to control traffic. No network rules are defined.,Define network rules and associate the storage account with NSGs to control inbound and outbound traffic.,N/A,AI,Generic
CRITICAL,NS-1,LacpGlobal.Template.json,180,"Key Vault resource 'Microsoft.KeyVault/vaults' at line 180 does not use network security groups (NSGs) or Azure Firewall. No network ACLs or private endpoints are defined, potentially exposing the Key Vault to the public internet.",Configure network ACLs to restrict access to trusted networks and use NSGs or Azure Firewall to protect the Key Vault.,N/A,AI,Generic
CRITICAL,NS-2,LacpGlobal.Template.json,180,Key Vault resource 'Microsoft.KeyVault/vaults' at line 180 does not restrict public network access. No network ACLs or private endpoints are configured.,Restrict public network access by configuring network ACLs and using private endpoints for the Key Vault.,N/A,AI,Generic
CRITICAL,NS-3,LacpGlobal.Template.json,180,Key Vault resource 'Microsoft.KeyVault/vaults' at line 180 does not implement Network Security Groups (NSGs) to control traffic. No network ACLs are defined.,Define network ACLs and associate the Key Vault with NSGs to control inbound and outbound traffic.,N/A,AI,Generic
CRITICAL,NS-1,LacpRegion.Template.json,1007,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1007 has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false, exposing the database to the public internet without network security group or firewall protection.","Set 'publicNetworkAccess' to 'Disabled' and enable 'isVirtualNetworkFilterEnabled' with appropriate 'virtualNetworkRules' to restrict access to trusted networks only, or use Azure Firewall/NSG to protect the resource.",N/A,AI,Generic
CRITICAL,NS-2,LacpRegion.Template.json,1007,CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1007 exposes a public endpoint by setting 'publicNetworkAccess' to 'Enabled' and not enabling virtual network filtering.,Disable public network access by setting 'publicNetworkAccess' to 'Disabled' and configure private endpoints or virtual network rules to restrict access.,N/A,AI,Generic
CRITICAL,DP-3,LacpStamp.Parameters-LacpStampResources.json,65,"The parameter 'dasStorageAccountKey' at line 65 contains a storage account key value reference in plain text, which is sensitive information. Storing sensitive data such as storage account keys directly in parameters violates ASB DP-3: Manage sensitive information disclosure.",Store the storage account key in Azure Key Vault and reference it securely using a Key Vault reference in the template parameter. Remove any plain text or direct value assignment of sensitive keys from the template.,N/A,AI,Generic
CRITICAL,NS-1,ReadAdxExhaust.Template.json,23,Microsoft.Kusto/clusters resource does not specify any network security groups (NSGs) or Azure Firewall configuration to protect the cluster. Absence of explicit network security controls may expose the resource to unauthorized network access.,Configure the Microsoft.Kusto/clusters resource to use private endpoints or associate it with a virtual network protected by NSGs or Azure Firewall. Update the template to include 'virtualNetworkConfiguration' with appropriate subnet and ensure NSGs are applied to restrict access.,N/A,AI,Generic
CRITICAL,NS-2,ReadAdxExhaust.Template.json,23,"Microsoft.Kusto/clusters resource does not restrict public network access. By default, the cluster may be accessible from the public internet, increasing exposure risk.",Restrict public network access by configuring 'publicNetworkAccess' property to 'Disabled' and use private endpoints. Update the template to include 'publicNetworkAccess': 'Disabled' in the cluster properties.,N/A,AI,Generic
CRITICAL,NS-3,ReadAdxExhaust.Template.json,23,No Network Security Groups (NSGs) are defined or referenced for the Microsoft.Kusto/clusters resource. Lack of NSGs may allow unrestricted inbound and outbound traffic.,Define and associate NSGs with the subnet hosting the Microsoft.Kusto/clusters resource. Update the template to deploy NSG resources and link them to the relevant subnet.,N/A,AI,Generic
CRITICAL,NS-10,TrafficManagerEndpoints.Template.json,38,"The resource 'Microsoft.Network/trafficManagerProfiles/externalEndpoints' at line 38 defines a public endpoint without explicit security controls to minimize exposure, violating ASB Control NS-10 (Protect public endpoints).","Restrict public access to the Traffic Manager external endpoint by implementing IP whitelisting, authentication, or moving to private endpoints where possible. Review and secure the 'target' property to ensure only authorized traffic is allowed.",N/A,AI,Generic
HIGH,NS-5,LacpGeo.Template.json,109,CosmosDB account does not use private endpoints; 'publicNetworkAccess' is 'Enabled' and no private endpoint configuration is present.,"Configure a private endpoint for the CosmosDB account and set 'publicNetworkAccess' to 'Disabled' to ensure secure, private connectivity as per ASB NS-5.",N/A,AI,Generic
HIGH,NS-5,LacpGlobal.Template.json,54,CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' at line 54 does not use private endpoints for secure access.,Configure a private endpoint for the CosmosDB account to restrict access to resources within your virtual network.,N/A,AI,Generic
HIGH,NS-5,LacpGlobal.Template.json,151,Storage Account resource 'Microsoft.Storage/storageAccounts' at line 151 does not use private endpoints for secure access.,Configure a private endpoint for the storage account to restrict access to resources within your virtual network.,N/A,AI,Generic
HIGH,NS-5,LacpGlobal.Template.json,180,Key Vault resource 'Microsoft.KeyVault/vaults' at line 180 does not use private endpoints for secure access.,Configure a private endpoint for the Key Vault to restrict access to resources within your virtual network.,N/A,AI,Generic
HIGH,NS-22,LacpRegion.Template.json,1007,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1007 does not use private endpoints, as 'publicNetworkAccess' is 'Enabled' and 'isVirtualNetworkFilterEnabled' is false.","Implement Azure Private Endpoints for the CosmosDB account to ensure secure, private connectivity from your virtual network.",N/A,AI,Generic
HIGH,NS-23,LacpStamp.Template.json,1002,"Microsoft.Storage/storageAccounts resources do not have private endpoints configured. No 'privateEndpointConnections' or 'privateEndpoints' are defined, exposing storage accounts to potential public access.",Add 'Microsoft.Network/privateEndpoints' resources for each storage account and configure the storage account to only allow traffic from private endpoints.,N/A,AI,Generic
HIGH,NS-23,LacpStamp.Template.json,1082,"Microsoft.Storage/storageAccounts resources (shimStorageAccounts) do not have private endpoints configured. No 'privateEndpointConnections' or 'privateEndpoints' are defined, exposing storage accounts to potential public access.",Add 'Microsoft.Network/privateEndpoints' resources for each shim storage account and configure the storage account to only allow traffic from private endpoints.,N/A,AI,Generic
HIGH,NS-23,LacpStamp.Template.json,964,"Microsoft.Storage/storageAccounts resource 'tipLACPStampStorageAccountName' does not have a private endpoint configured. No 'privateEndpointConnections' or 'privateEndpoints' are defined, exposing the storage account to potential public access.",Add a 'Microsoft.Network/privateEndpoints' resource for this storage account and configure the storage account to only allow traffic from private endpoints.,N/A,AI,Generic
HIGH,NS-23,LacpStamp.Template.json,1242,"Microsoft.KeyVault/vaults resource 'stampKeyVaultName' does not have a private endpoint configured. No 'privateEndpointConnections' or 'privateEndpoints' are defined, exposing the Key Vault to potential public access.",Add a 'Microsoft.Network/privateEndpoints' resource for this Key Vault and configure the Key Vault to only allow traffic from private endpoints.,N/A,AI,Generic
HIGH,NS-23,LacpStamp.Template.json,1322,"Microsoft.KeyVault/vaults resource 'stampSharedKeyVaultName' (conditional) does not have a private endpoint configured. No 'privateEndpointConnections' or 'privateEndpoints' are defined, exposing the Key Vault to potential public access.",Add a 'Microsoft.Network/privateEndpoints' resource for this Key Vault and configure the Key Vault to only allow traffic from private endpoints.,N/A,AI,Generic
HIGH,AM-1,RoleAssignment.Template.json,49,Role assignment at line 49 grants 'Contributor' role to the 'Ev2BuildoutServicePrincipalId' principal. The Contributor role provides broad permissions and may exceed least privilege requirements.,"Review the permissions required by the 'Ev2BuildoutServicePrincipalId' principal and assign a more restrictive, custom role with only the necessary permissions as per the principle of least privilege.",N/A,AI,Generic
