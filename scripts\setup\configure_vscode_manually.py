#!/usr/bin/env python3
"""
Manual VS Code configuration for MCP integration.
Use this if the automatic setup doesn't work.
"""

import json
import os
from pathlib import Path

def print_configuration():
    """Print the configuration that needs to be added to VS Code settings."""
    
    current_dir = Path.cwd().as_posix()
    
    config = {
        "github.copilot.chat.experimental.mcp.enabled": True,
        "github.copilot.chat.experimental.mcp.servers": {
            "iac-guardian": {
                "command": "python",
                "args": ["-u", "mcp_server.py"],
                "cwd": current_dir,
                "env": {
                    "ENFORCE_DOMAIN_PRIORITY": "true",
                    "USE_OPTIMIZED_PROMPTS": "true",
                    "ANALYSIS_SEED": "42",
                    "PYTHONPATH": current_dir
                }
            }
        }
    }
    
    print("🔧 MANUAL VS CODE CONFIGURATION")
    print("=" * 50)
    print()
    print("1. Open VS Code")
    print("2. Press Ctrl+Shift+P (or Cmd+Shift+P on Mac)")
    print("3. Type: 'Preferences: Open Settings (JSON)'")
    print("4. Add this configuration to your settings.json:")
    print()
    print("```json")
    print(json.dumps(config, indent=2))
    print("```")
    print()
    print("5. Save the file")
    print("6. Restart VS Code")
    print("7. Test with: @iac-guardian analyze_iac_file file_path=\"./your-file.json\"")
    print()
    print("📁 Working Directory:", current_dir)
    print("🐍 Python Command: python")
    print("📄 MCP Server: mcp_server.py")
    print()
    print("🔍 Alternative Configuration (if above doesn't work):")
    print()
    
    # Alternative with full paths
    python_exe = None
    try:
        import sys
        python_exe = sys.executable
    except:
        python_exe = "python"
    
    alt_config = {
        "github.copilot.chat.experimental.mcp.enabled": True,
        "github.copilot.chat.experimental.mcp.servers": {
            "iac-guardian": {
                "command": python_exe,
                "args": ["-u", str(Path(current_dir) / "mcp_server.py")],
                "env": {
                    "ENFORCE_DOMAIN_PRIORITY": "true",
                    "USE_OPTIMIZED_PROMPTS": "true",
                    "ANALYSIS_SEED": "42"
                }
            }
        }
    }
    
    print("```json")
    print(json.dumps(alt_config, indent=2))
    print("```")
    print()
    print("🚀 After configuration, use these commands in VS Code Copilot Chat:")
    print()
    print("   @iac-guardian analyze_iac_file file_path=\"./template.json\"")
    print("   @iac-guardian analyze_iac_folder folder_path=\"./infrastructure\"")
    print("   @iac-guardian get_security_controls resource_type=\"Storage\"")
    print("   @iac-guardian validate_security_config resource_type=\"Microsoft.Storage/storageAccounts\" resource_config='{...}'")
    print()

def test_server():
    """Test if the MCP server can start."""
    print("🧪 TESTING MCP SERVER")
    print("=" * 30)
    
    try:
        import mcp_server
        print("✅ MCP server module imports successfully")
        
        # Test if we can create the server
        from mcp_server import server
        print("✅ MCP server object created successfully")
        
        # Test security reviewer
        from security_opt import SecurityPRReviewer
        reviewer = SecurityPRReviewer(local_folder=".")
        print("✅ SecurityPRReviewer created successfully")
        
        print()
        print("🎉 MCP server is ready!")
        print("The issue is likely with VS Code configuration, not the server.")
        
    except Exception as e:
        print(f"❌ Error testing MCP server: {e}")
        print("Please check dependencies and file paths.")

def main():
    """Main function."""
    print("🔧 IaC Guardian MCP Server - Manual Configuration")
    print("=" * 60)
    print()
    
    # Test the server first
    test_server()
    print()
    
    # Print configuration instructions
    print_configuration()
    
    print("💡 TROUBLESHOOTING TIPS:")
    print("- Ensure GitHub Copilot extension is updated")
    print("- MCP support is experimental - may need latest VS Code")
    print("- Check VS Code Developer Tools (Help → Toggle Developer Tools) for errors")
    print("- Try restarting VS Code after configuration")
    print()
    print("📚 For more help, see: MCP_TROUBLESHOOTING.md")

if __name__ == "__main__":
    main()
