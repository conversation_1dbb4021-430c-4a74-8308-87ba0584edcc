"""
Template Performance Dashboard for IaC Guardian

This module provides a web-based dashboard for monitoring template performance,
viewing metrics, and analyzing usage patterns.
"""

import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class TemplateDashboard:
    """Web-based dashboard for template performance monitoring"""
    
    def __init__(self, metrics_collector, config: Dict[str, Any]):
        """
        Initialize template dashboard
        
        Args:
            metrics_collector: TemplateMetricsCollector instance
            config: Dashboard configuration
        """
        self.metrics_collector = metrics_collector
        self.config = config
        self.enabled = config.get('enabled', True)
        self.dashboard_port = config.get('port', 8080)
        self.dashboard_host = config.get('host', 'localhost')
    
    def generate_dashboard_data(self) -> Dict[str, Any]:
        """Generate comprehensive dashboard data"""
        if not self.enabled or not self.metrics_collector:
            return {'error': 'Dashboard or metrics collector not available'}
        
        # Get overall statistics
        overall_stats = self.metrics_collector.get_stats()
        
        # Get statistics for different time windows
        hourly_stats = self.metrics_collector.get_stats(time_window_hours=1)
        daily_stats = self.metrics_collector.get_stats(time_window_hours=24)
        weekly_stats = self.metrics_collector.get_stats(time_window_hours=168)
        
        # Get slow operations
        slow_operations = self.metrics_collector.get_slow_operations(limit=10)
        
        # Get error summary
        error_summary = self.metrics_collector.get_error_summary(time_window_hours=24)
        
        # Get cache statistics
        cache_stats = self.metrics_collector.get_cache_stats()
        
        return {
            'timestamp': datetime.now().isoformat(),
            'overall_stats': {
                'total_operations': overall_stats.total_operations,
                'avg_duration_ms': round(overall_stats.avg_duration_ms, 2),
                'success_rate': round(overall_stats.success_rate, 2),
                'cache_hit_rate': round(overall_stats.cache_hit_rate, 2)
            },
            'time_window_stats': {
                'last_hour': {
                    'operations': hourly_stats.total_operations,
                    'avg_duration': round(hourly_stats.avg_duration_ms, 2),
                    'ops_per_second': round(hourly_stats.operations_per_second, 2)
                },
                'last_24_hours': {
                    'operations': daily_stats.total_operations,
                    'avg_duration': round(daily_stats.avg_duration_ms, 2),
                    'ops_per_second': round(daily_stats.operations_per_second, 2)
                },
                'last_week': {
                    'operations': weekly_stats.total_operations,
                    'avg_duration': round(weekly_stats.avg_duration_ms, 2),
                    'ops_per_second': round(weekly_stats.operations_per_second, 2)
                }
            },
            'slow_operations': [
                {
                    'template_path': op.template_path,
                    'operation': op.operation,
                    'duration_ms': round(op.duration_ms, 2),
                    'timestamp': op.timestamp.isoformat()
                }
                for op in slow_operations
            ],
            'error_summary': error_summary,
            'cache_stats': cache_stats,
            'performance_trends': self._generate_performance_trends()
        }
    
    def _generate_performance_trends(self) -> Dict[str, List[Dict[str, Any]]]:
        """Generate performance trend data for charts"""
        trends = {
            'hourly_performance': [],
            'operation_breakdown': [],
            'template_usage': []
        }
        
        # Generate hourly performance data for the last 24 hours
        now = datetime.now()
        for i in range(24):
            hour_start = now - timedelta(hours=i+1)
            hour_end = now - timedelta(hours=i)
            
            # This is a simplified version - in a real implementation,
            # you'd filter metrics by time window
            stats = self.metrics_collector.get_stats(time_window_hours=1)
            
            trends['hourly_performance'].append({
                'hour': hour_start.strftime('%H:00'),
                'operations': stats.total_operations // 24,  # Simplified
                'avg_duration': round(stats.avg_duration_ms, 2),
                'success_rate': round(stats.success_rate, 2)
            })
        
        # Operation type breakdown
        operation_types = ['load', 'validate', 'render', 'cache_hit', 'cache_miss']
        for op_type in operation_types:
            stats = self.metrics_collector.get_stats(operation=op_type)
            trends['operation_breakdown'].append({
                'operation': op_type,
                'count': stats.total_operations,
                'avg_duration': round(stats.avg_duration_ms, 2)
            })
        
        return trends
    
    def generate_html_dashboard(self) -> str:
        """Generate HTML dashboard"""
        dashboard_data = self.generate_dashboard_data()
        
        html_template = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IaC Guardian Template Performance Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .dashboard {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            color: #333;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 30px;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .table th, .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .table th {
            background: #667eea;
            color: white;
        }
        .error-item {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 10px;
            margin: 5px 0;
        }
        .refresh-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-bottom: 20px;
        }
        .refresh-btn:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>🚀 IaC Guardian Template Performance Dashboard</h1>
            <p>Real-time monitoring of template system performance</p>
            <button class="refresh-btn" onclick="location.reload()">🔄 Refresh Data</button>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{total_operations}</div>
                <div class="stat-label">Total Operations</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{avg_duration}ms</div>
                <div class="stat-label">Average Duration</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{success_rate}%</div>
                <div class="stat-label">Success Rate</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{cache_hit_rate}%</div>
                <div class="stat-label">Cache Hit Rate</div>
            </div>
        </div>
        
        <div class="section">
            <h3>📊 Performance Trends (Last 24 Hours)</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>Time Window</th>
                        <th>Operations</th>
                        <th>Avg Duration (ms)</th>
                        <th>Ops/Second</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Last Hour</td>
                        <td>{hour_ops}</td>
                        <td>{hour_duration}</td>
                        <td>{hour_ops_sec}</td>
                    </tr>
                    <tr>
                        <td>Last 24 Hours</td>
                        <td>{day_ops}</td>
                        <td>{day_duration}</td>
                        <td>{day_ops_sec}</td>
                    </tr>
                    <tr>
                        <td>Last Week</td>
                        <td>{week_ops}</td>
                        <td>{week_duration}</td>
                        <td>{week_ops_sec}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="section">
            <h3>🐌 Slowest Operations</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>Template</th>
                        <th>Operation</th>
                        <th>Duration (ms)</th>
                        <th>Timestamp</th>
                    </tr>
                </thead>
                <tbody>
                    {slow_operations_rows}
                </tbody>
            </table>
        </div>
        
        <div class="section">
            <h3>❌ Recent Errors</h3>
            {error_items}
        </div>
        
        <div class="section">
            <h3>💾 Cache Performance</h3>
            <p><strong>Hit Rate:</strong> {cache_hit_rate}%</p>
            <p><strong>Total Operations:</strong> {cache_total_ops}</p>
            <p><strong>Cache Hits:</strong> {cache_hits}</p>
            <p><strong>Cache Misses:</strong> {cache_misses}</p>
        </div>
        
        <div class="section">
            <h3>ℹ️ System Information</h3>
            <p><strong>Last Updated:</strong> {timestamp}</p>
            <p><strong>Dashboard Status:</strong> ✅ Active</p>
            <p><strong>Monitoring:</strong> ✅ Enabled</p>
        </div>
    </div>
</body>
</html>
        """
        
        # Format slow operations table
        slow_ops_rows = ""
        for op in dashboard_data.get('slow_operations', []):
            slow_ops_rows += f"""
                <tr>
                    <td>{op['template_path']}</td>
                    <td>{op['operation']}</td>
                    <td>{op['duration_ms']}</td>
                    <td>{op['timestamp']}</td>
                </tr>
            """
        
        # Format error items
        error_items = ""
        for error, count in dashboard_data.get('error_summary', {}).items():
            error_items += f'<div class="error-item"><strong>{count}x:</strong> {error}</div>'
        
        if not error_items:
            error_items = '<p>✅ No recent errors</p>'
        
        # Get data safely
        overall = dashboard_data.get('overall_stats', {})
        time_windows = dashboard_data.get('time_window_stats', {})
        cache_stats = dashboard_data.get('cache_stats', {})
        
        return html_template.format(
            total_operations=overall.get('total_operations', 0),
            avg_duration=overall.get('avg_duration_ms', 0),
            success_rate=overall.get('success_rate', 0),
            cache_hit_rate=overall.get('cache_hit_rate', 0),
            hour_ops=time_windows.get('last_hour', {}).get('operations', 0),
            hour_duration=time_windows.get('last_hour', {}).get('avg_duration', 0),
            hour_ops_sec=time_windows.get('last_hour', {}).get('ops_per_second', 0),
            day_ops=time_windows.get('last_24_hours', {}).get('operations', 0),
            day_duration=time_windows.get('last_24_hours', {}).get('avg_duration', 0),
            day_ops_sec=time_windows.get('last_24_hours', {}).get('ops_per_second', 0),
            week_ops=time_windows.get('last_week', {}).get('operations', 0),
            week_duration=time_windows.get('last_week', {}).get('avg_duration', 0),
            week_ops_sec=time_windows.get('last_week', {}).get('ops_per_second', 0),
            slow_operations_rows=slow_ops_rows,
            error_items=error_items,
            cache_total_ops=cache_stats.get('total_operations', 0),
            cache_hits=cache_stats.get('hits', 0),
            cache_misses=cache_stats.get('misses', 0),
            timestamp=dashboard_data.get('timestamp', 'Unknown')
        )
    
    def export_dashboard(self, output_path: Path) -> bool:
        """
        Export dashboard as HTML file
        
        Args:
            output_path: Path to save the dashboard HTML
            
        Returns:
            True if successful, False otherwise
        """
        try:
            html_content = self.generate_html_dashboard()
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"Dashboard exported to {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export dashboard: {e}")
            return False
