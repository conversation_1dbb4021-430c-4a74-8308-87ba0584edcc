#!/usr/bin/env python3
"""
Template System Test Runner for IaC Guardian

This script runs comprehensive tests for the template system and generates
detailed reports on test results and system performance.
"""

import sys
import os
import unittest
import time
import json
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

def run_template_tests():
    """Run all template system tests"""
    print("🚀 IaC Guardian Template System Test Suite")
    print("=" * 60)
    
    # Import test modules
    try:
        from tests.test_template_system import (
            TestTemplateValidator,
            TestTemplateLoader, 
            TestTemplateMetrics,
            TestTemplateVersioning,
            TestTemplateIntegration
        )
        print("✅ Test modules imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import test modules: {e}")
        return False
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Test classes with descriptions
    test_classes = [
        (TestTemplateValidator, "Template Validation System"),
        (TestTemplateLoader, "Template Loading & Caching"),
        (TestTemplateMetrics, "Performance Monitoring"),
        (TestTemplateVersioning, "Version Management"),
        (TestTemplateIntegration, "Complete System Integration")
    ]
    
    print(f"\n📋 Test Categories:")
    for i, (test_class, description) in enumerate(test_classes, 1):
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
        print(f"  {i}. {description} ({tests.countTestCases()} tests)")
    
    print(f"\n🔬 Running {test_suite.countTestCases()} tests...")
    print("-" * 60)
    
    # Run tests with detailed output
    start_time = time.time()
    runner = unittest.TextTestRunner(
        verbosity=2,
        stream=sys.stdout,
        buffer=True
    )
    
    result = runner.run(test_suite)
    end_time = time.time()
    
    # Generate test report
    generate_test_report(result, end_time - start_time)
    
    return result.wasSuccessful()

def generate_test_report(result, duration):
    """Generate comprehensive test report"""
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    skipped = len(result.skipped) if hasattr(result, 'skipped') else 0
    successful = total_tests - failures - errors - skipped
    
    success_rate = (successful / total_tests * 100) if total_tests > 0 else 0
    
    print(f"📈 Overall Statistics:")
    print(f"  • Total Tests: {total_tests}")
    print(f"  • Successful: {successful} ({success_rate:.1f}%)")
    print(f"  • Failed: {failures}")
    print(f"  • Errors: {errors}")
    print(f"  • Skipped: {skipped}")
    print(f"  • Duration: {duration:.2f} seconds")
    
    # Status indicator
    if success_rate == 100:
        status = "✅ ALL TESTS PASSED"
        status_color = "green"
    elif success_rate >= 80:
        status = "⚠️  MOSTLY PASSING"
        status_color = "yellow"
    else:
        status = "❌ TESTS FAILING"
        status_color = "red"
    
    print(f"\n🎯 Status: {status}")
    
    # Detailed failure/error reporting
    if failures:
        print(f"\n❌ FAILURES ({len(failures)}):")
        for i, (test, traceback) in enumerate(failures, 1):
            print(f"  {i}. {test}")
            print(f"     {traceback.split('AssertionError:')[-1].strip()}")
    
    if errors:
        print(f"\n💥 ERRORS ({len(errors)}):")
        for i, (test, traceback) in enumerate(errors, 1):
            print(f"  {i}. {test}")
            error_msg = traceback.split('\n')[-2] if '\n' in traceback else traceback
            print(f"     {error_msg}")
    
    # Generate JSON report
    report_data = {
        "timestamp": datetime.now().isoformat(),
        "summary": {
            "total_tests": total_tests,
            "successful": successful,
            "failures": failures,
            "errors": errors,
            "skipped": skipped,
            "success_rate": success_rate,
            "duration_seconds": duration,
            "status": status
        },
        "failures": [
            {
                "test": str(test),
                "traceback": traceback
            }
            for test, traceback in result.failures
        ],
        "errors": [
            {
                "test": str(test),
                "traceback": traceback
            }
            for test, traceback in result.errors
        ]
    }
    
    # Save report
    reports_dir = Path("test_reports")
    reports_dir.mkdir(exist_ok=True)
    
    report_file = reports_dir / f"template_tests_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    try:
        with open(report_file, 'w') as f:
            json.dump(report_data, f, indent=2)
        print(f"\n📄 Detailed report saved: {report_file}")
    except Exception as e:
        print(f"\n⚠️  Could not save report: {e}")
    
    # Performance recommendations
    if duration > 30:
        print(f"\n⚡ Performance Note: Tests took {duration:.1f}s - consider optimizing slow tests")
    
    print("\n" + "=" * 60)

def run_specific_test_category(category):
    """Run tests for a specific category"""
    categories = {
        'validator': 'TestTemplateValidator',
        'loader': 'TestTemplateLoader',
        'metrics': 'TestTemplateMetrics',
        'versioning': 'TestTemplateVersioning',
        'integration': 'TestTemplateIntegration'
    }
    
    if category not in categories:
        print(f"❌ Unknown category: {category}")
        print(f"Available categories: {', '.join(categories.keys())}")
        return False
    
    print(f"🎯 Running {category} tests only...")
    
    try:
        from tests.test_template_system import *
        test_class = globals()[categories[category]]
        
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        return result.wasSuccessful()
        
    except Exception as e:
        print(f"❌ Failed to run {category} tests: {e}")
        return False

def check_test_environment():
    """Check if test environment is properly set up"""
    print("🔍 Checking test environment...")
    
    checks = []
    
    # Check Python version
    python_version = sys.version_info
    if python_version >= (3, 7):
        checks.append(("✅", f"Python {python_version.major}.{python_version.minor}.{python_version.micro}"))
    else:
        checks.append(("❌", f"Python {python_version.major}.{python_version.minor} (requires 3.7+)"))
    
    # Check required modules
    required_modules = [
        'unittest', 'tempfile', 'json', 'pathlib', 'datetime'
    ]
    
    for module in required_modules:
        try:
            __import__(module)
            checks.append(("✅", f"Module: {module}"))
        except ImportError:
            checks.append(("❌", f"Module: {module} (missing)"))
    
    # Check project structure
    project_paths = [
        project_root / 'src',
        project_root / 'src' / 'utils',
        project_root / 'templates',
        project_root / 'tests'
    ]
    
    for path in project_paths:
        if path.exists():
            checks.append(("✅", f"Directory: {path.name}"))
        else:
            checks.append(("❌", f"Directory: {path.name} (missing)"))
    
    # Print results
    for status, message in checks:
        print(f"  {status} {message}")
    
    # Overall status
    failed_checks = [check for check in checks if check[0] == "❌"]
    if failed_checks:
        print(f"\n⚠️  {len(failed_checks)} environment issues detected")
        return False
    else:
        print(f"\n✅ Environment check passed ({len(checks)} checks)")
        return True

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='Run IaC Guardian Template System Tests')
    parser.add_argument('--category', '-c', 
                       choices=['validator', 'loader', 'metrics', 'versioning', 'integration'],
                       help='Run tests for specific category only')
    parser.add_argument('--check-env', action='store_true',
                       help='Check test environment setup')
    parser.add_argument('--quick', action='store_true',
                       help='Run quick tests only (skip integration tests)')
    
    args = parser.parse_args()
    
    if args.check_env:
        success = check_test_environment()
        sys.exit(0 if success else 1)
    
    if args.category:
        success = run_specific_test_category(args.category)
    else:
        success = run_template_tests()
    
    sys.exit(0 if success else 1)
