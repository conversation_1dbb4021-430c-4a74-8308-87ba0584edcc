#!/usr/bin/env python3
"""
Enhanced Resource-Control Mapping System V2 for IaC Guardian
Integrates all control IDs from enhanced CSV files with comprehensive URL links
and adds new features for better security coverage and reporting
"""

import json
import csv
import logging
from pathlib import Path
from typing import Dict, List, Set, Optional, Tuple
import re
from datetime import datetime
from collections import defaultdict

logger = logging.getLogger(__name__)

class EnhancedResourceControlMapperV2:
    """
    Enhanced system to map Azure resources to all relevant control IDs from CSV files
    and extract URL links for comprehensive reporting with additional features.
    """
    
    def __init__(self):
        self.csv_files = {
            "Identity Management": "data/benchmarks/identity_management.csv",
            "Network Security": "data/benchmarks/network_security_with_urls.csv",
            "Data Protection": "data/benchmarks/data_protection.csv"
        }
        
        self.control_database = {}
        self.resource_mappings = {}
        self.url_links_database = {}
        self.control_relationships = {}
        self.severity_mappings = {}
        self.compliance_frameworks = {}
        
        # Load existing Azure resource mappings from the mappings file
        self.azure_resource_mappings = {}
        mappings_path = Path("data/mappings/azure_resource_mappings.json")
        try:
            if mappings_path.exists():
                with open(mappings_path, 'r') as f:
                    self.azure_resource_mappings = json.load(f)
            else:
                logger.warning("Azure resource mappings file not found: data/mappings/azure_resource_mappings.json")
        except Exception as e:
            logger.error(f"Error loading Azure resource mappings: {e}")
        
        # Load and process CSV files
        self._load_csv_controls()
        
        # Generate comprehensive resource-control mappings
        self._generate_comprehensive_mappings()
        
        # Build control relationships
        self._build_control_relationships()
        
        # Map severity levels
        self._map_severity_levels()
        
        # Map compliance frameworks
        self._map_compliance_frameworks()
    
    def _load_csv_controls(self):
        """Load all controls from enhanced CSV files with URL extraction."""
        logger.info("Loading enhanced CSV control data...")
        
        for domain_name, csv_path in self.csv_files.items():
            try:
                csv_file_path = Path(csv_path)
                if not csv_file_path.exists():
                    logger.warning(f"CSV file not found: {csv_path}")
                    continue
                
                with open(csv_file_path, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    domain_controls = []
                    
                    for row in reader:
                        control_id = row.get('ASB ID', '').strip()
                        if not control_id:
                            continue
                        
                        # Extract control information
                        control_data = {
                            'id': control_id,
                            'domain': domain_name,
                            'name': row.get('Name', '').strip(),
                            'security_principle': row.get('Security Principle', '').strip(),
                            'azure_guidance': row.get('Azure Guidance', '').strip(),
                            'implementation_context': row.get('Implementation and additional context', '').strip(),
                            'stakeholders': row.get('Stakeholders', '').strip(),
                            'tags': row.get('Tags', '').strip(),
                            'priority': self._determine_priority(row),
                            'automation_possible': self._check_automation_possibility(row),
                            'monitoring_required': self._check_monitoring_requirement(row)
                        }
                        
                        # Extract URLs from implementation context
                        urls = self._extract_urls_from_text(control_data['implementation_context'])
                        control_data['urls'] = urls
                        
                        # Store in control database
                        self.control_database[control_id] = control_data
                        domain_controls.append(control_data)
                        
                        # Store URL links for reporting
                        if urls:
                            self.url_links_database[control_id] = {
                                'raw_links': urls,
                                'formatted_links': self._format_links_for_display(urls, control_data['implementation_context']),
                                'azure_guidance': control_data['azure_guidance'],
                                'implementation_context': control_data['implementation_context']
                            }
                    
                    logger.info(f"Loaded {len(domain_controls)} controls from {domain_name}")
                    
            except Exception as e:
                logger.error(f"Error loading CSV file {csv_path}: {e}")
    
    def _determine_priority(self, row: Dict) -> str:
        """Determine control priority based on control data."""
        name = row.get('Name', '').lower()
        tags = row.get('Tags', '').lower()
        
        # High priority keywords
        high_priority_keywords = ['privileged', 'admin', 'encryption', 'authentication', 'critical', 'emergency']
        medium_priority_keywords = ['monitoring', 'logging', 'access', 'network', 'configuration']
        
        for keyword in high_priority_keywords:
            if keyword in name or keyword in tags:
                return 'HIGH'
        
        for keyword in medium_priority_keywords:
            if keyword in name or keyword in tags:
                return 'MEDIUM'
        
        return 'LOW'
    
    def _check_automation_possibility(self, row: Dict) -> bool:
        """Check if control can be automated."""
        automation_keywords = ['azure policy', 'automate', 'script', 'template', 'configuration']
        context = row.get('Implementation and additional context', '').lower()
        
        return any(keyword in context for keyword in automation_keywords)
    
    def _check_monitoring_requirement(self, row: Dict) -> bool:
        """Check if control requires monitoring."""
        monitoring_keywords = ['monitor', 'alert', 'log', 'track', 'audit', 'review']
        context = row.get('Implementation and additional context', '').lower()
        
        return any(keyword in context for keyword in monitoring_keywords)
    
    def _extract_urls_from_text(self, text: str) -> List[str]:
        """Extract all URLs from text using comprehensive regex patterns."""
        if not text:
            return []
        
        # Enhanced URL pattern to catch various formats
        url_patterns = [
            r'https?://[^\s\n\r\t,;]+',  # Standard HTTP/HTTPS URLs
            r'www\.[^\s\n\r\t,;]+',      # www URLs without protocol
            r'aka\.ms/[^\s\n\r\t,;]+',   # Microsoft short links
        ]
        
        urls = []
        for pattern in url_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                # Clean up URL (remove trailing punctuation)
                cleaned_url = re.sub(r'[.,;:!?)\]}>"\'\s]+$', '', match)
                if cleaned_url and cleaned_url not in urls:
                    # Add protocol if missing
                    if cleaned_url.startswith('www.'):
                        cleaned_url = 'https://' + cleaned_url
                    elif cleaned_url.startswith('aka.ms'):
                        cleaned_url = 'https://' + cleaned_url
                    urls.append(cleaned_url)
        
        return urls
    
    def _format_links_for_display(self, urls: List[str], context: str) -> str:
        """Format URLs for display in reports with meaningful descriptions."""
        if not urls:
            return ""
        
        formatted_links = []
        for i, url in enumerate(urls, 1):
            description = self._extract_link_description(url, context)
            if description:
                formatted_links.append(f"[{description}]({url})")
            else:
                formatted_links.append(f"[Reference {i}]({url})")
        
        return " | ".join(formatted_links)
    
    def _extract_link_description(self, url: str, context: str) -> str:
        """Extract meaningful description for URL from context."""
        # Try to find text before the URL that describes it
        patterns = [
            rf'([^:\n•]+):\s*[^\n]*{re.escape(url)}',  # "Description: URL"
            rf'([^:\n•]+)\s*\n[^\n]*{re.escape(url)}',  # "Description \n URL"
            rf'•\s*([^:\n]+):\s*[^\n]*{re.escape(url)}',  # "• Description: URL"
        ]
        
        for pattern in patterns:
            match = re.search(pattern, context, re.IGNORECASE)
            if match:
                description = match.group(1).strip()
                # Clean up description
                description = re.sub(r'^[^\w]*', '', description)
                description = re.sub(r'[^\w\s]*$', '', description)
                if 3 < len(description) < 60:
                    return description
        
        # Fallback to URL-based description
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            
            if 'docs.microsoft.com' in parsed.netloc:
                # Extract meaningful part from Microsoft docs URL
                path_parts = parsed.path.strip('/').split('/')
                if len(path_parts) >= 2:
                    service = path_parts[0] if path_parts[0] != 'en-us' else path_parts[1] if len(path_parts) > 1 else 'azure'
                    return f"Microsoft {service.replace('-', ' ').title()} Documentation"
                return "Microsoft Documentation"
            elif 'azure.microsoft.com' in parsed.netloc:
                return "Azure Portal Documentation"
            elif 'github.com' in parsed.netloc:
                return "GitHub Repository"
            elif 'aka.ms' in parsed.netloc:
                return "Microsoft Quick Link"
            else:
                return parsed.netloc.replace('www.', '').replace('.com', '').title()
        except:
            return "Reference"
    
    def _build_control_relationships(self):
        """Build relationships between controls based on shared keywords and domains."""
        logger.info("Building control relationships...")
        
        for control_id, control_data in self.control_database.items():
            related_controls = []
            
            # Find controls with similar tags or principles
            control_tags = set(control_data.get('tags', '').lower().split(','))
            control_principle = control_data.get('security_principle', '').lower()
            
            for other_id, other_data in self.control_database.items():
                if control_id == other_id:
                    continue
                
                other_tags = set(other_data.get('tags', '').lower().split(','))
                other_principle = other_data.get('security_principle', '').lower()
                
                # Check for common tags
                common_tags = control_tags.intersection(other_tags)
                if len(common_tags) > 0:
                    related_controls.append({
                        'control_id': other_id,
                        'relationship_type': 'shared_tags',
                        'common_elements': list(common_tags)
                    })
                
                # Check for similar principles
                if control_principle and other_principle:
                    principle_keywords = ['access', 'encryption', 'monitoring', 'authentication', 'network']
                    for keyword in principle_keywords:
                        if keyword in control_principle and keyword in other_principle:
                            related_controls.append({
                                'control_id': other_id,
                                'relationship_type': 'similar_principle',
                                'common_elements': [keyword]
                            })
                            break
            
            self.control_relationships[control_id] = related_controls
    
    def _map_severity_levels(self):
        """Map controls to severity levels based on their impact."""
        for control_id, control_data in self.control_database.items():
            priority = control_data.get('priority', 'MEDIUM')
            domain = control_data.get('domain', '')
            
            # Determine severity based on priority and domain
            if priority == 'HIGH':
                if domain == 'Identity Management':
                    severity = 'CRITICAL'
                else:
                    severity = 'HIGH'
            elif priority == 'MEDIUM':
                severity = 'MEDIUM'
            else:
                severity = 'LOW'
            
            self.severity_mappings[control_id] = {
                'severity': severity,
                'priority': priority,
                'domain': domain,
                'impact_score': self._calculate_impact_score(control_data)
            }
    
    def _calculate_impact_score(self, control_data: Dict) -> int:
        """Calculate impact score for a control (0-100)."""
        score = 50  # Base score
        
        # Adjust based on priority
        priority = control_data.get('priority', 'MEDIUM')
        if priority == 'HIGH':
            score += 30
        elif priority == 'LOW':
            score -= 20
        
        # Adjust based on automation possibility
        if control_data.get('automation_possible', False):
            score += 10
        
        # Adjust based on monitoring requirement
        if control_data.get('monitoring_required', False):
            score += 10
        
        # Ensure score is within bounds
        return max(0, min(100, score))
    
    def _map_compliance_frameworks(self):
        """Map controls to various compliance frameworks."""
        # This is a simplified mapping - in production, this would be more comprehensive
        framework_mappings = {
            'PCI-DSS': ['DP-', 'NS-', 'IM-'],  # Payment Card Industry
            'HIPAA': ['DP-', 'IM-'],  # Healthcare
            'SOC2': ['IM-', 'NS-', 'DP-'],  # Service Organization Control
            'ISO27001': ['IM-', 'NS-', 'DP-'],  # Information Security
            'GDPR': ['DP-', 'IM-'],  # Data Protection
            'NIST': ['IM-', 'NS-', 'DP-']  # NIST Cybersecurity Framework
        }
        
        for control_id, control_data in self.control_database.items():
            applicable_frameworks = []
            
            for framework, prefixes in framework_mappings.items():
                for prefix in prefixes:
                    if control_id.startswith(prefix):
                        applicable_frameworks.append(framework)
                        break
            
            self.compliance_frameworks[control_id] = applicable_frameworks

    def _generate_comprehensive_mappings(self):
        """Generate comprehensive resource-to-control mappings with ALL controls for every resource."""
        logger.info("Generating comprehensive resource-control mappings...")
        logger.info("🔒 APPLYING ALL 27 CONTROLS TO EVERY RESOURCE TYPE FOR MAXIMUM SECURITY COVERAGE")
        logger.info("Rationale: Any resource can be compromised through unexpected attack vectors")

        # Get ALL controls from all domains for comprehensive coverage
        all_controls = list(self.control_database.values())
        logger.info(f"📊 Applying {len(all_controls)} controls to every resource type")

        # Generate mappings for each resource category from the loaded azure_resource_mappings
        for resource_category in self.azure_resource_mappings:
            self.resource_mappings[resource_category] = {
                "primary_domains": ["Identity Management", "Network Security", "Data Protection"],
                "applicable_controls": all_controls,
                "control_ids": [ctrl['id'] for ctrl in all_controls],
                "focus_areas": self._determine_focus_areas(resource_category),
                "arm_types": self.azure_resource_mappings.get(resource_category, {}).get("arm_types", []),
                "terraform_types": self.azure_resource_mappings.get(resource_category, {}).get("terraform_types", []),
                "bicep_types": self.azure_resource_mappings.get(resource_category, {}).get("bicep_types", []),
                "keywords": self.azure_resource_mappings.get(resource_category, {}).get("keywords", []),
                "risk_level": self._determine_risk_level(resource_category),
                "common_vulnerabilities": self._get_common_vulnerabilities(resource_category)
            }

        logger.info(f"Generated comprehensive mappings for {len(self.resource_mappings)} resource categories")

    def _determine_focus_areas(self, resource_category: str) -> List[str]:
        """Determine focus areas based on resource category using Azure resource mappings."""
        # Base security areas that apply to all resources
        base_areas = ["access_control", "network_isolation", "data_encryption", "monitoring", "identity_security"]
        
        # Get keywords from resource mappings for the category
        category_mapping = self.azure_resource_mappings.get(resource_category, {})
        keywords = category_mapping.get("keywords", [])
        
        # Extract security-focused keywords
        security_keywords = []
        security_indicators = ["security", "protection", "encryption", "authentication", "authorization", 
                             "audit", "monitoring", "compliance", "governance", "isolation", "firewall",
                             "policy", "restriction", "control"]
        
        for keyword in keywords:
            keyword_lower = keyword.lower()
            if any(indicator in keyword_lower for indicator in security_indicators):
                # Convert keyword to focus area format
                focus_area = keyword_lower.replace(" ", "_").replace("-", "_")
                security_keywords.append(focus_area)
        
        # If no specific security keywords found, add a generic service-specific security area
        if not security_keywords:
            security_keywords = ["service_specific_security"]
            
        return base_areas + list(set(security_keywords))

    def _determine_risk_level(self, resource_category: str) -> str:
        """Determine risk level for resource category using keyword analysis."""
        category_mapping = self.azure_resource_mappings.get(resource_category, {})
        keywords = category_mapping.get("keywords", [])
        
        # Risk indicators and their weights
        high_risk_indicators = {
            "security": 3,
            "encryption": 3,
            "key": 3,
            "secret": 3,
            "certificate": 3,
            "identity": 3,
            "authentication": 3,
            "credential": 3,
            "sensitive": 3,
            "compliance": 2,
            "audit": 2,
            "protection": 2,
            "governance": 2,
            "backup": 2,
            "recovery": 2,
            "monitoring": 1,
            "logging": 1,
            "alert": 1
        }
        
        # Calculate risk score based on keywords
        risk_score = 0
        total_matches = 0
        
        for keyword in keywords:
            keyword_lower = keyword.lower()
            for indicator, weight in high_risk_indicators.items():
                if indicator in keyword_lower:
                    risk_score += weight
                    total_matches += 1
        
        # Additional risk factors from ARM types
        arm_types = category_mapping.get("arm_types", [])
        sensitive_arm_patterns = ["vault", "identity", "secret", "certificate", "sql", "database", "storage", "keyvault"]
        for arm_type in arm_types:
            for pattern in sensitive_arm_patterns:
                if pattern.lower() in arm_type.lower():
                    risk_score += 2
                    total_matches += 1
        
        # Normalize score if we have matches
        if total_matches > 0:
            normalized_score = (risk_score / total_matches)
            
            # Determine risk level based on normalized score
            if normalized_score >= 2.5:
                return "HIGH"
            elif normalized_score >= 1.5:
                return "MEDIUM"
        
        # Default to LOW if no significant risk indicators found
        return "LOW"

    def _get_common_vulnerabilities(self, resource_category: str) -> List[str]:
        """Get common vulnerabilities for resource category using Azure resource mappings."""
        # Default vulnerabilities that apply to all resources
        default_vulnerabilities = [
            "misconfiguration",
            "missing_monitoring",
            "weak_access_control",
            "unauthorized_access",
            "data_exfiltration"
        ]

        # Get keywords from resource mappings
        category_mapping = self.azure_resource_mappings.get(resource_category, {})
        keywords = category_mapping.get("keywords", [])
        
        # Extract security-related keywords and convert them to vulnerability patterns
        vulnerabilities = set()
        vulnerability_indicators = {
            "security": "weak_security",
            "authentication": "authentication_bypass",
            "encryption": "encryption_failure",
            "firewall": "firewall_bypass",
            "policy": "policy_violation",
            "audit": "audit_evasion",
            "monitoring": "monitoring_bypass",
            "protection": "protection_bypass",
            "access": "unauthorized_access",
            "network": "network_exposure",
            "isolation": "isolation_breach",
            "compliance": "compliance_violation",
            "identity": "identity_theft",
            "backup": "backup_failure",
            "replication": "replication_failure",
            "credential": "credential_exposure",
        }
        
        for keyword in keywords:
            keyword_lower = keyword.lower()
            # Check for vulnerability indicators in the keyword
            for indicator, vuln_pattern in vulnerability_indicators.items():
                if indicator in keyword_lower:
                    # Create vulnerability from keyword
                    vuln = f"weak_{keyword_lower.replace(' ', '_')}"
                    vulnerabilities.add(vuln)
                    vulnerabilities.add(vuln_pattern)
        
        # Add resource-specific prefixes to default vulnerabilities
        if resource_category:
            prefix = resource_category.lower()
            for vuln in default_vulnerabilities:
                vulnerabilities.add(f"{prefix}_{vuln}")
        
        return sorted(list(vulnerabilities))

    def get_controls_for_resource_type(self, resource_type: str) -> List[Dict]:
        """Get ALL applicable controls for a specific Azure resource type - comprehensive security coverage."""
        # Determine resource category from ARM type
        resource_category = self._determine_resource_category(resource_type)

        if resource_category and resource_category in self.resource_mappings:
            controls = self.resource_mappings[resource_category]["applicable_controls"]
            logger.debug(f"🔒 Returning {len(controls)} controls for {resource_type} (category: {resource_category})")
            return controls

        # Fallback: return all controls if category not found - ensures comprehensive coverage
        logger.info(f"🔒 Resource category not found for {resource_type}, applying ALL {len(self.control_database)} controls for maximum security coverage")
        return list(self.control_database.values())

    def _determine_resource_category(self, resource_type: str) -> Optional[str]:
        """Determine resource category from ARM resource type."""
        # First normalize the resource type
        if "Microsoft." in resource_type:
            # Extract the service name from ARM type (e.g., Microsoft.KeyVault/vaults -> KeyVault)
            service_name = resource_type.split('/')[0].split('.')[1]
        else:
            service_name = resource_type
            
        # Try exact match first
        if service_name in self.resource_mappings:
            return service_name

        # Then try case-insensitive match on the category itself
        service_name_norm = service_name.lower().replace(' ', '').replace('-', '')
        for category in self.resource_mappings:
            category_norm = category.lower().replace(' ', '').replace('-', '')
            if category_norm == service_name_norm:
                return category

        # Check each category's ARM types
        for category, mapping in self.resource_mappings.items():
            arm_types = mapping.get("arm_types", [])
            for arm_type in arm_types:
                # Handle both full ARM types and service names
                if arm_type.lower() == resource_type.lower() or arm_type.lower() == service_name.lower():
                    return category

        # Pattern matching using keywords from resource mappings
        service_name_lower = service_name.lower()
        for category, mapping in self.azure_resource_mappings.items():
            keywords = mapping.get('keywords', [])
            if any(keyword.lower() in service_name_lower for keyword in keywords):
                return category

        return None

    def get_control_links(self, control_id: str) -> Dict:
        """Get URL links and context for a specific control ID."""
        return self.url_links_database.get(control_id, {
            'raw_links': [],
            'formatted_links': '',
            'azure_guidance': '',
            'implementation_context': ''
        })

    def get_related_controls(self, control_id: str) -> List[Dict]:
        """Get controls related to a specific control ID."""
        return self.control_relationships.get(control_id, [])

    def get_control_severity(self, control_id: str) -> Dict:
        """Get severity information for a control."""
        return self.severity_mappings.get(control_id, {
            'severity': 'MEDIUM',
            'priority': 'MEDIUM',
            'domain': 'Unknown',
            'impact_score': 50
        })

    def get_compliance_frameworks(self, control_id: str) -> List[str]:
        """Get applicable compliance frameworks for a control."""
        return self.compliance_frameworks.get(control_id, [])

    def generate_security_posture_report(self, resource_types: List[str]) -> Dict:
        """Generate a comprehensive security posture report for given resource types."""
        report = {
            'timestamp': datetime.now().isoformat(),
            'total_resources': len(resource_types),
            'total_controls': len(self.control_database),
            'coverage_analysis': {},
            'risk_summary': defaultdict(int),
            'compliance_coverage': defaultdict(set),
            'priority_actions': []
        }

        for resource_type in resource_types:
            controls = self.get_controls_for_resource_type(resource_type)
            category = self._determine_resource_category(resource_type)
            
            if category:
                risk_level = self.resource_mappings[category].get('risk_level', 'MEDIUM')
                report['risk_summary'][risk_level] += 1
                
                # Track compliance coverage
                for control in controls:
                    frameworks = self.get_compliance_frameworks(control['id'])
                    for framework in frameworks:
                        report['compliance_coverage'][framework].add(control['id'])
                
                # Identify priority actions
                high_priority_controls = [c for c in controls if c.get('priority') == 'HIGH']
                if high_priority_controls and risk_level == 'HIGH':
                    report['priority_actions'].append({
                        'resource_type': resource_type,
                        'category': category,
                        'risk_level': risk_level,
                        'high_priority_controls': len(high_priority_controls),
                        'total_controls': len(controls)
                    })

        # Convert sets to lists for JSON serialization
        report['compliance_coverage'] = {k: list(v) for k, v in report['compliance_coverage'].items()}
        
        return report

    def export_enhanced_mappings(self, output_path: str = "enhanced_resource_mappings_v2.json"):
        """Export the enhanced resource mappings to JSON file."""
        try:
            export_data = {
                "metadata": {
                    "version": "2.0",
                    "description": "Enhanced Azure Resource to Security Control Mappings V2",
                    "total_controls": len(self.control_database),
                    "total_resource_categories": len(self.resource_mappings),
                    "domains_covered": list(self.csv_files.keys()),
                    "features": [
                        "comprehensive_control_coverage",
                        "url_link_extraction",
                        "control_relationships",
                        "severity_mapping",
                        "compliance_framework_mapping",
                        "risk_assessment",
                        "vulnerability_tracking"
                    ]
                },
                "control_database": self.control_database,
                "resource_mappings": self.resource_mappings,
                "url_links_database": self.url_links_database,
                "control_relationships": self.control_relationships,
                "severity_mappings": self.severity_mappings,
                "compliance_frameworks": self.compliance_frameworks
            }

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)

            logger.info(f"Enhanced resource mappings V2 exported to {output_path}")
            return True

        except Exception as e:
            logger.error(f"Error exporting enhanced mappings: {e}")
            return False

    def generate_resource_summary_report(self) -> str:
        """Generate a summary report of resource-control mappings."""
        report = []
        report.append("# Enhanced Resource-Control Mapping Summary V2")
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Resource Categories: {len(self.resource_mappings)}")
        report.append(f"Total Controls: {len(self.control_database)}")
        report.append(f"Controls with URLs: {len(self.url_links_database)}")
        report.append(f"Control Relationships Mapped: {len(self.control_relationships)}")
        report.append("")

        # Domain breakdown
        domain_counts = defaultdict(int)
        for control in self.control_database.values():
            domain_counts[control['domain']] += 1

        report.append("## Controls by Domain:")
        for domain, count in sorted(domain_counts.items()):
            report.append(f"- **{domain}**: {count} controls")
        report.append("")

        # Priority breakdown
        priority_counts = defaultdict(int)
        for control in self.control_database.values():
            priority_counts[control.get('priority', 'MEDIUM')] += 1

        report.append("## Controls by Priority:")
        for priority in ['HIGH', 'MEDIUM', 'LOW']:
            count = priority_counts[priority]
            report.append(f"- **{priority}**: {count} controls")
        report.append("")

        # Risk level breakdown
        risk_counts = defaultdict(int)
        for mapping in self.resource_mappings.values():
            risk_counts[mapping.get('risk_level', 'MEDIUM')] += 1

        report.append("## Resource Categories by Risk Level:")
        for risk in ['HIGH', 'MEDIUM', 'LOW']:
            count = risk_counts[risk]
            report.append(f"- **{risk}**: {count} categories")
        report.append("")

        # Automation possibilities
        automation_count = sum(1 for c in self.control_database.values() if c.get('automation_possible', False))
        monitoring_count = sum(1 for c in self.control_database.values() if c.get('monitoring_required', False))
        
        report.append("## Control Characteristics:")
        report.append(f"- **Automation Possible**: {automation_count} controls")
        report.append(f"- **Monitoring Required**: {monitoring_count} controls")

        return "\n".join(report)

def main():
    """Main function to demonstrate the enhanced resource mapping system V2."""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    print("🚀 Enhanced Resource-Control Mapping System V2")
    print("=" * 60)

    try:
        # Initialize the mapper
        mapper = EnhancedResourceControlMapperV2()

        # Generate summary report
        print("\n📊 Summary Report:")
        print(mapper.generate_resource_summary_report())

        # Test specific resource type mapping
        print("\n🔍 Testing Resource Type Mappings:")
        test_resources = [
            "Microsoft.Storage/storageAccounts",
            "Microsoft.KeyVault/vaults",
            "Microsoft.Network/networkSecurityGroups",
            "Microsoft.Sql/servers",
            "Microsoft.ContainerService/managedClusters",
            "Microsoft.MachineLearningServices/workspaces"
        ]

        for resource_type in test_resources:
            controls = mapper.get_controls_for_resource_type(resource_type)
            category = mapper._determine_resource_category(resource_type)
            risk_level = mapper.resource_mappings.get(category, {}).get('risk_level', 'UNKNOWN') if category else 'UNKNOWN'
            
            print(f"\n{resource_type}:")
            print(f"  📋 Category: {category}")
            print(f"  ⚠️  Risk Level: {risk_level}")
            print(f"  📋 Applicable Controls: {len(controls)}")

            # Show first 3 controls with details
            for i, control in enumerate(controls[:3]):
                control_id = control['id']
                severity = mapper.get_control_severity(control_id)
                frameworks = mapper.get_compliance_frameworks(control_id)
                links = mapper.get_control_links(control_id)
                
                print(f"    {control_id} ({control['domain']})")
                print(f"      - Priority: {control.get('priority', 'MEDIUM')}")
                print(f"      - Severity: {severity['severity']}")
                print(f"      - Compliance: {', '.join(frameworks) if frameworks else 'N/A'}")
                print(f"      - URLs: {len(links.get('raw_links', []))}")

        # Generate security posture report
        print("\n📈 Security Posture Analysis:")
        posture_report = mapper.generate_security_posture_report(test_resources)
        print(f"  🎯 Total Resources: {posture_report['total_resources']}")
        print(f"  🔒 Total Controls: {posture_report['total_controls']}")
        print(f"  ⚠️  Risk Summary:")
        for risk, count in posture_report['risk_summary'].items():
            print(f"    - {risk}: {count} resources")
        print(f"  📋 Compliance Coverage:")
        for framework, controls in posture_report['compliance_coverage'].items():
            print(f"    - {framework}: {len(controls)} controls")
        
        if posture_report['priority_actions']:
            print(f"  🚨 Priority Actions Required: {len(posture_report['priority_actions'])} resources")

        # Test control relationships
        print("\n🔗 Testing Control Relationships:")
        test_control = "IM-1"
        related = mapper.get_related_controls(test_control)
        print(f"  Control {test_control} has {len(related)} related controls")
        for rel in related[:3]:
            print(f"    - {rel['control_id']} ({rel['relationship_type']})")

        # Export enhanced mappings
        print("\n💾 Exporting Enhanced Mappings V2...")
        if mapper.export_enhanced_mappings():
            print("✅ Enhanced mappings V2 exported successfully!")
        else:
            print("❌ Failed to export enhanced mappings")

        print("\n🎉 Enhanced Resource-Control Mapping System V2 completed successfully!")

    except Exception as e:
        print(f"❌ Error: {e}")
        logging.exception("Detailed error information:")

if __name__ == "__main__":
    main()
