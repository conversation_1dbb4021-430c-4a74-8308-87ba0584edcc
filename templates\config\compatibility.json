{"compatibility_rules": [{"min_version": "1.0.0", "max_version": "1.9.9", "breaking_changes": [], "migration_notes": ["Version 1.x series maintains backward compatibility", "All template variables and structure remain the same"]}, {"min_version": "2.0.0", "max_version": "2.9.9", "breaking_changes": ["Template variable naming convention changed to snake_case", "CSS class structure updated for Glass UI v2", "JavaScript API methods renamed for consistency"], "migration_notes": ["Update template variables from camelCase to snake_case", "Review CSS classes for Glass UI v2 compatibility", "Update JavaScript method calls to new API", "Run template validation after migration"]}, {"min_version": "3.0.0", "max_version": null, "breaking_changes": ["Complete template structure overhaul", "New component-based architecture", "Enhanced security validation requirements"], "migration_notes": ["Major version upgrade requires full template review", "Consider using migration tools for automatic conversion", "Test all templates thoroughly after upgrade", "Update documentation and training materials"]}], "version_history": [{"version": "1.0.0", "release_date": "2024-01-01", "changes": ["Initial template system release", "Basic HTML, CSS, JS template support", "Simple prompt template system"]}, {"version": "1.1.0", "release_date": "2024-02-01", "changes": ["Added Glass UI framework", "Enhanced responsive design", "Improved security analysis prompts"]}, {"version": "1.2.0", "release_date": "2024-03-01", "changes": ["Added template validation", "Performance monitoring integration", "Hot reloading support"]}], "migration_tools": {"1.x_to_2.x": {"script": "scripts/migrate_v1_to_v2.py", "description": "Automated migration from v1.x to v2.x templates", "backup_required": true}, "2.x_to_3.x": {"script": "scripts/migrate_v2_to_v3.py", "description": "Automated migration from v2.x to v3.x templates", "backup_required": true, "manual_review_required": true}}, "deprecation_warnings": [{"feature": "inline_styles", "deprecated_in": "1.1.0", "removed_in": "2.0.0", "replacement": "Use Glass UI CSS classes instead of inline styles"}, {"feature": "legacy_prompt_format", "deprecated_in": "1.2.0", "removed_in": "2.0.0", "replacement": "Use new structured prompt templates with sections"}]}