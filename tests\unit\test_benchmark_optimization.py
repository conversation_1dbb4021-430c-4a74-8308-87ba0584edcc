#!/usr/bin/env python3
"""
Test script to validate Azure Security Benchmark optimization.
Tests source prioritization, domain ordering, and consistency improvements.
"""

import os
import json
import tempfile
from pathlib import Path
from security_opt import SecurityPRReviewer
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_benchmark_source_priority():
    """Test that benchmark sources are loaded in correct priority order."""
    logger.info("🧪 Testing benchmark source priority")
    
    test_cases = [
        ("csv,json,excel,fallback", "csv"),
        ("json,excel,fallback", "json"),
        ("excel,fallback", "excel"),
        ("fallback", "fallback")
    ]
    
    for priority_setting, expected_source in test_cases:
        logger.info(f"Testing priority: {priority_setting}")
        
        # Set environment
        os.environ['BENCHMARK_SOURCE_PRIORITY'] = priority_setting
        os.environ['ENFORCE_DOMAIN_PRIORITY'] = 'true'
        
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                reviewer = SecurityPRReviewer(local_folder=temp_dir)
                benchmark = reviewer.prepare_benchmark()
                
                actual_source = benchmark.get('metadata', {}).get('source', '').lower()
                
                if expected_source in actual_source:
                    logger.info(f"✅ Correctly loaded from {expected_source.upper()}")
                else:
                    logger.warning(f"⚠️  Expected {expected_source}, got {actual_source}")
                    
        except Exception as e:
            logger.error(f"❌ Error testing {priority_setting}: {str(e)}")

def test_domain_prioritization():
    """Test that controls are properly prioritized by domain."""
    logger.info("🧪 Testing domain prioritization")
    
    # Test with domain prioritization enabled
    os.environ['ENFORCE_DOMAIN_PRIORITY'] = 'true'
    os.environ['BENCHMARK_SOURCE_PRIORITY'] = 'csv,fallback'
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            reviewer = SecurityPRReviewer(local_folder=temp_dir)
            benchmark = reviewer.prepare_benchmark()
            
            controls = benchmark.get('controls', [])
            if not controls:
                logger.warning("⚠️  No controls loaded for domain prioritization test")
                return
            
            # Check if controls have domain priorities
            prioritized_controls = [c for c in controls if 'domain_priority' in c]
            logger.info(f"📊 Found {len(prioritized_controls)} controls with domain priorities")
            
            # Verify domain order
            domain_order = []
            current_priority = 0
            
            for control in controls:
                priority = control.get('domain_priority', 999)
                domain = control.get('domain', 'Unknown')
                
                if priority > current_priority:
                    current_priority = priority
                    if domain not in domain_order:
                        domain_order.append(domain)
            
            logger.info("📋 Domain order in controls:")
            for i, domain in enumerate(domain_order, 1):
                logger.info(f"   {i}. {domain}")
            
            # Check if Identity Management comes first
            if domain_order and 'Identity' in domain_order[0]:
                logger.info("✅ Identity Management correctly prioritized first")
            else:
                logger.warning("⚠️  Identity Management not first in priority order")
                
    except Exception as e:
        logger.error(f"❌ Error testing domain prioritization: {str(e)}")

def test_control_consistency():
    """Test that control selection is consistent across multiple runs."""
    logger.info("🧪 Testing control selection consistency")
    
    # Create test template
    test_template = {
        "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
        "contentVersion": "*******",
        "resources": [
            {
                "type": "Microsoft.Storage/storageAccounts",
                "apiVersion": "2021-04-01",
                "name": "teststorage",
                "location": "[resourceGroup().location]",
                "properties": {
                    "supportsHttpsTrafficOnly": False
                }
            }
        ]
    }
    
    os.environ['ENFORCE_DOMAIN_PRIORITY'] = 'true'
    os.environ['USE_OPTIMIZED_PROMPTS'] = 'true'
    os.environ['ANALYSIS_SEED'] = '42'
    
    control_sets = []
    
    for run in range(3):
        logger.info(f"🔄 Consistency test run {run + 1}")
        
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                # Create test file
                test_file = Path(temp_dir) / "test.json"
                test_file.write_text(json.dumps(test_template, indent=2))
                
                reviewer = SecurityPRReviewer(local_folder=temp_dir)
                
                # Get relevant controls for Storage resource
                relevant_controls = reviewer._find_relevant_controls("Storage")
                control_ids = [c.get('id') for c in relevant_controls]
                control_sets.append(sorted(control_ids))
                
                logger.info(f"   Found {len(control_ids)} relevant controls")
                
        except Exception as e:
            logger.error(f"❌ Error in consistency test run {run + 1}: {str(e)}")
            control_sets.append([])
    
    # Check consistency
    if len(set(map(tuple, control_sets))) == 1:
        logger.info("✅ Control selection is consistent across runs")
        logger.info(f"   Controls: {control_sets[0][:5]}..." if control_sets[0] else "   No controls found")
    else:
        logger.warning("⚠️  Control selection varies across runs")
        for i, controls in enumerate(control_sets, 1):
            logger.warning(f"   Run {i}: {controls[:5]}...")

def test_domain_specific_controls():
    """Test that domain-specific controls are properly categorized."""
    logger.info("🧪 Testing domain-specific control categorization")
    
    os.environ['ENFORCE_DOMAIN_PRIORITY'] = 'true'
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            reviewer = SecurityPRReviewer(local_folder=temp_dir)
            benchmark = reviewer.prepare_benchmark()
            
            controls = benchmark.get('controls', [])
            domain_stats = {}
            
            for control in controls:
                domain = control.get('domain', 'Unknown')
                domain_stats[domain] = domain_stats.get(domain, 0) + 1
            
            logger.info("📊 Domain distribution:")
            total_controls = len(controls)
            
            for domain, count in sorted(domain_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total_controls) * 100 if total_controls > 0 else 0
                logger.info(f"   • {domain}: {count} controls ({percentage:.1f}%)")
            
            # Check for expected domains
            expected_domains = ['Identity Management', 'Network Security', 'Data Protection']
            found_domains = list(domain_stats.keys())
            
            for expected in expected_domains:
                if any(expected.lower() in domain.lower() for domain in found_domains):
                    logger.info(f"✅ Found {expected} controls")
                else:
                    logger.warning(f"⚠️  Missing {expected} controls")
                    
    except Exception as e:
        logger.error(f"❌ Error testing domain categorization: {str(e)}")

def test_csv_validation():
    """Test CSV file validation and error handling."""
    logger.info("🧪 Testing CSV validation")
    
    # Test with missing CSV files
    os.environ['BENCHMARK_SOURCE_PRIORITY'] = 'csv,fallback'
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create SecurityBenchmarks directory but no CSV files
            benchmark_dir = Path(temp_dir) / "SecurityBenchmarks"
            benchmark_dir.mkdir()
            
            reviewer = SecurityPRReviewer(local_folder=temp_dir)
            benchmark = reviewer.prepare_benchmark()
            
            source = benchmark.get('metadata', {}).get('source', '').lower()
            
            if 'fallback' in source:
                logger.info("✅ Correctly fell back to fallback controls when CSV missing")
            else:
                logger.warning(f"⚠️  Expected fallback, got {source}")
                
    except Exception as e:
        logger.error(f"❌ Error testing CSV validation: {str(e)}")

def run_comprehensive_test():
    """Run all benchmark optimization tests."""
    logger.info("🚀 Starting comprehensive benchmark optimization tests")
    logger.info("=" * 80)
    
    tests = [
        ("Benchmark Source Priority", test_benchmark_source_priority),
        ("Domain Prioritization", test_domain_prioritization),
        ("Control Consistency", test_control_consistency),
        ("Domain-Specific Controls", test_domain_specific_controls),
        ("CSV Validation", test_csv_validation)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running: {test_name}")
        logger.info("-" * 50)
        
        try:
            test_func()
            results[test_name] = "PASSED"
            logger.info(f"✅ {test_name} completed successfully")
        except Exception as e:
            results[test_name] = f"FAILED: {str(e)}"
            logger.error(f"❌ {test_name} failed: {str(e)}")
    
    # Summary
    logger.info("\n" + "=" * 80)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 80)
    
    passed = sum(1 for result in results.values() if result == "PASSED")
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅" if result == "PASSED" else "❌"
        logger.info(f"{status} {test_name}: {result}")
    
    logger.info(f"\n📈 Overall: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        logger.info("🎉 All benchmark optimization tests passed!")
    else:
        logger.warning("⚠️  Some tests failed - review optimization implementation")

if __name__ == "__main__":
    try:
        run_comprehensive_test()
    except KeyboardInterrupt:
        logger.info("\n🛑 Tests interrupted by user")
    except Exception as e:
        logger.error(f"💥 Test suite failed: {str(e)}")
        raise
