File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
app-config.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyValues' flows across template boundary,"Secure parameter 'keyValues' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'app_insights_key' flows across template boundary,"Secure parameter 'app_insights_key' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyvault_name' flows across template boundary,"Secure parameter 'keyvault_name' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
keyvault.bicep,NS-1,Network Security,Protect resources using network security groups,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,"Review template dependencies and ensure secure communication between templates

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'secrets' flows across template boundary,"Secure parameter 'secrets' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
storage-accounts.bicep,NS-1,Network Security,Protect resources using network security groups,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,"Review template dependencies and ensure secure communication between templates

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,DP-6,Data Protection,Secure Data with Customer-Managed Keys (CMK),HIGH,1.0,Parameter flow security risk: Sensitive parameters may be exposed through template dependencies,"Use Key Vault references for sensitive parameters and validate parameter flow security

📚 References: [Customer Managed Keys Overview](https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-overview) | [Customer Managed Keys Configure Key Vault](https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-configure-key-vault) | [Transparent Data Encryption Byok Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-byok-overview)

🔵 Azure Guidance: Use customer-managed keys for critical data.",[Customer Managed Keys Overview](https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-overview) | [Customer Managed Keys Configure Key Vault](https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-configure-key-vault) | [Transparent Data Encryption Byok Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-byok-overview),Use customer-managed keys for critical data.,"Configure Key Vault for key management. Rotate keys regularly.
Customer-managed keys: https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-overview
Key Vault CMK setup: https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-configure-key-vault
SQL CMK configuration: https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-byok-overview

This control is implemented through 3 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,parameter_flow,Validated
app-config.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,7.0,"The App Configuration resource 'Microsoft.AppConfiguration/configurationStores' does not restrict public network access or define access controls for its public endpoint. By default, App Configuration endpoints are publicly accessible, which violates ASB NS-2 (Protect public endpoints).","Restrict public network access to the App Configuration resource by setting 'publicNetworkAccess' to 'Disabled' or configuring 'networkAcl' to allow only required IPs or subnets. Consider using Private Link for secure access. Reference: https://docs.microsoft.com/en-us/azure/azure-app-configuration/concept-private-endpoint

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
app-config.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,7.0,"The App Configuration resource 'Microsoft.AppConfiguration/configurationStores' does not configure a Private Endpoint, leaving the service accessible over the public internet. This increases the attack surface and violates ASB NS-5 (Use Private Endpoints).","Configure a Private Endpoint for the App Configuration resource to ensure access is only available from within your private network. Reference: https://docs.microsoft.com/en-us/azure/azure-app-configuration/concept-private-endpoint

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
autoscale-settings.bicep,NS-1,Network Security,Protect resources using network security groups,MEDIUM,10.0,The autoscaleSettings resource (Microsoft.Insights/autoscalesettings) does not specify any network security group (NSG) or network segmentation. Network segmentation is critical for defense in depth as required by ASB NS-1.,"Ensure that the underlying resources (such as the App Service Plan or VMs) associated with autoscaleSettings are protected by appropriate Network Security Groups (NSGs) at the subnet or NIC level. Review and apply NSGs to restrict access as needed.

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
autoscale-settings.bicep,NS-2,Network Security,Protect public endpoints,MEDIUM,10.0,The autoscaleSettings resource does not explicitly restrict public endpoints or define access controls for public exposure. ASB NS-2 requires strict access control for public endpoints.,"Restrict public access to the underlying resources managed by autoscaleSettings. Use IP restrictions, Private Link, or Application Gateway to control access to public endpoints.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
autoscale-settings.bicep,NS-3,Network Security,Use Network Security Groups (NSGs),MEDIUM,10.0,No Network Security Groups (NSGs) are defined or referenced for the autoscaleSettings resource or its associated resources. ASB NS-3 requires NSGs to deny all inbound traffic by default and allow only necessary traffic.,"Configure NSGs for the subnets or NICs associated with the autoscaleSettings target resources. Set default deny rules for inbound traffic and allow only required traffic.

📚 References: [Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules)

🔵 Azure Guidance: Implement NSGs with deny-by-default rules.",[Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules),Implement NSGs with deny-by-default rules.,"Configure NSGs to deny all inbound traffic by default. Allow only necessary traffic.
NSG rules documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works
Security rules best practices: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
autoscale-settings.bicep,NS-4,Network Security,Use Azure Firewall or third-party firewall,HIGH,10.0,"The template does not reference or configure Azure Firewall or a third-party firewall for advanced network protection for the autoscaleSettings resource or its associated resources, as required by ASB NS-4.","Deploy and configure Azure Firewall or a supported third-party firewall to monitor and protect network traffic to and from the resources managed by autoscaleSettings. Enable threat intelligence and logging.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/firewall/overview) | [docs.microsoft.com/en-us/azure/firewall/overview](https://docs.microsoft.com/en-us/azure/firewall/policy-overview) | [Threat Intel](https://docs.microsoft.com/en-us/azure/firewall/threat-intel)

🔵 Azure Guidance: Deploy Azure Firewall or partner solution for enhanced security.",[Overview](https://docs.microsoft.com/en-us/azure/firewall/overview) | [docs.microsoft.com/en-us/azure/firewall/overview](https://docs.microsoft.com/en-us/azure/firewall/policy-overview) | [Threat Intel](https://docs.microsoft.com/en-us/azure/firewall/threat-intel),Deploy Azure Firewall or partner solution for enhanced security.,"Configure Azure Firewall policies. Enable threat intelligence. Monitor traffic.
Azure Firewall documentation: https://docs.microsoft.com/en-us/azure/firewall/overview
Firewall policies guide: https://docs.microsoft.com/en-us/azure/firewall/policy-overview
Threat intelligence setup: https://docs.microsoft.com/en-us/azure/firewall/threat-intel",ai_analysis,,Validated
autoscale-settings.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,10.0,The template does not configure Private Endpoints for the autoscaleSettings resource or its associated storage account (func_storage_account_id). ASB NS-5 requires use of Private Endpoints to reduce attack surface.,"Configure Private Endpoints for the storage account referenced by func_storage_account_id and any other PaaS resources to ensure traffic remains on the Azure backbone and is not exposed to the public internet.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
event-grid.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,19.0,"The Event Grid system topic resource (line 19) does not configure Private Endpoints, which is required to reduce the attack surface for Azure PaaS services.","Configure Private Endpoints for the Event Grid system topic to ensure traffic remains on the Azure backbone network. Refer to https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview for implementation guidance.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
event-grid.bicep,NS-6,Network Security,Use Virtual Network Service Endpoints,MEDIUM,19.0,"The Event Grid system topic resource (line 19) does not configure Virtual Network Service Endpoints, which are recommended to restrict access to trusted networks.","Enable Virtual Network Service Endpoints for the Event Grid system topic to restrict access to trusted subnets. See https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview for details.

📚 References: [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Storage Network Security](https://docs.microsoft.com/en-us/azure/storage/common/storage-network-security#grant-access-from-a-virtual-network) | [Vnet Service Endpoint Rule Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/vnet-service-endpoint-rule-overview)

🔵 Azure Guidance: Enable service endpoints to secure Azure service traffic.",[Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Storage Network Security](https://docs.microsoft.com/en-us/azure/storage/common/storage-network-security#grant-access-from-a-virtual-network) | [Vnet Service Endpoint Rule Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/vnet-service-endpoint-rule-overview),Enable service endpoints to secure Azure service traffic.,"Configure service endpoints for Storage SQL CosmosDB and other services.
Service endpoints overview: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Storage service endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-network-security#grant-access-from-a-virtual-network
SQL service endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/vnet-service-endpoint-rule-overview

This control is implemented through 2 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
function-settings.bicep,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,38.0,"The parameter 'ado_access_client_id' is assigned a hardcoded client ID value directly in the template. Storing sensitive information such as client IDs or secrets in code violates DP-3, which requires secrets to be managed securely (e.g., via Azure Key Vault references).","Remove the hardcoded value from the 'ado_access_client_id' parameter and instead reference the value from Azure Key Vault using a Key Vault reference. Update the template to retrieve sensitive values securely at deployment/runtime.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",[Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references),Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.
Key Vault overview: https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Secret management: https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets
App Service Key Vault integration: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
function.bicep,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,44.0,"The parameter 'clientSecretSettingName' in the Azure Active Directory identity provider registration is set to the static string 'OVERRIDE_USE_MI_FIC_ASSERTION_CLIENTID' at line 44. This may indicate a hardcoded secret or credential reference, which violates the requirement to never store secrets in code and to use Key Vault references instead.","Replace the static 'clientSecretSettingName' value with a Key Vault reference or use managed identities for authentication. Ensure that secrets are not stored in code or parameters, and configure the application to retrieve secrets securely from Azure Key Vault as per ASB DP-3.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",[Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references),Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.
Key Vault overview: https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Secret management: https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets
App Service Key Vault integration: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
function.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,38.0,"The App Service resource at line 38 does not restrict public access or IP ranges. By default, App Service endpoints are publicly accessible, which violates the requirement to restrict public endpoints to required IPs only.","Restrict public access to the App Service by configuring access restrictions (IP allowlist/denylist), using Private Endpoints, or integrating with Azure Application Gateway or Azure Front Door. Refer to ASB NS-2 for guidance on protecting public endpoints.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
hub-network.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,7.0,The subnet 'hub-subnet' defined at line 7 does not have a Network Security Group (NSG) associated. NSGs are required to segment and protect network resources as per ASB NS-1.,"Associate a Network Security Group (NSG) with the 'hub-subnet' by adding the 'networkSecurityGroup' property referencing an NSG resource. Define and attach an NSG to restrict traffic as needed.

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
hub-network.bicep,NS-3,Network Security,Use Network Security Groups (NSGs),CRITICAL,7.0,The subnet 'hub-subnet' at line 7 lacks an explicit Network Security Group (NSG) configuration. NSGs are required to enforce network-level access controls and deny all inbound traffic by default as per ASB NS-3.,"Create an NSG resource with appropriate security rules and associate it with the 'hub-subnet' using the 'networkSecurityGroup' property.

📚 References: [Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules)

🔵 Azure Guidance: Implement NSGs with deny-by-default rules.",[Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules),Implement NSGs with deny-by-default rules.,"Configure NSGs to deny all inbound traffic by default. Allow only necessary traffic.
NSG rules documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works
Security rules best practices: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
hub-network.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,18.0,The subnet 'hub-subnet' at line 18 uses service endpoints for 'Microsoft.Storage.Global' and 'Microsoft.Keyvault' but does not use Private Endpoints. ASB NS-5 recommends using Private Endpoints for Storage and Key Vault to reduce attack surface.,"Replace or supplement service endpoints with Private Endpoints for Azure Storage and Key Vault by defining 'Microsoft.Network/privateEndpoints' resources and associating them with the required services.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
instance-config.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,23.0,"The network_config object at line 23 defines an address_space and subnet but does not reference or configure any Network Security Groups (NSGs) to segment or protect the subnet, violating NS-1 (Protect resources using network security groups).","Define and associate a Network Security Group (NSG) with the subnet in the network_config object. Explicitly specify NSG rules to allow only required ports and protocols. Refer to Azure NSG documentation for implementation.

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
instance-config.bicep,NS-3,Network Security,Use Network Security Groups (NSGs),CRITICAL,23.0,"The template at line 23 configures a subnet in network_config but does not apply any NSG or specify access control rules, violating NS-3 (Use Network Security Groups (NSGs)).","Add an NSG resource and associate it with the subnet defined in network_config. Configure the NSG to deny all inbound traffic by default and allow only necessary traffic.

📚 References: [Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules)

🔵 Azure Guidance: Implement NSGs with deny-by-default rules.",[Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules),Implement NSGs with deny-by-default rules.,"Configure NSGs to deny all inbound traffic by default. Allow only necessary traffic.
NSG rules documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works
Security rules best practices: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
instance-config.bicep,NS-4,Network Security,Use Azure Firewall or third-party firewall,HIGH,23.0,"No Azure Firewall or third-party firewall is configured for the network defined at line 23, violating NS-4 (Use Azure Firewall or third-party firewall).","Deploy an Azure Firewall or a supported third-party firewall in the virtual network. Configure firewall policies and enable threat intelligence for advanced network protection.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/firewall/overview) | [docs.microsoft.com/en-us/azure/firewall/overview](https://docs.microsoft.com/en-us/azure/firewall/policy-overview) | [Threat Intel](https://docs.microsoft.com/en-us/azure/firewall/threat-intel)

🔵 Azure Guidance: Deploy Azure Firewall or partner solution for enhanced security.",[Overview](https://docs.microsoft.com/en-us/azure/firewall/overview) | [docs.microsoft.com/en-us/azure/firewall/overview](https://docs.microsoft.com/en-us/azure/firewall/policy-overview) | [Threat Intel](https://docs.microsoft.com/en-us/azure/firewall/threat-intel),Deploy Azure Firewall or partner solution for enhanced security.,"Configure Azure Firewall policies. Enable threat intelligence. Monitor traffic.
Azure Firewall documentation: https://docs.microsoft.com/en-us/azure/firewall/overview
Firewall policies guide: https://docs.microsoft.com/en-us/azure/firewall/policy-overview
Threat intelligence setup: https://docs.microsoft.com/en-us/azure/firewall/threat-intel",ai_analysis,,Validated
instance-config.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,23.0,"The network_config at line 23 does not configure Private Endpoints for any PaaS services, violating NS-5 (Use Private Endpoints).","Configure Private Endpoints for all Azure PaaS services (such as Storage, Key Vault, SQL) used by resources in this network. Update the template to include private endpoint resources and associate them with the relevant services.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
keyvault.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,19.0,"Key Vault 'networkAcls.defaultAction' is set to 'Allow', which exposes the Key Vault to the public internet. This violates the requirement to restrict public endpoints.","Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only required IPs or virtual networks to access the Key Vault. Example: change 'defaultAction' to 'Deny' in the Key Vault resource definition.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
scaleset-networks.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,38.0,The subnet 'scaleset' defined at line 38 does not have a Network Security Group (NSG) associated. NSGs are required to segment and protect network resources.,"Associate a Network Security Group (NSG) with the 'scaleset' subnet to restrict inbound and outbound traffic according to least privilege. Define an NSG resource and reference its id in the subnet's 'networkSecurityGroup' property.

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
scaleset-networks.bicep,NS-3,Network Security,Use Network Security Groups (NSGs),CRITICAL,38.0,"No Network Security Group (NSG) is configured for the 'scaleset' subnet at line 38, violating the requirement to use NSGs for network-level access control.","Create and associate an NSG with the 'scaleset' subnet. Configure the NSG to deny all inbound traffic by default and allow only necessary traffic.

📚 References: [Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules)

🔵 Azure Guidance: Implement NSGs with deny-by-default rules.",[Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules),Implement NSGs with deny-by-default rules.,"Configure NSGs to deny all inbound traffic by default. Allow only necessary traffic.
NSG rules documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works
Security rules best practices: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
server-farms.bicep,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,109.0,"The 'CERTIFICATE_PASSWORD_GENEVACERT' App Service setting is configured with an empty string as its value, which may indicate a placeholder for a secret or password directly in the template. Storing secrets in code or templates violates sensitive information disclosure controls.","Remove the 'CERTIFICATE_PASSWORD_GENEVACERT' setting from the template or reference the secret securely using Azure Key Vault integration. Never store secrets or passwords directly in code or templates. See: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",[Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references),Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.
Key Vault overview: https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Secret management: https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets
App Service Key Vault integration: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
signalR.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,4.0,"The Microsoft.SignalRService/signalR resource does not restrict public network access or define allowed IPs. By default, SignalR Service is accessible over the public internet, which violates the requirement to restrict public endpoints.","Restrict public network access to the SignalR resource by configuring the 'publicNetworkAccess' property to 'Disabled' or by specifying allowed IP address ranges using the 'networkACLs' property. Alternatively, use Private Link to eliminate public exposure.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
signalR.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,4.0,The Microsoft.SignalRService/signalR resource does not use a Private Endpoint. Private Endpoints are required to reduce the attack surface and prevent exposure to the public internet.,"Configure a Private Endpoint for the SignalR resource by adding a Microsoft.Network/privateEndpoints resource and associating it with the SignalR instance.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,22.0,"The 'networkAcls' configuration for 'storageAccountFunc' sets 'defaultAction' to 'Allow', which exposes the storage account to public network access. This violates ASB NS-2, which requires strict access control for public endpoints.","Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only required IPs or virtual networks. Example: change 'defaultAction: ""Allow""' to 'defaultAction: ""Deny""' in the 'networkAcls' block.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,54.0,"The 'networkAcls' configuration for 'fuzzStorageProperties' sets 'defaultAction' to 'Allow', which exposes the storage account to public network access. This violates ASB NS-2, which requires strict access control for public endpoints.","Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only required IPs or virtual networks. Example: change 'defaultAction: ""Allow""' to 'defaultAction: ""Deny""' in the 'networkAcls' block.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 34,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 7,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-17T15:08:58.256016,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
