# IaC Guardian GPT - Optimized Configuration for Consistent Analysis
# Copy this file to .env and update with your actual values

# =============================================================================
# AZURE DEVOPS CONFIGURATION
# =============================================================================
AZURE_DEVOPS_PAT=your_personal_access_token_here
AZURE_DEVOPS_ORG=your_organization_name
AZURE_DEVOPS_PROJECT=your_project_name

# =============================================================================
# AZURE OPENAI CONFIGURATION
# =============================================================================
AZURE_OPENAI_ENDPOINT=https://your-openai-resource.openai.azure.com/
AZURE_OPENAI_API_KEY=your_api_key_here
AZURE_OPENAI_DEPLOYMENT=gpt-4o
AZURE_OPENAI_API_VERSION=2024-02-01

# Optional: Use Azure AD authentication instead of API key
AZURE_OPENAI_USE_AD_AUTH=false

# Model type configuration (auto-detected from deployment name, but can be overridden)
# Supported values: gpt-4o, o1-preview, o1-mini, gpt-35-turbo, gpt-4
# AZURE_OPENAI_MODEL_TYPE=gpt-4o

# =============================================================================
# PROMPT OPTIMIZATION SETTINGS (NEW)
# =============================================================================

# Enable optimized prompts for consistent analysis results
# Set to 'true' for production use, 'false' for backward compatibility
USE_OPTIMIZED_PROMPTS=true

# Analysis seed for reproducible results
# Use the same seed across environments for consistent findings
ANALYSIS_SEED=42

# =============================================================================
# BENCHMARK OPTIMIZATION SETTINGS (NEW)
# =============================================================================

# Benchmark source priority (comma-separated, in order of preference)
# Options: csv, json, excel, fallback
# csv = Optimized CSV files with domain prioritization
# json = Processed JSON cache files
# excel = Official Microsoft Excel files (downloaded if needed)
# fallback = Hardcoded emergency controls
BENCHMARK_SOURCE_PRIORITY=csv,json,excel,fallback

# Enforce domain prioritization in recommendations
# When true, recommendations follow: Identity → Network → Data Protection → Access → Monitoring
ENFORCE_DOMAIN_PRIORITY=true

# Domain priority order (used when ENFORCE_DOMAIN_PRIORITY=true)
# This order ensures Identity issues are addressed first, followed by Network isolation, then Data protection
DOMAIN_PRIORITY_ORDER=Identity Management,Network Security,Data Protection,Access Management,Logging and Monitoring

# =============================================================================
# TEMPLATE PROCESSING CONFIGURATION
# =============================================================================

# Template file patterns (comma-separated glob patterns)
TEMPLATE_PATTERNS=*Template*,*template*,main.json,*.json,*app*.json,*deploymentTemplate*

# Parameter file patterns (comma-separated glob patterns)
PARAMETER_PATTERNS=*Param*,*Parameter*,*params*,*parameters*,*deploymentParameters*

# Template file identifiers (comma-separated)
TEMPLATE_IDENTIFIERS=Template,template,main,deploy,ArmTemplate,deploymentTemplate

# Parameter file identifiers (comma-separated)
PARAMETER_IDENTIFIERS=Param,Parameter,params,parameters,ArmParam,deploymentParameters

# Deployment worthiness threshold (default: 80, lower = more findings)
DEPLOYMENT_THRESHOLD=70

# Foundational security controls that get additional bonus points (comma-separated)
# These controls are considered critical foundations of security architecture
#
# Default foundational controls:
# - IM-1: Centralized identity management system
# - IM-2: Use strong authentication for all users
# - IM-6: Use privileged access workstations
# - NS-1: Establish network segmentation boundaries
# - NS-3: Deploy Azure DDoS Protection Standard
# - DP-3: Monitor and block unauthorized data transfer
# - DP-4: Encrypt sensitive data in transit
#
# Customize by adding/removing control IDs (e.g., IM-1,IM-3,NS-2,DP-1)
FOUNDATIONAL_CONTROLS=IM-1,IM-2,IM-6,NS-1,NS-3,DP-3,DP-4
FOUNDATIONAL_BONUS_POINTS=30

# Template-parameter matching strategy
# Options: 'strict', 'fuzzy', 'smart' (recommended)
MATCHING_STRATEGY=smart

# Enable parameter expansion for enhanced analysis
ENABLE_PARAMETER_EXPANSION=true

# Parameter search depth for cross-references
PARAM_SEARCH_DEPTH=3

# Matching options for template-parameter pairing
MATCH_BY_PREFIX=true
MATCH_BY_SUFFIX=true
MATCH_BY_SUBSTRING=true

# Minimum similarity score for fuzzy matching (0.0-1.0)
MIN_SIMILARITY_SCORE=0.7

# Analyze parameter files independently
ANALYZE_PARAM_FILES=true

# Combine findings from templates and parameters
COMBINE_FINDINGS=true

# =============================================================================
# ANALYSIS BEHAVIOR SETTINGS
# =============================================================================

# Output format options: 'json', 'html', 'csv', 'all'
OUTPUT_FORMAT=html

# Enable detailed logging for troubleshooting
LOG_LEVEL=INFO

# Maximum number of controls to include per resource type
MAX_CONTROLS_PER_RESOURCE=15

# Include pattern-based findings alongside AI analysis
ENABLE_PATTERN_MATCHING=true

# =============================================================================
# CONSISTENCY VALIDATION SETTINGS
# =============================================================================

# Enable consistency validation logging
ENABLE_CONSISTENCY_LOGGING=true

# Validate findings across multiple runs (for testing)
VALIDATE_CONSISTENCY=false

# Number of validation runs (only used if VALIDATE_CONSISTENCY=true)
CONSISTENCY_VALIDATION_RUNS=3

# =============================================================================
# PERFORMANCE OPTIMIZATION
# =============================================================================

# Maximum tokens for AI analysis (adjust based on template complexity)
MAX_ANALYSIS_TOKENS=3000

# Timeout for AI analysis requests (seconds)
ANALYSIS_TIMEOUT=60

# Enable caching of benchmark data
ENABLE_BENCHMARK_CACHING=true

# Cache directory for benchmark data
BENCHMARK_CACHE_DIR=./cache

# =============================================================================
# REPORTING CONFIGURATION
# =============================================================================

# Report title for HTML output
REPORT_TITLE=Security Assessment Report - IaC Guardian

# Include executive summary in reports
INCLUDE_EXECUTIVE_SUMMARY=true

# Group findings by severity in reports
GROUP_BY_SEVERITY=true

# Include remediation guidance in reports
INCLUDE_REMEDIATION=true

# Show code snippets in findings
INCLUDE_CODE_SNIPPETS=true

# =============================================================================
# ADVANCED SETTINGS
# =============================================================================

# Custom Azure resource mappings file
AZURE_RESOURCE_MAPPINGS_FILE=azure_resource_mappings.json

# Custom security benchmark file
SECURITY_BENCHMARK_FILE=

# Enable experimental features
ENABLE_EXPERIMENTAL_FEATURES=false

# Debug mode for detailed troubleshooting
DEBUG_MODE=false

# =============================================================================
# ENVIRONMENT-SPECIFIC OVERRIDES
# =============================================================================

# Development environment settings
# Uncomment and modify for development use
# USE_OPTIMIZED_PROMPTS=false
# LOG_LEVEL=DEBUG
# VALIDATE_CONSISTENCY=true

# Production environment settings
# Uncomment and modify for production use
# USE_OPTIMIZED_PROMPTS=true
# LOG_LEVEL=INFO
# ENABLE_CONSISTENCY_LOGGING=true
# ANALYSIS_SEED=42

# Testing environment settings
# Uncomment and modify for testing use
# USE_OPTIMIZED_PROMPTS=true
# VALIDATE_CONSISTENCY=true
# CONSISTENCY_VALIDATION_RUNS=5
# DEBUG_MODE=true
