Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
CRITICAL,NS-1,keyvault.bicep,22.0,"The Key Vault 'networkAcls' property sets 'defaultAction' to 'Allow', which means the vault is accessible from all public networks except for those explicitly denied. This provides broad network access and significantly increases the attack surface, violating network segmentation best practices.","Set 'networkAcls.defaultAction' to 'Deny' to ensure the Key Vault is only accessible via defined IP rules, virtual network rules, or trusted Azure services. Only grant exceptions for specific, necessary sources.",N/A,AI,Generic
CRITICAL,NS-2,keyvault.bicep,22.0,The Key Vault permits public GET/PUT access to anyone on the internet due to 'networkAcls.defaultAction' set to 'Allow'. This exposes sensitive secrets in the vault to potential unauthorized access.,Set Key Vault 'networkAcls.defaultAction' to 'Deny' and define access only from necessary trusted IPs and VNets to avoid public endpoint exposure.,N/A,AI,Generic
CRITICAL,NS-1,storage-accounts.bicep,21.0,"The 'networkAcls' for all storage accounts sets 'defaultAction' to 'Allow', which permits access from all networks except those explicitly blocked, exposing storage accounts to attacks from unauthorized networks. ASB NS-1 recommends restricting access using NSGs/Azure Firewall and minimizing public accessibility.","Change 'defaultAction' in 'networkAcls' to 'Deny' and ensure that only required IP addresses (e.g., via ipRules or virtualNetworkRules) are granted access to the storage accounts. This will restrict unauthorized access and minimize the attack surface.",N/A,AI,Generic
CRITICAL,NS-2,storage-accounts.bicep,21.0,"Setting 'networkAcls.defaultAction' to 'Allow' leaves storage accounts with public endpoints exposed to the Internet unless restricted by IP rules or VNET rules, which may not always be sufficiently narrow. ASB NS-2 requires public endpoints to be secured or disabled.","Set 'defaultAction' to 'Deny' in 'networkAcls'. Review and limit IP rules and virtual network rules to only those required. If possible, disable public endpoints entirely for these storage accounts.",N/A,AI,Generic
HIGH,NS-5,app-config.bicep,7.0,"The App Configuration resource does not configure a private endpoint, meaning management and data plane access are potentially exposed over public internet by default, violating the secure access requirement for sensitive configuration stores.",Configure a private endpoint for the App Configuration resource to restrict access solely to approved virtual networks. Add a 'privateEndpointConnections' property or deploy a 'Microsoft.Network/privateEndpoints' resource referencing the AppConfiguration resource.,N/A,AI,Generic
HIGH,NS-2,app-config.bicep,7.0,The App Configuration service is publicly accessible by default and no access restrictions or public network disablement is present. This increases risk of unauthorized access.,Disable public network access for the App Configuration resource or implement network access rules to restrict access to only approved IP ranges or virtual networks using the 'publicNetworkAccess' property set to 'Disabled'.,N/A,AI,Generic
HIGH,DP-3,app-config.bicep,19.0,"Key-values and potentially sensitive configurations may be stored inline via the 'keyValues' object, and no integration with Azure Key Vault for storing secrets or sensitive information is enforced.","Ensure that secrets, such as connection strings or keys, are stored securely in Azure Key Vault and only references (e.g., Key Vault URIs) are placed in App Configuration. Implement validation or clearly document secure usage for contributors.",N/A,AI,Generic
HIGH,NS-5,event-grid.bicep,9.0,"No implementation of Private Endpoints for Event Grid system topics or referenced storage accounts. Without private endpoints, Event Grid topics and their Storage Account sources may be exposed over the public Internet, increasing risk of unauthorized access or data exfiltration.","Integrate Private Endpoints for each storage account source referenced in 'storageFuzzId' and 'storageCorpusIds', and, if supported and required, for system topics as well. Ensure all data flows remain within the Azure backbone, without traversing the public Internet.",N/A,AI,Generic
HIGH,NS-3,event-grid.bicep,9.0,No configuration or reference for Network Security Groups (NSGs) to protect associated storage account(s) or service endpoints. Lack of NSGs may allow unwanted network access to the referenced storage accounts and function destinations.,Associate NSGs with all relevant subnets that host storage accounts or function apps. Create restrictive NSG rules to allow only required traffic from known sources.,N/A,AI,Generic
HIGH,NS-1,event-grid.bicep,9.0,File does not define any Azure Firewall or similar centralized network control for storage accounts or system topics. Critical Azure resources are potentially left exposed to wider network access.,Implement Azure Firewall or a similar network security appliance between critical storage accounts and external/public endpoints. Restrict allowed traffic to necessary internal sources only.,N/A,AI,Generic
HIGH,NS-2,event-grid.bicep,9.0,Potential exposure of public endpoints on referenced storage accounts or Event Grid topics due to lack of access restriction or IP whitelisting configuration.,Restrict storage account and Event Grid topic endpoints to only required IP ranges. Deny public Internet access unless explicitly justified and mitigated.,N/A,AI,Generic
HIGH,NS-1,function-settings.bicep,32.0,"The storage account 'funcStorage' is referenced as an existing resource, with no configuration for network access restrictions or integration with network security groups (NSGs) or Azure Firewall. Lack of explicit access controls puts the storage account at risk of unauthorized or broad public access, violating the requirement to protect storage accounts with network controls.","Ensure the storage account is configured to deny public network access and is protected with private endpoints, NSGs, or Azure Firewall. If managed outside this template, confirm that corresponding configurations exist for 'funcStorage'.",N/A,AI,Generic
HIGH,NS-2,function-settings.bicep,32.0,"No properties are set to restrict public network access on the storage account 'funcStorage'. Azure Storage accounts are public by default unless explicitly locked down, creating exposure to the internet.",Update the storage account configuration to set 'publicNetworkAccess' to 'Disabled' or ensure that only required networks/subnets have access. Use private endpoints for the storage account whenever possible.,N/A,AI,Generic
HIGH,DP-3,function-settings.bicep,58.0,"Sensitive information such as the Application Insights instrumentation key ('APPINSIGHTS_INSTRUMENTATIONKEY'), application ID, and potentially credential-like parameters are set as app settings. While 'app_insights_key' uses the @secure() attribute, settings are stored in the app configuration and may be accessible unless further restricted.","Move secrets and high-sensitivity configurations (e.g., instrumentation keys, secret URIs) into Azure Key Vault, and reference them using Key Vault references in the app settings. This ensures secrets are centrally managed and protected in accordance with best practices.",N/A,AI,Generic
HIGH,NS-1,function.bicep,69.0,"The storage account 'funcStorage' is referenced as 'existing' and no network security controls (NSGs, firewalls, or private endpoints) are applied or validated in this template. Unrestricted storage accounts could be accessible over public networks, violating the requirement to protect sensitive resources.","Ensure the referenced storage account limits access to trusted networks only (using Private Endpoints, firewall rules, and/or NSGs). Update the template or deployment process to enforce network-based access restrictions on the storage account per ASB NS-1.",N/A,AI,Generic
HIGH,DP-1,function.bicep,69.0,"The storage account 'funcStorage' is referenced as 'existing' but there is no validation or enforcement of encryption at rest. Without explicit enforcement, the storage account may not protect data with encryption as required by ASB.",Ensure that the storage account has encryption at rest enabled (with Microsoft-managed or customer-managed keys). Validate or enforce this setting out-of-band or by querying the existing resource properties in the deployment pipeline.,N/A,AI,Generic
HIGH,NS-2,function.bicep,69.0,"The storage account 'funcStorage' may expose its endpoints publicly because there are no firewall or private endpoint configurations specified, nor are any public access restrictions validated in the template.",Restrict storage account public endpoints with network rules. Enable the firewall and use private endpoints. Confirm that 'funcStorage' permits traffic only from trusted Azure networks or on-premises networks as per business requirements.,N/A,AI,Generic
HIGH,DP-3,function.bicep,109.0,"A SAS token for the storage account is constructed in the template using parameters, and a connection string with the SAS token is constructed for log storage. There are no references to Azure Key Vault or secured parameter usage for sensitive information. SAS tokens should be tightly controlled and not exposed in templates.","Store sensitive information such as SAS tokens and storage secrets in Azure Key Vault and reference them securely from your deployments. Avoid constructing or exposing such tokens directly in templates or as parameters, and rotate SAS tokens regularly.",N/A,AI,Generic
HIGH,NS-1,hub-network.bicep,1.0,"The virtual network and subnet are defined without any associated Network Security Groups (NSGs) or Azure Firewall. According to ASB Control NS-1, resources should be protected with NSGs or Azure Firewall to control and restrict network traffic.","Associate an NSG to the 'hub-subnet' and define appropriate inbound and outbound security rules to restrict access to only necessary sources and destinations. Alternately, deploy Azure Firewall for advanced network protection as required for your environment.",N/A,AI,Generic
HIGH,NS-3,hub-network.bicep,1.0,"No Network Security Groups (NSGs) are implemented or referenced for the subnets in this virtual network, violating ASB NS-3. Absence of NSGs means there are no explicit controls on allowed or denied traffic flow.","Create an NSG resource and associate it with the 'hub-subnet'. Define rules to restrict traffic according to the needs of your workload and best practices (e.g., deny all inbound except specific required ports, allow legitimate outbound as needed).",N/A,AI,Generic
HIGH,NS-1,instance-config.bicep,24.0,"The network configuration defines an address space and subnet, but there is no evidence of the deployment or association of Network Security Groups (NSGs) or Azure Firewall to protect the subnets or VMs. This leaves resources potentially open to unfiltered network traffic and violates the requirement to use NSGs or Azure Firewall for protecting sensitive resources.",Explicitly deploy and associate NSGs to the defined subnet(s) and/or VMs. Restrict both inbound and outbound traffic to only that which is required. Ensure all subnets and VM NICs have appropriate NSG rules as per the workload needs.,N/A,AI,Generic
HIGH,NS-3,instance-config.bicep,24.0,No Network Security Groups (NSGs) are defined or referenced for the subnet or resources. This violates the requirement to use NSGs to control inbound and outbound traffic at both the subnet and NIC level.,Define NSGs within the template and associate them to the subnet and/or VM NICs. Create granular security rules that explicitly permit only necessary traffic and deny all others by default.,N/A,AI,Generic
HIGH,DP-4,instance-config.bicep,0.0,"There are VMs or VM images defined, but there is no indication that managed disks are used and configured for encryption at rest.","Explicitly define VM disks as managed disks (default since Azure ARM V2) and verify that encryption is enabled, ideally with customer-managed keys (CMKs) where required.",N/A,AI,Generic
HIGH,DP-1,instance-config.bicep,0.0,"The configuration does not explicitly specify disk encryption at rest for VM disks or any data storage, which could leave data unprotected if the default settings are changed or not enabled in the deployment.",Explicitly set 'encryptionSettings' or equivalent for all managed disks and configure encryption at rest with Azure-managed or customer-managed keys for all storage resources.,N/A,AI,Generic
HIGH,DP-3,instance-config.bicep,3.0,"Sensitive values such as admin lists, tenant IDs, client IDs, and domains are pulled from parameters, but their secure storage or retrieval is not specified. Without proper protection, these secrets could be exposed in logs or accidental misconfiguration.","Do not store sensitive values directly in parameters or outputs. Use Azure Key Vault to securely store and retrieve credentials, tenant information, client IDs, and other secrets during deployment and at runtime. Reference Key Vault secrets securely within templates.",N/A,AI,Generic
HIGH,IM-2,instance-config.bicep,3.0,No controls or settings are defined to require or enforce Multi-Factor Authentication (MFA) for users or administrators listed in the 'Admins' parameter.,Configure Azure AD conditional access policies to enforce MFA for all administrator and user accounts with access to this deployment. Clearly document the requirement for MFA usage in operational procedures.,N/A,AI,Generic
HIGH,IM-3,instance-config.bicep,3.0,There are no policies or technical controls in place to enforce conditional access policies for admin or user access to the VMs or their management interfaces.,"Ensure conditional access policies are in place in Azure AD that restrict access based on risk, location, or device compliance. Reference or link conditional access requirements in your deployment documentation.",N/A,AI,Generic
HIGH,NS-2,ip-rules.bicep,3.0,"The 'corpNetIps' variable allows very broad public IP ranges (e.g., '*******/8', '********/8', etc.) which represent entire Class A/B public address spaces. Allowing such broad public IPs as trusted sources to critical resources (e.g., Storage, LogicApps) increases the potential attack surface and defeats network isolation. ASB NS-2 requires minimizing public exposure.","Restrict allowed IP addresses to the smallest possible set necessary for business operations. Replace broad ranges like '/8' or '/16' with narrowly scoped ranges or specific addresses. Where possible, use private IPs/VNET integration or trusted NAT gateways rather than wide public prefixes.",N/A,AI,Generic
HIGH,NS-3,keyvault.bicep,,There is no evidence of Network Security Groups (NSGs) being applied to subnets accessing the Key Vault. NSGs are needed to filter and restrict which resources and users can communicate with the Key Vault in accordance with least privilege principles.,Ensure that NSGs are applied to the relevant subnets (such as 'hubSubnetId') with rules restricting both inbound and outbound access to only required addresses and ports. Document or reference the NSG implementation.,N/A,AI,Generic
HIGH,DP-3,operational-insights.bicep,74.0,"The outputs 'appInsightsAppId' and 'appInsightsInstrumentationKey' expose sensitive instrumentation keys and IDs as deployment outputs. Instrumentation keys can be used to send telemetry to the Application Insights resource, potentially leading to data leakage if accessed by unauthorized parties. This contravenes the requirement to keep sensitive secrets or keys secure and not exposed in output.","Remove sensitive outputs such as instrumentation keys and AppIds from the outputs section. If these outputs are absolutely needed, restrict their exposure to authorized automation only, or preferably retrieve such values at runtime through secure mechanisms like managed identities and Azure Key Vault (ASB DP-3).",N/A,AI,Generic
HIGH,NS-1,scaleset-networks.bicep,1.0,"There are no Network Security Groups (NSGs) defined or associated with the virtual network or its subnets. This means that network-layer access controls are missing, leaving resources unprotected from unauthorized or malicious traffic. ASB NS-1 recommends protecting resources using NSGs or Azure Firewall.","Define and associate NSGs with each subnet, specifying restrictive inbound and outbound traffic rules aligning with the least privilege principle. Consider using an Azure Firewall for centralized control if appropriate.",N/A,AI,Generic
HIGH,NS-3,scaleset-networks.bicep,1.0,The template does not define or reference any Network Security Groups (NSGs) for the scaleset subnet or the virtual network. ASB NS-3 mandates using NSGs to control inbound and outbound traffic at the subnet and/or NIC level.,"Add resources for NSGs and associate them with the scaleset subnet. Create NSG rules to explicitly allow only necessary traffic (e.g., from management or application security groups), and deny all other traffic by default.",N/A,AI,Generic
HIGH,NS-1,server-farms.bicep,38.0,"The Key Vaults referenced in 'genevaCertVaultId' variables are likely used to store sensitive certificates. The template does not define any network security controls (e.g., private endpoints, firewall rules, or NSGs) to restrict access to these Key Vaults as recommended by ASB. Without such controls, the Key Vaults may be accessible from the public internet, increasing the attack surface.","Define Key Vault resources in the template with network ACLs to limit access only to required networks or private endpoints. Ensure the referenced Key Vaults are configured to block public access and allow access only from trusted subnets or private endpoints, per ASB control NS-1.",N/A,AI,Generic
HIGH,IM-6,server-farms.bicep,101.0,"The Key Vault settings for certificate retrieval ('serverFarms_AntMDS_CERTIFICATE_PFX_GENEVACERT') are specified but there is no evidence of assigning access permissions using Role-Based Access Control (RBAC). Without explicit RBAC or Access Policies, applications or users may have broader or implicit access.","Explicitly define and assign roles (e.g., Key Vault Secrets User or Key Vault Certificates Officer) to the managed identities or applications that need access. Limit access to least privilege necessary and audit assigned roles regularly.",N/A,AI,Generic
HIGH,NS-2,signalR.bicep,4.0,"The SignalR resource is deployed without explicit network access controls or restriction of public accessibility. By default, Azure SignalR creates a public endpoint that can be accessed over the internet, increasing the risk of unauthorized access or exposure.",Restrict public network access to the SignalR resource by configuring 'networkACLs' with access modes set to 'Private' or allow only specific IP ranges or subnets. Consider using Azure Private Link to make the SignalR resource accessible only from within your private network.,N/A,AI,Generic
HIGH,DP-3,storage-accounts.bicep,63.0,"The CORS configuration for blobs uses 'allowedHeaders' and 'exposedHeaders' set to '*', which may disclose sensitive information and increases risk of cross-origin attacks. ASB DP-3 requires minimizing sensitive information disclosure.",Restrict 'allowedHeaders' and 'exposedHeaders' to only the necessary headers. Avoid using wildcard '*' unless strictly needed and fully risk-assessed. Regularly review CORS rules to ensure they are as restrictive as possible.,N/A,AI,Generic
HIGH,DP-3,storage-accounts.bicep,97.0,"CORS rules for each dynamically generated corpus storage account also allow 'allowedHeaders' and 'exposedHeaders' as '*', risking excessive data exposure and cross-origin attacks, in violation of ASB DP-3.",Limit 'allowedHeaders' and 'exposedHeaders' to only those required by client applications. Periodically audit CORS settings to ensure minimum required exposure.,N/A,AI,Generic
MEDIUM,NS-1,autoscale-settings.bicep,1.0,"The autoscale settings reference resources such as server farms and storage accounts, but there are no associated network security configurations (like NSGs or firewalls) defined or enforced to protect these associated resources, per ASB NS-1.","Ensure that the underlying resources (App Service plans, storage accounts) referenced by autoscale rules are protected by appropriate Network Security Groups or Azure Firewall rules. Update the deployment to link or reference these protections for critical resources.",N/A,AI,Generic
MEDIUM,NS-6,event-grid.bicep,9.0,No reference to Virtual Network Service Endpoints for storage accounts; this may permit traffic from outside the trusted network.,"Enable Service Endpoints on subnets accessing storage accounts, and restrict storage accounts to accept traffic only from designated subnets with service endpoints enabled.",N/A,AI,Generic
MEDIUM,NS-9,event-grid.bicep,28.0,"There is no configuration for enabling diagnostics, logging, or monitoring on the Event Grid system topics or event subscriptions. Lack of logging may hamper forensic and incident response efforts.","Enable diagnostic settings on Event Grid system topics and associated storage resources. Stream logs to Log Analytics, Event Hub, or another centralized log solution.",N/A,AI,Generic
MEDIUM,DP-1,event-grid.bicep,9.0,"No explicit configuration to enforce encryption at rest for referenced storage accounts or event destination (StorageQueue). While encryption may be enabled by default, explicit configuration is recommended to enforce compliance.","Specify and enforce encryption at rest on all referenced storage accounts. Where possible, use customer-managed keys (CMK) for sensitive data.",N/A,AI,Generic
MEDIUM,DP-2,event-grid.bicep,9.0,"No explicit enforcement of encryption in transit (TLS 1.2+) for storage accounts, queues, or Event Grid endpoints. Data in transit may be vulnerable if not enforced.",Enforce minimum TLS version to 1.2+ for all storage account services and explicitly require https-only connections.,N/A,AI,Generic
MEDIUM,DP-1,function-settings.bicep,32.0,"Encryption at rest is not explicitly enabled for the storage account 'funcStorage', nor is there any specification of customer-managed keys (CMK). If the storage account's encryption is not configured, it may default to platform-managed keys, which may not meet all compliance requirements.","Explicitly check and, if necessary, set the storage account to use encryption at rest, ideally with customer-managed keys (CMK) from Azure Key Vault, depending on compliance needs. If this is managed externally, document the encryption configuration.",N/A,AI,Generic
MEDIUM,NS-4,hub-network.bicep,1.0,"No Azure Firewall or third-party firewall is deployed to provide advanced network protection as per ASB NS-4. While this may be intentional for simplicity, lacking explicit firewall protection may expose resources if NSGs are bypassed or misconfigured.",Consider provisioning Azure Firewall or a supported third-party firewall solution and integrate it with the virtual network to provide centralized governance and additional security controls over network flows.,N/A,AI,Generic
MEDIUM,NS-8,hub-network.bicep,1.0,"No indication that DDoS protection is enabled for the virtual network, per ASB NS-8. Without DDoS protection, workloads in this VNet may be vulnerable to distributed denial-of-service attacks.","Enable Azure DDoS Protection Standard on the virtual network to safeguard against volumetric, protocol, and application layer DDoS attacks.",N/A,AI,Generic
MEDIUM,NS-2,instance-config.bicep,24.0,"The template does not specify whether public endpoints (e.g., public IPs for VMs) are created or restricted. Without explicit restriction, there is a risk of unintentional public exposure of VMs or resources.","Ensure VM NICs and load balancers do not attach public IPs unless absolutely necessary. When required, use whitelisted IP ranges and service endpoints. Otherwise, enforce that VMs are only accessible through private endpoints or secure jump hosts.",N/A,AI,Generic
MEDIUM,NS-7,instance-config.bicep,0.0,"There is no evidence that Just-In-Time (JIT) VM access is enabled or configured for the deployed VMs. Without JIT, management ports (SSH/RDP) may be exposed for longer durations than necessary.",Enable JIT VM access on all management ports of deployed VMs by integrating with Azure Security Center configuration post-deployment or via ARM/Bicep extensions where supported.,N/A,AI,Generic
MEDIUM,NS-10,instance-config.bicep,0.0,"No Azure Bastion host or equivalent is defined for secure VM access. Without Bastion, administrators may be forced to use less secure access mechanisms (e.g., direct RDP/SSH).",Deploy an Azure Bastion host in the subnet and ensure VM management is routed exclusively through Bastion for administrative access. Remove direct public access to SSH/RDP.,N/A,AI,Generic
MEDIUM,IM-1,instance-config.bicep,3.0,"Although Azure AD tenant and client IDs are passed and referenced, the template does not explicitly configure the deployed VMs or applications to use Azure AD for authentication and authorization. This could lead to inconsistent or insecure access controls.",Integrate all VM and application authentication/authorization flows with Azure AD. Use managed identities where possible and require Azure AD accounts for all administrator and application access.,N/A,AI,Generic
MEDIUM,NS-5,ip-rules.bicep,3.0,"The IP whitelist approach in 'corpNetIps', 'sawVnetIps', and 'ingestionServiceIps' implies public IP-based access control rather than leveraging Private Endpoints. ASB NS-5 recommends using Private Endpoints for secure and private access to Azure resources, minimizing the need to expose public IPs at all.","Use Azure Private Endpoints for all supported resources (e.g., Storage, LogicApps), and remove public IP address whitelisting from resource firewall/network rules where possible.",N/A,AI,Generic
MEDIUM,NS-6,ip-rules.bicep,22.0,"No virtual network service endpoints are defined or referenced. ASB NS-6 recommends the use of service endpoints to restrict access to Azure resources only from trusted Azure VNets, not from public address ranges.",Implement virtual network service endpoints on the resources being protected to ensure only traffic from trusted VNets is allowed. Avoid relying only on public IP allow-listing.,N/A,AI,Generic
MEDIUM,NS-1,ip-rules.bicep,3.0,"The file defines IP allow rules, but there is no evidence of enforcement using network security groups (NSGs) or Azure Firewall. ASB NS-1 requires the use of NSGs or Azure Firewall to restrict and monitor access to critical resources.","Apply these rules using an NSG or Azure Firewall resource in the deployment, ensuring that only explicitly allowed and necessary traffic is permitted. Document how these IP ranges are enforced at the network layer.",N/A,AI,Generic
MEDIUM,NS-3,ip-rules.bicep,3.0,There is no indication that NSGs are used to enforce these IP restriction rules. ASB NS-3 expects the use of NSGs to control inbound and outbound traffic.,"Attach NSGs with appropriate rules to the relevant subnet or network interfaces, ensuring that only required inbound and outbound traffic as per business justification is allowed.",N/A,AI,Generic
MEDIUM,DP-6,keyvault.bicep,17.0,"The Key Vault deployment does not specify the use of customer-managed keys (CMK) for encrypting data at rest, potentially limiting control over encryption key lifecycle and compliance requirements for sensitive environments.","Enable and configure customer-managed keys (CMK) for the Key Vault by creating a Key Vault key and assigning it as the encryption key for Key Vault, if compliance or data governance necessitates.",N/A,AI,Generic
MEDIUM,DP-3,keyvault.bicep,36.0,"Secrets are passed as plain values in the 'secrets' object and could, depending on deployment process, be exposed in deployment logs or state files. Inline or non-protected secret values may lead to accidental disclosure.",Use secure parameters and avoid inline secret values in your templates. Pass secrets via secure mechanisms (such as Azure Key Vault references or deployment pipeline secrets) and ensure all secrets are injected at runtime and not hardcoded or exposed in code.,N/A,AI,Generic
MEDIUM,IM-6,keyvault.bicep,31.0,"Key Vault is deployed with 'enableRbacAuthorization' set to true, but no Azure RBAC role assignments are defined in the template. Without explicit least-privilege role assignments, vault access could either default to too permissive rights or be misconfigured later.","Define and assign appropriate Azure RBAC roles (e.g., Key Vault Reader, Secrets User) to managed identities, users, or service principals that require access. Ensure only the minimum set of privileges necessary for operational requirements are granted.",N/A,AI,Generic
MEDIUM,NS-2,scaleset-networks.bicep,11.0,"A public IPv4 address is created and associated with the NAT Gateway for outbound traffic, but there are no corresponding controls (such as NSGs or firewalls) mentioned to restrict or monitor access from the public network. ASB NS-2 recommends securing all public endpoints to minimize exposure.",Ensure that NSGs are in place to restrict outbound and inbound access to only required ports and IPs. Consider Azure Firewall or restricting NAT Gateway outbound access to approved destinations only.,N/A,AI,Generic
MEDIUM,NS-9,scaleset-networks.bicep,1.0,There is no evidence of enabling network traffic monitoring via Azure Monitor or Network Watcher within the template. ASB NS-9 recommends monitoring and logging network traffic for auditing and threat detection.,"Deploy and configure Azure Network Watcher and Diagnostic Settings for relevant network resources (such as public IPs, NAT Gateway, vNET, and subnets) to collect and analyze traffic logs and metrics.",N/A,AI,Generic
MEDIUM,DP-3,server-farms.bicep,101.0,"Sensitive certificate secrets are referenced via Key Vault, which is appropriate. However, the deployment does not ensure that the Key Vault resource enforcing secret storage exists or is securely configured within this scope, risking unintended data exposure if the referenced Key Vaults are not compliant.","Ensure that the referenced Key Vaults exist and are securely configured. Consider provisioning and configuring Key Vaults in this (or a prerequisite) template with secure access, logging, and monitoring.",N/A,AI,Generic
MEDIUM,DP-1,server-farms.bicep,101.0,"The template references secret storage in Key Vault but does not show explicit configuration for encryption at rest for the Key Vaults used. While Azure Key Vault encrypts by default, explicit configuration is necessary to comply with strict requirements and prove compliance.","Document and, if possible, explicitly configure Key Vaults for encryption at rest with required compliance-level keys (e.g., specify use of HSM-backed keys or CMK).",N/A,AI,Generic
MEDIUM,NS-1,signalR.bicep,4.0,"No Network Security Groups (NSG) or Azure Firewall controls are referenced or associated with the SignalR resource. While SignalR is a managed service, network access can still be limited by using Private Link or service endpoints.","Deploy the SignalR resource with Private Endpoint enabled, reducing reliance on public internet exposure and enforce NSG controls on the virtual network. Document compensating controls or mitigations if NSGs or Azure Firewall are not directly applicable.",N/A,AI,Generic
MEDIUM,DP-1,signalR.bicep,4.0,"The configuration does not specify encryption at rest settings or customer-managed keys (CMK) for the SignalR resource. While Azure manages encryption by default, best practice is to enable customer-managed keys for greater control.",Enable encryption at rest with customer-managed keys for the SignalR resource by specifying 'encryption' property with a Key Vault reference in the resource definition.,N/A,AI,Generic
MEDIUM,NS-3,storage-accounts.bicep,21.0,"'networkAcls' are defined, but there is no evidence of associated Network Security Groups (NSGs) protecting the underlying storage subnet, as required by ASB NS-3. Relying solely on storage firewall increases risk if VNET/network configuration changes.",Apply Network Security Groups (NSGs) to the 'hubSubnetId' or relevant subnet to enforce additional network-layer inbound traffic restrictions for storage account traffic.,N/A,AI,Generic
MEDIUM,DP-1,storage-accounts.bicep,16.0,"There is no explicit configuration in the storage account resources to enable customer-managed keys (CMK) for encryption at rest. While Azure storage accounts provide Microsoft-managed key encryption by default, ASB DP-1 recommends using CMK for sensitive or critical workloads.","Configure storage accounts to use Azure Key Vault customer-managed keys (CMK) for encryption at rest, if sensitive data is stored, by adding the 'encryption.keySource' and 'encryption.keyVaultProperties' properties.",N/A,AI,Generic
MEDIUM,IM-1,storage-accounts.bicep,1.0,"No Azure AD-based authentication (e.g., Azure AD DS integration, Azure RBAC, managed identities) is configured for storage accounts, relying solely on Shared Key and SAS tokens. ASB IM-1 recommends using Azure AD for authentication/authorization.","Configure storage accounts to require Azure AD authentication for data plane access. Assign appropriate RBAC roles (such as 'Storage Blob Data Reader/Contributor') to users and applications. Set 'isLocalUserEnabled' to 'false' (already set) and disable Shared Key access (already set), but also enforce Azure AD auth.",N/A,AI,Generic
LOW,DP-3,autoscale-settings.bicep,1.0,"Parameters 'server_farm_id', 'func_storage_account_id', and 'workspaceId' appear to reference potentially sensitive resources, but there is no evidence of secure referencing such as using Key Vault integration for secrets or sensitive identifiers. ASB DP-3 recommends using Key Vault for storing such information.","Where possible, use Azure Key Vault reference for sensitive parameter values or connection strings. Review parameter passing to ensure no secrets or sensitive data are stored in plaintext or configuration files.",N/A,AI,Generic
LOW,AM-1,autoscale-settings.bicep,1.0,"No role assignments or RBAC configurations are specified, and there is no indication of least-privilege access for managing autoscale settings or diagnostic configurations. This could result in excessive privileges if managed outside IaC.","Explicitly define least-privilege RBAC assignments (e.g., using Microsoft.Authorization/roleAssignments) within the template, or ensure that only minimal required permissions are granted to principals that can modify these resources.",N/A,AI,Generic
LOW,NS-9,hub-network.bicep,1.0,"There is no evidence of configuring network monitoring or logging via Azure Monitor or Network Watcher, as required by ASB NS-9. This limits visibility into network activities and troubleshooting.","Enable and configure Azure Network Watcher for the subscription and region. Set up diagnostic logging (flow logs, NSG events, etc.) to monitor and analyze network traffic within the virtual network.",N/A,AI,Generic
LOW,NS-4,ip-rules.bicep,3.0,"There is no reference to the use of Azure Firewall or a third-party firewall. For critical resource protection, ASB NS-4 recommends using such firewalls for advanced threat protection and centralized policy management.",Deploy Azure Firewall or an approved third-party firewall if the resources protected by these IP rules are considered sensitive or critical.,N/A,AI,Generic
LOW,NS-8,ip-rules.bicep,3.0,The template does not include any settings or resource declarations for Azure DDoS Protection. ASB NS-8 recommends enabling DDoS Protection for resources exposed to the Internet.,Add Azure DDoS Protection Standard at the virtual network level for Internet-exposed workloads.,N/A,AI,Generic
LOW,NS-9,ip-rules.bicep,3.0,"There is no mention of network monitoring, traffic analytics, or diagnostic logging. NS-9 requires logging and monitoring of network traffic for detection and audit.","Integrate network logging using Azure Network Watcher, enable NSG flow logs, and ensure diagnostic logs are sent to a Log Analytics Workspace or SIEM.",N/A,AI,Generic
LOW,NS-7,ip-rules.bicep,3.0,"No Just-in-Time (JIT) VM access controls are in place. If any of these IP rules are for management ports to VMs, this risks overexposure. ASB NS-7 recommends restricting direct management access using JIT access.","For VM management, enable JIT VM access in Defender for Cloud, and configure NSGs to only allow management access when JIT is active.",N/A,AI,Generic
LOW,NS-8,scaleset-networks.bicep,1.0,The template does not specify enabling Azure DDoS Protection for the virtual network. This may leave resources exposed to volumetric attacks. ASB NS-8 recommends enabling DDoS Protection for critical workloads.,Add a resource to enable Azure DDoS Protection on the virtual network if it supports critical services or public exposure.,N/A,AI,Generic
LOW,NS-6,scaleset-networks.bicep,35.0,"Service endpoints are defined as an empty array for the scaleset subnet, implying no service endpoints are used for secure access to Azure PaaS services. ASB NS-6 recommends using service endpoints to restrict Azure resource access to specific networks.","If the workload will access Azure platform services (e.g., Storage, SQL), define service endpoints in the subnet configuration for those services to enforce private, secure access.",N/A,AI,Generic
LOW,NS-5,scaleset-networks.bicep,37.0,"'privateEndpointNetworkPolicies' and 'privateLinkServiceNetworkPolicies' are both set to 'Enabled', but no private endpoint resources are present. ASB NS-5 recommends using Private Endpoints to secure access to Azure services, which requires disabling some network policies as needed.","If service access requires it, deploy Azure Private Endpoints and adjust network policy settings accordingly to fully leverage private connectivity. Otherwise, review if policies should be set to 'Disabled' for future private endpoint integration.",N/A,AI,Generic
LOW,DP-6,server-farms.bicep,101.0,"There is no indication that Customer-Managed Keys (CMKs) are utilized for the Key Vaults containing sensitive certificates. Without CMK, control over encryption keys and data residency may not be sufficient for highly sensitive workloads.","Configure the Key Vaults to use customer-managed keys for controlling encryption at rest for certificates and secrets stored in Key Vault, especially for confidential or regulated data.",N/A,AI,Generic
LOW,DP-2,server-farms.bicep,101.0,There is no explicit enforcement of using TLS 1.2 or higher for accessing Key Vaults. Depending on default settings may not satisfy compliance needs or future best practices.,Explicitly configure Key Vaults to require TLS 1.2 (or higher) through deployment scripts or Azure Policy. Update all consuming applications and services to use TLS 1.2+.,N/A,AI,Generic
