Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
CRITICAL,DP-1,IngestionStorageAccount.Template.json,36,Storage account resource does not explicitly enable encryption at rest. No 'encryption' property is set in the resource definition.,"Add the 'encryption' property to the storage account resource with 'services', 'keySource', and 'enabled' set to ensure encryption at rest is enabled. Example: ""encryption"": { ""services"": { ""blob"": { ""enabled"": true } }, ""keySource"": ""Microsoft.Storage"" }",N/A,AI,Generic
CRITICAL,DP-1,IngestionStorageAccount.Template.json,56,Storage account resource does not explicitly enable encryption at rest. No 'encryption' property is set in the resource definition.,"Add the 'encryption' property to the storage account resource with 'services', 'keySource', and 'enabled' set to ensure encryption at rest is enabled. Example: ""encryption"": { ""services"": { ""blob"": { ""enabled"": true } }, ""keySource"": ""Microsoft.Storage"" }",N/A,AI,Generic
CRITICAL,NS-1,IngestionStorageAccount.Template.json,36,Storage account resource does not restrict network access using network security groups (NSGs) or Azure Firewall. No 'networkAcls' property is defined.,"Add the 'networkAcls' property to the storage account resource to restrict access to trusted networks and subnets. Example: ""networkAcls"": { ""defaultAction"": ""Deny"", ""bypass"": ""AzureServices"", ""virtualNetworkRules"": [], ""ipRules"": [] }",N/A,AI,Generic
CRITICAL,NS-1,IngestionStorageAccount.Template.json,56,Storage account resource does not restrict network access using network security groups (NSGs) or Azure Firewall. No 'networkAcls' property is defined.,"Add the 'networkAcls' property to the storage account resource to restrict access to trusted networks and subnets. Example: ""networkAcls"": { ""defaultAction"": ""Deny"", ""bypass"": ""AzureServices"", ""virtualNetworkRules"": [], ""ipRules"": [] }",N/A,AI,Generic
CRITICAL,NS-2,IngestionStorageAccount.Template.json,36,"Storage account resource does not restrict public endpoints. No 'networkAcls' property is defined, so the storage account may be accessible from the public internet.",Configure the 'networkAcls' property to restrict public network access. Set 'defaultAction' to 'Deny' and allow only required IPs or subnets.,N/A,AI,Generic
CRITICAL,NS-2,IngestionStorageAccount.Template.json,56,"Storage account resource does not restrict public endpoints. No 'networkAcls' property is defined, so the storage account may be accessible from the public internet.",Configure the 'networkAcls' property to restrict public network access. Set 'defaultAction' to 'Deny' and allow only required IPs or subnets.,N/A,AI,Generic
CRITICAL,NS-1,LacpBilling.Template.json,3932,Microsoft.Storage/storageAccounts resource at line 3932 does not specify network rules or networkAcls to restrict access. Storage accounts should be protected using network security groups (NSGs) or Azure Firewall to prevent unauthorized access.,Add 'networkAcls' property to the storage account resource to restrict access to trusted networks and subnets. Configure defaultAction to 'Deny' and specify allowed virtual networks and IP addresses.,N/A,AI,Generic
CRITICAL,NS-2,LacpBilling.Template.json,3932,"Microsoft.Storage/storageAccounts resource at line 3932 does not restrict public network access. Public endpoints are not explicitly disabled, increasing the risk of exposure.",Set 'publicNetworkAccess' property to 'Disabled' in the storage account resource to prevent public endpoint exposure.,N/A,AI,Generic
CRITICAL,NS-1,LacpBillingExhaust.Template.json,33,Microsoft.Kusto/clusters resource at line 33 does not specify any network security group (NSG) or Azure Firewall configuration to restrict network access. This exposes the resource to potential unauthorized access.,Configure the Microsoft.Kusto cluster to use private endpoints and/or associate it with a subnet protected by a Network Security Group (NSG) or Azure Firewall. Restrict inbound and outbound traffic to only required sources and destinations.,N/A,AI,Generic
CRITICAL,NS-2,LacpBillingExhaust.Template.json,33,"Microsoft.Kusto/clusters resource at line 33 does not restrict public endpoints. By default, the cluster may be accessible from the public internet, increasing exposure risk.",Disable public network access for the Microsoft.Kusto cluster by setting 'publicNetworkAccess' to 'Disabled' and use private endpoints to restrict access to internal networks only.,N/A,AI,Generic
CRITICAL,NS-3,LacpBillingExhaust.Template.json,33,"Microsoft.Kusto/clusters resource at line 33 is not associated with any Network Security Group (NSG), which is required to control inbound and outbound traffic.",Deploy the Microsoft.Kusto cluster within a subnet that is protected by a Network Security Group (NSG) with rules that restrict access to only necessary IP ranges and ports.,N/A,AI,Generic
CRITICAL,DP-3,LacpBillingExhaustExport.Template.json,61,"Sensitive connection information (adxExhaustUri, adxExhaustDataIngestionUri) is provided directly as parameters and referenced in the adxParameters block without explicit integration with Azure Key Vault or secure parameter handling. This violates DP-3: Manage sensitive information disclosure.",Store sensitive connection strings and URIs in Azure Key Vault and reference them securely in the template using Key Vault references. Update the template to retrieve these values from Key Vault instead of direct parameter input.,N/A,AI,Generic
CRITICAL,NS-1,LacpGeo.Template.json,97,"Microsoft.KeyVault/vaults resource does not restrict network access using network security groups (NSGs) or Azure Firewall. No network rules are defined, leaving the Key Vault potentially exposed.","Configure the Key Vault to use private endpoints or set network ACLs to restrict access. Add 'networkAcls' property with appropriate 'bypass', 'defaultAction', and 'ipRules' or 'virtualNetworkRules' to limit access.",N/A,AI,Generic
CRITICAL,NS-2,LacpGeo.Template.json,153,"Microsoft.DocumentDB/databaseAccounts resource has 'publicNetworkAccess' set to 'Enabled', exposing the Cosmos DB account to the public internet.",Set 'publicNetworkAccess' to 'Disabled' and use private endpoints to restrict access to the Cosmos DB account.,N/A,AI,Generic
CRITICAL,NS-1,LacpGeo.Template.json,153,Microsoft.DocumentDB/databaseAccounts resource does not restrict network access using network security groups (NSGs) or Azure Firewall. 'isVirtualNetworkFilterEnabled' is set to false and 'virtualNetworkRules' is empty.,"Enable 'isVirtualNetworkFilterEnabled' and define 'virtualNetworkRules' to restrict access to trusted subnets. Alternatively, use private endpoints for secure access.",N/A,AI,Generic
CRITICAL,NS-1,LacpGlobal.Template.json,54,"CosmosDB account 'publicNetworkAccess' is set to 'Enabled' and 'isVirtualNetworkFilterEnabled' is false at line 54, exposing the resource to the public internet without network security groups or Azure Firewall.",Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to true. Restrict access using virtual network rules or integrate with Azure Firewall.,N/A,AI,Generic
CRITICAL,NS-2,LacpGlobal.Template.json,54,"CosmosDB account at line 54 has 'publicNetworkAccess' set to 'Enabled', exposing a public endpoint.","Set 'publicNetworkAccess' to 'Disabled' to remove the public endpoint, or restrict access using IP rules and virtual network filters.",N/A,AI,Generic
CRITICAL,NS-3,LacpGlobal.Template.json,54,CosmosDB account at line 54 does not implement Network Security Groups (NSGs) or equivalent network controls.,Enable virtual network filtering and associate the CosmosDB account with a subnet protected by NSGs.,N/A,AI,Generic
CRITICAL,NS-1,LacpGlobal.Template.json,163,Key Vault at line 163 does not restrict network access using network security groups or Azure Firewall; no network ACLs are defined.,Configure Key Vault network ACLs to allow access only from trusted virtual networks and subnets protected by NSGs or Azure Firewall.,N/A,AI,Generic
CRITICAL,NS-2,LacpGlobal.Template.json,163,"Key Vault at line 163 is accessible from all networks by default, exposing a public endpoint.",Restrict Key Vault access by setting network ACLs to allow only trusted networks or use private endpoints.,N/A,AI,Generic
CRITICAL,NS-3,LacpGlobal.Template.json,163,Key Vault at line 163 does not implement Network Security Groups (NSGs) or equivalent network controls.,Restrict Key Vault access to subnets protected by NSGs and configure network ACLs accordingly.,N/A,AI,Generic
CRITICAL,NS-1,LacpGlobal.Template.json,120,Storage Account at line 120 does not restrict network access using network security groups or Azure Firewall; no network ACLs are defined.,Configure Storage Account network rules to allow access only from trusted virtual networks and subnets protected by NSGs or Azure Firewall.,N/A,AI,Generic
CRITICAL,NS-2,LacpGlobal.Template.json,120,"Storage Account at line 120 is accessible from all networks by default, exposing a public endpoint.",Restrict Storage Account access by setting network rules to allow only trusted networks or use private endpoints.,N/A,AI,Generic
CRITICAL,NS-3,LacpGlobal.Template.json,120,Storage Account at line 120 does not implement Network Security Groups (NSGs) or equivalent network controls.,Restrict Storage Account access to subnets protected by NSGs and configure network rules accordingly.,N/A,AI,Generic
CRITICAL,NS-1,LacpRegion.Template.json,1002,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1002 has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false, exposing the database to the public internet without network security group or firewall protection.","Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to true. Add appropriate 'virtualNetworkRules' to restrict access to trusted networks only, in accordance with ASB NS-1.",N/A,AI,Generic
CRITICAL,NS-20,LacpRegion.Template.json,1002,CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1002 exposes a public endpoint by setting 'publicNetworkAccess' to 'Enabled' and not enabling virtual network filtering.,"Set 'publicNetworkAccess' to 'Disabled' and enable 'isVirtualNetworkFilterEnabled'. Use private endpoints to restrict access to the CosmosDB account, as required by ASB NS-20.",N/A,AI,Generic
CRITICAL,DP-3,LacpStamp.Parameters-LacpStampResources.json,74,"Parameter 'dasStorageAccountKey' at line 74 references a storage account key directly in the template. Sensitive information such as storage account keys must be stored in Azure Key Vault, not in template parameters or outputs.",Store the storage account key in Azure Key Vault and reference it securely using a Key Vault reference in the template. Remove direct exposure of sensitive keys from parameters.,N/A,AI,Generic
CRITICAL,NS-18,LacpStamp.Template.json,1002,"Microsoft.Storage/storageAccounts resource at line 1002 does not specify network security groups (NSGs), firewall rules, or private endpoints. This exposes the storage account to potential public network access, violating ASB NS-1 and NS-18.","Restrict network access to the storage account by configuring network rules to allow only trusted subnets, enable private endpoints, or associate the storage account with a virtual network and NSG.",N/A,AI,Generic
CRITICAL,NS-18,LacpStamp.Template.json,1027,"Microsoft.Storage/storageAccounts resource at line 1027 does not specify network security groups (NSGs), firewall rules, or private endpoints. This exposes the storage account to potential public network access, violating ASB NS-1 and NS-18.","Restrict network access to the storage account by configuring network rules to allow only trusted subnets, enable private endpoints, or associate the storage account with a virtual network and NSG.",N/A,AI,Generic
CRITICAL,NS-18,LacpStamp.Template.json,1052,"Microsoft.Storage/storageAccounts resource at line 1052 does not specify network security groups (NSGs), firewall rules, or private endpoints. This exposes the storage account to potential public network access, violating ASB NS-1 and NS-18.","Restrict network access to the storage account by configuring network rules to allow only trusted subnets, enable private endpoints, or associate the storage account with a virtual network and NSG.",N/A,AI,Generic
CRITICAL,NS-20,LacpStamp.Template.json,1002,"Microsoft.Storage/storageAccounts resource at line 1002 does not restrict public network access. Public endpoints are not explicitly disabled, violating ASB NS-20.",Set 'networkAcls.defaultAction' to 'Deny' and configure 'bypass' as needed. Use private endpoints to eliminate public access.,N/A,AI,Generic
CRITICAL,NS-20,LacpStamp.Template.json,1027,"Microsoft.Storage/storageAccounts resource at line 1027 does not restrict public network access. Public endpoints are not explicitly disabled, violating ASB NS-20.",Set 'networkAcls.defaultAction' to 'Deny' and configure 'bypass' as needed. Use private endpoints to eliminate public access.,N/A,AI,Generic
CRITICAL,NS-20,LacpStamp.Template.json,1052,"Microsoft.Storage/storageAccounts resource at line 1052 does not restrict public network access. Public endpoints are not explicitly disabled, violating ASB NS-20.",Set 'networkAcls.defaultAction' to 'Deny' and configure 'bypass' as needed. Use private endpoints to eliminate public access.,N/A,AI,Generic
CRITICAL,NS-21,LacpStamp.Template.json,1002,"Microsoft.Storage/storageAccounts resource at line 1002 does not implement Network Security Groups (NSGs) to control inbound and outbound traffic, violating ASB NS-3 and NS-21.",Associate the storage account with a virtual network and apply NSGs to restrict traffic as per least privilege principle.,N/A,AI,Generic
CRITICAL,NS-21,LacpStamp.Template.json,1027,"Microsoft.Storage/storageAccounts resource at line 1027 does not implement Network Security Groups (NSGs) to control inbound and outbound traffic, violating ASB NS-3 and NS-21.",Associate the storage account with a virtual network and apply NSGs to restrict traffic as per least privilege principle.,N/A,AI,Generic
CRITICAL,NS-21,LacpStamp.Template.json,1052,"Microsoft.Storage/storageAccounts resource at line 1052 does not implement Network Security Groups (NSGs) to control inbound and outbound traffic, violating ASB NS-3 and NS-21.",Associate the storage account with a virtual network and apply NSGs to restrict traffic as per least privilege principle.,N/A,AI,Generic
CRITICAL,NS-1,ReadAdxExhaust.Template.json,23,"Microsoft.Kusto/clusters resource does not specify any network security groups (NSGs) or Azure Firewall configuration, leaving the resource potentially exposed to unfiltered network access.",Configure the Kusto cluster to use private endpoints or associate it with a virtual network protected by NSGs or Azure Firewall. Update the resource definition to include network isolation settings.,N/A,AI,Generic
CRITICAL,NS-2,ReadAdxExhaust.Template.json,23,"Microsoft.Kusto/clusters resource does not restrict public endpoints, which may expose the cluster to the public internet.",Disable public network access for the Kusto cluster by setting 'publicNetworkAccess' to 'Disabled' or configure private endpoints. Update the resource properties accordingly.,N/A,AI,Generic
CRITICAL,NS-3,ReadAdxExhaust.Template.json,23,"Microsoft.Kusto/clusters resource is not associated with any Network Security Groups (NSGs), which are required to control inbound and outbound traffic.","Associate the Kusto cluster with a subnet that has an NSG applied, or explicitly define NSG rules to restrict access. Update the deployment to ensure NSG protection.",N/A,AI,Generic
CRITICAL,DP-1,ReadAdxExhaust.Template.json,23,Microsoft.Kusto/clusters resource does not explicitly enable encryption at rest or specify customer-managed keys.,"Enable encryption at rest for the Kusto cluster and, if required, configure customer-managed keys by adding the 'keyVaultProperties' property with a valid Key Vault URI.",N/A,AI,Generic
CRITICAL,DP-2,ReadAdxExhaust.Template.json,23,Microsoft.Kusto/clusters resource does not explicitly enforce encryption in transit (TLS 1.2+).,"Configure the Kusto cluster to require TLS 1.2 or higher for all data in transit by setting the 'enablePurge' and 'enableDoubleEncryption' properties as appropriate, and ensure all endpoints enforce TLS 1.2+.",N/A,AI,Generic
CRITICAL,DP-3,ReadAdxExhaust.Template.json,23,"Sensitive information such as cluster URIs is output in plain text via the 'outputs' section, which may lead to information disclosure.",Restrict outputs containing sensitive information or ensure outputs are only accessible to authorized users. Remove or secure the 'adxExhaustUri' and 'adxExhaustDataIngestionUri' outputs.,N/A,AI,Generic
CRITICAL,DP-1,ReadUsageAccount.Template.json,20,"The Microsoft.UsageBilling/accounts resource does not specify encryption at rest settings, violating the requirement to enable encryption at rest for all data storage (DP-1).","Configure the Microsoft.UsageBilling/accounts resource to enable encryption at rest, specifying encryption settings or using customer-managed keys if supported.",N/A,AI,Generic
CRITICAL,DP-2,ReadUsageAccount.Template.json,20,"The template does not specify encryption in transit (TLS 1.2+) for the Microsoft.UsageBilling/accounts resource, violating the requirement to secure all data transfers (DP-2).",Ensure the Microsoft.UsageBilling/accounts resource enforces TLS 1.2 or higher for all data in transit by configuring the appropriate settings.,N/A,AI,Generic
CRITICAL,DP-3,ReadUsageAccount.Template.json,20,"Sensitive information such as keys or secrets are not referenced or stored in Azure Key Vault, violating the requirement to manage sensitive information disclosure (DP-3).","Store any sensitive data, keys, or secrets required by the Microsoft.UsageBilling/accounts resource in Azure Key Vault and reference them securely in the template.",N/A,AI,Generic
CRITICAL,NS-1,ReadUsageAccount.Template.json,20,"The Microsoft.UsageBilling/accounts resource is not protected by any network security group (NSG) or Azure Firewall, violating the requirement to protect sensitive resources (NS-1).",Associate the Microsoft.UsageBilling/accounts resource with a network security group or protect it using Azure Firewall to restrict network access.,N/A,AI,Generic
CRITICAL,NS-2,ReadUsageAccount.Template.json,20,"The template does not restrict public endpoints for the Microsoft.UsageBilling/accounts resource, increasing exposure risk (NS-2).",Restrict or disable public endpoints for the Microsoft.UsageBilling/accounts resource and use private endpoints where possible.,N/A,AI,Generic
CRITICAL,NS-3,ReadUsageAccount.Template.json,20,No Network Security Groups (NSGs) are implemented to control inbound and outbound traffic for the Microsoft.UsageBilling/accounts resource (NS-3).,Implement NSGs to control network traffic to and from the Microsoft.UsageBilling/accounts resource.,N/A,AI,Generic
CRITICAL,NS-10,TrafficManagerEndpoints.Template.json,38,"The resource 'Microsoft.Network/trafficManagerProfiles/externalEndpoints' defines a public endpoint without explicit security controls to restrict access, violating ASB NS-10: Protect public endpoints.","Restrict public access to the Traffic Manager external endpoint by implementing IP whitelisting, authentication, or moving to private endpoints where possible. Review and secure the 'target' property to minimize exposure.",N/A,AI,Generic
HIGH,NS-5,LacpGeo.Template.json,153,Microsoft.DocumentDB/databaseAccounts resource does not use private endpoints. 'publicNetworkAccess' is enabled and no private endpoint configuration is present.,"Implement private endpoints for the Cosmos DB account to ensure secure, private connectivity from within your virtual network.",N/A,AI,Generic
HIGH,NS-5,LacpGlobal.Template.json,54,CosmosDB account at line 54 does not use private endpoints for secure access.,Configure a private endpoint for the CosmosDB account to restrict access to private network traffic only.,N/A,AI,Generic
HIGH,NS-5,LacpGlobal.Template.json,163,Key Vault at line 163 does not use a private endpoint for secure access.,Configure a private endpoint for the Key Vault to restrict access to private network traffic only.,N/A,AI,Generic
HIGH,NS-5,LacpGlobal.Template.json,120,Storage Account at line 120 does not use a private endpoint for secure access.,Configure a private endpoint for the Storage Account to restrict access to private network traffic only.,N/A,AI,Generic
HIGH,NS-22,LacpRegion.Template.json,1002,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1002 does not use private endpoints, increasing the risk of unauthorized access.","Configure a private endpoint for the CosmosDB account and update client applications to use the private endpoint DNS name, as recommended by ASB NS-22.",N/A,AI,Generic
HIGH,NS-23,LacpStamp.Template.json,1002,Microsoft.Storage/storageAccounts resource at line 1002 does not implement private endpoints. This weakens network security posture as recommended by ASB NS-5 and NS-23.,Configure a private endpoint for the storage account to restrict access to resources within your virtual network.,N/A,AI,Generic
HIGH,NS-23,LacpStamp.Template.json,1027,Microsoft.Storage/storageAccounts resource at line 1027 does not implement private endpoints. This weakens network security posture as recommended by ASB NS-5 and NS-23.,Configure a private endpoint for the storage account to restrict access to resources within your virtual network.,N/A,AI,Generic
HIGH,NS-23,LacpStamp.Template.json,1052,Microsoft.Storage/storageAccounts resource at line 1052 does not implement private endpoints. This weakens network security posture as recommended by ASB NS-5 and NS-23.,Configure a private endpoint for the storage account to restrict access to resources within your virtual network.,N/A,AI,Generic
HIGH,AM-1,ReadUsageAccount.Template.json,20,"The Microsoft.UsageBilling/accounts resource does not specify any access control or RBAC assignments, which may result in excessive permissions or lack of least privilege enforcement (AM-1).",Explicitly define role assignments with least privilege access for the Microsoft.UsageBilling/accounts resource using Azure RBAC in the template.,N/A,AI,Generic
HIGH,IM-1,ReadUsageAccount.Template.json,20,"The template does not configure Azure Active Directory integration for the Microsoft.UsageBilling/accounts resource, violating the requirement to use Azure AD for identity management (IM-1).",Integrate the Microsoft.UsageBilling/accounts resource with Azure Active Directory for secure identity and access management.,N/A,AI,Generic
HIGH,IM-2,ReadUsageAccount.Template.json,20,The template does not enforce Multi-Factor Authentication (MFA) for users or administrators accessing the Microsoft.UsageBilling/accounts resource (IM-2).,Enforce MFA for all users and administrators accessing the Microsoft.UsageBilling/accounts resource by configuring Azure AD conditional access policies.,N/A,AI,Generic
HIGH,AM-1,RoleAssignment.Template.json,49,"Role assignment at line 49 grants 'Contributor' role to the 'Ev2BuildoutServicePrincipalId' principal. The 'Contributor' role provides broad permissions, which may exceed least privilege requirements.","Review the permissions required by 'Ev2BuildoutServicePrincipalId' and assign a custom role or a built-in role with only the necessary permissions, following the principle of least privilege as per ASB AM-1.",N/A,AI,Generic
MEDIUM,IM-3,ReadUsageAccount.Template.json,20,"Conditional access policies are not defined for the Microsoft.UsageBilling/accounts resource, reducing secure access enforcement (IM-3).",Implement Azure AD conditional access policies to enforce secure access to the Microsoft.UsageBilling/accounts resource.,N/A,AI,Generic
MEDIUM,IM-8,ReadUsageAccount.Template.json,20,"The template does not explicitly configure a managed identity for the Microsoft.UsageBilling/accounts resource, which is recommended for secure resource-to-resource authentication (IM-8).",Enable and configure a managed identity (system-assigned or user-assigned) for the Microsoft.UsageBilling/accounts resource in the template.,N/A,AI,Generic
