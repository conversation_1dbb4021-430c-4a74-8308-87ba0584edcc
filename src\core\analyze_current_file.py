#!/usr/bin/env python3
"""
Analyze the current file for security issues.
Usage: python analyze_current_file.py <file_path>
"""

import sys
import os
import tempfile
import json
from pathlib import Path

# Add current directory to path
sys.path.append('.')

def analyze_file(file_path):
    """Analyze a single file for security issues."""
    try:
        from security_opt import SecurityPRReviewer
        
        # Set environment variables for optimal analysis
        os.environ.setdefault('ENFORCE_DOMAIN_PRIORITY', 'true')
        os.environ.setdefault('USE_OPTIMIZED_PROMPTS', 'true')
        os.environ.setdefault('ANALYSIS_SEED', '42')
        
        # Validate file exists
        if not Path(file_path).exists():
            print(f'❌ File not found: {file_path}')
            return
        
        # Create temporary directory for analysis
        temp_dir = tempfile.mkdtemp()
        temp_file = Path(temp_dir) / Path(file_path).name
        temp_file.write_text(Path(file_path).read_text(encoding='utf-8'))
        
        # Analyze the file
        reviewer = SecurityPRReviewer(local_folder=temp_dir)
        files = reviewer.analyze_folder(temp_dir)
        findings = reviewer.analyze_files(files)
        
        # Display results
        print('\n🛡️ SECURITY ANALYSIS RESULTS')
        print('='*60)
        print(f'📄 File: {Path(file_path).name}')
        print(f'📁 Path: {file_path}')
        print('='*60)
        
        if findings:
            # Sort findings by domain priority
            sorted_findings = reviewer._sort_findings_by_priority(findings)
            
            for f in sorted_findings:
                severity = f.get("severity", "UNKNOWN")
                control_id = f.get("control_id", "N/A")
                description = f.get("description", "N/A")
                file_name = f.get("file_path", "N/A")
                line = f.get("line", "N/A")
                remediation = f.get("remediation", "N/A")
                
                # Severity emoji
                severity_emoji = {
                    "CRITICAL": "🔴",
                    "HIGH": "🟠", 
                    "MEDIUM": "🟡",
                    "LOW": "🔵"
                }.get(severity, "⚪")
                
                print(f'{severity_emoji} {severity} - {control_id}: {description}')
                print(f'   📄 File: {file_name} (Line {line})')
                print(f'   🔧 Fix: {remediation}')
                print()
            
            print(f'📊 Total Issues Found: {len(findings)}')
            
            # Domain summary
            domains = {}
            for f in findings:
                domain = reviewer._get_finding_domain(f)
                domains[domain] = domains.get(domain, 0) + 1
            
            if len(domains) > 1:
                print(f'\n📈 Issues by Security Domain:')
                for domain, count in domains.items():
                    print(f'   • {domain}: {count} issues')
        else:
            print('✅ No security issues found!')
            print('🎉 Your Infrastructure-as-Code follows security best practices.')
        
        print(f'\n💡 Analysis based on Azure Security Benchmark v3.0')
        print(f'🔄 Domain prioritization: Identity → Network → Data Protection')
        
    except ImportError as e:
        print(f'❌ Import error: {e}')
        print('Please ensure you are in the IaC Guardian directory')
    except Exception as e:
        print(f'❌ Error analyzing file: {e}')
        import traceback
        traceback.print_exc()

def main():
    """Main function."""
    if len(sys.argv) != 2:
        print('Usage: python analyze_current_file.py <file_path>')
        print('Example: python analyze_current_file.py ./storage.json')
        sys.exit(1)
    
    file_path = sys.argv[1]
    analyze_file(file_path)

if __name__ == "__main__":
    main()
