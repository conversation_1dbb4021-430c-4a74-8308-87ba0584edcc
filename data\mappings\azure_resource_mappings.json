{"Storage": {"arm_types": ["Microsoft.Storage/storageAccounts", "Microsoft.Storage/storageAccounts/blobServices", "Microsoft.Storage/storageAccounts/blobServices/containers", "Microsoft.Storage/storageAccounts/fileServices", "Microsoft.Storage/storageAccounts/fileServices/shares", "Microsoft.Storage/storageAccounts/queueServices", "Microsoft.Storage/storageAccounts/queueServices/queues", "Microsoft.Storage/storageAccounts/tableServices", "Microsoft.Storage/storageAccounts/tableServices/tables", "Microsoft.Storage/storageAccounts/encryptionScopes", "Microsoft.Storage/storageAccounts/managementPolicies", "Microsoft.Storage/storageAccounts/objectReplicationPolicies", "Microsoft.Storage/storageAccounts/privateEndpointConnections"], "terraform_types": ["azurerm_storage_account", "azurerm_storage_account_customer_managed_key", "azurerm_storage_account_network_rules", "azurerm_storage_container", "azurerm_storage_blob", "azurerm_storage_blob_inventory_policy", "azurerm_storage_share", "azurerm_storage_share_directory", "azurerm_storage_share_file", "azurerm_storage_queue", "azurerm_storage_table", "azurerm_storage_table_entity", "azurerm_storage_encryption_scope", "azurerm_storage_management_policy", "azurerm_storage_object_replication"], "bicep_types": ["storageAccount", "storageAccounts", "blobServices", "blobC<PERSON>r", "fileServices", "fileShare", "queueServices", "queue", "tableServices", "table", "managementPolicies", "encryptionScopes"], "keywords": ["storage account", "blob", "container", "file share", "queue storage", "table storage", "storage encryption", "storage firewall", "storage network rules", "storage lifecycle", "immutable storage", "blob versioning", "soft delete", "storage tier", "storage replication"]}, "KeyVault": {"arm_types": ["Microsoft.KeyVault/vaults", "Microsoft.KeyVault/vaults/secrets", "Microsoft.KeyVault/vaults/keys", "Microsoft.KeyVault/vaults/certificates", "Microsoft.KeyVault/vaults/accessPolicies", "Microsoft.KeyVault/vaults/privateEndpointConnections", "Microsoft.KeyVault/managedHSMs", "Microsoft.KeyVault/managedHSMs/privateEndpointConnections"], "terraform_types": ["azurerm_key_vault", "azurerm_key_vault_access_policy", "azurerm_key_vault_secret", "azurerm_key_vault_key", "azurerm_key_vault_certificate", "azurerm_key_vault_certificate_issuer", "azurerm_key_vault_managed_hardware_security_module", "azurerm_key_vault_managed_storage_account", "azurerm_key_vault_managed_storage_account_sas_token_definition"], "bicep_types": ["<PERSON><PERSON><PERSON>", "vault", "vaults", "managedHSM", "managedHSMs", "secret", "key", "certificate", "accessPolicy", "accessPolicies"], "keywords": ["key vault", "keyvault", "secrets", "certificates", "keys", "hsm", "hardware security module", "access policy", "rbac", "purge protection", "soft delete", "key rotation", "certificate authority", "cryptographic"]}, "SQL": {"arm_types": ["Microsoft.Sql/servers", "Microsoft.Sql/servers/databases", "Microsoft.Sql/servers/elasticPools", "Microsoft.Sql/servers/firewallRules", "Microsoft.Sql/servers/virtualNetworkRules", "Microsoft.Sql/servers/securityAlertPolicies", "Microsoft.Sql/servers/vulnerabilityAssessments", "Microsoft.Sql/servers/auditingSettings", "Microsoft.Sql/servers/devOpsAuditingSettings", "Microsoft.Sql/servers/encryptionProtector", "Microsoft.Sql/servers/keys", "Microsoft.Sql/servers/administrators", "Microsoft.Sql/servers/azureADOnlyAuthentications", "Microsoft.Sql/servers/connectionPolicies", "Microsoft.Sql/servers/databases/auditingSettings", "Microsoft.Sql/servers/databases/backupLongTermRetentionPolicies", "Microsoft.Sql/servers/databases/backupShortTermRetentionPolicies", "Microsoft.Sql/servers/databases/dataMaskingPolicies", "Microsoft.Sql/servers/databases/geoBackupPolicies", "Microsoft.Sql/servers/databases/securityAlertPolicies", "Microsoft.Sql/servers/databases/transparentDataEncryption", "Microsoft.Sql/servers/databases/vulnerabilityAssessments", "Microsoft.Sql/servers/databases/workloadGroups", "Microsoft.Sql/servers/privateEndpointConnections", "Microsoft.Sql/managedInstances", "Microsoft.Sql/managedInstances/databases", "Microsoft.Sql/managedInstances/securityAlertPolicies", "Microsoft.Sql/managedInstances/vulnerabilityAssessments", "Microsoft.Sql/managedInstances/administrators", "Microsoft.Sql/managedInstances/azureADOnlyAuthentications", "Microsoft.Sql/managedInstances/encryptionProtector", "Microsoft.Sql/managedInstances/keys", "Microsoft.Sql/managedInstances/privateEndpointConnections"], "terraform_types": ["azurerm_mssql_server", "azurerm_sql_server", "azurerm_mssql_database", "azurerm_sql_database", "azurerm_mssql_database_extended_auditing_policy", "azurerm_mssql_elasticpool", "azurerm_sql_elasticpool", "azurerm_mssql_managed_instance", "azurerm_mssql_managed_database", "azurerm_sql_firewall_rule", "azurerm_mssql_firewall_rule", "azurerm_sql_virtual_network_rule", "azurerm_mssql_virtual_network_rule", "azurerm_mssql_server_security_alert_policy", "azurerm_mssql_database_vulnerability_assessment_rule_baseline", "azurerm_mssql_server_vulnerability_assessment", "azurerm_mssql_server_extended_auditing_policy", "azurerm_sql_active_directory_administrator", "azurerm_mssql_server_microsoft_support_auditing_policy", "azurerm_mssql_server_transparent_data_encryption", "azurerm_mssql_database_transparent_data_encryption", "azurerm_mssql_outbound_firewall_rule", "azurerm_mssql_managed_instance_security_alert_policy", "azurerm_mssql_managed_instance_vulnerability_assessment", "azurerm_mssql_managed_instance_transparent_data_encryption", "azurerm_mssql_managed_instance_active_directory_administrator", "azurerm_mssql_job_agent", "azurerm_mssql_job_credential", "azurerm_mssql_elastic_job_agent"], "bicep_types": ["sqlServer", "sqlDatabase", "sql", "server", "database", "managedInstance", "managedDatabase", "elasticPool", "firewallRule", "virtualNetworkRule", "securityAlertPolicy", "vulnerabilityAssessment", "auditingSettings", "encryptionProtector", "transparentDataEncryption", "administrator", "azureADOnlyAuthentication", "connectionPolicy", "dataMaskingPolicy", "backupLongTermRetentionPolicy"], "keywords": ["sql server", "sql database", "managed instance", "elastic pool", "mssql", "azure sql", "database server", "sql managed instance", "sql firewall", "sql security", "sql audit", "sql encryption", "sql tde", "transparent data encryption", "sql vulnerability", "sql assessment", "sql authentication", "azure ad authentication", "sql admin", "sql connection", "data masking", "always encrypted", "row level security", "dynamic data masking", "geo replication", "failover group", "backup retention"]}, "Network": {"arm_types": ["Microsoft.Network/virtualNetworks", "Microsoft.Network/virtualNetworks/subnets", "Microsoft.Network/virtualNetworks/virtualNetworkPeerings", "Microsoft.Network/networkSecurityGroups", "Microsoft.Network/networkSecurityGroups/securityRules", "Microsoft.Network/applicationGateways", "Microsoft.Network/azureFirewalls", "Microsoft.Network/azureFirewallPolicies", "Microsoft.Network/firewallPolicies", "Microsoft.Network/firewallPolicies/ruleCollectionGroups", "Microsoft.Network/bastionHosts", "Microsoft.Network/ddosProtectionPlans", "Microsoft.Network/expressRouteCircuits", "Microsoft.Network/frontDoors", "Microsoft.Network/frontDoorWebApplicationFirewallPolicies", "Microsoft.Network/loadBalancers", "Microsoft.Network/localNetworkGateways", "Microsoft.Network/natGateways", "Microsoft.Network/networkInterfaces", "Microsoft.Network/networkWatchers", "Microsoft.Network/privateEndpoints", "Microsoft.Network/privateLinkServices", "Microsoft.Network/publicIPAddresses", "Microsoft.Network/publicIPPrefixes", "Microsoft.Network/routeTables", "Microsoft.Network/serviceEndpointPolicies", "Microsoft.Network/trafficManagerProfiles", "Microsoft.Network/virtualHubs", "Microsoft.Network/virtualNetworkGateways", "Microsoft.Network/virtualWans", "Microsoft.Network/vpnGateways", "Microsoft.Network/vpnSites", "Microsoft.Network/applicationSecurityGroups", "Microsoft.Network/ipGroups"], "terraform_types": ["azurerm_virtual_network", "azurerm_subnet", "azurerm_subnet_network_security_group_association", "azurerm_subnet_route_table_association", "azurerm_virtual_network_peering", "azurerm_network_security_group", "azurerm_network_security_rule", "azurerm_application_gateway", "azurerm_firewall", "azurerm_firewall_policy", "azurerm_firewall_policy_rule_collection_group", "azurerm_bastion_host", "azurerm_network_ddos_protection_plan", "azurerm_express_route_circuit", "azurerm_frontdoor", "azurerm_frontdoor_firewall_policy", "azurerm_lb", "azurerm_lb_backend_address_pool", "azurerm_lb_nat_rule", "azurerm_lb_probe", "azurerm_lb_rule", "azurerm_local_network_gateway", "azurerm_nat_gateway", "azurerm_network_interface", "azurerm_network_interface_application_gateway_backend_address_pool_association", "azurerm_network_interface_backend_address_pool_association", "azurerm_network_interface_nat_rule_association", "azurerm_network_interface_security_group_association", "azurerm_network_watcher", "azurerm_network_watcher_flow_log", "azurerm_private_endpoint", "azurerm_private_link_service", "azurerm_public_ip", "azurerm_public_ip_prefix", "azurerm_route", "azurerm_route_table", "azurerm_traffic_manager_profile", "azurerm_virtual_hub", "azurerm_virtual_network_gateway", "azurerm_virtual_network_gateway_connection", "azurerm_virtual_wan", "azurerm_vpn_gateway", "azurerm_vpn_site", "azurerm_application_security_group", "azurerm_ip_group", "azurerm_web_application_firewall_policy"], "bicep_types": ["virtualNetwork", "vnet", "subnet", "networkSecurityGroup", "nsg", "securityRule", "applicationGateway", "appGateway", "firewall", "azureFirewall", "firewallPolicy", "bastionHost", "bastion", "ddosProtectionPlan", "expressRouteCircuit", "frontDoor", "loadBalancer", "lb", "localNetworkGateway", "natGateway", "networkInterface", "nic", "networkWatcher", "privateEndpoint", "privateLinkService", "publicIPAdd<PERSON>", "publicIP", "pip", "routeTable", "trafficManagerProfile", "virtualHub", "virtualNetworkGateway", "vpnGateway", "virtualWan", "applicationSecurityGroup", "asg", "ipGroup", "wafPolicy"], "keywords": ["virtual network", "vnet", "subnet", "nsg", "network security group", "firewall", "azure firewall", "application gateway", "app gateway", "waf", "web application firewall", "load balancer", "traffic manager", "front door", "bastion", "ddos protection", "express route", "vpn", "vpn gateway", "virtual network gateway", "peering", "private endpoint", "private link", "service endpoint", "network interface", "nic", "public ip", "nat gateway", "route table", "udr", "user defined route", "network watcher", "flow logs", "packet capture", "network security", "network isolation", "network segmentation", "hub and spoke", "virtual wan", "sd-wan"]}, "Compute": {"arm_types": ["Microsoft.Compute/virtualMachines", "Microsoft.Compute/virtualMachines/extensions", "Microsoft.Compute/virtualMachineScaleSets", "Microsoft.Compute/virtualMachineScaleSets/extensions", "Microsoft.Compute/virtualMachineScaleSets/virtualMachines", "Microsoft.Compute/availabilitySets", "Microsoft.Compute/proximityPlacementGroups", "Microsoft.Compute/dedicatedHosts", "Microsoft.Compute/dedicatedHostGroups", "Microsoft.Compute/disks", "Microsoft.Compute/diskEncryptionSets", "Microsoft.Compute/diskAccesses", "Microsoft.Compute/snapshots", "Microsoft.Compute/images", "Microsoft.Compute/galleries", "Microsoft.Compute/galleries/images", "Microsoft.Compute/galleries/images/versions", "Microsoft.Compute/restorePointCollections", "Microsoft.Compute/sshPublicKeys", "Microsoft.Compute/capacityReservationGroups", "Microsoft.Compute/capacityReservationGroups/capacityReservations"], "terraform_types": ["azurerm_virtual_machine", "azurerm_linux_virtual_machine", "azurerm_windows_virtual_machine", "azurerm_virtual_machine_extension", "azurerm_virtual_machine_scale_set", "azurerm_linux_virtual_machine_scale_set", "azurerm_windows_virtual_machine_scale_set", "azurerm_virtual_machine_scale_set_extension", "azurerm_orchestrated_virtual_machine_scale_set", "azurerm_availability_set", "azurerm_proximity_placement_group", "azurerm_dedicated_host", "azurerm_dedicated_host_group", "azurerm_managed_disk", "azurerm_disk_encryption_set", "azurerm_disk_access", "azurerm_snapshot", "azurerm_image", "azurerm_shared_image_gallery", "azurerm_shared_image", "azurerm_shared_image_version", "azurerm_virtual_machine_restore_point_collection", "azurerm_ssh_public_key", "azurerm_capacity_reservation", "azurerm_capacity_reservation_group", "azurerm_virtual_machine_data_disk_attachment", "azurerm_virtual_machine_scale_set_packet_capture"], "bicep_types": ["virtualMachine", "vm", "virtualMachineScaleSet", "vmss", "virtualMachineExtension", "vmExtension", "availabilitySet", "proximityPlacementGroup", "dedicatedHost", "dedicatedHostGroup", "disk", "managedDisk", "diskEncryptionSet", "diskAccess", "snapshot", "image", "gallery", "galleryImage", "galleryImageVersion", "restorePointCollection", "sshPublicKey", "capacityReservation", "capacityReservationGroup", "computeResource"], "keywords": ["virtual machine", "vm", "vmss", "scale set", "virtual machine scale set", "compute", "ubuntu", "windows server", "linux", "redhat", "centos", "debian", "suse", "trusted launch", "secure boot", "vtpm", "guest attestation", "managed disk", "os disk", "data disk", "ssh key", "rdp", "password authentication", "key authentication", "network interface", "diagnostic settings", "extension", "custom script extension", "vm extension", "availability set", "availability zone", "proximity placement", "dedicated host", "spot instance", "reserved instance", "disk encryption", "azure disk encryption", "encryption at host", "accelerated networking", "managed identity", "system assigned identity", "user assigned identity", "boot diagnostics", "serial console", "vm backup", "disaster recovery", "site recovery", "update management", "patch management", "auto shutdown", "vm insights"]}, "AppService": {"arm_types": ["Microsoft.Web/sites", "Microsoft.Web/sites/config", "Microsoft.Web/sites/slots", "Microsoft.Web/sites/slots/config", "Microsoft.Web/serverFarms", "Microsoft.Web/certificates", "Microsoft.Web/connectionGateways", "Microsoft.Web/connections", "Microsoft.Web/customApis", "Microsoft.Web/deletedSites", "Microsoft.Web/deploymentLocations", "Microsoft.Web/georegions", "Microsoft.Web/hostingEnvironments", "Microsoft.Web/kubeEnvironments", "Microsoft.Web/publishingUsers", "Microsoft.Web/recommendations", "Microsoft.Web/resourceHealthMetadata", "Microsoft.Web/runtimes", "Microsoft.Web/serverFarms/eventGridFilters", "Microsoft.Web/serverFarms/firstPartyApps", "Microsoft.Web/serverFarms/firstPartyApps/keyVaultSettings", "Microsoft.Web/sites/deployments", "Microsoft.Web/sites/domainOwnershipIdentifiers", "Microsoft.Web/sites/extensions", "Microsoft.Web/sites/functions", "Microsoft.Web/sites/hostNameBindings", "Microsoft.Web/sites/hybridconnection", "Microsoft.Web/sites/instances", "Microsoft.Web/sites/instances/extensions", "Microsoft.Web/sites/premieraddons", "Microsoft.Web/sites/privateEndpointConnections", "Microsoft.Web/sites/processes", "Microsoft.Web/sites/publicCertificates", "Microsoft.Web/sites/recommendationHistory", "Microsoft.Web/sites/resourceHealthMetadata", "Microsoft.Web/sites/slots/deployments", "Microsoft.Web/sites/slots/domainOwnershipIdentifiers", "Microsoft.Web/sites/slots/extensions", "Microsoft.Web/sites/slots/functions", "Microsoft.Web/sites/slots/hostNameBindings", "Microsoft.Web/sites/slots/hybridconnection", "Microsoft.Web/sites/slots/instances", "Microsoft.Web/sites/slots/instances/extensions", "Microsoft.Web/sites/slots/premieraddons", "Microsoft.Web/sites/slots/privateEndpointConnections", "Microsoft.Web/sites/slots/processes", "Microsoft.Web/sites/slots/publicCertificates", "Microsoft.Web/sites/slots/virtualNetworkConnections", "Microsoft.Web/sites/virtualNetworkConnections", "Microsoft.Web/sourceControls", "Microsoft.Web/staticSites", "Microsoft.Web/staticSites/builds", "Microsoft.Web/staticSites/config", "Microsoft.Web/staticSites/customDomains", "Microsoft.Web/staticSites/privateEndpointConnections", "Microsoft.Web/validate", "Microsoft.Web/verifyHostingEnvironmentVnet"], "terraform_types": ["azurerm_app_service", "azurerm_app_service_plan", "azurerm_app_service_slot", "azurerm_app_service_active_slot", "azurerm_app_service_certificate", "azurerm_app_service_certificate_binding", "azurerm_app_service_certificate_order", "azurerm_app_service_custom_hostname_binding", "azurerm_app_service_hybrid_connection", "azurerm_app_service_managed_certificate", "azurerm_app_service_source_control_token", "azurerm_app_service_virtual_network_swift_connection", "azurerm_function_app", "azurerm_function_app_slot", "azurerm_function_app_active_slot", "azurerm_function_app_hybrid_connection", "azurerm_linux_function_app", "azurerm_linux_function_app_slot", "azurerm_linux_web_app", "azurerm_linux_web_app_slot", "azurerm_logic_app_action_custom", "azurerm_logic_app_action_http", "azurerm_logic_app_integration_account", "azurerm_logic_app_trigger_custom", "azurerm_logic_app_trigger_http_request", "azurerm_logic_app_trigger_recurrence", "azurerm_logic_app_workflow", "azurerm_service_plan", "azurerm_source_control_token", "azurerm_static_site", "azurerm_static_site_custom_domain", "azurerm_web_application_firewall_policy", "azurerm_windows_function_app", "azurerm_windows_function_app_slot", "azurerm_windows_web_app", "azurerm_windows_web_app_slot"], "bicep_types": ["site", "sites", "webApp", "functionApp", "appService", "appServicePlan", "serverfarm", "serverFarms", "webAppSlot", "functionAppSlot", "slot", "certificate", "customDomain", "hostNameBinding", "hybridConnection", "virtualNetworkConnection", "staticSite", "logicApp", "workflow", "apiConnection", "customApi"], "keywords": ["app service", "web app", "function app", "api app", "mobile app", "app service plan", "service plan", "consumption plan", "premium plan", "dedicated plan", "isolated plan", "app service environment", "ase", "deployment slot", "staging slot", "production slot", "custom domain", "ssl certificate", "tls certificate", "managed certificate", "hybrid connection", "vnet integration", "private endpoint", "access restriction", "ip restriction", "cors", "authentication", "authorization", "easy auth", "managed identity", "deployment center", "continuous deployment", "ci/cd", "github actions", "azure devops", "kudu", "scm", "diagnostic logs", "application insights", "auto scale", "scale out", "scale up", "always on", "health check", "backup", "disaster recovery", "traffic manager", "front door", "cdn", "static web app", "serverless", "functions", "durable functions", "logic apps", "api management"]}, "Container": {"arm_types": ["Microsoft.ContainerService/managedClusters", "Microsoft.ContainerService/managedClusters/agentPools", "Microsoft.ContainerService/managedClusters/maintenanceConfigurations", "Microsoft.ContainerService/managedClusters/privateEndpointConnections", "Microsoft.ContainerRegistry/registries", "Microsoft.ContainerRegistry/registries/agentPools", "Microsoft.ContainerRegistry/registries/buildTasks", "Microsoft.ContainerRegistry/registries/buildTasks/steps", "Microsoft.ContainerRegistry/registries/builds", "Microsoft.ContainerRegistry/registries/connectedRegistries", "Microsoft.ContainerRegistry/registries/exportPipelines", "Microsoft.ContainerRegistry/registries/importPipelines", "Microsoft.ContainerRegistry/registries/pipelineRuns", "Microsoft.ContainerRegistry/registries/privateEndpointConnections", "Microsoft.ContainerRegistry/registries/replications", "Microsoft.ContainerRegistry/registries/runs", "Microsoft.ContainerRegistry/registries/scopeMaps", "Microsoft.ContainerRegistry/registries/taskRuns", "Microsoft.ContainerRegistry/registries/tasks", "Microsoft.ContainerRegistry/registries/tokens", "Microsoft.ContainerRegistry/registries/webhooks", "Microsoft.ContainerInstance/containerGroups", "Microsoft.ContainerInstance/serviceAssociationLinks"], "terraform_types": ["azurerm_kubernetes_cluster", "azurerm_kubernetes_cluster_node_pool", "azurerm_kubernetes_cluster_extension", "azurerm_kubernetes_fleet_manager", "azurerm_container_registry", "azurerm_container_registry_agent_pool", "azurerm_container_registry_scope_map", "azurerm_container_registry_task", "azurerm_container_registry_task_schedule_run_now", "azurerm_container_registry_token", "azurerm_container_registry_token_password", "azurerm_container_registry_webhook", "azurerm_container_connected_registry", "azurerm_container_group", "azurerm_container_app", "azurerm_container_app_environment", "azurerm_container_app_environment_certificate", "azurerm_container_app_environment_custom_domain", "azurerm_container_app_environment_dapr_component", "azurerm_container_app_environment_storage"], "bicep_types": ["managedCluster", "aks", "aksCluster", "kubernetesCluster", "agentPool", "nodePool", "containerRegistry", "acr", "registry", "containerGroup", "containerInstance", "aci", "containerApp", "containerAppEnvironment", "scopeMap", "token", "webhook", "replication", "task"], "keywords": ["aks", "kubernetes", "k8s", "container", "docker", "containerization", "orchestration", "cluster", "node pool", "agent pool", "kubelet", "kubectl", "helm", "container registry", "acr", "docker registry", "image", "container image", "dockerfile", "container instance", "aci", "container group", "container apps", "microservices", "service mesh", "istio", "linkerd", "ingress", "ingress controller", "load balancer", "cluster autoscaler", "horizontal pod autoscaler", "hpa", "vertical pod autoscaler", "vpa", "rbac", "role based access control", "service principal", "managed identity", "aad integration", "azure active directory", "network policy", "calico", "azure cni", "kubenet", "private cluster", "api server", "authorized ip", "pod security", "admission controller", "azure policy", "gatekeeper", "opa", "open policy agent", "container insights", "prometheus", "grafana", "monitoring", "logging", "fluentd", "fluent bit", "azure monitor", "log analytics", "container security", "image scanning", "vulnerability scanning", "azure defender", "devsecops", "gitops", "flux", "argo cd", "ci/cd", "azure devops", "github actions", "jenkins", "registry replication", "geo replication", "webhook", "acr tasks", "base image update"]}, "CosmosDB": {"arm_types": ["Microsoft.DocumentDB/databaseAccounts", "Microsoft.DocumentDB/databaseAccounts/apis/databases", "Microsoft.DocumentDB/databaseAccounts/apis/databases/collections", "Microsoft.DocumentDB/databaseAccounts/apis/databases/containers", "Microsoft.DocumentDB/databaseAccounts/apis/databases/graphs", "Microsoft.DocumentDB/databaseAccounts/apis/keyspaces", "Microsoft.DocumentDB/databaseAccounts/apis/keyspaces/tables", "Microsoft.DocumentDB/databaseAccounts/apis/tables", "Microsoft.DocumentDB/databaseAccounts/cassandraKeyspaces", "Microsoft.DocumentDB/databaseAccounts/cassandraKeyspaces/tables", "Microsoft.DocumentDB/databaseAccounts/gremlinDatabases", "Microsoft.DocumentDB/databaseAccounts/gremlinDatabases/graphs", "Microsoft.DocumentDB/databaseAccounts/mongodbDatabases", "Microsoft.DocumentDB/databaseAccounts/mongodbDatabases/collections", "Microsoft.DocumentDB/databaseAccounts/notebookWorkspaces", "Microsoft.DocumentDB/databaseAccounts/privateEndpointConnections", "Microsoft.DocumentDB/databaseAccounts/privateLinkResources", "Microsoft.DocumentDB/databaseAccounts/sqlDatabases", "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers", "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers/storedProcedures", "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers/triggers", "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers/userDefinedFunctions", "Microsoft.DocumentDB/databaseAccounts/sqlRoleAssignments", "Microsoft.DocumentDB/databaseAccounts/sqlRoleDefinitions", "Microsoft.DocumentDB/databaseAccounts/tables"], "terraform_types": ["azurerm_cosmosdb_account", "azurerm_cosmosdb_sql_database", "azurerm_cosmosdb_sql_container", "azurerm_cosmosdb_sql_function", "azurerm_cosmosdb_sql_stored_procedure", "azurerm_cosmosdb_sql_trigger", "azurerm_cosmosdb_sql_role_assignment", "azurerm_cosmosdb_sql_role_definition", "azurerm_cosmosdb_sql_dedicated_gateway", "azurerm_cosmosdb_cassandra_keyspace", "azurerm_cosmosdb_cassandra_table", "azurerm_cosmosdb_cassandra_cluster", "azurerm_cosmosdb_cassandra_datacenter", "azurerm_cosmosdb_gremlin_database", "azurerm_cosmosdb_gremlin_graph", "azurerm_cosmosdb_mongo_database", "azurerm_cosmosdb_mongo_collection", "azurerm_cosmosdb_notebook_workspace", "azurerm_cosmosdb_table"], "bicep_types": ["databaseAccount", "cosmosDb", "cosmos", "documentDb", "sqlDatabase", "sqlContainer", "cassandraKeyspace", "cassandraTable", "gremlinDatabase", "gremlinGraph", "mongoDatabase", "mongoCollection", "table", "notebookWorkspace", "sqlRoleAssignment", "sqlRoleDefinition"], "keywords": ["cosmos db", "cosmosdb", "documentdb", "nosql", "globally distributed", "multi-model", "sql api", "core sql", "mongodb api", "cassandra api", "gremlin api", "graph api", "table api", "consistency level", "strong consistency", "bounded staleness", "session consistency", "consistent prefix", "eventual consistency", "partition key", "throughput", "request units", "ru/s", "autoscale", "manual throughput", "provisioned throughput", "serverless", "global distribution", "multi-region", "multi-master", "multi-write", "automatic failover", "manual failover", "conflict resolution", "last write wins", "custom conflict resolution", "stored procedures", "triggers", "user defined functions", "udf", "change feed", "time to live", "ttl", "indexing policy", "composite index", "spatial index", "unique key", "analytical store", "synapse link", "htap", "backup", "continuous backup", "periodic backup", "point in time restore", "pitr", "rbac", "data plane rbac", "managed identity", "private endpoint", "ip firewall", "virtual network", "service endpoint", "encryption at rest", "customer managed keys", "cmk", "azure monitor", "diagnostic logs", "metrics", "alerts"]}, "EventHub": {"arm_types": ["Microsoft.EventHub/namespaces", "Microsoft.EventHub/namespaces/authorizationRules", "Microsoft.EventHub/namespaces/disasterRecoveryConfigs", "Microsoft.EventHub/namespaces/eventhubs", "Microsoft.EventHub/namespaces/eventhubs/authorizationRules", "Microsoft.EventHub/namespaces/eventhubs/consumergroups", "Microsoft.EventHub/namespaces/ipFilterRules", "Microsoft.EventHub/namespaces/networkRuleSets", "Microsoft.EventHub/namespaces/privateEndpointConnections", "Microsoft.EventHub/namespaces/virtualNetworkRules", "Microsoft.EventHub/clusters", "Microsoft.EventHub/namespaces/schemagroups"], "terraform_types": ["azurerm_eventhub_namespace", "azurerm_eventhub_namespace_authorization_rule", "azurerm_eventhub_namespace_customer_managed_key", "azurerm_eventhub_namespace_disaster_recovery_config", "azurerm_eventhub_namespace_schema_group", "azurerm_eventhub", "azurerm_eventhub_authorization_rule", "azurerm_eventhub_consumer_group", "azurerm_eventhub_cluster"], "bicep_types": ["eventHubNamespace", "eventHub", "namespace", "authorizationRule", "consumerGroup", "disasterRecoveryConfig", "networkRuleSet", "privateEndpointConnection", "cluster", "schemaGroup"], "keywords": ["event hub", "eventhub", "event hubs", "eventhubs", "event streaming", "streaming", "event ingestion", "big data", "real-time", "namespace", "partition", "partition key", "consumer group", "event processor", "throughput unit", "auto-inflate", "capture", "event hub capture", "avro", "schema registry", "kafka", "kafka endpoint", "amqp", "authorization rule", "shared access signature", "sas", "connection string", "disaster recovery", "geo-disaster recovery", "geo-dr", "paired namespace", "failover", "dedicated cluster", "zone redundancy", "availability zones", "private endpoint", "ip filtering", "firewall", "virtual network", "service endpoint", "managed identity", "customer managed key", "encryption", "diagnostic logs", "metrics", "azure monitor", "event hub insights", "stream analytics", "azure functions", "logic apps", "data factory"]}, "ServiceBus": {"arm_types": ["Microsoft.ServiceBus/namespaces", "Microsoft.ServiceBus/namespaces/authorizationRules", "Microsoft.ServiceBus/namespaces/disasterRecoveryConfigs", "Microsoft.ServiceBus/namespaces/ipFilterRules", "Microsoft.ServiceBus/namespaces/migrationConfigurations", "Microsoft.ServiceBus/namespaces/networkRuleSets", "Microsoft.ServiceBus/namespaces/privateEndpointConnections", "Microsoft.ServiceBus/namespaces/queues", "Microsoft.ServiceBus/namespaces/queues/authorizationRules", "Microsoft.ServiceBus/namespaces/topics", "Microsoft.ServiceBus/namespaces/topics/authorizationRules", "Microsoft.ServiceBus/namespaces/topics/subscriptions", "Microsoft.ServiceBus/namespaces/topics/subscriptions/rules", "Microsoft.ServiceBus/namespaces/virtualNetworkRules"], "terraform_types": ["azurerm_servicebus_namespace", "azurerm_servicebus_namespace_authorization_rule", "azurerm_servicebus_namespace_customer_managed_key", "azurerm_servicebus_namespace_disaster_recovery_config", "azurerm_servicebus_namespace_network_rule_set", "azurerm_servicebus_queue", "azurerm_servicebus_queue_authorization_rule", "azurerm_servicebus_topic", "azurerm_servicebus_topic_authorization_rule", "azurerm_servicebus_subscription", "azurerm_servicebus_subscription_rule"], "bicep_types": ["serviceBusNamespace", "serviceBus", "namespace", "queue", "topic", "subscription", "authorizationRule", "disasterRecoveryConfig", "networkRuleSet", "privateEndpointConnection", "subscriptionRule"], "keywords": ["service bus", "servicebus", "messaging", "message queue", "queue", "topic", "subscription", "enterprise messaging", "message broker", "publish subscribe", "pub/sub", "dead letter queue", "dlq", "session", "message session", "fifo", "first in first out", "duplicate detection", "message deferral", "scheduled messages", "message batching", "transaction", "amqp", "authorization rule", "shared access signature", "sas", "connection string", "premium tier", "standard tier", "basic tier", "messaging unit", "namespace", "geo-disaster recovery", "geo-dr", "paired namespace", "failover", "zone redundancy", "availability zones", "private endpoint", "ip filtering", "firewall", "virtual network", "service endpoint", "managed identity", "customer managed key", "encryption", "diagnostic logs", "metrics", "azure monitor", "service bus explorer", "azure functions", "logic apps", "event grid"]}, "DataFactory": {"arm_types": ["Microsoft.DataFactory/factories", "Microsoft.DataFactory/factories/datasets", "Microsoft.DataFactory/factories/dataflows", "Microsoft.DataFactory/factories/integrationRuntimes", "Microsoft.DataFactory/factories/linkedservices", "Microsoft.DataFactory/factories/managedVirtualNetworks", "Microsoft.DataFactory/factories/managedVirtualNetworks/managedPrivateEndpoints", "Microsoft.DataFactory/factories/pipelines", "Microsoft.DataFactory/factories/privateEndpointConnections", "Microsoft.DataFactory/factories/triggers"], "terraform_types": ["azurerm_data_factory", "azurerm_data_factory_custom_dataset", "azurerm_data_factory_data_flow", "azurerm_data_factory_dataset_azure_blob", "azurerm_data_factory_dataset_azure_sql_table", "azurerm_data_factory_dataset_cosmosdb_sqlapi", "azurerm_data_factory_dataset_delimited_text", "azurerm_data_factory_dataset_http", "azurerm_data_factory_dataset_json", "azurerm_data_factory_dataset_mysql", "azurerm_data_factory_dataset_parquet", "azurerm_data_factory_dataset_postgresql", "azurerm_data_factory_dataset_snowflake", "azurerm_data_factory_dataset_sql_server_table", "azurerm_data_factory_integration_runtime_azure", "azurerm_data_factory_integration_runtime_azure_ssis", "azurerm_data_factory_integration_runtime_self_hosted", "azurerm_data_factory_linked_custom_service", "azurerm_data_factory_linked_service_azure_blob_storage", "azurerm_data_factory_linked_service_azure_databricks", "azurerm_data_factory_linked_service_azure_file_storage", "azurerm_data_factory_linked_service_azure_function", "azurerm_data_factory_linked_service_azure_search", "azurerm_data_factory_linked_service_azure_sql_database", "azurerm_data_factory_linked_service_azure_table_storage", "azurerm_data_factory_linked_service_cosmosdb", "azurerm_data_factory_linked_service_cosmosdb_mongoapi", "azurerm_data_factory_linked_service_data_lake_storage_gen2", "azurerm_data_factory_linked_service_key_vault", "azurerm_data_factory_linked_service_kusto", "azurerm_data_factory_linked_service_mysql", "azurerm_data_factory_linked_service_odata", "azurerm_data_factory_linked_service_odbc", "azurerm_data_factory_linked_service_postgresql", "azurerm_data_factory_linked_service_sftp", "azurerm_data_factory_linked_service_snowflake", "azurerm_data_factory_linked_service_sql_server", "azurerm_data_factory_linked_service_synapse", "azurerm_data_factory_linked_service_web", "azurerm_data_factory_managed_private_endpoint", "azurerm_data_factory_pipeline", "azurerm_data_factory_trigger_blob_event", "azurerm_data_factory_trigger_custom_event", "azurerm_data_factory_trigger_schedule", "azurerm_data_factory_trigger_tumbling_window"], "bicep_types": ["dataFactory", "factory", "adf", "dataset", "dataflow", "integrationRuntime", "linkedService", "managedVirtualNetwork", "managedPrivateEndpoint", "pipeline", "trigger"], "keywords": ["data factory", "adf", "azure data factory", "etl", "elt", "data integration", "data pipeline", "pipeline", "activity", "dataset", "linked service", "integration runtime", "self-hosted integration runtime", "shir", "azure integration runtime", "ssis integration runtime", "data flow", "mapping data flow", "wrangling data flow", "copy activity", "data movement", "transformation", "trigger", "schedule trigger", "tumbling window trigger", "event trigger", "storage event trigger", "custom event trigger", "parameter", "variable", "expression", "dynamic content", "managed virtual network", "managed private endpoint", "git integration", "ci/cd", "devops", "source control", "arm template", "monitoring", "alerts", "metrics", "diagnostic logs", "data lineage", "purview", "managed identity", "key vault", "encryption", "customer managed key"]}, "Databricks": {"arm_types": ["Microsoft.Databricks/workspaces", "Microsoft.Databricks/workspaces/privateEndpointConnections", "Microsoft.Databricks/workspaces/virtualNetworkPeerings"], "terraform_types": ["azurerm_databricks_workspace", "azurerm_databricks_workspace_customer_managed_key", "azurerm_databricks_access_connector", "azurerm_databricks_virtual_network_peering"], "bicep_types": ["databricksWorkspace", "databricks", "workspace", "privateEndpointConnection", "virtualNetworkPeering"], "keywords": ["databricks", "azure databricks", "spark", "apache spark", "workspace", "cluster", "notebook", "job", "library", "dbfs", "databricks file system", "delta lake", "delta table", "unity catalog", "mlflow", "machine learning", "ml", "data science", "data engineering", "streaming", "batch processing", "vnet injection", "secure cluster connectivity", "no public ip", "npip", "private link", "managed identity", "azure ad", "scim", "token", "pat", "personal access token", "secret scope", "key vault backed", "encryption", "customer managed key", "cmk", "diagnostic logs", "audit logs", "workspace analytics"]}, "Synapse": {"arm_types": ["Microsoft.Synapse/workspaces", "Microsoft.Synapse/workspaces/administrators", "Microsoft.Synapse/workspaces/auditingSettings", "Microsoft.Synapse/workspaces/bigDataPools", "Microsoft.Synapse/workspaces/databases", "Microsoft.Synapse/workspaces/dedicatedSQLminimalTlsSettings", "Microsoft.Synapse/workspaces/encryptionProtector", "Microsoft.Synapse/workspaces/eventGridFilters", "Microsoft.Synapse/workspaces/extendedAuditingSettings", "Microsoft.Synapse/workspaces/firewallRules", "Microsoft.Synapse/workspaces/integrationRuntimes", "Microsoft.Synapse/workspaces/keys", "Microsoft.Synapse/workspaces/kustoPools", "Microsoft.Synapse/workspaces/kustoPools/attachedDatabaseConfigurations", "Microsoft.Synapse/workspaces/kustoPools/databases", "Microsoft.Synapse/workspaces/kustoPools/databases/dataConnections", "Microsoft.Synapse/workspaces/kustoPools/databases/principalAssignments", "Microsoft.Synapse/workspaces/kustoPools/principalAssignments", "Microsoft.Synapse/workspaces/libraries", "Microsoft.Synapse/workspaces/linkedServices", "Microsoft.Synapse/workspaces/managedIdentitySqlControlSettings", "Microsoft.Synapse/workspaces/privateEndpointConnections", "Microsoft.Synapse/workspaces/privateLinkHubs", "Microsoft.Synapse/workspaces/securityAlertPolicies", "Microsoft.Synapse/workspaces/sparkConfigurations", "Microsoft.Synapse/workspaces/sqlAdministrators", "Microsoft.Synapse/workspaces/sqlPools", "Microsoft.Synapse/workspaces/sqlPools/auditingSettings", "Microsoft.Synapse/workspaces/sqlPools/connectionPolicies", "Microsoft.Synapse/workspaces/sqlPools/dataMaskingPolicies", "Microsoft.Synapse/workspaces/sqlPools/dataMaskingPolicies/rules", "Microsoft.Synapse/workspaces/sqlPools/dataWarehouseUserActivities", "Microsoft.Synapse/workspaces/sqlPools/encryptionProtector", "Microsoft.Synapse/workspaces/sqlPools/extendedAuditingSettings", "Microsoft.Synapse/workspaces/sqlPools/geoBackupPolicies", "Microsoft.Synapse/workspaces/sqlPools/maintenanceWindows", "Microsoft.Synapse/workspaces/sqlPools/metadataSync", "Microsoft.Synapse/workspaces/sqlPools/replicationLinks", "Microsoft.Synapse/workspaces/sqlPools/restorePoints", "Microsoft.Synapse/workspaces/sqlPools/schemas", "Microsoft.Synapse/workspaces/sqlPools/schemas/tables", "Microsoft.Synapse/workspaces/sqlPools/schemas/tables/columns", "Microsoft.Synapse/workspaces/sqlPools/schemas/tables/columns/sensitivityLabels", "Microsoft.Synapse/workspaces/sqlPools/securityAlertPolicies", "Microsoft.Synapse/workspaces/sqlPools/transparentDataEncryption", "Microsoft.Synapse/workspaces/sqlPools/vulnerabilityAssessments", "Microsoft.Synapse/workspaces/sqlPools/vulnerabilityAssessments/rules/baselines", "Microsoft.Synapse/workspaces/sqlPools/workloadGroups", "Microsoft.Synapse/workspaces/sqlPools/workloadGroups/workloadClassifiers", "Microsoft.Synapse/workspaces/vulnerabilityAssessments"], "terraform_types": ["azurerm_synapse_workspace", "azurerm_synapse_workspace_aad_admin", "azurerm_synapse_workspace_extended_auditing_policy", "azurerm_synapse_workspace_key", "azurerm_synapse_workspace_security_alert_policy", "azurerm_synapse_workspace_sql_aad_admin", "azurerm_synapse_workspace_vulnerability_assessment", "azurerm_synapse_firewall_rule", "azurerm_synapse_integration_runtime_azure", "azurerm_synapse_integration_runtime_self_hosted", "azurerm_synapse_linked_service", "azurerm_synapse_managed_private_endpoint", "azurerm_synapse_private_link_hub", "azurerm_synapse_role_assignment", "azurerm_synapse_spark_pool", "azurerm_synapse_sql_pool", "azurerm_synapse_sql_pool_extended_auditing_policy", "azurerm_synapse_sql_pool_security_alert_policy", "azurerm_synapse_sql_pool_workload_classifier", "azurerm_synapse_sql_pool_workload_group"], "bicep_types": ["synapseWorkspace", "synapse", "workspace", "sqlPool", "sparkPool", "bigDataPool", "kustoPool", "integrationRuntime", "linkedService", "firewall", "firewallRule", "privateEndpointConnection", "privateLinkHub", "administrator", "auditingSettings", "encryptionProtector", "securityAlertPolicy", "vulnerabilityAssessment", "workloadGroup", "workloadClassifier"], "keywords": ["synapse", "azure synapse", "synapse analytics", "sql pool", "dedicated sql pool", "serverless sql pool", "spark pool", "apache spark", "data warehouse", "data lake", "big data", "analytics", "workspace", "synapse studio", "pipeline", "data flow", "notebook", "sql script", "spark job", "integration runtime", "linked service", "dataset", "data explorer", "kusto pool", "power bi", "purview", "data lineage", "managed virtual network", "managed private endpoint", "firewall", "ip firewall", "azure ad", "managed identity", "sql authentication", "encryption", "transparent data encryption", "tde", "column level security", "row level security", "dynamic data masking", "auditing", "threat detection", "vulnerability assessment", "workload management", "workload isolation", "result set caching", "materialized views", "replicated tables", "distribution", "partition", "polybase", "copy statement", "external table", "openrowset", "delta lake", "parquet", "monitoring", "dmv", "dynamic management views"]}, "ServiceFabric": {"arm_types": ["Microsoft.ServiceFabric/clusters", "Microsoft.ServiceFabric/managedClusters", "Microsoft.ServiceFabric/clusters/applications", "Microsoft.ServiceFabric/clusters/applicationTypes", "Microsoft.ServiceFabric/clusters/nodeTypes", "Microsoft.ServiceFabric/managedclusters/nodetypes", "Microsoft.ServiceFabric/managedclusters/applications"], "terraform_types": ["azurerm_service_fabric_cluster", "azurerm_service_fabric_managed_cluster"], "bicep_types": ["serviceFabricCluster", "serviceFabricManagedCluster", "Microsoft.ServiceFabric/clusters", "Microsoft.ServiceFabric/managedClusters"], "keywords": ["fabric", "service fabric", "service fabric cluster", "managed cluster", "microservices", "reliable services", "reliable actors", "node type", "application type", "service manifest", "application manifest"]}, "LogicApps": {"arm_types": ["Microsoft.Logic/workflows", "Microsoft.Logic/integrationAccounts", "Microsoft.Logic/integrationAccounts/agreements", "Microsoft.Logic/integrationAccounts/assemblies", "Microsoft.Logic/integrationAccounts/batchConfigurations", "Microsoft.Logic/integrationAccounts/certificates", "Microsoft.Logic/integrationAccounts/groups", "Microsoft.Logic/integrationAccounts/maps", "Microsoft.Logic/integrationAccounts/partners", "Microsoft.Logic/integrationAccounts/rosettanetprocessconfigurations", "Microsoft.Logic/integrationAccounts/schemas", "Microsoft.Logic/integrationAccounts/sessions", "Microsoft.Logic/integrationServiceEnvironments", "Microsoft.Logic/integrationServiceEnvironments/managedApis", "Microsoft.Logic/isolatedEnvironments", "Microsoft.Logic/workflows/accessKeys", "Microsoft.Logic/workflows/runs", "Microsoft.Logic/workflows/runs/actions", "Microsoft.Logic/workflows/runs/actions/repetitions", "Microsoft.Logic/workflows/runs/actions/repetitions/requestHistories", "Microsoft.Logic/workflows/runs/actions/requestHistories", "Microsoft.Logic/workflows/runs/actions/scopeRepetitions", "Microsoft.Logic/workflows/runs/operations", "Microsoft.Logic/workflows/triggers", "Microsoft.Logic/workflows/versions", "Microsoft.Logic/workflows/versions/triggers"], "terraform_types": ["azurerm_logic_app_workflow", "azurerm_logic_app_action_custom", "azurerm_logic_app_action_http", "azurerm_logic_app_integration_account", "azurerm_logic_app_integration_account_agreement", "azurerm_logic_app_integration_account_assembly", "azurerm_logic_app_integration_account_batch_configuration", "azurerm_logic_app_integration_account_certificate", "azurerm_logic_app_integration_account_map", "azurerm_logic_app_integration_account_partner", "azurerm_logic_app_integration_account_schema", "azurerm_logic_app_integration_account_session", "azurerm_logic_app_trigger_custom", "azurerm_logic_app_trigger_http_request", "azurerm_logic_app_trigger_recurrence"], "bicep_types": ["logicApp", "workflow", "workflows", "integrationAccount", "integrationAccounts", "integrationServiceEnvironment", "ise", "agreement", "assembly", "batchConfiguration", "certificate", "map", "partner", "schema", "session", "trigger", "action", "run"], "keywords": ["logic apps", "logic app", "workflow", "integration", "business process", "automation", "connector", "trigger", "action", "condition", "loop", "switch", "scope", "parallel branch", "http trigger", "recurrence trigger", "request trigger", "webhook", "integration account", "b2b", "edi", "xml", "json", "flat file", "transform", "map", "schema", "agreement", "partner", "certificate", "assembly", "batch", "session", "integration service environment", "ise", "isolated", "premium", "consumption", "standard", "stateful", "stateless", "managed connector", "custom connector", "api connection", "connection", "authentication", "o<PERSON>h", "managed identity", "service principal", "run history", "monitoring", "diagnostic logs", "metrics", "alerts", "error handling", "retry policy", "timeout", "concurrency", "throttling"]}, "AzureFirewall": {"arm_types": ["Microsoft.Network/azureFirewalls", "Microsoft.Network/firewallPolicies", "Microsoft.Network/firewallPolicies/ruleCollectionGroups", "Microsoft.Network/azureFirewalls/applicationRuleCollections", "Microsoft.Network/azureFirewalls/natRuleCollections", "Microsoft.Network/azureFirewalls/networkRuleCollections", "Microsoft.Network/publicIPAddresses", "Microsoft.Network/publicIPPrefixes"], "terraform_types": ["azurerm_firewall", "azurerm_firewall_policy", "azurerm_firewall_policy_rule_collection_group", "azurerm_firewall_application_rule_collection", "azurerm_firewall_nat_rule_collection", "azurerm_firewall_network_rule_collection", "azurerm_public_ip", "azurerm_public_ip_prefix"], "bicep_types": ["azureFirewall", "firewall", "firewallPolicy", "firewallPolicies", "ruleCollectionGroups", "applicationRuleCollections", "natRuleCollections", "networkRuleCollections", "publicIPAdd<PERSON>", "publicIPPrefix"], "keywords": ["azure firewall", "firewall", "firewall policy", "rule collection", "application rule", "network rule", "nat rule", "dnat", "snat", "threat intelligence", "idps", "intrusion detection", "firewall logs", "firewall metrics", "hub spoke", "forced tunneling", "premium firewall", "standard firewall", "basic firewall", "firewall manager", "secure hub", "virtual wan", "availability zones", "dns proxy", "fqdn filtering", "url filtering", "web categories", "tls inspection"]}, "Identity": {"arm_types": ["Microsoft.ManagedIdentity/userAssignedIdentities", "Microsoft.ManagedIdentity/identities", "Microsoft.Authorization/roleAssignments", "Microsoft.Authorization/roleDefinitions", "Microsoft.Authorization/policyAssignments", "Microsoft.Authorization/policyDefinitions", "Microsoft.Authorization/policySetDefinitions", "Microsoft.AAD/domainServices", "Microsoft.AzureActiveDirectory/b2cDirectories"], "terraform_types": ["azurerm_user_assigned_identity", "azurerm_role_assignment", "azurerm_role_definition", "azurerm_policy_assignment", "azurerm_policy_definition", "azurerm_policy_set_definition", "azuread_user", "azuread_group", "azuread_application", "azuread_service_principal", "azuread_application_password", "azuread_application_certificate", "azuread_conditional_access_policy", "azurerm_active_directory_domain_service"], "bicep_types": ["userAssignedIdentity", "identity", "roleAssignment", "roleDefinition", "policyAssignment", "policyDefinition", "policySetDefinition", "managedIdentity", "principalId", "tenantId", "objectId"], "keywords": ["managed identity", "user assigned identity", "system assigned identity", "service principal", "role assignment", "rbac", "role based access control", "azure ad", "azure active directory", "identity", "authentication", "authorization", "principal id", "object id", "tenant id", "conditional access", "privileged identity", "pim", "access review", "identity governance", "azure ad b2c", "azure ad ds", "domain services", "application registration", "app registration", "enterprise application", "multi-factor authentication", "mfa", "single sign on", "sso", "federation", "saml", "o<PERSON>h", "openid connect", "oidc", "jwt", "claims", "token", "directory sync", "azure ad connect", "hybrid identity", "password hash sync", "pass through authentication", "pta", "seamless sso"]}}