Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P1-Identity-HIGH,Identity Management,HIGH,IM-1,template.json,181,App Service config does not specify integration with Azure Active Directory for identity management. This weakens secure identity and access management.,Integrate App Service with Azure Active Directory for authentication and access control.,N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-2,template.json,181,App Service config does not specify enforcement of Multi-Factor Authentication (MFA) for users and administrators. This weakens secure access.,Enforce Multi-Factor Authentication (MFA) for all users and administrators accessing the App Service.,N/A,AI,Generic
P1-Identity-MEDIUM,Identity Management,MEDIUM,IM-3,template.json,181,App Service config does not specify use of Conditional Access Policies. This is a recommended best practice for secure access.,Implement Conditional Access Policies in Azure AD to enforce secure access to the App Service.,N/A,AI,Generic
P1-Identity-MEDIUM,Identity Management,MEDIUM,IM-4,template.json,181,App Service config does not specify use of Access Reviews. This is a recommended best practice to ensure only necessary access remains.,Configure Access Reviews in Azure AD to regularly review and remove unnecessary access to the App Service.,N/A,AI,Generic
P1-Identity-MEDIUM,Identity Management,MEDIUM,IM-6,template.json,181,App Service config does not specify use of Role-Based Access Control (RBAC) for access assignments. This is a security best practice recommendation.,Assign access rights to the App Service using Role-Based Access Control (RBAC) to limit privileges.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,181,"App Service config 'ipSecurityRestrictions' allows access from 'Any' IP address with action 'Allow', exposing the application to the public internet. This violates the requirement to secure all public endpoints.",Restrict 'ipSecurityRestrictions' to only allow trusted IP ranges. Remove or modify the rule that allows 'Any' to minimize public exposure.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,189,"App Service config 'scmIpSecurityRestrictions' allows access from 'Any' IP address with action 'Allow', exposing the SCM (deployment) endpoint to the public internet. This violates the requirement to secure all public endpoints.",Restrict 'scmIpSecurityRestrictions' to only allow trusted IP ranges. Remove or modify the rule that allows 'Any' to minimize public exposure.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,181,"App Service config 'publicNetworkAccess' is set to 'Enabled', allowing public network access. This does not use private endpoints as recommended for secure access.",Set 'publicNetworkAccess' to 'Disabled' and configure a private endpoint for the App Service to restrict access to private networks only.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,189,"App Service config 'publicNetworkAccess' is set to 'Enabled' for the SCM endpoint, allowing public network access. This does not use private endpoints as recommended for secure access.",Set 'publicNetworkAccess' to 'Disabled' for the SCM endpoint and configure a private endpoint to restrict access to private networks only.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,181,App Service config does not specify encryption at rest settings for application data. This violates the requirement to enable encryption at rest for all data storage.,Enable encryption at rest for App Service by configuring Azure Storage with encryption and ensuring all connected resources use encrypted storage.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,189,App Service SCM config does not specify encryption at rest settings for deployment data. This violates the requirement to enable encryption at rest for all data storage.,Enable encryption at rest for SCM data by configuring Azure Storage with encryption and ensuring all connected resources use encrypted storage.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,61,"App Service hostNameSslStates for 'onefuzz-daily-ui.azurewebsites.net' and 'onefuzz-daily-ui.scm.azurewebsites.net' have 'sslState' set to 'Disabled', which allows unencrypted HTTP connections. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates to enforce HTTPS. Ensure all endpoints require TLS 1.2 or higher.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,153,"App Service config includes 'publishingUsername' in plain text, which is sensitive information. This violates the requirement to store sensitive data like credentials in Azure Key Vault.",Remove 'publishingUsername' from the template and use Azure Key Vault references for sensitive credentials.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-5,template.json,181,App Service config does not specify backup and recovery settings for application data. This is a recommended best practice for critical data.,Configure backup and recovery for App Service by enabling regular backups and verifying restore procedures.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-5,template.json,189,App Service SCM config does not specify backup and recovery settings for deployment data. This is a recommended best practice for critical data.,Configure backup and recovery for App Service SCM by enabling regular backups and verifying restore procedures.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-6,template.json,181,App Service config does not specify use of customer-managed keys (CMK) for encryption at rest. This is a recommended best practice for sensitive data.,Configure App Service to use customer-managed keys (CMK) for encryption at rest by integrating with Azure Key Vault.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-6,template.json,189,App Service SCM config does not specify use of customer-managed keys (CMK) for encryption at rest. This is a recommended best practice for sensitive data.,Configure App Service SCM to use customer-managed keys (CMK) for encryption at rest by integrating with Azure Key Vault.,N/A,AI,Generic
P4-Access-HIGH,Access Management,HIGH,AM-1,template.json,181,App Service config does not specify assignment of least privilege access. This is a significant access control weakness.,Review and assign least privilege access to users and applications for the App Service.,N/A,AI,Generic
P4-Access-MEDIUM,Access Management,MEDIUM,AM-2,template.json,181,App Service config does not specify regular review of access rights. This is a security best practice recommendation.,Establish a process to regularly review access assignments for the App Service.,N/A,AI,Generic
P4-Access-MEDIUM,Access Management,MEDIUM,AM-3,template.json,181,App Service config does not specify use of Privileged Identity Management (PIM) for privileged access. This is a security best practice recommendation.,Implement Privileged Identity Management (PIM) in Azure AD for managing privileged access to the App Service.,N/A,AI,Generic
