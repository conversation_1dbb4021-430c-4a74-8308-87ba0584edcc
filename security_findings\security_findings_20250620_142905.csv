File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
Parameters\LacpBillingExhaust.Parameters-LacpBillingExhaust.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'usageAccountSystemAssignedIdentityPrincipalId' flows across template boundary,Secure parameter 'usageAccountSystemAssignedIdentityPrincipalId' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyVaultSuffix' flows across template boundary,Secure parameter 'keyVaultSuffix' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'regionalCosmosAccountLocation' flows across template boundary,Secure parameter 'regionalCosmosAccountLocation' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'dasStorageAccountKey' flows across template boundary,Secure parameter 'dasStorageAccountKey' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'globalKeyVaultName' flows across template boundary,Secure parameter 'globalKeyVaultName' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'createSharedKeyVault' flows across template boundary,Secure parameter 'createSharedKeyVault' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,40.0,"The parameter 'isBoundariesRestricted' is explicitly set to 'false' (Line 041), indicating that boundary restrictions are disabled. This configuration can allow public network access to Logic Apps and related resources, increasing the risk of initial access by external attackers. Without network boundary restrictions, attackers can directly target exposed endpoints, increasing the blast radius to all resources accessible via public endpoints.","Set 'isBoundariesRestricted' to 'true' to enforce private access points and restrict public network access. Additionally, configure private endpoints and disable public network access for all Logic Apps and associated resources to align with Azure Security Benchmark NS-2 guidance.",,,,ai_analysis,,Validated
Templates\IngestionStorageAccount.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,44.0,"The 'allowBlobPublicAccess' property is explicitly set to false on Line 044, which is correct. However, there is no configuration present in the template to restrict public network access or require private endpoints for the Microsoft.Storage/storageAccounts resources. Without explicit enforcement of private endpoints or disabling public network access, storage accounts may be exposed to the public internet, enabling attackers to attempt initial access, data exfiltration, or lateral movement. The blast radius includes all data stored in these accounts and any services integrated with them.","Explicitly configure the 'publicNetworkAccess' property to 'Disabled' and deploy private endpoints for all storage accounts. Add the following to the storage account resource properties: 'publicNetworkAccess': 'Disabled'. Additionally, create and associate a private endpoint for each storage account to ensure all access is via private network only.",,,,ai_analysis,,Validated
Templates\LacpGeo.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,225.0,"The CosmosDB resource at line 225 has 'publicNetworkAccess' set to 'Enabled', which allows access from the public internet. This configuration exposes the database to initial access attacks, increases the risk of data exfiltration, and expands the blast radius in the event of credential compromise. Attackers can directly target the CosmosDB endpoint, bypassing internal network controls.",Set 'publicNetworkAccess' to 'Disabled' in the CosmosDB resource configuration to restrict access to private endpoints only. Implement Azure Private Link and configure 'virtualNetworkRules' to allow access only from trusted VNets. Reference: NS-2 (Secure cloud services with network controls).,,,,ai_analysis,,Validated
Templates\LacpGeo.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,228.0,"The CosmosDB resource at line 228 has 'isVirtualNetworkFilterEnabled' set to 'false', which disables VNet-based access controls. This allows connections from any network, including the public internet, enabling lateral movement and increasing the risk of unauthorized access and data exfiltration.",Set 'isVirtualNetworkFilterEnabled' to 'true' and define 'virtualNetworkRules' to restrict access to only trusted VNets and subnets. This will enforce network boundaries and reduce the attack surface. Reference: NS-2 (Secure cloud services with network controls).,,,,ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,105.0,"CosmosDB resource 'publicNetworkAccess' is set to 'Enabled' on line 105. This allows the CosmosDB account to be accessed from the public internet, enabling initial access and data exfiltration attack vectors. Attackers can exploit this exposure to attempt brute force, credential stuffing, or exploit vulnerabilities, potentially compromising all data in the CosmosDB account. The blast radius includes all data and services dependent on this CosmosDB instance.","Set 'publicNetworkAccess' to 'Disabled' in the CosmosDB resource definition to restrict access to private endpoints only. Additionally, configure Private Link and virtual network rules to ensure only trusted networks can access the database. Reference: ASB NS-2.",,,,ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,108.0,"CosmosDB resource 'isVirtualNetworkFilterEnabled' is set to 'false' on line 108. This disables virtual network filtering, allowing connections from any network, including the public internet. Attackers can exploit this to access the database from untrusted networks, increasing the risk of data compromise and lateral movement.",Set 'isVirtualNetworkFilterEnabled' to 'true' and define appropriate 'virtualNetworkRules' to restrict access to trusted subnets only. This will ensure that only authorized networks can access the CosmosDB account. Reference: ASB NS-2.,,,,ai_analysis,,Validated
Templates\LacpRegion.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,561.0,"The CosmosDB account resource at line 558 has 'publicNetworkAccess' explicitly set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false. This configuration allows unrestricted public network access to the CosmosDB account, enabling attackers to attempt direct access from the internet, increasing the risk of data exfiltration and lateral movement. The blast radius includes all data stored in this CosmosDB account, and could allow initial access or privilege escalation if credentials are compromised.","Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to true for the CosmosDB account. Configure 'virtualNetworkRules' to allow only trusted subnets or private endpoints. This will restrict access to the CosmosDB account to only private network paths, reducing the attack surface as per Azure Security Benchmark NS-2.",,,,ai_analysis,,Validated
Templates\LacpRegion.Template.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,561.0,"The CosmosDB account resource at line 561 has 'isVirtualNetworkFilterEnabled' set to false, which means network traffic is not restricted to secure, private networks. Combined with 'publicNetworkAccess' enabled, this allows unencrypted or weakly protected data-in-transit paths, exposing sensitive data to interception or man-in-the-middle attacks.","Set 'isVirtualNetworkFilterEnabled' to true and enforce access via private endpoints or trusted VNets. Ensure all client connections use TLS 1.2 or higher. This will enforce encryption in transit and restrict data flows to secure channels, in line with Azure Security Benchmark DP-3.",,,,ai_analysis,,Validated
Templates\LacpRegion.Template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,558.0,"The CosmosDB account at line 558 is exposed to the public internet ('publicNetworkAccess': 'Enabled'), but there is no evidence of Azure Defender for CosmosDB or equivalent threat detection being enabled. This increases the risk of undetected data exfiltration or anomalous access patterns, as attackers could probe or exploit the account without triggering alerts.",Enable Azure Defender for CosmosDB to monitor for anomalous activities and data exfiltration attempts. Restrict public access and configure alerting for suspicious access patterns as per Azure Security Benchmark DP-2.,,,,ai_analysis,,Validated
Templates\LacpRegion.Template.json,DP-8,Data Protection,Ensure security of key and certificate repository,MEDIUM,558.0,"The Key Vault resource at line 705 does not specify 'publicNetworkAccess' or private endpoint configuration. Without explicit restriction, the Key Vault may be accessible from public networks, increasing the risk of unauthorized access to keys and secrets, which could compromise all dependent resources.","Explicitly set 'publicNetworkAccess' to 'Disabled' and configure a private endpoint for the Key Vault. Restrict access policies to least privilege and enable logging for all access attempts, following Azure Security Benchmark DP-8.",,,,ai_analysis,,Validated
Templates\LacpRegion.Template.json,DP-8,Data Protection,Ensure security of key and certificate repository,MEDIUM,803.0,"The Key Vault at line 801 has 'enabledForDeployment' and 'enabledForTemplateDeployment' set to true, which can increase the attack surface if not combined with strict access policies and network restrictions. This may allow broad access to secrets during deployments, increasing the risk of credential exposure.","Review and restrict 'enabledForDeployment' and 'enabledForTemplateDeployment' to only required scenarios. Ensure access policies are tightly scoped and network access is restricted to trusted sources, as recommended by Azure Security Benchmark DP-8.",,,,ai_analysis,,Validated
Templates\LacpRegion.Template.json,DP-8,Data Protection,Ensure security of key and certificate repository,MEDIUM,804.0,"The Key Vault at line 804 has 'enableSoftDelete' set to true, but there is no evidence of 'enablePurgeProtection' being set. Without purge protection, deleted keys and secrets can be permanently removed, increasing the risk of accidental or malicious data loss.","Set 'enablePurgeProtection' to true in the Key Vault properties to prevent permanent deletion of keys and secrets, ensuring recoverability and compliance with Azure Security Benchmark DP-8.",,,,ai_analysis,,Validated
Templates\LacpRegion.Template.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,312.0,"The customer configuration storage account at line 492 does not explicitly set 'allowSharedKeyAccess' or 'supportsHttpsTrafficOnly' to false/true, respectively. If 'supportsHttpsTrafficOnly' is not enforced, data in transit may be exposed to interception. If 'allowSharedKeyAccess' is true, shared keys could be used for insecure access.","Set 'supportsHttpsTrafficOnly' to true and 'allowSharedKeyAccess' to false for all storage accounts to enforce secure, encrypted connections and prevent shared key usage, as required by Azure Security Benchmark DP-3.",,,,ai_analysis,,Validated
Templates\LacpRegion.Template.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,312.0,"The data puller event hub storage account at line 345 does not specify 'allowSharedKeyAccess' or enforce 'supportsHttpsTrafficOnly'. This may allow insecure data-in-transit paths or shared key access, increasing the risk of interception or unauthorized access.","Explicitly set 'supportsHttpsTrafficOnly' to true and 'allowSharedKeyAccess' to false for the storage account. This ensures all data transfers are encrypted and shared key access is disabled, in line with Azure Security Benchmark DP-3.",,,,ai_analysis,,Validated
Templates\LacpRegion.Template.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,312.0,"The AMS backup storage account at line 364 does not specify 'allowSharedKeyAccess' or enforce 'supportsHttpsTrafficOnly'. This may allow insecure data-in-transit paths or shared key access, increasing the risk of interception or unauthorized access.","Explicitly set 'supportsHttpsTrafficOnly' to true and 'allowSharedKeyAccess' to false for the storage account. This ensures all data transfers are encrypted and shared key access is disabled, in line with Azure Security Benchmark DP-3.",,,,ai_analysis,,Validated
Templates\LacpStamp.Template.json,DP-8,Data Protection,Ensure security of key and certificate repository,MEDIUM,76.0,"The property 'value' on line 1062 directly injects the parameter 'dasStorageAccountKey' into a Key Vault secret. If 'dasStorageAccountKey' is provided as a raw secret value (not securely referenced from Key Vault or managed identity), this exposes sensitive credentials in deployment parameters, increasing the risk of credential exposure and lateral movement if the deployment pipeline or template is compromised.",Store the storage account key in Azure Key Vault and reference it securely using a managed identity or Key Vault reference. Remove direct secret injection from parameters and ensure all secrets are provisioned and accessed only via Key Vault. Update the template to use a secure reference pattern for 'dasStorageAccountKey'.,,,,ai_analysis,,Validated
Templates\RoleAssignment.Template.json,IM-2,Identity Management,Protect identity and authentication systems,HIGH,5.0,"The role assignment on line 72 grants the 'Contributor' role to the principal specified by 'Ev2BuildoutServicePrincipalId'. The Contributor role provides broad permissions, including the ability to modify resources, which can enable privilege escalation or lateral movement if the service principal is compromised. Without explicit restrictions or strong authentication controls, this increases the attack surface and blast radius for identity compromise.","Restrict the permissions granted to 'Ev2BuildoutServicePrincipalId' by assigning only the minimum required role instead of 'Contributor'. Enforce strong authentication (such as Azure AD MFA) for the service principal, monitor its activity, and use Privileged Identity Management (PIM) to limit standing access. Review and update the role assignment to follow least privilege principles as per Azure Security Benchmark IM-2.",,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 23,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 6,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T14:29:05.068656,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
