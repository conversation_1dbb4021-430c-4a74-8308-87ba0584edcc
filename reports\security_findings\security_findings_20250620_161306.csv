File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
network_demo.tf,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,24.0,"The 'source_address_prefix' property in the 'azurerm_network_security_group.demo' resource is set to '0.0.0.0/0' on line 24, allowing inbound SSH (port 22) from any IP address. This exposes the resource to the entire internet, enabling initial access, brute-force attacks, and lateral movement into the network. The blast radius includes any VM or resource associated with this NSG, potentially compromising all assets in the subnet.","Restrict 'source_address_prefix' to trusted IP ranges (e.g., corporate office or jumpbox IPs) instead of '0.0.0.0/0'. Implement a deny-by-default approach and only allow necessary management access from secure locations. Example: source_address_prefix = ""************/32"". Review and update NSG rules to minimize public exposure.",,,,ai_analysis,,Validated
network_demo.tf,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,43.0,"The 'allow_blob_public_access' property in the 'azurerm_storage_account.demo' resource is set to 'true' on line 43, enabling public anonymous access to blobs. This allows unauthenticated users on the internet to access data stored in the storage account, creating a direct data exfiltration vector and significantly increasing the blast radius of potential data exposure.",Set 'allow_blob_public_access' to 'false' to disable anonymous public access. Use private endpoints and restrict access to trusted networks or identities. Example: allow_blob_public_access = false. Review storage account network rules to ensure only authorized access is permitted.,,,,ai_analysis,,Validated
network_demo.tf,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,46.0,"The 'https_traffic_only' property in the 'azurerm_storage_account.demo' resource is set to 'false' on line 46, allowing unencrypted HTTP connections. This exposes sensitive data in transit to interception and man-in-the-middle attacks, enabling attackers to read or modify data between clients and the storage account.",Set 'https_traffic_only' to 'true' to enforce encryption for all data in transit. Example: https_traffic_only = true. Ensure all clients and applications use HTTPS endpoints for storage access.,,,,ai_analysis,,Validated
storage_demo.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,20.0,"The 'minimumTlsVersion' property is set to 'TLS1_0' for the storage account. This enables the use of deprecated and insecure TLS protocols, allowing attackers to exploit known vulnerabilities (e.g., POODLE, BEAST) to intercept or modify data in transit. The blast radius includes all data transferred to and from this storage account, potentially exposing sensitive information to network-based attacks.",Set 'minimumTlsVersion' to 'TLS1_2' or higher in the storage account properties to enforce strong encryption for data in transit. Example: minimumTlsVersion: 'TLS1_2'.,,,,ai_analysis,,Validated
storage_demo.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,23.0,"The 'supportsHttpsTrafficOnly' property is set to false, allowing HTTP (unencrypted) traffic to the storage account. This exposes all data in transit to interception and manipulation by attackers, enabling credential theft, session hijacking, and data exfiltration. The blast radius includes all clients and services communicating with this storage account over HTTP.",Set 'supportsHttpsTrafficOnly' to true in the storage account properties to enforce encrypted HTTPS connections. Example: supportsHttpsTrafficOnly: true.,,,,ai_analysis,,Validated
storage_demo.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,40.0,"The 'networkAcls.defaultAction' property is set to 'Allow', which permits public network access to the storage account from any source. This configuration enables attackers to access storage resources over the public internet, increasing the risk of unauthorized data access, brute-force attacks, and data exfiltration. The blast radius includes all data and services within the storage account.",Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only trusted networks or private endpoints. Example: defaultAction: 'Deny'. Configure 'virtualNetworkRules' or 'ipRules' as needed for legitimate access.,,,,ai_analysis,,Validated
storage_demo.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,19.0,"The storage account's 'networkAcls.defaultAction' is set to 'Allow', violating network segmentation best practices. This exposes the storage account to the entire internet, enabling lateral movement and initial access for attackers who can exploit public endpoints. The blast radius includes all data and workloads in the storage account, and may facilitate attacks on other resources in the same network.",Set 'networkAcls.defaultAction' to 'Deny' to enforce network segmentation. Only allow access from specific subnets or IP addresses using 'virtualNetworkRules' or 'ipRules'.,,,,ai_analysis,,Validated
storage_demo.bicep,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,17.0,"The 'allowBlobPublicAccess' property is set to true, enabling anonymous public access to blob data in the storage account. This allows attackers to enumerate, read, and exfiltrate sensitive data without authentication, significantly increasing the risk of data breaches. The blast radius includes all blobs in containers with public access enabled.",Set 'allowBlobPublicAccess' to false in the storage account properties to prevent anonymous public access. Example: allowBlobPublicAccess: false.,,,,ai_analysis,,Validated
storage_demo.bicep,NS-8,Network Security,Detect and disable insecure services and protocols,HIGH,26.0,"The storage account allows TLS1_0 via the 'minimumTlsVersion' property, which is an insecure protocol. Attackers can exploit weaknesses in TLS1_0 to decrypt or tamper with data in transit, bypassing encryption controls. This weakens the overall security posture and may allow defense evasion.",Update 'minimumTlsVersion' to 'TLS1_2' or higher to disable insecure protocols. Example: minimumTlsVersion: 'TLS1_2'.,,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 9,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 0,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T16:13:06.983430,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
