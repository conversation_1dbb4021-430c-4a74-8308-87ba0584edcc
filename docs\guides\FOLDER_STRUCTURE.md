# IaC Guardian Project Structure

This document explains the organized folder structure of the IaC Guardian project.

## Directory Overview

### `/src/` - Source Code
Contains all the main application source code, organized by functionality:

#### `/src/core/` - Core Application Modules
- `security_opt.py` - Main security analysis engine and entry point
- `security_pr_review.py` - Pull request review functionality
- `enhanced_resource_control_mappings.py` - Enhanced resource-to-control mapping logic
- `template_parameter_expander.py` - Template parameter expansion and resolution
- Other core utility modules

#### `/src/analysis/` - Security Analysis Modules
- `azure_security_benchmark_patterns.py` - Security pattern detection
- `get_security_controls.py` - Security controls retrieval and processing

#### `/src/reporting/` - Report Generation
- `generate_html_report.py` - HTML report generation with Glass UI

#### `/src/mcp/` - MCP Server Implementation
- `mcp_server.py` - Model Context Protocol server for AI tool integration

### `/tests/` - Test Files
All test files organized by test type:

#### `/tests/unit/` - Unit Tests
- Individual module tests
- Debug scripts and utilities
- Test runners

#### `/tests/integration/` - Integration Tests
- End-to-end testing scenarios
- Cross-module integration tests

### `/docs/` - Documentation
Project documentation organized by type:

#### `/docs/guides/` - User Guides and Tutorials
- User guides and how-to documentation
- Configuration guides
- Best practices

#### `/docs/architecture/` - Architecture Documentation
- System architecture documents
- Technical specifications
- Business logic documentation

### `/config/` - Configuration Files
- `requirements.txt` - Python dependencies
- `requirements-mcp.txt` - MCP-specific dependencies
- `requirements-mcp-minimal.txt` - Minimal MCP dependencies
- `mcp_config.json` - MCP server configuration

### `/data/` - Data Files
Static data files and mappings:

#### `/data/benchmarks/` - Azure Security Benchmark Data
- CSV files with security controls
- JSON benchmark definitions
- Excel benchmark files

#### `/data/mappings/` - Resource-Control Mappings
- JSON mapping files
- Resource type definitions
- Control correlation data

### `/reports/` - Generated Reports
Output directory for all generated reports:

#### `/reports/security_findings/` - Security Analysis Reports
- HTML and CSV security reports
- Timestamped analysis results

#### `/reports/demo/` - Demo Reports
- Sample reports for demonstration
- Glass UI design examples

#### `/reports/archived/` - Archived Reports
- Historical reports
- Test reports
- Backup reports

### `/scripts/` - Utility Scripts
Helper scripts organized by purpose:

#### `/scripts/setup/` - Setup and Installation Scripts
- Installation helpers
- Configuration setup scripts
- Environment setup utilities

#### `/scripts/utilities/` - Utility Scripts
- Diagnostic tools
- Validation scripts
- Maintenance utilities

### Other Directories

- `/demo_files/` - Sample IaC files for testing and demonstration
- `/test_code_snippets/` - Code snippets used in testing
- `/assets/` - Static assets (images, icons, etc.)
- `/database/` - Database schemas and SQL files
- `/master_controls_db/` - Master controls database system (separate component)
- `/IacG/` - Python virtual environment (if using venv)

## Import Path Changes

With the new structure, import statements have been updated:

### Before (Old Structure)
```python
from enhanced_resource_control_mappings import EnhancedResourceControlMapper
from template_parameter_expander import TemplateParameterExpander
```

### After (New Structure)
```python
from src.core.enhanced_resource_control_mappings import EnhancedResourceControlMapper
from src.core.template_parameter_expander import TemplateParameterExpander
```

## Running the Application

### Main Security Analysis
```bash
python src/core/security_opt.py --local-folder path/to/iac/files
```

### MCP Server
```bash
python src/mcp/mcp_server.py
```

### Tests
```bash
python tests/unit/run_tests.py
```

## Benefits of New Structure

1. **Clear Separation of Concerns**: Each directory has a specific purpose
2. **Easier Navigation**: Logical grouping makes finding files easier
3. **Better Maintainability**: Organized structure supports long-term maintenance
4. **Scalability**: Easy to add new modules in appropriate directories
5. **Professional Structure**: Follows Python project best practices
6. **Cleaner Root Directory**: Reduced clutter in the main directory

## Migration Notes

- All core functionality remains the same
- Import paths have been updated in existing files
- Configuration files moved to `/config/` directory
- Reports now generate in organized `/reports/` subdirectories
- Tests are properly organized by type
- Documentation is centralized in `/docs/`
