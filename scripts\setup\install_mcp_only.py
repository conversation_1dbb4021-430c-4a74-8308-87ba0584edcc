#!/usr/bin/env python3
"""
Quick installer for just the MCP dependency.
Use this if the full setup fails due to dependency conflicts.
"""

import subprocess
import sys

def install_mcp():
    """Install only the MCP library."""
    print("📦 Installing MCP library only...")
    
    try:
        # Install just the MCP library
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "mcp>=1.0.0"
        ])
        print("✅ MCP library installed successfully")
        
        # Test import
        try:
            import mcp
            print("✅ MCP import test successful")
        except ImportError as e:
            print(f"❌ MCP import failed: {e}")
            return False
        
        # Test security_opt import
        try:
            import security_opt
            print("✅ IaC Guardian security_opt module found")
        except ImportError as e:
            print(f"⚠️ Warning: security_opt import failed: {e}")
            print("Make sure you're in the IaC Guardian directory")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing MCP: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Quick MCP Installation")
    print("=" * 40)
    
    if install_mcp():
        print("\n🎉 MCP installation completed!")
        print("\nNext steps:")
        print("1. Configure VS Code settings manually (see MCP_SETUP_GUIDE.md)")
        print("2. Test with: python test_mcp.py")
        print("3. Use in VS Code Copilot Chat")
    else:
        print("\n❌ Installation failed")
        print("Try manual installation: pip install mcp")
        sys.exit(1)
