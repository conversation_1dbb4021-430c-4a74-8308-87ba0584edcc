# Control Verification Tool

This tool verifies that all control IDs from the Azure Security Benchmark are being referenced in the security analysis recommendations generated by `security_pr_review.py`.

## Purpose

When running security analysis on Azure infrastructure code, it's important to ensure that:
1. All security controls from the Azure Security Benchmark are being considered
2. The AI and pattern-based detection systems are covering the full scope of security requirements
3. No critical security controls are being missed in the analysis

## Files

- **`verify_controls.py`** - Main verification script
- **`test_control_verification.py`** - Test script demonstrating the workflow
- **`README_control_verification.md`** - This documentation

## Usage

### Basic Usage

```bash
python verify_controls.py <benchmark_file> <analysis_output>
```

Example:
```bash
python verify_controls.py \
  SecurityBenchmarks/Azure_Security_Benchmark_v3.json \
  analysis_output.json
```

### Command Line Options

- `benchmark_file` - Path to the Azure Security Benchmark JSON file
- `analysis_output` - Path to the JSON file containing security findings from `security_pr_review.py`
- `--export-missing` - Export missing controls to a JSON file for further analysis
- `--verbose` - Show detailed output including all control IDs

### Examples

1. **Basic verification:**
   ```bash
   python verify_controls.py benchmark.json findings.json
   ```

2. **Verbose output with missing controls export:**
   ```bash
   python verify_controls.py benchmark.json findings.json \
     --verbose \
     --export-missing missing_controls.json
   ```

3. **Running the test workflow:**
   ```bash
   python test_control_verification.py
   ```

## Output

The tool provides:

1. **Coverage Statistics:**
   - Total controls in benchmark
   - Controls covered in analysis
   - Coverage percentage

2. **Missing Controls Report:**
   - Lists all control IDs not referenced in any recommendations
   - Groups missing controls by security domain
   - Shows control names for easier identification

3. **Extra Controls:**
   - Identifies control IDs in recommendations that aren't in the benchmark
   - Helps identify potential typos or custom controls

## Integration with CI/CD

The script exits with:
- **Exit code 0** - All controls are covered
- **Exit code 1** - Some controls are missing

This makes it suitable for CI/CD pipelines:

```yaml
# Azure DevOps Pipeline Example
- script: |
    python verify_controls.py \
      SecurityBenchmarks/Azure_Security_Benchmark_v3.json \
      $(Build.ArtifactStagingDirectory)/analysis_output.json
  displayName: 'Verify Security Control Coverage'
  continueOnError: false
```

## Workflow Example

1. **Run security analysis:**
   ```bash
   python security_pr_review.py --repo-id myrepo --pr-id 123
   ```

2. **Capture the findings** (modify `security_pr_review.py` to save findings):
   ```python
   # In analyze_files method, add:
   with open('analysis_output.json', 'w') as f:
       json.dump(findings, f, indent=2)
   ```

3. **Verify control coverage:**
   ```bash
   python verify_controls.py \
     SecurityBenchmarks/Azure_Security_Benchmark_v3.json \
     analysis_output.json
   ```

4. **Review missing controls:**
   ```bash
   cat missing_controls.json
   ```

## Interpreting Results

### Good Coverage (>90%)
```
📊 Coverage Statistics:
  • Total controls in benchmark: 50
  • Controls covered in analysis: 48
  • Coverage percentage: 96.0%
```

### Poor Coverage (<70%)
```
⚠️  Missing Controls: 18 controls not referenced
  Control IDs not appearing in any recommendations:

  Identity Management:
    • IM-1: Use Azure Active Directory for Identity Management
    • IM-2: Enable Multi-Factor Authentication (MFA)
    ...
```

## Troubleshooting

1. **"Benchmark file not found"**
   - Ensure the Azure Security Benchmark JSON exists at the specified path
   - Run `security_pr_review.py` first to generate the benchmark file

2. **"No control IDs found in analysis"**
   - Check that the analysis output JSON contains findings with `control_id` fields
   - Verify the JSON structure matches expected format

3. **Low coverage percentage**
   - Review which controls are missing
   - Check if the file types being analyzed match the control requirements
   - Consider if AI prompts need adjustment to reference more controls

## Extending the Tool

To add custom verification logic:

1. **Custom control mappings:**
   ```python
   # In verify_controls.py
   def map_custom_controls(control_id):
       mappings = {
           'CUSTOM-1': 'NS-1',  # Map custom to benchmark
       }
       return mappings.get(control_id, control_id)
   ```

2. **Severity-based filtering:**
   ```python
   # Only check HIGH and CRITICAL controls
   high_priority_controls = [
       c for c in benchmark_data['controls'] 
       if c.get('severity') in ['HIGH', 'CRITICAL']
   ]
   ```

3. **Resource-specific verification:**
   ```python
   # Check coverage for specific resource types
   storage_controls = [
       c for c in benchmark_data['controls']
       if 'Storage' in c.get('resource_types', [])
   ]
   ```

## Best Practices

1. **Regular Verification**
   - Run control verification as part of your CI/CD pipeline
   - Monitor coverage trends over time

2. **Baseline Coverage**
   - Establish a minimum acceptable coverage percentage (e.g., 85%)
   - Fail builds if coverage drops below baseline

3. **Control Prioritization**
   - Focus on HIGH and CRITICAL severity controls first
   - Ensure 100% coverage for compliance-required controls

4. **Continuous Improvement**
   - Review missing controls regularly
   - Update pattern detection and AI prompts to improve coverage
   - Add new patterns for commonly missed controls

## Support

For issues or questions:
1. Check the logs for detailed error messages
2. Verify JSON file formats match expected structure
3. Ensure all dependencies are installed (`pip install -r requirements.txt`)
