Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
CRITICAL,NS-2,IngestionStorageAccount.Template.json,37.0,"The template does not restrict public endpoints for the storage accounts. When network rules are not defined, storage accounts may be accessible via public endpoints, increasing risk of exposure.","Explicitly configure 'networkAcls' to deny public network access by setting 'defaultAction' to 'Deny', and limit access to private endpoints or specific IPs.",N/A,AI,Generic
CRITICAL,NS-1,LacpGeo.Template.json,160.0,"The CosmosDB resource (Microsoft.DocumentDB/databaseAccounts) has ""publicNetworkAccess"" set to ""Enabled"" and ""isVirtualNetworkFilterEnabled"" set to false. No virtual network rules or NSGs are defined to limit access. This exposes the CosmosDB instance to the public internet, violating requirements for protecting critical resources.","Set ""publicNetworkAccess"" to ""Disabled"" or enable virtual network filtering (""isVirtualNetworkFilterEnabled"": true) and define appropriate virtualNetworkRules restricting access to trusted subnets. Implement NSGs where appropriate to further restrict access.",N/A,AI,Generic
CRITICAL,NS-2,LacpGeo.Template.json,160.0,"CosmosDB is deployed with public network access enabled (""publicNetworkAccess"": ""Enabled"") and no IP allowlist (""ipRules"": []). This exposes the database to all public IPs with no restriction, increasing risk of network-based attacks or unauthorized access.","Configure CosmosDB to use private endpoints or at minimum restrict public access via IP allowlist. Set ""publicNetworkAccess"" to ""Disabled"" and define private endpoints and/or IP rules to allow only known trusted sources.",N/A,AI,Generic
CRITICAL,NS-13,LacpGeo.Template.json,160.0,"No private endpoints (Microsoft.Network/privateEndpoints) are provisioned for CosmosDB or Key Vault. Relying solely on public endpoints exposes these sensitive resources to broader Azure network attack surface, violating the principle that access to such critical services should be restricted via private networking.","Provision Azure Private Endpoints for CosmosDB and Key Vault, associate them with the correct VNets/subnets, and enforce that access is only allowed through these endpoints.",N/A,AI,Generic
CRITICAL,NS-2,LacpGlobal.Template.json,49.0,"The CosmosDB account enables 'publicNetworkAccess' (""publicNetworkAccess"": ""Enabled"") and explicitly disables virtual network filters ('isVirtualNetworkFilterEnabled': false), meaning the database is exposed to the public internet without restriction. This poses a high risk of unauthorized access.",Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to true to restrict access to private endpoints or trusted networks only. Use Azure Private Endpoints where possible.,N/A,AI,Generic
CRITICAL,NS-2,LacpGlobal.Template.json,133.0,"Key Vault resource does not specify 'networkAcls' or private endpoint settings and leaves default network access posture, allowing public access by default. This exposes secrets to potential breaches from the public network.","Set 'networkAcls.bypass' to 'AzureServices' or 'None', add explicit 'networkAcls' to restrict access to trusted VNets, and/or configure Private Endpoints for Key Vault.",N/A,AI,Generic
CRITICAL,NS-1,LacpRegion.Template.json,1070.0,"The CosmosDB account ('Microsoft.DocumentDB/databaseAccounts') is configured with 'publicNetworkAccess': 'Enabled' and 'isVirtualNetworkFilterEnabled': false, exposing the database to the public internet without any network restrictions or private endpoints, in violation of network security best practices.","Set 'isVirtualNetworkFilterEnabled' to true, restrict access with virtual network rules, and/or disable public network access. Consider using private endpoints to ensure only trusted networks can reach the CosmosDB resource.",N/A,AI,Generic
CRITICAL,NS-2,LacpRegion.Template.json,1070.0,"CosmosDB account is exposed with public network access ('publicNetworkAccess': 'Enabled'), making it accessible from the public internet, which increases the risk of data exposure or exploitation.",Set 'publicNetworkAccess' to 'Disabled' to block all public network access. Use private endpoints or configured IP rules to explicitly authorize trusted sources.,N/A,AI,Generic
CRITICAL,NS-14,LacpRegion.Template.json,1070.0,"No 'privateEndpointConnections' or private endpoint configuration is present on the CosmosDB resource, which means connectivity is not limited to private Azure networks.",Configure a private endpoint for the CosmosDB account to restrict network access to Azure virtual networks only.,N/A,AI,Generic
HIGH,NS-1,IngestionStorageAccount.Template.json,37.0,"The storage account resources lack network rules restricting access (e.g., no 'networkAcls' property is specified to restrict access to specific virtual networks or selected IP ranges). Without such rules, the account could be exposed to the public internet, violating the need for network security boundaries.",Add the 'networkAcls' property to each storage account resource to restrict access to trusted virtual networks and/or specific IP addresses. Consider leveraging private endpoints for additional protection.,N/A,AI,Generic
HIGH,NS-1,LacpBilling.Template.json,0.0,"The Storage Account resources do not have any network security controls such as Virtual Network (VNet) integration or private endpoints. No NSGs, service endpoints, or firewall rules restricting access are defined, potentially exposing storage accounts to the public internet.","Use network rules to restrict Storage Account access only to required networks and trusted services. Configure Storage Account firewall settings to allow only selected VNets/subnets or private endpoints, and deny public network access. Apply NSGs at the subnet level where storage endpoints reside.",N/A,AI,Generic
HIGH,NS-2,LacpBilling.Template.json,0.0,"Storage Accounts may be exposed via public endpoints because the template does not explicitly restrict public network access, nor does it provision private endpoints. This increases the attack surface.",Set the Storage Account property 'publicNetworkAccess' to 'Disabled' to prevent public access to storage. Implement private endpoints to access Storage Accounts securely over Azure's backbone.,N/A,AI,Generic
HIGH,IM-13,LacpBilling.Template.json,0.0,Role assignments for both the Usage Billing Account and Storage Accounts are created and directly reference a user-assigned managed identity (UAMI) and use hard-coded role definition IDs. There is no process or parameterization ensuring that only necessary permissions are granted for these roles. Overbroad role assignments may result in unnecessary privilege escalation.,"Carefully review and enforce least-privilege RBAC role assignments. Where possible, restrict the scope to only what is required (resource or resource group-level), and parameterize/validate role assignment IDs and principals. Regularly audit and attest assigned roles.",N/A,AI,Generic
HIGH,NS-2,LacpBillingExhaust.Template.json,28.0,"The Kusto (Azure Data Explorer) cluster is deployed without any network restrictions such as private endpoints, IP allowlists, VNet integration, or restricted firewall rules. This can result in the cluster being accessible from the public internet, violating the requirement to protect public endpoints.","Restrict public access to the Kusto cluster by enabling VNet integration or Private endpoints, setting up firewall rules to allow access only from trusted IP ranges, and/or disabling public network access completely.",N/A,AI,Generic
HIGH,NS-1,LacpBillingExhaust.Template.json,28.0,There are no network security groups (NSGs) or Azure Firewall configured for the Kusto cluster or related resources. This exposes the resource to unfiltered inbound connections and potential unauthorized access over the network.,Deploy the Kusto cluster within a subnet protected by NSGs or behind an Azure Firewall. Define rules to allow only required traffic from trusted sources.,N/A,AI,Generic
HIGH,DP-3,LacpBillingExhaustExport.Template.json,12.0,"Sensitive information such as 'adxExhaustDataIngestionUri' and 'adxExhaustUri' are passed as plain string parameters. These may contain secrets, connection strings, or sensitive endpoints, stored in plaintext within the deployment template or parameter file. This violates the requirement to manage sensitive information disclosure.","Store sensitive parameters containing secrets, connection strings, or URIs in an Azure Key Vault and reference them using secure parameters/Key Vault references in the ARM template. Avoid including sensitive values directly or as plain type parameters.",N/A,AI,Generic
HIGH,NS-2,LacpBillingExhaustExport.Template.json,56.0,"Potential public exposure of Azure Data Explorer (ADX) endpoints ('adxExhaustUri' and 'adxExhaustDataIngestionUri') as there are no restrictions, private endpoint configuration, or firewall rules specified. This increases the risk of data exfiltration or unauthorized access to sensitive data.","Ensure that ADX endpoints used in the template are restricted to private endpoints or virtual networks, and configure firewall rules to allow only trusted connections. Do not expose ingestion or data query URIs to public networks unless required and mitigate risk with additional controls.",N/A,AI,Generic
HIGH,NS-14,LacpGeo.Template.json,160.0,"CosmosDB is deployed without virtual network service endpoints (""isVirtualNetworkFilterEnabled"": false) and no virtualNetworkRules are defined. Service endpoints help secure traffic between VNets and Azure services.","Enable virtual network filtering (""isVirtualNetworkFilterEnabled"": true) for CosmosDB and define appropriate ""virtualNetworkRules"" that allow access only from trusted and required subnets.",N/A,AI,Generic
HIGH,DP-6,LacpGeo.Template.json,160.0,"The CosmosDB configuration does not reference or enable customer-managed keys (CMK) for encryption at rest. Without CMK, the organization cannot control or rotate its own encryption keys for this sensitive database.",Update the CosmosDB resource properties to reference a Key Vault and configure customer-managed keys (CMK) for at-rest encryption.,N/A,AI,Generic
HIGH,NS-8,LacpGeo.Template.json,48.0,"The Key Vault is deployed without any network-level restrictions. By default, this allows access from any source, including the public internet, unless explicitly restricted.",Enable Key Vault firewall and virtual network rules to restrict access to trusted networks or set up Private Endpoints for the Key Vault.,N/A,AI,Generic
HIGH,DP-2,LacpGeo.Template.json,162.0,"CosmosDB resource defers ""minimalTlsVersion"" to a parameter (""REQUIRED_PARAM_minimalCosmosDbTlsVersion""). If this is not explicitly set to at least 'Tls1_2', encryption in transit may not meet benchmark requirements.","Explicitly set the ""minimalTlsVersion"" property to at least 'Tls1_2' for CosmosDB. Validate the parameter at deployment time to disallow insecure protocols.",N/A,AI,Generic
HIGH,NS-5,LacpGlobal.Template.json,49.0,"CosmosDB public exposure without Network Security Group or Private Endpoint. The resource does not use Private Endpoints, leaving traffic exposed to the public internet.","Configure and require a Private Endpoint for the CosmosDB account to ensure all access occurs over the Azure backbone, not the public internet.",N/A,AI,Generic
HIGH,NS-6,LacpGlobal.Template.json,49.0,CosmosDB is not configured with service endpoints or virtual network filtering. This exposes CosmosDB to traffic from any network.,Enable virtual network filtering and configure Azure Virtual Network Service Endpoints to restrict access to trusted VNets only.,N/A,AI,Generic
HIGH,NS-5,LacpGlobal.Template.json,133.0,"Key Vault is accessible without a Private Endpoint or networkAcls restrictions, making it reachable from any public endpoint.",Configure a Private Endpoint and/or restrict public network access to Key Vault. Apply 'networkAcls' to permit only necessary traffic from trusted subnets.,N/A,AI,Generic
HIGH,NS-2,LacpGlobal.Template.json,110.0,"Storage Accounts do not specify 'networkAcls' or private endpoints and have default network access (no restriction), which makes the accounts accessible from the public internet.",Restrict public network access to the storage account by configuring 'networkAcls' to allow only trusted virtual network subnets or by requiring Private Endpoints.,N/A,AI,Generic
HIGH,NS-1,LacpRegion.Template.json,1097.0,"Key Vault ('Microsoft.KeyVault/vaults') does not specify 'networkAcls' to restrict access and defaults to being accessible from any address (public internet). By default, without network ACLs or private endpoint configuration, Key Vault can be reached over the internet.",Add 'networkAcls' to restrict access to specific virtual networks/subnets and consider adding private endpoints to remove public internet exposure.,N/A,AI,Generic
HIGH,NS-2,LacpRegion.Template.json,1097.0,Azure Key Vault is not network restricted (no 'networkAcls') and may be accessible publicly. Sensitive secrets and keys could be exposed to the public internet.,Configure 'networkAcls' on the Key Vault to only permit traffic from trusted VNets/subnets and set 'defaultAction' to 'Deny'. Deploy a private endpoint and disable public network access.,N/A,AI,Generic
HIGH,NS-14,LacpRegion.Template.json,1097.0,"Azure Key Vault is missing a private endpoint configuration, risking exposure over the public internet.",Implement a private endpoint for the Key Vault to ensure it is only accessible over private Azure networks.,N/A,AI,Generic
HIGH,NS-1,LacpRegion.Template.json,672.0,"Storage accounts are deployed without explicit network restrictions (such as 'networkAcls', 'virtualNetworkRules', or private endpoint resources). By default, storage accounts allow access from all networks unless explicitly restricted.",Add 'networkAcls' to explicitly restrict storage account access to trusted networks. Use private endpoints to prevent public access.,N/A,AI,Generic
HIGH,NS-2,LacpRegion.Template.json,672.0,"Storage accounts do not have 'networkAcls' defined and there are no private endpoints, meaning they may be publicly accessible if not otherwise restricted at account or tenant level.","Configure 'networkAcls' to block public access, only allowing approved Azure services/networks, and require the use of private endpoints for all storage account access.",N/A,AI,Generic
HIGH,NS-14,LacpRegion.Template.json,672.0,"Storage accounts lack private endpoint configurations, which is recommended for eliminating public exposure for storage services.",Add private endpoints for each storage account to ensure all access occurs over private connections.,N/A,AI,Generic
HIGH,DP-12,LacpRegion.Template.json,1070.0,"The CosmosDB databaseAccount does not specify 'keyVaultKeyUri' or any parameterization for Customer-Managed Key (CMK) encryption, defaulting to Microsoft-managed keys. This does not meet the requirement for customer-controlled encryption for highly sensitive data.",Configure CosmosDB to use customer-managed keys (CMK) by referencing a Key Vault key in the 'keyVaultKeyUri' property.,N/A,AI,Generic
HIGH,DP-12,LacpRegion.Template.json,672.0,Storage Accounts do not specify customer-managed keys (CMK) but instead default to Microsoft-managed encryption keys. Highly sensitive storage data should allow for customer control over encryption keys.,Enable and configure CMK encryption using Azure Key Vault by setting the appropriate properties in the storage accounts.,N/A,AI,Generic
HIGH,DP-3,LacpStamp.Parameters-LacpStampResources.json,64.0,"The parameter 'dasStorageAccountKey' is populated using a cross-template reference, which may result in the raw storage account key being exposed in clear text in parameters. Storing or passing sensitive credentials (such as keys) outside of a secure secret store like Azure Key Vault increases risk of exposure.",Store and reference storage account keys securely from Azure Key Vault. Avoid passing sensitive credentials as plain parameters. Use Key Vault references instead.,N/A,AI,Generic
HIGH,NS-1,LacpStamp.Template.json,0.0,"The template does not deploy or reference any Network Security Groups (NSGs) or Azure Firewall resources to protect Storage Accounts, Key Vaults, or other sensitive components. This violates the requirement to protect sensitive resources with NSGs or firewalls.","Add NSGs and/or Azure Firewall to control network traffic to and from critical resources, and associate them with relevant subnets or resources. For example, ensure Storage Accounts and Key Vaults can be accessed only from trusted sources.",N/A,AI,Generic
HIGH,NS-2,LacpStamp.Template.json,0.0,"Multiple Storage Accounts and Key Vaults are created without any explicit restriction of their public endpoints (no 'networkAcls' property, no private endpoint configuration, and no service endpoints are defined). This can potentially expose critical services to the public internet.",Restrict public access to Storage Accounts and Key Vaults by configuring their 'networkAcls' property to allow traffic only from selected virtual networks or use private endpoints.,N/A,AI,Generic
HIGH,NS-5,LacpStamp.Template.json,0.0,"None of the Storage Accounts or Key Vaults use private endpoints, which are required for secure access to these resources without traversing the public internet.","Define and associate private endpoints with all Storage Account and Key Vault resources to ensure private, secured access.",N/A,AI,Generic
HIGH,NS-7,LacpStamp.Template.json,0.0,"There is no evidence of any Network Security Groups being created or assigned to resources or subnets, so inbound and outbound traffic control is not enforced.",Implement and associate appropriate NSGs with subnets or directly with resources to allow only required traffic.,N/A,AI,Generic
HIGH,AM-1,LacpStamp.Template.json,0.0,"Several role assignments grant broad or powerful permissions (e.g., 'Data Owner' on Redis, Key Vault access policies with extensive permissions) to managed identities. This risks violating least privilege and increases the attack surface.",Review all role assignments and access policies. Limit permissions strictly to those required by applications/processes. Avoid granting owner/contributor/data owner roles unless absolutely necessary.,N/A,AI,Generic
HIGH,IM-8,LacpStamp.Template.json,0.0,"Some managed identities are used, but not all principal assignments are managed identities. Some principal assignments rely on AAD objects/service principals; these should be transitioned to managed identities for resource-to-resource authentication wherever feasible.",Adopt managed identities in place of direct service principal/AAD assignments for all resource-to-resource authentication scenarios.,N/A,AI,Generic
HIGH,NS-1,ReadAdxExhaust.Template.json,21.0,"The Microsoft.Kusto/cluster resource does not specify any network restrictions such as network security groups (NSGs), private endpoints, or integration with Azure Firewall. By default, Kusto clusters are deployed with a public endpoint, potentially exposing the resource to the internet.","Restrict cluster access by enabling private endpoints, configuring virtual network integration, or associating the cluster with properly scoped NSGs or Azure Firewall rules to limit public exposure.",N/A,AI,Generic
HIGH,NS-2,ReadAdxExhaust.Template.json,21.0,No configuration is provided to secure or restrict public endpoints for the Microsoft.Kusto/cluster resource. This may expose the cluster's endpoint to the public internet and increase the risk of unauthorized access.,"Configure the cluster to use private endpoints and disable the public endpoint unless explicitly required. If the public endpoint is required, restrict access to trusted IP addresses only.",N/A,AI,Generic
HIGH,IM-8,ReadUsageAccount.Template.json,13.0,"The UsageBilling account resource does not have a system-assigned managed identity configured, despite the variable 'enableSystemAssignedIdentity' being set to true. The properties or identity block for enabling managed identity is missing from the resource definition, contrary to best practices for secure resource-to-resource authentication.","Add the 'identity' block with type 'SystemAssigned' to the UsageBilling account resource definition to enable a managed identity, as recommended by ASB IM-8.",N/A,AI,Generic
HIGH,IM-6,RoleAssignment.Template.json,52.0,"The template assigns the 'Contributor' role to the Ev2 Buildout Service Principal at the subscription scope. The Contributor role grants broad permissions, including management of all resources (except access control), which increases the attack surface and does not follow the principle of least privilege.","Assign a more restrictive, custom role tailored to the minimum permissions required for the Ev2 Buildout Service Principal. Limit scope to the specific resources or resource groups needed, rather than the entire subscription.",N/A,AI,Generic
HIGH,AM-1,RoleAssignment.Template.json,52.0,The template grants Contributor access to a service principal over the subscription. Granting such broad permissions to a service principal presents a significant risk if the identity or its credentials are compromised.,"Review and reduce the scope of the role assignment. Use a least privilege, custom RBAC role with only the required permissions, and assign at the most granular resource scope possible (e.g., resource group or specific resource), not the subscription.",N/A,AI,Generic
HIGH,NS-2,TrafficManagerEndpoints.Template.json,46.0,"The template defines external Traffic Manager endpoints using 'Microsoft.Network/trafficManagerProfiles/externalEndpoints', which are by nature public. There is no evidence of access restrictions, IP allowlists, or security controls to minimize the exposure of these public endpoints as recommended by ASB NS-2.","Restrict access to public endpoints by implementing client IP allowlists, traffic authentication, or using private endpoints wherever possible. Leverage Traffic Manager features such as endpoint monitoring, and evaluate if these endpoints can be internal or require network restrictions via fronting services or firewalls.",N/A,AI,Generic
MEDIUM,NS-3,IngestionStorageAccount.Template.json,37.0,No mention or configuration of Network Security Groups (NSGs) is present for securing traffic to services that may interact with these storage accounts.,"When integrating storage with virtual networks, ensure relevant subnets have appropriate Network Security Groups (NSGs) attached to restrict and monitor traffic.",N/A,AI,Generic
MEDIUM,IM-1,IngestionStorageAccount.Template.json,37.0,"The template does not specify or enforce Azure Active Directory (AAD) integration for storage account authentication (e.g., Azure AD-DS integration for blob/file access, or Azure AD RBAC for management).",Enable Azure AD integration for the storage accounts to enforce identity-based access control and reduce reliance on account keys.,N/A,AI,Generic
MEDIUM,DP-3,IngestionStorageAccount.Template.json,37.0,"No configuration for secure storage of sensitive secrets, such as access keys or connection strings, in Azure Key Vault. While no secrets appear directly in the template, there is no evidence that access keys are being securely managed.",Use Azure Key Vault to securely manage and reference storage account keys and connection strings. Never embed secrets in templates or parameter files.,N/A,AI,Generic
MEDIUM,NS-7,LacpBilling.Template.json,0.0,There are no Network Security Groups (NSGs) applied—neither at subnet level nor referenced in any resource—in the template. NSGs help control and restrict traffic even when resources are only accessible from within the VNet.,Implement NSGs on the relevant subnets (if using VNets) that might access the Storage Accounts or any deployed VMs/services to restrict traffic to what is necessary.,N/A,AI,Generic
MEDIUM,IM-8,LacpBillingExhaust.Template.json,61.0,"Principal assignments for database access use explicit principal IDs with 'principalType' set to 'App', but there is no evidence of managed identities being referenced as principals. Directly assigning access to applications without using managed identities is less secure as it may involve managing secrets or certificates externally.","Assign access to the database resources using managed identities where possible, which enables secure, credential-free authentication for Azure resources.",N/A,AI,Generic
MEDIUM,AM-1,LacpBillingExhaust.Template.json,61.0,Principal assignments grant 'User' and 'Ingestor' roles at the database level to entire application principals without clear evidence of the principle of least privilege being followed. Over-permissioned assignments may allow broader access than necessary.,Review the assigned roles and ensure that each principal is granted only the minimum permissions required for their intended operation within the cluster.,N/A,AI,Generic
MEDIUM,DP-1,LacpBillingExhaust.Template.json,28.0,"There is no explicit configuration for encryption at rest for the Kusto cluster or database in the template. While encryption at rest is enabled by default for Azure Data Explorer, the absence of customer-managed keys (CMK) configuration means data is protected only with platform-managed keys.","Configure customer-managed keys (CMK) for the cluster to enhance encryption-at-rest controls, ensuring compliance with stricter security policies if required by your organization.",N/A,AI,Generic
MEDIUM,NS-1,LacpBillingExhaustExport.Template.json,56.0,"The template does not specify any network security controls, access restrictions, or private endpoints for the Data Export resources, potentially exposing sensitive billing or diagnostic data. There is no evidence of NSG, firewall, or network isolation applied to the data flows.","Explicitly define network security groups (NSGs) or Azure Firewall to limit access to the deployed resources and their endpoints. Where supported, restrict access to private networks or leverage Private Link/Private Endpoints for data connectivity.",N/A,AI,Generic
MEDIUM,DP-2,LacpBillingExhaustExport.Template.json,56.0,"There is no evidence in the template that transport layer encryption (TLS >= 1.2) is enforced for connections to the ADX endpoints or between resources, leaving possible risk of unencrypted data-in-transit.","Enforce connections to ADX and related data export endpoints using at least TLS 1.2. When specifying endpoints, use 'https' and ensure that service configurations restrict or reject insecure protocols.",N/A,AI,Generic
MEDIUM,IM-6,LacpGeo.Template.json,48.0,"Key Vault access policies grant extensive permissions (e.g., ""Get""/""List"" for keys, secrets, certificates) to service principals and groups, without indications of necessity per least privilege. Granting broad permissions risks misuse or compromise.","Review and minimize Key Vault access policies to only those operations required by each principal. Use Azure RBAC for fine-grained, least-privilege access management.",N/A,AI,Generic
MEDIUM,AM-1,LacpGeo.Template.json,48.0,"Key Vault access policies for ""lacpAadServicePrincipal"" and ""managedIdentitiesSecurityGroupId"" allow both ""Get"" and ""List"" on keys, secrets, and certificates. The ""List"" permission can reveal all names, which may aid reconnaissance.","Restrict permissions to minimum required (e.g., remove ""List"" if not strictly needed). Audit why each principal needs each permission, and remove any not justified by their business function.",N/A,AI,Generic
MEDIUM,IM-3,LacpGeo.Template.json,0.0,There is no indication that conditional access policies are used for any identity principal interacting with resources like Key Vault or CosmosDB.,"Implement and enforce Azure AD conditional access policies requiring users and administrators to authenticate under defined secure conditions (e.g., trusted devices, MFA, location-based access).",N/A,AI,Generic
MEDIUM,DP-6,LacpGlobal.Template.json,133.0,"Key Vault is set to use 'Standard' SKU with no explicit configuration for customer-managed keys (CMK) on dependent resources such as Storage or CosmosDB, missing out on full customer control of encryption keys.","Where regulatory requirements apply, enable and enforce Customer Managed Keys (CMK) on all sensitive resources, referencing a Key Vault key.",N/A,AI,Generic
MEDIUM,NS-5,LacpGlobal.Template.json,110.0,"Storage Accounts lack configuration for Private Endpoints, exposing data transfers to the public internet.",Implement Azure Private Endpoints for storage accounts to limit their exposure to the public internet.,N/A,AI,Generic
MEDIUM,NS-6,LacpGlobal.Template.json,110.0,"Storage Accounts do not leverage Azure Virtual Network Service Endpoints, which could provide additional network layer controls.",Configure Service Endpoints on the storage accounts to restrict traffic to trusted VNets only.,N/A,AI,Generic
MEDIUM,DP-6,LacpGlobal.Template.json,110.0,Storage Accounts do not have customer-managed keys configured for encryption at rest (default platform-managed keys are likely used). This weakens data control and compliance posture.,Enable encryption at rest with customer-managed keys (CMK) stored in Azure Key Vault for all storage accounts if compliance and regulatory requirements dictate.,N/A,AI,Generic
MEDIUM,NS-1,LacpGlobal.Template.json,110.0,"Storage, CosmosDB, and Key Vault resources lack any reference to Network Security Groups or Azure Firewall, missing an important line of defense for access control.",Deploy NSGs or Azure Firewall in the relevant subnets or resource configurations to protect these resources with explicit traffic filtering.,N/A,AI,Generic
MEDIUM,NS-3,LacpGlobal.Template.json,110.0,There are no NSG (Network Security Group) attachments or rules applied to secure access to Storage or dependent resources.,Apply appropriate Network Security Groups to the subnets of these resources to enforce allowed/denied inbound and outbound traffic.,N/A,AI,Generic
MEDIUM,AM-1,LacpRegion.Template.json,1362.0,Data Factory pipelines and resources assign broad 'Data Contributor' roles and potentially other permissions to managed identities and security groups. There is no evidence of enforcing least privilege or reviewing permissions.,"Review all RBAC and data permissions in role assignments and access policies, ensuring managed identities and security groups receive only the minimum set of privileges required for their operations.",N/A,AI,Generic
MEDIUM,IM-11,LacpRegion.Template.json,1097.0,Key Vault access policies grant broad secrets/certificates/keys 'Get' and 'List' permissions to groups and identities. Excessive permissions can result in unnecessary risk if accounts are compromised.,"Limit Key Vault permissions to only those that are necessary per principal, and avoid granting highly privileged access broadly.",N/A,AI,Generic
MEDIUM,DP-3,LacpRegion.Template.json,1411.0,"Secrets such as storage account connection strings and CosmosDB keys are stored in Key Vault, but there is no evidence these are referenced using Key Vault references in linked services or other consuming resources, increasing operational risk if secrets are rotated or leaked.","Consume secrets securely using Key Vault references in consuming services (e.g., App Service or Data Factory linked services), and rotate them regularly.",N/A,AI,Generic
MEDIUM,DP-9,LacpRegion.Template.json,672.0,"All storage accounts and CosmosDB instances have minimum TLS version set to 'TLS1_2', which is correct, but ensure all consumers enforce TLS1.2+ as well. Some DataFactory linked services or connections may not enforce a minimum TLS version.",Ensure all consuming applications and linked services enforce TLS1.2+ for connections to data sources and sinks.,N/A,AI,Generic
MEDIUM,NS-6,LacpStamp.Template.json,0.0,"There are no Azure Virtual Network service endpoints configured for Storage Accounts or Key Vaults, which would limit access to these resources to traffic from selected virtual networks.",Enable and configure Virtual Network Service Endpoints on the required subnets and associate the Storage Accounts/Key Vaults to accept traffic only from those endpoints.,N/A,AI,Generic
MEDIUM,DP-1,LacpStamp.Template.json,0.0,Encryption at rest is not explicitly configured for Storage Accounts or Redis Cache. Absence of explicit configuration can lead to potentially unencrypted or weak encryption defaults.,Explicitly enable encryption at rest using platform-managed or customer-managed keys (CMK) for all data storage resources like Storage Accounts and Redis. Review and set the encryption section where supported.,N/A,AI,Generic
MEDIUM,DP-6,LacpStamp.Template.json,0.0,"Customer-Managed Key (CMK) encryption is not configured for Storage Accounts, Redis, or Key Vaults, which is recommended for sensitive data and regulated workloads.",Implement CMK encryption by linking resources to Azure Key Vault-managed encryption keys and updating resource definitions with necessary CMK configuration.,N/A,AI,Generic
MEDIUM,DP-2,LacpStamp.Template.json,0.0,"Although Storage Accounts specify 'minimumTlsVersion' as 'TLS1_2' and 'supportsHttpsTrafficOnly' is true, App Service endpoints and traffic manager endpoints are exposed via HTTPS but without custom DNS/hostnames or explicit TLS certificate references. This may lead to weak server identity assurance if default certificates are used.",Use custom domain names and upload trusted TLS certificates where possible for external endpoints and App Services to ensure strong in-transit encryption and proper identity validation.,N/A,AI,Generic
MEDIUM,IM-11,LacpStamp.Template.json,0.0,"RBAC is used for some resources but not comprehensively enforced across all resources, and coarse-grained roles are seen. More granular RBAC and scope definition can further restrict access.","Enforce granular RBAC for all resources, with least-privilege assignments and appropriate scope limitations.",N/A,AI,Generic
MEDIUM,IM-8,ReadAdxExhaust.Template.json,21.0,The deployment does not define or reference a system-assigned or user-assigned managed identity for the Kusto cluster. Managed identities should be used for secure resource-to-resource authentication.,Add a managed identity property to the Microsoft.Kusto/cluster resource to enable secure authentication to other Azure services without managing credentials.,N/A,AI,Generic
MEDIUM,IM-7,RoleAssignment.Template.json,60.0,A service principal (KeyVaultPrincipalId) is assigned the 'Storage Account Key Operator Service Role' at the subscription scope. Application identities should be limited in permission and assignment scope to reduce risk in case of compromise.,"Assign this role at the minimal required scope (e.g., only to the relevant storage accounts, not the entire subscription) and use managed identities where possible.",N/A,AI,Generic
MEDIUM,NS-1,TrafficManagerEndpoints.Template.json,46.0,There is no reference to Network Security Groups (NSGs) or Azure Firewall rules being used to protect the resources associated with the Traffic Manager endpoints. This may expose backend resources to unauthorized access.,Ensure appropriate network security controls like NSGs or Azure Firewall are applied to backend resources referenced by these endpoints. Document or enforce network layer protections for all traffic flows associated with Traffic Manager endpoints to mitigate unauthorized access.,N/A,AI,Generic
MEDIUM,NS-5,TrafficManagerEndpoints.Template.json,46.0,"The usage of external endpoints in Traffic Manager suggests public exposure. There is no indication of Private Endpoints being implemented to securely access the resources behind the Traffic Manager, missing the ASB recommendation to prefer private connectivity for sensitive services.","Where possible, use Azure Private Endpoints for backend services and configure Traffic Manager profiles to use internal or private endpoints, minimizing direct public access to critical infrastructure.",N/A,AI,Generic
LOW,DP-2,IngestionStorageAccount.Template.json,45.0,"'minimumTlsVersion' is set to 'TLS1_2', which meets baseline, but best practice is to regularly review and apply the highest supported TLS version (e.g., TLS1_3 when available).",Monitor for updates and configure storage accounts to use 'TLS1_3' (or higher) when available to maintain strong transport encryption.,N/A,AI,Generic
LOW,DP-1,LacpBilling.Template.json,0.0,"The Storage Accounts do not specify customer-managed keys (CMK) for encryption at rest, relying on platform-managed keys (default behavior). While Azure encrypts data at rest by default, using CMK provides an additional layer of control and compliance.",Configure Storage Accounts to use customer-managed keys (CMK) stored in Azure Key Vault for encryption at rest where regulatory or compliance requirements exist.,N/A,AI,Generic
LOW,DP-2,LacpBilling.Template.json,0.0,"The Storage Accounts set 'minimumTlsVersion' to 'TLS1_2' and 'supportsHttpsTrafficOnly' to 'true', which meets the requirement for encryption in transit. No finding of violation; this is flagged for completeness.",None required. Ensure continued enforcement of TLS 1.2+ and HTTPS-only traffic on Storage Accounts.,N/A,AI,Generic
LOW,DP-3,LacpBilling.Template.json,0.0,"No sensitive information such as access keys, connection strings, or secrets are hardcoded in this template, and 'allowSharedKeyAccess' is set 'false'. However, Storage Account identities and access might be managed manually elsewhere, and key management details (such as Key Vault integration) are not present.",Use Azure Key Vault to manage secrets and keys for applications. Integrate Storage Account with Key Vault for managing keys if customer-managed keys are used.,N/A,AI,Generic
LOW,IM-15,LacpBilling.Template.json,0.0,"User-assigned and system-assigned managed identities are used for the service, which meets the requirement for using managed identities. No violation; noted for transparency.",Continue to use managed identities for internal authentication between Azure resources. Regularly audit assigned privileges to managed identities.,N/A,AI,Generic
LOW,DP-3,LacpBillingExhaust.Template.json,103.0,"An AAD group (<EMAIL>) is granted database viewer permissions via a script, but group membership and access governance are not controlled or referenced in the template. Improper governance risks unauthorized disclosure of sensitive data.","Ensure the group assigned as database viewer is governed through Azure AD with proper access reviews, and avoid granting group-level access unless necessary and tightly controlled.",N/A,AI,Generic
LOW,DP-3,LacpGeo.Template.json,87.0,"A CosmosDB account's primary key is retrieved via ARM listKeys and stored as a Key Vault secret. Exposure risk is minimized by using Key Vault, but the primary key is highly sensitive and should not be programmatically or broadly accessible.","Regularly rotate CosmosDB keys and audit Key Vault access. Restrict which identities can retrieve and use these secrets, removing broad 'List'/'Get' access.",N/A,AI,Generic
LOW,IM-1,LacpGeo.Template.json,0.0,"The template utilizes objectIds and service principals, but does not explicitly define the use of Azure AD groups or managed identities for all access, leaving possible gaps in leveraging Azure AD's full capabilities.","Ensure all resource-to-resource and user access leverages Azure AD, and prefer managed identities for resource access rather than application IDs and secrets.",N/A,AI,Generic
LOW,DP-5,LacpGlobal.Template.json,49.0,"CosmosDB is configured with a backup policy of 'Continuous30Days,' which typically meets backup requirements; however, there is a comment indicating potential changes are needed in test environments, suggesting backup policies may not be consistent.","Ensure that backup policy is enforced in all environments and not only in production, and that compliance and recovery requirements are met for all critical data.",N/A,AI,Generic
LOW,NS-18,LacpRegion.Template.json,,"There are no explicit logging or Azure Monitor diagnostic settings applied to storage accounts, Key Vaults, Data Factory, or Cosmos DB. Without diagnostics, issues and access anomalies may go undetected.",Configure diagnostic settings and enable logging for all key data resources and identity management planes.,N/A,AI,Generic
LOW,NS-9,LacpStamp.Template.json,0.0,"Network traffic logging/monitoring is not enabled for any resource. There are no 'diagnosticSettings' or log collection policies in place for Storage, Network, or Key Vault resources.","Enable diagnostic logging for Storage Accounts, Key Vaults, and network resources. Stream logs to a Log Analytics Workspace, Storage Account, or Event Hub as appropriate.",N/A,AI,Generic
LOW,DP-3,LacpStamp.Template.json,0.0,"The parameter 'dasStorageAccountKey' is passed as a string parameter and stored directly in Key Vault as-is. If this value is ever set inline (potentially via parameter file), there is risk of secret exposure at deployment time.","Secure input of secrets via Azure Key Vault references, and avoid passing secrets as parameters in templates. Always inject secrets using secure methods.",N/A,AI,Generic
LOW,DP-5,LacpStamp.Template.json,0.0,No explicit backup or recovery configuration is provided for Storage Accounts or Redis instances.,"Implement backup policies and strategies for all critical data repositories (enable soft delete, point-in-time restore, or geo-redundant backup where available).",N/A,AI,Generic
LOW,NS-4,TrafficManagerEndpoints.Template.json,46.0,"The template does not show use of Azure Firewall or any third-party firewall to further protect the resources exposed through Traffic Manager, which may increase risk, especially if endpoints must remain public.",Implement Azure Firewall (or a third-party firewall) between public-facing Traffic Manager endpoints and backend services to provide additional threat mitigation such as intrusion detection and threat intelligence filtering.,N/A,AI,Generic
