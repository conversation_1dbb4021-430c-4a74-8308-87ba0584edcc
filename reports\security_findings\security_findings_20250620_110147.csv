File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
simple_test.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,25.0,"The 'supportsHttpsTrafficOnly' property for the Microsoft.Storage/storageAccounts resource is set to false, allowing unencrypted HTTP connections. This enables attackers to intercept or modify data in transit, exposing sensitive storage account data to man-in-the-middle attacks. The blast radius includes all data transferred to and from the storage account, potentially impacting all connected resources.","Set 'supportsHttpsTrafficOnly' to true in the storage account properties to enforce encrypted HTTPS connections for all data in transit. Example: ""supportsHttpsTrafficOnly"": true",,,,ai_analysis,,Validated
simple_test.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,27.0,"The 'minimumTlsVersion' property for the Microsoft.Storage/storageAccounts resource is set to 'TLS1_0', which is an outdated and insecure protocol. Attackers can exploit known vulnerabilities in TLS 1.0 to decrypt or tamper with data in transit, increasing the risk of data exfiltration and compromise of connected services.","Set 'minimumTlsVersion' to 'TLS1_2' or higher in the storage account properties to enforce strong encryption for data in transit. Example: ""minimumTlsVersion"": ""TLS1_2""",,,,ai_analysis,,Validated
simple_test.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,29.0,"The 'networkAcls.defaultAction' property for the Microsoft.Storage/storageAccounts resource is set to 'Allow', which permits public network access to the storage account. This exposes the storage account to the internet, enabling attackers to attempt unauthorized access, brute force, or data exfiltration. The blast radius includes all data stored in the account and any dependent resources.","Set 'networkAcls.defaultAction' to 'Deny' and configure specific network rules or private endpoints to restrict access. Example: ""defaultAction"": ""Deny"" and use 'bypass' or 'ipRules' as needed for legitimate access.",,,,ai_analysis,,Validated
simple_test.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,29.0,"The 'networkAcls.defaultAction' property for the Microsoft.KeyVault/vaults resource is set to 'Allow', which exposes the Key Vault to public network access. This allows attackers to probe, enumerate, and attempt unauthorized access to secrets and keys, increasing the risk of credential theft and privilege escalation across connected resources.","Set 'networkAcls.defaultAction' to 'Deny' and use private endpoints or specific network rules to restrict access to trusted networks only. Example: ""defaultAction"": ""Deny""",,,,ai_analysis,,Validated
simple_test.json,DP-8,Data Protection,Ensure security of key and certificate repository,CRITICAL,50.0,"The 'enableSoftDelete' property for the Microsoft.KeyVault/vaults resource is set to false, disabling soft delete protection. Attackers or malicious insiders can permanently delete secrets, keys, or certificates, making recovery impossible and enabling denial-of-service or cover-up of malicious activity.","Set 'enableSoftDelete' to true to ensure deleted Key Vault objects can be recovered, reducing the risk of permanent data loss. Example: ""enableSoftDelete"": true",,,,ai_analysis,,Validated
simple_test.json,DP-8,Data Protection,Ensure security of key and certificate repository,CRITICAL,51.0,"The 'enablePurgeProtection' property for the Microsoft.KeyVault/vaults resource is set to false, disabling purge protection. Attackers can permanently purge deleted secrets, keys, or certificates, bypassing soft delete and preventing recovery, which increases the risk of irreversible data loss and impairs forensic investigations.","Set 'enablePurgeProtection' to true to prevent permanent deletion of Key Vault objects, ensuring that deleted items can be recovered and supporting incident response. Example: ""enablePurgeProtection"": true",,,,ai_analysis,,Validated
simple_test.json,NS-8,Network Security,Detect and disable insecure services and protocols,HIGH,27.0,"The 'minimumTlsVersion' property for the Microsoft.Storage/storageAccounts resource is set to 'TLS1_0', which is an insecure protocol. Attackers can exploit weaknesses in TLS 1.0 to decrypt or manipulate network traffic, increasing the risk of credential theft and data compromise.","Set 'minimumTlsVersion' to 'TLS1_2' or higher to disable insecure protocols and enforce strong encryption. Example: ""minimumTlsVersion"": ""TLS1_2""",,,,ai_analysis,,Validated
simple_test.json,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,71.0,"The subnet 'default' in the Microsoft.Network/virtualNetworks resource does not have an associated Network Security Group (NSG). Without an NSG, all inbound and outbound traffic is allowed by default, enabling attackers to laterally move, scan, or exploit resources within the subnet, increasing the blast radius of any compromise.","Associate a Network Security Group (NSG) with the 'default' subnet and define deny-by-default rules, allowing only required traffic. Example: Add an 'networkSecurityGroup' property referencing an NSG resource.",,,,ai_analysis,,Validated
simple_test.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,29.0,"The 'networkAcls.defaultAction' property for the Microsoft.KeyVault/vaults resource is set to 'Allow', permitting unencrypted and potentially unauthorized network access to Key Vault. This exposes sensitive secrets and keys to interception or unauthorized access over insecure channels.","Set 'networkAcls.defaultAction' to 'Deny' and require access via private endpoints or trusted networks, ensuring all access is encrypted and restricted. Example: ""defaultAction"": ""Deny""",,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 9,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 0,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T11:01:47.199401,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
