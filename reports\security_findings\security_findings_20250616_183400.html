
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Security Findings Report</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    h1 { color: #333; }
                    .summary { background: #f5f5f5; padding: 15px; margin: 20px 0; border-radius: 5px; }
                    .severity-group { margin: 20px 0; }
                    .severity-header { 
                        padding: 10px; 
                        border-radius: 5px;
                        margin: 10px 0;
                        color: white;
                        font-weight: bold;
                    }
                    .critical { background-color: #dc3545; }
                    .high { background-color: #fd7e14; }
                    .medium { background-color: #ffc107; color: black; }
                    .low { background-color: #17a2b8; }
                    .finding {
                        border: 1px solid #ddd;
                        padding: 15px;
                        margin: 10px 0;
                        border-radius: 5px;
                        background: white;
                    }
                    .code-snippet {
                        background: #f8f9fa;
                        padding: 10px;
                        border-radius: 3px;
                        font-family: monospace;
                        white-space: pre-wrap;
                        margin: 10px 0;
                    }
                    .label { font-weight: bold; color: #555; }
                </style>
            </head>
            <body>
                <h1>🔒 Security Findings Report</h1>
                
            <div class="summary">
                <h2>Summary</h2>
                <p><strong>Total Findings:</strong> 65</p>
                <p><strong>Files Affected:</strong> 14</p>
                <p><strong>Findings by Severity:</strong></p>
                <ul>
            <li>🔴 CRITICAL: 4</li><li>🟠 HIGH: 28</li><li>🟡 MEDIUM: 29</li><li>🔵 LOW: 4</li></ul></div>
                
                    <div class="severity-group">
                        <div class="severity-header critical">🔴 CRITICAL Severity Findings</div>
                    
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> function.bicep</p>
                            <p><span class="label">Line:</span> 111</p>
                            <p><span class="label">Issue:</span> The Function App writes log data directly to a blob container using a SAS token constructed in-template and embedded in the App Service log configuration. If this template or deployment history is exposed, this could leak tokens with broad permissions, violating best practices for sensitive information disclosure.</p>
                            <p><span class="label">Remediation:</span> Use a Key Vault reference or managed identity for accessing storage instead of embedding SAS tokens in code or configuration. Limit the scope and lifetime of any SAS tokens if used, and prefer Azure AD authentication.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> keyvault.bicep</p>
                            <p><span class="label">Line:</span> 18</p>
                            <p><span class="label">Issue:</span> The Key Vault resource configures &#x27;networkAcls.defaultAction&#x27; as &#x27;Allow&#x27;, which allows public network access from any source unless specifically restricted via rules. This defeats network-level isolation of sensitive resources such as Key Vault per ASB NS-1.</p>
                            <p><span class="label">Remediation:</span> Set &#x27;networkAcls.defaultAction&#x27; to &#x27;Deny&#x27; to restrict public network access, allowing only explicitly defined networks, IPs, or subnets to access the Key Vault.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> keyvault.bicep</p>
                            <p><span class="label">Line:</span> 18</p>
                            <p><span class="label">Issue:</span> Key Vault is exposed to public networks by default due to &#x27;networkAcls.defaultAction&#x27; set as &#x27;Allow&#x27;. This increases the attack surface and risk of unauthorized access to secrets in the vault.</p>
                            <p><span class="label">Remediation:</span> Change &#x27;networkAcls.defaultAction&#x27; to &#x27;Deny&#x27; and validate that only required subnets or IPs have been granted access via &#x27;ipRules&#x27; and &#x27;virtualNetworkRules&#x27;.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> storage-accounts.bicep</p>
                            <p><span class="label">Line:</span> 21</p>
                            <p><span class="label">Issue:</span> All storage accounts (&#x27;storageAccountFunc&#x27;, &#x27;storageAccountFuzz&#x27;, and &#x27;storageAccountsCorpus&#x27;) are configured with &#x27;networkAcls.defaultAction&#x27; set to &#x27;Allow&#x27;, which permits public network access by default. This exposes storage account endpoints and increases the risk of unauthorized access, violating the control to protect public endpoints.</p>
                            <p><span class="label">Remediation:</span> Set &#x27;networkAcls.defaultAction&#x27; to &#x27;Deny&#x27; for all storage accounts. Allow access only via required IP addresses, virtual networks, or private endpoints. Ensure required access rules are explicitly defined.</p>
                        </div></div>
                    <div class="severity-group">
                        <div class="severity-header high">🟠 HIGH Severity Findings</div>
                    
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-5</p>
                            <p><span class="label">File:</span> app-config.bicep</p>
                            <p><span class="label">Line:</span> 7</p>
                            <p><span class="label">Issue:</span> The App Configuration resource does not implement a private endpoint. Without a private endpoint, the service is accessible via a public endpoint, increasing exposure to potential attacks. This violates ASB NS-5, which recommends using private endpoints for secure access.</p>
                            <p><span class="label">Remediation:</span> Add a Microsoft.Network/privateEndpoints resource to the template and associate it with the App Configuration store. This will limit access to the service to private networks only.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> app-config.bicep</p>
                            <p><span class="label">Line:</span> 7</p>
                            <p><span class="label">Issue:</span> The App Configuration store is deployed without restricting public network access or IP firewall rules. Without explicit access restrictions, the endpoint becomes publicly accessible, violating ASB NS-2 requirements to secure all public endpoints.</p>
                            <p><span class="label">Remediation:</span> Restrict public network access by configuring the App Configuration store with &#x27;publicNetworkAccess: &#x27;Disabled&#x27;&#x27; and implement appropriate firewall rules or use private endpoints.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> app-config.bicep</p>
                            <p><span class="label">Line:</span> 16</p>
                            <p><span class="label">Issue:</span> The key-values are provisioned using plain property assignment (`item.value`), which may result in storing sensitive data (such as secrets or connection strings) in-line. ASB DP-3 requires secrets to be stored in Azure Key Vault, not directly in App Configuration or IaC files.</p>
                            <p><span class="label">Remediation:</span> Do not store secrets, passwords, or sensitive connection strings directly in App Configuration or the IaC definition. Instead, store them in Azure Key Vault and reference them securely from your applications.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-5</p>
                            <p><span class="label">File:</span> event-grid.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> Private Endpoints are not configured for the Storage Account resources used as Event Grid sources (referenced by &#x27;storageFuzzId&#x27; and &#x27;storageCorpusIds&#x27;). This risks exposing Storage resources to the public internet.</p>
                            <p><span class="label">Remediation:</span> Configure Private Endpoints for all Storage Accounts used in blob event subscriptions to ensure that access is restricted to private networks only, minimizing attack surface.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> function-settings.bicep</p>
                            <p><span class="label">Line:</span> 48</p>
                            <p><span class="label">Issue:</span> The configuration does not specify any network security groups (NSGs) or Azure Firewall protecting the referenced storage account &#x27;funcStorage&#x27;. Protecting storage accounts with NSGs, firewall rules, or restricting access is required to minimize exposure.</p>
                            <p><span class="label">Remediation:</span> Update the storage account configuration to implement network rules so that only authorized networks/subnets can access the storage account. Use Azure Firewall rules or associate the storage account with NSGs or configure network rules to deny public network access.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-6</p>
                            <p><span class="label">File:</span> function-settings.bicep</p>
                            <p><span class="label">Line:</span> 64</p>
                            <p><span class="label">Issue:</span> No restrictions or access configurations are defined to protect the public endpoint of the Azure App Service (function app). By default, app services are publicly accessible.</p>
                            <p><span class="label">Remediation:</span> Implement access restrictions on the Azure App Service, such as applying IP Allow/Deny rules using &#x27;ipSecurityRestrictions&#x27;, or using Azure Front Door, or restricting access via VNet integration. Enable authentication and require secure access to the app.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> function-settings.bicep</p>
                            <p><span class="label">Line:</span> 65</p>
                            <p><span class="label">Issue:</span> The App Insights instrumentation key (&#x27;app_insights_key&#x27;) is set as an app setting and not configured for retrieval from a secure location such as Azure Key Vault. Sensitive data should not be stored in environment variables or app settings.</p>
                            <p><span class="label">Remediation:</span> Store the Application Insights instrumentation key in Azure Key Vault and use Key Vault references in your App Service configuration, or configure Application Insights with managed identity where possible to avoid explicit key exposure.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> function.bicep</p>
                            <p><span class="label">Line:</span> 62</p>
                            <p><span class="label">Issue:</span> The storage account resource &#x27;funcStorage&#x27; is referenced as &#x27;existing&#x27;, with no indication in the template that it is protected by Network Security Groups (NSGs) or Azure Firewall. Storage accounts should be protected from unauthorized network access.</p>
                            <p><span class="label">Remediation:</span> Ensure the storage account referenced by &#x27;logs_storage&#x27; is protected by appropriate NSG rules or is only accessible from trusted subnets or private endpoints. Consider documenting or verifying security controls on referenced resources.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> function.bicep</p>
                            <p><span class="label">Line:</span> 94</p>
                            <p><span class="label">Issue:</span> The Azure Function App exposes an HTTP endpoint and the site config does not restrict inbound access. Without explicit IP restrictions or integration with Private Endpoints, the app may be accessible from the public internet.</p>
                            <p><span class="label">Remediation:</span> Implement access restrictions for the Function App by defining allowed IP address ranges or integrating with Azure Private Endpoints. Review App Service Access Restrictions settings to minimize public exposure.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-5</p>
                            <p><span class="label">File:</span> function.bicep</p>
                            <p><span class="label">Line:</span> 99</p>
                            <p><span class="label">Issue:</span> The Function App is integrated with a virtual network subnet (&#x27;virtualNetworkSubnetId&#x27;), but there is no use of Private Endpoints for accessing the storage account logs, leaving the possibility of exposure over public network paths.</p>
                            <p><span class="label">Remediation:</span> Implement Private Endpoints for the storage account to enable secure, private access from the Function App. Update the logs configuration to use the storage account&#x27;s private endpoint DNS.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> hub-network.bicep</p>
                            <p><span class="label">Line:</span> 2</p>
                            <p><span class="label">Issue:</span> The virtual network and subnet defined in this template do not reference or associate any Network Security Group (NSG). This exposes all resources in the &#x27;hub-subnet&#x27; to unrestricted network access, violating controls to protect resources with NSGs or Azure Firewall.</p>
                            <p><span class="label">Remediation:</span> Define and associate a Network Security Group (NSG) with the &#x27;hub-subnet&#x27; to restrict and control inbound and outbound traffic according to the principle of least privilege.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-3</p>
                            <p><span class="label">File:</span> hub-network.bicep</p>
                            <p><span class="label">Line:</span> 2</p>
                            <p><span class="label">Issue:</span> The template lacks implementation of Network Security Groups (NSGs) to restrict network traffic, leaving the subnet unprotected from unwanted and potentially malicious traffic.</p>
                            <p><span class="label">Remediation:</span> Create an NSG resource and associate it with &#x27;hub-subnet&#x27; to enforce explicit allow/deny rules for inbound and outbound traffic at the subnet level.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> instance-config.bicep</p>
                            <p><span class="label">Line:</span> 32</p>
                            <p><span class="label">Issue:</span> The network configuration for this Compute deployment defines an &#x27;address_space&#x27; and &#x27;subnet&#x27;, but the template does not include any definition or reference to Network Security Groups (NSGs) or Azure Firewall to protect the VM resources. According to ASB NS-1, critical resources such as Compute instances must be protected with NSG or firewall rules to reduce attack surface.</p>
                            <p><span class="label">Remediation:</span> Explicitly define and associate Network Security Groups with the relevant subnets or network interfaces for all VM resources. Ensure that only required inbound and outbound traffic is permitted by the NSG rules. Consider using Azure Firewall for additional control if appropriate.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-3</p>
                            <p><span class="label">File:</span> instance-config.bicep</p>
                            <p><span class="label">Line:</span> 32</p>
                            <p><span class="label">Issue:</span> There are no Network Security Groups (NSG) implemented or referenced for the defined subnet (&#x27;10.0.0.0/16&#x27;), which is required by ASB NS-3 to control inbound and outbound VM traffic. The absence of NSGs can leave Compute resources exposed to unwanted traffic.</p>
                            <p><span class="label">Remediation:</span> Add NSG resource definitions to this template or its dependencies, and associate NSGs with the specified subnet and/or VM network interfaces. Configure rules to allow only required management and application traffic, and deny all others.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-4</p>
                            <p><span class="label">File:</span> instance-config.bicep</p>
                            <p><span class="label">Line:</span> 37</p>
                            <p><span class="label">Issue:</span> The template does not specify any managed disk encryption configuration for the default VM images or attached storage, violating ASB DP-4. Encryption at rest for managed disks is not guaranteed by omission.</p>
                            <p><span class="label">Remediation:</span> Ensure VM resource definitions configure encryption for OS and data disks, either by using encrypted managed disks or disk encryption sets integrated with Azure Key Vault.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-1</p>
                            <p><span class="label">File:</span> instance-config.bicep</p>
                            <p><span class="label">Line:</span> 37</p>
                            <p><span class="label">Issue:</span> The template does not ensure encryption at rest for data associated with the Compute instances or their disks as required by ASB DP-1. Explicit configuration is needed to ensure all persisted data is encrypted.</p>
                            <p><span class="label">Remediation:</span> Add explicit configuration to all VM and managed disk resources to enforce encryption at rest, leveraging Azure-managed or customer-managed keys where appropriate.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> ip-rules.bicep</p>
                            <p><span class="label">Line:</span> 3</p>
                            <p><span class="label">Issue:</span> The corpNetIps and sawVnetIps lists include large public IP ranges (such as &#x27;*******/8&#x27; and &#x27;********/8&#x27;), which, if applied as allow rules for access to Azure resources (e.g., storage accounts or Logic Apps), could result in significant overexposure of public endpoints and defeat network isolation best practices.</p>
                            <p><span class="label">Remediation:</span> Restrict allowed IP address ranges to only those necessary for access, avoiding overly broad network ranges. Use specific subnets, trusted enterprise VPN endpoints, or private networking (Private Endpoints or Service Endpoints) to minimize exposure.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-5</p>
                            <p><span class="label">File:</span> ip-rules.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> The template only defines IP allow rules and does not leverage Azure Private Endpoints, which are strongly recommended to securely access resources and to avoid exposure via public IPs.</p>
                            <p><span class="label">Remediation:</span> Wherever possible, use Private Endpoints for Azure resources instead of public IP allow lists. This ensures network traffic remains within the Azure backbone network and is not exposed to the public internet.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> keyvault.bicep</p>
                            <p><span class="label">Line:</span> 37</p>
                            <p><span class="label">Issue:</span> Secrets are provisioned into Key Vault via parameter values. If the deployment parameter &#x27;secrets&#x27; contains plaintext secrets (in deployment files or parameters), this risks accidental exposure through IaC, logs, or deployment history, violating best practices for managing sensitive information.</p>
                            <p><span class="label">Remediation:</span> Ensure all secret values are passed using secure mechanisms, such as ARM secureString, Bicep @secure(), or Key Vault references, and never check plaintext secrets into source code or parameter files. Regularly audit deployment process for exposure.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> operational-insights.bicep</p>
                            <p><span class="label">Line:</span> 85</p>
                            <p><span class="label">Issue:</span> The output &#x27;appInsightsInstrumentationKey&#x27; exposes the Application Insights Instrumentation Key directly. Instrumentation Keys are sensitive secrets that can allow an attacker to push arbitrary telemetry or exfiltrate data if leaked.</p>
                            <p><span class="label">Remediation:</span> Do not output or expose sensitive secrets such as Instrumentation Keys in outputs or logs. Use Azure Key Vault references to manage and access secrets securely.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> scaleset-networks.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> No Network Security Group (NSG) or Azure Firewall is defined or associated with the subnet &#x27;scaleset&#x27; or the virtual network. This leaves resources unprotected against unwanted or malicious network traffic.</p>
                            <p><span class="label">Remediation:</span> Define and associate an NSG with the &#x27;scaleset&#x27; subnet or configure Azure Firewall to restrict and monitor network traffic, only allowing required ports/protocols.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> scaleset-networks.bicep</p>
                            <p><span class="label">Line:</span> 12</p>
                            <p><span class="label">Issue:</span> A public IP address is allocated for outbound network traffic via a NAT Gateway, but there are no controls shown restricting which resources or subnets can access the public endpoint. Public exposure is not minimized.</p>
                            <p><span class="label">Remediation:</span> Apply NSGs or Firewall rules to restrict outbound traffic to necessary destinations, and limit which resources/subnets can access the public IP address. Only expose public endpoints when absolutely required.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-3</p>
                            <p><span class="label">File:</span> scaleset-networks.bicep</p>
                            <p><span class="label">Line:</span> 32</p>
                            <p><span class="label">Issue:</span> The subnet &#x27;scaleset&#x27; has no Network Security Group (NSG) associated to control inbound/outbound traffic. Absence of NSGs allows unrestricted communication to and from the subnet.</p>
                            <p><span class="label">Remediation:</span> Associate a Network Security Group with the &#x27;scaleset&#x27; subnet, defining explicit rules for allowed and denied traffic, following least privilege.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> server-farms.bicep</p>
                            <p><span class="label">Line:</span> 60</p>
                            <p><span class="label">Issue:</span> The App Service Plan resource &#x27;Microsoft.Web/serverfarms&#x27; does not specify access restrictions, private endpoints, or networking rules, which may result in the App Service exposing public endpoints by default. This violates the principle of minimizing public exposure as per ASB NS-2.</p>
                            <p><span class="label">Remediation:</span> Configure access restrictions for App Service or use App Service Environments (ASE) for private deployments. Implement IP Restrictions to allow only trusted sources and restrict public exposure.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> server-farms.bicep</p>
                            <p><span class="label">Line:</span> 60</p>
                            <p><span class="label">Issue:</span> There are no Network Security Groups (NSGs), firewalls, or explicit network protections implemented on resources such as the App Service or Key Vault reference. Sensitive resources are not adequately protected as per ASB NS-1.</p>
                            <p><span class="label">Remediation:</span> Deploy NSGs or Azure Firewall to protect the subnet/app service environment where resources reside. For App Service, use VNet integration and restrict networks for Key Vault where possible.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> signalR.bicep</p>
                            <p><span class="label">Line:</span> 4</p>
                            <p><span class="label">Issue:</span> The SignalR Service resource is created with default networking, which exposes a public endpoint. There is no restriction or access control to limit exposure. This may allow unintended public access to the service, violating the control&#x27;s guidance to secure all public endpoints.</p>
                            <p><span class="label">Remediation:</span> Configure the SignalR resource with &#x27;publicNetworkAccess: &#x27;Disabled&#x27;&#x27; property, or use private endpoints and IP firewall restrictions to limit inbound access to trusted sources only.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> storage-accounts.bicep</p>
                            <p><span class="label">Line:</span> 23</p>
                            <p><span class="label">Issue:</span> &#x27;networkAcls.bypass&#x27; is set to &#x27;AzureServices, Logging, Metrics&#x27;, which allows certain Azure service traffic to bypass network restrictions to the storage accounts. While sometimes necessary, this can unintentionally widen access to critical resources.</p>
                            <p><span class="label">Remediation:</span> Re-evaluate which services are required to bypass network rules. Limit &#x27;bypass&#x27; values to only necessary services and document business justifications. Ideally, use private endpoints for trusted services.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> IM-1</p>
                            <p><span class="label">File:</span> storage-accounts.bicep</p>
                            <p><span class="label">Line:</span> 15</p>
                            <p><span class="label">Issue:</span> Storage accounts have &#x27;allowSharedKeyAccess&#x27; set to false (good), but there is no indication that Azure Active Directory (Azure AD)-based authentication (e.g., Azure AD integration for Blob/Queue) is enforced for resource access. Relying solely on key-based access without enabling Azure AD can weaken identity management.</p>
                            <p><span class="label">Remediation:</span> Enable Azure AD authentication (via &#x27;azureActiveDirectoryProperties&#x27;) for Blob and Queue services on all storage accounts to ensure strong identity controls and centralized access management.</p>
                        </div></div>
                    <div class="severity-group">
                        <div class="severity-header medium">🟡 MEDIUM Severity Findings</div>
                    
                        <div class="finding">
                            <p><span class="label">Control:</span> IM-6</p>
                            <p><span class="label">File:</span> app-config.bicep</p>
                            <p><span class="label">Line:</span> 7</p>
                            <p><span class="label">Issue:</span> The template does not define any RBAC assignments or restrict access using defined roles. Absence of RBAC assignments can lead to excessive permissions in violation of ASB IM-6.</p>
                            <p><span class="label">Remediation:</span> Add &#x27;Microsoft.Authorization/roleAssignments&#x27; resources to assign granular access to the App Configuration store based on least privilege principles.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> IM-8</p>
                            <p><span class="label">File:</span> app-config.bicep</p>
                            <p><span class="label">Line:</span> 7</p>
                            <p><span class="label">Issue:</span> There is no explicit assignment or usage of managed identities for resource-to-resource authentication in the template. ASB IM-8 recommends using managed identities for secure authentication between resources.</p>
                            <p><span class="label">Remediation:</span> Enable a managed identity on dependent resources (such as apps consuming this App Configuration) and grant appropriate permissions. While not directly configured here, add guidance or examples for secure consumption.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-1</p>
                            <p><span class="label">File:</span> app-config.bicep</p>
                            <p><span class="label">Line:</span> 7</p>
                            <p><span class="label">Issue:</span> The template does not specify encryption settings for data at rest for the App Configuration store. While encryption at rest is provided by default, the lack of customer-managed key (CMK) settings may violate strict interpretations of ASB DP-1.</p>
                            <p><span class="label">Remediation:</span> If regulatory requirements mandate, configure the App Configuration store with customer-managed keys (CMK) for encryption at rest.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> autoscale-settings.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> No network security controls (such as Network Security Groups or Azure Firewall) are defined or referenced to protect the resources (e.g., the referenced Storage Account via func_storage_account_id or the App Service Plan via server_farm_id). This omission may expose sensitive backend services to unrestricted network access.</p>
                            <p><span class="label">Remediation:</span> Ensure that the references for func_storage_account_id and server_farm_id point to resources protected by Network Security Groups (for supported types) or are otherwise restricted (using firewall rules, service endpoints, or private endpoints as appropriate). Explicitly deploy or reference NSGs or other network controls in templates that provision infrastructure involving sensitive resources such as Storage Accounts.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> autoscale-settings.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> The template allows for sensitive parameters (such as workspaceId, func_storage_account_id, and server_farm_id) to be provided directly, but does not enforce use of secure parameter types, Key Vault references, or provide guidance on protecting secrets. This can lead to accidental disclosure if parameter values are hard-coded or improperly managed.</p>
                            <p><span class="label">Remediation:</span> Use parameter metadata to indicate which parameters are sensitive and should be provided via secure mechanism (e.g., Key Vault references). Avoid hard-coding identifiers that could potentially reveal sensitive resource details or access patterns. Add guidance or enforce secure parameter passing in deployment pipelines.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-6</p>
                            <p><span class="label">File:</span> event-grid.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> Virtual Network Service Endpoints are not specified for the Storage Accounts being subscribed to in Event Grid System Topics. Without service endpoints, access might be permitted from outside trusted VNets.</p>
                            <p><span class="label">Remediation:</span> Enable and restrict Storage Account access to selected Virtual Networks by configuring Service Endpoints. This reduces exposure to untrusted networks.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-1</p>
                            <p><span class="label">File:</span> function-settings.bicep</p>
                            <p><span class="label">Line:</span> 48</p>
                            <p><span class="label">Issue:</span> The storage account resource block does not explicitly specify encryption settings. Although encryption at rest is enabled by default for most Azure storage resources, not specifying this explicitly risks accidental misconfiguration.</p>
                            <p><span class="label">Remediation:</span> Explicitly define encryption settings for the storage account resource with &#x27;encryption&#x27; properties, specifying the use of Microsoft-managed keys or customer-managed keys as appropriate.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-3</p>
                            <p><span class="label">File:</span> function.bicep</p>
                            <p><span class="label">Line:</span> 62</p>
                            <p><span class="label">Issue:</span> There is no deployment or linkage of a Network Security Group (NSG) applied to the subnet referenced in &#x27;hubSubnetId&#x27; for the Function App integration. NSGs are recommended to control traffic to subnets used by critical services.</p>
                            <p><span class="label">Remediation:</span> Deploy and associate a suitable NSG to the subnet referenced by &#x27;hubSubnetId&#x27;. Ensure ingress and egress rules restrict access to only required sources and destinations.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-2</p>
                            <p><span class="label">File:</span> function.bicep</p>
                            <p><span class="label">Line:</span> 62</p>
                            <p><span class="label">Issue:</span> There is no configuration shown to enforce encryption in transit (TLS 1.2+) for the storage account used for application logs. Data in transit to blob endpoints should be encrypted with strong TLS.</p>
                            <p><span class="label">Remediation:</span> Ensure the storage account enforces &#x27;minimumTlsVersion&#x27; to TLS1_2 or higher. If possible, restrict blob access to HTTPS only and prohibit HTTP access in the resource configuration.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> IM-6</p>
                            <p><span class="label">File:</span> function.bicep</p>
                            <p><span class="label">Line:</span> 87</p>
                            <p><span class="label">Issue:</span> There are no explicit RBAC role definitions or assignments in this template for App Service, Storage, or Managed Identity resources. Without RBAC, access rights may be over-provisioned or unclear.</p>
                            <p><span class="label">Remediation:</span> Define and deploy explicit Role Assignments granting only necessary roles (e.g., &#x27;Storage Blob Data Contributor&#x27;) to identities such as the Function App&#x27;s Managed Identity for the resources they access.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-4</p>
                            <p><span class="label">File:</span> hub-network.bicep</p>
                            <p><span class="label">Line:</span> 2</p>
                            <p><span class="label">Issue:</span> No Azure Firewall or third-party firewall resource is defined or referenced for inspecting or securing traffic into or out of the hub virtual network.</p>
                            <p><span class="label">Remediation:</span> Deploy an Azure Firewall or a third-party firewall appliance in the hub network to inspect and control egress and ingress traffic as appropriate for your network architecture.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-5</p>
                            <p><span class="label">File:</span> hub-network.bicep</p>
                            <p><span class="label">Line:</span> 17</p>
                            <p><span class="label">Issue:</span> The subnet is configured with service endpoints for &#x27;Microsoft.Storage.Global&#x27; and &#x27;Microsoft.KeyVault&#x27;, but there is no evidence of private endpoints for accessing these services, which would provide enhanced security over service endpoints.</p>
                            <p><span class="label">Remediation:</span> Where possible, use Azure Private Endpoints to connect securely to Storage and Key Vault, ensuring traffic remains on the Microsoft backbone and cannot be accessed from public networks.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> instance-config.bicep</p>
                            <p><span class="label">Line:</span> 32</p>
                            <p><span class="label">Issue:</span> The template does not specify network restrictions or private endpoints, nor does it ensure that public endpoints are protected for Compute instances. According to ASB NS-2, public exposure must be minimized and endpoints restricted. Without explicit configuration, there may be risk of accidental exposure.</p>
                            <p><span class="label">Remediation:</span> Explicitly configure VM and subnet settings to avoid public IP assignment unless strictly required. For any services requiring internet access, use Azure Private Link or Application Gateway with WAF, and ensure that NSG rules restrict public ingress.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-7</p>
                            <p><span class="label">File:</span> instance-config.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> The template does not define resources or policies for Just-in-Time (JIT) VM Access. JIT access reduces exposure of management (RDP/SSH) ports as required by ASB NS-7.</p>
                            <p><span class="label">Remediation:</span> Implement JIT VM Access for all Compute resources using Azure Security Center or specify the enabling of JIT in the resource manager template. Restrict management port exposure to as-needed basis only.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-10</p>
                            <p><span class="label">File:</span> instance-config.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> There is no mention of using Azure Bastion or jump host configuration for secure management of Compute VMs, as recommended in ASB NS-10. Direct SSH/RDP access may be inadvertently permitted.</p>
                            <p><span class="label">Remediation:</span> Integrate Azure Bastion or designate a jump host for secure administrative access to VMs. Avoid exposing management ports directly to the internet.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-6</p>
                            <p><span class="label">File:</span> ip-rules.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> The current approach does not include use of Virtual Network Service Endpoints, which further restricts traffic to Azure services to only come from permitted VNets/subnets. Relying solely on IP allow lists is less secure and more difficult to manage than VNet-based restrictions.</p>
                            <p><span class="label">Remediation:</span> Implement VNet Service Endpoints for supported services to restrict access to trusted virtual networks/subnets, in addition to or instead of IP-based rules.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> ip-rules.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> There is no evidence of the use of Network Security Groups (NSGs) or Azure Firewall in the template to protect network-accessible resources. Solely relying on IP allow lists (such as for Storage Accounts or Logic Apps) may leave gaps in network security.</p>
                            <p><span class="label">Remediation:</span> Ensure that NSGs and/or Azure Firewall rules are configured to provide robust, layered control of network traffic to and from sensitive resources. Use them in conjunction with resource-level access controls.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> IM-6</p>
                            <p><span class="label">File:</span> keyvault.bicep</p>
                            <p><span class="label">Line:</span> 22</p>
                            <p><span class="label">Issue:</span> The Key Vault enables RBAC authorization by setting &#x27;enableRbacAuthorization&#x27; to true, but there are no role assignments included in the template. This may result in no access being granted or requiring out-of-band access configuration.</p>
                            <p><span class="label">Remediation:</span> Add explicit role assignments for required users, groups, or applications to ensure least privilege access is configured using RBAC, instead of relying on default or external access configuration.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-6</p>
                            <p><span class="label">File:</span> keyvault.bicep</p>
                            <p><span class="label">Line:</span> 23</p>
                            <p><span class="label">Issue:</span> The Key Vault is deployed with the default &#x27;Standard&#x27; SKU, which does not support dedicated HSM or Customer Managed Keys for encrypting keys/secrets at rest. This may not meet compliance for sensitive data that requires customer-managed encryption.</p>
                            <p><span class="label">Remediation:</span> Consider using the &#x27;Premium&#x27; SKU for dedicated HSM support and/or configure Key Vault to use CMK for encryption if regulatory, compliance, or data protection needs require it.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> operational-insights.bicep</p>
                            <p><span class="label">Line:</span> 84</p>
                            <p><span class="label">Issue:</span> The output &#x27;appInsightsAppId&#x27; exposes the Application Insights AppId, which can be useful for attackers in reconnaissance, although less critical than Instrumentation Key.</p>
                            <p><span class="label">Remediation:</span> Avoid outputting internal resource identifiers if not necessary. Restrict output to information strictly required by downstream processes.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-1</p>
                            <p><span class="label">File:</span> operational-insights.bicep</p>
                            <p><span class="label">Line:</span> 35</p>
                            <p><span class="label">Issue:</span> The resource &#x27;Microsoft.OperationalInsights/workspaces&#x27; does not specify the use of customer-managed or service-managed encryption keys. The template does not enforce encryption at rest configuration for the workspace.</p>
                            <p><span class="label">Remediation:</span> Explicitly set &#x27;encryption&#x27; properties within the workspace resource and consider using customer-managed keys (CMK) for enhanced data protection.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-6</p>
                            <p><span class="label">File:</span> operational-insights.bicep</p>
                            <p><span class="label">Line:</span> 35</p>
                            <p><span class="label">Issue:</span> No configuration for customer-managed keys (CMK) for encryption is present for the Log Analytics workspace, which could be required for handling highly sensitive data.</p>
                            <p><span class="label">Remediation:</span> Add and configure the &#x27;encryption&#x27; block under the workspace resource to leverage customer-managed keys from Azure Key Vault.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> operational-insights.bicep</p>
                            <p><span class="label">Line:</span> 35</p>
                            <p><span class="label">Issue:</span> The Log Analytics workspace is not protected by any network security group (NSG), firewall, or private endpoint configuration. This may expose logging data to unauthorized access.</p>
                            <p><span class="label">Remediation:</span> Restrict access to the workspace using private endpoints and/or NSGs. Consider workspace networking features to allow access only from trusted VNETs.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> operational-insights.bicep</p>
                            <p><span class="label">Line:</span> 35</p>
                            <p><span class="label">Issue:</span> No configuration to restrict public network access for the Log Analytics workspace. By default, the workspace may be publicly accessible over the internet.</p>
                            <p><span class="label">Remediation:</span> Set the &#x27;publicNetworkAccessForIngestion&#x27; and &#x27;publicNetworkAccessForQuery&#x27; properties to &#x27;Disabled&#x27; and configure private endpoints for secure access.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-4</p>
                            <p><span class="label">File:</span> scaleset-networks.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> There is no reference to Azure Firewall or a third-party firewall appliance to provide centralized network traffic filtering/protection between segments or from/to the internet.</p>
                            <p><span class="label">Remediation:</span> Include and configure Azure Firewall or a supported third-party firewall to inspect and protect traffic between subnets, to/from the internet, and enforce security policies.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> server-farms.bicep</p>
                            <p><span class="label">Line:</span> 111</p>
                            <p><span class="label">Issue:</span> The property &#x27;CERTIFICATE_PASSWORD_GENEVACERT&#x27; for the AntMDS component is created with an empty string. While sensitive values for certificates are referenced from Key Vault, setting a password field (even empty) in configuration may risk later misconfigurations or accidental exposure.</p>
                            <p><span class="label">Remediation:</span> Do not provision empty or placeholder sensitive settings. If certificate passwords are needed, always retrieve them securely from Azure Key Vault, not as inline configuration values.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> IM-8</p>
                            <p><span class="label">File:</span> server-farms.bicep</p>
                            <p><span class="label">Line:</span> 103</p>
                            <p><span class="label">Issue:</span> The Key Vault integration for Geneva certificates references the vault and secret but does not specify use of a managed identity for accessing Key Vault. Secure resource-to-resource authentication should use managed identity.</p>
                            <p><span class="label">Remediation:</span> Enable and assign a system-assigned or user-assigned managed identity to the App Service Plan, and configure Key Vault access policies/rbac to grant appropriate secret/certificate access via that managed identity.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-5</p>
                            <p><span class="label">File:</span> server-farms.bicep</p>
                            <p><span class="label">Line:</span> 103</p>
                            <p><span class="label">Issue:</span> Key Vault is referenced using its full resource ID but there is no evidence that private endpoint connectivity is used. Exposing Key Vault via public endpoints increases risk of data exposure.</p>
                            <p><span class="label">Remediation:</span> Configure Azure Key Vault to use private endpoints, and restrict public network access. Update reference patterns and access methods to align with private networking.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-3</p>
                            <p><span class="label">File:</span> storage-accounts.bicep</p>
                            <p><span class="label">Line:</span> 21</p>
                            <p><span class="label">Issue:</span> No Network Security Groups (NSGs) are defined or enforced for the subnet (referenced via &#x27;hubSubnetId&#x27;) associated with storage account virtual network rules. NSGs are required to restrict traffic at the subnet/network level in addition to storage account ACLs.</p>
                            <p><span class="label">Remediation:</span> Define and associate NSGs to the referenced subnet(s) to explicitly control allowed inbound and outbound traffic. This ensures a defense-in-depth network security posture.</p>
                        </div></div>
                    <div class="severity-group">
                        <div class="severity-header low">🔵 LOW Severity Findings</div>
                    
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-1</p>
                            <p><span class="label">File:</span> function.bicep</p>
                            <p><span class="label">Line:</span> 62</p>
                            <p><span class="label">Issue:</span> The template does not show explicit enforcement of encryption at rest on the referenced storage account. While storage accounts are encrypted by default, explicit configuration is recommended to align with policy and future-proof templates.</p>
                            <p><span class="label">Remediation:</span> Set the &#x27;enableHttpsTrafficOnly&#x27; and &#x27;encryption&#x27; properties explicitly on the storage account in its defining template. If not owned by this template, confirm those settings are present in the storage account&#x27;s deployment.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-9</p>
                            <p><span class="label">File:</span> ip-rules.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> The template does not define logging or monitoring settings for the network rules or Logic Apps. Lack of network traffic monitoring can reduce visibility into potential threats or misconfigurations.</p>
                            <p><span class="label">Remediation:</span> Enable diagnostics and logging (such as Azure Monitor, Network Watcher, and resource-level diagnostic settings) for all resources to monitor network access and rule effectiveness.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-5</p>
                            <p><span class="label">File:</span> scaleset-networks.bicep</p>
                            <p><span class="label">Line:</span> 40</p>
                            <p><span class="label">Issue:</span> Private Endpoints are not implemented for the subnet or any referenced resource. Absence of private endpoints can lead to unintentional public or wide network exposure for PaaS resources.</p>
                            <p><span class="label">Remediation:</span> Implement Azure Private Endpoints for services like Azure Storage, SQL, or Key Vault that are accessed from this subnet, to ensure network traffic remains on the Microsoft backbone.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-6</p>
                            <p><span class="label">File:</span> scaleset-networks.bicep</p>
                            <p><span class="label">Line:</span> 40</p>
                            <p><span class="label">Issue:</span> No Network Service Endpoints are enabled in the subnet configuration, which could help restrict access to Azure PaaS services (e.g., Storage, SQL) to only this subnet.</p>
                            <p><span class="label">Remediation:</span> Enable required service endpoints (e.g., for Microsoft.Storage, Microsoft.Sql, etc.) on the subnet for Azure PaaS resources, and enforce appropriate access policies.</p>
                        </div></div>
            </body>
            </html>
            