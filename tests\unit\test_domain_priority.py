#!/usr/bin/env python3
"""
Test script to verify the Azure Security Benchmark domain priority implementation.
This script tests the dynamic domain priority system to ensure consistent ordering.
"""

import sys
import os
import json
from pathlib import Path

# Add the current directory to Python path to import security_opt
sys.path.insert(0, str(Path(__file__).parent))

try:
    from security_opt import SecurityPRReviewer
    print("✅ Successfully imported SecurityPRReviewer")
except ImportError as e:
    print(f"❌ Failed to import SecurityPRReviewer: {e}")
    sys.exit(1)

def test_domain_priority_configuration():
    """Test the domain priority configuration."""
    print("\n🔧 Testing Domain Priority Configuration...")
    
    try:
        # Initialize the reviewer (this will load the domain configuration)
        reviewer = SecurityPRReviewer(local_folder="./templates")
        
        # Test domain priority configuration
        print(f"📊 Domain Priority Configuration:")
        for domain_name, config in reviewer.domain_priority_config.items():
            priority = config["priority"]
            prefix = config["prefix"]
            print(f"  P{priority:02d}: {domain_name} ({prefix}-*)")
        
        return True
    except Exception as e:
        print(f"❌ Error testing domain configuration: {e}")
        return False

def test_domain_priority_mapping():
    """Test the dynamic domain priority mapping generation."""
    print("\n🔢 Testing Domain Priority Mapping...")
    
    try:
        reviewer = SecurityPRReviewer(local_folder="./templates")
        
        # Test domain priority mapping
        domain_mapping = reviewer._get_domain_priority_mapping()
        print(f"📋 Generated Domain Priority Mapping:")
        
        # Sort by priority for display
        sorted_domains = sorted(domain_mapping.items(), key=lambda x: x[1])
        for domain, priority in sorted_domains:
            print(f"  P{priority:02d}: {domain}")
        
        return True
    except Exception as e:
        print(f"❌ Error testing domain mapping: {e}")
        return False

def test_critical_controls_generation():
    """Test the dynamic critical controls generation."""
    print("\n🎯 Testing Critical Controls Generation...")
    
    try:
        reviewer = SecurityPRReviewer(local_folder="./templates")
        
        # Test critical controls generation
        critical_controls = reviewer._get_critical_controls_by_domain()
        print(f"🔒 Generated Critical Controls by Domain:")
        
        # Sort by domain priority for display
        domain_priorities = {domain: config["priority"] 
                           for domain, config in reviewer.domain_priority_config.items()}
        
        sorted_domains = sorted(critical_controls.items(), 
                              key=lambda x: domain_priorities.get(x[0], 999))
        
        for domain, controls in sorted_domains:
            if controls:
                priority = domain_priorities.get(domain, 999)
                print(f"  P{priority:02d} {domain}: {', '.join(controls)}")
        
        return True
    except Exception as e:
        print(f"❌ Error testing critical controls: {e}")
        return False

def test_resource_control_mappings():
    """Test the dynamic resource control mappings."""
    print("\n🔗 Testing Resource Control Mappings...")
    
    try:
        reviewer = SecurityPRReviewer(local_folder="./templates")
        
        # Test resource control mappings
        print(f"📦 Resource Control Mappings:")
        for resource_type, mapping in reviewer.resource_control_cache.items():
            control_count = len(mapping["relevant_controls"])
            domains = ", ".join(mapping["primary_domains"])
            control_ids = ", ".join(mapping["control_ids"][:3])  # Show first 3
            print(f"  {resource_type}: {control_count} controls from [{domains}]")
            print(f"    Sample controls: {control_ids}{'...' if control_count > 3 else ''}")
        
        return True
    except Exception as e:
        print(f"❌ Error testing resource mappings: {e}")
        return False

def test_domain_display_order():
    """Test the domain display order generation."""
    print("\n📋 Testing Domain Display Order...")
    
    try:
        reviewer = SecurityPRReviewer(local_folder="./templates")
        
        # Test with sample domains
        sample_domains = [
            "Identity Management", 
            "Network Security", 
            "Data Protection", 
            "Privileged Access",
            "Logging and Threat Detection",
            "Unknown"
        ]
        
        ordered_domains = reviewer._get_domain_display_order(sample_domains)
        print(f"🎯 Domain Display Order:")
        for i, domain in enumerate(ordered_domains, 1):
            print(f"  {i}. {domain}")
        
        return True
    except Exception as e:
        print(f"❌ Error testing domain display order: {e}")
        return False

def test_sorting_functionality():
    """Test the findings sorting functionality."""
    print("\n📊 Testing Findings Sorting...")
    
    try:
        reviewer = SecurityPRReviewer(local_folder="./templates")
        
        # Create sample findings with different domains and severities
        sample_findings = [
            {"control_id": "DP-1", "severity": "HIGH", "file_path": "test.json", "line": 10},
            {"control_id": "NS-1", "severity": "CRITICAL", "file_path": "test.json", "line": 20},
            {"control_id": "ID-1", "severity": "MEDIUM", "file_path": "test.json", "line": 30},
            {"control_id": "PA-1", "severity": "HIGH", "file_path": "test.json", "line": 40},
            {"control_id": "LT-1", "severity": "LOW", "file_path": "test.json", "line": 50},
        ]
        
        # Sort the findings
        sorted_findings = reviewer._sort_findings_by_priority(sample_findings)
        
        print(f"🔄 Sorted Findings (Domain Priority Order):")
        for i, finding in enumerate(sorted_findings, 1):
            control_id = finding["control_id"]
            severity = finding["severity"]
            print(f"  {i}. {control_id} ({severity})")
        
        return True
    except Exception as e:
        print(f"❌ Error testing sorting: {e}")
        return False

def main():
    """Run all domain priority tests."""
    print("🧪 Azure Security Benchmark Domain Priority Implementation Test")
    print("=" * 70)
    
    tests = [
        ("Domain Priority Configuration", test_domain_priority_configuration),
        ("Domain Priority Mapping", test_domain_priority_mapping),
        ("Critical Controls Generation", test_critical_controls_generation),
        ("Resource Control Mappings", test_resource_control_mappings),
        ("Domain Display Order", test_domain_display_order),
        ("Sorting Functionality", test_sorting_functionality),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 Running: {test_name}")
        print(f"{'='*50}")
        
        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"💥 {test_name}: ERROR - {e}")
    
    print(f"\n{'='*70}")
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Domain priority implementation is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
