# 🎯 Line Number Accuracy Guide for IaC Guardian

## Overview
Accurate line numbers are critical for security reports. This guide provides strategies and implementation details for ensuring precise line number tracking in your IaC Guardian security analysis.

## 🔍 Methods for Accurate Line Number Tracking

### 1. **File Parsing with Line Awareness**

```python
def get_accurate_line_number(content: str, search_pattern: str) -> Dict:
    """Get precise line number for security issues."""
    lines = content.split('\n')
    
    for i, line in enumerate(lines, 1):
        if search_pattern.lower() in line.lower():
            return {
                'line_number': i,
                'matched_line': line.strip(),
                'context_before': lines[max(0, i-4):i-1],
                'context_after': lines[i:min(len(lines), i+3)]
            }
    return None
```

### 2. **AST-Based Analysis (Most Accurate)**

```python
import ast
import json

def get_ast_line_numbers(file_path: str, issue_type: str) -> List[int]:
    """Use Abstract Syntax Tree for precise line numbers."""
    
    if file_path.endswith('.py'):
        return get_python_ast_lines(file_path, issue_type)
    elif file_path.endswith('.tf'):
        return get_terraform_hcl_lines(file_path, issue_type)
    elif file_path.endswith('.bicep'):
        return get_bicep_ast_lines(file_path, issue_type)
    elif file_path.endswith(('.yaml', '.yml')):
        return get_yaml_lines(file_path, issue_type)
    
def get_terraform_hcl_lines(file_path: str, issue_type: str) -> List[int]:
    """Parse Terraform HCL for exact line numbers."""
    try:
        import python_hcl2
        with open(file_path, 'r') as f:
            parsed = python_hcl2.loads(f.read())
        
        # Find specific resource blocks and their line numbers
        # This requires custom parsing logic for each issue type
        return find_issue_lines_in_hcl(parsed, issue_type)
    except Exception as e:
        logger.warning(f"HCL parsing failed: {e}")
        return []
```

### 3. **Regex-Based Pattern Matching**

```python
import re

def find_security_issues_with_lines(content: str) -> List[Dict]:
    """Find security issues using regex patterns with line numbers."""
    
    security_patterns = {
        'public_access': {
            'pattern': r'allow_blob_public_access\s*=\s*true',
            'severity': 'HIGH',
            'description': 'Public blob access enabled'
        },
        'weak_tls': {
            'pattern': r'min_tls_version\s*=\s*["\']TLS1_[01]["\']',
            'severity': 'MEDIUM', 
            'description': 'Weak TLS version configured'
        },
        'no_encryption': {
            'pattern': r'encryption\s*=\s*["\']?false["\']?',
            'severity': 'CRITICAL',
            'description': 'Encryption disabled'
        }
    }
    
    findings = []
    lines = content.split('\n')
    
    for issue_type, config in security_patterns.items():
        pattern = re.compile(config['pattern'], re.IGNORECASE)
        
        for line_num, line in enumerate(lines, 1):
            match = pattern.search(line)
            if match:
                findings.append({
                    'issue_type': issue_type,
                    'line_number': line_num,
                    'matched_content': line.strip(),
                    'severity': config['severity'],
                    'description': config['description'],
                    'column_start': match.start(),
                    'column_end': match.end()
                })
    
    return findings
```

## 🔧 Implementation Strategies

### 1. **Multi-Stage Validation**

```python
def validate_finding_accuracy(file_path: str, finding: Dict) -> Dict:
    """Validate finding accuracy with multiple checks."""
    
    # Stage 1: File existence and readability
    if not os.path.exists(file_path):
        return {'valid': False, 'error': 'File not found'}
    
    # Stage 2: Line number range validation
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    line_num = finding.get('line_number', 0)
    if line_num <= 0 or line_num > len(lines):
        return {
            'valid': False, 
            'error': f'Line {line_num} out of range (1-{len(lines)})'
        }
    
    # Stage 3: Content verification
    actual_line = lines[line_num - 1].strip()
    expected_pattern = finding.get('pattern', '')
    
    if expected_pattern.lower() in actual_line.lower():
        return {'valid': True, 'confidence': 'high'}
    
    # Stage 4: Fuzzy matching for moved content
    similarity = calculate_line_similarity(expected_pattern, actual_line)
    if similarity > 0.8:
        return {'valid': True, 'confidence': 'medium', 'similarity': similarity}
    
    # Stage 5: Search nearby lines
    closest_match = find_closest_matching_line(lines, expected_pattern, line_num)
    return {
        'valid': False,
        'suggested_line': closest_match['line_number'],
        'suggested_content': closest_match['content'],
        'confidence': 'low'
    }
```

### 2. **Real-Time File Content Integration**

```python
def get_real_file_content(file_path: str, line_number: int, context: int = 5) -> Dict:
    """Get actual file content for code dialog display."""
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        start_line = max(1, line_number - context)
        end_line = min(len(lines), line_number + context)
        
        return {
            'success': True,
            'total_lines': len(lines),
            'content_lines': [
                {
                    'number': i,
                    'content': lines[i-1].rstrip('\n'),
                    'highlighted': i == line_number
                }
                for i in range(start_line, end_line + 1)
            ],
            'file_info': {
                'size': os.path.getsize(file_path),
                'modified': os.path.getmtime(file_path),
                'encoding': 'utf-8'
            }
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'fallback_content': generate_sample_content(file_path, line_number)
        }
```

## 📊 Accuracy Metrics and Monitoring

### 1. **Line Number Confidence Scoring**

```python
def calculate_line_confidence(finding: Dict, validation: Dict) -> float:
    """Calculate confidence score for line number accuracy."""
    
    base_score = 1.0
    
    # Deduct for validation issues
    if not validation.get('valid', True):
        base_score -= 0.5
    
    # Adjust for match type
    match_type = validation.get('match_type', 'exact')
    if match_type == 'exact':
        base_score *= 1.0
    elif match_type == 'partial':
        base_score *= 0.8
    elif match_type == 'fuzzy':
        base_score *= 0.6
    else:
        base_score *= 0.3
    
    # Factor in similarity score
    similarity = validation.get('similarity', 1.0)
    base_score *= similarity
    
    return max(0.0, min(1.0, base_score))
```

### 2. **Accuracy Reporting**

```python
def generate_accuracy_report(findings: List[Dict]) -> Dict:
    """Generate accuracy report for line numbers."""
    
    total_findings = len(findings)
    high_confidence = sum(1 for f in findings if f.get('confidence', 0) > 0.8)
    medium_confidence = sum(1 for f in findings if 0.5 < f.get('confidence', 0) <= 0.8)
    low_confidence = sum(1 for f in findings if f.get('confidence', 0) <= 0.5)
    
    return {
        'total_findings': total_findings,
        'accuracy_breakdown': {
            'high_confidence': {'count': high_confidence, 'percentage': high_confidence/total_findings*100},
            'medium_confidence': {'count': medium_confidence, 'percentage': medium_confidence/total_findings*100},
            'low_confidence': {'count': low_confidence, 'percentage': low_confidence/total_findings*100}
        },
        'overall_accuracy': (high_confidence + medium_confidence*0.7) / total_findings * 100,
        'recommendations': generate_accuracy_recommendations(findings)
    }
```

## 🚀 Best Practices

### 1. **Always Validate Before Reporting**
- Check file existence and readability
- Verify line numbers are within file bounds
- Validate content matches expected patterns

### 2. **Use Multiple Detection Methods**
- Combine AST parsing with regex patterns
- Cross-validate findings with different approaches
- Implement fallback mechanisms

### 3. **Provide Context and Confidence**
- Include surrounding lines for context
- Report confidence levels for each finding
- Suggest corrections for inaccurate line numbers

### 4. **Handle Edge Cases**
- Empty files or single-line files
- Files with different line endings (CRLF vs LF)
- Binary files or files with encoding issues
- Very large files that might cause memory issues

### 5. **Real-Time Validation**
- Validate line numbers when generating reports
- Update line numbers if files have changed
- Provide warnings for stale or inaccurate data

## 🔍 Testing Line Number Accuracy

```python
def test_line_number_accuracy():
    """Test suite for line number accuracy."""
    
    test_cases = [
        {
            'file_content': 'line1\nline2\nSECURITY_ISSUE_HERE\nline4',
            'search_pattern': 'SECURITY_ISSUE_HERE',
            'expected_line': 3
        },
        {
            'file_content': 'resource "aws_s3_bucket" {\n  bucket = "test"\n  acl = "public-read"\n}',
            'search_pattern': 'public-read',
            'expected_line': 3
        }
    ]
    
    for i, test in enumerate(test_cases):
        result = get_accurate_line_number(test['file_content'], test['search_pattern'])
        assert result['line_number'] == test['expected_line'], f"Test {i+1} failed"
    
    print("✅ All line number accuracy tests passed!")
```

This comprehensive approach ensures your IaC Guardian provides accurate, reliable line number references that developers can trust and act upon immediately.
