#!/usr/bin/env python3
"""
Create VS Code settings for MCP integration.
"""

import json
import os
from pathlib import Path

# Create .vscode directory if it doesn't exist
vscode_dir = Path(".vscode")
vscode_dir.mkdir(exist_ok=True)

# MCP configuration for VS Code
settings = {
    "github.copilot.chat.experimental.mcp.enabled": True,
    "github.copilot.chat.experimental.mcp.servers": {
        "iac-guardian": {
            "command": "python",
            "args": ["-u", "mcp_server.py"],
            "cwd": str(Path.cwd().as_posix()),
            "env": {
                "ENFORCE_DOMAIN_PRIORITY": "true",
                "USE_OPTIMIZED_PROMPTS": "true",
                "ANALYSIS_SEED": "42",
                "PYTHONPATH": str(Path.cwd().as_posix())
            }
        }
    }
}

# Write settings file
settings_file = vscode_dir / "settings.json"
with open(settings_file, 'w', encoding='utf-8') as f:
    json.dump(settings, f, indent=2)

print(f"✅ VS Code settings created: {settings_file}")
print("📋 Configuration:")
print(json.dumps(settings, indent=2))
