# Security PR Review Code Improvements

## Overview
This document provides comprehensive improvements for the `security_pr_review.py` file, focusing on refactoring, optimization, and better practices.

## Key Improvements

### 1. Extract Resource Type Detection Logic into Separate Classes

**Current Issue**: The `_determine_resource_type` method is over 200 lines long and handles multiple responsibilities.

**Improved Approach**:
```python
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple
import re

class ResourceTypeDetector(ABC):
    """Abstract base class for resource type detection strategies."""
    
    @abstractmethod
    def detect(self, file_path: str, content: str) -> Dict[str, float]:
        """Detect resource types and return scores."""
        pass

class BicepResourceDetector(ResourceTypeDetector):
    """Detector specifically for Bicep files."""
    
    def __init__(self, resource_mappings: Dict):
        self.resource_mappings = resource_mappings
        self.arm_declaration_pattern = re.compile(r"resource\s+\w+\s+'([^']+)'")
    
    def detect(self, file_path: str, content: str) -> Dict[str, float]:
        scores = {rt: 0 for rt in self.resource_mappings.keys()}
        
        # Extract ARM resource type declarations
        declarations = self.arm_declaration_pattern.findall(content)
        
        for declaration in declarations:
            resource_type_only = declaration.split('@')[0] if '@' in declaration else declaration
            self._score_arm_type(resource_type_only, scores)
        
        return scores
    
    def _score_arm_type(self, arm_type: str, scores: Dict[str, float]):
        """Score ARM type matches."""
        arm_type_lower = arm_type.lower()
        provider = arm_type_lower.split('/')[0]
        resource_path = '/'.join(arm_type_lower.split('/')[1:])
        
        for rt, mappings in self.resource_mappings.items():
            for mapped_arm_type in mappings['arm_types']:
                if self._matches_arm_type(arm_type_lower, mapped_arm_type.lower()):
                    scores[rt] += self._calculate_match_score(arm_type_lower, mapped_arm_type.lower())

class TerraformResourceDetector(ResourceTypeDetector):
    """Detector for Terraform files."""
    
    def detect(self, file_path: str, content: str) -> Dict[str, float]:
        # Implementation for Terraform detection
        pass

class ResourceTypeDetectorFactory:
    """Factory to create appropriate detector based on file type."""
    
    @staticmethod
    def create_detector(file_extension: str, resource_mappings: Dict) -> ResourceTypeDetector:
        detectors = {
            '.bicep': BicepResourceDetector,
            '.tf': TerraformResourceDetector,
            '.json': JsonResourceDetector,
            '.arm': ArmResourceDetector
        }
        
        detector_class = detectors.get(file_extension, GenericResourceDetector)
        return detector_class(resource_mappings)
```

### 2. Externalize Resource Mappings

**Current Issue**: The `AZURE_RESOURCE_MAPPINGS` dictionary is huge and embedded in the class.

**Improved Approach**:
```python
# azure_resource_mappings.yaml
resource_mappings:
  Storage:
    arm_types:
      - Microsoft.Storage/storageAccounts
      - Microsoft.Storage/storageAccounts/blobServices
    terraform_types:
      - azurerm_storage_account
      - azurerm_storage_container
    keywords:
      - storage
      - blob
      - container

# In the main code:
import yaml

class ResourceMappingLoader:
    """Load and cache resource mappings from external configuration."""
    
    _cache = None
    
    @classmethod
    def load_mappings(cls, file_path: str = "azure_resource_mappings.yaml") -> Dict:
        if cls._cache is None:
            with open(file_path, 'r') as f:
                cls._cache = yaml.safe_load(f)['resource_mappings']
        return cls._cache
```

### 3. Improve Error Handling with Context Managers

**Current Issue**: Error handling is scattered and inconsistent.

**Improved Approach**:
```python
from contextlib import contextmanager
import logging

@contextmanager
def log_operation(operation_name: str, logger: logging.Logger):
    """Context manager for consistent operation logging and error handling."""
    logger.info(f"Starting {operation_name}")
    try:
        yield
        logger.info(f"Completed {operation_name}")
    except Exception as e:
        logger.error(f"Error in {operation_name}: {str(e)}")
        raise

# Usage:
def analyze_files(self, files: List[Dict]) -> List[Dict]:
    with log_operation("file analysis", logger):
        # ... analysis code
```

### 4. Implement Caching for Performance

**Current Issue**: Resource type detection and benchmark data loading happen repeatedly.

**Improved Approach**:
```python
from functools import lru_cache
import hashlib

class CachedResourceTypeDetector:
    """Resource type detector with caching capabilities."""
    
    @lru_cache(maxsize=1000)
    def determine_resource_type(self, file_hash: str, file_extension: str) -> str:
        """Cache resource type detection results based on file hash."""
        # Detection logic here
        pass
    
    def detect_with_cache(self, file_path: str, content: str) -> str:
        """Detect resource type with caching."""
        file_hash = hashlib.md5(content.encode()).hexdigest()
        file_extension = os.path.splitext(file_path)[1].lower()
        return self.determine_resource_type(file_hash, file_extension)
```

### 5. Use Async Operations for Parallel Processing

**Current Issue**: Files are analyzed sequentially, which is slow for large PRs.

**Improved Approach**:
```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

class AsyncSecurityAnalyzer:
    """Analyze files asynchronously for better performance."""
    
    def __init__(self, max_workers: int = 5):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
    
    async def analyze_files_async(self, files: List[Dict]) -> List[Dict]:
        """Analyze multiple files in parallel."""
        tasks = []
        
        for file_info in files:
            task = asyncio.create_task(self._analyze_single_file(file_info))
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions and flatten results
        findings = []
        for result in results:
            if isinstance(result, list):
                findings.extend(result)
            elif isinstance(result, Exception):
                logger.error(f"Error analyzing file: {result}")
        
        return findings
    
    async def _analyze_single_file(self, file_info: Dict) -> List[Dict]:
        """Analyze a single file asynchronously."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor,
            self._analyze_file_sync,
            file_info
        )
```

### 6. Implement Better Logging Structure

**Current Issue**: Logging is inconsistent and doesn't provide structured data.

**Improved Approach**:
```python
import structlog

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

class SecurityPRReviewer:
    def __init__(self, repo_id: str, pr_id: int):
        self.logger = structlog.get_logger().bind(
            repo_id=repo_id,
            pr_id=pr_id,
            component="SecurityPRReviewer"
        )
```

### 7. Improve Type Safety with Dataclasses

**Current Issue**: Dictionaries are used extensively, which lacks type safety.

**Improved Approach**:
```python
from dataclasses import dataclass, field
from typing import List, Optional, Dict
from enum import Enum

class Severity(Enum):
    CRITICAL = "CRITICAL"
    HIGH = "HIGH"
    MEDIUM = "MEDIUM"
    LOW = "LOW"

@dataclass
class SecurityFinding:
    """Represents a security finding in code."""
    file_path: str
    line: int
    severity: Severity
    control_id: str
    description: str
    remediation: str
    matching_content: Optional[str] = None
    metadata: Dict = field(default_factory=dict)

@dataclass
class FileInfo:
    """Information about a file to analyze."""
    path: str
    content: str
    resource_type: Optional[str] = None
    extension: Optional[str] = None
    
    def __post_init__(self):
        if not self.extension:
            self.extension = os.path.splitext(self.path)[1].lower()

@dataclass
class AnalysisContext:
    """Context for security analysis."""
    file_info: FileInfo
    relevant_controls: List[Dict]
    resource_keywords: List[str]
```

### 8. Implement Strategy Pattern for Different Analysis Types

**Current Issue**: The analysis logic is monolithic and hard to extend.

**Improved Approach**:
```python
from abc import ABC, abstractmethod

class SecurityAnalysisStrategy(ABC):
    """Abstract strategy for security analysis."""
    
    @abstractmethod
    def analyze(self, file_info: FileInfo) -> List[SecurityFinding]:
        """Analyze file and return security findings."""
        pass

class PatternBasedAnalysis(SecurityAnalysisStrategy):
    """Pattern-based security analysis."""
    
    def __init__(self, patterns: List[Dict]):
        self.patterns = patterns
    
    def analyze(self, file_info: FileInfo) -> List[SecurityFinding]:
        findings = []
        for pattern_info in self.patterns:
            matches = self._find_matches(file_info.content, pattern_info)
            findings.extend(matches)
        return findings

class AIBasedAnalysis(SecurityAnalysisStrategy):
    """AI-based security analysis using Azure OpenAI."""
    
    def __init__(self, openai_client, benchmark_data):
        self.openai_client = openai_client
        self.benchmark_data = benchmark_data
    
    def analyze(self, file_info: FileInfo) -> List[SecurityFinding]:
        # AI analysis implementation
        pass

class CompositeAnalysis(SecurityAnalysisStrategy):
    """Combines multiple analysis strategies."""
    
    def __init__(self, strategies: List[SecurityAnalysisStrategy]):
        self.strategies = strategies
    
    def analyze(self, file_info: FileInfo) -> List[SecurityFinding]:
        all_findings = []
        for strategy in self.strategies:
            findings = strategy.analyze(file_info)
            all_findings.extend(findings)
        return self._deduplicate_findings(all_findings)
```

### 9. Add Configuration Management

**Current Issue**: Configuration is hardcoded or from environment variables only.

**Improved Approach**:
```python
from pydantic import BaseSettings, Field
from typing import Optional

class SecurityReviewerConfig(BaseSettings):
    """Configuration for security reviewer."""
    
    # Azure DevOps settings
    azure_devops_pat: str = Field(..., env='AZURE_DEVOPS_PAT')
    azure_devops_org: str = Field(..., env='AZURE_DEVOPS_ORG')
    azure_devops_project: str = Field(..., env='AZURE_DEVOPS_PROJECT')
    
    # Azure OpenAI settings
    azure_openai_endpoint: str = Field(..., env='AZURE_OPENAI_ENDPOINT')
    azure_openai_api_key: Optional[str] = Field(None, env='AZURE_OPENAI_API_KEY')
    azure_openai_deployment: str = Field('gpt-35-turbo', env='AZURE_OPENAI_DEPLOYMENT')
    azure_openai_api_version: str = Field('2024-02-01', env='AZURE_OPENAI_API_VERSION')
    use_azure_ad_auth: bool = Field(False, env='AZURE_OPENAI_USE_AD_AUTH')
    
    # Analysis settings
    max_parallel_analyses: int = Field(5, env='MAX_PARALLEL_ANALYSES')
    cache_ttl_seconds: int = Field(3600, env='CACHE_TTL_SECONDS')
    
    class Config:
        env_file = '.env'
        env_file_encoding = 'utf-8'
```

### 10. Add Metrics and Monitoring

**Current Issue**: No performance metrics or monitoring capabilities.

**Improved Approach**:
```python
import time
from prometheus_client import Counter, Histogram, Gauge

# Define metrics
analysis_duration = Histogram('security_analysis_duration_seconds', 
                            'Time spent analyzing files',
                            ['file_type', 'resource_type'])
findings_counter = Counter('security_findings_total', 
                         'Total number of security findings',
                         ['severity', 'control_id'])
active_analyses = Gauge('active_security_analyses', 
                       'Number of active security analyses')

class MetricsDecorator:
    """Decorator for adding metrics to methods."""
    
    @staticmethod
    def track_duration(metric: Histogram, labels: Dict):
        def decorator(func):
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    duration = time.time() - start_time
                    metric.labels(**labels).observe(duration)
            return wrapper
        return decorator
```

## Summary of Benefits

1. **Modularity**: Code is broken down into smaller, focused components
2. **Testability**: Each component can be tested independently
3. **Performance**: Async operations and caching improve speed
4. **Maintainability**: Clear separation of concerns and better organization
5. **Type Safety**: Use of type hints and dataclasses reduces errors
6. **Extensibility**: Strategy pattern makes it easy to add new analysis types
7. **Observability**: Structured logging and metrics provide better insights
8. **Configuration**: Centralized configuration management
9. **Error Handling**: Consistent and comprehensive error handling
10. **Code Reuse**: Common patterns extracted into reusable components

These improvements transform the codebase into a more professional, maintainable, and scalable solution while preserving all the original functionality.
