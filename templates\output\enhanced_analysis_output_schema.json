{"$schema": "http://json-schema.org/draft-07/schema#", "title": "IaC Guardian Enhanced Security Analysis Output", "type": "object", "required": ["findings", "analysis_metadata", "executive_summary"], "properties": {"findings": {"type": "array", "items": {"type": "object", "required": ["control_id", "severity", "line", "description", "remediation", "confidence_score", "exploitation_complexity", "remediation_effort_hours"], "properties": {"control_id": {"type": "string", "description": "Exact control ID from Azure Security Benchmark or extended frameworks", "pattern": "^(IM|NS|DP|PA|LT|CS|AS|DS)-[0-9]+$"}, "severity": {"type": "string", "enum": ["CRITICAL", "HIGH", "MEDIUM", "LOW"], "description": "Security impact severity level"}, "line": {"type": "integer", "minimum": 1, "description": "Exact line number from the numbered template content"}, "description": {"type": "string", "minLength": 50, "description": "Specific violation description with template reference and context justification"}, "remediation": {"type": "string", "minLength": 50, "description": "Actionable fix with specific configuration changes and code examples"}, "confidence_score": {"type": "integer", "minimum": 0, "maximum": 100, "description": "AI confidence level based on analysis depth and validation"}, "exploitation_complexity": {"type": "string", "enum": ["Low", "Medium", "High", "Very High"], "description": "Attack difficulty assessment"}, "remediation_effort_hours": {"type": "string", "enum": ["1-4", "4-8", "8-16", "16+"], "description": "Estimated development effort in hours"}, "mitre_attack_techniques": {"type": "array", "items": {"type": "string", "pattern": "^T[0-9]{4}(\\.[0-9]{3})?$"}, "description": "Relevant MITRE ATT&CK technique IDs"}, "compliance_frameworks": {"type": "array", "items": {"type": "string", "enum": ["CIS", "NIST", "SOC2", "ISO27001", "PCI-DSS"]}, "description": "Applicable compliance standards"}, "azure_policy_definitions": {"type": "array", "items": {"type": "string"}, "description": "Relevant Azure Policy names for automated remediation"}, "compensating_controls": {"type": "string", "description": "Alternative security measures if direct fix not feasible"}, "business_impact": {"type": "string", "enum": ["Critical", "High", "Medium", "Low"], "description": "Potential business impact if exploited"}, "deployment_worthiness_score": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Score indicating if finding justifies development investment"}}}}, "analysis_metadata": {"type": "object", "required": ["total_controls_evaluated", "framework_coverage", "analysis_confidence", "false_positive_risk"], "properties": {"total_controls_evaluated": {"type": "integer", "minimum": 1, "description": "Total number of security controls evaluated"}, "framework_coverage": {"type": "array", "items": {"type": "string", "enum": ["ASB", "MITRE", "CIS", "NIST", "SOC2", "ISO27001", "Container", "API", "DevOps"]}, "description": "Security frameworks applied in analysis"}, "analysis_confidence": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Overall confidence in analysis results"}, "false_positive_risk": {"type": "string", "enum": ["Low", "Medium", "High"], "description": "Assessed risk of false positive findings"}, "analysis_duration_seconds": {"type": "number", "description": "Time taken for analysis execution"}, "template_complexity_score": {"type": "integer", "minimum": 1, "maximum": 10, "description": "Complexity assessment of analyzed template"}, "ai_model_version": {"type": "string", "description": "Version of AI model used for analysis"}}}, "executive_summary": {"type": "object", "required": ["overall_security_posture", "critical_findings_count", "top_risks", "recommended_priorities"], "properties": {"overall_security_posture": {"type": "string", "enum": ["Excellent", "Good", "Fair", "Poor", "Critical"], "description": "Overall security assessment"}, "critical_findings_count": {"type": "integer", "minimum": 0, "description": "Number of critical severity findings"}, "high_findings_count": {"type": "integer", "minimum": 0, "description": "Number of high severity findings"}, "total_findings_count": {"type": "integer", "minimum": 0, "description": "Total number of security findings"}, "top_risks": {"type": "array", "maxItems": 5, "items": {"type": "object", "properties": {"risk_description": {"type": "string", "description": "Brief description of the risk"}, "impact_level": {"type": "string", "enum": ["Critical", "High", "Medium", "Low"]}, "affected_resources": {"type": "array", "items": {"type": "string"}}}}}, "recommended_priorities": {"type": "array", "maxItems": 10, "items": {"type": "object", "properties": {"priority_rank": {"type": "integer", "minimum": 1, "maximum": 10}, "control_id": {"type": "string"}, "justification": {"type": "string"}, "estimated_effort": {"type": "string", "enum": ["1-4", "4-8", "8-16", "16+"]}}}}, "compliance_status": {"type": "object", "properties": {"azure_security_benchmark": {"type": "object", "properties": {"compliant_controls": {"type": "integer"}, "non_compliant_controls": {"type": "integer"}, "compliance_percentage": {"type": "number", "minimum": 0, "maximum": 100}}}, "industry_frameworks": {"type": "object", "additionalProperties": {"type": "object", "properties": {"compliance_percentage": {"type": "number", "minimum": 0, "maximum": 100}, "key_gaps": {"type": "array", "items": {"type": "string"}}}}}}}}}, "trend_analysis": {"type": "object", "properties": {"security_improvement_trend": {"type": "string", "enum": ["Improving", "Stable", "Declining", "Unknown"], "description": "Trend compared to previous analyses"}, "common_vulnerability_patterns": {"type": "array", "items": {"type": "object", "properties": {"pattern_name": {"type": "string"}, "frequency": {"type": "integer"}, "trend": {"type": "string", "enum": ["Increasing", "Stable", "Decreasing"]}}}}, "framework_adoption_progress": {"type": "object", "additionalProperties": {"type": "object", "properties": {"current_adoption_percentage": {"type": "number"}, "previous_adoption_percentage": {"type": "number"}, "trend": {"type": "string", "enum": ["Improving", "Stable", "Declining"]}}}}}}, "remediation_roadmap": {"type": "object", "properties": {"immediate_actions": {"type": "array", "description": "Critical fixes needed within 24-48 hours", "items": {"type": "object", "properties": {"control_id": {"type": "string"}, "action_description": {"type": "string"}, "estimated_effort": {"type": "string"}}}}, "short_term_goals": {"type": "array", "description": "Improvements to complete within 1-4 weeks", "items": {"type": "object", "properties": {"goal_description": {"type": "string"}, "related_controls": {"type": "array", "items": {"type": "string"}}, "estimated_effort": {"type": "string"}}}}, "long_term_objectives": {"type": "array", "description": "Strategic security improvements over 1-6 months", "items": {"type": "object", "properties": {"objective_description": {"type": "string"}, "business_value": {"type": "string"}, "estimated_effort": {"type": "string"}}}}}}}}