File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,109.0,"The App Service configuration at line 109 allows public network access (""publicNetworkAccess"": ""Enabled"") and has an IP security restriction that allows all IP addresses (""ipAddress"": ""Any"", ""action"": ""Allow""). This exposes the App Service to the public internet without restriction, violating the requirement to restrict public endpoints to required IPs only as per ASB NS-2.","Restrict public network access by setting ""publicNetworkAccess"" to ""Disabled"" or configure ""ipSecurityRestrictions"" to allow only specific trusted IP addresses. Consider using Private Link or an Application Gateway to further restrict access. Reference: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
template.json,NS-3,Network Security,Use Network Security Groups (NSGs),CRITICAL,109.0,"The App Service configuration at line 109 includes an ""ipSecurityRestrictions"" rule that allows all inbound traffic (""ipAddress"": ""Any"", ""action"": ""Allow""). This violates the requirement to deny all inbound traffic by default and only allow necessary traffic, as per ASB NS-3.","Remove the 'Allow all' rule from ""ipSecurityRestrictions"" and configure rules to allow only necessary traffic from trusted IP addresses. Ensure the default is to deny all inbound traffic. Reference: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works

📚 References: [Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules)

🔵 Azure Guidance: Implement NSGs with deny-by-default rules.",[Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules),Implement NSGs with deny-by-default rules.,"Configure NSGs to deny all inbound traffic by default. Allow only necessary traffic.
NSG rules documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works
Security rules best practices: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
template.json,NS-3,Network Security,Use Network Security Groups (NSGs),CRITICAL,120.0,"The App Service SCM site configuration at line 120 includes a ""scmIpSecurityRestrictions"" rule that allows all inbound traffic (""ipAddress"": ""Any"", ""action"": ""Allow""). This violates the requirement to deny all inbound traffic by default and only allow necessary traffic, as per ASB NS-3.","Remove the 'Allow all' rule from ""scmIpSecurityRestrictions"" and configure rules to allow only necessary traffic from trusted IP addresses. Ensure the default is to deny all inbound traffic. Reference: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works

📚 References: [Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules)

🔵 Azure Guidance: Implement NSGs with deny-by-default rules.",[Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules),Implement NSGs with deny-by-default rules.,"Configure NSGs to deny all inbound traffic by default. Allow only necessary traffic.
NSG rules documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works
Security rules best practices: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 3,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 0,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-17T17:45:30.336925,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
