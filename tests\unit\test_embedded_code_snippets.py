#!/usr/bin/env python3
"""
Test script to demonstrate embedded code snippets with ±100 lines context.
This creates real files and generates a report with actual code content embedded directly.
"""

import os
import sys
from pathlib import Path
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from security_opt import SecurityPRReviewer

def create_test_files_with_known_issues():
    """Create test files with security issues at known line numbers."""
    
    # Create test directory
    test_dir = "test_code_snippets"
    os.makedirs(test_dir, exist_ok=True)
    
    # Create a Bicep file with security issues
    bicep_file = os.path.join(test_dir, "azure_storage.bicep")
    bicep_content = """// Azure Storage Account Configuration
// This file contains intentional security issues for testing code snippets

param storageAccountName string = 'testsecuritystorage'
param location string = resourceGroup().location
param environment string = 'development'
param tags object = {
  Environment: environment
  Purpose: 'Security Testing'
  Owner: 'DevSecOps Team'
}

// Resource Group
resource rg 'Microsoft.Resources/resourceGroups@2021-04-01' = {
  name: 'rg-security-test'
  location: location
  tags: tags
}

// Storage Account with multiple security issues
resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {
  name: storageAccountName
  location: location
  kind: 'StorageV2'
  sku: {
    name: 'Standard_LRS'
  }
  properties: {
    // SECURITY ISSUE #1: Public blob access enabled (Line 29)
    allowBlobPublicAccess: true
    
    // SECURITY ISSUE #2: Weak TLS version (Line 32)
    minimumTlsVersion: 'TLS1_0'
    
    // SECURITY ISSUE #3: HTTP traffic allowed (Line 35)
    supportsHttpsTrafficOnly: false
    
    // Good configuration examples
    encryption: {
      services: {
        blob: {
          enabled: true
          keyType: 'Account'
        }
        file: {
          enabled: true
          keyType: 'Account'
        }
        queue: {
          enabled: true
          keyType: 'Service'
        }
        table: {
          enabled: true
          keyType: 'Service'
        }
      }
      keySource: 'Microsoft.Storage'
      requireInfrastructureEncryption: true
    }
    
    // SECURITY ISSUE #4: Default network access (Line 59)
    networkAcls: {
      defaultAction: 'Allow'
      bypass: 'AzureServices'
      virtualNetworkRules: []
      ipRules: []
    }
    
    // Additional properties
    accessTier: 'Hot'
    allowSharedKeyAccess: true
    isHnsEnabled: false
    isNfsV3Enabled: false
    largeFileSharesState: 'Disabled'
  }
  
  tags: tags
}

// Blob Service Configuration
resource blobService 'Microsoft.Storage/storageAccounts/blobServices@2021-04-01' = {
  parent: storageAccount
  name: 'default'
  properties: {
    cors: {
      corsRules: []
    }
    deleteRetentionPolicy: {
      enabled: false
    }
    isVersioningEnabled: false
    changeFeed: {
      enabled: false
    }
    restorePolicy: {
      enabled: false
    }
    containerDeleteRetentionPolicy: {
      enabled: false
    }
  }
}

// File Service Configuration
resource fileService 'Microsoft.Storage/storageAccounts/fileServices@2021-04-01' = {
  parent: storageAccount
  name: 'default'
  properties: {
    cors: {
      corsRules: []
    }
    shareDeleteRetentionPolicy: {
      enabled: false
    }
  }
}

// Output section
output storageAccountId string = storageAccount.id
output storageAccountName string = storageAccount.name
output primaryEndpoints object = storageAccount.properties.primaryEndpoints
output resourceGroupId string = rg.id"""

    with open(bicep_file, 'w', encoding='utf-8') as f:
        f.write(bicep_content)
    
    # Create a Terraform file with security issues
    tf_file = os.path.join(test_dir, "network_security.tf")
    tf_content = """# Network Security Configuration
# This file contains intentional security issues for testing code snippets

terraform {
  required_version = ">= 1.0"
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.0"
    }
  }
}

provider "azurerm" {
  features {}
}

# Resource Group
resource "azurerm_resource_group" "main" {
  name     = "rg-network-security-test"
  location = "East US"
  
  tags = {
    Environment = "Development"
    Purpose     = "Security Testing"
    Owner       = "DevSecOps Team"
  }
}

# Virtual Network
resource "azurerm_virtual_network" "main" {
  name                = "vnet-security-test"
  address_space       = ["10.0.0.0/16"]
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  
  tags = azurerm_resource_group.main.tags
}

# Subnet
resource "azurerm_subnet" "internal" {
  name                 = "internal"
  resource_group_name  = azurerm_resource_group.main.name
  virtual_network_name = azurerm_virtual_network.main.name
  address_prefixes     = ["********/24"]
}

# Network Security Group with security issues
resource "azurerm_network_security_group" "main" {
  name                = "nsg-security-test"
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name

  # SECURITY ISSUE #1: SSH from anywhere (Line 52)
  security_rule {
    name                       = "SSH"
    priority                   = 1001
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "22"
    source_address_prefix      = "0.0.0.0/0"
    destination_address_prefix = "*"
  }

  # SECURITY ISSUE #2: RDP from anywhere (Line 63)
  security_rule {
    name                       = "RDP"
    priority                   = 1002
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "3389"
    source_address_prefix      = "0.0.0.0/0"
    destination_address_prefix = "*"
  }

  # Good rule example
  security_rule {
    name                       = "HTTP"
    priority                   = 1003
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "80"
    source_address_prefix      = "10.0.0.0/16"
    destination_address_prefix = "*"
  }

  tags = azurerm_resource_group.main.tags
}

# Storage Account with issues
resource "azurerm_storage_account" "main" {
  name                     = "securityteststorage"
  resource_group_name      = azurerm_resource_group.main.name
  location                 = azurerm_resource_group.main.location
  account_tier             = "Standard"
  account_replication_type = "LRS"

  # SECURITY ISSUE #3: Public blob access (Line 95)
  allow_blob_public_access = true
  
  # SECURITY ISSUE #4: HTTP traffic allowed (Line 98)
  https_traffic_only = false
  
  # SECURITY ISSUE #5: Weak TLS version (Line 101)
  min_tls_version = "TLS1_0"

  tags = azurerm_resource_group.main.tags
}

# Outputs
output "resource_group_id" {
  description = "ID of the resource group"
  value       = azurerm_resource_group.main.id
}

output "storage_account_id" {
  description = "ID of the storage account"
  value       = azurerm_storage_account.main.id
}"""

    with open(tf_file, 'w', encoding='utf-8') as f:
        f.write(tf_content)
    
    return [bicep_file, tf_file]

def create_findings_with_accurate_lines(file_paths):
    """Create findings with accurate line numbers."""
    
    findings = [
        {
            "control_id": "SNIPPET-01",
            "severity": "HIGH",
            "file_path": file_paths[0],  # Bicep file
            "line": 29,
            "description": "Storage account allows public blob access. This exposes all blobs to the internet without authentication, creating a significant data exposure risk.",
            "remediation": "Set allowBlobPublicAccess to false and use private endpoints, SAS tokens, or Azure AD authentication for secure access.",
            "domain": "Data Protection"
        },
        {
            "control_id": "SNIPPET-02",
            "severity": "MEDIUM",
            "file_path": file_paths[0],  # Bicep file
            "line": 32,
            "description": "Storage account configured with weak TLS version (TLS1_0). This version has known security vulnerabilities and should not be used.",
            "remediation": "Update minimumTlsVersion to 'TLS1_2' or higher to ensure secure data transmission.",
            "domain": "Data Protection"
        },
        {
            "control_id": "SNIPPET-03",
            "severity": "CRITICAL",
            "file_path": file_paths[1],  # Terraform file
            "line": 59,
            "description": "Network Security Group allows SSH access from any source (0.0.0.0/0). This creates a critical security vulnerability by exposing SSH to the entire internet.",
            "remediation": "Restrict SSH access to specific IP ranges, use Azure Bastion for secure access, or implement just-in-time (JIT) access.",
            "domain": "Network Security"
        },
        {
            "control_id": "SNIPPET-04",
            "severity": "CRITICAL",
            "file_path": file_paths[1],  # Terraform file
            "line": 70,
            "description": "Network Security Group allows RDP access from any source (0.0.0.0/0). This exposes Windows remote desktop to internet-based attacks.",
            "remediation": "Restrict RDP access to specific management networks or use Azure Bastion for secure remote access.",
            "domain": "Network Security"
        },
        {
            "control_id": "SNIPPET-05",
            "severity": "HIGH",
            "file_path": file_paths[1],  # Terraform file
            "line": 95,
            "description": "Storage account allows public blob access. This exposes storage data to the internet without authentication.",
            "remediation": "Set allow_blob_public_access to false and implement proper access controls.",
            "domain": "Data Protection"
        }
    ]
    
    return findings

def main():
    """Main test function."""
    
    print("🧪 Testing Embedded Code Snippets with ±100 Lines Context...")
    
    # Create test files
    file_paths = create_test_files_with_known_issues()
    print(f"✅ Created test files:")
    for file_path in file_paths:
        file_size = os.path.getsize(file_path)
        with open(file_path, 'r') as f:
            line_count = sum(1 for _ in f)
        print(f"   📄 {file_path} ({line_count} lines, {file_size:,} bytes)")
    
    # Create findings
    findings = create_findings_with_accurate_lines(file_paths)
    print(f"✅ Created {len(findings)} findings with accurate line numbers")
    
    # Initialize reviewer
    reviewer = SecurityPRReviewer(local_folder="./")
    
    # Generate HTML report with embedded code snippets
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    html_path = f"embedded_code_snippets_report_{timestamp}.html"
    
    print(f"📄 Generating report with embedded code snippets...")
    reviewer._export_findings_to_html(findings, html_path)
    
    print(f"✅ Report generated: {html_path}")
    print(f"💾 File size: {os.path.getsize(html_path):,} bytes")
    
    print(f"\n🎯 Test the Embedded Code Snippets:")
    print(f"   1. Open {html_path} in your browser")
    print(f"   2. Each finding now shows the actual code with ±100 lines context")
    print(f"   3. The problematic line is highlighted with a red border and marker")
    print(f"   4. Click 'Copy Code (±100 lines)' to copy the entire snippet")
    print(f"   5. No need to click 'View Code' - the code is embedded directly!")
    
    print(f"\n📊 Expected Features:")
    print(f"   ✅ Real file content displayed inline")
    print(f"   ✅ ±100 lines context around each security issue")
    print(f"   ✅ Highlighted problematic lines with security markers")
    print(f"   ✅ Line numbers with accurate references")
    print(f"   ✅ Copy functionality for code snippets")
    print(f"   ✅ File information showing line ranges")
    
    return html_path

if __name__ == "__main__":
    try:
        html_path = main()
        
        # Optional: Open in browser
        import webbrowser
        user_input = input(f"\n🚀 Open the embedded code snippets report in your browser? (y/n): ")
        if user_input.lower() in ['y', 'yes']:
            webbrowser.open(f"file://{os.path.abspath(html_path)}")
            print("🌐 Report opened in your browser!")
        
        print("\n🎉 Embedded code snippets test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
