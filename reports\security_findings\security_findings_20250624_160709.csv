File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
Grafana.deploymentTemplate.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,38.0,"The 'publicNetworkAccess' property is set to 'Enabled' for the Microsoft.Dashboard/grafana resource. This exposes the Grafana instance to the public internet, creating an initial access vector for attackers. If compromised, attackers could gain access to monitoring data, credentials, or use the service as a pivot point for lateral movement within the Azure environment. The blast radius includes potential data exposure and compromise of connected Azure resources.","Set 'publicNetworkAccess' to 'Disabled' to restrict access to the Grafana instance via private endpoints only. Implement Azure Private Link and ensure only trusted VNets/subnets can access the resource. Example: ""publicNetworkAccess"": ""Disabled"". Reference: Azure Security Benchmark v3.0 NS-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
Grafana.deploymentTemplate.json,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,38.0,"The 'publicNetworkAccess' property is set to 'Enabled', allowing the Grafana instance to be accessible from any network, including the public internet. This violates network segmentation and isolation best practices, increasing the risk of unauthorized access, brute-force attacks, and exposure of sensitive monitoring data. The attack vector is direct network access to the management and data plane of the Grafana service.","Restrict network access by disabling 'publicNetworkAccess' and integrating the resource with a private VNet using private endpoints. Apply Network Security Groups (NSGs) to limit access to only required subnets. Reference: Azure Security Benchmark v3.0 NS-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
Grafana.deploymentTemplate.json,NS-3,Network Security,Deploy firewall at the edge of enterprise network,CRITICAL,38.0,"The Grafana instance is exposed to the public internet ('publicNetworkAccess': 'Enabled') without evidence of Azure Firewall or equivalent network filtering. This allows unrestricted inbound and outbound traffic, increasing the risk of exploitation via known vulnerabilities or brute-force attacks. Attackers could bypass perimeter defenses and directly target the service.","Deploy Azure Firewall or a similar network security appliance to filter and monitor traffic to the Grafana instance. Restrict allowed IP ranges and enforce deny-by-default rules. Reference: Azure Security Benchmark v3.0 NS-3.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal) | [Virtual network traffic routing](https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview) | [Azure Firewall Manager](https://docs.microsoft.com/azure/firewall-manager/overview) | [Hub-spoke topology](https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Use Azure Firewall for stateful application layer traffic restriction and central management. Create user-defined routes (UDR) for complex topologies. Block known bad IPs and high-risk protocols.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal) | [Virtual network traffic routing](https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview) | [Azure Firewall Manager](https://docs.microsoft.com/azure/firewall-manager/overview) | [Hub-spoke topology](https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Use Azure Firewall for stateful application layer traffic restriction and central management. Create user-defined routes (UDR) for complex topologies. Block known bad IPs and high-risk protocols.,"Enhanced Implementation Context:
• Azure Firewall deployment: https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal
• Virtual network traffic routing: https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview
• Azure Firewall Manager: https://docs.microsoft.com/azure/firewall-manager/overview
• Hub-spoke topology: https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 4.4, 4.8, 13.10
• NIST SP800-53 r4: AC-4, SC-7, CM-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Management ports should be closed on your virtual machines
• Management ports of virtual machines should be protected with just-in-time network access control
• IP Forwarding on your virtual machine should be disabled
• All Internet traffic should be routed via your deployed Azure Firewall",ai_analysis,,Validated
Grafana.deploymentTemplate.json,NS-10,Network Security,Ensure Domain Name System (DNS) security,HIGH,27.0,"The 'autoGeneratedDomainNameLabelScope' property is set to 'TenantReuse', which may allow subdomain reuse across the tenant. This increases the risk of subdomain takeover if the resource is deleted and the DNS entry is not properly cleaned up, potentially enabling attackers to hijack the DNS name and intercept traffic or credentials.","Set 'autoGeneratedDomainNameLabelScope' to 'NoReuse' to prevent subdomain reuse and reduce the risk of DNS-based attacks. Regularly audit DNS records for orphaned entries. Reference: Azure Security Benchmark v3.0 NS-10.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/dns/dns-overview) | [NIST DNS Deployment Guide](https://csrc.nist.gov/publications/detail/sp/800-81/2/final) | [Azure Private DNS](https://docs.microsoft.com/azure/dns/private-dns-overview) | [Azure Defender for DNS](https://docs.microsoft.com/azure/security-center/defender-for-dns-introduction) | [Prevent dangling DNS entries](https://docs.microsoft.com/azure/security/fundamentals/subdomain-takeover) | [DNS security best practices](https://docs.microsoft.com/azure/security/fundamentals/network-best-practices#secure-name-resolution) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Use Azure recursive DNS or trusted external DNS servers. Use Azure Private DNS for private zones. Use Azure Defender for DNS for advanced protection against DNS threats including data exfiltration mal...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/dns/dns-overview) | [NIST DNS Deployment Guide](https://csrc.nist.gov/publications/detail/sp/800-81/2/final) | [Azure Private DNS](https://docs.microsoft.com/azure/dns/private-dns-overview) | [Azure Defender for DNS](https://docs.microsoft.com/azure/security-center/defender-for-dns-introduction) | [Prevent dangling DNS entries](https://docs.microsoft.com/azure/security/fundamentals/subdomain-takeover) | [DNS security best practices](https://docs.microsoft.com/azure/security/fundamentals/network-best-practices#secure-name-resolution) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Use Azure recursive DNS or trusted external DNS servers. Use Azure Private DNS for private zones. Use Azure Defender for DNS for advanced protection against DNS threats including data exfiltration malware communication and DNS attacks.,"Enhanced Implementation Context:
• Azure DNS overview: https://docs.microsoft.com/azure/dns/dns-overview
• NIST DNS Deployment Guide: https://csrc.nist.gov/publications/detail/sp/800-81/2/final
• Azure Private DNS: https://docs.microsoft.com/azure/dns/private-dns-overview
• Azure Defender for DNS: https://docs.microsoft.com/azure/security-center/defender-for-dns-introduction
• Prevent dangling DNS entries: https://docs.microsoft.com/azure/security/fundamentals/subdomain-takeover
• DNS security best practices: https://docs.microsoft.com/azure/security/fundamentals/network-best-practices#secure-name-resolution
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 4.9, 9.2
• NIST SP800-53 r4: SC-20, SC-21
• PCI-DSS v3.2.1: Not specified

Azure Policy Examples:
• Azure Defender for DNS should be enabled
• Configure trusted DNS servers for virtual networks
• Monitor DNS queries for malicious activity
• Implement DNS filtering and threat protection",ai_analysis,,Validated
KustoScripts.template.json,DP-1,Data Protection,Discover classify and label sensitive data,HIGH,53.0,"The 'scriptContent' property at line 53 references a variable ('variables('$fxv#0')') for Kusto script deployment, but there is no evidence of data classification, labeling, or inventory for sensitive data within the script or its metadata. Attackers gaining access to this script could exfiltrate or manipulate unclassified sensitive data, increasing the blast radius of a compromise.","Implement Azure Purview or Azure Information Protection to classify and label all data assets referenced or manipulated by Kusto scripts. Ensure that all scripts and data sources are inventoried and classified according to organizational data protection policies. Reference: DP-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources)

🔵 Azure Guidance: Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other loca...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources),Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other locations.,"Enhanced Implementation Context:
• Data classification overview: https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification
• Azure Purview sensitivity labels: https://docs.microsoft.com/azure/purview/create-sensitivity-label
• Azure Information Protection: https://docs.microsoft.com/azure/information-protection/what-is-information-protection
• Azure SQL Data Discovery: https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification
• Azure Purview data sources: https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources

Compliance Mappings:
• CIS Controls v8: 3.2, 3.7, 3.13
• NIST SP800-53 r4: RA-2, SC-28
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Sensitive data in your SQL databases should be classified
• Implement data discovery and classification across all data stores
• Deploy Azure Purview for enterprise data governance
• Configure sensitivity labels for all data assets",ai_analysis,,Validated
KustoScripts.template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,HIGH,55.0,"The 'continueOnErrors' property is set to true at line 55, which may allow Kusto script execution to proceed even if errors occur. This can mask unauthorized data access or exfiltration attempts, as failures in data protection logic may not halt deployment, enabling attackers to bypass controls and increase the risk of undetected data compromise.","Set 'continueOnErrors' to false for all critical data operations to ensure that any error in script execution halts the process and triggers alerting. Implement monitoring and alerting for anomalous script execution and data access using Azure Defender for Storage, SQL, and Kusto. Reference: DP-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
KustoScripts.template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,105.0,"""continueOnErrors"": true is set for Kusto script deployment. This setting allows the deployment to proceed even if the script fails, which can result in incomplete or inconsistent data processing. Attackers could exploit this by introducing errors that are silently ignored, potentially bypassing data validation or masking data exfiltration attempts. The blast radius includes undetected data loss, corruption, or unauthorized data access due to unmonitored failures.","Set ""continueOnErrors"" to false for all Kusto script deployments to ensure that any error halts the deployment and triggers alerting. Implement monitoring and alerting for failed script executions. Reference: Azure Security Benchmark DP-2 (Monitor for anomalies around sensitive data such as unauthorized transfer to locations outside enterprise visibility and control).

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
KustoScripts.template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,176.0,"The 'continueOnErrors' property is set to true for the Kusto script deployment. This configuration allows the deployment to proceed even if errors occur during script execution, which can result in incomplete or inconsistent data processing. Attackers could exploit this by introducing errors that bypass critical data validation or security logic, increasing the risk of unauthorized data access or exfiltration. The blast radius includes potential data integrity compromise and undetected data exposure.","Set 'continueOnErrors' to false to ensure that any errors during script execution halt the deployment process. This enforces strict error handling and prevents partial or insecure deployments. Additionally, implement monitoring and alerting for failed script executions to ensure rapid response to anomalies. Reference: Azure Security Benchmark DP-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
KustoScripts.template.json,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,224.0,"The function 'fun_FetchLabelValueByRegionsInJson' (line 224) processes dynamic input parameters (_input_monikers) and region identifiers without any evidence of data classification, labeling, or inventory of sensitive data. Attackers who gain access to this function or its upstream data sources could enumerate or exfiltrate sensitive identifiers or metadata, increasing the blast radius of a compromise. Lack of data classification impedes detection and response to data exposure.","Implement Azure Purview or Azure Information Protection to classify and label all sensitive data processed by this function. Ensure that all Kusto tables and dynamic inputs are inventoried and classified according to organizational data protection policies. Reference: ASB DP-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources)

🔵 Azure Guidance: Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other loca...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources),Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other locations.,"Enhanced Implementation Context:
• Data classification overview: https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification
• Azure Purview sensitivity labels: https://docs.microsoft.com/azure/purview/create-sensitivity-label
• Azure Information Protection: https://docs.microsoft.com/azure/information-protection/what-is-information-protection
• Azure SQL Data Discovery: https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification
• Azure Purview data sources: https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources

Compliance Mappings:
• CIS Controls v8: 3.2, 3.7, 3.13
• NIST SP800-53 r4: RA-2, SC-28
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Sensitive data in your SQL databases should be classified
• Implement data discovery and classification across all data stores
• Deploy Azure Purview for enterprise data governance
• Configure sensitivity labels for all data assets",ai_analysis,,Validated
KustoScripts.template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,224.0,"The function 'fun_FetchLabelValueByRegionsInJson' (line 224) does not include any monitoring or alerting for anomalous access or data exfiltration attempts. Without monitoring, attackers could leverage this function to enumerate or extract sensitive label values across regions without detection, enabling data exfiltration and lateral movement.","Enable Azure Defender for SQL, Storage, and Kusto to monitor for anomalous data access and exfiltration. Integrate monitoring with SIEM for alerting on suspicious queries or access patterns. Reference: ASB DP-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
KustoScripts.template.json,IM-3,Identity Management,Manage application identities securely and automatically,HIGH,224.0,"The function 'fun_FetchLabelValueByRegionsInJson' (line 224) does not specify the use of managed identities or service principals for access control. If this function is invoked by Logic Apps or other automation, lack of managed identity increases the risk of credential exposure and privilege escalation, as attackers could exploit hardcoded or less-secure credentials.","Configure the Logic App or automation invoking this function to use Azure Managed Identity for authentication. Remove any hardcoded credentials and restrict permissions to least privilege. Reference: ASB IM-3.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview) | [Services supporting managed identities](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities) | [Azure service principal creation](https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps) | [Service principal with certificates](https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Use Azure managed identities for services supporting Azure AD authentication. For services without managed identity support use Azure AD service principals with certificate credentials and restricted ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview) | [Services supporting managed identities](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities) | [Azure service principal creation](https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps) | [Service principal with certificates](https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),Use Azure managed identities for services supporting Azure AD authentication. For services without managed identity support use Azure AD service principals with certificate credentials and restricted permissions.,"Enhanced Implementation Context:
• Azure managed identities overview: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview
• Services supporting managed identities: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities
• Azure service principal creation: https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps
• Service principal with certificates: https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: Not specified
• NIST SP800-53 r4: AC-2, AC-3, IA-4, IA-5, IA-9
• PCI-DSS v3.2.1: Not applicable

Azure Policy Examples:
• Managed identity should be used in your Function App
• Managed identity should be used in your Web App
• Service principals should be used to protect your subscriptions instead of management certificates
• Managed identity should be used in your API App
• Virtual machines' Guest Configuration extension should be deployed with system-assigned managed identity",ai_analysis,,Validated
KustoScripts.template.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,291.0,"The configuration sets ""continueOnErrors"": true, which can allow the deployment to proceed even if critical errors occur in data processing or script execution. This can result in incomplete or inconsistent data being processed or exposed, potentially allowing attackers to exploit partial deployments, bypass data validation, or access sensitive information in an unprotected state. The blast radius includes the risk of data in transit being exposed or manipulated due to incomplete security controls being enforced.","Set ""continueOnErrors"" to false to ensure that any error in the deployment or script execution halts the process, preventing partial or insecure deployments. Additionally, implement robust error handling and monitoring to detect and respond to failures. Reference: Azure Security Benchmark DP-3 (Protect data in transit and ensure secure deployment states).

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account)

🔵 Azure Guidance: Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windo...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account),Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windows remote management.,"Enhanced Implementation Context:
• Double encryption for Azure data in transit: https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit
• Encryption in transit overview: https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit
• TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem
• Azure Storage secure transfer: https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account

Compliance Mappings:
• CIS Controls v8: 3.10
• NIST SP800-53 r4: SC-8
• PCI-DSS v3.2.1: 3.5, 3.6, 4.1

Azure Policy Examples:
• Kubernetes clusters should be accessible only over HTTPS
• Only secure connections to your Azure Cache for Redis should be enabled
• FTPS only should be required in your Function App
• Secure transfer to storage accounts should be enabled
• Function App should only be accessible over HTTPS
• Latest TLS version should be used in your API App
• Web Application should only be accessible over HTTPS
• Enforce SSL connection should be enabled for PostgreSQL database servers
• Latest TLS version should be used in your Web App",ai_analysis,,Validated
KustoScripts.template.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,319.0,"The property 'continueOnErrors' is set to true, which may allow the deployment to proceed even if there are errors in the script execution. This can result in incomplete or insecure deployments, potentially leaving sensitive data flows unprotected or misconfigured. Attackers could exploit this by targeting partially deployed or misconfigured resources, increasing the risk of data-in-transit exposure and unauthorized access.","Set 'continueOnErrors' to false to ensure that any errors during deployment halt the process, allowing for immediate investigation and remediation. This enforces strict deployment integrity and reduces the risk of insecure or incomplete resource configuration. Review all deployment scripts to ensure secure transfer and encryption settings are enforced as per Azure Security Benchmark DP-3.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account)

🔵 Azure Guidance: Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windo...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account),Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windows remote management.,"Enhanced Implementation Context:
• Double encryption for Azure data in transit: https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit
• Encryption in transit overview: https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit
• TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem
• Azure Storage secure transfer: https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account

Compliance Mappings:
• CIS Controls v8: 3.10
• NIST SP800-53 r4: SC-8
• PCI-DSS v3.2.1: 3.5, 3.6, 4.1

Azure Policy Examples:
• Kubernetes clusters should be accessible only over HTTPS
• Only secure connections to your Azure Cache for Redis should be enabled
• FTPS only should be required in your Function App
• Secure transfer to storage accounts should be enabled
• Function App should only be accessible over HTTPS
• Latest TLS version should be used in your API App
• Web Application should only be accessible over HTTPS
• Enforce SSL connection should be enabled for PostgreSQL database servers
• Latest TLS version should be used in your Web App",ai_analysis,,Validated
roleAssignment.deploymentTemplate.json,IM-2,Identity Management,Protect identity and authentication systems,CRITICAL,11.0,"The 'builtInRoleType' parameter is set to 'Owner' by default, and line 26 assigns the 'Owner' roleDefinitionId (8e3af657-a8ff-443c-a75c-2fe8c4bcb635) to the specified principal. Assigning the Owner role grants full administrative privileges over the subscription or resource group, enabling initial access, privilege escalation, lateral movement, and potential full environment compromise if the principal is misconfigured or compromised. The blast radius includes all resources within the scope of the assignment.","Restrict the use of the 'Owner' role. Assign the least privileged built-in role necessary for the principal's function (e.g., 'Contributor' or custom roles with limited permissions). Implement Privileged Identity Management (PIM) for just-in-time elevation and require multi-factor authentication (MFA) for all privileged roles. Review and monitor all role assignments for excessive permissions. Reference: Azure Security Benchmark v3.0 IM-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score) | [Active Directory security best practices](https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory) | [Azure AD security baseline](https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline) | [Privileged Identity Management](https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Use Azure AD security baseline and Identity Secure Score to evaluate security posture. Implement limited administrative roles user risk policies MFA for administrative roles and block legacy authentic...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score) | [Active Directory security best practices](https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory) | [Azure AD security baseline](https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline) | [Privileged Identity Management](https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),Use Azure AD security baseline and Identity Secure Score to evaluate security posture. Implement limited administrative roles user risk policies MFA for administrative roles and block legacy authentication.,"Enhanced Implementation Context:
• Azure AD Identity Secure Score: https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score
• Active Directory security best practices: https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory
• Azure AD security baseline: https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline
• Privileged Identity Management: https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 5.4, 6.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8, SI-4
• PCI-DSS v3.2.1: 8.2, 8.3

Azure Policy Examples:
• No applicable built-in policy (requires configuration-based implementation)
• Use Azure AD Identity Secure Score recommendations
• Implement Azure AD security baseline configurations
• Monitor privileged account activities through Azure AD logs",ai_analysis,,Validated
roleAssignment.deploymentTemplate.json,IM-6,Identity Management,Use strong authentication controls,CRITICAL,13.0,"The template assigns the 'Owner' role to a principal without enforcing strong authentication controls such as multi-factor authentication (MFA). This enables an attacker with access to the principal's credentials to gain full administrative control, increasing the risk of privilege escalation and environment takeover.","Enforce MFA for all accounts with Owner or other privileged roles. Use Conditional Access policies to require strong authentication for all privileged actions. Regularly audit privileged accounts and role assignments. Reference: Azure Security Benchmark v3.0 IM-6.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/authentication/howto-mfa-getstarted) | [Passwordless authentication options](https://docs.microsoft.com/azure/active-directory/authentication/concept-authentication-passwordless) | [Azure AD password policies](https://docs.microsoft.com/azure/active-directory/authentication/concept-sspr-policy#password-policies-that-only-apply-to-cloud-user-accounts) | [Azure AD Password Protection](https://docs.microsoft.com/azure/active-directory/authentication/concept-password-ban-bad) | [Block legacy authentication](https://docs.microsoft.com/azure/active-directory/conditional-access/block-legacy-authentication)

🔵 Azure Guidance: Use Azure AD passwordless authentication as default method (Windows Hello Microsoft Authenticator FIDO2 Keys). Enable Azure MFA for all users with conditional access policies. Block legacy authenticat...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/authentication/howto-mfa-getstarted) | [Passwordless authentication options](https://docs.microsoft.com/azure/active-directory/authentication/concept-authentication-passwordless) | [Azure AD password policies](https://docs.microsoft.com/azure/active-directory/authentication/concept-sspr-policy#password-policies-that-only-apply-to-cloud-user-accounts) | [Azure AD Password Protection](https://docs.microsoft.com/azure/active-directory/authentication/concept-password-ban-bad) | [Block legacy authentication](https://docs.microsoft.com/azure/active-directory/conditional-access/block-legacy-authentication),Use Azure AD passwordless authentication as default method (Windows Hello Microsoft Authenticator FIDO2 Keys). Enable Azure MFA for all users with conditional access policies. Block legacy authentication.,"Enhanced Implementation Context:
• Azure MFA deployment: https://docs.microsoft.com/azure/active-directory/authentication/howto-mfa-getstarted
• Passwordless authentication options: https://docs.microsoft.com/azure/active-directory/authentication/concept-authentication-passwordless
• Azure AD password policies: https://docs.microsoft.com/azure/active-directory/authentication/concept-sspr-policy#password-policies-that-only-apply-to-cloud-user-accounts
• Azure AD Password Protection: https://docs.microsoft.com/azure/active-directory/authentication/concept-password-ban-bad
• Block legacy authentication: https://docs.microsoft.com/azure/active-directory/conditional-access/block-legacy-authentication

Compliance Mappings:
• CIS Controls v8: 6.3, 6.4
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-5, IA-8
• PCI-DSS v3.2.1: 7.2, 8.2, 8.3, 8.4

Azure Policy Examples:
• Authentication to Linux machines should require SSH keys
• MFA should be enabled on accounts with write permissions on your subscription
• MFA should be enabled on accounts with owner permissions on your subscription
• MFA should be enabled on accounts with read permissions on your subscription",ai_analysis,,Validated
roleAssignment.deploymentParameters.json,IM-2,Identity Management,Protect identity and authentication systems,HIGH,6.0,"The 'builtInRoleType' parameter is set to 'Owner', which grants full administrative privileges to the assigned principal. Assigning the Owner role via deployment parameters without enforcing strong authentication (such as MFA) or privileged identity management increases the risk of privilege escalation and unauthorized access. Attackers who compromise the principalId or deployment pipeline could gain unrestricted access to all resources in the subscription, resulting in a total environment compromise (maximum blast radius).","Restrict assignment of the 'Owner' role to only those identities that are protected by Azure AD Privileged Identity Management (PIM) and require multi-factor authentication (MFA) for activation. Use least privilege by assigning more restrictive roles (such as 'Contributor' or custom roles) where possible. Implement Azure AD Conditional Access policies to enforce strong authentication for all privileged operations. Reference: Azure Security Benchmark IM-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score) | [Active Directory security best practices](https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory) | [Azure AD security baseline](https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline) | [Privileged Identity Management](https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Use Azure AD security baseline and Identity Secure Score to evaluate security posture. Implement limited administrative roles user risk policies MFA for administrative roles and block legacy authentic...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score) | [Active Directory security best practices](https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory) | [Azure AD security baseline](https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline) | [Privileged Identity Management](https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),Use Azure AD security baseline and Identity Secure Score to evaluate security posture. Implement limited administrative roles user risk policies MFA for administrative roles and block legacy authentication.,"Enhanced Implementation Context:
• Azure AD Identity Secure Score: https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score
• Active Directory security best practices: https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory
• Azure AD security baseline: https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline
• Privileged Identity Management: https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 5.4, 6.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8, SI-4
• PCI-DSS v3.2.1: 8.2, 8.3

Azure Policy Examples:
• No applicable built-in policy (requires configuration-based implementation)
• Use Azure AD Identity Secure Score recommendations
• Implement Azure AD security baseline configurations
• Monitor privileged account activities through Azure AD logs",ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 16,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 0,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-24T16:07:09.263460,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
