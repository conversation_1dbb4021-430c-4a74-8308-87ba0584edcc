{"metadata": {"version": "1.0", "source": "MITRE ATT&CK for Cloud", "framework_version": "v13.1", "created_date": "2025-06-20T00:00:00.000000", "focus": "Azure Cloud Infrastructure", "total_techniques": 25, "tactics_covered": 11}, "tactics_and_techniques": {"initial_access": {"tactic_id": "TA0001", "tactic_name": "Initial Access", "techniques": [{"technique_id": "T1190", "technique_name": "Exploit Public-Facing Application", "description": "Adversaries may attempt to take advantage of a weakness in an Internet-facing computer or program using software, data, or commands in order to cause unintended or unanticipated behavior.", "azure_resources": ["App Services", "API Management", "Application Gateway", "<PERSON><PERSON>r", "Virtual Machines"], "detection_indicators": ["Public endpoints without WAF", "Unpatched services", "Weak authentication", "Missing rate limiting"], "related_asb_controls": ["NS-2", "NS-6", "AS-1"], "severity_impact": "CRITICAL"}, {"technique_id": "T1078", "technique_name": "Valid Accounts", "description": "Adversaries may obtain and abuse credentials of existing accounts as a means of gaining Initial Access, Persistence, Privilege Escalation, or Defense Evasion.", "azure_resources": ["Azure AD", "Service Principals", "Managed Identities", "Storage Accounts", "<PERSON>"], "detection_indicators": ["Weak password policies", "Missing MFA", "Default credentials", "Excessive permissions"], "related_asb_controls": ["IM-1", "IM-2", "IM-6"], "severity_impact": "HIGH"}, {"technique_id": "T1133", "technique_name": "External Remote Services", "description": "Adversaries may leverage external-facing remote services to initially access and/or persist within a network.", "azure_resources": ["VPN Gateway", "Bastion Host", "Virtual Machines", "Remote Desktop"], "detection_indicators": ["Weak VPN configurations", "Missing JIT access", "Open RDP/SSH ports", "No network segmentation"], "related_asb_controls": ["NS-1", "NS-3", "NS-7"], "severity_impact": "HIGH"}]}, "privilege_escalation": {"tactic_id": "TA0004", "tactic_name": "Privilege Escalation", "techniques": [{"technique_id": "T1068", "technique_name": "Exploitation for Privilege Escalation", "description": "Adversaries may exploit software vulnerabilities in an attempt to elevate privileges.", "azure_resources": ["Virtual Machines", "Container Instances", "AKS Clusters", "App Services"], "detection_indicators": ["Unpatched systems", "Privileged containers", "Weak RBAC policies", "Missing security updates"], "related_asb_controls": ["CS-1", "PA-1", "PA-2"], "severity_impact": "HIGH"}, {"technique_id": "T1078.004", "technique_name": "Cloud Accounts", "description": "Adversaries may obtain and abuse credentials of a cloud account as a means of gaining Initial Access, Persistence, Privilege Escalation, or Defense Evasion.", "azure_resources": ["Azure AD", "Subscription Management", "Resource Groups", "RBAC Assignments"], "detection_indicators": ["Excessive RBAC permissions", "Global admin accounts", "Service principal abuse", "Missing conditional access"], "related_asb_controls": ["IM-3", "PA-1", "PA-3"], "severity_impact": "CRITICAL"}]}, "lateral_movement": {"tactic_id": "TA0008", "tactic_name": "Lateral Movement", "techniques": [{"technique_id": "T1021", "technique_name": "Remote Services", "description": "Adversaries may use Valid Accounts to log into a service specifically designed to accept remote connections.", "azure_resources": ["Virtual Networks", "Network Security Groups", "Virtual Machines", "Private Endpoints"], "detection_indicators": ["Missing network segmentation", "Open internal ports", "Weak NSG rules", "No micro-segmentation"], "related_asb_controls": ["NS-1", "NS-3", "NS-5"], "severity_impact": "HIGH"}, {"technique_id": "T1550", "technique_name": "Use Alternate Authentication Material", "description": "Adversaries may use alternate authentication material, such as password hashes, Kerberos tickets, and application access tokens.", "azure_resources": ["Service Principals", "Managed Identities", "Access Tokens", "Certificates"], "detection_indicators": ["Long-lived tokens", "Shared service principals", "Weak certificate management", "Missing token rotation"], "related_asb_controls": ["IM-3", "DP-6", "DP-7"], "severity_impact": "MEDIUM"}]}, "exfiltration": {"tactic_id": "TA0010", "tactic_name": "Exfiltration", "techniques": [{"technique_id": "T1041", "technique_name": "Exfiltration Over C2 Channel", "description": "Adversaries may steal data by exfiltrating it over an existing command and control channel.", "azure_resources": ["Storage Accounts", "Databases", "Virtual Machines", "Network Traffic"], "detection_indicators": ["Unencrypted data transfer", "Missing DLP policies", "No traffic monitoring", "Weak access controls"], "related_asb_controls": ["DP-2", "DP-3", "LT-4"], "severity_impact": "CRITICAL"}, {"technique_id": "T1537", "technique_name": "Transfer Data to Cloud Account", "description": "Adversaries may exfiltrate data by transferring the data to another cloud account they control on the same service.", "azure_resources": ["Storage Accounts", "Blob Storage", "File Shares", "Databases"], "detection_indicators": ["Public storage access", "Missing access logging", "Weak authentication", "No data classification"], "related_asb_controls": ["DP-1", "DP-2", "NS-2"], "severity_impact": "HIGH"}]}}, "azure_resource_technique_matrix": {"Microsoft.Storage/storageAccounts": ["T1190", "T1078", "T1537", "T1041"], "Microsoft.Compute/virtualMachines": ["T1190", "T1078", "T1133", "T1068", "T1021"], "Microsoft.Web/sites": ["T1190", "T1078", "T1068"], "Microsoft.ContainerService/managedClusters": ["T1190", "T1068", "T1610", "T1611"], "Microsoft.Network/networkSecurityGroups": ["T1021", "T1133", "T1190"], "Microsoft.KeyVault/vaults": ["T1078", "T1550", "T1552"]}, "confidence_scoring_rules": {"high_confidence_techniques": ["T1190", "T1078.004", "T1041", "T1537"], "medium_confidence_techniques": ["T1068", "T1021", "T1550", "T1078"], "context_dependent_techniques": ["T1133", "T1552", "T1083"]}}