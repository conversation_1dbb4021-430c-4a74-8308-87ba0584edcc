Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
CRITICAL,DP-3,Parameters\LacpStamp.Parameters-LacpStampResources.json,74,Parameter 'dasStorageAccountKey' at line 74 contains a storage account key value reference directly in the template. Storing sensitive information such as storage account keys in parameters violates ASB DP-3: Manage sensitive information disclosure.,Store sensitive values such as storage account keys in Azure Key Vault and reference them securely in the template using a key vault reference.,N/A,AI,Generic
CRITICAL,NS-1,Templates\IngestionStorageAccount.Template.json,38,"Storage account resource (Microsoft.Storage/storageAccounts) at line 38 does not specify network security controls such as networkAcls, network rules, or integration with NSGs or Azure Firewall. This exposes the storage account to potential unauthorized network access, violating ASB NS-1.","Add the 'networkAcls' property to the storage account resource to restrict access by IP, virtual network, or private endpoint. Integrate with NSGs or Azure Firewall to control network traffic to the storage account.",N/A,AI,Generic
CRITICAL,NS-1,Templates\IngestionStorageAccount.Template.json,56,"Storage account resource (Microsoft.Storage/storageAccounts) at line 56 does not specify network security controls such as networkAcls, network rules, or integration with NSGs or Azure Firewall. This exposes the storage account to potential unauthorized network access, violating ASB NS-1.","Add the 'networkAcls' property to the storage account resource to restrict access by IP, virtual network, or private endpoint. Integrate with NSGs or Azure Firewall to control network traffic to the storage account.",N/A,AI,Generic
CRITICAL,NS-2,Templates\IngestionStorageAccount.Template.json,38,"Storage account resource (Microsoft.Storage/storageAccounts) at line 38 does not restrict public network access. Absence of 'networkAcls' or 'publicNetworkAccess' property may allow public endpoints, violating ASB NS-2.",Explicitly set 'publicNetworkAccess' to 'Disabled' and configure 'networkAcls' to restrict access to only trusted networks or private endpoints.,N/A,AI,Generic
CRITICAL,NS-2,Templates\IngestionStorageAccount.Template.json,56,"Storage account resource (Microsoft.Storage/storageAccounts) at line 56 does not restrict public network access. Absence of 'networkAcls' or 'publicNetworkAccess' property may allow public endpoints, violating ASB NS-2.",Explicitly set 'publicNetworkAccess' to 'Disabled' and configure 'networkAcls' to restrict access to only trusted networks or private endpoints.,N/A,AI,Generic
CRITICAL,NS-1,Templates\LacpBilling.Template.json,563,"Microsoft.Storage/storageAccounts resource at line 563 does not restrict network access using network security groups (NSGs) or Azure Firewall. No network rules or firewall settings are defined, leaving the storage account potentially exposed.","Configure network rules for the storage account to restrict access. Use 'networkAcls' property to allow only trusted subnets or private endpoints, and consider integrating with Azure Firewall or NSGs.",N/A,AI,Generic
CRITICAL,DP-1,Templates\LacpBillingExhaust.Template.json,38,"Microsoft.Kusto/clusters resource at line 38 does not specify 'encryption' or 'keyVaultProperties', indicating encryption at rest is not explicitly enabled as required by ASB DP-1.",Add the 'encryption' property to the Microsoft.Kusto/clusters resource and configure either service-managed or customer-managed keys to ensure encryption at rest.,N/A,AI,Generic
CRITICAL,DP-6,Templates\LacpBillingExhaust.Template.json,38,"Microsoft.Kusto/clusters resource at line 38 does not configure customer-managed keys (CMK) for data encryption, which is recommended for sensitive data per ASB DP-6.",Configure the 'keyVaultProperties' property in the Microsoft.Kusto/clusters resource to use customer-managed keys stored in Azure Key Vault.,N/A,AI,Generic
CRITICAL,NS-1,Templates\LacpBillingExhaust.Template.json,38,"Microsoft.Kusto/clusters resource at line 38 does not specify any network security controls such as NSGs or Azure Firewall, leaving the resource potentially exposed in violation of ASB NS-1.",Associate the Microsoft.Kusto/clusters resource with a subnet protected by a Network Security Group (NSG) or restrict access using Azure Firewall.,N/A,AI,Generic
CRITICAL,NS-2,Templates\LacpBillingExhaust.Template.json,38,"Microsoft.Kusto/clusters resource at line 38 does not restrict public endpoints or specify public network access settings, potentially exposing the cluster to the public internet in violation of ASB NS-2.",Set the 'publicNetworkAccess' property to 'Disabled' or restrict access to trusted networks only.,N/A,AI,Generic
CRITICAL,DP-1,Templates\LacpBillingExhaustExport.Template.json,61,Resource 'Microsoft.UsageBilling/accounts/dataExports' at line 61 does not specify encryption at rest settings. ASB DP-1 requires all data storage to be encrypted at rest.,Explicitly configure encryption at rest for the 'Microsoft.UsageBilling/accounts/dataExports' resource. Ensure the resource properties include encryption settings as per Azure documentation.,N/A,AI,Generic
CRITICAL,DP-2,Templates\LacpBillingExhaustExport.Template.json,61,Resource 'Microsoft.UsageBilling/accounts/dataExports' at line 61 does not specify encryption in transit (TLS 1.2+). ASB DP-2 requires all data transfers to use TLS 1.2 or higher.,Ensure that all endpoints and data transfer mechanisms for 'Microsoft.UsageBilling/accounts/dataExports' enforce TLS 1.2 or higher. Add explicit configuration for secure transport in the resource definition.,N/A,AI,Generic
CRITICAL,DP-3,Templates\LacpBillingExhaustExport.Template.json,61,"Sensitive connection information (adxExhaustUri, adxExhaustDataIngestionUri) is passed as plain parameters at line 61. ASB DP-3 requires sensitive data like keys and connection strings to be stored in Azure Key Vault.",Store sensitive parameters such as 'adxExhaustUri' and 'adxExhaustDataIngestionUri' in Azure Key Vault and reference them securely in the template using Key Vault references.,N/A,AI,Generic
CRITICAL,NS-2,Templates\LacpBillingExhaustExport.Template.json,61,"Resource 'Microsoft.UsageBilling/accounts/dataExports' at line 61 does not specify network restrictions or private endpoints, potentially exposing public endpoints. ASB NS-2 requires securing all public endpoints.",Restrict public access to the 'Microsoft.UsageBilling/accounts/dataExports' resource by configuring private endpoints or network rules to limit exposure.,N/A,AI,Generic
CRITICAL,NS-1,Templates\LacpGeo.Template.json,61,"Key Vault resource at line 61 does not restrict network access using network security groups (NSGs) or Azure Firewall. No network rules are defined, which may expose the Key Vault to public access.",Configure the Key Vault to use network ACLs to restrict access to trusted networks only. Add 'networkAcls' property with appropriate 'defaultAction' set to 'Deny' and specify allowed virtual networks and IP addresses.,N/A,AI,Generic
CRITICAL,NS-20,Templates\LacpGeo.Template.json,120,"Cosmos DB account at line 120 has 'publicNetworkAccess' set to 'Enabled', exposing the database to the public internet.",Set the 'publicNetworkAccess' property to 'Disabled' in the Cosmos DB account resource to prevent public access.,N/A,AI,Generic
CRITICAL,NS-1,Templates\LacpGlobal.Template.json,54,"CosmosDB account 'publicNetworkAccess' is set to 'Enabled' and 'isVirtualNetworkFilterEnabled' is false at line 54, exposing the resource to the public internet without network security groups or Azure Firewall.",Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to true. Restrict access using virtual network rules or integrate with Azure Firewall to protect the CosmosDB account as per ASB NS-1.,N/A,AI,Generic
CRITICAL,NS-2,Templates\LacpGlobal.Template.json,54,"CosmosDB account at line 54 has 'publicNetworkAccess' set to 'Enabled', exposing a public endpoint without restriction.",Set 'publicNetworkAccess' to 'Disabled' or restrict access using IP rules or virtual network rules to minimize public exposure as per ASB NS-2.,N/A,AI,Generic
CRITICAL,NS-3,Templates\LacpGlobal.Template.json,54,"CosmosDB account at line 54 does not implement Network Security Groups (NSGs) or any network restrictions ('isVirtualNetworkFilterEnabled' is false), allowing unrestricted inbound and outbound traffic.",Enable 'isVirtualNetworkFilterEnabled' and configure appropriate virtual network rules or NSGs to control traffic as per ASB NS-3.,N/A,AI,Generic
CRITICAL,NS-1,Templates\LacpRegion.Template.json,1007,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1007 has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false, exposing the database to the public internet without network security controls. This violates NS-1: Protect resources using network security groups or Azure Firewall.",Set 'publicNetworkAccess' to 'Disabled' and enable 'isVirtualNetworkFilterEnabled' with appropriate 'virtualNetworkRules' to restrict access to trusted networks. Consider using NSGs or Azure Firewall to further protect the resource.,N/A,AI,Generic
CRITICAL,NS-2,Templates\LacpRegion.Template.json,1007,CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1007 exposes a public endpoint by setting 'publicNetworkAccess' to 'Enabled' and not enabling virtual network filtering. This violates NS-2: Protect public endpoints.,Disable public network access by setting 'publicNetworkAccess' to 'Disabled' and configure private endpoints or virtual network rules to restrict access.,N/A,AI,Generic
CRITICAL,NS-21,Templates\LacpStamp.Template.json,682,Microsoft.Network/trafficManagerProfiles resources (line 682 and similar) expose public DNS endpoints. ASB NS-2 requires securing all public endpoints to minimize exposure.,"Restrict access to the public endpoints using IP whitelisting, authentication, or migrate to private endpoints where possible.",N/A,AI,Generic
CRITICAL,NS-21,Templates\LacpStamp.Template.json,728,Microsoft.Network/trafficManagerProfiles resources (line 728 and similar) expose public DNS endpoints. ASB NS-2 requires securing all public endpoints to minimize exposure.,"Restrict access to the public endpoints using IP whitelisting, authentication, or migrate to private endpoints where possible.",N/A,AI,Generic
CRITICAL,NS-19,Templates\LacpStamp.Template.json,1002,Microsoft.Storage/storageAccounts resources (line 1002 and similar) do not have network security groups (NSGs) or Azure Firewall configured. ASB NS-1 requires protecting sensitive resources with NSGs or Azure Firewall.,Configure network rules for the storage account to allow access only from trusted subnets protected by NSGs or Azure Firewall.,N/A,AI,Generic
CRITICAL,NS-19,Templates\LacpStamp.Template.json,1082,Microsoft.Storage/storageAccounts resources (line 1082 and similar) do not have network security groups (NSGs) or Azure Firewall configured. ASB NS-1 requires protecting sensitive resources with NSGs or Azure Firewall.,Configure network rules for the storage account to allow access only from trusted subnets protected by NSGs or Azure Firewall.,N/A,AI,Generic
CRITICAL,NS-19,Templates\LacpStamp.Template.json,1132,Microsoft.Storage/storageAccounts resources (line 1132 and similar) do not have network security groups (NSGs) or Azure Firewall configured. ASB NS-1 requires protecting sensitive resources with NSGs or Azure Firewall.,Configure network rules for the storage account to allow access only from trusted subnets protected by NSGs or Azure Firewall.,N/A,AI,Generic
CRITICAL,DP-1,Templates\ReadAdxExhaust.Template.json,23,Microsoft.Kusto/clusters resource does not explicitly enable encryption at rest. The 'properties' object does not contain an 'encryption' configuration.,"Add the 'encryption' property to the Microsoft.Kusto/clusters resource and configure it to enable encryption at rest, specifying either service-managed or customer-managed keys as appropriate.",N/A,AI,Generic
CRITICAL,DP-2,Templates\ReadAdxExhaust.Template.json,23,Microsoft.Kusto/clusters resource does not specify minimum TLS version for data in transit. No 'properties.enablePurge' or 'properties.engineType' or explicit TLS configuration is present.,Explicitly set the minimum TLS version for the Microsoft.Kusto/clusters resource by adding the 'properties.engineType' and 'properties.enablePurge' or other relevant properties to enforce TLS 1.2 or higher for all data transfers.,N/A,AI,Generic
CRITICAL,DP-6,Templates\ReadAdxExhaust.Template.json,23,Sensitive information such as cluster names and URIs are output without using Azure Key Vault or secure outputs. Outputs 'adxExhaustUri' and 'adxExhaustDataIngestionUri' may expose sensitive endpoints.,"Store sensitive outputs such as cluster URIs in Azure Key Vault and reference them securely, or mark outputs as 'secureString' to prevent disclosure.",N/A,AI,Generic
CRITICAL,DP-6,Templates\ReadAdxExhaust.Template.json,54,"Output 'adxExhaustUri' exposes the cluster URI directly, which may be sensitive information.",Mark the output as 'secureString' or store the URI in Azure Key Vault to prevent disclosure.,N/A,AI,Generic
CRITICAL,DP-6,Templates\ReadAdxExhaust.Template.json,58,"Output 'adxExhaustDataIngestionUri' exposes the data ingestion URI directly, which may be sensitive information.",Mark the output as 'secureString' or store the URI in Azure Key Vault to prevent disclosure.,N/A,AI,Generic
CRITICAL,NS-1,Templates\ReadAdxExhaust.Template.json,23,Microsoft.Kusto/clusters resource does not specify any network security group (NSG) or Azure Firewall configuration to restrict access.,Associate the cluster with a subnet protected by an NSG or configure Azure Firewall rules to restrict network access to the resource.,N/A,AI,Generic
CRITICAL,NS-2,Templates\ReadAdxExhaust.Template.json,23,"Microsoft.Kusto/clusters resource does not restrict public endpoints or specify access controls, potentially exposing the cluster to the public internet.",Disable public network access or restrict access to trusted networks by configuring 'publicNetworkAccess' property to 'Disabled' or specifying allowed IP ranges.,N/A,AI,Generic
CRITICAL,DP-3,Templates\ReadIdentity.Template.json,22,"Sensitive identity information (clientId and principalId) is output directly in the template outputs (lines 22 and 30) without using Azure Key Vault or secure parameter handling, violating ASB DP-3: Manage sensitive information disclosure.",Store sensitive identity outputs such as clientId and principalId in Azure Key Vault and reference them securely. Remove direct output of these values from the template outputs section.,N/A,AI,Generic
CRITICAL,NS-15,Templates\TrafficManagerEndpoints.Template.json,38,"The resource 'Microsoft.Network/trafficManagerProfiles/externalEndpoints' at line 38 defines a public endpoint without explicit security controls such as network security groups, firewalls, or private endpoints. This exposes the endpoint to the public internet, violating ASB control NS-15 (Protect public endpoints).","Restrict public access to the Traffic Manager external endpoint by implementing network security controls such as NSGs, Azure Firewall, or by using private endpoints where possible. Review the endpoint configuration to ensure only authorized traffic is allowed.",N/A,AI,Generic
HIGH,NS-5,Templates\IngestionStorageAccount.Template.json,38,"Storage account resource (Microsoft.Storage/storageAccounts) at line 38 does not implement a private endpoint. Lack of private endpoint configuration increases exposure risk, violating ASB NS-5.","Add a 'privateEndpointConnections' property or deploy a Microsoft.Network/privateEndpoints resource to the storage account to ensure secure, private connectivity.",N/A,AI,Generic
HIGH,NS-5,Templates\IngestionStorageAccount.Template.json,56,"Storage account resource (Microsoft.Storage/storageAccounts) at line 56 does not implement a private endpoint. Lack of private endpoint configuration increases exposure risk, violating ASB NS-5.","Add a 'privateEndpointConnections' property or deploy a Microsoft.Network/privateEndpoints resource to the storage account to ensure secure, private connectivity.",N/A,AI,Generic
HIGH,NS-5,Templates\LacpBilling.Template.json,563,"Microsoft.Storage/storageAccounts resource at line 563 does not implement private endpoints. The storage account is accessible over public endpoints, increasing exposure risk.",Add a private endpoint for the storage account and disable public network access by setting 'publicNetworkAccess' to 'Disabled'.,N/A,AI,Generic
HIGH,NS-5,Templates\LacpBillingExhaust.Template.json,38,"Microsoft.Kusto/clusters resource at line 38 does not configure private endpoints, which is recommended for secure access per ASB NS-5.","Configure a private endpoint for the Microsoft.Kusto/clusters resource to ensure secure, private connectivity.",N/A,AI,Generic
HIGH,NS-5,Templates\LacpBillingExhaustExport.Template.json,61,Resource 'Microsoft.UsageBilling/accounts/dataExports' at line 61 does not implement private endpoints for secure access as required by ASB NS-5.,"Configure private endpoints for the 'Microsoft.UsageBilling/accounts/dataExports' resource to ensure secure, private connectivity.",N/A,AI,Generic
HIGH,NS-22,Templates\LacpGeo.Template.json,120,"Cosmos DB account at line 120 does not use private endpoints. 'isVirtualNetworkFilterEnabled' is set to false and 'virtualNetworkRules' is empty, which does not restrict access to private networks.",Enable private endpoints for the Cosmos DB account by setting up a Private Endpoint resource and updating 'isVirtualNetworkFilterEnabled' to true. Add the appropriate 'virtualNetworkRules' to restrict access.,N/A,AI,Generic
HIGH,NS-5,Templates\LacpGlobal.Template.json,54,"CosmosDB account at line 54 does not use private endpoints, increasing risk of exposure to public networks.","Configure a private endpoint for the CosmosDB account to ensure secure, private connectivity as per ASB NS-5.",N/A,AI,Generic
HIGH,NS-17,Templates\LacpGlobal.Template.json,134,"Key Vault at line 134 does not specify any private endpoint configuration, exposing the vault to public network access.",Configure a private endpoint for the Key Vault to restrict access to trusted networks as per ASB NS-5.,N/A,AI,Generic
HIGH,NS-23,Templates\LacpRegion.Template.json,1007,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1007 does not use private endpoints and allows public network access, which is a network security weakness per NS-23: Use Private Endpoints.","Configure a private endpoint for the CosmosDB account and set 'publicNetworkAccess' to 'Disabled' to ensure secure, private connectivity.",N/A,AI,Generic
HIGH,NS-24,Templates\LacpStamp.Template.json,1002,"Microsoft.Storage/storageAccounts resources (line 1002 and similar) do not implement private endpoints. According to ASB NS-5, storage accounts should use private endpoints to restrict public network access.",Add a Microsoft.Network/privateEndpoints resource for each storage account and configure the storage account's network rules to deny public access.,N/A,AI,Generic
HIGH,NS-24,Templates\LacpStamp.Template.json,1082,"Microsoft.Storage/storageAccounts resources (line 1082 and similar) do not implement private endpoints. According to ASB NS-5, storage accounts should use private endpoints to restrict public network access.",Add a Microsoft.Network/privateEndpoints resource for each storage account and configure the storage account's network rules to deny public access.,N/A,AI,Generic
HIGH,NS-24,Templates\LacpStamp.Template.json,1132,"Microsoft.Storage/storageAccounts resources (line 1132 and similar) do not implement private endpoints. According to ASB NS-5, storage accounts should use private endpoints to restrict public network access.",Add a Microsoft.Network/privateEndpoints resource for each storage account and configure the storage account's network rules to deny public access.,N/A,AI,Generic
HIGH,NS-5,Templates\ReadAdxExhaust.Template.json,23,Microsoft.Kusto/clusters resource does not use private endpoints for secure access.,"Configure a private endpoint for the Microsoft.Kusto/clusters resource to ensure secure, private connectivity.",N/A,AI,Generic
HIGH,AM-1,Templates\RoleAssignment.Template.json,38,"Role assignment at line 38 grants 'Contributor' role to the 'Ev2BuildoutServicePrincipalId' principal. The Contributor role provides broad permissions, which may exceed least privilege requirements.","Review the permissions required by the 'Ev2BuildoutServicePrincipalId' principal and assign a custom role or a built-in role with only the necessary permissions instead of the Contributor role, in accordance with the principle of least privilege.",N/A,AI,Generic
MEDIUM,IM-8,Templates\LacpBillingExhaustExport.Template.json,61,Resource 'Microsoft.UsageBilling/accounts/dataExports' at line 61 does not specify the use of managed identities for secure resource-to-resource authentication as required by ASB IM-8.,Enable and assign a managed identity to the 'Microsoft.UsageBilling/accounts/dataExports' resource and use it for authentication to other Azure resources.,N/A,AI,Generic
MEDIUM,DP-7,Templates\ReadAdxExhaust.Template.json,23,Microsoft.Kusto/clusters resource does not specify use of customer-managed keys (CMK) for encryption.,Configure the 'encryption' property to use customer-managed keys (CMK) for enhanced control over encryption keys.,N/A,AI,Generic
MEDIUM,IM-8,Templates\ReadUsageAccount.Template.json,23,The resource 'Microsoft.UsageBilling/accounts' does not explicitly configure a managed identity. ASB IM-8 recommends using managed identities for secure resource-to-resource authentication.,Add the 'identity' property with 'type': 'SystemAssigned' or 'UserAssigned' to the resource definition to enable managed identity.,N/A,AI,Generic
