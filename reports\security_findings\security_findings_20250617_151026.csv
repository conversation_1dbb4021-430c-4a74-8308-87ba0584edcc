File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
app-config.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyValues' flows across template boundary,"Secure parameter 'keyValues' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'app_insights_key' flows across template boundary,"Secure parameter 'app_insights_key' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyvault_name' flows across template boundary,"Secure parameter 'keyvault_name' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
keyvault.bicep,NS-1,Network Security,Protect resources using network security groups,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,"Review template dependencies and ensure secure communication between templates

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'secrets' flows across template boundary,"Secure parameter 'secrets' and validate its usage across template boundaries

📚 References: [Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices)

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",[Active Directory Whatis](https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis) | [Overview](https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview) | [Identity Management Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices),Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.
Azure AD documentation: https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-whatis
Managed identities guide: https://docs.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview
Azure AD best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/identity-management-best-practices

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
storage-accounts.bicep,NS-1,Network Security,Protect resources using network security groups,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,"Review template dependencies and ensure secure communication between templates

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,DP-6,Data Protection,Secure Data with Customer-Managed Keys (CMK),HIGH,1.0,Parameter flow security risk: Sensitive parameters may be exposed through template dependencies,"Use Key Vault references for sensitive parameters and validate parameter flow security

📚 References: [Customer Managed Keys Overview](https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-overview) | [Customer Managed Keys Configure Key Vault](https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-configure-key-vault) | [Transparent Data Encryption Byok Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-byok-overview)

🔵 Azure Guidance: Use customer-managed keys for critical data.",[Customer Managed Keys Overview](https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-overview) | [Customer Managed Keys Configure Key Vault](https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-configure-key-vault) | [Transparent Data Encryption Byok Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-byok-overview),Use customer-managed keys for critical data.,"Configure Key Vault for key management. Rotate keys regularly.
Customer-managed keys: https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-overview
Key Vault CMK setup: https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-configure-key-vault
SQL CMK configuration: https://docs.microsoft.com/en-us/azure/azure-sql/database/transparent-data-encryption-byok-overview

This control is implemented through 3 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,parameter_flow,Validated
app-config.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,7.0,"The App Configuration resource 'Microsoft.AppConfiguration/configurationStores' does not restrict public network access or define access controls for its public endpoint. By default, App Configuration endpoints are public unless network rules or private endpoints are explicitly configured.","Restrict public network access by configuring the 'publicNetworkAccess' property to 'Disabled' or by defining network ACLs. Alternatively, use Private Endpoints to limit access to trusted networks only. See: https://docs.microsoft.com/en-us/azure/azure-app-configuration/concept-private-endpoint

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
app-config.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,7.0,"The App Configuration resource 'Microsoft.AppConfiguration/configurationStores' does not configure a Private Endpoint, leaving the service accessible over the public internet and increasing the attack surface.","Add a Private Endpoint resource for the App Configuration instance to ensure access is only possible from within your private network. See: https://docs.microsoft.com/en-us/azure/azure-app-configuration/concept-private-endpoint

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
autoscale-settings.bicep,NS-1,Network Security,Protect resources using network security groups,MEDIUM,10.0,The autoscaleSettings resource (Microsoft.Insights/autoscalesettings) does not specify any network security group (NSG) or network segmentation. Network segmentation is critical for defense in depth as per ASB NS-1.,"Ensure that the underlying resources (such as the App Service Plan or VMs referenced by server_farm_id) are associated with appropriate Network Security Groups (NSGs) to restrict network access. Review and apply NSGs at the subnet or NIC level as required.

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
autoscale-settings.bicep,NS-2,Network Security,Protect public endpoints,MEDIUM,10.0,The autoscaleSettings resource does not restrict public endpoints or specify access controls for public exposure. Protecting public endpoints is required by ASB NS-2.,"Restrict public access to the underlying resources (such as App Service Plan or Storage Account) by configuring access restrictions, using Private Endpoints, or limiting allowed IP ranges. Consider using Azure Front Door or Application Gateway for additional protection.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
autoscale-settings.bicep,NS-3,Network Security,Use Network Security Groups (NSGs),MEDIUM,10.0,No Network Security Groups (NSGs) are defined or referenced for the autoscaleSettings resource or its dependencies. NSGs provide network-level access control as required by ASB NS-3.,"Configure NSGs for the subnets or network interfaces associated with the resources referenced by server_farm_id and func_storage_account_id. Deny all inbound traffic by default and allow only necessary traffic.

📚 References: [Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules)

🔵 Azure Guidance: Implement NSGs with deny-by-default rules.",[Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules),Implement NSGs with deny-by-default rules.,"Configure NSGs to deny all inbound traffic by default. Allow only necessary traffic.
NSG rules documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works
Security rules best practices: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
autoscale-settings.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,10.0,The template does not configure Private Endpoints for the Storage Account referenced by func_storage_account_id. Using Private Endpoints reduces the attack surface as required by ASB NS-5.,"Configure a Private Endpoint for the Storage Account referenced by func_storage_account_id to ensure access is only available from within your virtual network. Update the template to include a Microsoft.Network/privateEndpoints resource targeting the Storage Account.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
event-grid.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,20.0,"The Event Grid system topic resource (Microsoft.EventGrid/systemTopics) does not specify the use of Private Endpoints, which is required to reduce the attack surface for service resources.","Configure Private Endpoints for the Event Grid system topic resource to ensure private access. Refer to: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
event-grid.bicep,NS-6,Network Security,Use Virtual Network Service Endpoints,MEDIUM,20.0,"The Event Grid system topic resource (Microsoft.EventGrid/systemTopics) does not explicitly configure Virtual Network Service Endpoints, which are recommended to improve security for Azure resources.","Enable Virtual Network Service Endpoints for the Event Grid system topic resource. Refer to: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview

📚 References: [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Storage Network Security](https://docs.microsoft.com/en-us/azure/storage/common/storage-network-security#grant-access-from-a-virtual-network) | [Vnet Service Endpoint Rule Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/vnet-service-endpoint-rule-overview)

🔵 Azure Guidance: Enable service endpoints to secure Azure service traffic.",[Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Storage Network Security](https://docs.microsoft.com/en-us/azure/storage/common/storage-network-security#grant-access-from-a-virtual-network) | [Vnet Service Endpoint Rule Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/vnet-service-endpoint-rule-overview),Enable service endpoints to secure Azure service traffic.,"Configure service endpoints for Storage SQL CosmosDB and other services.
Service endpoints overview: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Storage service endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-network-security#grant-access-from-a-virtual-network
SQL service endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/vnet-service-endpoint-rule-overview

This control is implemented through 2 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
function-settings.bicep,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,32.0,"The parameter 'ado_access_client_id' is assigned a hardcoded client ID value directly in the template. Storing sensitive information such as client IDs or secrets in code violates DP-3, which requires secrets to be managed securely (e.g., via Key Vault references).","Remove the hardcoded value from the 'ado_access_client_id' parameter and retrieve it securely from Azure Key Vault using a Key Vault reference. Update the template to reference the secret instead of embedding it.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",[Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references),Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.
Key Vault overview: https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Secret management: https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets
App Service Key Vault integration: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
function.bicep,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,41.0,"The 'clientSecretSettingName' property in the 'funcAuthSettings' resource (line 41) is set to a static string 'OVERRIDE_USE_MI_FIC_ASSERTION_CLIENTID', which may indicate a secret or credential is being referenced directly in code rather than using a secure Key Vault reference.","Replace the static 'clientSecretSettingName' value with a Key Vault reference or use managed identity for secret retrieval. Ensure no secrets are stored in code. See: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",[Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references),Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.
Key Vault overview: https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Secret management: https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets
App Service Key Vault integration: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
function.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,36.0,"The App Service resource 'function' (line 36) does not restrict public access or configure access restrictions, potentially exposing the function app to the public internet.","Configure access restrictions for the App Service to allow only required IPs or subnets. Consider using Private Endpoints or Application Gateway to limit public exposure. See: https://docs.microsoft.com/en-us/azure/app-service/app-service-ip-restrictions

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
function.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,36.0,"The App Service resource 'function' (line 36) does not use Private Endpoints, increasing the attack surface by allowing public network access.","Configure a Private Endpoint for the App Service to restrict access to private networks only. See: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
hub-network.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,8.0,The subnet 'hub-subnet' defined at line 8 does not have a Network Security Group (NSG) associated. This violates the requirement to protect resources using NSGs for network segmentation and defense in depth.,"Associate a Network Security Group (NSG) with the 'hub-subnet' by adding the 'networkSecurityGroup' property referencing an NSG resource. Ensure the NSG has rules to allow only required traffic.

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
hub-network.bicep,NS-3,Network Security,Use Network Security Groups (NSGs),CRITICAL,8.0,"The subnet 'hub-subnet' at line 8 is missing an explicit NSG association, which is required to provide network-level access control and deny all inbound traffic by default.","Attach a Network Security Group (NSG) to the 'hub-subnet' and configure security rules to deny all inbound traffic except for explicitly allowed traffic.

📚 References: [Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules)

🔵 Azure Guidance: Implement NSGs with deny-by-default rules.",[Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules),Implement NSGs with deny-by-default rules.,"Configure NSGs to deny all inbound traffic by default. Allow only necessary traffic.
NSG rules documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works
Security rules best practices: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
hub-network.bicep,NS-4,Network Security,Use Azure Firewall or third-party firewall,HIGH,2.0,"No Azure Firewall or third-party firewall is configured in the template (line 2 onwards), which is required for advanced network protection.","Deploy an Azure Firewall or a supported third-party firewall in the virtual network and configure appropriate firewall policies for traffic inspection and threat intelligence.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/firewall/overview) | [docs.microsoft.com/en-us/azure/firewall/overview](https://docs.microsoft.com/en-us/azure/firewall/policy-overview) | [Threat Intel](https://docs.microsoft.com/en-us/azure/firewall/threat-intel)

🔵 Azure Guidance: Deploy Azure Firewall or partner solution for enhanced security.",[Overview](https://docs.microsoft.com/en-us/azure/firewall/overview) | [docs.microsoft.com/en-us/azure/firewall/overview](https://docs.microsoft.com/en-us/azure/firewall/policy-overview) | [Threat Intel](https://docs.microsoft.com/en-us/azure/firewall/threat-intel),Deploy Azure Firewall or partner solution for enhanced security.,"Configure Azure Firewall policies. Enable threat intelligence. Monitor traffic.
Azure Firewall documentation: https://docs.microsoft.com/en-us/azure/firewall/overview
Firewall policies guide: https://docs.microsoft.com/en-us/azure/firewall/policy-overview
Threat intelligence setup: https://docs.microsoft.com/en-us/azure/firewall/threat-intel",ai_analysis,,Validated
hub-network.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,8.0,"The subnet 'hub-subnet' at line 8 uses service endpoints for 'Microsoft.Storage.Global' and 'Microsoft.Keyvault' but does not use Private Endpoints, which are required to reduce the attack surface for PaaS services.","Replace or supplement service endpoints with Private Endpoints for Azure Storage and Key Vault to ensure private access to these services from within the virtual network.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
instance-config.bicep,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,32.0,"The configuration at line 32 hardcodes 'monitoringGCSAuthId' with a value resembling a Key Vault or sensitive identifier directly in the template, which may expose sensitive information. This violates DP-3: Manage sensitive information disclosure.","Remove hardcoded secrets or sensitive identifiers from the template. Reference secrets using Azure Key Vault and Key Vault references instead. Ensure all sensitive values are retrieved securely at deployment/runtime.

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",[Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references),Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.
Key Vault overview: https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Secret management: https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets
App Service Key Vault integration: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
keyvault.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,19.0,"Key Vault 'networkAcls.defaultAction' is set to 'Allow', which exposes the Key Vault to the public internet. This violates the requirement to restrict public endpoints.","Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only required IPs or virtual networks. Example: change 'defaultAction' to 'Deny' in the Key Vault resource configuration.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
scaleset-networks.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,38.0,The subnet 'scaleset' defined at line 38 does not have a Network Security Group (NSG) associated. NSGs are required to segment and protect network resources as per ASB NS-1.,"Associate a Network Security Group (NSG) with the 'scaleset' subnet. Define an NSG resource and link it to the subnet using the 'networkSecurityGroup' property.

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
scaleset-networks.bicep,NS-3,Network Security,Use Network Security Groups (NSGs),CRITICAL,38.0,"The subnet 'scaleset' at line 38 lacks an NSG, which is required to enforce network-level access controls and deny all inbound traffic by default as per ASB NS-3.","Create and associate a Network Security Group (NSG) with the 'scaleset' subnet. Configure the NSG to deny all inbound traffic by default and allow only necessary traffic.

📚 References: [Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules)

🔵 Azure Guidance: Implement NSGs with deny-by-default rules.",[Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules),Implement NSGs with deny-by-default rules.,"Configure NSGs to deny all inbound traffic by default. Allow only necessary traffic.
NSG rules documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works
Security rules best practices: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
scaleset-networks.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,38.0,"The subnet 'scaleset' at line 38 is configured with 'defaultOutboundAccess: true', which allows unrestricted outbound internet access. This exposes public endpoints without restriction, violating ASB NS-2.","Set 'defaultOutboundAccess' to false for the 'scaleset' subnet and restrict outbound access using NSG rules to only required destinations.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
server-farms.bicep,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,109.0,"The 'serverFarms_AntMDS_CERTIFICATE_PASSWORD_GENEVACERT' resource sets 'settingValue' to an empty string, which may indicate a placeholder for a certificate password. Storing secrets or sensitive values directly in the template, even as empty strings, violates the requirement to never store secrets in code. All secrets must be referenced from Azure Key Vault.","Remove the 'settingValue' property or replace it with a Key Vault reference. Ensure that all secrets, such as certificate passwords, are stored and accessed securely using Azure Key Vault references. See: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

📚 References: [Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",[Overview](https://docs.microsoft.com/en-us/azure/key-vault/general/overview) | [About Secrets](https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets) | [App Service Key Vault References](https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references),Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.
Key Vault overview: https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Secret management: https://docs.microsoft.com/en-us/azure/key-vault/secrets/about-secrets
App Service Key Vault integration: https://docs.microsoft.com/en-us/azure/app-service/app-service-key-vault-references

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
signalR.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,4.0,"The Microsoft.SignalRService/signalR resource does not restrict public network access or define allowed IPs. By default, SignalR Service is accessible over the public internet, which violates the requirement to restrict public endpoints.","Restrict public network access to the SignalR resource by configuring the 'publicNetworkAccess' property to 'Disabled' or specifying allowed IP ranges using the 'networkACLs' property. Consider using Private Link or service endpoints to limit exposure.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
signalR.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,4.0,The Microsoft.SignalRService/signalR resource does not configure a Private Endpoint. Private Endpoints are required to reduce the attack surface and prevent public exposure.,"Configure a Private Endpoint for the SignalR resource by adding a Microsoft.Network/privateEndpoints resource and associating it with the SignalR instance. Update client applications to use the private endpoint connection string.

📚 References: [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview)

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",[Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview) | [Storage Private Endpoints](https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints) | [Private Endpoint Overview](https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview),Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.
Private endpoints overview: https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Storage private endpoints: https://docs.microsoft.com/en-us/azure/storage/common/storage-private-endpoints
SQL private endpoints: https://docs.microsoft.com/en-us/azure/azure-sql/database/private-endpoint-overview

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,19.0,"The 'networkAcls' configuration for 'storageAccountFunc' sets 'defaultAction' to 'Allow', which permits public network access to the storage account. This violates ASB NS-2, which requires strict access control for public endpoints.","Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only required IPs or subnets. Consider using Private Endpoints or Service Endpoints to restrict public access.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,49.0,"The 'fuzzStorageProperties' object used for 'storageAccountFuzz' and 'storageAccountsCorpus' sets 'networkAcls.defaultAction' to 'Allow', which permits public network access to the storage accounts. This violates ASB NS-2, which requires strict access control for public endpoints.","Set 'networkAcls.defaultAction' to 'Deny' in 'fuzzStorageProperties' and explicitly allow only required IPs or subnets. Consider using Private Endpoints or Service Endpoints to restrict public access.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 33,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 7,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-17T15:10:26.471246,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
