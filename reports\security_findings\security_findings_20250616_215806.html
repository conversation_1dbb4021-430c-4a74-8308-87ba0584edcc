<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Assessment Report - IaC Guardian</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            /* Modern Color Palette */
            --primary-blue: #1e40af;
            --primary-blue-light: #3b82f6;
            --secondary-blue: #0ea5e9;
            --accent-purple: #8b5cf6;
            --success-green: #10b981;
            --warning-amber: #f59e0b;
            --danger-red: #ef4444;
            --info-cyan: #06b6d4;

            /* Neutral Colors */
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;

            /* Semantic Colors */
            --critical-bg: #fef2f2;
            --critical-border: #fecaca;
            --critical-text: #dc2626;
            --high-bg: #fffbeb;
            --high-border: #fed7aa;
            --high-text: #ea580c;
            --medium-bg: #fefce8;
            --medium-border: #fde68a;
            --medium-text: #ca8a04;
            --low-bg: #f0f9ff;
            --low-border: #bae6fd;
            --low-text: #0284c7;

            /* Layout */
            --max-width: 1400px;
            --border-radius: 12px;
            --border-radius-sm: 8px;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--gray-700);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-size: 14px;
        }

        .main-container {
            max-width: var(--max-width);
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        /* Header Section */
        .report-header {
            background: white;
            border-radius: var(--border-radius);
            padding: 3rem 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-lg);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .report-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue), var(--accent-purple));
        }

        .report-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
        }

        .report-subtitle {
            font-size: 1.125rem;
            color: var(--gray-600);
            font-weight: 400;
            margin-bottom: 1rem;
        }

        .report-meta {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 1.5rem;
            font-size: 0.875rem;
            color: var(--gray-500);
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* Controls Section */
        .controls-section {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }

        .controls-grid {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 1.5rem;
            align-items: center;
        }

        .search-container {
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius-sm);
            font-size: 0.875rem;
            transition: all 0.2s ease;
            background: var(--gray-50);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
        }

        .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
        }

        .filter-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 0.5rem 1rem;
            border: 2px solid transparent;
            border-radius: 2rem;
            background: var(--gray-100);
            color: var(--gray-600);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            white-space: nowrap;
        }

        .filter-btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .filter-btn.active {
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .filter-btn.all.active { background: var(--primary-blue); }
        .filter-btn.critical.active { background: var(--danger-red); }
        .filter-btn.high.active { background: var(--warning-amber); }
        .filter-btn.medium.active { background: var(--medium-text); }
        .filter-btn.low.active { background: var(--info-cyan); }

        /* Multi-select filter enhancements */
        .filter-btn.active {
            position: relative;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .multi-select-info {
            text-align: center;
            margin-top: 0.5rem;
        }

        .filter-summary {
            text-align: center;
            font-weight: 500;
        }

        /* Animation for filter changes */
        .severity-group {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        .severity-group[style*="display: none"] {
            opacity: 0;
            transform: translateY(-10px);
        }

        .finding-item {
            transition: opacity 0.2s ease;
        }

        .finding-item[style*="display: none"] {
            opacity: 0;
        }

        /* Summary Section */
        .summary-section {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }

        .summary-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-blue);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--gray-600);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .severity-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .severity-badge {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .severity-badge:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .severity-badge.critical {
            background: var(--critical-bg);
            border: 1px solid var(--critical-border);
            color: var(--critical-text);
        }

        .severity-badge.high {
            background: var(--high-bg);
            border: 1px solid var(--high-border);
            color: var(--high-text);
        }

        .severity-badge.medium {
            background: var(--medium-bg);
            border: 1px solid var(--medium-border);
            color: var(--medium-text);
        }

        .severity-badge.low {
            background: var(--low-bg);
            border: 1px solid var(--low-border);
            color: var(--low-text);
        }

        .severity-count {
            font-size: 1.25rem;
            font-weight: 700;
        }

        .severity-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* Findings Section */
        .findings-container {
            margin-bottom: 2rem;
        }

        .severity-group {
            background: white;
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
            box-shadow: var(--shadow);
            overflow: hidden;
            border: 1px solid var(--gray-200);
        }

        .severity-group:last-child {
            margin-bottom: 0;
        }

        /* Domain Section Styles */
        .domain-section {
            margin-bottom: 2rem;
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius);
            background: white;
            box-shadow: var(--shadow);
        }

        .domain-header {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: white;
            padding: 1.5rem 2rem;
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        .domain-header i {
            font-size: 1.5rem;
        }

        .domain-section .severity-group {
            margin: 0;
            border-radius: 0;
            border: none;
            border-bottom: 1px solid var(--gray-200);
            box-shadow: none;
        }

        .domain-section .severity-group:last-child {
            border-bottom: none;
            border-radius: 0 0 var(--border-radius) var(--border-radius);
        }

        .severity-header {
            padding: 1.25rem 1.5rem;
            font-weight: 600;
            font-size: 1.125rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 1px solid var(--gray-200);
        }

        .severity-header:hover {
            background: var(--gray-50);
        }

        .severity-header-left {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .severity-icon {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.875rem;
        }

        .severity-header.critical {
            background: var(--critical-bg);
            color: var(--critical-text);
            border-left: 4px solid var(--critical-text);
        }

        .severity-header.critical .severity-icon {
            background: var(--critical-text);
        }

        .severity-header.high {
            background: var(--high-bg);
            color: var(--high-text);
            border-left: 4px solid var(--high-text);
        }

        .severity-header.high .severity-icon {
            background: var(--high-text);
        }

        .severity-header.medium {
            background: var(--medium-bg);
            color: var(--medium-text);
            border-left: 4px solid var(--medium-text);
        }

        .severity-header.medium .severity-icon {
            background: var(--medium-text);
        }

        .severity-header.low {
            background: var(--low-bg);
            color: var(--low-text);
            border-left: 4px solid var(--low-text);
        }

        .severity-header.low .severity-icon {
            background: var(--low-text);
        }

        .severity-title {
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .severity-count {
            background: rgba(255, 255, 255, 0.9);
            color: inherit;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 700;
            margin-left: 0.5rem;
        }

        .toggle-icon {
            transition: transform 0.2s ease;
            color: var(--gray-500);
        }

        .severity-header.collapsed .toggle-icon {
            transform: rotate(-90deg);
        }

        .findings-list {
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .findings-list.collapsed {
            max-height: 0;
        }

        .finding-item {
            border-bottom: 1px solid var(--gray-200);
            padding: 1.5rem;
            transition: all 0.2s ease;
            background: white;
        }

        .finding-item:last-child {
            border-bottom: none;
        }

        .finding-item:hover {
            background: var(--gray-50);
        }

        .finding-header {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .finding-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
            flex-shrink: 0;
        }

        .finding-icon.critical { background: var(--danger-red); }
        .finding-icon.high { background: var(--warning-amber); }
        .finding-icon.medium { background: var(--medium-text); }
        .finding-icon.low { background: var(--info-cyan); }

        .finding-content {
            flex: 1;
            min-width: 0;
        }

        .finding-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            flex-wrap: wrap;
        }

        .control-id {
            background: var(--primary-blue);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .finding-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1rem;
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.375rem;
        }

        .meta-icon {
            color: var(--gray-400);
            width: 1rem;
        }

        .finding-description {
            color: var(--gray-700);
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .remediation-section {
            background: var(--success-green);
            background: linear-gradient(135deg, var(--success-green) 0%, #059669 100%);
            color: white;
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            margin-top: 1rem;
        }

        .remediation-title {
            font-weight: 600;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .remediation-content {
            font-size: 0.875rem;
            line-height: 1.5;
            opacity: 0.95;
        }

        .code-snippet {
            background: var(--gray-900);
            color: var(--gray-100);
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 0.8125rem;
            line-height: 1.5;
            white-space: pre-wrap;
            margin: 1rem 0;
            overflow-x: auto;
            border: 1px solid var(--gray-700);
        }

        .no-findings {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--gray-500);
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }

        .no-findings-icon {
            font-size: 3rem;
            color: var(--gray-300);
            margin-bottom: 1rem;
        }

        .no-findings h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
        }

        /* Footer */
        .report-footer {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-top: 2rem;
            box-shadow: var(--shadow);
            text-align: center;
        }

        .export-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }

        .export-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius-sm);
            background: var(--primary-blue);
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.875rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .export-btn:hover {
            background: var(--primary-blue-light);
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .footer-info {
            color: var(--gray-600);
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .footer-info strong {
            color: var(--gray-900);
        }

        /* Responsive Design */

        /* Large Desktop (1200px+) */
        @media (min-width: 1200px) {
            .main-container {
                padding: 3rem 2rem;
            }

            .stats-grid {
                grid-template-columns: repeat(4, 1fr);
            }

            .severity-overview {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        /* Desktop (992px - 1199px) */
        @media (min-width: 992px) and (max-width: 1199px) {
            .main-container {
                padding: 2rem 1.5rem;
            }

            .report-title {
                font-size: 2.25rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .severity-overview {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Tablet (768px - 991px) */
        @media (min-width: 768px) and (max-width: 991px) {
            .main-container {
                padding: 1.5rem 1rem;
            }

            .report-header {
                padding: 2rem 1.5rem;
            }

            .report-title {
                font-size: 2rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .report-meta {
                flex-direction: column;
                gap: 0.5rem;
            }

            .controls-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .filter-buttons {
                justify-content: center;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .severity-overview {
                grid-template-columns: repeat(2, 1fr);
            }

            .finding-title {
                font-size: 1rem;
            }

            .finding-meta {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        /* Mobile Large (576px - 767px) */
        @media (min-width: 576px) and (max-width: 767px) {
            .main-container {
                padding: 1rem 0.75rem;
            }

            .report-header {
                padding: 1.5rem 1rem;
            }

            .report-title {
                font-size: 1.75rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .report-subtitle {
                font-size: 1rem;
            }

            .report-meta {
                flex-direction: column;
                gap: 0.25rem;
                font-size: 0.8125rem;
            }

            .controls-section {
                padding: 1rem;
            }

            .controls-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .filter-buttons {
                justify-content: center;
                gap: 0.375rem;
            }

            .filter-btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.6875rem;
            }

            .summary-section {
                padding: 1.5rem 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .severity-overview {
                grid-template-columns: 1fr;
            }

            .finding-item {
                padding: 1rem;
            }

            .finding-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.75rem;
            }

            .finding-title {
                font-size: 0.9375rem;
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .export-actions {
                flex-direction: column;
                align-items: center;
            }
        }

        /* Mobile Small (up to 575px) */
        @media (max-width: 575px) {
            .main-container {
                padding: 0.75rem 0.5rem;
            }

            .report-header {
                padding: 1.25rem 0.75rem;
                margin-bottom: 1rem;
            }

            .report-title {
                font-size: 1.5rem;
                flex-direction: column;
                gap: 0.375rem;
            }

            .report-subtitle {
                font-size: 0.9375rem;
            }

            .report-meta {
                flex-direction: column;
                gap: 0.25rem;
                font-size: 0.75rem;
            }

            .controls-section {
                padding: 0.75rem;
            }

            .search-input {
                font-size: 1rem; /* Prevents zoom on iOS */
                padding: 0.75rem 1rem 0.75rem 2.25rem;
            }

            .filter-buttons {
                gap: 0.25rem;
            }

            .filter-btn {
                padding: 0.375rem 0.625rem;
                font-size: 0.625rem;
                min-width: auto;
            }

            .summary-section {
                padding: 1.25rem 0.75rem;
            }

            .summary-title {
                font-size: 1.25rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            .stat-card {
                padding: 1rem;
            }

            .stat-number {
                font-size: 2rem;
            }

            .severity-overview {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .severity-header {
                padding: 1rem;
                font-size: 1rem;
            }

            .finding-item {
                padding: 0.75rem;
            }

            .finding-icon {
                width: 2rem;
                height: 2rem;
                font-size: 0.875rem;
            }

            .finding-title {
                font-size: 0.875rem;
            }

            .control-id {
                font-size: 0.6875rem;
                padding: 0.1875rem 0.5rem;
            }

            .finding-meta {
                font-size: 0.8125rem;
            }

            .finding-description {
                font-size: 0.875rem;
            }

            .code-snippet {
                font-size: 0.75rem;
                padding: 0.75rem;
            }

            .export-actions {
                flex-direction: column;
                gap: 0.5rem;
            }

            .export-btn {
                width: 100%;
                justify-content: center;
            }
        }

        /* Print Styles */
        @media print {
            body {
                background: white !important;
            }

            .main-container {
                max-width: none;
                padding: 0;
            }

            .report-header,
            .controls-section,
            .summary-section,
            .severity-group,
            .report-footer {
                box-shadow: none !important;
                break-inside: avoid;
            }

            .controls-section,
            .export-actions {
                display: none !important;
            }

            .findings-list {
                max-height: none !important;
            }
        }
    </style>

    <script>
        // Modern JavaScript for enhanced interactivity with multi-select filtering
        let searchTimeout;
        let allFindings = [];
        let activeFilters = new Set(['all']); // Support multiple active filters

        document.addEventListener('DOMContentLoaded', function() {
            initializeReport();
            setupEventListeners();
            loadFindings();
        });

        function initializeReport() {
            // Initialize filter buttons with multi-select support
            const filterButtons = document.querySelectorAll('.filter-btn');
            filterButtons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    // Allow Ctrl/Cmd + click for multi-select
                    const isMultiSelect = e.ctrlKey || e.metaKey;
                    toggleFilter(this.dataset.severity, isMultiSelect);
                });
            });

            // Initialize search
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.addEventListener('input', debounceSearch);
            }

            // Initialize collapsible sections
            const severityHeaders = document.querySelectorAll('.severity-header');
            severityHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    toggleSeverityGroup(this);
                });
            });

            // Add instructions for multi-select
            addMultiSelectInstructions();
        }

        function addMultiSelectInstructions() {
            const controlsSection = document.querySelector('.controls-section');
            if (controlsSection) {
                const instructions = document.createElement('div');
                instructions.className = 'multi-select-info';
                instructions.innerHTML = `
                    <small style="color: var(--gray-500); font-size: 0.75rem; margin-top: 0.5rem; display: block;">
                        <i class="fas fa-info-circle"></i>
                        Tip: Hold Ctrl/Cmd and click to select multiple severity levels
                    </small>
                `;
                controlsSection.appendChild(instructions);
            }
        }

        function setupEventListeners() {
            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    clearSearch();
                    resetFilters();
                }
                if (e.ctrlKey && e.key === 'f') {
                    e.preventDefault();
                    focusSearch();
                }
            });
        }

        function debounceSearch() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch();
            }, 300);
        }

        function performSearch() {
            const searchTerm = document.querySelector('.search-input').value.toLowerCase();
            applyFilters(searchTerm);
        }

        function toggleFilter(severity, isMultiSelect = false) {
            if (severity === 'all') {
                // If "All" is clicked, reset to show all
                activeFilters.clear();
                activeFilters.add('all');
            } else {
                if (isMultiSelect) {
                    // Multi-select mode
                    if (activeFilters.has('all')) {
                        activeFilters.clear();
                    }

                    if (activeFilters.has(severity)) {
                        activeFilters.delete(severity);
                    } else {
                        activeFilters.add(severity);
                    }

                    // If no filters selected, default to "all"
                    if (activeFilters.size === 0) {
                        activeFilters.add('all');
                    }
                } else {
                    // Single select mode (default behavior)
                    activeFilters.clear();
                    activeFilters.add(severity);
                }
            }

            updateFilterButtons();
            applyFilters();
            updateUrlHash();
        }

        function updateFilterButtons() {
            const filterButtons = document.querySelectorAll('.filter-btn');
            filterButtons.forEach(btn => {
                const severity = btn.dataset.severity;
                const isActive = activeFilters.has(severity);
                btn.classList.toggle('active', isActive);

                // Add visual indicator for multi-select
                if (activeFilters.size > 1 && !activeFilters.has('all')) {
                    btn.style.position = 'relative';
                    if (isActive && !btn.querySelector('.multi-indicator')) {
                        const indicator = document.createElement('span');
                        indicator.className = 'multi-indicator';
                        indicator.innerHTML = '✓';
                        indicator.style.cssText = `
                            position: absolute;
                            top: -2px;
                            right: -2px;
                            background: var(--success-green);
                            color: white;
                            border-radius: 50%;
                            width: 16px;
                            height: 16px;
                            font-size: 10px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-weight: bold;
                        `;
                        btn.appendChild(indicator);
                    }
                } else {
                    // Remove multi-select indicators
                    const indicator = btn.querySelector('.multi-indicator');
                    if (indicator) {
                        indicator.remove();
                    }
                }
            });
        }

        function applyFilters(searchTerm = '') {
            if (!searchTerm) {
                searchTerm = document.querySelector('.search-input').value.toLowerCase();
            }

            const severityGroups = document.querySelectorAll('.severity-group');
            let totalVisibleCount = 0;

            severityGroups.forEach(group => {
                const groupSeverity = group.dataset.severity;
                const findings = group.querySelectorAll('.finding-item');
                let groupVisibleCount = 0;

                // Check if this severity group should be visible
                const severityMatches = activeFilters.has('all') || activeFilters.has(groupSeverity);

                findings.forEach(finding => {
                    const text = finding.textContent.toLowerCase();
                    const searchMatches = searchTerm === '' || text.includes(searchTerm);
                    const isVisible = severityMatches && searchMatches;

                    finding.style.display = isVisible ? 'block' : 'none';
                    if (isVisible) {
                        groupVisibleCount++;
                        totalVisibleCount++;
                    }
                });

                // Show/hide the entire severity group
                group.style.display = groupVisibleCount > 0 ? 'block' : 'none';
            });

            updateNoResultsMessage(totalVisibleCount === 0);
            updateFilterSummary();
        }

        function updateFilterSummary() {
            // Update or create filter summary
            let summary = document.querySelector('.filter-summary');
            if (!summary) {
                summary = document.createElement('div');
                summary.className = 'filter-summary';
                summary.style.cssText = `
                    margin-top: 0.5rem;
                    padding: 0.5rem;
                    background: var(--gray-100);
                    border-radius: var(--border-radius-sm);
                    font-size: 0.8125rem;
                    color: var(--gray-600);
                `;
                document.querySelector('.controls-section').appendChild(summary);
            }

            if (activeFilters.has('all')) {
                summary.textContent = 'Showing all severity levels';
            } else {
                const filterList = Array.from(activeFilters).map(f => f.charAt(0).toUpperCase() + f.slice(1)).join(', ');
                summary.textContent = `Showing: ${filterList} severity levels`;
            }
        }

        function resetFilters() {
            activeFilters.clear();
            activeFilters.add('all');
            updateFilterButtons();
            applyFilters();
            updateUrlHash();
        }

        function updateUrlHash() {
            const params = new URLSearchParams();
            if (!activeFilters.has('all')) {
                params.set('filters', Array.from(activeFilters).join(','));
            }
            const searchTerm = document.querySelector('.search-input').value;
            if (searchTerm) {
                params.set('search', searchTerm);
            }

            const hash = params.toString();
            if (hash) {
                window.location.hash = hash;
            } else {
                window.history.replaceState(null, null, window.location.pathname);
            }
        }

        function loadFromUrlHash() {
            const hash = window.location.hash.substring(1);
            if (hash) {
                const params = new URLSearchParams(hash);
                const filters = params.get('filters');
                const search = params.get('search');

                if (filters) {
                    activeFilters.clear();
                    filters.split(',').forEach(filter => activeFilters.add(filter.trim()));
                    updateFilterButtons();
                }

                if (search) {
                    document.querySelector('.search-input').value = search;
                }

                applyFilters();
            }
        }

        function toggleSeverityGroup(header) {
            const group = header.parentElement;
            const findingsList = group.querySelector('.findings-list');
            const isCollapsed = header.classList.contains('collapsed');

            if (isCollapsed) {
                header.classList.remove('collapsed');
                findingsList.classList.remove('collapsed');
                findingsList.style.maxHeight = findingsList.scrollHeight + 'px';
            } else {
                header.classList.add('collapsed');
                findingsList.classList.add('collapsed');
                findingsList.style.maxHeight = '0';
            }
        }

        function clearSearch() {
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.value = '';
                applyFilters();
            }
        }

        function focusSearch() {
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }

        function updateNoResultsMessage(show) {
            const noResults = document.querySelector('.no-findings');
            if (noResults) {
                noResults.style.display = show ? 'block' : 'none';
                if (show) {
                    // Update message based on active filters
                    const message = noResults.querySelector('p');
                    if (activeFilters.has('all')) {
                        message.textContent = 'Try adjusting your search terms';
                    } else {
                        const filterList = Array.from(activeFilters).join(', ');
                        message.textContent = `No ${filterList} severity findings match your search. Try different filters or search terms.`;
                    }
                }
            }
        }

        function exportToJson() {
            // Get currently visible findings for export
            const visibleFindings = [];
            document.querySelectorAll('.finding-item').forEach(finding => {
                if (finding.style.display !== 'none') {
                    const severityGroup = finding.closest('.severity-group');
                    const severity = severityGroup ? severityGroup.dataset.severity.toUpperCase() : 'UNKNOWN';
                    const controlId = finding.querySelector('.control-id')?.textContent || 'UNKNOWN';
                    const description = finding.querySelector('.finding-description')?.textContent || '';
                    const remediation = finding.querySelector('.remediation-content')?.textContent || '';
                    const filePath = finding.querySelector('.meta-item:first-child span')?.textContent || '';
                    const lineText = finding.querySelector('.meta-item:last-child span')?.textContent || '';
                    const line = lineText.replace('Line ', '') || '0';

                    visibleFindings.push({
                        control_id: controlId,
                        severity: severity,
                        file_path: filePath,
                        line: parseInt(line) || 0,
                        description: description.trim(),
                        remediation: remediation.trim()
                    });
                }
            });

            const data = {
                timestamp: new Date().toISOString(),
                filters_applied: Array.from(activeFilters),
                total_findings: visibleFindings.length,
                findings: visibleFindings,
                summary: {
                    critical: visibleFindings.filter(f => f.severity === 'CRITICAL').length,
                    high: visibleFindings.filter(f => f.severity === 'HIGH').length,
                    medium: visibleFindings.filter(f => f.severity === 'MEDIUM').length,
                    low: visibleFindings.filter(f => f.severity === 'LOW').length
                }
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], {
                type: 'application/json'
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `security-findings-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function loadFindings() {
            // Initialize findings data - this would be populated with actual findings
            allFindings = [];
        }

        // Initialize from URL hash on page load
        window.addEventListener('load', function() {
            loadFromUrlHash();
        });

        // Handle browser back/forward
        window.addEventListener('hashchange', function() {
            loadFromUrlHash();
        });
    </script>
</head>

<body>
    <div class="main-container">
        <!-- Header Section -->
        <header class="report-header">
            <h1 class="report-title">
                <i class="fas fa-shield-alt"></i>
                Security Assessment Report
            </h1>
            <p class="report-subtitle">Infrastructure as Code Security Analysis</p>
            <div class="report-meta">
                <div class="meta-item">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Generated: June 16, 2025 at 09:58 PM</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-robot"></i>
                    <span>IaC Guardian</span>
                </div>
            </div>
        </header>

        <!-- Controls Section -->
        <section class="controls-section">
            <div class="controls-grid">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search findings..." aria-label="Search findings">
                    <i class="fas fa-search search-icon"></i>
                </div>
                <div class="filter-buttons">
                    <button class="filter-btn all active" data-severity="all">All</button>
                    <button class="filter-btn critical" data-severity="critical">Critical</button>
                    <button class="filter-btn high" data-severity="high">High</button>
                    <button class="filter-btn medium" data-severity="medium">Medium</button>
                    <button class="filter-btn low" data-severity="low">Low</button>
                </div>
            </div>
        </section>

        
        <!-- Summary Section -->
        <section class="summary-section">
            <h2 class="summary-title">
                <i class="fas fa-chart-bar"></i>
                Executive Summary
            </h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">57</div>
                    <div class="stat-label">Total Findings</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">13</div>
                    <div class="stat-label">Files Affected</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">52</div>
                    <div class="stat-label">High Priority Issues</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">18</div>
                    <div class="stat-label">Security Controls</div>
                </div>
            </div>
            <div class="severity-overview">
                <div class="severity-badge critical">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        Critical
                    </div>
                    <div class="severity-count">38</div>
                </div>
                <div class="severity-badge high">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        High
                    </div>
                    <div class="severity-count">14</div>
                </div>
                <div class="severity-badge medium">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        Medium
                    </div>
                    <div class="severity-count">5</div>
                </div>
            </div>
        </section>

        <div class="findings-container">
            
            <div class="domain-section">
                <h3 class="domain-header">
                    <i class="fas fa-shield-alt"></i>
                    Identity Management (2 findings)
                </h3>
                <section class="severity-group" data-severity="medium" data-domain="identity-management">
                    <header class="severity-header medium">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">Medium Severity</div>
                            <div class="severity-count">2</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">IM-8</span>
                                <span class="finding-description">The resource 'Microsoft.UsageBilling/accounts/dataExports' at line 54 does not specify the use of managed identities for secure resource-to-resource authentication as recommended by Azure Security Benchmark IM-8.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpBillingExhaustExport.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 54</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Enable and configure a managed identity for the data export resource and use it for authentication to downstream services such as Azure Data Explorer.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">IM-8</span>
                                <span class="finding-description">The resource 'Microsoft.UsageBilling/accounts' does not explicitly configure a managed identity. ASB IM-8 recommends using managed identities for secure resource-to-resource authentication.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\ReadUsageAccount.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 23</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Add the 'identity' property with 'type': 'SystemAssigned' or 'UserAssigned' to the resource definition to enable managed identity.</div>
                            </div>
                        </div>
                    </article>
                    </div>
                </section>
            </div>
            <div class="domain-section">
                <h3 class="domain-header">
                    <i class="fas fa-shield-alt"></i>
                    Network Security (28 findings)
                </h3>
                <section class="severity-group" data-severity="critical" data-domain="network-security">
                    <header class="severity-header critical">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">Critical Severity</div>
                            <div class="severity-count">21</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-1</span>
                                <span class="finding-description">Storage account resource at line 38 does not restrict network access using network security groups (NSGs) or Azure Firewall. Public network access is not explicitly denied.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\IngestionStorageAccount.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 38</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Restrict network access to the storage account by setting 'networkAcls.defaultAction' to 'Deny' and specifying allowed subnets or private endpoints.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-1</span>
                                <span class="finding-description">Storage account resource at line 56 does not restrict network access using network security groups (NSGs) or Azure Firewall. Public network access is not explicitly denied.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\IngestionStorageAccount.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 56</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Restrict network access to the storage account by setting 'networkAcls.defaultAction' to 'Deny' and specifying allowed subnets or private endpoints.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-1</span>
                                <span class="finding-description">Microsoft.Storage/storageAccounts resource does not restrict network access using network security groups (NSGs), Azure Firewall, or private endpoints. No 'networkAcls' property is defined, so the storage account may be accessible from all networks.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpBilling.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 670</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Restrict network access to the storage account by configuring the 'networkAcls' property to allow only required subnets or private endpoints, and deny public network access.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-1</span>
                                <span class="finding-description">Key Vault resource at line 70 does not restrict network access using network security groups (NSGs) or Azure Firewall. No network rules are defined, which may expose the Key Vault to public access.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpGeo.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 70</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure the Key Vault to use network ACLs to restrict access to trusted networks only. Add 'networkAcls' property with appropriate 'bypass', 'defaultAction', and 'ipRules' or 'virtualNetworkRules' to limit access.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-1</span>
                                <span class="finding-description">CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' at line 70 has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false, exposing the database to the public internet without network security controls (NSG or Firewall).</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpGlobal.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 70</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'publicNetworkAccess' to 'Disabled' or 'isVirtualNetworkFilterEnabled' to true, and configure virtual network rules or firewall to restrict access to the CosmosDB account.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-1</span>
                                <span class="finding-description">Storage Account resource 'Microsoft.Storage/storageAccounts' at line 180 does not specify network rules, NSGs, or firewall settings, leaving it accessible from all networks.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpGlobal.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 180</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Restrict network access to the storage account by configuring 'networkAcls' with appropriate 'virtualNetworkRules' or 'ipRules', and associate the storage account with an NSG or Azure Firewall.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-1</span>
                                <span class="finding-description">Key Vault resource 'Microsoft.KeyVault/vaults' at line 217 does not restrict network access and is accessible from all networks by default.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpGlobal.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 217</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure 'networkAcls' for the Key Vault to restrict access to specific virtual networks and trusted IP addresses, and consider using private endpoints.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-1</span>
                                <span class="finding-description">CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1007 has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false, exposing the database to the public internet without network security controls. This violates ASB NS-1: Protect resources using network security groups or Azure Firewall.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpRegion.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1007</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to true. Add appropriate 'virtualNetworkRules' to restrict access to trusted networks only.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-1</span>
                                <span class="finding-description">Storage accounts (Microsoft.Storage/storageAccounts) are deployed without network security groups (NSGs) or Azure Firewall protection. No network rules or firewall settings are defined to restrict access.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpStamp.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1002</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure network rules for each storage account to restrict access to trusted networks only. Use NSGs or Azure Firewall to protect storage accounts as per ASB NS-1.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-1</span>
                                <span class="finding-description">Microsoft.Kusto/clusters resource does not specify any network security group (NSG) or Azure Firewall configuration, leaving the resource potentially exposed to unfiltered network traffic.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\ReadAdxExhaust.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 22</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure the Kusto cluster with a virtual network and associate a network security group (NSG) or protect access using Azure Firewall. Update the resource definition to include 'virtualNetworkConfiguration' and ensure NSG rules restrict access to only required sources.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">Storage account resource at line 38 does not restrict public endpoints. Public network access is not explicitly disabled.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\IngestionStorageAccount.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 38</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'networkAcls.defaultAction' to 'Deny' and use private endpoints to prevent public access to the storage account.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">Storage account resource at line 56 does not restrict public endpoints. Public network access is not explicitly disabled.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\IngestionStorageAccount.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 56</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'networkAcls.defaultAction' to 'Deny' and use private endpoints to prevent public access to the storage account.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">Microsoft.Kusto/clusters resource at line 38 does not restrict public network access or define private endpoints, potentially exposing public endpoints in violation of ASB NS-2.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpBillingExhaust.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 38</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set the 'publicNetworkAccess' property to 'Disabled' and configure private endpoints for the Microsoft.Kusto/clusters resource to restrict public access.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">Cosmos DB account at line 146 has 'publicNetworkAccess' set to 'Enabled', exposing the database account to the public internet.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpGeo.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 146</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'publicNetworkAccess' to 'Disabled' in the Cosmos DB account properties to prevent public access. Use private endpoints or virtual network rules for secure access.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' at line 70 exposes a public endpoint by setting 'publicNetworkAccess' to 'Enabled' and not restricting access with 'virtualNetworkRules' or 'ipRules'.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpGlobal.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 70</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Disable public network access by setting 'publicNetworkAccess' to 'Disabled' or configure 'virtualNetworkRules' and 'ipRules' to restrict access to trusted networks only.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">Storage Account resource 'Microsoft.Storage/storageAccounts' at line 180 is missing network restrictions, exposing a public endpoint.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpGlobal.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 180</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure 'networkAcls' to restrict public access and use private endpoints to limit exposure.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">Key Vault resource 'Microsoft.KeyVault/vaults' at line 217 is missing network restrictions, exposing a public endpoint.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpGlobal.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 217</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Restrict public network access to the Key Vault by configuring 'networkAcls' and enabling private endpoints.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-2</span>
                                <span class="finding-description">Microsoft.Kusto/clusters resource does not restrict public network access, potentially exposing public endpoints.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\ReadAdxExhaust.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 22</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set the 'publicNetworkAccess' property to 'Disabled' or restrict access to trusted networks only. Update the resource definition to include 'publicNetworkAccess': 'Disabled' to prevent public exposure.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-3</span>
                                <span class="finding-description">Storage account resource at line 38 does not implement Network Security Groups (NSGs) to control inbound and outbound traffic.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\IngestionStorageAccount.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 38</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Associate the storage account with a subnet protected by an NSG, or configure 'networkAcls' to restrict access.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-3</span>
                                <span class="finding-description">Storage account resource at line 56 does not implement Network Security Groups (NSGs) to control inbound and outbound traffic.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\IngestionStorageAccount.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 56</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Associate the storage account with a subnet protected by an NSG, or configure 'networkAcls' to restrict access.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-3</span>
                                <span class="finding-description">Microsoft.Kusto/clusters resource is missing Network Security Group (NSG) association, which is required to control inbound and outbound traffic.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\ReadAdxExhaust.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 22</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Associate the Kusto cluster with a subnet that has an NSG applied. Update the deployment to ensure the cluster is deployed within a secured subnet with appropriate NSG rules.</div>
                            </div>
                        </div>
                    </article>
                    </div>
                </section>
                <section class="severity-group" data-severity="high" data-domain="network-security">
                    <header class="severity-header high">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">High Severity</div>
                            <div class="severity-count">7</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-5</span>
                                <span class="finding-description">Storage account resource at line 38 does not use private endpoints for secure access.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\IngestionStorageAccount.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 38</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure a private endpoint for the storage account to ensure secure, private connectivity.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-5</span>
                                <span class="finding-description">Storage account resource at line 56 does not use private endpoints for secure access.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\IngestionStorageAccount.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 56</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure a private endpoint for the storage account to ensure secure, private connectivity.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-5</span>
                                <span class="finding-description">Microsoft.Storage/storageAccounts resource does not implement private endpoints. No 'privateEndpointConnections' or related configuration is present.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpBilling.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 670</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Add a private endpoint for the storage account to ensure secure, private connectivity from your virtual network.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-5</span>
                                <span class="finding-description">Microsoft.Kusto/clusters resource at line 38 does not implement private endpoints, which is required to securely access resources per ASB NS-5.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpBillingExhaust.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 38</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Add a Microsoft.Network/privateEndpoints resource and associate it with the Microsoft.Kusto/clusters resource to ensure secure, private connectivity.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-5</span>
                                <span class="finding-description">Key Vault resource at line 70 does not use private endpoints. No 'networkAcls' or private endpoint configuration is present.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpGeo.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 70</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure a private endpoint for the Key Vault and restrict access to trusted virtual networks using 'networkAcls'.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-5</span>
                                <span class="finding-description">Cosmos DB account at line 146 does not use private endpoints. 'isVirtualNetworkFilterEnabled' is set to false and 'virtualNetworkRules' is empty, which does not restrict access to private networks.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpGeo.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 146</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Enable private endpoints for the Cosmos DB account and set 'isVirtualNetworkFilterEnabled' to true. Add appropriate 'virtualNetworkRules' to restrict access to trusted subnets.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-5</span>
                                <span class="finding-description">Microsoft.Kusto/clusters resource does not use private endpoints, increasing the risk of unauthorized network access.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\ReadAdxExhaust.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 22</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure a private endpoint for the Kusto cluster by adding a 'privateEndpointConnections' property to the resource definition and ensure all access is routed through the private endpoint.</div>
                            </div>
                        </div>
                    </article>
                    </div>
                </section>
            </div>
            <div class="domain-section">
                <h3 class="domain-header">
                    <i class="fas fa-shield-alt"></i>
                    Data Protection (14 findings)
                </h3>
                <section class="severity-group" data-severity="critical" data-domain="data-protection">
                    <header class="severity-header critical">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">Critical Severity</div>
                            <div class="severity-count">11</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-1</span>
                                <span class="finding-description">Storage account resource at line 38 does not specify 'encryption' property. Azure Storage Accounts must have encryption at rest explicitly enabled to comply with ASB DP-1.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\IngestionStorageAccount.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 38</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Add the 'encryption' property to the storage account resource with 'services', 'keySource', and 'enabled' set to true. Example: "encryption": { "services": { "blob": { "enabled": true } }, "keySource": "Microsoft.Storage" }</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-1</span>
                                <span class="finding-description">Storage account resource at line 56 does not specify 'encryption' property. Azure Storage Accounts must have encryption at rest explicitly enabled to comply with ASB DP-1.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\IngestionStorageAccount.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 56</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Add the 'encryption' property to the storage account resource with 'services', 'keySource', and 'enabled' set to true. Example: "encryption": { "services": { "blob": { "enabled": true } }, "keySource": "Microsoft.Storage" }</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-1</span>
                                <span class="finding-description">Microsoft.Kusto/clusters resource at line 38 does not specify 'encryption' or 'keyVaultProperties', indicating encryption at rest is not explicitly enabled as required by ASB DP-1.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpBillingExhaust.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 38</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Add the 'encryption' property to the Microsoft.Kusto/clusters resource and configure it to enable encryption at rest. For enhanced security, specify customer-managed keys if required.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-1</span>
                                <span class="finding-description">The resource 'Microsoft.UsageBilling/accounts/dataExports' at line 54 does not specify encryption at rest for exported data. Azure Security Benchmark DP-1 requires all data storage to be encrypted at rest.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpBillingExhaustExport.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 54</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure the data export destination to use encryption at rest. Ensure that the target storage or database for data exports is encrypted using Azure-managed or customer-managed keys.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-2</span>
                                <span class="finding-description">The resource 'Microsoft.UsageBilling/accounts/dataExports' at line 54 does not explicitly enforce encryption in transit (TLS 1.2+) for data transfers to the specified 'adxParameters.kustoUri' and 'dataIngestionUri'. Azure Security Benchmark DP-2 requires all data transfers to use TLS 1.2 or higher.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpBillingExhaustExport.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 54</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Ensure that the 'adxParameters.kustoUri' and 'dataIngestionUri' endpoints enforce TLS 1.2 or higher for all data transfers. Update the configuration to use secure (https) endpoints and verify TLS enforcement.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-3</span>
                                <span class="finding-description">Parameter 'dasStorageAccountKey' at line 74 references a storage account key directly in the template. Storing sensitive information such as storage account keys in parameters violates ASB DP-3, which requires sensitive data to be stored in Azure Key Vault.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Parameters\LacpStamp.Parameters-LacpStampResources.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 74</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Store the storage account key in Azure Key Vault and reference it securely using a Key Vault reference in the template parameter.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-3</span>
                                <span class="finding-description">Sensitive connection information such as 'adxParameters.kustoUri' and 'dataIngestionUri' are provided as parameters and not integrated with Azure Key Vault. Azure Security Benchmark DP-3 requires sensitive information to be stored securely.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpBillingExhaustExport.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 54</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Store sensitive connection strings and URIs in Azure Key Vault and reference them securely in the template using Key Vault references.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-6</span>
                                <span class="finding-description">Storage account resource at line 38 does not configure Azure Key Vault integration for secrets or keys. Sensitive information should be managed using Azure Key Vault as per ASB DP-6.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\IngestionStorageAccount.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 38</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Integrate Azure Key Vault for managing secrets and keys. Reference Key Vault secrets in the storage account configuration using the 'keyVaultProperties' property.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-6</span>
                                <span class="finding-description">Storage account resource at line 56 does not configure Azure Key Vault integration for secrets or keys. Sensitive information should be managed using Azure Key Vault as per ASB DP-6.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\IngestionStorageAccount.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 56</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Integrate Azure Key Vault for managing secrets and keys. Reference Key Vault secrets in the storage account configuration using the 'keyVaultProperties' property.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-6</span>
                                <span class="finding-description">Microsoft.Kusto/clusters resource at line 38 does not configure customer-managed keys (CMK) for encryption, which is recommended for sensitive data per ASB DP-6.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpBillingExhaust.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 38</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure the 'keyVaultProperties' property in the Microsoft.Kusto/clusters resource to use customer-managed keys stored in Azure Key Vault.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-6</span>
                                <span class="finding-description">Cosmos DB account at line 146 does not specify the use of customer-managed keys (CMK) for encryption at rest. Only default encryption is implied.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpGeo.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 146</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure the Cosmos DB account to use customer-managed keys by adding the 'keyVaultKeyUri' property under 'properties' and referencing a Key Vault key.</div>
                            </div>
                        </div>
                    </article>
                    </div>
                </section>
                <section class="severity-group" data-severity="medium" data-domain="data-protection">
                    <header class="severity-header medium">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">Medium Severity</div>
                            <div class="severity-count">3</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-7</span>
                                <span class="finding-description">Storage account resource at line 38 does not specify customer-managed keys (CMK) for encryption. Using CMK is recommended for sensitive data as per ASB DP-7.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\IngestionStorageAccount.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 38</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure the storage account to use customer-managed keys by specifying the 'encryption.keySource' as 'Microsoft.Keyvault' and providing the Key Vault key URI.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-7</span>
                                <span class="finding-description">Storage account resource at line 56 does not specify customer-managed keys (CMK) for encryption. Using CMK is recommended for sensitive data as per ASB DP-7.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\IngestionStorageAccount.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 56</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure the storage account to use customer-managed keys by specifying the 'encryption.keySource' as 'Microsoft.Keyvault' and providing the Key Vault key URI.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">DP-8</span>
                                <span class="finding-description">Microsoft.Storage/storageAccounts resource does not specify customer-managed keys (CMK) for encryption at rest. Only platform-managed keys are implied.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpBilling.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 670</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure the storage account to use customer-managed keys (CMK) for encryption at rest by adding the 'encryption.keySource' property set to 'Microsoft.Keyvault' and specifying the key vault and key URI.</div>
                            </div>
                        </div>
                    </article>
                    </div>
                </section>
            </div>
            <div class="domain-section">
                <h3 class="domain-header">
                    <i class="fas fa-shield-alt"></i>
                    Access Management (1 findings)
                </h3>
                <section class="severity-group" data-severity="high" data-domain="access-management">
                    <header class="severity-header high">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">High Severity</div>
                            <div class="severity-count">1</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">AM-1</span>
                                <span class="finding-description">Role assignment at line 38 grants 'Contributor' role to the 'Ev2BuildoutServicePrincipalId' principal. The Contributor role provides broad permissions, which may exceed least privilege requirements.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\RoleAssignment.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 38</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Review the permissions required by the 'Ev2BuildoutServicePrincipalId' principal and assign a more restrictive, custom role with only the necessary permissions as per ASB AM-1.</div>
                            </div>
                        </div>
                    </article>
                    </div>
                </section>
            </div>
            <div class="domain-section">
                <h3 class="domain-header">
                    <i class="fas fa-shield-alt"></i>
                    Unknown (12 findings)
                </h3>
                <section class="severity-group" data-severity="critical" data-domain="unknown">
                    <header class="severity-header critical">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">Critical Severity</div>
                            <div class="severity-count">6</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-15</span>
                                <span class="finding-description">The resource 'Microsoft.Network/trafficManagerProfiles/externalEndpoints' at line 38 exposes a public endpoint without explicit security controls. Public endpoints must be protected to minimize exposure, as required by ASB Control NS-15.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\TrafficManagerEndpoints.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 38</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Restrict access to the external endpoint by implementing IP whitelisting, authentication, or by using private endpoints where possible. Review and secure all public-facing endpoints in the Traffic Manager profile.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-21</span>
                                <span class="finding-description">Cosmos DB account at line 146 does not implement Network Security Groups (NSGs) or any network-based access controls. 'isVirtualNetworkFilterEnabled' is false and 'virtualNetworkRules' is empty.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpGeo.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 146</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Enable 'isVirtualNetworkFilterEnabled' and define 'virtualNetworkRules' to restrict access to the Cosmos DB account from specific subnets protected by NSGs.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-21</span>
                                <span class="finding-description">CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1007 exposes a public endpoint by setting 'publicNetworkAccess' to 'Enabled'. This increases the risk of unauthorized access and violates ASB NS-2: Protect public endpoints.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpRegion.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1007</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Set 'publicNetworkAccess' to 'Disabled' to remove the public endpoint. Use private endpoints for secure access.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-21</span>
                                <span class="finding-description">Storage accounts (Microsoft.Storage/storageAccounts) are deployed without private endpoints or network restrictions, exposing public endpoints by default.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpStamp.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1002</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Disable public network access and configure private endpoints for all storage accounts to minimize public exposure as per ASB NS-21.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-21</span>
                                <span class="finding-description">Key Vaults (Microsoft.KeyVault/vaults) are deployed without private endpoints or network restrictions, exposing public endpoints by default.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpStamp.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1092</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Disable public network access and configure private endpoints for all Key Vaults to minimize public exposure as per ASB NS-21.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-22</span>
                                <span class="finding-description">Key Vaults (Microsoft.KeyVault/vaults) are deployed without network security groups (NSGs) or Azure Firewall protection. No network rules or firewall settings are defined to restrict access.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpStamp.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1092</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure Key Vault network ACLs to restrict access to trusted networks only. Use NSGs or Azure Firewall to protect Key Vaults as per ASB NS-1 and NS-22.</div>
                            </div>
                        </div>
                    </article>
                    </div>
                </section>
                <section class="severity-group" data-severity="high" data-domain="unknown">
                    <header class="severity-header high">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">High Severity</div>
                            <div class="severity-count">6</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-17</span>
                                <span class="finding-description">CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' at line 70 does not use private endpoints, increasing the risk of unauthorized network access.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpGlobal.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 70</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure a private endpoint for the CosmosDB account to ensure access is only possible from within your private network.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-17</span>
                                <span class="finding-description">Storage Account resource 'Microsoft.Storage/storageAccounts' at line 180 does not use private endpoints, increasing the risk of unauthorized access.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpGlobal.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 180</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Add a private endpoint for the storage account to ensure secure, private connectivity.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-17</span>
                                <span class="finding-description">Key Vault resource 'Microsoft.KeyVault/vaults' at line 217 does not use private endpoints, increasing the risk of unauthorized access.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpGlobal.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 217</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Add a private endpoint for the Key Vault to ensure secure, private connectivity.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-23</span>
                                <span class="finding-description">CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1007 does not configure private endpoints and allows public network access. This is a network security weakness per ASB NS-5: Use Private Endpoints.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpRegion.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1007</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Configure a private endpoint for the CosmosDB account and set 'publicNetworkAccess' to 'Disabled'.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-24</span>
                                <span class="finding-description">Storage accounts (Microsoft.Storage/storageAccounts) do not use private endpoints for secure access. No 'privateEndpointConnections' or equivalent configuration is present.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpStamp.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1002</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Add private endpoint configurations to all storage accounts to ensure secure, private connectivity as per ASB NS-24.</div>
                            </div>
                        </div>
                    </article>
                    <article class="finding-item">
                        <header class="finding-header">
                            <div class="finding-title">
                                <span class="control-id">NS-24</span>
                                <span class="finding-description">Key Vaults (Microsoft.KeyVault/vaults) do not use private endpoints for secure access. No 'privateEndpointConnections' or equivalent configuration is present.</span>
                            </div>
                        </header>
                        <div class="finding-content">
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code"></i>
                                    <span>Templates\LacpStamp.Template.json</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Line 1092</span>
                                </div>
                            </div>
                            <div class="remediation-section">
                                <h4 class="remediation-title">
                                    <i class="fas fa-tools"></i>
                                    Recommended Fix
                                </h4>
                                <div class="remediation-content">Add private endpoint configurations to all Key Vaults to ensure secure, private connectivity as per ASB NS-24.</div>
                            </div>
                        </div>
                    </article>
                    </div>
                </section>
            </div>
            <div class="no-findings" style="display: none;">
                <i class="fas fa-search no-findings-icon"></i>
                <h3>No findings match your search criteria</h3>
                <p>Try adjusting your search terms or filters</p>
            </div>
        </div>

        <!-- Footer Section -->
        <footer class="report-footer">
            <div class="export-actions">
                <button class="export-btn" onclick="window.print()">
                    <i class="fas fa-print"></i>
                    Print Report
                </button>
                <button class="export-btn" onclick="exportToJson()">
                    <i class="fas fa-download"></i>
                    Export JSON
                </button>
            </div>
            <div class="footer-info">
                <p><strong>Generated by IaC Guardian</strong> • June 16, 2025 at 09:58 PM</p>
                <p><strong>Disclaimer:</strong> This report is AI-generated and should be reviewed by security professionals.</p>
            </div>
        </footer>
    </div>
</body>
</html>