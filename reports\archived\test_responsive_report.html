<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Assessment Report - IaC Guardian</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            /* Modern Color Palette */
            --primary-blue: #1e40af;
            --primary-blue-light: #3b82f6;
            --secondary-blue: #0ea5e9;
            --accent-purple: #8b5cf6;
            --success-green: #10b981;
            --warning-amber: #f59e0b;
            --danger-red: #ef4444;
            --info-cyan: #06b6d4;

            /* Neutral Colors */
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;

            /* Semantic Colors */
            --critical-bg: #fef2f2;
            --critical-border: #fecaca;
            --critical-text: #dc2626;
            --high-bg: #fffbeb;
            --high-border: #fed7aa;
            --high-text: #ea580c;
            --medium-bg: #fefce8;
            --medium-border: #fde68a;
            --medium-text: #ca8a04;
            --low-bg: #f0f9ff;
            --low-border: #bae6fd;
            --low-text: #0284c7;

            /* Layout */
            --max-width: 1400px;
            --border-radius: 12px;
            --border-radius-sm: 8px;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--gray-700);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-size: 14px;
        }

        .main-container {
            max-width: var(--max-width);
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        /* Header Section */
        .report-header {
            background: white;
            border-radius: var(--border-radius);
            padding: 3rem 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-lg);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .report-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue), var(--accent-purple));
        }

        .report-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
        }

        .report-subtitle {
            font-size: 1.125rem;
            color: var(--gray-600);
            font-weight: 400;
            margin-bottom: 1rem;
        }

        .report-meta {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 1.5rem;
            font-size: 0.875rem;
            color: var(--gray-500);
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* Controls Section */
        .controls-section {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }

        .controls-grid {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 1.5rem;
            align-items: center;
        }

        .search-container {
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius-sm);
            font-size: 0.875rem;
            transition: all 0.2s ease;
            background: var(--gray-50);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
        }

        .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
        }

        .filter-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 0.5rem 1rem;
            border: 2px solid transparent;
            border-radius: 2rem;
            background: var(--gray-100);
            color: var(--gray-600);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            white-space: nowrap;
        }

        .filter-btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .filter-btn.active {
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .filter-btn.all.active { background: var(--primary-blue); }
        .filter-btn.critical.active { background: var(--danger-red); }
        .filter-btn.high.active { background: var(--warning-amber); }
        .filter-btn.medium.active { background: var(--medium-text); }
        .filter-btn.low.active { background: var(--info-cyan); }

        /* Summary Section */
        .summary-section {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }

        .summary-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-blue);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--gray-600);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .severity-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .severity-badge {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .severity-badge:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .severity-badge.critical {
            background: var(--critical-bg);
            border: 1px solid var(--critical-border);
            color: var(--critical-text);
        }

        .severity-badge.high {
            background: var(--high-bg);
            border: 1px solid var(--high-border);
            color: var(--high-text);
        }

        .severity-badge.medium {
            background: var(--medium-bg);
            border: 1px solid var(--medium-border);
            color: var(--medium-text);
        }

        .severity-badge.low {
            background: var(--low-bg);
            border: 1px solid var(--low-border);
            color: var(--low-text);
        }

        .severity-count {
            font-size: 1.25rem;
            font-weight: 700;
        }

        .severity-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* Findings Section */
        .findings-container {
            margin-bottom: 2rem;
        }

        .severity-group {
            background: white;
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
            box-shadow: var(--shadow);
            overflow: hidden;
            border: 1px solid var(--gray-200);
        }

        .severity-group:last-child {
            margin-bottom: 0;
        }

        .severity-header {
            padding: 1.25rem 1.5rem;
            font-weight: 600;
            font-size: 1.125rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 1px solid var(--gray-200);
        }

        .severity-header:hover {
            background: var(--gray-50);
        }

        .severity-header-left {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .severity-icon {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.875rem;
        }

        .severity-header.critical {
            background: var(--critical-bg);
            color: var(--critical-text);
            border-left: 4px solid var(--critical-text);
        }

        .severity-header.critical .severity-icon {
            background: var(--critical-text);
        }

        .severity-header.high {
            background: var(--high-bg);
            color: var(--high-text);
            border-left: 4px solid var(--high-text);
        }

        .severity-header.high .severity-icon {
            background: var(--high-text);
        }

        .severity-header.medium {
            background: var(--medium-bg);
            color: var(--medium-text);
            border-left: 4px solid var(--medium-text);
        }

        .severity-header.medium .severity-icon {
            background: var(--medium-text);
        }

        .severity-header.low {
            background: var(--low-bg);
            color: var(--low-text);
            border-left: 4px solid var(--low-text);
        }

        .severity-header.low .severity-icon {
            background: var(--low-text);
        }

        .severity-title {
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .severity-count {
            background: rgba(255, 255, 255, 0.9);
            color: inherit;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 700;
            margin-left: 0.5rem;
        }

        .toggle-icon {
            transition: transform 0.2s ease;
            color: var(--gray-500);
        }

        .severity-header.collapsed .toggle-icon {
            transform: rotate(-90deg);
        }

        .findings-list {
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .findings-list.collapsed {
            max-height: 0;
        }

        .finding-item {
            border-bottom: 1px solid var(--gray-200);
            padding: 1.5rem;
            transition: all 0.2s ease;
            background: white;
        }

        .finding-item:last-child {
            border-bottom: none;
        }

        .finding-item:hover {
            background: var(--gray-50);
        }

        .finding-header {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .finding-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
            flex-shrink: 0;
        }

        .finding-icon.critical { background: var(--danger-red); }
        .finding-icon.high { background: var(--warning-amber); }
        .finding-icon.medium { background: var(--medium-text); }
        .finding-icon.low { background: var(--info-cyan); }

        .finding-content {
            flex: 1;
            min-width: 0;
        }

        .finding-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            flex-wrap: wrap;
        }

        .control-id {
            background: var(--primary-blue);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .finding-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1rem;
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.375rem;
        }

        .meta-icon {
            color: var(--gray-400);
            width: 1rem;
        }

        .finding-description {
            color: var(--gray-700);
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .remediation-section {
            background: var(--success-green);
            background: linear-gradient(135deg, var(--success-green) 0%, #059669 100%);
            color: white;
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            margin-top: 1rem;
        }

        .remediation-title {
            font-weight: 600;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .remediation-content {
            font-size: 0.875rem;
            line-height: 1.5;
            opacity: 0.95;
        }

        .code-snippet {
            background: var(--gray-900);
            color: var(--gray-100);
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 0.8125rem;
            line-height: 1.5;
            white-space: pre-wrap;
            margin: 1rem 0;
            overflow-x: auto;
            border: 1px solid var(--gray-700);
        }

        .no-findings {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--gray-500);
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }

        .no-findings-icon {
            font-size: 3rem;
            color: var(--gray-300);
            margin-bottom: 1rem;
        }

        .no-findings h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
        }

        /* Footer */
        .report-footer {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-top: 2rem;
            box-shadow: var(--shadow);
            text-align: center;
        }

        .export-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }

        .export-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius-sm);
            background: var(--primary-blue);
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.875rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .export-btn:hover {
            background: var(--primary-blue-light);
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .footer-info {
            color: var(--gray-600);
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .footer-info strong {
            color: var(--gray-900);
        }

        /* Responsive Design */

        /* Large Desktop (1200px+) */
        @media (min-width: 1200px) {
            .main-container {
                padding: 3rem 2rem;
            }

            .stats-grid {
                grid-template-columns: repeat(4, 1fr);
            }

            .severity-overview {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        /* Desktop (992px - 1199px) */
        @media (min-width: 992px) and (max-width: 1199px) {
            .main-container {
                padding: 2rem 1.5rem;
            }

            .report-title {
                font-size: 2.25rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .severity-overview {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Tablet (768px - 991px) */
        @media (min-width: 768px) and (max-width: 991px) {
            .main-container {
                padding: 1.5rem 1rem;
            }

            .report-header {
                padding: 2rem 1.5rem;
            }

            .report-title {
                font-size: 2rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .report-meta {
                flex-direction: column;
                gap: 0.5rem;
            }

            .controls-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .filter-buttons {
                justify-content: center;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .severity-overview {
                grid-template-columns: repeat(2, 1fr);
            }

            .finding-title {
                font-size: 1rem;
            }

            .finding-meta {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        /* Mobile Large (576px - 767px) */
        @media (min-width: 576px) and (max-width: 767px) {
            .main-container {
                padding: 1rem 0.75rem;
            }

            .report-header {
                padding: 1.5rem 1rem;
            }

            .report-title {
                font-size: 1.75rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .report-subtitle {
                font-size: 1rem;
            }

            .report-meta {
                flex-direction: column;
                gap: 0.25rem;
                font-size: 0.8125rem;
            }

            .controls-section {
                padding: 1rem;
            }

            .controls-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .filter-buttons {
                justify-content: center;
                gap: 0.375rem;
            }

            .filter-btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.6875rem;
            }

            .summary-section {
                padding: 1.5rem 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .severity-overview {
                grid-template-columns: 1fr;
            }

            .finding-item {
                padding: 1rem;
            }

            .finding-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.75rem;
            }

            .finding-title {
                font-size: 0.9375rem;
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .export-actions {
                flex-direction: column;
                align-items: center;
            }
        }

        /* Mobile Small (up to 575px) */
        @media (max-width: 575px) {
            .main-container {
                padding: 0.75rem 0.5rem;
            }

            .report-header {
                padding: 1.25rem 0.75rem;
                margin-bottom: 1rem;
            }

            .report-title {
                font-size: 1.5rem;
                flex-direction: column;
                gap: 0.375rem;
            }

            .report-subtitle {
                font-size: 0.9375rem;
            }

            .report-meta {
                flex-direction: column;
                gap: 0.25rem;
                font-size: 0.75rem;
            }

            .controls-section {
                padding: 0.75rem;
            }

            .search-input {
                font-size: 1rem; /* Prevents zoom on iOS */
                padding: 0.75rem 1rem 0.75rem 2.25rem;
            }

            .filter-buttons {
                gap: 0.25rem;
            }

            .filter-btn {
                padding: 0.375rem 0.625rem;
                font-size: 0.625rem;
                min-width: auto;
            }

            .summary-section {
                padding: 1.25rem 0.75rem;
            }

            .summary-title {
                font-size: 1.25rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            .stat-card {
                padding: 1rem;
            }

            .stat-number {
                font-size: 2rem;
            }

            .severity-overview {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .severity-header {
                padding: 1rem;
                font-size: 1rem;
            }

            .finding-item {
                padding: 0.75rem;
            }

            .finding-icon {
                width: 2rem;
                height: 2rem;
                font-size: 0.875rem;
            }

            .finding-title {
                font-size: 0.875rem;
            }

            .control-id {
                font-size: 0.6875rem;
                padding: 0.1875rem 0.5rem;
            }

            .finding-meta {
                font-size: 0.8125rem;
            }

            .finding-description {
                font-size: 0.875rem;
            }

            .code-snippet {
                font-size: 0.75rem;
                padding: 0.75rem;
            }

            .export-actions {
                flex-direction: column;
                gap: 0.5rem;
            }

            .export-btn {
                width: 100%;
                justify-content: center;
            }
        }

        /* Print Styles */
        @media print {
            body {
                background: white !important;
            }

            .main-container {
                max-width: none;
                padding: 0;
            }

            .report-header,
            .controls-section,
            .summary-section,
            .severity-group,
            .report-footer {
                box-shadow: none !important;
                break-inside: avoid;
            }

            .controls-section,
            .export-actions {
                display: none !important;
            }

            .findings-list {
                max-height: none !important;
            }
        }
    </style>

    <script>
        // Modern JavaScript for enhanced interactivity
        let searchTimeout;
        let allFindings = [];
        let currentFilter = 'all';

        document.addEventListener('DOMContentLoaded', function() {
            initializeReport();
            setupEventListeners();
            loadFindings();
        });

        function initializeReport() {
            // Initialize filter buttons
            const filterButtons = document.querySelectorAll('.filter-btn');
            filterButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    setActiveFilter(this.dataset.severity);
                });
            });

            // Initialize search
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.addEventListener('input', debounceSearch);
            }

            // Initialize collapsible sections
            const severityHeaders = document.querySelectorAll('.severity-header');
            severityHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    toggleSeverityGroup(this);
                });
            });
        }

        function setupEventListeners() {
            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    clearSearch();
                }
                if (e.ctrlKey && e.key === 'f') {
                    e.preventDefault();
                    focusSearch();
                }
            });
        }

        function debounceSearch() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch();
            }, 300);
        }

        function performSearch() {
            const searchTerm = document.querySelector('.search-input').value.toLowerCase();
            const findings = document.querySelectorAll('.finding-item');
            let visibleCount = 0;

            findings.forEach(finding => {
                const text = finding.textContent.toLowerCase();
                const isVisible = text.includes(searchTerm);
                finding.style.display = isVisible ? 'block' : 'none';
                if (isVisible) visibleCount++;
            });

            updateNoResultsMessage(visibleCount === 0 && searchTerm.length > 0);
        }

        function setActiveFilter(severity) {
            currentFilter = severity;

            // Update button states
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-severity="${severity}"]`).classList.add('active');

            // Filter findings
            const severityGroups = document.querySelectorAll('.severity-group');
            severityGroups.forEach(group => {
                const groupSeverity = group.dataset.severity;
                if (severity === 'all' || groupSeverity === severity) {
                    group.style.display = 'block';
                } else {
                    group.style.display = 'none';
                }
            });

            // Update URL hash for bookmarking
            window.location.hash = severity === 'all' ? '' : severity;
        }

        function toggleSeverityGroup(header) {
            const group = header.parentElement;
            const findingsList = group.querySelector('.findings-list');
            const isCollapsed = header.classList.contains('collapsed');

            if (isCollapsed) {
                header.classList.remove('collapsed');
                findingsList.classList.remove('collapsed');
                findingsList.style.maxHeight = findingsList.scrollHeight + 'px';
            } else {
                header.classList.add('collapsed');
                findingsList.classList.add('collapsed');
                findingsList.style.maxHeight = '0';
            }
        }

        function clearSearch() {
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.value = '';
                performSearch();
            }
        }

        function focusSearch() {
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }

        function updateNoResultsMessage(show) {
            const noResults = document.querySelector('.no-findings');
            if (noResults) {
                noResults.style.display = show ? 'block' : 'none';
            }
        }

        function exportToJson() {
            const data = {
                timestamp: new Date().toISOString(),
                findings: allFindings,
                summary: {
                    total: allFindings.length,
                    critical: allFindings.filter(f => f.severity === 'CRITICAL').length,
                    high: allFindings.filter(f => f.severity === 'HIGH').length,
                    medium: allFindings.filter(f => f.severity === 'MEDIUM').length,
                    low: allFindings.filter(f => f.severity === 'LOW').length
                }
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], {
                type: 'application/json'
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `security-findings-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function loadFindings() {
            // This would be populated with actual findings data
            allFindings = [];
        }

        // Initialize filter from URL hash
        window.addEventListener('load', function() {
            const hash = window.location.hash.substring(1);
            if (hash && ['critical', 'high', 'medium', 'low'].includes(hash)) {
                setActiveFilter(hash);
            }
        });
    </script>
</head>

<body>
    <div class="main-container">
        <!-- Header Section -->
        <header class="report-header">
            <h1 class="report-title">
                <i class="fas fa-shield-alt"></i>
                Security Assessment Report
            </h1>
            <p class="report-subtitle">Infrastructure as Code Security Analysis</p>
            <div class="report-meta">
                <div class="meta-item">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Generated: {timestamp}</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-robot"></i>
                    <span>IaC Guardian GPT</span>
                </div>
            </div>
        </header>

        <!-- Controls Section -->
        <section class="controls-section">
            <div class="controls-grid">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search findings..." aria-label="Search findings">
                    <i class="fas fa-search search-icon"></i>
                </div>
                <div class="filter-buttons">
                    <button class="filter-btn all active" data-severity="all">All</button>
                    <button class="filter-btn critical" data-severity="critical">Critical</button>
                    <button class="filter-btn high" data-severity="high">High</button>
                    <button class="filter-btn medium" data-severity="medium">Medium</button>
                    <button class="filter-btn low" data-severity="low">Low</button>
                </div>
            </div>
        </section>

        
        <!-- Summary Section -->
        <section class="summary-section">
            <h2 class="summary-title">
                <i class="fas fa-chart-bar"></i>
                Executive Summary
            </h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">5</div>
                    <div class="stat-label">Total Findings</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">5</div>
                    <div class="stat-label">Files Affected</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">3</div>
                    <div class="stat-label">High Priority Issues</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">5</div>
                    <div class="stat-label">Security Controls</div>
                </div>
            </div>
            <div class="severity-overview">
                <div class="severity-badge critical">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        Critical
                    </div>
                    <div class="severity-count">1</div>
                </div>
                <div class="severity-badge high">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        High
                    </div>
                    <div class="severity-count">2</div>
                </div>
                <div class="severity-badge medium">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        Medium
                    </div>
                    <div class="severity-count">1</div>
                </div>
                <div class="severity-badge low">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        Low
                    </div>
                    <div class="severity-count">1</div>
                </div>
            </div>
        </section>

        <div class="findings-container">
            
        <section class="severity-group" data-severity="critical">
            <header class="severity-header critical">
                <div class="severity-header-left">
                    <div class="severity-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="severity-title">Critical Severity</div>
                    <div class="severity-count">1</div>
                </div>
                <i class="fas fa-chevron-down toggle-icon"></i>
            </header>
            <div class="findings-list">
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon critical">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>network.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 25</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Network Security Group allows unrestricted inbound access from the internet on port 22 (SSH). This creates a significant security risk as it exposes the SSH service to potential brute force attacks and unauthorized access attempts.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Restrict SSH access to specific IP addresses or ranges. Consider using Azure Bastion for secure remote access instead of exposing SSH directly to the internet.
                        </div>
                    </div>
                    <div class="code-snippet">securityRules: [
  {
    name: &#x27;SSH&#x27;
    properties: {
      access: &#x27;Allow&#x27;
      direction: &#x27;Inbound&#x27;
      sourceAddressPrefix: &#x27;*&#x27;
      destinationPortRange: &#x27;22&#x27;
    }
  }
]</div>
                </article>
            </div>
        </section>
        <section class="severity-group" data-severity="high">
            <header class="severity-header high">
                <div class="severity-header-left">
                    <div class="severity-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="severity-title">High Severity</div>
                    <div class="severity-count">2</div>
                </div>
                <i class="fas fa-chevron-down toggle-icon"></i>
            </header>
            <div class="findings-list">
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">IM-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>storage.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 42</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Storage account is configured to allow public blob access which could lead to unauthorized data exposure. Public access should be disabled unless specifically required.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Set &#x27;allowBlobPublicAccess&#x27; to false in the storage account configuration. Use private endpoints or shared access signatures for controlled access.
                        </div>
                    </div>
                    <div class="code-snippet">resource storageAccount &#x27;Microsoft.Storage/storageAccounts@2021-04-01&#x27; = {
  properties: {
    allowBlobPublicAccess: true
  }
}</div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>database.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 56</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        SQL Database server allows connections from all Azure services without IP restrictions. This broad access could be exploited if other Azure resources are compromised.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Configure specific firewall rules to allow only necessary Azure services and IP ranges. Disable &#x27;Allow Azure services&#x27; if not required.
                        </div>
                    </div>
                    <div class="code-snippet">resource sqlServer &#x27;Microsoft.Sql/servers@2021-02-01-preview&#x27; = {
  properties: {
    firewallRules: [
      {
        name: &#x27;AllowAllAzureServices&#x27;
        startIpAddress: &#x27;0.0.0.0&#x27;
        endIpAddress: &#x27;0.0.0.0&#x27;
      }
    ]
  }
}</div>
                </article>
            </div>
        </section>
        <section class="severity-group" data-severity="medium">
            <header class="severity-header medium">
                <div class="severity-header-left">
                    <div class="severity-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="severity-title">Medium Severity</div>
                    <div class="severity-count">1</div>
                </div>
                <i class="fas fa-chevron-down toggle-icon"></i>
            </header>
            <div class="findings-list">
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>webapp.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 18</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Web application is not configured with HTTPS-only access. This allows unencrypted HTTP traffic which could expose sensitive data in transit.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Enable HTTPS-only access by setting &#x27;httpsOnly&#x27; property to true in the web app configuration.
                        </div>
                    </div>
                    <div class="code-snippet">resource webApp &#x27;Microsoft.Web/sites@2021-02-01&#x27; = {
  properties: {
    httpsOnly: false
  }
}</div>
                </article>
            </div>
        </section>
        <section class="severity-group" data-severity="low">
            <header class="severity-header low">
                <div class="severity-header-left">
                    <div class="severity-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="severity-title">Low Severity</div>
                    <div class="severity-count">1</div>
                </div>
                <i class="fas fa-chevron-down toggle-icon"></i>
            </header>
            <div class="findings-list">
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">LT-4</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>keyvault.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 33</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Key Vault does not have diagnostic logging enabled. This limits visibility into access patterns and potential security incidents.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Enable diagnostic settings for the Key Vault to log access events to Azure Monitor or a Log Analytics workspace.
                        </div>
                    </div>
                    <div class="code-snippet">resource keyVault &#x27;Microsoft.KeyVault/vaults@2021-06-01-preview&#x27; = {
  properties: {
    // Missing diagnostic settings
  }
}</div>
                </article>
            </div>
        </section>
            <div class="no-findings" style="display: none;">
                <i class="fas fa-search no-findings-icon"></i>
                <h3>No findings match your search criteria</h3>
                <p>Try adjusting your search terms or filters</p>
            </div>
        </div>

        <!-- Footer Section -->
        <footer class="report-footer">
            <div class="export-actions">
                <button class="export-btn" onclick="window.print()">
                    <i class="fas fa-print"></i>
                    Print Report
                </button>
                <button class="export-btn" onclick="exportToJson()">
                    <i class="fas fa-download"></i>
                    Export JSON
                </button>
            </div>
            <div class="footer-info">
                <p><strong>Generated by IaC Guardian GPT</strong> • {timestamp}</p>
                <p><strong>Disclaimer:</strong> This report is AI-generated and should be reviewed by security professionals.</p>
            </div>
        </footer>
    </div>
</body>
</html>