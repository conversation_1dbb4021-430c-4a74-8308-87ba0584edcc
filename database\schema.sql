-- Master Security Controls Database Schema
-- Version: 1.0
-- Database: PostgreSQL 14+

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Security Frameworks Table
CREATE TABLE security_frameworks (
    framework_id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    version VARCHAR(50),
    description TEXT,
    official_url VARCHAR(500),
    priority INTEGER DEFAULT 100,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT unique_framework_name_version UNIQUE (name, version)
);

-- Security Domains Table
CREATE TABLE security_domains (
    domain_id VARCHAR(50) PRIMARY KEY,
    framework_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    priority INTEGER DEFAULT 100,
    is_active BOOLEAN DEFAULT TRUE,
    
    FOREI<PERSON><PERSON> KEY (framework_id) REFERENCES security_frameworks(framework_id) ON DELETE CASCADE,
    CONSTRAINT unique_domain_per_framework UNIQUE (framework_id, name)
);

-- Security Controls Master Table
CREATE TABLE security_controls (
    control_id VARCHAR(50) PRIMARY KEY,
    framework_id VARCHAR(50) NOT NULL,
    domain_id VARCHAR(50) NOT NULL,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    severity ENUM('CRITICAL', 'HIGH', 'MEDIUM', 'LOW') NOT NULL,
    implementation_guidance TEXT,
    azure_guidance TEXT,
    stakeholders TEXT[], -- Array of responsible roles
    policies TEXT[], -- Array of related policies
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    version INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    data_quality_score DECIMAL(3,2) DEFAULT 1.0,
    
    FOREIGN KEY (framework_id) REFERENCES security_frameworks(framework_id) ON DELETE CASCADE,
    FOREIGN KEY (domain_id) REFERENCES security_domains(domain_id) ON DELETE CASCADE
);

-- Control Sources (for audit trail and data lineage)
CREATE TABLE control_sources (
    source_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    control_id VARCHAR(50) NOT NULL,
    source_type ENUM('CSV', 'JSON', 'EXCEL', 'API', 'MANUAL', 'AI_MERGED') NOT NULL,
    source_file VARCHAR(500),
    source_url VARCHAR(500),
    source_content JSONB, -- Store original source data
    imported_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    imported_by VARCHAR(100),
    data_quality_score DECIMAL(3,2),
    
    FOREIGN KEY (control_id) REFERENCES security_controls(control_id) ON DELETE CASCADE
);

-- Resource Types Table
CREATE TABLE resource_types (
    resource_type_id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50),
    description TEXT,
    arm_types TEXT[], -- Array of ARM resource types
    terraform_types TEXT[], -- Array of Terraform resource types
    bicep_types TEXT[], -- Array of Bicep resource types
    keywords TEXT[], -- Array of keywords for matching
    is_active BOOLEAN DEFAULT TRUE,
    
    CONSTRAINT unique_resource_name UNIQUE (name)
);

-- Resource-Control Mappings
CREATE TABLE resource_control_mappings (
    mapping_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    resource_type_id VARCHAR(50) NOT NULL,
    control_id VARCHAR(50) NOT NULL,
    relevance_score DECIMAL(3,2) DEFAULT 1.0,
    mapping_type ENUM('DIRECT', 'INFERRED', 'CUSTOM', 'AI_GENERATED') DEFAULT 'DIRECT',
    confidence_score DECIMAL(3,2) DEFAULT 1.0,
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (resource_type_id) REFERENCES resource_types(resource_type_id) ON DELETE CASCADE,
    FOREIGN KEY (control_id) REFERENCES security_controls(control_id) ON DELETE CASCADE,
    CONSTRAINT unique_resource_control_mapping UNIQUE (resource_type_id, control_id)
);

-- Organizations Table (for multi-tenancy)
CREATE TABLE organizations (
    organization_id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    settings JSONB, -- Organization-specific settings
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Custom Controls (organization-specific extensions)
CREATE TABLE custom_controls (
    custom_control_id VARCHAR(50) PRIMARY KEY,
    organization_id VARCHAR(50) NOT NULL,
    base_control_id VARCHAR(50), -- Optional reference to base control
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    severity ENUM('CRITICAL', 'HIGH', 'MEDIUM', 'LOW') NOT NULL,
    implementation_guidance TEXT,
    custom_fields JSONB, -- Flexible custom metadata
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    
    FOREIGN KEY (organization_id) REFERENCES organizations(organization_id) ON DELETE CASCADE,
    FOREIGN KEY (base_control_id) REFERENCES security_controls(control_id) ON DELETE SET NULL
);

-- Control Relationships (for cross-validation and dependencies)
CREATE TABLE control_relationships (
    relationship_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    parent_control_id VARCHAR(50) NOT NULL,
    child_control_id VARCHAR(50) NOT NULL,
    relationship_type ENUM('DEPENDS_ON', 'CONFLICTS_WITH', 'COMPLEMENTS', 'SUPERSEDES', 'RELATED_TO'),
    strength DECIMAL(3,2) DEFAULT 1.0,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parent_control_id) REFERENCES security_controls(control_id) ON DELETE CASCADE,
    FOREIGN KEY (child_control_id) REFERENCES security_controls(control_id) ON DELETE CASCADE,
    CONSTRAINT no_self_relationship CHECK (parent_control_id != child_control_id),
    CONSTRAINT unique_control_relationship UNIQUE (parent_control_id, child_control_id, relationship_type)
);

-- Control References (documentation, links, policies)
CREATE TABLE control_references (
    reference_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    control_id VARCHAR(50) NOT NULL,
    reference_type ENUM('DOCUMENTATION', 'POLICY', 'IMPLEMENTATION', 'EXAMPLE', 'AZURE_GUIDANCE'),
    title VARCHAR(500),
    url VARCHAR(1000),
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (control_id) REFERENCES security_controls(control_id) ON DELETE CASCADE
);

-- Analysis History (for learning and optimization)
CREATE TABLE analysis_history (
    analysis_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(100),
    resource_type_id VARCHAR(50),
    control_id VARCHAR(50),
    file_path VARCHAR(1000),
    finding_type ENUM('TRUE_POSITIVE', 'FALSE_POSITIVE', 'FALSE_NEGATIVE', 'CONFIRMED', 'REJECTED'),
    confidence_score DECIMAL(3,2),
    user_feedback ENUM('CONFIRMED', 'REJECTED', 'MODIFIED', 'PENDING'),
    feedback_details JSONB,
    analyzed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (resource_type_id) REFERENCES resource_types(resource_type_id) ON DELETE SET NULL,
    FOREIGN KEY (control_id) REFERENCES security_controls(control_id) ON DELETE SET NULL
);

-- Performance optimization indexes
CREATE INDEX idx_controls_framework_domain ON security_controls(framework_id, domain_id);
CREATE INDEX idx_controls_severity ON security_controls(severity);
CREATE INDEX idx_controls_active ON security_controls(is_active) WHERE is_active = TRUE;
CREATE INDEX idx_controls_quality ON security_controls(data_quality_score DESC);

CREATE INDEX idx_mappings_resource_type ON resource_control_mappings(resource_type_id);
CREATE INDEX idx_mappings_relevance ON resource_control_mappings(relevance_score DESC);
CREATE INDEX idx_mappings_confidence ON resource_control_mappings(confidence_score DESC);

CREATE INDEX idx_sources_control ON control_sources(control_id);
CREATE INDEX idx_sources_type ON control_sources(source_type);
CREATE INDEX idx_sources_imported ON control_sources(imported_at DESC);

CREATE INDEX idx_relationships_parent ON control_relationships(parent_control_id);
CREATE INDEX idx_relationships_child ON control_relationships(child_control_id);
CREATE INDEX idx_relationships_type ON control_relationships(relationship_type);

CREATE INDEX idx_analysis_resource ON analysis_history(resource_type_id, analyzed_at DESC);
CREATE INDEX idx_analysis_control ON analysis_history(control_id, analyzed_at DESC);
CREATE INDEX idx_analysis_feedback ON analysis_history(user_feedback);

-- Full-text search indexes
CREATE INDEX idx_controls_search ON security_controls USING gin(to_tsvector('english', name || ' ' || description));
CREATE INDEX idx_controls_name_trgm ON security_controls USING gin(name gin_trgm_ops);

-- Triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_security_controls_updated_at 
    BEFORE UPDATE ON security_controls 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_security_frameworks_updated_at 
    BEFORE UPDATE ON security_frameworks 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Views for common queries
CREATE VIEW v_active_controls AS
SELECT 
    sc.control_id,
    sc.framework_id,
    sf.name as framework_name,
    sc.domain_id,
    sd.name as domain_name,
    sd.priority as domain_priority,
    sc.name,
    sc.description,
    sc.severity,
    sc.implementation_guidance,
    sc.azure_guidance,
    sc.data_quality_score
FROM security_controls sc
JOIN security_frameworks sf ON sc.framework_id = sf.framework_id
JOIN security_domains sd ON sc.domain_id = sd.domain_id
WHERE sc.is_active = TRUE 
AND sf.is_active = TRUE 
AND sd.is_active = TRUE;

CREATE VIEW v_resource_control_recommendations AS
SELECT 
    rt.name as resource_type,
    rt.category as resource_category,
    sc.control_id,
    sc.name as control_name,
    sc.severity,
    sd.name as domain_name,
    sd.priority as domain_priority,
    rcm.relevance_score,
    rcm.confidence_score,
    sf.priority as framework_priority
FROM resource_control_mappings rcm
JOIN resource_types rt ON rcm.resource_type_id = rt.resource_type_id
JOIN security_controls sc ON rcm.control_id = sc.control_id
JOIN security_domains sd ON sc.domain_id = sd.domain_id
JOIN security_frameworks sf ON sc.framework_id = sf.framework_id
WHERE rt.is_active = TRUE 
AND sc.is_active = TRUE 
AND sf.is_active = TRUE 
AND sd.is_active = TRUE;

-- Initial data population will be handled by migration scripts
