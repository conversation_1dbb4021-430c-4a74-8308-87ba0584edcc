PS C:\Users\<USER>\REPOS\IaCGuardianGPT> python .\src\core\security_opt.py --local-folder "C:\Users\<USER>\Downloads\drop_BuildEv2Artifacts_main (2)\drop_BuildEv2Artifacts_main\buildout\ev2\RegionAgnostic\Templates"
C:\Users\<USER>\REPOS\IaCGuardianGPT\src\core\security_opt.py:12321: SyntaxWarning: invalid escape sequence '\/'
  `${{filePath.split(/[\\\\\\\\\\\\\/]/).pop()}}_${{lineNumber}}`,
2025-06-24 15:53:24,933 - __main__ - INFO - Loading environment variables from: C:\Users\<USER>\REPOS\IaCGuardianGPT\.env
2025-06-24 15:53:24,937 - __main__ - INFO - Available Azure DevOps environment variables:
2025-06-24 15:53:24,938 - __main__ - INFO -   - AZURE_DEVOPS_PAT: D7S...80z
2025-06-24 15:53:24,938 - __main__ - INFO -   - AZURE_DEVOPS_ORG: kvashik50587
2025-06-24 15:53:24,938 - __main__ - INFO -   - AZURE_DEVOPS_PROJECT: AIAgents
2025-06-24 15:53:24,938 - __main__ - INFO - Available Azure OpenAI environment variables:
2025-06-24 15:53:24,938 - __main__ - INFO -   - AZURE_OPENAI_ENDPOINT: https://msh2024.openai.azure.com
2025-06-24 15:53:24,938 - __main__ - INFO -   - AZURE_OPENAI_API_KEY: 712...e15
2025-06-24 15:53:24,938 - __main__ - INFO -   - AZURE_OPENAI_DEPLOYMENT: gpt-4.1
2025-06-24 15:53:24,938 - __main__ - INFO -   - AZURE_OPENAI_API_VERSION: 2025-01-01-preview
2025-06-24 15:53:24,940 - __main__ - INFO -   - AZURE_OPENAI_USE_AD_AUTH: true
2025-06-24 15:53:24,940 - __main__ - INFO - Loading template-parameter matching configuration...
2025-06-24 15:53:24,940 - __main__ - INFO - Template identifiers: ['Template', 'template', 'main', 'deploy', 'ArmTemplate', 'app', 'deploymentTemplate']
2025-06-24 15:53:24,940 - __main__ - INFO - Parameter identifiers: ['Param', 'Parameter', 'params', 'parameters', 'ArmParam', 'deploymentParameters']
2025-06-24 15:53:24,940 - __main__ - INFO - Prompt optimization enabled: True
2025-06-24 15:53:24,940 - __main__ - INFO - Using analysis seed: 42 for consistent results
2025-06-24 15:53:24,941 - __main__ - INFO - Benchmark source priority: csv → json → excel → fallback
2025-06-24 15:53:24,941 - __main__ - INFO - Domain priority enforcement: True
2025-06-24 15:53:24,941 - __main__ - INFO - Domain priority order: Identity Management → Network Security → Data Protection → Access Management → Logging and Monitoring
2025-06-24 15:53:24,941 - __main__ - INFO - Control ID validation system initialized to prevent AI hallucination
2025-06-24 15:53:24,946 - __main__ - INFO - Enhanced template parameter expander initialized with cross-reference resolution
2025-06-24 15:53:24,947 - __main__ - INFO - Loading Azure resource mappings from: C:\Users\<USER>\REPOS\IaCGuardianGPT\data\mappings\azure_resource_mappings.json
2025-06-24 15:53:24,979 - __main__ - INFO - Successfully loaded 17 resource categories from data/mappings/azure_resource_mappings.json
2025-06-24 15:53:24,979 - __main__ - INFO - Initializing Azure OpenAI client with endpoint: https://msh2024.openai.azure.com
2025-06-24 15:53:24,979 - __main__ - INFO - Attempting to use Azure AD authentication
2025-06-24 15:53:24,979 - azure.identity._credentials.environment - INFO - No environment configuration found.
2025-06-24 15:53:24,988 - azure.identity._credentials.managed_identity - INFO - ManagedIdentityCredential will use IMDS
2025-06-24 15:53:25,728 - __main__ - INFO - Initializing Azure Security Benchmark data...
2025-06-24 15:53:25,728 - __main__ - INFO - ================================================================================
2025-06-24 15:53:25,728 - __main__ - INFO - PREPARING OPTIMIZED AZURE SECURITY BENCHMARK
2025-06-24 15:53:25,728 - __main__ - INFO - ================================================================================
2025-06-24 15:53:25,728 - __main__ - INFO - Attempting to load benchmark from source: CSV
2025-06-24 15:53:25,728 - __main__ - INFO - 📁 Loading optimized CSV benchmark with domain prioritization
2025-06-24 15:53:25,728 - __main__ - INFO - 📄 Found 3 CSV files to process
2025-06-24 15:53:25,728 - __main__ - INFO - 📄 Loading Identity Management controls from identity_management.csv (Priority: 1)
2025-06-24 15:53:25,774 - __main__ - INFO - 📊 CSV file identity_management.csv has 9 rows and columns: ['ASB ID', 'Control Domain', 'Recommendation', 'Security Principle', 'Azure Guidance', 'Implementation and additional context', 'Customer Security Stakeholders', 'Azure Policy Mapping']
2025-06-24 15:53:25,793 - __main__ - INFO - ✅ Loaded 9 Identity Management controls from identity_management.csv
2025-06-24 15:53:25,793 - __main__ - INFO - 📄 Loading Network Security controls from network_security_with_urls.csv (Priority: 2)
2025-06-24 15:53:25,858 - __main__ - INFO - 📊 CSV file network_security_with_urls.csv has 10 rows and columns: ['ASB ID', 'Control Domain', 'Recommendation', 'Security Principle', 'Azure Guidance', 'Implementation and additional context', 'Customer Security Stakeholders', 'Azure Policy Mapping']
2025-06-24 15:53:25,871 - __main__ - INFO - ✅ Loaded 10 Network Security controls from network_security_with_urls.csv
2025-06-24 15:53:25,871 - __main__ - INFO - 📄 Loading Data Protection controls from data_protection.csv (Priority: 3)
2025-06-24 15:53:25,924 - __main__ - INFO - 📊 CSV file data_protection.csv has 8 rows and columns: ['ASB ID', 'Control Domain', 'Recommendation', 'Security Principle', 'Azure Guidance', 'Implementation and additional context', 'Customer Security Stakeholders', 'Azure Policy Mapping']
2025-06-24 15:53:25,940 - __main__ - INFO - ✅ Loaded 8 Data Protection controls from data_protection.csv
2025-06-24 15:53:25,940 - __main__ - INFO - 📊 CSV Benchmark Summary: 27 total controls loaded
2025-06-24 15:53:25,940 - __main__ - INFO - 📊 Domain Distribution:
2025-06-24 15:53:25,940 - __main__ - INFO -    • Network Security: 10 controls (37.0%)
2025-06-24 15:53:25,940 - __main__ - INFO -    • Identity Management: 9 controls (33.3%)
2025-06-24 15:53:25,940 - __main__ - INFO -    • Data Protection: 8 controls (29.6%)
2025-06-24 15:53:25,941 - __main__ - INFO - ✅ Successfully loaded 27 controls from CSV
2025-06-24 15:53:25,941 - __main__ - INFO - 🔄 Applying domain prioritization to controls
2025-06-24 15:53:25,941 - __main__ - INFO - 📊 Domain prioritization applied:
2025-06-24 15:53:25,941 - __main__ - INFO -    1. Identity Management: 9 controls
2025-06-24 15:53:25,941 - __main__ - INFO -    2. Network Security: 10 controls
2025-06-24 15:53:25,941 - __main__ - INFO -    3. Data Protection: 8 controls
2025-06-24 15:53:25,941 - __main__ - INFO - 🏗️  Creating optimized benchmark structure from CSV
2025-06-24 15:53:25,941 - __main__ - INFO - 🔗 Creating RAG chunks for enhanced analysis
2025-06-24 15:53:25,942 - __main__ - INFO - 🔗 Enriching benchmark with Azure Security Center policies
2025-06-24 15:53:25,942 - __main__ - INFO - Loading Azure Security Center policies from: C:\Users\<USER>\REPOS\IaCGuardianGPT\data\mappings\AzureSecurityCenter.json
2025-06-24 15:53:26,004 - __main__ - INFO - Successfully loaded 85 policy groups and 225 policy definitions
2025-06-24 15:53:26,012 - __main__ - INFO - Enriched 19 benchmark controls with Azure Security Center policy information
2025-06-24 15:53:26,013 - __main__ - INFO - ✅ Security Center policies integrated
2025-06-24 15:53:26,014 - __main__ - INFO - 📊 Final benchmark: 27 controls, 27 RAG chunks
2025-06-24 15:53:26,015 - __main__ - INFO - 📊 BENCHMARK COMPOSITION ANALYSIS
2025-06-24 15:53:26,016 - __main__ - INFO - --------------------------------------------------
2025-06-24 15:53:26,016 - __main__ - INFO - Domain Distribution:
2025-06-24 15:53:26,016 - __main__ - INFO -    • Network Security: 10 controls (37.0%)
2025-06-24 15:53:26,017 - __main__ - INFO -    • Identity Management: 9 controls (33.3%)
2025-06-24 15:53:26,018 - __main__ - INFO -    • Data Protection: 8 controls (29.6%)
2025-06-24 15:53:26,019 - __main__ - INFO - Severity Distribution:
2025-06-24 15:53:26,020 - __main__ - INFO -    • CRITICAL: 11 controls (40.7%)
2025-06-24 15:53:26,021 - __main__ - INFO -    • MEDIUM: 12 controls (44.4%)
2025-06-24 15:53:26,021 - __main__ - INFO -    • LOW: 4 controls (14.8%)
2025-06-24 15:53:26,022 - __main__ - INFO - 🔍 Building control ID validation index...
2025-06-24 15:53:26,022 - __main__ - INFO - ✅ Built control ID index: 27 controls across 11 domains
2025-06-24 15:53:26,023 - __main__ - INFO -   - Identity Management: 9 controls (IM-1 to IM-9)
2025-06-24 15:53:26,023 - __main__ - INFO -   - Network Security: 10 controls (NS-1 to NS-10)
2025-06-24 15:53:26,023 - __main__ - INFO -   - Data Protection: 8 controls (DP-1 to DP-8)
2025-06-24 15:53:26,024 - __main__ - INFO -   - Privileged Access: 0 controls (PA-1 to PA-N/A)
2025-06-24 15:53:26,025 - __main__ - INFO -   - Logging and Threat Detection: 0 controls (LT-1 to LT-N/A)
2025-06-24 15:53:26,025 - __main__ - INFO -   - Asset Management: 0 controls (AM-1 to AM-N/A)
2025-06-24 15:53:26,025 - __main__ - INFO -   - Posture and Vulnerability Management: 0 controls (PV-1 to PV-N/A)
2025-06-24 15:53:26,025 - __main__ - INFO -   - Endpoint Security: 0 controls (ES-1 to ES-N/A)
2025-06-24 15:53:26,026 - __main__ - INFO -   - Backup and Recovery: 0 controls (BR-1 to BR-N/A)
2025-06-24 15:53:26,026 - __main__ - INFO -   - DevOps Security: 0 controls (DS-1 to DS-N/A)
2025-06-24 15:53:26,027 - __main__ - INFO -   - Governance and Strategy: 0 controls (GS-1 to GS-N/A)
2025-06-24 15:53:26,027 - __main__ - INFO - 🔗 Building dynamic resource-to-control correlations with domain priority...
2025-06-24 15:53:26,029 - __main__ - INFO - 📊 Generated 9 resource type mappings with domain priority ordering
2025-06-24 15:53:26,031 - __main__ - INFO - ✅ Built resource-control correlations for 9 resource types
2025-06-24 15:53:26,031 - __main__ - INFO -   - Storage: 27 controls from domains: Data Protection, Network Security, Identity Management
2025-06-24 15:53:26,032 - __main__ - INFO -   - KeyVault: 27 controls from domains: Identity Management, Data Protection, Privileged Access
2025-06-24 15:53:26,032 - __main__ - INFO -   - SQL: 27 controls from domains: Data Protection, Identity Management, Network Security
2025-06-24 15:53:26,033 - __main__ - INFO -   - Network: 27 controls from domains: Network Security, Privileged Access, Identity Management
2025-06-24 15:53:26,033 - __main__ - INFO -   - Compute: 27 controls from domains: Identity Management, Network Security, Data Protection
2025-06-24 15:53:26,034 - __main__ - INFO -   - AppService: 27 controls from domains: Identity Management, Network Security, Data Protection
2025-06-24 15:53:26,034 - __main__ - INFO -   - Container: 27 controls from domains: Identity Management, Network Security, Data Protection
2025-06-24 15:53:26,034 - __main__ - INFO -   - CosmosDB: 27 controls from domains: Data Protection, Network Security, Identity Management
2025-06-24 15:53:26,035 - __main__ - INFO -   - Generic: 27 controls from domains: Identity Management, Network Security, Data Protection
2025-06-24 15:53:26,041 - enhanced_resource_control_mappings - INFO - Loading enhanced CSV control data...
2025-06-24 15:53:26,089 - enhanced_resource_control_mappings - INFO - Loaded 9 controls from Identity Management
2025-06-24 15:53:26,183 - enhanced_resource_control_mappings - INFO - Loaded 10 controls from Network Security
2025-06-24 15:53:26,221 - enhanced_resource_control_mappings - INFO - Loaded 8 controls from Data Protection
2025-06-24 15:53:26,221 - enhanced_resource_control_mappings - INFO - Generating comprehensive resource-control mappings...
2025-06-24 15:53:26,221 - enhanced_resource_control_mappings - INFO - 🔒 APPLYING ALL 27 CONTROLS TO EVERY RESOURCE TYPE FOR MAXIMUM SECURITY COVERAGE
2025-06-24 15:53:26,221 - enhanced_resource_control_mappings - INFO - Rationale: Any resource can be compromised through unexpected attack vectors
2025-06-24 15:53:26,221 - enhanced_resource_control_mappings - INFO - 📊 Applying 27 controls to every resource type
2025-06-24 15:53:26,221 - enhanced_resource_control_mappings - INFO - Generated comprehensive mappings for 42 resource categories
2025-06-24 15:53:26,221 - __main__ - INFO - ✅ Enhanced resource-control mapper initialized successfully
2025-06-24 15:53:26,257 - template_loader - INFO - Loaded template configuration from C:\Users\<USER>\REPOS\IaCGuardianGPT\templates\config\template_config.json
2025-06-24 15:53:26,257 - template_loader - WARNING - Enhanced template features not available - using basic functionality
2025-06-24 15:53:26,257 - __main__ - INFO - ✅ Template loader initialized successfully
2025-06-24 15:53:26,258 - __main__ - INFO - 📁 Templates directory: C:\Users\<USER>\REPOS\IaCGuardianGPT\templates
2025-06-24 15:53:26,391 - __main__ - INFO - 📋 Template availability check: {'system_role': True, 'main_analysis': True, 'context_builder': True, 'templates_dir_exists': True}
2025-06-24 15:53:26,391 - __main__ - INFO - ============================================================
2025-06-24 15:53:26,391 - __main__ - INFO - 🎯 TEMPLATE SYSTEM STATUS
2025-06-24 15:53:26,391 - __main__ - INFO - ============================================================
2025-06-24 15:53:26,391 - __main__ - INFO - ✅ Template Loader: AVAILABLE
2025-06-24 15:53:26,395 - __main__ - INFO - 📁 Templates Directory: C:\Users\<USER>\REPOS\IaCGuardianGPT\templates
2025-06-24 15:53:26,396 - __main__ - INFO - 📋 Template Files Status:
2025-06-24 15:53:26,396 - __main__ - INFO -    ✅ system_role: Available
2025-06-24 15:53:26,397 - __main__ - INFO -    ✅ main_analysis: Available
2025-06-24 15:53:26,397 - __main__ - INFO -    ✅ context_builder: Available
2025-06-24 15:53:26,397 - __main__ - INFO -    ✅ templates_dir_exists: Available
2025-06-24 15:53:26,397 - __main__ - INFO - 🎉 ALL EXTERNAL TEMPLATES ARE READY FOR USE
2025-06-24 15:53:26,398 - __main__ - INFO - ============================================================
2025-06-24 15:53:26,399 - __main__ - INFO - Analyzing folder: C:\Users\<USER>\Downloads\drop_BuildEv2Artifacts_main (2)\drop_BuildEv2Artifacts_main\buildout\ev2\RegionAgnostic\Templates
2025-06-24 15:53:26,730 - __main__ - INFO - Matching templates with parameter files...
2025-06-24 15:53:26,730 - __main__ - INFO - 
================================================================================
Starting template-parameter matching process
================================================================================
2025-06-24 15:53:26,730 - __main__ - INFO - Found 6 files to process
2025-06-24 15:53:26,730 - __main__ - INFO -
Categorizing files...
2025-06-24 15:53:26,730 - __main__ - INFO - ✓ Identified ARM template file by schema: Grafana.deploymentParameters.json
2025-06-24 15:53:26,735 - __main__ - INFO - ✓ Identified ARM template file by schema: Grafana.deploymentTemplate.json
2025-06-24 15:53:26,736 - __main__ - INFO - ✓ Identified ARM template file by schema: KustoScripts.parameters.json
2025-06-24 15:53:26,736 - __main__ - INFO - ✓ Identified ARM template file by schema: KustoScripts.template.json
2025-06-24 15:53:26,736 - __main__ - INFO - ✓ Identified ARM template file by schema: roleAssignment.deploymentParameters.json
2025-06-24 15:53:26,738 - __main__ - INFO - ✓ Identified ARM template file by schema: roleAssignment.deploymentTemplate.json
2025-06-24 15:53:26,738 - __main__ - INFO -
Matching files using strategy: strict
2025-06-24 15:53:26,738 - __main__ - INFO - Using strict matching (exact naming conventions)
2025-06-24 15:53:26,739 - __main__ - INFO -
================================================================================
Matching Summary
================================================================================
2025-06-24 15:53:26,740 - __main__ - WARNING - Template 'Grafana.deploymentParameters.json' has no matching parameter files
2025-06-24 15:53:26,741 - __main__ - WARNING - Template 'Grafana.deploymentTemplate.json' has no matching parameter files
2025-06-24 15:53:26,741 - __main__ - WARNING - Template 'KustoScripts.parameters.json' has no matching parameter files
2025-06-24 15:53:26,742 - __main__ - WARNING - Template 'KustoScripts.template.json' has no matching parameter files
2025-06-24 15:53:26,742 - __main__ - WARNING - Template 'roleAssignment.deploymentParameters.json' has no matching parameter files
2025-06-24 15:53:26,743 - __main__ - WARNING - Template 'roleAssignment.deploymentTemplate.json' has no matching parameter files
2025-06-24 15:53:26,765 - __main__ - INFO - 🔍 Starting security analysis using EXTERNAL TEMPLATES
2025-06-24 15:53:26,766 - __main__ - INFO - 📄 Analyzing 6 files
2025-06-24 15:53:26,766 - __main__ - INFO - 🔗 Analyzing template relationships and cross-references...
2025-06-24 15:53:26,769 - __main__ - INFO - ✅ Template relationship analysis complete: 6 templates, 6 dependency relationships
2025-06-24 15:53:26,880 - __main__ - INFO - Determined resource types for Grafana.deploymentParameters.json: ['LogicApps']
2025-06-24 15:53:26,881 - __main__ - INFO - Analyzing expanded file Grafana.deploymentParameters.json for resource types: ['LogicApps']
2025-06-24 15:53:26,881 - __main__ - INFO - 🎯 Enhanced mapper found 27 controls for LogicApps
2025-06-24 15:53:26,881 - __main__ - INFO - 📋 Providing enhanced control IDs to AI: DP-1, DP-2, DP-3, DP-4, DP-5, DP-6, DP-7, DP-8, IM-1, IM-2, IM-3, IM-4, IM-5, IM-6, IM-7, IM-8, IM-9, NS-1, NS-10, NS-2, NS-3, NS-4, NS-5, NS-6, NS-7, NS-8, NS-9
2025-06-24 15:53:26,883 - __main__ - INFO - 🎯 Using EXTERNAL TEMPLATES for security analysis prompt generation
2025-06-24 15:53:26,887 - __main__ - INFO - Loaded 27 controls from CSV files
2025-06-24 15:53:26,889 - template_loader - WARNING - Missing template variable: '\nLine 002'
2025-06-24 15:53:26,889 - __main__ - INFO - ✅ Successfully loaded EXTERNAL TEMPLATES for security analysis
2025-06-24 15:53:26,889 - __main__ - INFO - 📏 External template content length: 57773 characters
2025-06-24 15:53:26,890 - __main__ - INFO - Analyzing Grafana.deploymentParameters.json with Azure OpenAI (attempt 1/3)
2025-06-24 15:53:26,890 - __main__ - INFO - Using standard model parameters for gpt-4.1
2025-06-24 15:53:26,894 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'http://***************/metadata/identity/oauth2/token?api-version=REDACTED&resource=REDACTED'
Request method: 'GET'
Request headers:
    'User-Agent': 'azsdk-python-identity/1.17.1 Python/3.12.10 (Windows-11-10.0.26100-SP0)'
No body was attached to the request
2025-06-24 15:53:31,170 - azure.identity._credentials.chained - INFO - DefaultAzureCredential acquired a token from AzureCliCredential
2025-06-24 15:53:33,912 - httpx - INFO - HTTP Request: POST https://msh2024.openai.azure.com/openai/deployments/gpt-4.1/chat/completions?api-version=2025-01-01-preview "HTTP/1.1 200 OK"
2025-06-24 15:53:33,916 - __main__ - INFO - ✅ AI analysis successful for Grafana.deploymentParameters.json with 0 findings
2025-06-24 15:53:34,047 - __main__ - INFO - Determined resource types for Grafana.deploymentTemplate.json: ['LogicApps']
2025-06-24 15:53:34,047 - __main__ - INFO - Analyzing expanded file Grafana.deploymentTemplate.json for resource types: ['LogicApps']
2025-06-24 15:53:34,047 - __main__ - INFO - 🎯 Enhanced mapper found 27 controls for LogicApps
2025-06-24 15:53:34,047 - __main__ - INFO - 📋 Providing enhanced control IDs to AI: DP-1, DP-2, DP-3, DP-4, DP-5, DP-6, DP-7, DP-8, IM-1, IM-2, IM-3, IM-4, IM-5, IM-6, IM-7, IM-8, IM-9, NS-1, NS-10, NS-2, NS-3, NS-4, NS-5, NS-6, NS-7, NS-8, NS-9
2025-06-24 15:53:34,050 - __main__ - INFO - 🎯 Using EXTERNAL TEMPLATES for security analysis prompt generation
2025-06-24 15:53:34,051 - template_loader - WARNING - Missing template variable: '\nLine 002'
2025-06-24 15:53:34,052 - __main__ - INFO - ✅ Successfully loaded EXTERNAL TEMPLATES for security analysis
2025-06-24 15:53:34,052 - __main__ - INFO - 📏 External template content length: 59052 characters
2025-06-24 15:53:34,053 - __main__ - INFO - ⚠️ Context validation flags for Grafana.deploymentTemplate.json: configuration_heavy
2025-06-24 15:53:34,053 - __main__ - INFO - 🔄 File Grafana.deploymentTemplate.json flagged as configuration_heavy - applying chunked analysis
2025-06-24 15:53:34,054 - __main__ - INFO - 🔄 Starting chunked analysis for configuration-heavy file: Grafana.deploymentTemplate.json
2025-06-24 15:53:34,054 - __main__ - WARNING - Failed to parse ARM template JSON, using generic splitting
2025-06-24 15:53:34,054 - __main__ - INFO - 📄 Split Grafana.deploymentTemplate.json into 1 chunks for analysis
2025-06-24 15:53:34,054 - __main__ - INFO - 🔍 Analyzing chunk 1/1 of Grafana.deploymentTemplate.json
2025-06-24 15:53:34,055 - __main__ - INFO - 🎯 Using EXTERNAL TEMPLATES for security analysis prompt generation
2025-06-24 15:53:34,056 - template_loader - WARNING - Missing template variable: '\nLine 002'
2025-06-24 15:53:34,056 - __main__ - INFO - ✅ Successfully loaded EXTERNAL TEMPLATES for security analysis
2025-06-24 15:53:34,056 - __main__ - INFO - 📏 External template content length: 59052 characters
2025-06-24 15:53:34,056 - __main__ - INFO - Analyzing Grafana.deploymentTemplate.json with Azure OpenAI (attempt 1/3)
2025-06-24 15:53:34,056 - __main__ - INFO - Using standard model parameters for gpt-4.1
2025-06-24 15:53:42,769 - httpx - INFO - HTTP Request: POST https://msh2024.openai.azure.com/openai/deployments/gpt-4.1/chat/completions?api-version=2025-01-01-preview "HTTP/1.1 200 OK"
2025-06-24 15:53:42,776 - __main__ - INFO - ✅ Validated deployment-worthy finding: NS-2 at line 38 (score: 235)
2025-06-24 15:53:42,776 - __main__ - INFO - ✅ Validated deployment-worthy finding: NS-2 at line 27 (score: 145)
2025-06-24 15:53:42,778 - __main__ - INFO - ✅ Validated deployment-worthy finding: NS-1 at line 38 (score: 180)
2025-06-24 15:53:42,779 - __main__ - INFO - ✅ Validated deployment-worthy finding: NS-3 at line 38 (score: 200)
2025-06-24 15:53:42,779 - __main__ - INFO - ✅ Validated deployment-worthy finding: NS-10 at line 27 (score: 120)
2025-06-24 15:53:42,780 - __main__ - INFO - ✅ AI analysis successful for Grafana.deploymentTemplate.json with 5 findings
2025-06-24 15:53:42,780 - __main__ - INFO - ✅ Chunk 1 analysis completed: 5 findings
2025-06-24 15:53:42,781 - __main__ - INFO - ✅ Chunked analysis completed for Grafana.deploymentTemplate.json: 1/1 chunks successful, 5 total findings
2025-06-24 15:53:42,876 - __main__ - INFO - Determined resource types for KustoScripts.parameters.json: ['LogicApps']
2025-06-24 15:53:42,876 - __main__ - INFO - Analyzing expanded file KustoScripts.parameters.json for resource types: ['LogicApps']
2025-06-24 15:53:42,876 - __main__ - INFO - 🎯 Enhanced mapper found 27 controls for LogicApps
2025-06-24 15:53:42,876 - __main__ - INFO - 📋 Providing enhanced control IDs to AI: DP-1, DP-2, DP-3, DP-4, DP-5, DP-6, DP-7, DP-8, IM-1, IM-2, IM-3, IM-4, IM-5, IM-6, IM-7, IM-8, IM-9, NS-1, NS-10, NS-2, NS-3, NS-4, NS-5, NS-6, NS-7, NS-8, NS-9
2025-06-24 15:53:42,891 - __main__ - INFO - 🎯 Using EXTERNAL TEMPLATES for security analysis prompt generation
2025-06-24 15:53:42,891 - template_loader - WARNING - Missing template variable: '\nLine 002'
2025-06-24 15:53:42,892 - __main__ - INFO - ✅ Successfully loaded EXTERNAL TEMPLATES for security analysis
2025-06-24 15:53:42,892 - __main__ - INFO - 📏 External template content length: 57852 characters
2025-06-24 15:53:42,893 - __main__ - INFO - Analyzing KustoScripts.parameters.json with Azure OpenAI (attempt 1/3)
2025-06-24 15:53:42,893 - __main__ - INFO - Using standard model parameters for gpt-4.1
2025-06-24 15:53:44,629 - httpx - INFO - HTTP Request: POST https://msh2024.openai.azure.com/openai/deployments/gpt-4.1/chat/completions?api-version=2025-01-01-preview "HTTP/1.1 200 OK"
2025-06-24 15:53:44,645 - __main__ - INFO - ✅ AI analysis successful for KustoScripts.parameters.json with 0 findings
2025-06-24 15:53:44,931 - __main__ - INFO - Determined resource types for KustoScripts.template.json: ['LogicApps']
2025-06-24 15:53:44,932 - __main__ - INFO - Analyzing expanded file KustoScripts.template.json for resource types: ['LogicApps']
2025-06-24 15:53:44,932 - __main__ - INFO - 🎯 Enhanced mapper found 27 controls for LogicApps
2025-06-24 15:53:44,932 - __main__ - INFO - 📋 Providing enhanced control IDs to AI: DP-1, DP-2, DP-3, DP-4, DP-5, DP-6, DP-7, DP-8, IM-1, IM-2, IM-3, IM-4, IM-5, IM-6, IM-7, IM-8, IM-9, NS-1, NS-10, NS-2, NS-3, NS-4, NS-5, NS-6, NS-7, NS-8, NS-9
2025-06-24 15:53:44,974 - __main__ - INFO - 🎯 Using EXTERNAL TEMPLATES for security analysis prompt generation
2025-06-24 15:53:44,974 - template_loader - WARNING - Missing template variable: '\nLine 002'
2025-06-24 15:53:44,974 - __main__ - INFO - ✅ Successfully loaded EXTERNAL TEMPLATES for security analysis
2025-06-24 15:53:44,978 - __main__ - INFO - 📏 External template content length: 93280 characters
2025-06-24 15:53:44,980 - __main__ - INFO - ⚠️ Context validation flags for KustoScripts.template.json: configuration_heavy, ui_configuration
2025-06-24 15:53:44,980 - __main__ - INFO - 🔄 File KustoScripts.template.json flagged as configuration_heavy - applying chunked analysis
2025-06-24 15:53:44,980 - __main__ - INFO - 🔄 Starting chunked analysis for configuration-heavy file: KustoScripts.template.json
2025-06-24 15:53:44,980 - __main__ - WARNING - Failed to parse ARM template JSON, using generic splitting
2025-06-24 15:53:44,980 - __main__ - INFO - 📄 Split KustoScripts.template.json into 7 chunks for analysis
2025-06-24 15:53:44,980 - __main__ - INFO - 🔍 Analyzing chunk 1/7 of KustoScripts.template.json
2025-06-24 15:53:45,006 - __main__ - INFO - 🎯 Using EXTERNAL TEMPLATES for security analysis prompt generation
2025-06-24 15:53:45,007 - template_loader - ERROR - Error applying template variables: unmatched '{' in format spec
2025-06-24 15:53:45,008 - __main__ - INFO - ✅ Successfully loaded EXTERNAL TEMPLATES for security analysis
2025-06-24 15:53:45,008 - __main__ - INFO - 📏 External template content length: 76706 characters
2025-06-24 15:53:45,008 - __main__ - INFO - Analyzing KustoScripts.template.json with Azure OpenAI (attempt 1/3)
2025-06-24 15:53:45,008 - __main__ - INFO - Using standard model parameters for gpt-4.1
2025-06-24 15:53:46,876 - httpx - INFO - HTTP Request: POST https://msh2024.openai.azure.com/openai/deployments/gpt-4.1/chat/completions?api-version=2025-01-01-preview "HTTP/1.1 200 OK"
2025-06-24 15:53:46,878 - __main__ - INFO - ✅ AI analysis successful for KustoScripts.template.json with 0 findings
2025-06-24 15:53:46,878 - __main__ - INFO - ✅ Chunk 1 analysis completed: 0 findings
2025-06-24 15:53:46,878 - __main__ - INFO - 🔍 Analyzing chunk 2/7 of KustoScripts.template.json
2025-06-24 15:53:46,880 - __main__ - INFO - 🎯 Using EXTERNAL TEMPLATES for security analysis prompt generation
2025-06-24 15:53:46,880 - template_loader - WARNING - Missing template variable: '\nLine 003'
2025-06-24 15:53:46,880 - __main__ - INFO - ✅ Successfully loaded EXTERNAL TEMPLATES for security analysis
2025-06-24 15:53:46,880 - __main__ - INFO - 📏 External template content length: 59977 characters
2025-06-24 15:53:46,880 - __main__ - INFO - Analyzing KustoScripts.template.json with Azure OpenAI (attempt 1/3)
2025-06-24 15:53:46,888 - __main__ - INFO - Using standard model parameters for gpt-4.1
2025-06-24 15:53:58,148 - httpx - INFO - HTTP Request: POST https://msh2024.openai.azure.com/openai/deployments/gpt-4.1/chat/completions?api-version=2025-01-01-preview "HTTP/1.1 200 OK"
2025-06-24 15:53:58,165 - __main__ - INFO - ✅ Validated deployment-worthy finding: DP-1 at line 3 (score: 145)
2025-06-24 15:53:58,166 - __main__ - INFO - ✅ Validated deployment-worthy finding: DP-1 at line 3 (score: 145)
2025-06-24 15:53:58,168 - __main__ - INFO - ✅ Validated deployment-worthy finding: DP-1 at line 3 (score: 145)
2025-06-24 15:53:58,169 - __main__ - INFO - ✅ Validated deployment-worthy finding: DP-1 at line 3 (score: 145)
2025-06-24 15:53:58,172 - __main__ - INFO - ✅ Validated deployment-worthy finding: DP-1 at line 3 (score: 145)
2025-06-24 15:53:58,173 - __main__ - INFO - ✅ Validated deployment-worthy finding: DP-2 at line 5 (score: 205)
2025-06-24 15:53:58,175 - __main__ - INFO - ✅ Validated deployment-worthy finding: DP-2 at line 5 (score: 150)
2025-06-24 15:53:58,176 - __main__ - INFO - ✅ Validated deployment-worthy finding: DP-2 at line 5 (score: 205)
2025-06-24 15:53:58,178 - __main__ - INFO - ✅ Validated deployment-worthy finding: DP-2 at line 5 (score: 150)
2025-06-24 15:53:58,179 - __main__ - INFO - ✅ Validated deployment-worthy finding: DP-2 at line 5 (score: 205)
2025-06-24 15:53:58,181 - __main__ - INFO - ✅ AI analysis successful for KustoScripts.template.json with 10 findings
2025-06-24 15:53:58,182 - __main__ - INFO - ✅ Chunk 2 analysis completed: 10 findings
2025-06-24 15:53:58,182 - __main__ - INFO - 🔍 Analyzing chunk 3/7 of KustoScripts.template.json
2025-06-24 15:53:58,192 - __main__ - INFO - 🎯 Using EXTERNAL TEMPLATES for security analysis prompt generation
2025-06-24 15:53:58,194 - template_loader - WARNING - Missing template variable: '\nLine 003'
2025-06-24 15:53:58,195 - __main__ - INFO - ✅ Successfully loaded EXTERNAL TEMPLATES for security analysis
2025-06-24 15:53:58,195 - __main__ - INFO - 📏 External template content length: 60475 characters
2025-06-24 15:53:58,196 - __main__ - INFO - Analyzing KustoScripts.template.json with Azure OpenAI (attempt 1/3)
2025-06-24 15:53:58,197 - __main__ - INFO - Using standard model parameters for gpt-4.1
2025-06-24 15:54:04,967 - httpx - INFO - HTTP Request: POST https://msh2024.openai.azure.com/openai/deployments/gpt-4.1/chat/completions?api-version=2025-01-01-preview "HTTP/1.1 200 OK"
2025-06-24 15:54:04,970 - __main__ - INFO - ✅ Validated deployment-worthy finding: DP-1 at line 23 (score: 145)
2025-06-24 15:54:04,971 - __main__ - INFO - ✅ Validated deployment-worthy finding: DP-2 at line 23 (score: 205)
2025-06-24 15:54:04,972 - __main__ - INFO - ✅ Validated deployment-worthy finding: DP-4 at line 32 (score: 130)
2025-06-24 15:54:04,973 - __main__ - INFO - ✅ AI analysis successful for KustoScripts.template.json with 3 findings
2025-06-24 15:54:04,973 - __main__ - INFO - ✅ Chunk 3 analysis completed: 3 findings
2025-06-24 15:54:04,974 - __main__ - INFO - 🔍 Analyzing chunk 4/7 of KustoScripts.template.json
2025-06-24 15:54:04,982 - __main__ - INFO - 🎯 Using EXTERNAL TEMPLATES for security analysis prompt generation
2025-06-24 15:54:04,984 - template_loader - ERROR - Error applying template variables: Single '}' encountered in format string
2025-06-24 15:54:04,984 - __main__ - INFO - ✅ Successfully loaded EXTERNAL TEMPLATES for security analysis
2025-06-24 15:54:04,985 - __main__ - INFO - 📏 External template content length: 60773 characters
2025-06-24 15:54:04,986 - __main__ - INFO - Analyzing KustoScripts.template.json with Azure OpenAI (attempt 1/3)
2025-06-24 15:54:04,986 - __main__ - INFO - Using standard model parameters for gpt-4.1
2025-06-24 15:54:07,389 - httpx - INFO - HTTP Request: POST https://msh2024.openai.azure.com/openai/deployments/gpt-4.1/chat/completions?api-version=2025-01-01-preview "HTTP/1.1 200 OK"
2025-06-24 15:54:07,390 - __main__ - INFO - ✅ Validated deployment-worthy finding: DP-2 at line 26 (score: 205)
2025-06-24 15:54:07,390 - __main__ - INFO - ✅ AI analysis successful for KustoScripts.template.json with 1 findings
2025-06-24 15:54:07,390 - __main__ - INFO - ✅ Chunk 4 analysis completed: 1 findings
2025-06-24 15:54:07,390 - __main__ - INFO - 🔍 Analyzing chunk 5/7 of KustoScripts.template.json
2025-06-24 15:54:07,394 - __main__ - INFO - 🎯 Using EXTERNAL TEMPLATES for security analysis prompt generation
2025-06-24 15:54:07,395 - template_loader - ERROR - Error applying template variables: Single '}' encountered in format string
2025-06-24 15:54:07,395 - __main__ - INFO - ✅ Successfully loaded EXTERNAL TEMPLATES for security analysis
2025-06-24 15:54:07,395 - __main__ - INFO - 📏 External template content length: 60426 characters
2025-06-24 15:54:07,395 - __main__ - INFO - Analyzing KustoScripts.template.json with Azure OpenAI (attempt 1/3)
2025-06-24 15:54:07,395 - __main__ - INFO - Using standard model parameters for gpt-4.1
2025-06-24 15:54:13,385 - httpx - INFO - HTTP Request: POST https://msh2024.openai.azure.com/openai/deployments/gpt-4.1/chat/completions?api-version=2025-01-01-preview "HTTP/1.1 200 OK"
2025-06-24 15:54:13,387 - __main__ - INFO - ✅ Validated deployment-worthy finding: DP-1 at line 31 (score: 145)
2025-06-24 15:54:13,387 - __main__ - INFO - ✅ Validated deployment-worthy finding: DP-2 at line 31 (score: 205)
2025-06-24 15:54:13,387 - __main__ - INFO - ✅ Validated deployment-worthy finding: IM-3 at line 18 (score: 135)
2025-06-24 15:54:13,387 - __main__ - INFO - ✅ AI analysis successful for KustoScripts.template.json with 3 findings
2025-06-24 15:54:13,387 - __main__ - INFO - ✅ Chunk 5 analysis completed: 3 findings
2025-06-24 15:54:13,387 - __main__ - INFO - 🔍 Analyzing chunk 6/7 of KustoScripts.template.json
2025-06-24 15:54:13,401 - __main__ - INFO - 🎯 Using EXTERNAL TEMPLATES for security analysis prompt generation
2025-06-24 15:54:13,403 - template_loader - ERROR - Error applying template variables: Single '}' encountered in format string
2025-06-24 15:54:13,404 - __main__ - INFO - ✅ Successfully loaded EXTERNAL TEMPLATES for security analysis
2025-06-24 15:54:13,404 - __main__ - INFO - 📏 External template content length: 61024 characters
2025-06-24 15:54:13,405 - __main__ - INFO - Analyzing KustoScripts.template.json with Azure OpenAI (attempt 1/3)
2025-06-24 15:54:13,406 - __main__ - INFO - Using standard model parameters for gpt-4.1
2025-06-24 15:54:16,155 - httpx - INFO - HTTP Request: POST https://msh2024.openai.azure.com/openai/deployments/gpt-4.1/chat/completions?api-version=2025-01-01-preview "HTTP/1.1 200 OK"
2025-06-24 15:54:16,157 - __main__ - INFO - ✅ Validated deployment-worthy finding: DP-2 at line 41 (score: 150)
2025-06-24 15:54:16,157 - __main__ - INFO - ✅ AI analysis successful for KustoScripts.template.json with 1 findings
2025-06-24 15:54:16,158 - __main__ - INFO - ✅ Chunk 6 analysis completed: 1 findings
2025-06-24 15:54:16,158 - __main__ - INFO - 🔍 Analyzing chunk 7/7 of KustoScripts.template.json
2025-06-24 15:54:16,162 - __main__ - INFO - 🎯 Using EXTERNAL TEMPLATES for security analysis prompt generation
2025-06-24 15:54:16,163 - template_loader - WARNING - Missing template variable: '\nLine 003'
2025-06-24 15:54:16,163 - __main__ - INFO - ✅ Successfully loaded EXTERNAL TEMPLATES for security analysis
2025-06-24 15:54:16,164 - __main__ - INFO - 📏 External template content length: 59308 characters
2025-06-24 15:54:16,164 - __main__ - INFO - Analyzing KustoScripts.template.json with Azure OpenAI (attempt 1/3)
2025-06-24 15:54:16,164 - __main__ - INFO - Using standard model parameters for gpt-4.1
2025-06-24 15:54:18,459 - httpx - INFO - HTTP Request: POST https://msh2024.openai.azure.com/openai/deployments/gpt-4.1/chat/completions?api-version=2025-01-01-preview "HTTP/1.1 200 OK"
2025-06-24 15:54:18,461 - __main__ - INFO - ✅ Validated deployment-worthy finding: DP-2 at line 19 (score: 150)
2025-06-24 15:54:18,462 - __main__ - INFO - ✅ AI analysis successful for KustoScripts.template.json with 1 findings
2025-06-24 15:54:18,462 - __main__ - INFO - ✅ Chunk 7 analysis completed: 1 findings
2025-06-24 15:54:18,463 - __main__ - INFO - ✅ Chunked analysis completed for KustoScripts.template.json: 7/7 chunks successful, 11 total findings
2025-06-24 15:54:18,598 - __main__ - INFO - Determined resource types for roleAssignment.deploymentParameters.json: ['LogicApps']
2025-06-24 15:54:18,598 - __main__ - INFO - Analyzing expanded cross-referenced file roleAssignment.deploymentParameters.json for resource types: ['LogicApps']
2025-06-24 15:54:18,598 - __main__ - INFO - 🎯 Enhanced mapper found 27 controls for LogicApps
2025-06-24 15:54:18,599 - __main__ - INFO - 📋 Providing enhanced control IDs to AI: DP-1, DP-2, DP-3, DP-4, DP-5, DP-6, DP-7, DP-8, IM-1, IM-2, IM-3, IM-4, IM-5, IM-6, IM-7, IM-8, IM-9, NS-1, NS-10, NS-2, NS-3, NS-4, NS-5, NS-6, NS-7, NS-8, NS-9
2025-06-24 15:54:18,600 - __main__ - INFO - 🎯 Using EXTERNAL TEMPLATES for security analysis prompt generation
2025-06-24 15:54:18,601 - template_loader - WARNING - Missing template variable: '\nLine 002'
2025-06-24 15:54:18,602 - __main__ - INFO - ✅ Successfully loaded EXTERNAL TEMPLATES for security analysis
2025-06-24 15:54:18,602 - __main__ - INFO - 📏 External template content length: 58011 characters
2025-06-24 15:54:18,603 - __main__ - INFO - ⚠️ Context validation flags for roleAssignment.deploymentParameters.json: configuration_heavy
2025-06-24 15:54:18,603 - __main__ - INFO - 🔄 File roleAssignment.deploymentParameters.json flagged as configuration_heavy - applying chunked analysis
2025-06-24 15:54:18,603 - __main__ - INFO - 🔄 Starting chunked analysis for configuration-heavy file: roleAssignment.deploymentParameters.json
2025-06-24 15:54:18,603 - __main__ - INFO - 📄 Split roleAssignment.deploymentParameters.json into 1 chunks for analysis
2025-06-24 15:54:18,604 - __main__ - INFO - 🔍 Analyzing chunk 1/1 of roleAssignment.deploymentParameters.json
2025-06-24 15:54:18,604 - __main__ - INFO - 🎯 Using EXTERNAL TEMPLATES for security analysis prompt generation
2025-06-24 15:54:18,606 - template_loader - WARNING - Missing template variable: '\nLine 002'
2025-06-24 15:54:18,606 - __main__ - INFO - ✅ Successfully loaded EXTERNAL TEMPLATES for security analysis
2025-06-24 15:54:18,606 - __main__ - INFO - 📏 External template content length: 57838 characters
2025-06-24 15:54:18,606 - __main__ - INFO - Analyzing roleAssignment.deploymentParameters.json with Azure OpenAI (attempt 1/3)
2025-06-24 15:54:18,607 - __main__ - INFO - Using standard model parameters for gpt-4.1
2025-06-24 15:54:24,010 - httpx - INFO - HTTP Request: POST https://msh2024.openai.azure.com/openai/deployments/gpt-4.1/chat/completions?api-version=2025-01-01-preview "HTTP/1.1 200 OK"
2025-06-24 15:54:24,016 - __main__ - INFO - ✅ Validated deployment-worthy finding: IM-2 at line 6 (score: 180)
2025-06-24 15:54:24,017 - __main__ - INFO - ✅ AI analysis successful for roleAssignment.deploymentParameters.json with 1 findings
2025-06-24 15:54:24,017 - __main__ - INFO - ✅ Chunk 1 analysis completed: 1 findings
2025-06-24 15:54:24,017 - __main__ - INFO - ✅ Chunked analysis completed for roleAssignment.deploymentParameters.json: 1/1 chunks successful, 1 total findings
2025-06-24 15:54:24,018 - __main__ - INFO - Exact ARM type match found: Microsoft.Authorization/roleAssignments -> Identity
2025-06-24 15:54:24,018 - __main__ - INFO - Exact ARM type match found: Microsoft.Authorization/roleDefinitions -> Identity
2025-06-24 15:54:24,019 - __main__ - INFO - Found 1 exact ARM type matches: ['Identity']
2025-06-24 15:54:24,114 - __main__ - INFO - Found ARM resource type: microsoft.authorization/roleassignments -> Identity
2025-06-24 15:54:24,115 - __main__ - INFO - Determined resource types for roleAssignment.deploymentTemplate.json: ['Identity']
2025-06-24 15:54:24,115 - __main__ - INFO - Analyzing expanded file roleAssignment.deploymentTemplate.json for resource types: ['Identity']
2025-06-24 15:54:24,115 - enhanced_resource_control_mappings - INFO - 🔒 Resource category not found for Identity, applying ALL 27 controls for maximum security coverage
2025-06-24 15:54:24,116 - __main__ - INFO - 🎯 Enhanced mapper found 27 controls for Identity
2025-06-24 15:54:24,116 - __main__ - INFO - 📋 Providing enhanced control IDs to AI: DP-1, DP-2, DP-3, DP-4, DP-5, DP-6, DP-7, DP-8, IM-1, IM-2, IM-3, IM-4, IM-5, IM-6, IM-7, IM-8, IM-9, NS-1, NS-10, NS-2, NS-3, NS-4, NS-5, NS-6, NS-7, NS-8, NS-9
2025-06-24 15:54:24,120 - __main__ - INFO - 🎯 Using EXTERNAL TEMPLATES for security analysis prompt generation
2025-06-24 15:54:24,122 - template_loader - WARNING - Missing template variable: '\nLine 002'
2025-06-24 15:54:24,122 - __main__ - INFO - ✅ Successfully loaded EXTERNAL TEMPLATES for security analysis
2025-06-24 15:54:24,123 - __main__ - INFO - 📏 External template content length: 58987 characters
2025-06-24 15:54:24,124 - __main__ - INFO - ⚠️ Context validation flags for roleAssignment.deploymentTemplate.json: configuration_heavy
2025-06-24 15:54:24,125 - __main__ - INFO - 🔄 File roleAssignment.deploymentTemplate.json flagged as configuration_heavy - applying chunked analysis
2025-06-24 15:54:24,125 - __main__ - INFO - 🔄 Starting chunked analysis for configuration-heavy file: roleAssignment.deploymentTemplate.json
2025-06-24 15:54:24,125 - __main__ - WARNING - Failed to parse ARM template JSON, using generic splitting
2025-06-24 15:54:24,125 - __main__ - INFO - 📄 Split roleAssignment.deploymentTemplate.json into 1 chunks for analysis
2025-06-24 15:54:24,126 - __main__ - INFO - 🔍 Analyzing chunk 1/1 of roleAssignment.deploymentTemplate.json
2025-06-24 15:54:24,126 - __main__ - INFO - 🎯 Using EXTERNAL TEMPLATES for security analysis prompt generation
2025-06-24 15:54:24,128 - template_loader - WARNING - Missing template variable: '\nLine 002'
2025-06-24 15:54:24,128 - __main__ - INFO - ✅ Successfully loaded EXTERNAL TEMPLATES for security analysis
2025-06-24 15:54:24,128 - __main__ - INFO - 📏 External template content length: 58987 characters
2025-06-24 15:54:24,128 - __main__ - INFO - Analyzing roleAssignment.deploymentTemplate.json with Azure OpenAI (attempt 1/3)
2025-06-24 15:54:24,129 - __main__ - INFO - Using standard model parameters for gpt-4.1
2025-06-24 15:54:29,664 - httpx - INFO - HTTP Request: POST https://msh2024.openai.azure.com/openai/deployments/gpt-4.1/chat/completions?api-version=2025-01-01-preview "HTTP/1.1 200 OK"
2025-06-24 15:54:29,673 - __main__ - INFO - ✅ Validated deployment-worthy finding: IM-2 at line 11 (score: 315)
2025-06-24 15:54:29,675 - __main__ - INFO - ✅ Validated deployment-worthy finding: IM-6 at line 5 (score: 275)
2025-06-24 15:54:29,676 - __main__ - INFO - ✅ AI analysis successful for roleAssignment.deploymentTemplate.json with 2 findings
2025-06-24 15:54:29,676 - __main__ - INFO - ✅ Chunk 1 analysis completed: 2 findings
2025-06-24 15:54:29,677 - __main__ - INFO - ✅ Chunked analysis completed for roleAssignment.deploymentTemplate.json: 1/1 chunks successful, 2 total findings
2025-06-24 15:54:29,677 - __main__ - INFO - Found 19 total security issues in 6 files
2025-06-24 15:54:29,685 - __main__ - INFO - ✅ Exported 19 findings to enhanced CSV with tooltip links: reports\security_findings\security_findings_20250624_155429.csv
2025-06-24 15:54:29,756 - __main__ - INFO - 🎯 Using EXTERNAL TEMPLATES for HTML report generation
2025-06-24 15:54:30,171 - __main__ - INFO - ✅ Successfully loaded EXTERNAL HTML TEMPLATES
2025-06-24 15:54:30,172 - __main__ - INFO - 📏 External HTML template content length: 509122 characters
2025-06-24 15:54:30,181 - __main__ - INFO - ✅ Exported 19 findings to modern responsive HTML using EXTERNAL TEMPLATES: reports\security_findings\security_findings_20250624_155429.html
2025-06-24 15:54:30,956 - __main__ - INFO - ✅ Exported 19 findings to enhanced HTML with validation metadata and tooltip links: reports\security_findings\security_findings_20250624_155429.html