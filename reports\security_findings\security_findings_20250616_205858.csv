Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
CRITICAL,DP-1,IngestionStorageAccount.Template.json,38,Storage account resource at line 38 does not explicitly enable encryption at rest. The 'encryption' property is missing from the resource definition.,Add the 'encryption' property to the storage account resource at line 38 and configure it to use Microsoft-managed keys or customer-managed keys for encryption at rest.,N/A,AI,Generic
CRITICAL,DP-1,IngestionStorageAccount.Template.json,59,Storage account resource at line 59 does not explicitly enable encryption at rest. The 'encryption' property is missing from the resource definition.,Add the 'encryption' property to the storage account resource at line 59 and configure it to use Microsoft-managed keys or customer-managed keys for encryption at rest.,N/A,AI,Generic
CRITICAL,NS-1,IngestionStorageAccount.Template.json,38,"Storage account resource at line 38 does not restrict network access using network security groups (NSGs), Azure Firewall, or private endpoints. No 'networkAcls' property is defined.",Add the 'networkAcls' property to the storage account resource at line 38 to restrict access to trusted networks or use private endpoints.,N/A,AI,Generic
CRITICAL,NS-1,IngestionStorageAccount.Template.json,59,"Storage account resource at line 59 does not restrict network access using network security groups (NSGs), Azure Firewall, or private endpoints. No 'networkAcls' property is defined.",Add the 'networkAcls' property to the storage account resource at line 59 to restrict access to trusted networks or use private endpoints.,N/A,AI,Generic
CRITICAL,NS-2,IngestionStorageAccount.Template.json,38,"Storage account resource at line 38 does not restrict public network access. The 'networkAcls' property is missing, which may allow public endpoints.",Explicitly configure the 'networkAcls' property at line 38 to deny public network access and allow only trusted subnets or private endpoints.,N/A,AI,Generic
CRITICAL,NS-2,IngestionStorageAccount.Template.json,59,"Storage account resource at line 59 does not restrict public network access. The 'networkAcls' property is missing, which may allow public endpoints.",Explicitly configure the 'networkAcls' property at line 59 to deny public network access and allow only trusted subnets or private endpoints.,N/A,AI,Generic
CRITICAL,NS-3,IngestionStorageAccount.Template.json,38,Storage account resource at line 38 does not implement Network Security Groups (NSGs) to control inbound and outbound traffic. No network restrictions are defined.,"Integrate the storage account at line 38 with a virtual network and apply NSGs to control traffic, or use the 'networkAcls' property to restrict access.",N/A,AI,Generic
CRITICAL,NS-3,IngestionStorageAccount.Template.json,59,Storage account resource at line 59 does not implement Network Security Groups (NSGs) to control inbound and outbound traffic. No network restrictions are defined.,"Integrate the storage account at line 59 with a virtual network and apply NSGs to control traffic, or use the 'networkAcls' property to restrict access.",N/A,AI,Generic
CRITICAL,NS-1,LacpBilling.Template.json,393,"Microsoft.Storage/storageAccounts resource at line 393 does not specify network rules (networkAcls) to restrict access. According to ASB NS-1, storage accounts must be protected using network security groups or Azure Firewall.",Add the 'networkAcls' property to the storage account resource to restrict access to trusted networks and subnets. Consider integrating with private endpoints and disabling all public network access except for required trusted sources.,N/A,AI,Generic
CRITICAL,NS-2,LacpBilling.Template.json,393,"Microsoft.Storage/storageAccounts resource at line 393 does not explicitly restrict public network access. According to ASB NS-2, all public endpoints must be protected to minimize exposure.","Set the 'publicNetworkAccess' property to 'Disabled' in the storage account resource to block all public access, or configure 'networkAcls' to allow only trusted networks.",N/A,AI,Generic
CRITICAL,DP-1,LacpBillingExhaustExport.Template.json,61,The resource 'Microsoft.UsageBilling/accounts/dataExports' at line 61 does not specify encryption at rest for exported data. Absence of explicit encryption settings violates ASB DP-1.,Configure the data export resource to enable encryption at rest. Specify encryption settings or ensure the destination supports and enforces encryption at rest.,N/A,AI,Generic
CRITICAL,DP-2,LacpBillingExhaustExport.Template.json,61,The resource 'Microsoft.UsageBilling/accounts/dataExports' at line 61 does not specify encryption in transit (TLS 1.2+) for data transfers to the ADX cluster. This violates ASB DP-2.,Ensure that all data transfers to and from the ADX cluster use TLS 1.2 or higher. Explicitly configure secure endpoints and protocols for data export.,N/A,AI,Generic
CRITICAL,DP-3,LacpBillingExhaustExport.Template.json,61,"Sensitive connection URIs (adxExhaustUri, adxExhaustDataIngestionUri) are passed as plain parameters at line 61 without Key Vault integration, violating ASB DP-3.",Store sensitive connection URIs in Azure Key Vault and reference them securely in the template using Key Vault references.,N/A,AI,Generic
CRITICAL,NS-1,LacpGeo.Template.json,109,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false, exposing the resource to the public internet without network security controls. (Line 109)","Set 'publicNetworkAccess' to 'Disabled' and enable 'isVirtualNetworkFilterEnabled' with appropriate virtual network rules to restrict access to trusted networks only, as required by ASB NS-1.",N/A,AI,Generic
CRITICAL,NS-2,LacpGeo.Template.json,109,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' exposes a public endpoint ('publicNetworkAccess': 'Enabled'), increasing risk of unauthorized access. (Line 109)","Set 'publicNetworkAccess' to 'Disabled' to remove the public endpoint, or restrict access using virtual network rules, as required by ASB NS-2.",N/A,AI,Generic
CRITICAL,NS-20,LacpGeo.Template.json,109,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' does not implement Network Security Groups (NSGs) or virtual network service endpoints ('isVirtualNetworkFilterEnabled': false, 'virtualNetworkRules': []), leaving the resource unprotected from unrestricted network access. (Line 109)","Enable 'isVirtualNetworkFilterEnabled' and define 'virtualNetworkRules' to restrict access to trusted subnets, and/or use NSGs to control traffic as required by ASB NS-20.",N/A,AI,Generic
CRITICAL,NS-1,LacpGlobal.Template.json,54,"CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' at line 54 has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false, exposing the resource to the public internet without network security groups or Azure Firewall.","Set 'publicNetworkAccess' to 'Disabled' or 'isVirtualNetworkFilterEnabled' to true, and restrict access using network security groups or Azure Firewall.",N/A,AI,Generic
CRITICAL,NS-2,LacpGlobal.Template.json,54,CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' at line 54 exposes a public endpoint by setting 'publicNetworkAccess' to 'Enabled' and not enabling virtual network filters.,Disable public network access by setting 'publicNetworkAccess' to 'Disabled' or enable virtual network filters to restrict access.,N/A,AI,Generic
CRITICAL,NS-3,LacpGlobal.Template.json,54,"CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' at line 54 does not implement Network Security Groups (NSGs) or equivalent controls, as 'isVirtualNetworkFilterEnabled' is false and 'virtualNetworkRules' is empty.",Enable 'isVirtualNetworkFilterEnabled' and define 'virtualNetworkRules' to restrict inbound and outbound traffic using NSGs.,N/A,AI,Generic
CRITICAL,NS-1,LacpGlobal.Template.json,151,"Storage Account resource 'Microsoft.Storage/storageAccounts' at line 151 does not specify network rules, NSGs, or firewall restrictions, potentially exposing the resource to the public internet.","Configure network rules to restrict access, associate the storage account with a virtual network and subnet, and use NSGs or Azure Firewall to control access.",N/A,AI,Generic
CRITICAL,NS-2,LacpGlobal.Template.json,151,"Storage Account resource 'Microsoft.Storage/storageAccounts' at line 151 does not restrict public endpoints, increasing the risk of unauthorized access.",Restrict public access by configuring network rules to allow only trusted networks or use private endpoints.,N/A,AI,Generic
CRITICAL,NS-3,LacpGlobal.Template.json,151,Storage Account resource 'Microsoft.Storage/storageAccounts' at line 151 does not implement Network Security Groups (NSGs) or equivalent network controls.,Associate the storage account with a virtual network and subnet protected by NSGs to control inbound and outbound traffic.,N/A,AI,Generic
CRITICAL,NS-1,LacpGlobal.Template.json,181,"Key Vault resource 'Microsoft.KeyVault/vaults' at line 181 does not specify network ACLs, NSGs, or firewall restrictions, potentially exposing the vault to the public internet.","Configure network ACLs to restrict access to trusted networks, and use NSGs or Azure Firewall to protect the Key Vault.",N/A,AI,Generic
CRITICAL,NS-2,LacpGlobal.Template.json,181,"Key Vault resource 'Microsoft.KeyVault/vaults' at line 181 does not restrict public endpoints, increasing the risk of unauthorized access.",Restrict public access by configuring network ACLs to allow only trusted networks or use private endpoints.,N/A,AI,Generic
CRITICAL,NS-3,LacpGlobal.Template.json,181,Key Vault resource 'Microsoft.KeyVault/vaults' at line 181 does not implement Network Security Groups (NSGs) or equivalent network controls.,Associate the Key Vault with a virtual network and subnet protected by NSGs to control inbound and outbound traffic.,N/A,AI,Generic
CRITICAL,NS-1,LacpRegion.Template.json,1007,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1007 has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false, exposing the database to the public internet without network security group or firewall protection. (ASB NS-1)",Set 'publicNetworkAccess' to 'Disabled' and enable 'isVirtualNetworkFilterEnabled' with appropriate 'virtualNetworkRules' to restrict access to trusted networks only.,N/A,AI,Generic
CRITICAL,NS-20,LacpRegion.Template.json,1007,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1007 exposes a public endpoint ('publicNetworkAccess': 'Enabled') without network restrictions, increasing risk of unauthorized access. (ASB NS-2)",Disable public network access by setting 'publicNetworkAccess' to 'Disabled' and use private endpoints to restrict access.,N/A,AI,Generic
CRITICAL,DP-3,LacpStamp.Parameters-LacpStampResources.json,74,"The parameter 'dasStorageAccountKey' at line 74 contains a storage account key value reference, which is sensitive information. Storing sensitive data such as storage account keys directly in parameters violates ASB DP-3: Manage sensitive information disclosure.",Store sensitive information such as storage account keys in Azure Key Vault and reference them securely in your template using Key Vault references.,N/A,AI,Generic
CRITICAL,NS-20,LacpStamp.Template.json,180,"Microsoft.Network/trafficManagerProfiles resource exposes public DNS endpoints (DasTrafficManagerName) without explicit access restrictions. Public endpoints should be secured to minimize exposure. (""target"": ""[concat(parameters('commonInfraClusterPrefix'),'-', parameters('stampMap')[copyIndex('endpoints', 1)].Index, '-ingress-lacp','.',parameters('regionfullName'),'.cloudapp.azure.com')]"")","Restrict access to the public endpoints by implementing IP restrictions, authentication, or private endpoints for the Traffic Manager profile.",N/A,AI,Generic
CRITICAL,NS-20,LacpStamp.Template.json,217,"Microsoft.Network/trafficManagerProfiles resource exposes public DNS endpoints (trafficManagerName) without explicit access restrictions. Public endpoints should be secured to minimize exposure. (""target"": ""[concat(parameters('commonInfraClusterPrefix'),'-', parameters('stampMap')[copyIndex('endpoints', 1)].Index, '-ingress-lacp','.',parameters('regionfullName'),'.cloudapp.azure.com')]"")","Restrict access to the public endpoints by implementing IP restrictions, authentication, or private endpoints for the Traffic Manager profile.",N/A,AI,Generic
CRITICAL,NS-1,ReadAdxExhaust.Template.json,22,Microsoft.Kusto/clusters resource does not specify any network security groups (NSGs) or Azure Firewall configuration. This exposes the resource to potential unauthorized network access.,Configure the Kusto cluster to use private endpoints or associate it with a virtual network protected by NSGs or Azure Firewall. Update the resource definition to include network settings that restrict access.,N/A,AI,Generic
CRITICAL,NS-2,ReadAdxExhaust.Template.json,22,"Microsoft.Kusto/clusters resource does not restrict public endpoints. By default, the cluster may be accessible from the public internet, increasing exposure risk.",Restrict public network access by enabling private endpoints or setting 'publicNetworkAccess' property to 'Disabled' in the cluster configuration.,N/A,AI,Generic
CRITICAL,NS-3,ReadAdxExhaust.Template.json,22,Microsoft.Kusto/clusters resource is not associated with any Network Security Groups (NSGs) to control inbound and outbound traffic.,Associate the Kusto cluster with a subnet that has NSGs configured to restrict traffic to only required sources and destinations.,N/A,AI,Generic
CRITICAL,NS-10,TrafficManagerEndpoints.Template.json,38,"The resource 'Microsoft.Network/trafficManagerProfiles/externalEndpoints' defines a public endpoint without explicit security controls to minimize exposure, violating ASB NS-10 (Protect public endpoints).","Restrict access to the external endpoint by implementing IP whitelisting, authentication, or by using private endpoints where possible. Review and secure the 'target' property to ensure it is not publicly accessible unless required.",N/A,AI,Generic
HIGH,NS-5,LacpGeo.Template.json,109,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' does not use private endpoints ('publicNetworkAccess': 'Enabled', no private endpoint configuration present), reducing network security. (Line 109)","Configure a private endpoint for the CosmosDB account and set 'publicNetworkAccess' to 'Disabled' to ensure secure, private connectivity as required by ASB NS-5.",N/A,AI,Generic
HIGH,NS-5,LacpGlobal.Template.json,54,"CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' at line 54 does not use private endpoints, increasing exposure risk.",Configure a private endpoint for the CosmosDB account to restrict access to private networks only.,N/A,AI,Generic
HIGH,NS-5,LacpGlobal.Template.json,151,"Storage Account resource 'Microsoft.Storage/storageAccounts' at line 151 does not use private endpoints, increasing exposure risk.",Configure a private endpoint for the storage account to restrict access to private networks only.,N/A,AI,Generic
HIGH,NS-5,LacpGlobal.Template.json,181,"Key Vault resource 'Microsoft.KeyVault/vaults' at line 181 does not use private endpoints, increasing exposure risk.",Configure a private endpoint for the Key Vault to restrict access to private networks only.,N/A,AI,Generic
HIGH,NS-22,LacpRegion.Template.json,1007,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1007 does not configure private endpoints, relying on public access. (ASB NS-5)","Configure a private endpoint for the CosmosDB account to ensure secure, private connectivity from trusted networks.",N/A,AI,Generic
HIGH,NS-23,LacpStamp.Template.json,343,Microsoft.Storage/storageAccounts resource (tipLACPStampStorageAccountName) does not implement private endpoints. Private endpoints are recommended to securely access storage accounts.,Add a Microsoft.Network/privateEndpoints resource for the storage account and configure DNS to use the private endpoint.,N/A,AI,Generic
HIGH,NS-23,LacpStamp.Template.json,370,Microsoft.Storage/storageAccounts resource (variables('storageAccounts')[copyIndex()].name) does not implement private endpoints. Private endpoints are recommended to securely access storage accounts.,Add a Microsoft.Network/privateEndpoints resource for each storage account and configure DNS to use the private endpoint.,N/A,AI,Generic
HIGH,NS-23,LacpStamp.Template.json,410,Microsoft.Storage/storageAccounts resource (variables('shimStorageAccounts')[copyIndex()].name) does not implement private endpoints. Private endpoints are recommended to securely access storage accounts.,Add a Microsoft.Network/privateEndpoints resource for each shim storage account and configure DNS to use the private endpoint.,N/A,AI,Generic
HIGH,NS-23,LacpStamp.Template.json,591,Microsoft.KeyVault/vaults resource (stampKeyVaultName) does not implement private endpoints. Private endpoints are recommended to securely access Key Vaults.,Add a Microsoft.Network/privateEndpoints resource for the Key Vault and configure DNS to use the private endpoint.,N/A,AI,Generic
HIGH,NS-23,LacpStamp.Template.json,670,Microsoft.KeyVault/vaults resource (stampSharedKeyVaultName) does not implement private endpoints. Private endpoints are recommended to securely access Key Vaults.,Add a Microsoft.Network/privateEndpoints resource for the shared Key Vault and configure DNS to use the private endpoint.,N/A,AI,Generic
HIGH,AM-1,RoleAssignment.Template.json,49,"Role assignment at line 49 grants 'Contributor' role to the 'Ev2BuildoutServicePrincipalId' principal. The Contributor role provides broad permissions, which may exceed least privilege requirements.","Review the permissions required by 'Ev2BuildoutServicePrincipalId' and assign a more restrictive, custom role if possible to adhere to least privilege principles as per ASB AM-1.",N/A,AI,Generic
HIGH,IM-7,RoleAssignment.Template.json,49,Role assignment at line 49 grants broad 'Contributor' permissions to an application identity ('Ev2BuildoutServicePrincipalId'). This may violate the principle of limiting application identity permissions.,"Restrict the permissions of application identities by assigning only the necessary roles or using custom roles with limited permissions, in accordance with ASB IM-7.",N/A,AI,Generic
