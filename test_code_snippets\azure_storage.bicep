// Azure Storage Account Configuration
// This file contains intentional security issues for testing code snippets

param storageAccountName string = 'testsecuritystorage'
param location string = resourceGroup().location
param environment string = 'development'
param tags object = {
  Environment: environment
  Purpose: 'Security Testing'
  Owner: 'DevSecOps Team'
}

// Resource Group
resource rg 'Microsoft.Resources/resourceGroups@2021-04-01' = {
  name: 'rg-security-test'
  location: location
  tags: tags
}

// Storage Account with multiple security issues
resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {
  name: storageAccountName
  location: location
  kind: 'StorageV2'
  sku: {
    name: 'Standard_LRS'
  }
  properties: {
    // SECURITY ISSUE #1: Public blob access enabled (Line 29)
    allowBlobPublicAccess: true
    
    // SECURITY ISSUE #2: Weak TLS version (Line 32)
    minimumTlsVersion: 'TLS1_0'
    
    // SECURITY ISSUE #3: HTTP traffic allowed (Line 35)
    supportsHttpsTrafficOnly: false
    
    // Good configuration examples
    encryption: {
      services: {
        blob: {
          enabled: true
          keyType: 'Account'
        }
        file: {
          enabled: true
          keyType: 'Account'
        }
        queue: {
          enabled: true
          keyType: 'Service'
        }
        table: {
          enabled: true
          keyType: 'Service'
        }
      }
      keySource: 'Microsoft.Storage'
      requireInfrastructureEncryption: true
    }
    
    // SECURITY ISSUE #4: Default network access (Line 59)
    networkAcls: {
      defaultAction: 'Allow'
      bypass: 'AzureServices'
      virtualNetworkRules: []
      ipRules: []
    }
    
    // Additional properties
    accessTier: 'Hot'
    allowSharedKeyAccess: true
    isHnsEnabled: false
    isNfsV3Enabled: false
    largeFileSharesState: 'Disabled'
  }
  
  tags: tags
}

// Blob Service Configuration
resource blobService 'Microsoft.Storage/storageAccounts/blobServices@2021-04-01' = {
  parent: storageAccount
  name: 'default'
  properties: {
    cors: {
      corsRules: []
    }
    deleteRetentionPolicy: {
      enabled: false
    }
    isVersioningEnabled: false
    changeFeed: {
      enabled: false
    }
    restorePolicy: {
      enabled: false
    }
    containerDeleteRetentionPolicy: {
      enabled: false
    }
  }
}

// File Service Configuration
resource fileService 'Microsoft.Storage/storageAccounts/fileServices@2021-04-01' = {
  parent: storageAccount
  name: 'default'
  properties: {
    cors: {
      corsRules: []
    }
    shareDeleteRetentionPolicy: {
      enabled: false
    }
  }
}

// Output section
output storageAccountId string = storageAccount.id
output storageAccountName string = storageAccount.name
output primaryEndpoints object = storageAccount.properties.primaryEndpoints
output resourceGroupId string = rg.id