#!/usr/bin/env python3
"""
Quick analysis script for IaC Guardian.
Usage: python analyze.py <folder_path>
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src" / "core"))

def main():
    if len(sys.argv) != 2:
        print("Usage: python analyze.py <folder_path>")
        print("Example: python analyze.py C:\\path\\to\\iac\\files")
        sys.exit(1)

    folder_path_input = sys.argv[1]

    # Convert to absolute path if it's relative
    if not os.path.isabs(folder_path_input):
        folder_path = project_root / folder_path_input
    else:
        folder_path = Path(folder_path_input)

    if not folder_path.exists():
        print(f"❌ Error: Folder '{folder_path}' does not exist")
        sys.exit(1)

    print(f"🔍 Starting IaC Guardian security analysis...")
    print(f"📁 Analyzing folder: {folder_path}")

    try:
        # Change to the src/core directory for imports
        os.chdir(project_root / "src" / "core")
        from security_opt import SecurityPRReviewer

        # Initialize the security reviewer in local mode
        reviewer = SecurityPRReviewer(local_folder=str(folder_path))
        
        # Run the analysis
        print("🚀 Running security analysis...")
        files = reviewer.analyze_folder(folder_path)
        
        if files:
            print(f"📁 Found {len(files)} files to analyze")

            # Use graph-enhanced analysis for better security recommendations
            print("🔗 Building resource dependency graph...")
            findings = reviewer.analyze_files_with_graph(files)

            if findings:
                print(f"📊 Found {len(findings)} security findings with dependency context")

                # Show cascade risk summary
                cascade_risks = [f.get("cascade_risk", "LOW") for f in findings]
                risk_summary = {risk: cascade_risks.count(risk) for risk in set(cascade_risks)}
                print(f"🔥 Cascade risk summary: {risk_summary}")

                # Export the findings to reports
                output_dir = project_root / "reports" / "security_findings"

                print(f"📄 Generating reports with dependency graph...")
                reviewer.export_findings(findings, "both", str(output_dir))
                print(f"✅ Graph-enhanced analysis completed successfully!")
                print(f"📄 Reports with dependency visualization generated in: {output_dir}")
            else:
                print("⚠️ No security findings detected")
        else:
            print("⚠️ No files found for analysis")
            
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
