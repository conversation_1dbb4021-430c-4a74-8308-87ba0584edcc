File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
Parameters\LacpBillingExhaust.Parameters-LacpBillingExhaust.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'usageAccountSystemAssignedIdentityPrincipalId' flows across template boundary,Secure parameter 'usageAccountSystemAssignedIdentityPrincipalId' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyVaultSuffix' flows across template boundary,Secure parameter 'keyVaultSuffix' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'regionalCosmosAccountLocation' flows across template boundary,Secure parameter 'regionalCosmosAccountLocation' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'dasStorageAccountKey' flows across template boundary,Secure parameter 'dasStorageAccountKey' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'globalKeyVaultName' flows across template boundary,Secure parameter 'globalKeyVaultName' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'createSharedKeyVault' flows across template boundary,Secure parameter 'createSharedKeyVault' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Templates\IngestionStorageAccount.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,44.0,"The 'allowBlobPublicAccess' property is explicitly set to false on Line 044, which is correct. However, there is no configuration present for 'networkAcls', 'publicNetworkAccess', or private endpoint settings for the Microsoft.Storage/storageAccounts resources (Lines 037 and 060). Without explicit restriction of public network access or enforcement of private endpoints, these storage accounts may be accessible from public networks, enabling initial access, data exfiltration, and lateral movement by attackers. The blast radius includes all data stored in these accounts and any services integrated with them.","Explicitly set 'publicNetworkAccess' to 'Disabled' and configure 'networkAcls' to restrict access to trusted networks or private endpoints only. Add a 'privateEndpointConnections' block to enforce private access. Example: 'publicNetworkAccess': 'Disabled', and configure 'networkAcls' with appropriate IP rules and virtual network rules. Reference ASB control NS-2 for secure cloud service network controls.",,,,ai_analysis,,Validated
Templates\LacpGeo.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,228.0,"The CosmosDB resource at line 225 has 'publicNetworkAccess' set to 'Enabled', and 'isVirtualNetworkFilterEnabled' is set to 'false'. This configuration allows the CosmosDB account to be accessed from the public internet, exposing it to potential unauthorized access, data exfiltration, and lateral movement by attackers. Attackers can exploit this public endpoint to attempt brute force, credential stuffing, or exploit vulnerabilities, increasing the blast radius to all data in the CosmosDB account.","Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to 'true' in the CosmosDB resource configuration. Additionally, define 'virtualNetworkRules' to restrict access to only trusted VNets and subnets. This will ensure the CosmosDB account is only accessible from private networks, reducing the attack surface and preventing unauthorized public access.",,,,ai_analysis,,Validated
Templates\LacpGeo.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,228.0,"The CosmosDB resource at line 228 has 'isVirtualNetworkFilterEnabled' set to 'false', which disables VNet-based access controls. This allows any IP address to access the CosmosDB account if public network access is enabled, significantly increasing the risk of unauthorized access and data compromise. Attackers can exploit this to access sensitive data or move laterally within the environment.","Set 'isVirtualNetworkFilterEnabled' to 'true' and configure 'virtualNetworkRules' with the required subnets to enforce network-based access controls. This will restrict access to the CosmosDB account to only trusted networks, mitigating the risk of public exposure and unauthorized access.",,,,ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,96.0,"CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' has 'publicNetworkAccess' set to 'Enabled' on line 105, and 'isVirtualNetworkFilterEnabled' is set to false on line 108. This exposes the CosmosDB account to the public internet, allowing attackers to attempt direct access, brute force, or exploit vulnerabilities, significantly increasing the blast radius for data exfiltration and lateral movement.","Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to true in the CosmosDB account properties. Additionally, configure 'virtualNetworkRules' to restrict access to only trusted subnets and deploy a private endpoint for the CosmosDB account to eliminate public exposure.",,,,ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,216.0,"Key Vault resource 'Microsoft.KeyVault/vaults' does not specify any network ACLs or private endpoint configuration on line 216, which means it is accessible from all public networks by default. This enables attackers to attempt unauthorized access to secrets and keys, increasing the risk of credential theft and privilege escalation.","Configure the Key Vault with 'networkAcls' to restrict access to specific trusted virtual networks and IP addresses, and deploy a private endpoint to the Key Vault to disable public network access.",,,,ai_analysis,,Validated
Templates\LacpRegion.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,561.0,"The CosmosDB resource at line 558 has 'publicNetworkAccess' explicitly set to 'Enabled', and 'isVirtualNetworkFilterEnabled' is set to false (line 561). This configuration allows unrestricted public network access to the CosmosDB account, enabling attackers to attempt direct access from the internet. This significantly increases the attack surface for initial access, brute force, and data exfiltration attacks, and exposes all data in the CosmosDB account to potential compromise.","Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to true. Configure 'virtualNetworkRules' to allow only trusted subnets or use Private Endpoints for CosmosDB. This will restrict access to the database account to only approved networks, reducing the risk of unauthorized access. Reference: ASB NS-2 (Secure cloud services with network controls).",,,,ai_analysis,,Validated
Templates\LacpRegion.Template.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,561.0,"The CosmosDB resource at line 561 has 'isVirtualNetworkFilterEnabled' set to false, which disables network-level filtering and allows unencrypted or non-secure connections from any network. This increases the risk of data-in-transit interception and man-in-the-middle attacks, as attackers can connect from untrusted networks without restriction.",Set 'isVirtualNetworkFilterEnabled' to true and configure 'virtualNetworkRules' to restrict access to trusted subnets. Ensure all connections use TLS 1.2 or higher. Reference: ASB DP-3 (Encrypt sensitive data in transit).,,,,ai_analysis,,Validated
Templates\LacpRegion.Template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,561.0,"The CosmosDB resource at line 558 with 'publicNetworkAccess' enabled and no network restrictions ('isVirtualNetworkFilterEnabled': false) lacks monitoring for anomalous access from untrusted networks. This configuration allows attackers to attempt data exfiltration or reconnaissance from anywhere, and without network controls, monitoring for such threats is severely impaired.","Restrict public network access and enable network filtering. Additionally, enable Azure Defender for CosmosDB to monitor for anomalous activities and data exfiltration attempts. Reference: ASB DP-2 (Monitor anomalies and threats targeting sensitive data).",,,,ai_analysis,,Validated
Templates\RoleAssignment.Template.json,IM-2,Identity Management,Protect identity and authentication systems,HIGH,5.0,"The 'roleDefinitionId' property on line 72 assigns the built-in 'Contributor' role to the principal specified by 'Ev2BuildoutServicePrincipalId'. The Contributor role grants broad permissions, including the ability to modify most resources within the subscription. If this service principal is compromised, an attacker could escalate privileges, modify or delete resources, and move laterally across the environment. This increases the blast radius and risk of privilege escalation, violating Azure Security Benchmark IM-2 guidance to restrict privileged roles and require strong authentication for privileged access.","Review the necessity of assigning the Contributor role to the 'Ev2BuildoutServicePrincipalId'. If full Contributor access is not required, assign a more restrictive custom role with only the necessary permissions. Enforce strong authentication (such as Azure AD MFA) for the service principal, and monitor its activity using Azure AD logs and Privileged Identity Management. Limit the scope of the role assignment to the minimum required (resource group or resource level) instead of the entire subscription.",,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 15,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 6,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T13:43:26.058088,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
