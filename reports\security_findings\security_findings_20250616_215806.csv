Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P1-Identity-MEDIUM,Identity Management,MEDIUM,IM-8,Templates\LacpBillingExhaustExport.Template.json,54,The resource 'Microsoft.UsageBilling/accounts/dataExports' at line 54 does not specify the use of managed identities for secure resource-to-resource authentication as recommended by Azure Security Benchmark IM-8.,Enable and configure a managed identity for the data export resource and use it for authentication to downstream services such as Azure Data Explorer.,N/A,AI,Generic
P1-Identity-MEDIUM,Identity Management,MEDIUM,IM-8,Templates\ReadUsageAccount.Template.json,23,The resource 'Microsoft.UsageBilling/accounts' does not explicitly configure a managed identity. ASB IM-8 recommends using managed identities for secure resource-to-resource authentication.,Add the 'identity' property with 'type': 'SystemAssigned' or 'UserAssigned' to the resource definition to enable managed identity.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,Templates\IngestionStorageAccount.Template.json,38,Storage account resource at line 38 does not restrict network access using network security groups (NSGs) or Azure Firewall. Public network access is not explicitly denied.,Restrict network access to the storage account by setting 'networkAcls.defaultAction' to 'Deny' and specifying allowed subnets or private endpoints.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,Templates\IngestionStorageAccount.Template.json,56,Storage account resource at line 56 does not restrict network access using network security groups (NSGs) or Azure Firewall. Public network access is not explicitly denied.,Restrict network access to the storage account by setting 'networkAcls.defaultAction' to 'Deny' and specifying allowed subnets or private endpoints.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,Templates\LacpBilling.Template.json,670,"Microsoft.Storage/storageAccounts resource does not restrict network access using network security groups (NSGs), Azure Firewall, or private endpoints. No 'networkAcls' property is defined, so the storage account may be accessible from all networks.","Restrict network access to the storage account by configuring the 'networkAcls' property to allow only required subnets or private endpoints, and deny public network access.",N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,Templates\LacpGeo.Template.json,70,"Key Vault resource at line 70 does not restrict network access using network security groups (NSGs) or Azure Firewall. No network rules are defined, which may expose the Key Vault to public access.","Configure the Key Vault to use network ACLs to restrict access to trusted networks only. Add 'networkAcls' property with appropriate 'bypass', 'defaultAction', and 'ipRules' or 'virtualNetworkRules' to limit access.",N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,Templates\LacpGlobal.Template.json,70,"CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' at line 70 has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false, exposing the database to the public internet without network security controls (NSG or Firewall).","Set 'publicNetworkAccess' to 'Disabled' or 'isVirtualNetworkFilterEnabled' to true, and configure virtual network rules or firewall to restrict access to the CosmosDB account.",N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,Templates\LacpGlobal.Template.json,180,"Storage Account resource 'Microsoft.Storage/storageAccounts' at line 180 does not specify network rules, NSGs, or firewall settings, leaving it accessible from all networks.","Restrict network access to the storage account by configuring 'networkAcls' with appropriate 'virtualNetworkRules' or 'ipRules', and associate the storage account with an NSG or Azure Firewall.",N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,Templates\LacpGlobal.Template.json,217,Key Vault resource 'Microsoft.KeyVault/vaults' at line 217 does not restrict network access and is accessible from all networks by default.,"Configure 'networkAcls' for the Key Vault to restrict access to specific virtual networks and trusted IP addresses, and consider using private endpoints.",N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,Templates\LacpRegion.Template.json,1007,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1007 has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false, exposing the database to the public internet without network security controls. This violates ASB NS-1: Protect resources using network security groups or Azure Firewall.",Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to true. Add appropriate 'virtualNetworkRules' to restrict access to trusted networks only.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,Templates\LacpStamp.Template.json,1002,Storage accounts (Microsoft.Storage/storageAccounts) are deployed without network security groups (NSGs) or Azure Firewall protection. No network rules or firewall settings are defined to restrict access.,Configure network rules for each storage account to restrict access to trusted networks only. Use NSGs or Azure Firewall to protect storage accounts as per ASB NS-1.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,Templates\ReadAdxExhaust.Template.json,22,"Microsoft.Kusto/clusters resource does not specify any network security group (NSG) or Azure Firewall configuration, leaving the resource potentially exposed to unfiltered network traffic.",Configure the Kusto cluster with a virtual network and associate a network security group (NSG) or protect access using Azure Firewall. Update the resource definition to include 'virtualNetworkConfiguration' and ensure NSG rules restrict access to only required sources.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,Templates\IngestionStorageAccount.Template.json,38,Storage account resource at line 38 does not restrict public endpoints. Public network access is not explicitly disabled.,Set 'networkAcls.defaultAction' to 'Deny' and use private endpoints to prevent public access to the storage account.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,Templates\IngestionStorageAccount.Template.json,56,Storage account resource at line 56 does not restrict public endpoints. Public network access is not explicitly disabled.,Set 'networkAcls.defaultAction' to 'Deny' and use private endpoints to prevent public access to the storage account.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,Templates\LacpBillingExhaust.Template.json,38,"Microsoft.Kusto/clusters resource at line 38 does not restrict public network access or define private endpoints, potentially exposing public endpoints in violation of ASB NS-2.",Set the 'publicNetworkAccess' property to 'Disabled' and configure private endpoints for the Microsoft.Kusto/clusters resource to restrict public access.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,Templates\LacpGeo.Template.json,146,"Cosmos DB account at line 146 has 'publicNetworkAccess' set to 'Enabled', exposing the database account to the public internet.",Set 'publicNetworkAccess' to 'Disabled' in the Cosmos DB account properties to prevent public access. Use private endpoints or virtual network rules for secure access.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,Templates\LacpGlobal.Template.json,70,CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' at line 70 exposes a public endpoint by setting 'publicNetworkAccess' to 'Enabled' and not restricting access with 'virtualNetworkRules' or 'ipRules'.,Disable public network access by setting 'publicNetworkAccess' to 'Disabled' or configure 'virtualNetworkRules' and 'ipRules' to restrict access to trusted networks only.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,Templates\LacpGlobal.Template.json,180,"Storage Account resource 'Microsoft.Storage/storageAccounts' at line 180 is missing network restrictions, exposing a public endpoint.",Configure 'networkAcls' to restrict public access and use private endpoints to limit exposure.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,Templates\LacpGlobal.Template.json,217,"Key Vault resource 'Microsoft.KeyVault/vaults' at line 217 is missing network restrictions, exposing a public endpoint.",Restrict public network access to the Key Vault by configuring 'networkAcls' and enabling private endpoints.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,Templates\ReadAdxExhaust.Template.json,22,"Microsoft.Kusto/clusters resource does not restrict public network access, potentially exposing public endpoints.",Set the 'publicNetworkAccess' property to 'Disabled' or restrict access to trusted networks only. Update the resource definition to include 'publicNetworkAccess': 'Disabled' to prevent public exposure.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-3,Templates\IngestionStorageAccount.Template.json,38,Storage account resource at line 38 does not implement Network Security Groups (NSGs) to control inbound and outbound traffic.,"Associate the storage account with a subnet protected by an NSG, or configure 'networkAcls' to restrict access.",N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-3,Templates\IngestionStorageAccount.Template.json,56,Storage account resource at line 56 does not implement Network Security Groups (NSGs) to control inbound and outbound traffic.,"Associate the storage account with a subnet protected by an NSG, or configure 'networkAcls' to restrict access.",N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-3,Templates\ReadAdxExhaust.Template.json,22,"Microsoft.Kusto/clusters resource is missing Network Security Group (NSG) association, which is required to control inbound and outbound traffic.",Associate the Kusto cluster with a subnet that has an NSG applied. Update the deployment to ensure the cluster is deployed within a secured subnet with appropriate NSG rules.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,Templates\IngestionStorageAccount.Template.json,38,Storage account resource at line 38 does not use private endpoints for secure access.,"Configure a private endpoint for the storage account to ensure secure, private connectivity.",N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,Templates\IngestionStorageAccount.Template.json,56,Storage account resource at line 56 does not use private endpoints for secure access.,"Configure a private endpoint for the storage account to ensure secure, private connectivity.",N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,Templates\LacpBilling.Template.json,670,Microsoft.Storage/storageAccounts resource does not implement private endpoints. No 'privateEndpointConnections' or related configuration is present.,"Add a private endpoint for the storage account to ensure secure, private connectivity from your virtual network.",N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,Templates\LacpBillingExhaust.Template.json,38,"Microsoft.Kusto/clusters resource at line 38 does not implement private endpoints, which is required to securely access resources per ASB NS-5.","Add a Microsoft.Network/privateEndpoints resource and associate it with the Microsoft.Kusto/clusters resource to ensure secure, private connectivity.",N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,Templates\LacpGeo.Template.json,70,Key Vault resource at line 70 does not use private endpoints. No 'networkAcls' or private endpoint configuration is present.,Configure a private endpoint for the Key Vault and restrict access to trusted virtual networks using 'networkAcls'.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,Templates\LacpGeo.Template.json,146,"Cosmos DB account at line 146 does not use private endpoints. 'isVirtualNetworkFilterEnabled' is set to false and 'virtualNetworkRules' is empty, which does not restrict access to private networks.",Enable private endpoints for the Cosmos DB account and set 'isVirtualNetworkFilterEnabled' to true. Add appropriate 'virtualNetworkRules' to restrict access to trusted subnets.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,Templates\ReadAdxExhaust.Template.json,22,"Microsoft.Kusto/clusters resource does not use private endpoints, increasing the risk of unauthorized network access.",Configure a private endpoint for the Kusto cluster by adding a 'privateEndpointConnections' property to the resource definition and ensure all access is routed through the private endpoint.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,Templates\IngestionStorageAccount.Template.json,38,Storage account resource at line 38 does not specify 'encryption' property. Azure Storage Accounts must have encryption at rest explicitly enabled to comply with ASB DP-1.,"Add the 'encryption' property to the storage account resource with 'services', 'keySource', and 'enabled' set to true. Example: ""encryption"": { ""services"": { ""blob"": { ""enabled"": true } }, ""keySource"": ""Microsoft.Storage"" }",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,Templates\IngestionStorageAccount.Template.json,56,Storage account resource at line 56 does not specify 'encryption' property. Azure Storage Accounts must have encryption at rest explicitly enabled to comply with ASB DP-1.,"Add the 'encryption' property to the storage account resource with 'services', 'keySource', and 'enabled' set to true. Example: ""encryption"": { ""services"": { ""blob"": { ""enabled"": true } }, ""keySource"": ""Microsoft.Storage"" }",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,Templates\LacpBillingExhaust.Template.json,38,"Microsoft.Kusto/clusters resource at line 38 does not specify 'encryption' or 'keyVaultProperties', indicating encryption at rest is not explicitly enabled as required by ASB DP-1.","Add the 'encryption' property to the Microsoft.Kusto/clusters resource and configure it to enable encryption at rest. For enhanced security, specify customer-managed keys if required.",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,Templates\LacpBillingExhaustExport.Template.json,54,The resource 'Microsoft.UsageBilling/accounts/dataExports' at line 54 does not specify encryption at rest for exported data. Azure Security Benchmark DP-1 requires all data storage to be encrypted at rest.,Configure the data export destination to use encryption at rest. Ensure that the target storage or database for data exports is encrypted using Azure-managed or customer-managed keys.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,Templates\LacpBillingExhaustExport.Template.json,54,The resource 'Microsoft.UsageBilling/accounts/dataExports' at line 54 does not explicitly enforce encryption in transit (TLS 1.2+) for data transfers to the specified 'adxParameters.kustoUri' and 'dataIngestionUri'. Azure Security Benchmark DP-2 requires all data transfers to use TLS 1.2 or higher.,Ensure that the 'adxParameters.kustoUri' and 'dataIngestionUri' endpoints enforce TLS 1.2 or higher for all data transfers. Update the configuration to use secure (https) endpoints and verify TLS enforcement.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,Parameters\LacpStamp.Parameters-LacpStampResources.json,74,"Parameter 'dasStorageAccountKey' at line 74 references a storage account key directly in the template. Storing sensitive information such as storage account keys in parameters violates ASB DP-3, which requires sensitive data to be stored in Azure Key Vault.",Store the storage account key in Azure Key Vault and reference it securely using a Key Vault reference in the template parameter.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,Templates\LacpBillingExhaustExport.Template.json,54,Sensitive connection information such as 'adxParameters.kustoUri' and 'dataIngestionUri' are provided as parameters and not integrated with Azure Key Vault. Azure Security Benchmark DP-3 requires sensitive information to be stored securely.,Store sensitive connection strings and URIs in Azure Key Vault and reference them securely in the template using Key Vault references.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-6,Templates\IngestionStorageAccount.Template.json,38,Storage account resource at line 38 does not configure Azure Key Vault integration for secrets or keys. Sensitive information should be managed using Azure Key Vault as per ASB DP-6.,Integrate Azure Key Vault for managing secrets and keys. Reference Key Vault secrets in the storage account configuration using the 'keyVaultProperties' property.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-6,Templates\IngestionStorageAccount.Template.json,56,Storage account resource at line 56 does not configure Azure Key Vault integration for secrets or keys. Sensitive information should be managed using Azure Key Vault as per ASB DP-6.,Integrate Azure Key Vault for managing secrets and keys. Reference Key Vault secrets in the storage account configuration using the 'keyVaultProperties' property.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-6,Templates\LacpBillingExhaust.Template.json,38,"Microsoft.Kusto/clusters resource at line 38 does not configure customer-managed keys (CMK) for encryption, which is recommended for sensitive data per ASB DP-6.",Configure the 'keyVaultProperties' property in the Microsoft.Kusto/clusters resource to use customer-managed keys stored in Azure Key Vault.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-6,Templates\LacpGeo.Template.json,146,Cosmos DB account at line 146 does not specify the use of customer-managed keys (CMK) for encryption at rest. Only default encryption is implied.,Configure the Cosmos DB account to use customer-managed keys by adding the 'keyVaultKeyUri' property under 'properties' and referencing a Key Vault key.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-7,Templates\IngestionStorageAccount.Template.json,38,Storage account resource at line 38 does not specify customer-managed keys (CMK) for encryption. Using CMK is recommended for sensitive data as per ASB DP-7.,Configure the storage account to use customer-managed keys by specifying the 'encryption.keySource' as 'Microsoft.Keyvault' and providing the Key Vault key URI.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-7,Templates\IngestionStorageAccount.Template.json,56,Storage account resource at line 56 does not specify customer-managed keys (CMK) for encryption. Using CMK is recommended for sensitive data as per ASB DP-7.,Configure the storage account to use customer-managed keys by specifying the 'encryption.keySource' as 'Microsoft.Keyvault' and providing the Key Vault key URI.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-8,Templates\LacpBilling.Template.json,670,Microsoft.Storage/storageAccounts resource does not specify customer-managed keys (CMK) for encryption at rest. Only platform-managed keys are implied.,Configure the storage account to use customer-managed keys (CMK) for encryption at rest by adding the 'encryption.keySource' property set to 'Microsoft.Keyvault' and specifying the key vault and key URI.,N/A,AI,Generic
P4-Access-HIGH,Access Management,HIGH,AM-1,Templates\RoleAssignment.Template.json,38,"Role assignment at line 38 grants 'Contributor' role to the 'Ev2BuildoutServicePrincipalId' principal. The Contributor role provides broad permissions, which may exceed least privilege requirements.","Review the permissions required by the 'Ev2BuildoutServicePrincipalId' principal and assign a more restrictive, custom role with only the necessary permissions as per ASB AM-1.",N/A,AI,Generic
P6-Other-CRITICAL,Unknown,CRITICAL,NS-15,Templates\TrafficManagerEndpoints.Template.json,38,"The resource 'Microsoft.Network/trafficManagerProfiles/externalEndpoints' at line 38 exposes a public endpoint without explicit security controls. Public endpoints must be protected to minimize exposure, as required by ASB Control NS-15.","Restrict access to the external endpoint by implementing IP whitelisting, authentication, or by using private endpoints where possible. Review and secure all public-facing endpoints in the Traffic Manager profile.",N/A,AI,Generic
P6-Other-CRITICAL,Unknown,CRITICAL,NS-21,Templates\LacpGeo.Template.json,146,Cosmos DB account at line 146 does not implement Network Security Groups (NSGs) or any network-based access controls. 'isVirtualNetworkFilterEnabled' is false and 'virtualNetworkRules' is empty.,Enable 'isVirtualNetworkFilterEnabled' and define 'virtualNetworkRules' to restrict access to the Cosmos DB account from specific subnets protected by NSGs.,N/A,AI,Generic
P6-Other-CRITICAL,Unknown,CRITICAL,NS-21,Templates\LacpRegion.Template.json,1007,CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1007 exposes a public endpoint by setting 'publicNetworkAccess' to 'Enabled'. This increases the risk of unauthorized access and violates ASB NS-2: Protect public endpoints.,Set 'publicNetworkAccess' to 'Disabled' to remove the public endpoint. Use private endpoints for secure access.,N/A,AI,Generic
P6-Other-CRITICAL,Unknown,CRITICAL,NS-21,Templates\LacpStamp.Template.json,1002,"Storage accounts (Microsoft.Storage/storageAccounts) are deployed without private endpoints or network restrictions, exposing public endpoints by default.",Disable public network access and configure private endpoints for all storage accounts to minimize public exposure as per ASB NS-21.,N/A,AI,Generic
P6-Other-CRITICAL,Unknown,CRITICAL,NS-21,Templates\LacpStamp.Template.json,1092,"Key Vaults (Microsoft.KeyVault/vaults) are deployed without private endpoints or network restrictions, exposing public endpoints by default.",Disable public network access and configure private endpoints for all Key Vaults to minimize public exposure as per ASB NS-21.,N/A,AI,Generic
P6-Other-CRITICAL,Unknown,CRITICAL,NS-22,Templates\LacpStamp.Template.json,1092,Key Vaults (Microsoft.KeyVault/vaults) are deployed without network security groups (NSGs) or Azure Firewall protection. No network rules or firewall settings are defined to restrict access.,Configure Key Vault network ACLs to restrict access to trusted networks only. Use NSGs or Azure Firewall to protect Key Vaults as per ASB NS-1 and NS-22.,N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,NS-17,Templates\LacpGlobal.Template.json,70,"CosmosDB resource 'Microsoft.DocumentDB/databaseAccounts' at line 70 does not use private endpoints, increasing the risk of unauthorized network access.",Configure a private endpoint for the CosmosDB account to ensure access is only possible from within your private network.,N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,NS-17,Templates\LacpGlobal.Template.json,180,"Storage Account resource 'Microsoft.Storage/storageAccounts' at line 180 does not use private endpoints, increasing the risk of unauthorized access.","Add a private endpoint for the storage account to ensure secure, private connectivity.",N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,NS-17,Templates\LacpGlobal.Template.json,217,"Key Vault resource 'Microsoft.KeyVault/vaults' at line 217 does not use private endpoints, increasing the risk of unauthorized access.","Add a private endpoint for the Key Vault to ensure secure, private connectivity.",N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,NS-23,Templates\LacpRegion.Template.json,1007,CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1007 does not configure private endpoints and allows public network access. This is a network security weakness per ASB NS-5: Use Private Endpoints.,Configure a private endpoint for the CosmosDB account and set 'publicNetworkAccess' to 'Disabled'.,N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,NS-24,Templates\LacpStamp.Template.json,1002,Storage accounts (Microsoft.Storage/storageAccounts) do not use private endpoints for secure access. No 'privateEndpointConnections' or equivalent configuration is present.,"Add private endpoint configurations to all storage accounts to ensure secure, private connectivity as per ASB NS-24.",N/A,AI,Generic
P6-Other-HIGH,Unknown,HIGH,NS-24,Templates\LacpStamp.Template.json,1092,Key Vaults (Microsoft.KeyVault/vaults) do not use private endpoints for secure access. No 'privateEndpointConnections' or equivalent configuration is present.,"Add private endpoint configurations to all Key Vaults to ensure secure, private connectivity as per ASB NS-24.",N/A,AI,Generic
