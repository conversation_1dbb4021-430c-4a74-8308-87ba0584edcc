#!/usr/bin/env python3
"""
Test script to verify the schema-based file identification fix
"""

import os
import sys
import logging
from pathlib import Path

# Add src/core to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'core'))

from security_opt import SecurityPRReviewer

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_schema_identification():
    """Test that the schema-based identification correctly distinguishes templates from parameters"""
    
    # Create test directory
    test_dir = Path("test_schema_fix")
    test_dir.mkdir(exist_ok=True)
    
    # Create test files with proper schemas
    test_files = {
        # ARM Template (should be identified as template)
        "Grafana.deploymentTemplate.json": '''
{
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
    "contentVersion": "*******",
    "parameters": {
        "grafanaName": {
            "type": "string"
        }
    },
    "resources": [
        {
            "type": "Microsoft.Dashboard/grafana",
            "apiVersion": "2022-08-01",
            "name": "[parameters('grafanaName')]",
            "location": "[resourceGroup().location]"
        }
    ]
}''',
        
        # ARM Parameter File (should be identified as parameter)
        "Grafana.deploymentParameters.json": '''
{
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#",
    "contentVersion": "*******",
    "parameters": {
        "grafanaName": {
            "value": "my-grafana-instance"
        }
    }
}''',
        
        # Another template
        "KustoScripts.template.json": '''
{
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
    "contentVersion": "*******",
    "parameters": {
        "clusterName": {
            "type": "string"
        }
    },
    "resources": [
        {
            "type": "Microsoft.Kusto/clusters",
            "apiVersion": "2022-02-01",
            "name": "[parameters('clusterName')]",
            "location": "[resourceGroup().location]"
        }
    ]
}''',
        
        # Another parameter file
        "KustoScripts.parameters.json": '''
{
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#",
    "contentVersion": "*******",
    "parameters": {
        "clusterName": {
            "value": "my-kusto-cluster"
        }
    }
}''',
    }
    
    # Write test files
    for filename, content in test_files.items():
        (test_dir / filename).write_text(content)
    
    print("🧪 Testing Schema-Based File Identification Fix")
    print("=" * 60)
    
    # Initialize the security reviewer
    reviewer = SecurityPRReviewer(local_folder=str(test_dir))
    
    # Test the file categorization
    print("\n📋 Testing File Categorization:")
    print("-" * 40)
    
    # Analyze the folder to trigger file categorization
    files = []
    for root, _, filenames in os.walk(test_dir):
        for fname in filenames:
            if fname.endswith('.json'):
                fpath = os.path.join(root, fname)
                with open(fpath, "r", encoding="utf-8") as f:
                    content = f.read()
                rel_path = os.path.relpath(fpath, test_dir)
                files.append({"path": rel_path, "content": content})
    
    # Call the template matching function to see the categorization
    templates = reviewer._match_template_parameter_files(files)
    
    print(f"\n📊 Results:")
    print("-" * 40)
    
    # Check results
    template_files = list(templates.keys())
    print(f"Templates identified: {len(template_files)}")
    for template_path in template_files:
        template_name = os.path.basename(template_path)
        param_count = len(templates[template_path]["parameters"])
        param_names = [os.path.basename(p["path"]) for p in templates[template_path]["parameters"]]
        
        print(f"  ✅ Template: {template_name}")
        if param_count > 0:
            print(f"     Parameters: {param_names}")
        else:
            print(f"     Parameters: None")
    
    # Verify expected results
    expected_templates = ["Grafana.deploymentTemplate.json", "KustoScripts.template.json"]
    expected_parameters = ["Grafana.deploymentParameters.json", "KustoScripts.parameters.json"]
    
    print(f"\n🔍 Verification:")
    print("-" * 40)
    
    # Check if all expected templates were identified
    found_templates = [os.path.basename(t) for t in template_files]
    for expected in expected_templates:
        if expected in found_templates:
            print(f"  ✅ Template correctly identified: {expected}")
        else:
            print(f"  ❌ Template missing: {expected}")
    
    # Check if parameter files were NOT identified as templates
    for expected_param in expected_parameters:
        if expected_param in found_templates:
            print(f"  ❌ Parameter file incorrectly identified as template: {expected_param}")
        else:
            print(f"  ✅ Parameter file correctly excluded from templates: {expected_param}")
    
    # Cleanup
    import shutil
    shutil.rmtree(test_dir)
    
    print(f"\n✨ Test completed!")

if __name__ == "__main__":
    test_schema_identification()
