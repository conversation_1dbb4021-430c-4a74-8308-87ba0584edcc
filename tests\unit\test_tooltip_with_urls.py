#!/usr/bin/env python3
"""
Test script to validate HTML tooltip functionality with actual URLs.
Creates sample benchmark data with real URLs to test tooltip extraction and display.
"""

import sys
import json
import tempfile
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from security_opt import SecurityPRReviewer
    print("✅ Successfully imported SecurityPRReviewer")
except ImportError as e:
    print(f"❌ Failed to import SecurityPRReviewer: {e}")
    sys.exit(1)

def create_sample_benchmark_with_urls():
    """Create sample benchmark data with actual URLs for testing."""
    sample_controls = [
        {
            "id": "NS-2",
            "domain": "Network Security",
            "name": "Protect public endpoints",
            "description": "Public endpoints need strict access control.",
            "Azure Guidance": "Use Private Link or service endpoints where possible. Configure network access restrictions to limit exposure.",
            "Implementation and additional context": """Configure private endpoints for Azure services:
https://docs.microsoft.com/en-us/azure/private-link/private-endpoint-overview
Azure Service Endpoints documentation:
https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Network security best practices:
https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices"""
        },
        {
            "id": "DP-2", 
            "domain": "Data Protection",
            "name": "Enable encryption in transit",
            "description": "Data must be protected during transmission.",
            "Azure Guidance": "Use TLS 1.2+ for all data transfers. Enable HTTPS-only access for all web applications and APIs.",
            "Implementation and additional context": """Configure HTTPS and TLS settings:
https://docs.microsoft.com/en-us/azure/app-service/configure-ssl-bindings
Azure Storage encryption in transit:
https://docs.microsoft.com/en-us/azure/storage/common/storage-require-secure-transfer
TLS best practices for Azure:
https://docs.microsoft.com/en-us/azure/security/fundamentals/tls-best-practices"""
        },
        {
            "id": "DP-6",
            "domain": "Data Protection", 
            "name": "Secure Data with Customer-Managed Keys (CMK)",
            "description": "Control your own encryption keys.",
            "Azure Guidance": "Use customer-managed keys for critical data. Store keys in Azure Key Vault with proper access controls.",
            "Implementation and additional context": """Customer-managed keys overview:
https://docs.microsoft.com/en-us/azure/storage/common/customer-managed-keys-overview
Key Vault integration guide:
https://docs.microsoft.com/en-us/azure/key-vault/general/overview
Azure Disk Encryption with CMK:
https://docs.microsoft.com/en-us/azure/virtual-machines/disk-encryption-overview"""
        }
    ]
    return sample_controls

def test_tooltip_extraction_with_urls():
    """Test tooltip extraction with actual URLs."""
    print("\n🔗 Testing Tooltip Extraction with Real URLs")
    print("=" * 60)
    
    try:
        # Create a reviewer instance
        reviewer = SecurityPRReviewer(local_folder=".")
        
        # Create sample benchmark data with URLs
        sample_controls = create_sample_benchmark_with_urls()
        
        # Test link extraction for each control
        for control in sample_controls:
            control_id = control["id"]
            print(f"\n📋 Testing control {control_id}:")
            
            # Manually create the control data structure that would be in benchmark
            control_data = {
                "id": control_id,
                "domain": control["domain"],
                "name": control["name"],
                "Azure Guidance": control["Azure Guidance"],
                "Implementation and additional context": control["Implementation and additional context"]
            }
            
            # Temporarily add this control to the reviewer's benchmark data
            if not hasattr(reviewer, 'benchmark_data') or not reviewer.benchmark_data:
                reviewer.benchmark_data = {"controls": []}
            
            # Add to both the controls list and the raw sheet data
            reviewer.benchmark_data["controls"].append(control_data)
            if "Azure Security Benchmark v3" not in reviewer.benchmark_data:
                reviewer.benchmark_data["Azure Security Benchmark v3"] = []
            reviewer.benchmark_data["Azure Security Benchmark v3"].append({
                "ASB ID": control_id,
                "Azure Guidance": control["Azure Guidance"],
                "Implementation and additional context": control["Implementation and additional context"]
            })
            
            # Test link extraction
            links_info = reviewer._extract_control_links(control_id)
            
            print(f"   📚 Raw links found: {len(links_info.get('raw_links', []))}")
            for i, link in enumerate(links_info.get('raw_links', []), 1):
                print(f"      {i}. {link}")
            
            print(f"   🔗 Formatted links: {links_info.get('formatted_links', 'None')}")
            print(f"   🔵 Azure guidance: {'Yes' if links_info.get('azure_guidance') else 'No'}")
            print(f"   📖 Implementation context: {'Yes' if links_info.get('implementation_context') else 'No'}")
            
            # Test description extraction for first URL
            if links_info.get('raw_links'):
                first_url = links_info['raw_links'][0]
                description = reviewer._extract_link_description(first_url, control["Implementation and additional context"])
                print(f"   🏷️ Generated description for first URL: '{description}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during URL extraction test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_html_generation_with_tooltips():
    """Test HTML generation with tooltip data."""
    print("\n🌐 Testing HTML Generation with Tooltip Data")
    print("=" * 60)
    
    try:
        # Create temporary directory for test
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create sample Bicep file
            bicep_content = """
resource storageAccount 'Microsoft.Storage/storageAccounts@2023-01-01' = {
  name: 'teststorage'
  properties: {
    publicNetworkAccess: 'Enabled'  // Security issue for NS-2
    supportsHttpsTrafficOnly: false  // Security issue for DP-2
  }
}
"""
            bicep_file = temp_path / "test.bicep"
            bicep_file.write_text(bicep_content)
            
            # Initialize reviewer
            reviewer = SecurityPRReviewer(local_folder=str(temp_path))
            
            # Add sample controls with URLs to benchmark data
            sample_controls = create_sample_benchmark_with_urls()
            
            # Ensure benchmark data structure exists
            if not hasattr(reviewer, 'benchmark_data') or not reviewer.benchmark_data:
                reviewer.benchmark_data = {"controls": []}
            
            # Add controls to benchmark data
            for control in sample_controls:
                control_data = {
                    "id": control["id"],
                    "domain": control["domain"],
                    "name": control["name"],
                    "Azure Guidance": control["Azure Guidance"],
                    "Implementation and additional context": control["Implementation and additional context"]
                }
                reviewer.benchmark_data["controls"].append(control_data)
            
            # Add to raw sheet data for link extraction
            if "Azure Security Benchmark v3" not in reviewer.benchmark_data:
                reviewer.benchmark_data["Azure Security Benchmark v3"] = []
            
            for control in sample_controls:
                reviewer.benchmark_data["Azure Security Benchmark v3"].append({
                    "ASB ID": control["id"],
                    "Azure Guidance": control["Azure Guidance"],
                    "Implementation and additional context": control["Implementation and additional context"]
                })
            
            # Create sample findings
            sample_findings = [
                {
                    "file_path": str(bicep_file),
                    "control_id": "NS-2",
                    "severity": "HIGH",
                    "line": 4,
                    "description": "Public network access enabled",
                    "remediation": "Configure private endpoints",
                    "source": "ai_analysis"
                },
                {
                    "file_path": str(bicep_file),
                    "control_id": "DP-2",
                    "severity": "CRITICAL",
                    "line": 5,
                    "description": "HTTPS not enforced",
                    "remediation": "Enable HTTPS-only access",
                    "source": "ai_analysis"
                },
                {
                    "file_path": str(bicep_file),
                    "control_id": "DP-6",
                    "severity": "MEDIUM",
                    "line": 1,
                    "description": "Customer-managed keys not configured",
                    "remediation": "Configure CMK with Key Vault",
                    "source": "ai_analysis"
                }
            ]
            
            # Create output directory
            output_dir = Path("tooltip_test_reports")
            output_dir.mkdir(exist_ok=True)
            
            print(f"📊 Generating HTML report with {len(sample_findings)} findings...")
            
            # Export HTML with tooltip functionality
            reviewer.export_findings(sample_findings, format="html", output_dir=str(output_dir))
            
            # Find generated HTML file
            html_files = list(output_dir.glob("*.html"))
            if html_files:
                html_file = html_files[0]
                print(f"✅ Generated HTML report: {html_file}")
                
                # Check if tooltip data is present in HTML
                with open(html_file, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # Check for tooltip functionality
                has_tooltip_function = 'addTooltipFunctionality' in html_content
                has_tooltip_data = 'window.tooltipLinks' in html_content and 'window.tooltipLinks = {};' not in html_content
                has_tooltip_icons = '📚' in html_content
                has_tooltip_css = '.custom-tooltip' in html_content
                
                print(f"   🔧 Tooltip function present: {'✅' if has_tooltip_function else '❌'}")
                print(f"   📊 Tooltip data populated: {'✅' if has_tooltip_data else '❌'}")
                print(f"   📚 Tooltip icons included: {'✅' if has_tooltip_icons else '❌'}")
                print(f"   🎨 Tooltip CSS styling: {'✅' if has_tooltip_css else '❌'}")
                
                # Extract tooltip data from HTML
                if 'window.tooltipLinks = ' in html_content:
                    start = html_content.find('window.tooltipLinks = ') + len('window.tooltipLinks = ')
                    end = html_content.find('};', start) + 1
                    tooltip_data_str = html_content[start:end]
                    
                    try:
                        tooltip_data = json.loads(tooltip_data_str)
                        print(f"   🔗 Tooltip data for controls: {list(tooltip_data.keys())}")
                        
                        for control_id, data in tooltip_data.items():
                            links_count = len(data.get('raw_links', []))
                            has_guidance = bool(data.get('azure_guidance'))
                            print(f"      {control_id}: {links_count} links, guidance: {'Yes' if has_guidance else 'No'}")
                            
                    except json.JSONDecodeError:
                        print(f"   ⚠️ Could not parse tooltip data")
                
                # Check file size
                file_size = html_file.stat().st_size
                print(f"   📏 HTML file size: {file_size:,} bytes ({file_size/1024:.1f} KB)")
                
                return True
            else:
                print("❌ No HTML file generated")
                return False
                
    except Exception as e:
        print(f"❌ Error during HTML generation test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run tooltip validation tests."""
    print("🧪 TOOLTIP FUNCTIONALITY VALIDATION WITH URLS")
    print("=" * 70)
    
    tests = [
        ("URL Extraction", test_tooltip_extraction_with_urls),
        ("HTML Generation with Tooltips", test_html_generation_with_tooltips)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running {test_name} Test...")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"❌ {test_name} test ERROR: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 VALIDATION SUMMARY")
    print("=" * 70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tooltip validation tests PASSED!")
        print("\n💡 Tooltip functionality is working correctly with URLs!")
        return True
    else:
        print("⚠️ Some tests failed. Check implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
