#!/usr/bin/env python3
"""
HTML Report Validation Script
Validates HTML security reports for common issues including line number visibility,
CSS styling, JavaScript functionality, and overall report quality.
"""

import os
import re
import json
from pathlib import Path
from bs4 import BeautifulSoup
import argparse
from datetime import datetime

class HTMLReportValidator:
    def __init__(self):
        self.validation_results = {
            'line_number_issues': [],
            'css_issues': [],
            'javascript_issues': [],
            'accessibility_issues': [],
            'performance_issues': [],
            'general_issues': []
        }
    
    def validate_line_numbers(self, soup, file_path):
        """Validate line number visibility and styling"""
        issues = []
        
        # Check for line-numbers CSS class
        line_numbers_css = soup.find('style')
        if line_numbers_css:
            css_content = line_numbers_css.get_text()
            
            # Check if line-numbers class exists
            if '.line-numbers' not in css_content:
                issues.append("Missing .line-numbers CSS class")
            else:
                # Check for visibility issues
                line_numbers_match = re.search(r'\.line-numbers\s*\{([^}]+)\}', css_content, re.DOTALL)
                if line_numbers_match:
                    css_rules = line_numbers_match.group(1)
                    
                    # Check for low contrast colors
                    if 'var(--text-muted)' in css_rules:
                        issues.append("Line numbers using low-contrast --text-muted color")
                    
                    # Check for insufficient background opacity
                    if re.search(r'background:.*0\.[1-5]\)', css_rules):
                        issues.append("Line numbers background has low opacity (may be hard to see)")
                    
                    # Check for proper font weight
                    if 'font-weight: 500' in css_rules or 'font-weight: 400' in css_rules:
                        issues.append("Line numbers font weight too light (should be 600+)")
        
        # Check for line number HTML elements
        line_number_divs = soup.find_all('div', {'id': 'line-numbers'})
        if not line_number_divs:
            issues.append("Missing line-numbers div element")
        
        return issues
    
    def validate_css_styling(self, soup, file_path):
        """Validate CSS styling and Glass UI implementation"""
        issues = []
        
        style_tags = soup.find_all('style')
        if not style_tags:
            issues.append("No CSS styles found")
            return issues
        
        css_content = ''.join([tag.get_text() for tag in style_tags])
        
        # Check for Glass UI variables
        required_vars = [
            '--hue-primary', '--hue-secondary', '--text-primary', 
            '--text-muted', '--glass-border', '--primary500'
        ]
        
        for var in required_vars:
            if var not in css_content:
                issues.append(f"Missing CSS variable: {var}")
        
        # Check for responsive design
        if '@media' not in css_content:
            issues.append("No responsive design media queries found")
        
        # Check for backdrop-filter support
        if 'backdrop-filter' not in css_content:
            issues.append("Missing backdrop-filter for glass effects")
        
        return issues
    
    def validate_javascript(self, soup, file_path):
        """Validate JavaScript functionality"""
        issues = []
        
        script_tags = soup.find_all('script')
        if not script_tags:
            issues.append("No JavaScript found")
            return issues
        
        js_content = ''.join([tag.get_text() for tag in script_tags])
        
        # Check for essential functions
        required_functions = [
            'displayCodeWithLineNumbers',
            'initLineNumberHighlighting',
            'showCodeDialog',
            'applyFilters'
        ]
        
        for func in required_functions:
            if func not in js_content:
                issues.append(f"Missing JavaScript function: {func}")
        
        # Check for error handling
        if 'try {' not in js_content and 'catch' not in js_content:
            issues.append("No error handling found in JavaScript")
        
        return issues
    
    def validate_accessibility(self, soup, file_path):
        """Validate accessibility features"""
        issues = []
        
        # Check for alt attributes on images
        images = soup.find_all('img')
        for img in images:
            if not img.get('alt'):
                issues.append("Image missing alt attribute")
        
        # Check for proper heading hierarchy
        headings = soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
        if not headings:
            issues.append("No heading elements found")
        
        # Check for ARIA labels on interactive elements
        buttons = soup.find_all('button')
        for button in buttons:
            if not button.get('aria-label') and not button.get_text().strip():
                issues.append("Button missing aria-label or text content")
        
        return issues
    
    def validate_performance(self, soup, file_path):
        """Validate performance-related issues"""
        issues = []
        
        # Check file size
        file_size = os.path.getsize(file_path)
        if file_size > 5 * 1024 * 1024:  # 5MB
            issues.append(f"Large file size: {file_size / 1024 / 1024:.1f}MB")
        
        # Check for inline styles (should be minimal)
        inline_styles = soup.find_all(attrs={'style': True})
        if len(inline_styles) > 10:
            issues.append(f"Too many inline styles: {len(inline_styles)}")
        
        # Check for external resources
        external_links = soup.find_all('link', {'href': re.compile(r'^https?://')})
        external_scripts = soup.find_all('script', {'src': re.compile(r'^https?://')})
        
        if external_links or external_scripts:
            issues.append("External resources found (may affect loading)")
        
        return issues
    
    def validate_general_structure(self, soup, file_path):
        """Validate general HTML structure"""
        issues = []
        
        # Check for DOCTYPE
        if not str(soup).startswith('<!DOCTYPE html>'):
            issues.append("Missing DOCTYPE declaration")
        
        # Check for meta viewport
        viewport_meta = soup.find('meta', {'name': 'viewport'})
        if not viewport_meta:
            issues.append("Missing viewport meta tag")
        
        # Check for title
        title = soup.find('title')
        if not title or not title.get_text().strip():
            issues.append("Missing or empty title tag")
        
        # Check for main content structure
        main_content = soup.find('main') or soup.find(class_=re.compile(r'main|content'))
        if not main_content:
            issues.append("No main content area identified")
        
        return issues
    
    def validate_file(self, file_path):
        """Validate a single HTML file"""
        print(f"\n🔍 Validating: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            soup = BeautifulSoup(content, 'html.parser')
            
            # Run all validations
            self.validation_results['line_number_issues'] = self.validate_line_numbers(soup, file_path)
            self.validation_results['css_issues'] = self.validate_css_styling(soup, file_path)
            self.validation_results['javascript_issues'] = self.validate_javascript(soup, file_path)
            self.validation_results['accessibility_issues'] = self.validate_accessibility(soup, file_path)
            self.validation_results['performance_issues'] = self.validate_performance(soup, file_path)
            self.validation_results['general_issues'] = self.validate_general_structure(soup, file_path)
            
            return True
            
        except Exception as e:
            print(f"❌ Error validating {file_path}: {str(e)}")
            return False
    
    def print_results(self, file_path):
        """Print validation results"""
        print(f"\n📊 Validation Results for {os.path.basename(file_path)}")
        print("=" * 60)
        
        total_issues = 0
        
        for category, issues in self.validation_results.items():
            if issues:
                category_name = category.replace('_', ' ').title()
                print(f"\n🔸 {category_name}:")
                for issue in issues:
                    print(f"  • {issue}")
                total_issues += len(issues)
        
        if total_issues == 0:
            print("\n✅ No issues found! Report looks good.")
        else:
            print(f"\n⚠️  Total issues found: {total_issues}")
        
        return total_issues

def main():
    parser = argparse.ArgumentParser(description='Validate HTML security reports')
    parser.add_argument('path', help='Path to HTML file or directory')
    parser.add_argument('--latest', action='store_true', help='Validate only the latest file')
    
    args = parser.parse_args()
    
    validator = HTMLReportValidator()
    
    if os.path.isfile(args.path):
        # Single file validation
        if validator.validate_file(args.path):
            validator.print_results(args.path)
    
    elif os.path.isdir(args.path):
        # Directory validation
        html_files = list(Path(args.path).glob('*.html'))
        
        if not html_files:
            print(f"No HTML files found in {args.path}")
            return
        
        if args.latest:
            # Sort by modification time and get the latest
            html_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            html_files = [html_files[0]]
        
        total_files = len(html_files)
        successful_validations = 0
        
        for html_file in html_files:
            if validator.validate_file(str(html_file)):
                validator.print_results(str(html_file))
                successful_validations += 1
        
        print(f"\n📈 Summary: {successful_validations}/{total_files} files validated successfully")
    
    else:
        print(f"Path not found: {args.path}")

if __name__ == "__main__":
    main()
