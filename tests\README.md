# IaC Guardian Template System Testing Framework

This directory contains comprehensive tests for the IaC Guardian template system, including validation, loading, performance monitoring, versioning, and integration testing.

## 🧪 Test Structure

### Test Categories

1. **Template Validation Tests** (`TestTemplateValidator`)
   - HTML template syntax validation
   - CSS template validation
   - JavaScript template validation
   - Security pattern detection
   - Variable placeholder validation

2. **Template Loading Tests** (`TestTemplateLoader`)
   - Template file loading and caching
   - Variable substitution
   - Error handling for missing templates
   - Cache performance

3. **Performance Monitoring Tests** (`TestTemplateMetrics`)
   - Metrics collection and recording
   - Performance timer functionality
   - Slow operation detection
   - Cache statistics

4. **Version Management Tests** (`TestTemplateVersioning`)
   - Version parsing and comparison
   - Compatibility checking
   - Version extraction from templates
   - Version header management

5. **Integration Tests** (`TestTemplateIntegration`)
   - Complete HTML report generation
   - Security analysis prompt generation
   - System status reporting
   - End-to-end template workflows

## 🚀 Running Tests

### Run All Tests
```bash
python tests/run_template_tests.py
```

### Run Specific Test Category
```bash
python tests/run_template_tests.py --category validator
python tests/run_template_tests.py --category loader
python tests/run_template_tests.py --category metrics
python tests/run_template_tests.py --category versioning
python tests/run_template_tests.py --category integration
```

### Check Test Environment
```bash
python tests/run_template_tests.py --check-env
```

### Run Individual Test File
```bash
python -m pytest tests/test_template_system.py -v
```

## 📊 Test Reports

Test results are automatically saved to the `test_reports/` directory:

- **JSON Reports**: Detailed test results with timestamps
- **Performance Metrics**: Template loading performance data
- **Validation Results**: Template validation summaries

### Sample Test Report Structure
```json
{
  "timestamp": "2024-01-01T00:00:00",
  "summary": {
    "total_tests": 45,
    "successful": 43,
    "failures": 1,
    "errors": 1,
    "success_rate": 95.6,
    "duration_seconds": 12.34
  },
  "failures": [...],
  "errors": [...]
}
```

## 🔧 Test Configuration

Test behavior is controlled by `test_config.json`:

### Key Configuration Sections

- **Validation Settings**: Control template validation rules
- **Performance Thresholds**: Set performance expectations
- **Test Data**: Sample templates and variables for testing
- **Expected Results**: Define test success criteria

### Example Configuration
```json
{
  "test_configuration": {
    "template_system": {
      "validation": {
        "enabled": true,
        "strict_mode": false,
        "max_template_size_mb": 5
      },
      "performance": {
        "slow_load_threshold_ms": 100,
        "cache_hit_rate_target": 80
      }
    }
  }
}
```

## 🧩 Test Data

The framework includes comprehensive test data:

### Valid Templates
- **HTML**: Basic, with CSS, with JavaScript
- **CSS**: Basic styles, responsive design, Glass UI
- **JavaScript**: Basic functions, configuration, event handlers
- **Prompts**: System roles, analysis instructions, context builders

### Invalid Templates
- **HTML**: Missing DOCTYPE, unclosed tags, malformed structure
- **CSS**: Mismatched braces, invalid syntax
- **JavaScript**: Syntax errors, security issues

### Template Variables
- **Common**: Title, heading, content, timestamps
- **Security Analysis**: File paths, resource types, control sections
- **Styling**: Colors, fonts, layout parameters

## 🎯 Test Scenarios

### Performance Tests
- Load time measurements for different template sizes
- Cache hit rate validation
- Concurrent loading stress tests

### Validation Tests
- Security pattern detection
- Required HTML element validation
- Template structure verification

### Integration Tests
- Complete HTML report generation workflow
- Security analysis prompt assembly
- Multi-template coordination

## 📈 Performance Benchmarks

### Expected Performance Targets

| Metric | Target | Measurement |
|--------|--------|-------------|
| Small Template Load | < 10ms | Templates < 1KB |
| Medium Template Load | < 50ms | Templates 1-10KB |
| Large Template Load | < 200ms | Templates > 10KB |
| Cache Hit Rate | > 80% | After initial load |
| Memory Usage | < 50MB | During test execution |

### Performance Test Results
```
Template Loading Performance:
✅ Small templates: 5.2ms avg (target: <10ms)
✅ Medium templates: 23.1ms avg (target: <50ms)
✅ Large templates: 145.7ms avg (target: <200ms)
✅ Cache hit rate: 87.3% (target: >80%)
```

## 🔍 Debugging Tests

### Verbose Output
```bash
python tests/run_template_tests.py --verbose
```

### Test Specific Issues
```bash
python -m unittest tests.test_template_system.TestTemplateValidator.test_validate_html_template -v
```

### Environment Issues
If tests fail due to environment issues:

1. Check Python version (requires 3.7+)
2. Verify project structure exists
3. Ensure all required modules are available
4. Check file permissions for test directories

## 🛠️ Adding New Tests

### Test Method Template
```python
def test_new_functionality(self):
    """Test description"""
    # Arrange
    test_data = self._create_test_data()
    
    # Act
    result = self.system_under_test.method(test_data)
    
    # Assert
    self.assertTrue(result.is_valid)
    self.assertEqual(result.expected_value, actual_value)
```

### Test Data Creation
```python
def _create_test_template(self, template_type='html'):
    """Create test template for specific type"""
    templates = {
        'html': '<!DOCTYPE html><html><head><title>{title}</title></head></html>',
        'css': '.test { color: {color}; }',
        'js': 'function test() { return "{message}"; }'
    }
    return templates.get(template_type, '')
```

## 📋 Test Checklist

Before submitting changes, ensure:

- [ ] All existing tests pass
- [ ] New functionality has corresponding tests
- [ ] Test coverage is maintained above 80%
- [ ] Performance benchmarks are met
- [ ] Security validation tests pass
- [ ] Integration tests complete successfully

## 🚨 Common Issues

### Import Errors
```
ImportError: No module named 'src.utils.template_loader'
```
**Solution**: Ensure project root is in Python path

### Template Not Found
```
Template file not found: test.html
```
**Solution**: Check template directory structure and file paths

### Performance Test Failures
```
Template loading too slow: 150ms > 100ms threshold
```
**Solution**: Optimize template loading or adjust performance thresholds

## 📞 Support

For test-related issues:

1. Check the test logs in `test_reports/`
2. Run environment check: `--check-env`
3. Review test configuration in `test_config.json`
4. Examine individual test failures with verbose output

## 🔄 Continuous Integration

The test framework is designed for CI/CD integration:

```yaml
# Example GitHub Actions workflow
- name: Run Template Tests
  run: |
    python tests/run_template_tests.py
    python tests/run_template_tests.py --check-env
```

Exit codes:
- `0`: All tests passed
- `1`: Test failures or environment issues
