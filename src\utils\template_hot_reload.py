"""
Hot Reloading System for IaC Guardian Templates

This module provides hot reloading capabilities for template files during development.
"""

import time
import threading
from pathlib import Path
from typing import Dict, List, Any, Callable, Optional
import logging

logger = logging.getLogger(__name__)

class TemplateHotReloader:
    """Hot reloading system for template files"""
    
    def __init__(self, config: Dict[str, Any], template_loader):
        """
        Initialize hot reloader
        
        Args:
            config: Hot reload configuration
            template_loader: Template loader instance to refresh
        """
        self.config = config
        self.template_loader = template_loader
        self.enabled = config.get('enabled', False)
        self.watch_dirs = [Path(d) for d in config.get('watch_directories', ['templates'])]
        self.watch_extensions = config.get('watch_extensions', ['.html', '.css', '.js', '.txt'])
        self.debounce_ms = config.get('debounce_ms', 500)
        self.auto_reload = config.get('auto_reload_on_change', True)
        
        self._file_timestamps = {}
        self._change_callbacks = []
        self._watcher_thread = None
        self._stop_event = threading.Event()
        self._last_change_time = {}
        
        if self.enabled:
            self._start_polling_watcher()
    
    def add_change_callback(self, callback: Callable[[Path], None]):
        """
        Add callback to be called when templates change
        
        Args:
            callback: Function to call with changed file path
        """
        self._change_callbacks.append(callback)
    
    def _start_polling_watcher(self):
        """Start polling-based file watcher (fallback if watchdog not available)"""
        def poll_files():
            while not self._stop_event.is_set():
                try:
                    self._check_file_changes()
                    time.sleep(1)  # Check every second
                except Exception as e:
                    logger.error(f"Error in file polling: {e}")
                    time.sleep(5)  # Wait longer on error
        
        self._watcher_thread = threading.Thread(target=poll_files, daemon=True)
        self._watcher_thread.start()
        logger.info("Started polling-based hot reload watcher")
    
    def _check_file_changes(self):
        """Check for file changes using timestamps"""
        for watch_dir in self.watch_dirs:
            if not watch_dir.exists():
                continue
            
            for file_path in watch_dir.rglob('*'):
                if not file_path.is_file():
                    continue
                
                if file_path.suffix not in self.watch_extensions:
                    continue
                
                try:
                    current_mtime = file_path.stat().st_mtime
                    file_key = str(file_path)
                    
                    if file_key in self._file_timestamps:
                        if current_mtime > self._file_timestamps[file_key]:
                            self._handle_file_change(file_path)
                    
                    self._file_timestamps[file_key] = current_mtime
                    
                except Exception as e:
                    logger.debug(f"Error checking file {file_path}: {e}")
    
    def _handle_file_change(self, file_path: Path):
        """Handle file change with debouncing"""
        # Debounce rapid changes
        now = time.time()
        file_key = str(file_path)
        last_change = self._last_change_time.get(file_key, 0)
        
        if (now - last_change) * 1000 < self.debounce_ms:
            return
        
        self._last_change_time[file_key] = now
        
        logger.info(f"Template file changed: {file_path}")
        
        # Clear template cache if auto-reload is enabled
        if self.auto_reload and hasattr(self.template_loader, 'clear_cache'):
            self.template_loader.clear_cache()
            logger.info("Template cache cleared due to file change")
        
        # Call registered callbacks
        for callback in self._change_callbacks:
            try:
                callback(file_path)
            except Exception as e:
                logger.error(f"Error in change callback: {e}")
    
    def force_reload(self):
        """Force reload all templates"""
        if hasattr(self.template_loader, 'clear_cache'):
            self.template_loader.clear_cache()
            logger.info("Forced template cache clear")
    
    def stop(self):
        """Stop the hot reloader"""
        if self._stop_event:
            self._stop_event.set()
        
        if self._watcher_thread and self._watcher_thread.is_alive():
            self._watcher_thread.join(timeout=5)
        
        logger.info("Hot reloader stopped")
    
    def get_status(self) -> Dict[str, Any]:
        """Get hot reloader status"""
        return {
            'enabled': self.enabled,
            'watching_directories': [str(d) for d in self.watch_dirs],
            'watch_extensions': self.watch_extensions,
            'tracked_files': len(self._file_timestamps),
            'auto_reload': self.auto_reload,
            'callbacks_registered': len(self._change_callbacks)
        }

class AdvancedHotReloader(TemplateHotReloader):
    """Advanced hot reloader with watchdog support"""
    
    def __init__(self, config: Dict[str, Any], template_loader):
        """Initialize advanced hot reloader with watchdog if available"""
        self._observer = None
        super().__init__(config, template_loader)
        
        if self.enabled:
            self._try_watchdog_watcher()
    
    def _try_watchdog_watcher(self):
        """Try to use watchdog for more efficient file watching"""
        try:
            from watchdog.observers import Observer
            from watchdog.events import FileSystemEventHandler
            
            class TemplateChangeHandler(FileSystemEventHandler):
                def __init__(self, parent):
                    self.parent = parent
                
                def on_modified(self, event):
                    if not event.is_directory:
                        file_path = Path(event.src_path)
                        if file_path.suffix in self.parent.watch_extensions:
                            self.parent._handle_file_change(file_path)
                
                def on_created(self, event):
                    if not event.is_directory:
                        file_path = Path(event.src_path)
                        if file_path.suffix in self.parent.watch_extensions:
                            self.parent._handle_file_change(file_path)
            
            self._observer = Observer()
            handler = TemplateChangeHandler(self)
            
            for watch_dir in self.watch_dirs:
                if watch_dir.exists():
                    self._observer.schedule(handler, str(watch_dir), recursive=True)
                    logger.info(f"Watchdog monitoring: {watch_dir}")
            
            self._observer.start()
            logger.info("Advanced hot reload with watchdog started")
            
            # Stop the polling thread since we have watchdog
            if self._stop_event:
                self._stop_event.set()
            
        except ImportError:
            logger.info("Watchdog not available, using polling-based watcher")
        except Exception as e:
            logger.warning(f"Failed to start watchdog watcher: {e}, falling back to polling")
    
    def stop(self):
        """Stop the advanced hot reloader"""
        if self._observer:
            self._observer.stop()
            self._observer.join()
            logger.info("Watchdog observer stopped")
        
        super().stop()

def create_hot_reloader(config: Dict[str, Any], template_loader) -> TemplateHotReloader:
    """
    Factory function to create appropriate hot reloader
    
    Args:
        config: Hot reload configuration
        template_loader: Template loader instance
        
    Returns:
        Hot reloader instance
    """
    try:
        import watchdog
        return AdvancedHotReloader(config, template_loader)
    except ImportError:
        return TemplateHotReloader(config, template_loader)
