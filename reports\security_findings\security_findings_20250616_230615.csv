Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P1-Identity-HIGH,Identity Management,HIGH,IM-1,IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json,1,Azure Active Directory (Azure AD) is not referenced for identity management of compute resources (IM-1).,Integrate compute resources with Azure Active Directory for secure identity and access management.,N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-1,missing_controls.json,136,IM-1: Missing control for using Azure Active Directory for identity management. The template summary indicates IM-1 is not implemented.,Integrate all identity and access management with Azure Active Directory. Use Azure AD for authentication and authorization of users and applications.,N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-1,security_findings.json,74,"Authentication is not enforced for all users. The 'requireAuthentication' property is set to false, which means unauthenticated access is allowed to the App Service. This violates the requirement to leverage Azure AD for secure identity and access management.",Set 'requireAuthentication' to true in the 'globalValidation' section to enforce authentication for all users accessing the App Service.,N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-2,IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json,1,Multi-Factor Authentication (MFA) is not enabled or enforced for users or administrators accessing compute resources (IM-2).,Require and enforce MFA for all users and administrators accessing compute resources.,N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-2,missing_controls.json,150,IM-2: Missing control for enabling Multi-Factor Authentication (MFA). The template summary indicates IM-2 is not implemented.,Enable Multi-Factor Authentication (MFA) for all users and administrators to enhance account security.,N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-3,missing_controls.json,164,IM-3: Missing control for using conditional access policies. The template summary indicates IM-3 is not implemented.,"Implement conditional access policies in Azure AD to enforce secure access based on user, location, device, and risk.",N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-6,IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json,1,Role-Based Access Control (RBAC) is not configured for compute resources (IM-6).,Assign access rights to compute resources using Azure RBAC to ensure least privilege.,N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-6,missing_controls.json,206,IM-6: Missing control for using Role-Based Access Control (RBAC). The template summary indicates IM-6 is not implemented.,Assign access rights using RBAC to ensure users and applications have only the permissions necessary for their roles.,N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-8,IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json,1,"Managed identities are not configured for compute resources, which is required for secure resource-to-resource authentication (IM-8).",Enable and configure managed identities for all compute resources.,N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-8,missing_controls.json,248,IM-8: Missing control for using managed identities for Azure resources. The template summary indicates IM-8 is not implemented.,Enable managed identities for all supported Azure resources to securely handle resource-to-resource authentication without credentials in code.,N/A,AI,Generic
P1-Identity-MEDIUM,Identity Management,MEDIUM,IM-3,AzureSecurityCenter.json,354,"Conditional access policies are not explicitly enforced for Key Vault secrets and keys expiration (lines 354 and 363). Without enforcement, conditional access for secret/key lifecycle is not guaranteed.",Configure 'secretsExpirationSetEffect' and 'keysExpirationSetEffect' to 'Audit' or 'Deny' to support conditional access enforcement for secrets and keys.,N/A,AI,Generic
P1-Identity-MEDIUM,Identity Management,MEDIUM,IM-3,IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json,1,Conditional access policies are not defined or referenced for compute resources (IM-3).,Implement Azure AD conditional access policies to enforce secure access to compute resources.,N/A,AI,Generic
P1-Identity-MEDIUM,Identity Management,MEDIUM,IM-8,AzureSecurityCenter.json,354,"Managed identities are not enforced for App Services and Function Apps (parameters 'webAppUseManagedIdentityMonitoringEffect', 'functionAppUseManagedIdentityMonitoringEffect', and 'apiAppUseManagedIdentityMonitoringEffect' are set to 'Disabled' at lines 354, 354, and 354). This weakens secure resource-to-resource authentication.","Set 'webAppUseManagedIdentityMonitoringEffect', 'functionAppUseManagedIdentityMonitoringEffect', and 'apiAppUseManagedIdentityMonitoringEffect' to 'AuditIfNotExists' to enforce the use of managed identities for App Services and Function Apps.",N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json,1,The template does not reference network security groups (NSGs) or Azure Firewall to protect compute resources (NS-1).,Associate compute resources with NSGs or protect them using Azure Firewall.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,missing_controls.json,1,"NS-1: No evidence of network security groups (NSGs) or Azure Firewall protecting AppService, Container, or LogicApps resources. The template does not specify network security controls.","Configure network security groups (NSGs) or Azure Firewall to protect all sensitive or critical resources, including AppService, Container, and LogicApps.",N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,test_analysis_output.json,13,"The 'networkAcls' block in 'test_keyvault.bicep' at line 13 allows access from all networks, which exposes the Key Vault to unnecessary security risks. ASB Control NS-1 requires protection of sensitive resources using network security groups or Azure Firewall.",Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only trusted networks or private endpoints.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json,1,"The template does not secure public endpoints for compute resources, increasing exposure risk (NS-2).","Restrict or secure all public endpoints for compute resources using NSGs, firewalls, or private endpoints.",N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,missing_controls.json,1,"NS-2: No evidence of protection for public endpoints. The template does not specify secure configuration for public endpoints on AppService, Container, or LogicApps.","Secure all public endpoints by restricting access, using private endpoints, or implementing firewall rules to minimize exposure.",N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,test_analysis_output.json,13,"The 'networkAcls.defaultAction' property in 'test_keyvault.bicep' at line 13 is set to 'Allow', permitting public network access to the Key Vault. This violates ASB Control NS-2, which requires securing all public endpoints to minimize exposure.",Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only trusted networks or private endpoints.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-3,IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json,1,Network Security Groups (NSGs) are not implemented to control traffic to compute resources (NS-3).,Implement NSGs to control inbound and outbound traffic for all compute resources.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-3,missing_controls.json,1,"NS-3: No evidence of Network Security Groups (NSGs) controlling inbound and outbound traffic. The template does not specify NSG configuration for AppService, Container, or LogicApps.","Implement NSGs to control inbound and outbound traffic for all resources, ensuring only approved traffic is allowed.",N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-10,IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json,1,"Azure Bastion is not configured for secure VM access, potentially exposing management ports (NS-10).",Deploy Azure Bastion to securely manage VM access without exposing SSH/RDP ports.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-2,AzureSecurityCenter.json,354,"Public endpoint protection is not enforced for App Services and Function Apps (parameters 'webAppConfigureIPRestrictionsMonitoringEffect', 'functionAppConfigureIPRestrictionsMonitoringEffect', and 'apiAppConfigureIPRestrictionsMonitoringEffect' are set to 'Disabled' at lines 354, 354, and 354). This increases the risk of public exposure.","Set 'webAppConfigureIPRestrictionsMonitoringEffect', 'functionAppConfigureIPRestrictionsMonitoringEffect', and 'apiAppConfigureIPRestrictionsMonitoringEffect' to 'AuditIfNotExists' to enforce IP restrictions and protect public endpoints.",N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json,1,"Private endpoints are not configured for compute resources, which may expose them to public networks (NS-5).",Implement private endpoints for compute resources to restrict access to private networks.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,missing_controls.json,1,"NS-5: No evidence of private endpoints for secure resource access. The template does not specify private endpoint configuration for AppService, Container, or LogicApps.","Implement private endpoints for all supported resources to ensure secure, private connectivity and reduce exposure to the public internet.",N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-7,IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json,1,"Just-in-Time (JIT) VM access is not enabled, increasing the risk of exposed management ports (NS-7).",Enable Just-in-Time VM access to restrict management port exposure for compute resources.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-9,missing_controls.json,1,NS-9: No evidence of network traffic monitoring using Azure Monitor or Network Watcher. The template does not specify monitoring configuration.,"Enable Azure Monitor and Network Watcher to monitor and log network traffic for all resources, ensuring visibility and detection of suspicious activity.",N/A,AI,Generic
P2-Network-MEDIUM,Network Security,MEDIUM,NS-2,security_findings.json,38,"The App Service is not configured with access restrictions (IP restrictions or service endpoints), which means it is potentially accessible from any network, including the public internet. This does not fully secure public endpoints as required.",Configure 'ipSecurityRestrictions' in the App Service configuration to restrict access to only trusted IP addresses or networks.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json,1,The template does not specify encryption at rest for compute resources or associated storage (DP-1).,Configure encryption at rest for all compute resource disks and storage by enabling Azure Disk Encryption or using encrypted managed disks.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,missing_controls.json,80,"DP-1: Missing control for enabling encryption at rest. The template summary indicates DP-1 is not implemented, which is required to protect data at rest.","Enable encryption at rest for all data storage resources, such as databases, storage accounts, and managed disks. Use Azure built-in encryption or customer-managed keys as appropriate.",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json,1,"The template does not enforce encryption in transit (e.g., TLS 1.2+) for data associated with compute resources (DP-2).",Ensure all data in transit to and from compute resources is encrypted using TLS 1.2 or higher.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,missing_controls.json,1,"DP-2: No evidence of encryption in transit (TLS 1.2+) configuration for AppService, Container, or LogicApps resources. The template does not specify secure transport settings.","Configure all AppService, Container, and LogicApps resources to require TLS 1.2 or higher for all data transfers. Update resource settings to enforce secure protocols.",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json,1,"No configuration is present to manage sensitive information disclosure, such as storing secrets in Azure Key Vault (DP-3).","Store all sensitive data, secrets, and credentials in Azure Key Vault and reference them securely in the template.",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,missing_controls.json,1,DP-3: No evidence of sensitive information being stored in Azure Key Vault. The template does not reference secure storage for secrets or keys.,"Store all sensitive data, such as secrets, keys, and certificates, in Azure Key Vault. Update resource configurations to reference Key Vault for secret management.",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,test_analysis_output.json,9,"Soft delete is not enabled for the Key Vault in 'test_keyvault.bicep' at line 9. Without soft delete, deleted secrets, keys, and certificates cannot be recovered, increasing the risk of accidental or malicious data loss. This violates ASB Control DP-3.",Set 'enableSoftDelete' to true to ensure deleted items can be recovered.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,test_analysis_output.json,10,"Purge protection is not enabled for the Key Vault in 'test_keyvault.bicep' at line 10. Without purge protection, a malicious actor could permanently delete (purge) secrets, keys, or certificates, even if soft delete is enabled. This violates ASB Control DP-3.",Set 'enablePurgeProtection' to true to prevent permanent deletion of Key Vault objects.,N/A,AI,Generic
P3-Data-HIGH,Data Protection,HIGH,DP-4,IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json,1,The template does not specify the use of managed disks with encryption for compute resources (DP-4).,Use Azure managed disks with encryption enabled for all compute resources.,N/A,AI,Generic
P3-Data-HIGH,Data Protection,HIGH,DP-5,missing_controls.json,94,"DP-5: Missing control for backup and recovery. The template summary indicates DP-5 is not implemented, which is required for data protection.",Implement backup and recovery strategies for all critical data. Configure regular backups and test recovery procedures to ensure data can be restored in case of loss.,N/A,AI,Generic
P3-Data-HIGH,Data Protection,HIGH,DP-6,IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json,1,Customer-managed keys (CMK) are not configured for encryption of sensitive data in compute resources (DP-6).,Configure compute resources to use customer-managed keys (CMK) for encryption where applicable.,N/A,AI,Generic
P3-Data-HIGH,Data Protection,HIGH,DP-6,missing_controls.json,108,DP-6: Missing control for securing data with customer-managed keys (CMK). The template summary indicates DP-6 is not implemented.,Configure customer-managed keys for all supported resources to control encryption keys and enhance data security.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-1,security_findings.json,38,"The App Service does not have 'clientCertEnabled' set, which means client certificate authentication is not enforced. This weakens transport security for sensitive applications and does not fully enable encryption at rest as required.",Add 'clientCertEnabled': true to the App Service properties if client certificate authentication is required for your scenario.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-6,AzureSecurityCenter.json,354,"Key Vault secrets and keys expiration policies are set to 'Disabled' (lines 354 and 363), which does not enforce expiration for secrets and keys. This reduces the effectiveness of key and secret lifecycle management.",Set 'secretsExpirationSetEffect' and 'keysExpirationSetEffect' parameters to 'Audit' or 'Deny' to enforce expiration dates for Key Vault secrets and keys.,N/A,AI,Generic
P3-Data-LOW,Data Protection,LOW,DP-3,security_findings.json,38,"There is no evidence of HTTPS minimum TLS version enforcement. The App Service should enforce a minimum TLS version (e.g., 1.2) to ensure secure transport and prevent sensitive information disclosure.","Set 'minTlsVersion' property (e.g., '1.2') in the App Service configuration to enforce a secure TLS version.",N/A,AI,Generic
P4-Access-HIGH,Access Management,HIGH,AM-1,IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json,1,"The template does not specify any access controls or permissions for compute resources, violating the least privilege principle (AM-1).",Explicitly define and assign least privilege access roles for all compute resources using Azure RBAC in the template.,N/A,AI,Generic
P4-Access-HIGH,Access Management,HIGH,AM-1,missing_controls.json,10,"AM-1: Missing control for assigning least privilege access. The template summary indicates 80 missing controls, including AM-1, which is required to limit user and application permissions to what is required.","Review and assign least privilege access to all users and applications. Ensure that permissions are limited to only what is necessary for each role, following the principle of least privilege.",N/A,AI,Generic
P4-Access-HIGH,Access Management,HIGH,AM-3,missing_controls.json,38,"AM-3: Missing control for implementing Privileged Identity Management (PIM). The template summary indicates AM-3 is not implemented, which is required to manage and control privileged access.","Enable and configure Azure Privileged Identity Management (PIM) to manage, control, and monitor access to important resources in your environment.",N/A,AI,Generic
P4-Access-HIGH,Access Management,HIGH,AM-5,missing_controls.json,66,"AM-5: Missing control for enabling logging for access management. The template summary indicates AM-5 is not implemented, which is required to monitor access management activities.",Enable logging for all access management activities. Configure Azure AD logs and monitor access changes to detect and respond to unauthorized access.,N/A,AI,Generic
P4-Access-MEDIUM,Access Management,MEDIUM,AM-2,IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json,1,The template does not include any configuration for periodic review of access rights for compute resources (AM-2).,Implement mechanisms or document procedures to regularly review and update access assignments for compute resources.,N/A,AI,Generic
P4-Access-MEDIUM,Access Management,MEDIUM,AM-2,missing_controls.json,24,"AM-2: Missing control for regularly reviewing access rights. The template summary indicates AM-2 is not implemented, which is a security best practice.",Implement periodic access reviews for all users and applications. Schedule regular audits to ensure that only necessary access is retained and revoke unnecessary permissions.,N/A,AI,Generic
P4-Access-MEDIUM,Access Management,MEDIUM,AM-3,IacG\Lib\site-packages\update-0.0.1.dist-info\metadata.json,1,Privileged Identity Management (PIM) is not referenced or configured for compute resources in the template (AM-3).,Integrate Azure Privileged Identity Management (PIM) for managing privileged access to compute resources.,N/A,AI,Generic
P4-Access-MEDIUM,Access Management,MEDIUM,AM-4,missing_controls.json,52,"AM-4: Missing control for using access reviews. The template summary indicates AM-4 is not implemented, which is a security best practice.",Configure and schedule access reviews in Azure AD to ensure that only necessary access is maintained and unnecessary permissions are removed.,N/A,AI,Generic
