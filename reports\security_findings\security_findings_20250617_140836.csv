File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
app-config.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyValues' flows across template boundary,"Secure parameter 'keyValues' and validate its usage across template boundaries

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",,Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'app_insights_key' flows across template boundary,"Secure parameter 'app_insights_key' and validate its usage across template boundaries

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",,Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyvault_name' flows across template boundary,"Secure parameter 'keyvault_name' and validate its usage across template boundaries

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",,Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
keyvault.bicep,NS-1,Network Security,Protect resources using network security groups,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,"Review template dependencies and ensure secure communication between templates

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'secrets' flows across template boundary,"Secure parameter 'secrets' and validate its usage across template boundaries

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",,Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
storage-accounts.bicep,NS-1,Network Security,Protect resources using network security groups,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,"Review template dependencies and ensure secure communication between templates

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,DP-6,Data Protection,Secure Data with Customer-Managed Keys (CMK),HIGH,1.0,Parameter flow security risk: Sensitive parameters may be exposed through template dependencies,"Use Key Vault references for sensitive parameters and validate parameter flow security

🔵 Azure Guidance: Use customer-managed keys for critical data.",,Use customer-managed keys for critical data.,"Configure Key Vault for key management. Rotate keys regularly.

This control is implemented through 3 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,parameter_flow,Validated
app-config.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,7.0,"The App Configuration resource 'Microsoft.AppConfiguration/configurationStores' does not restrict public network access or configure Private Link/service endpoints. By default, this exposes a public endpoint, violating NS-2: Protect public endpoints.","Restrict public network access by setting 'publicNetworkAccess' to 'Disabled' and configure Private Link or service endpoints for the App Configuration resource. Refer to Azure documentation for securing App Configuration endpoints.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
function-settings.bicep,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,38.0,Hardcoded secret detected: 'ado_access_client_id' is assigned a client ID value directly in the template. This may expose sensitive information and violates DP-3 (Manage sensitive information disclosure).,"Remove the hardcoded client ID from the template. Store sensitive values such as client IDs and secrets in Azure Key Vault and reference them securely using Key Vault references in your configuration.

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",,Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
function.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,61.0,"The App Service resource 'function' (Microsoft.Web/sites@2021-03-01) does not restrict public endpoints. By default, App Service exposes a public endpoint unless access restrictions or private endpoints are explicitly configured.","Restrict public access to the App Service by configuring access restrictions to allow only required IPs, or use Private Endpoints or an Application Gateway to control access. Refer to Azure documentation for configuring App Service access restrictions.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
function.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,61.0,The App Service resource 'function' (Microsoft.Web/sites@2021-03-01) does not use Private Endpoints. Private Endpoints reduce the attack surface by eliminating public exposure.,"Configure a Private Endpoint for the App Service to ensure access is only available from within your private network. Refer to Azure documentation for enabling Private Endpoints on App Service.

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",,Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
function.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,48.0,"The Storage Account resource 'funcStorage' (Microsoft.Storage/storageAccounts@2021-08-01) is referenced as 'existing', but there is no evidence of access restrictions or private endpoints. By default, storage accounts allow public network access unless explicitly restricted.","Restrict public access to the Storage Account by configuring network rules to allow only required subnets or IPs, or enable Private Endpoints. Review the storage account's network access settings.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
function.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,48.0,The Storage Account resource 'funcStorage' (Microsoft.Storage/storageAccounts@2021-08-01) does not use Private Endpoints. Private Endpoints are recommended to reduce public exposure.,"Configure a Private Endpoint for the Storage Account to ensure access is only available from within your private network. Refer to Azure documentation for enabling Private Endpoints on Storage Accounts.

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",,Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
hub-network.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,6.0,The 'hub-subnet' subnet in the virtual network 'hub-vnet' (line 6) does not have a Network Security Group (NSG) associated. NSGs are required to segment and protect network resources.,"Associate a Network Security Group (NSG) with the 'hub-subnet' subnet by adding an 'networkSecurityGroup' property referencing an NSG resource. Define the NSG resource in the template and link it to the subnet.

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
hub-network.bicep,NS-3,Network Security,Use Network Security Groups (NSGs),CRITICAL,6.0,No Network Security Group (NSG) is configured for the 'hub-subnet' subnet in the virtual network 'hub-vnet' (line 6). NSGs are required to enforce network-level access controls.,"Create an NSG resource and associate it with the 'hub-subnet' subnet. Define security rules to deny all inbound traffic by default and allow only necessary traffic.

📚 References: [Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules)

🔵 Azure Guidance: Implement NSGs with deny-by-default rules.",[Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules),Implement NSGs with deny-by-default rules.,"Configure NSGs to deny all inbound traffic by default. Allow only necessary traffic.
NSG rules documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works
Security rules best practices: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
instance-config.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,22.0,"The network_config object at line 22 defines an address_space ('10.0.0.0/8') and subnet ('10.0.0.0/16') but does not reference or configure any Network Security Groups (NSGs) to protect the subnet or associated NICs, violating NS-1 requirements for network segmentation and defense in depth.","Define and associate a Network Security Group (NSG) with the subnet and/or NICs in the network_config. Explicitly specify allowed ports and protocols to restrict network access as per Azure Security Benchmark NS-1.

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,23.0,"The 'corpNetIps' array includes broad IP ranges such as '*******/8', '********/8', and others, which may allow excessive public access to resources. This violates NS-2: Protect public endpoints, which requires strict restriction of public access to required IPs only.","Restrict allowed IP ranges to only those that are strictly necessary. Replace broad ranges (e.g., '*******/8') with specific, minimal IP addresses or subnets that require access. Review and update the 'corpNetIps' array to comply with least privilege principles.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
ip-rules.bicep,NS-3,Network Security,Use Network Security Groups (NSGs),CRITICAL,23.0,The template defines IP allow rules but does not configure Network Security Groups (NSGs) to deny all inbound traffic by default and only allow necessary traffic. This violates NS-3: Use Network Security Groups (NSGs).,"Implement NSGs for all relevant subnets and network interfaces. Configure NSG rules to deny all inbound traffic by default and explicitly allow only the required IPs and ports. Reference: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works

📚 References: [Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules)

🔵 Azure Guidance: Implement NSGs with deny-by-default rules.",[Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules),Implement NSGs with deny-by-default rules.,"Configure NSGs to deny all inbound traffic by default. Allow only necessary traffic.
NSG rules documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works
Security rules best practices: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
keyvault.bicep,DP-6,Data Protection,Secure Data with Customer-Managed Keys (CMK),HIGH,17.0,"Key Vault 'networkAcls.defaultAction' is set to 'Allow', which permits public network access. This does not enforce secure access to encryption keys as required by DP-6.","Set 'networkAcls.defaultAction' to 'Deny' to restrict public network access. Only allow trusted networks and services via 'ipRules' and 'virtualNetworkRules'.

🔵 Azure Guidance: Use customer-managed keys for critical data.",,Use customer-managed keys for critical data.,"Configure Key Vault for key management. Rotate keys regularly.

This control is implemented through 3 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
operational-insights.bicep,IM-8,Identity Management,Use Managed Identities for Azure Resources,HIGH,61.0,Resource 'insightsMonitorAccount' of type 'Microsoft.OperationalInsights/workspaces' does not configure a managed identity. Managed identities should be used for secure resource access instead of credentials.,"Add an 'identity' block to the 'insightsMonitorAccount' resource to enable a system-assigned or user-assigned managed identity.

🔵 Azure Guidance: Enable system/user-assigned managed identities for Azure services.",,Enable system/user-assigned managed identities for Azure services.,"Configure managed identities for VMs apps and Azure Functions. Use for Key Vault access.

This control is implemented through 3 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
operational-insights.bicep,IM-8,Identity Management,Use Managed Identities for Azure Resources,HIGH,91.0,Resource 'vmInsights' of type 'Microsoft.OperationsManagement/solutions' does not configure a managed identity. Managed identities should be used for secure resource access instead of credentials.,"Add an 'identity' block to the 'vmInsights' resource to enable a system-assigned or user-assigned managed identity.

🔵 Azure Guidance: Enable system/user-assigned managed identities for Azure services.",,Enable system/user-assigned managed identities for Azure services.,"Configure managed identities for VMs apps and Azure Functions. Use for Key Vault access.

This control is implemented through 3 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
operational-insights.bicep,IM-8,Identity Management,Use Managed Identities for Azure Resources,HIGH,106.0,Resource 'insightsComponents' of type 'Microsoft.Insights/components' does not configure a managed identity. Managed identities should be used for secure resource access instead of credentials.,"Add an 'identity' block to the 'insightsComponents' resource to enable a system-assigned or user-assigned managed identity.

🔵 Azure Guidance: Enable system/user-assigned managed identities for Azure services.",,Enable system/user-assigned managed identities for Azure services.,"Configure managed identities for VMs apps and Azure Functions. Use for Key Vault access.

This control is implemented through 3 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
operational-insights.bicep,IM-8,Identity Management,Use Managed Identities for Azure Resources,HIGH,121.0,Resource 'insightsWorkbooks' of type 'Microsoft.Insights/workbooks' does not configure a managed identity. Managed identities should be used for secure resource access instead of credentials.,"Add an 'identity' block to the 'insightsWorkbooks' resource to enable a system-assigned or user-assigned managed identity.

🔵 Azure Guidance: Enable system/user-assigned managed identities for Azure services.",,Enable system/user-assigned managed identities for Azure services.,"Configure managed identities for VMs apps and Azure Functions. Use for Key Vault access.

This control is implemented through 3 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
scaleset-networks.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,38.0,The 'scaleset' subnet in the virtual network (line 38) does not have a Network Security Group (NSG) associated. NSGs are required to segment and protect network resources.,"Associate a Network Security Group (NSG) with the 'scaleset' subnet to restrict inbound and outbound traffic according to least privilege. Define an NSG resource and reference its ID in the subnet's 'networkSecurityGroup' property.

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
scaleset-networks.bicep,NS-3,Network Security,Use Network Security Groups (NSGs),CRITICAL,38.0,No Network Security Group (NSG) is configured for the 'scaleset' subnet in the virtual network (line 38). NSGs are required to enforce network-level access control and deny all inbound traffic by default.,"Create and associate a Network Security Group (NSG) with the 'scaleset' subnet. Configure the NSG to deny all inbound traffic by default and allow only necessary traffic.

📚 References: [Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules)

🔵 Azure Guidance: Implement NSGs with deny-by-default rules.",[Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules),Implement NSGs with deny-by-default rules.,"Configure NSGs to deny all inbound traffic by default. Allow only necessary traffic.
NSG rules documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works
Security rules best practices: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
server-farms.bicep,DP-2,Data Protection,Enable encryption in transit,CRITICAL,74.0,"The App Service Plan resource 'serverFarms' at line 74 does not explicitly enforce HTTPS-only or specify a minimum TLS version, which is required to ensure encryption in transit (DP-2).","Add an App Service resource (Microsoft.Web/sites) with 'httpsOnly' set to true and specify 'minTlsVersion' in the 'siteConfig' block to enforce encryption in transit.

🔵 Azure Guidance: Use TLS 1.2+ for all data transfers.",,Use TLS 1.2+ for all data transfers.,"Enable HTTPS-only. Configure minimum TLS version. Use secure protocols.

This control is implemented through 6 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
server-farms.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,74.0,"The App Service Plan resource 'serverFarms' at line 74 does not configure a managed identity, which is required for secure identity management (IM-1).","Add an 'identity' block to the App Service Plan or associated App Service resource to enable a system-assigned or user-assigned managed identity.

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",,Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
signalR.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,5.0,"The Microsoft.SignalRService/signalR resource does not restrict public network access or configure private endpoints. By default, SignalR Service is accessible over the public internet, which violates the requirement to protect public endpoints.","Restrict public network access to the SignalR resource by enabling private endpoints or configuring IP ACLs to allow only required IP addresses. Consider using Azure Private Link or service endpoints to limit exposure.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,19.0,"The 'networkAcls' configuration for 'storageAccountFunc' sets 'defaultAction' to 'Allow', which permits public network access to the storage account. This violates NS-2, which requires strict access control for public endpoints.","Set 'networkAcls.defaultAction' to 'Deny' for 'storageAccountFunc' and explicitly allow only required IPs or subnets. Example: change 'defaultAction: ""Allow""' to 'defaultAction: ""Deny""'.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,54.0,"The 'networkAcls' configuration in 'fuzzStorageProperties' (used by 'storageAccountFuzz' and 'storageAccountsCorpus') sets 'defaultAction' to 'Allow', which permits public network access to the storage accounts. This violates NS-2, which requires strict access control for public endpoints.","Set 'networkAcls.defaultAction' to 'Deny' in 'fuzzStorageProperties' and explicitly allow only required IPs or subnets. Example: change 'defaultAction: ""Allow""' to 'defaultAction: ""Deny""'.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 30,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 7,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-17T14:08:36.918981,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
