<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Assessment Report - IaC Guardian</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            /* Modern Color Palette */
            --primary-blue: #1e40af;
            --primary-blue-light: #3b82f6;
            --secondary-blue: #0ea5e9;
            --accent-purple: #8b5cf6;
            --success-green: #10b981;
            --warning-amber: #f59e0b;
            --danger-red: #ef4444;
            --info-cyan: #06b6d4;

            /* Neutral Colors */
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;

            /* Semantic Colors */
            --critical-bg: #fef2f2;
            --critical-border: #fecaca;
            --critical-text: #dc2626;
            --high-bg: #fffbeb;
            --high-border: #fed7aa;
            --high-text: #ea580c;
            --medium-bg: #fefce8;
            --medium-border: #fde68a;
            --medium-text: #ca8a04;
            --low-bg: #f0f9ff;
            --low-border: #bae6fd;
            --low-text: #0284c7;

            /* Layout */
            --max-width: 1400px;
            --border-radius: 12px;
            --border-radius-sm: 8px;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--gray-700);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-size: 14px;
        }

        .main-container {
            max-width: var(--max-width);
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        /* Header Section */
        .report-header {
            background: white;
            border-radius: var(--border-radius);
            padding: 3rem 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-lg);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .report-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue), var(--accent-purple));
        }

        .report-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
        }

        .report-subtitle {
            font-size: 1.125rem;
            color: var(--gray-600);
            font-weight: 400;
            margin-bottom: 1rem;
        }

        .report-meta {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 1.5rem;
            font-size: 0.875rem;
            color: var(--gray-500);
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* Controls Section */
        .controls-section {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }

        .controls-grid {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 1.5rem;
            align-items: center;
        }

        .search-container {
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius-sm);
            font-size: 0.875rem;
            transition: all 0.2s ease;
            background: var(--gray-50);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
        }

        .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
        }

        .filter-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 0.5rem 1rem;
            border: 2px solid transparent;
            border-radius: 2rem;
            background: var(--gray-100);
            color: var(--gray-600);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            white-space: nowrap;
        }

        .filter-btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .filter-btn.active {
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .filter-btn.all.active { background: var(--primary-blue); }
        .filter-btn.critical.active { background: var(--danger-red); }
        .filter-btn.high.active { background: var(--warning-amber); }
        .filter-btn.medium.active { background: var(--medium-text); }
        .filter-btn.low.active { background: var(--info-cyan); }

        /* Summary Section */
        .summary-section {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }

        .summary-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-blue);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--gray-600);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .severity-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .severity-badge {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .severity-badge:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .severity-badge.critical {
            background: var(--critical-bg);
            border: 1px solid var(--critical-border);
            color: var(--critical-text);
        }

        .severity-badge.high {
            background: var(--high-bg);
            border: 1px solid var(--high-border);
            color: var(--high-text);
        }

        .severity-badge.medium {
            background: var(--medium-bg);
            border: 1px solid var(--medium-border);
            color: var(--medium-text);
        }

        .severity-badge.low {
            background: var(--low-bg);
            border: 1px solid var(--low-border);
            color: var(--low-text);
        }

        .severity-count {
            font-size: 1.25rem;
            font-weight: 700;
        }

        .severity-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* Findings Section */
        .findings-container {
            margin-bottom: 2rem;
        }

        .severity-group {
            background: white;
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
            box-shadow: var(--shadow);
            overflow: hidden;
            border: 1px solid var(--gray-200);
        }

        .severity-group:last-child {
            margin-bottom: 0;
        }

        .severity-header {
            padding: 1.25rem 1.5rem;
            font-weight: 600;
            font-size: 1.125rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 1px solid var(--gray-200);
        }

        .severity-header:hover {
            background: var(--gray-50);
        }

        .severity-header-left {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .severity-icon {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.875rem;
        }

        .severity-header.critical {
            background: var(--critical-bg);
            color: var(--critical-text);
            border-left: 4px solid var(--critical-text);
        }

        .severity-header.critical .severity-icon {
            background: var(--critical-text);
        }

        .severity-header.high {
            background: var(--high-bg);
            color: var(--high-text);
            border-left: 4px solid var(--high-text);
        }

        .severity-header.high .severity-icon {
            background: var(--high-text);
        }

        .severity-header.medium {
            background: var(--medium-bg);
            color: var(--medium-text);
            border-left: 4px solid var(--medium-text);
        }

        .severity-header.medium .severity-icon {
            background: var(--medium-text);
        }

        .severity-header.low {
            background: var(--low-bg);
            color: var(--low-text);
            border-left: 4px solid var(--low-text);
        }

        .severity-header.low .severity-icon {
            background: var(--low-text);
        }

        .severity-title {
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .severity-count {
            background: rgba(255, 255, 255, 0.9);
            color: inherit;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 700;
            margin-left: 0.5rem;
        }

        .toggle-icon {
            transition: transform 0.2s ease;
            color: var(--gray-500);
        }

        .severity-header.collapsed .toggle-icon {
            transform: rotate(-90deg);
        }

        .findings-list {
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .findings-list.collapsed {
            max-height: 0;
        }

        .finding-item {
            border-bottom: 1px solid var(--gray-200);
            padding: 1.5rem;
            transition: all 0.2s ease;
            background: white;
        }

        .finding-item:last-child {
            border-bottom: none;
        }

        .finding-item:hover {
            background: var(--gray-50);
        }

        .finding-header {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .finding-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
            flex-shrink: 0;
        }

        .finding-icon.critical { background: var(--danger-red); }
        .finding-icon.high { background: var(--warning-amber); }
        .finding-icon.medium { background: var(--medium-text); }
        .finding-icon.low { background: var(--info-cyan); }

        .finding-content {
            flex: 1;
            min-width: 0;
        }

        .finding-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            flex-wrap: wrap;
        }

        .control-id {
            background: var(--primary-blue);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .finding-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1rem;
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.375rem;
        }

        .meta-icon {
            color: var(--gray-400);
            width: 1rem;
        }

        .finding-description {
            color: var(--gray-700);
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .remediation-section {
            background: var(--success-green);
            background: linear-gradient(135deg, var(--success-green) 0%, #059669 100%);
            color: white;
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            margin-top: 1rem;
        }

        .remediation-title {
            font-weight: 600;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .remediation-content {
            font-size: 0.875rem;
            line-height: 1.5;
            opacity: 0.95;
        }

        .code-snippet {
            background: var(--gray-900);
            color: var(--gray-100);
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 0.8125rem;
            line-height: 1.5;
            white-space: pre-wrap;
            margin: 1rem 0;
            overflow-x: auto;
            border: 1px solid var(--gray-700);
        }

        .no-findings {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--gray-500);
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }

        .no-findings-icon {
            font-size: 3rem;
            color: var(--gray-300);
            margin-bottom: 1rem;
        }

        .no-findings h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
        }

        /* Footer */
        .report-footer {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-top: 2rem;
            box-shadow: var(--shadow);
            text-align: center;
        }

        .export-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }

        .export-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius-sm);
            background: var(--primary-blue);
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.875rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .export-btn:hover {
            background: var(--primary-blue-light);
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .footer-info {
            color: var(--gray-600);
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .footer-info strong {
            color: var(--gray-900);
        }

        /* Responsive Design */

        /* Large Desktop (1200px+) */
        @media (min-width: 1200px) {
            .main-container {
                padding: 3rem 2rem;
            }

            .stats-grid {
                grid-template-columns: repeat(4, 1fr);
            }

            .severity-overview {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        /* Desktop (992px - 1199px) */
        @media (min-width: 992px) and (max-width: 1199px) {
            .main-container {
                padding: 2rem 1.5rem;
            }

            .report-title {
                font-size: 2.25rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .severity-overview {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Tablet (768px - 991px) */
        @media (min-width: 768px) and (max-width: 991px) {
            .main-container {
                padding: 1.5rem 1rem;
            }

            .report-header {
                padding: 2rem 1.5rem;
            }

            .report-title {
                font-size: 2rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .report-meta {
                flex-direction: column;
                gap: 0.5rem;
            }

            .controls-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .filter-buttons {
                justify-content: center;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .severity-overview {
                grid-template-columns: repeat(2, 1fr);
            }

            .finding-title {
                font-size: 1rem;
            }

            .finding-meta {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        /* Mobile Large (576px - 767px) */
        @media (min-width: 576px) and (max-width: 767px) {
            .main-container {
                padding: 1rem 0.75rem;
            }

            .report-header {
                padding: 1.5rem 1rem;
            }

            .report-title {
                font-size: 1.75rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .report-subtitle {
                font-size: 1rem;
            }

            .report-meta {
                flex-direction: column;
                gap: 0.25rem;
                font-size: 0.8125rem;
            }

            .controls-section {
                padding: 1rem;
            }

            .controls-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .filter-buttons {
                justify-content: center;
                gap: 0.375rem;
            }

            .filter-btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.6875rem;
            }

            .summary-section {
                padding: 1.5rem 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .severity-overview {
                grid-template-columns: 1fr;
            }

            .finding-item {
                padding: 1rem;
            }

            .finding-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.75rem;
            }

            .finding-title {
                font-size: 0.9375rem;
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .export-actions {
                flex-direction: column;
                align-items: center;
            }
        }

        /* Mobile Small (up to 575px) */
        @media (max-width: 575px) {
            .main-container {
                padding: 0.75rem 0.5rem;
            }

            .report-header {
                padding: 1.25rem 0.75rem;
                margin-bottom: 1rem;
            }

            .report-title {
                font-size: 1.5rem;
                flex-direction: column;
                gap: 0.375rem;
            }

            .report-subtitle {
                font-size: 0.9375rem;
            }

            .report-meta {
                flex-direction: column;
                gap: 0.25rem;
                font-size: 0.75rem;
            }

            .controls-section {
                padding: 0.75rem;
            }

            .search-input {
                font-size: 1rem; /* Prevents zoom on iOS */
                padding: 0.75rem 1rem 0.75rem 2.25rem;
            }

            .filter-buttons {
                gap: 0.25rem;
            }

            .filter-btn {
                padding: 0.375rem 0.625rem;
                font-size: 0.625rem;
                min-width: auto;
            }

            .summary-section {
                padding: 1.25rem 0.75rem;
            }

            .summary-title {
                font-size: 1.25rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            .stat-card {
                padding: 1rem;
            }

            .stat-number {
                font-size: 2rem;
            }

            .severity-overview {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .severity-header {
                padding: 1rem;
                font-size: 1rem;
            }

            .finding-item {
                padding: 0.75rem;
            }

            .finding-icon {
                width: 2rem;
                height: 2rem;
                font-size: 0.875rem;
            }

            .finding-title {
                font-size: 0.875rem;
            }

            .control-id {
                font-size: 0.6875rem;
                padding: 0.1875rem 0.5rem;
            }

            .finding-meta {
                font-size: 0.8125rem;
            }

            .finding-description {
                font-size: 0.875rem;
            }

            .code-snippet {
                font-size: 0.75rem;
                padding: 0.75rem;
            }

            .export-actions {
                flex-direction: column;
                gap: 0.5rem;
            }

            .export-btn {
                width: 100%;
                justify-content: center;
            }
        }

        /* Print Styles */
        @media print {
            body {
                background: white !important;
            }

            .main-container {
                max-width: none;
                padding: 0;
            }

            .report-header,
            .controls-section,
            .summary-section,
            .severity-group,
            .report-footer {
                box-shadow: none !important;
                break-inside: avoid;
            }

            .controls-section,
            .export-actions {
                display: none !important;
            }

            .findings-list {
                max-height: none !important;
            }
        }
    </style>

    <script>
        // Modern JavaScript for enhanced interactivity
        let searchTimeout;
        let allFindings = [];
        let currentFilter = 'all';

        document.addEventListener('DOMContentLoaded', function() {
            initializeReport();
            setupEventListeners();
            loadFindings();
        });

        function initializeReport() {
            // Initialize filter buttons
            const filterButtons = document.querySelectorAll('.filter-btn');
            filterButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    setActiveFilter(this.dataset.severity);
                });
            });

            // Initialize search
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.addEventListener('input', debounceSearch);
            }

            // Initialize collapsible sections
            const severityHeaders = document.querySelectorAll('.severity-header');
            severityHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    toggleSeverityGroup(this);
                });
            });
        }

        function setupEventListeners() {
            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    clearSearch();
                }
                if (e.ctrlKey && e.key === 'f') {
                    e.preventDefault();
                    focusSearch();
                }
            });
        }

        function debounceSearch() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch();
            }, 300);
        }

        function performSearch() {
            const searchTerm = document.querySelector('.search-input').value.toLowerCase();
            const findings = document.querySelectorAll('.finding-item');
            let visibleCount = 0;

            findings.forEach(finding => {
                const text = finding.textContent.toLowerCase();
                const isVisible = text.includes(searchTerm);
                finding.style.display = isVisible ? 'block' : 'none';
                if (isVisible) visibleCount++;
            });

            updateNoResultsMessage(visibleCount === 0 && searchTerm.length > 0);
        }

        function setActiveFilter(severity) {
            currentFilter = severity;

            // Update button states
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-severity="${severity}"]`).classList.add('active');

            // Filter findings
            const severityGroups = document.querySelectorAll('.severity-group');
            severityGroups.forEach(group => {
                const groupSeverity = group.dataset.severity;
                if (severity === 'all' || groupSeverity === severity) {
                    group.style.display = 'block';
                } else {
                    group.style.display = 'none';
                }
            });

            // Update URL hash for bookmarking
            window.location.hash = severity === 'all' ? '' : severity;
        }

        function toggleSeverityGroup(header) {
            const group = header.parentElement;
            const findingsList = group.querySelector('.findings-list');
            const isCollapsed = header.classList.contains('collapsed');

            if (isCollapsed) {
                header.classList.remove('collapsed');
                findingsList.classList.remove('collapsed');
                findingsList.style.maxHeight = findingsList.scrollHeight + 'px';
            } else {
                header.classList.add('collapsed');
                findingsList.classList.add('collapsed');
                findingsList.style.maxHeight = '0';
            }
        }

        function clearSearch() {
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.value = '';
                performSearch();
            }
        }

        function focusSearch() {
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }

        function updateNoResultsMessage(show) {
            const noResults = document.querySelector('.no-findings');
            if (noResults) {
                noResults.style.display = show ? 'block' : 'none';
            }
        }

        function exportToJson() {
            const data = {
                timestamp: new Date().toISOString(),
                findings: allFindings,
                summary: {
                    total: allFindings.length,
                    critical: allFindings.filter(f => f.severity === 'CRITICAL').length,
                    high: allFindings.filter(f => f.severity === 'HIGH').length,
                    medium: allFindings.filter(f => f.severity === 'MEDIUM').length,
                    low: allFindings.filter(f => f.severity === 'LOW').length
                }
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], {
                type: 'application/json'
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `security-findings-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function loadFindings() {
            // This would be populated with actual findings data
            allFindings = [];
        }

        // Initialize filter from URL hash
        window.addEventListener('load', function() {
            const hash = window.location.hash.substring(1);
            if (hash && ['critical', 'high', 'medium', 'low'].includes(hash)) {
                setActiveFilter(hash);
            }
        });
    </script>
</head>

<body>
    <div class="main-container">
        <!-- Header Section -->
        <header class="report-header">
            <h1 class="report-title">
                <i class="fas fa-shield-alt"></i>
                Security Assessment Report
            </h1>
            <p class="report-subtitle">Infrastructure as Code Security Analysis</p>
            <div class="report-meta">
                <div class="meta-item">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Generated: June 16, 2025 at 08:23 PM</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-robot"></i>
                    <span>IaC Guardian GPT</span>
                </div>
            </div>
        </header>

        <!-- Controls Section -->
        <section class="controls-section">
            <div class="controls-grid">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search findings..." aria-label="Search findings">
                    <i class="fas fa-search search-icon"></i>
                </div>
                <div class="filter-buttons">
                    <button class="filter-btn all active" data-severity="all">All</button>
                    <button class="filter-btn critical" data-severity="critical">Critical</button>
                    <button class="filter-btn high" data-severity="high">High</button>
                    <button class="filter-btn medium" data-severity="medium">Medium</button>
                    <button class="filter-btn low" data-severity="low">Low</button>
                </div>
            </div>
        </section>

        
        <!-- Summary Section -->
        <section class="summary-section">
            <h2 class="summary-title">
                <i class="fas fa-chart-bar"></i>
                Executive Summary
            </h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">73</div>
                    <div class="stat-label">Total Findings</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">14</div>
                    <div class="stat-label">Files Affected</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">39</div>
                    <div class="stat-label">High Priority Issues</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">20</div>
                    <div class="stat-label">Security Controls</div>
                </div>
            </div>
            <div class="severity-overview">
                <div class="severity-badge critical">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        Critical
                    </div>
                    <div class="severity-count">2</div>
                </div>
                <div class="severity-badge high">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        High
                    </div>
                    <div class="severity-count">37</div>
                </div>
                <div class="severity-badge medium">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        Medium
                    </div>
                    <div class="severity-count">19</div>
                </div>
                <div class="severity-badge low">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        Low
                    </div>
                    <div class="severity-count">15</div>
                </div>
            </div>
        </section>

        <div class="findings-container">
            
        <section class="severity-group" data-severity="critical">
            <header class="severity-header critical">
                <div class="severity-header-left">
                    <div class="severity-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="severity-title">Critical Severity</div>
                    <div class="severity-count">2</div>
                </div>
                <i class="fas fa-chevron-down toggle-icon"></i>
            </header>
            <div class="findings-list">
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon critical">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>keyvault.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 22</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The Key Vault &#x27;networkAcls.defaultAction&#x27; is set to &#x27;Allow&#x27;, which permits unrestricted network access to the Key Vault from any IP address, exposing sensitive secrets and keys to potential attacks. This violates the control&#x27;s requirement to protect sensitive or critical resources using network security mechanisms.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Set &#x27;networkAcls.defaultAction&#x27; to &#x27;Deny&#x27; to restrict access to the Key Vault. Specify allowed &#x27;ipRules&#x27; and &#x27;virtualNetworkRules&#x27; explicitly to limit access only to authorized networks. Review and verify the subnet and IP rules defined.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon critical">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>keyvault.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 22</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        By configuring &#x27;networkAcls.defaultAction&#x27; as &#x27;Allow&#x27;, the Key Vault&#x27;s public endpoint is unprotected, making it accessible from the public internet. This increases the attack surface and risk of unauthorized access to sensitive secrets.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Set &#x27;networkAcls.defaultAction&#x27; to &#x27;Deny&#x27; to secure the public endpoint. Only allow authorized networks or specific IP addresses via &#x27;ipRules&#x27; or by using private endpoints. Remove public network access if not strictly required.
                        </div>
                    </div>
                </article>
            </div>
        </section>
        <section class="severity-group" data-severity="high">
            <header class="severity-header high">
                <div class="severity-header-left">
                    <div class="severity-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="severity-title">High Severity</div>
                    <div class="severity-count">37</div>
                </div>
                <i class="fas fa-chevron-down toggle-icon"></i>
            </header>
            <div class="findings-list">
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-5</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>app-config.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 7</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The App Configuration resource does not implement or expose configuration for private endpoints, leaving the endpoint potentially accessible over the public internet. This increases exposure to unauthorized access.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Configure the App Configuration instance to use Azure Private Endpoint, ensuring access is restricted to the private network. Reference and deploy a &#x27;Microsoft.Network/privateEndpoints&#x27; resource connected to the App Configuration store.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>app-config.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 7</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The App Configuration store is deployed without explicit restriction on public endpoints. Publicly accessible App Configuration instances are at higher risk of attacks and data exfiltration.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Limit public endpoint access by enabling private endpoints and configuring IP firewall rules (&#x27;publicNetworkAccess&#x27;: &#x27;Disabled&#x27; in properties), controlling which networks can access the service.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>app-config.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 19</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        App Configuration key-values are parameterized as &#x27;object[]&#x27;, and their values are set directly in the template. If secrets or sensitive values are passed in the &#x27;keyValues&#x27; parameter, these could be exposed in deployment logs or template source code, violating best practices for secret management.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Ensure no secrets (such as credentials, API keys, or connection strings) are passed via template parameters or hardcoded in the template. Store all secrets in Azure Key Vault and reference them securely from your application at runtime.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-5</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>event-grid.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The template provisions Event Grid System Topics and configures event subscriptions with a StorageQueue destination. There is no evidence that private endpoints are enforced for the related Storage Accounts or the Service Bus-like resources (Storage Queues), which can result in exposure of queues over public networks.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Configure private endpoints for all Storage Accounts used with Event Grid and for any Storage Queue used as a destination, ensuring that all access occurs over a private link, not the public internet.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>event-grid.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        No Network Security Groups (NSGs) or Azure Firewall are referenced for protecting the Storage Accounts used as sources or the Storage Queues used as destinations in the event subscription. This leaves resources potentially exposed to unfiltered network traffic.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Apply NSGs or Azure Firewall rules to the subnets containing these storage and queue resources to restrict access to trusted sources and services only.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>event-grid.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The template does not show any measures to restrict public endpoints for the Azure Storage Accounts or queues. Unrestricted public endpoints can expose sensitive event data or allow unauthorized access.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Restrict access to public endpoints for all storage and messaging resources, and ensure they are only accessible through private endpoints or restricted firewall rules.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>function-settings.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 36</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The value for &#x27;APPINSIGHTS_INSTRUMENTATIONKEY&#x27; is assigned directly from the &#x27;app_insights_key&#x27; parameter within application settings instead of using a managed secret reference (e.g., Azure Key Vault). Application Insights instrumentation keys are sensitive and should not be set directly in app settings.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Store the Application Insights instrumentation key in Azure Key Vault and reference it using a Key Vault reference in the Function App application settings. Only assign the reference in the template, not the raw secret value.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>function-settings.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 22</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The &#x27;ado_access_client_id&#x27; parameter is hardcoded with a specific GUID in the template. Hardcoding sensitive identifiers may increase the risk of identity exposure if the identifier is considered confidential or privileged.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Consider passing sensitive client IDs as secure parameters or storing them in a secure location such as Azure Key Vault, particularly if this identifier is used for privileged access.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>function-settings.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 25</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The storage account resource &#x27;funcStorage&#x27; is declared as an existing resource, but the template does not show or guarantee any network security measures such as NSG rules, private endpoints, or firewall restrictions protecting the storage account used by the Function App.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Ensure that the referenced storage account has NSG or firewall rules in place, restricting network access to only required services, such as the Application Service. Use private endpoints to limit exposure of the storage account.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>function-settings.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 55</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The application setting &#x27;AzureSignalRConnectionString__serviceUri&#x27; constructs a public endpoint URL (https://${signalRName}.service.signalr.net) for SignalR access. No mention is made of access restrictions, private endpoints, or limiting exposure, potentially exposing the endpoint publicly.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Configure SignalR with private endpoints and restrict access using access controls or firewall rules. If public endpoints must be used, ensure strict authentication and authorization controls are in place.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-6</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>function-settings.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 60</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Application setting &#x27;ONEFUZZ_INSTANCE&#x27; constructs a public URL (https://${instance_name}.azurewebsites.net), indicating that the Function App will be accessible via a public endpoint. There are no explicit configurations shown for access restrictions (e.g., IP restrictions, service endpoints, or private endpoints).
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Restrict public endpoint access by configuring access restrictions on the App Service, or use Private Endpoints to reduce exposure.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>function-settings.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The template does not specify or enforce encryption settings for the referenced storage account (&#x27;funcStorage&#x27;); it relies on the existing resource configuration, which may not have encryption at rest enabled.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Ensure that the referenced storage account has encryption at rest enabled (e.g., by using Storage Service Encryption with customer-managed or Microsoft-managed keys). Verify and enforce this in the storage account resource configuration.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-9</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>function-settings.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        No explicit enforcement of encryption in transit (TLS 1.2 or above) for connections to the Storage Account, AppService, or SignalR Service. Default configurations may not guarantee the minimum required TLS protocol version.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Explicitly set minimum TLS version to 1.2 for all service resources (Storage Account, App Service, SignalR) and update client configurations to enforce secure protocols.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-5</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>function-settings.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 25</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        There is no evidence of private endpoints being used for the Storage Account resource (&#x27;funcStorage&#x27;), meaning the storage remains potentially accessible over the public internet.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Configure the Storage Account with a Private Endpoint to ensure it is accessible only from within the trusted virtual network.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-5</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>function-settings.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 55</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        SignalR endpoint is accessed via the public FQDN in settings. There is no evidence of the use of private endpoints for SignalR, increasing the risk of exposure.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Enable private endpoints for the SignalR service and update configuration to use the private DNS zone.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>function.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 39</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The storage account resource &#x27;funcStorage&#x27; is referenced as &#x27;existing&#x27; and there is no evidence in the template that it is protected by a Network Security Group (NSG) or Azure Firewall. Unprotected storage accounts are at risk from unauthorized access or data exfiltration.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Ensure that the referenced storage account is restricted via network security controls, either by using private endpoints, service endpoints, or explicitly applying NSG rules or Azure Firewall for allowed networks only.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>function.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 52</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The Azure Function App &#x27;function&#x27; resource does not explicitly restrict public network access. Without settings such as &#x27;publicNetworkAccess: Disabled&#x27; or proper VNet integration, the Function App may be accessible from the public internet.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Set &#x27;publicNetworkAccess&#x27; to &#x27;Disabled&#x27; for the Function App, and ensure that only necessary internal networks can reach the function by using VNet integration and Private Endpoints if possible.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-5</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>function.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 39</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The storage account &#x27;funcStorage&#x27; is not configured within this template to use a Private Endpoint, nor is there assurance that private endpoint connectivity is enforced for blob or queue services. This increases the attack surface and risk of data exfiltration.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Configure Private Endpoints for the storage account&#x27;s services that the function app will use. Ensure the Function App interacts with the storage account exclusively via a Private Endpoint.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>function.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 78</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The SAS token for storage is constructed within the template (&#x27;sas.accountSasToken&#x27;) and included directly in the logs configuration, potentially exposing sensitive storage access information. This is a risk if the template or deployment output is stored insecurely.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Generate SAS tokens at runtime, not in code or templates. Use managed identities for Azure resource access wherever possible, or securely retrieve SAS from a Key Vault rather than constructing inline within the template.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>hub-network.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 2</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The defined virtual network and subnet (&#x27;hub-vnet&#x27;, &#x27;hub-subnet&#x27;) do not have any associated Network Security Groups (NSGs), leaving resources unprotected from unwanted inbound and outbound network traffic. This violates the benchmark&#x27;s requirement to use NSGs or Azure Firewall to protect resources.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Associate an NSG with the subnet &#x27;hub-subnet&#x27; to define and enforce security rules that restrict inbound and outbound traffic according to the principle of least privilege.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>hub-network.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 2</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        No Network Security Groups (NSGs) are implemented for the subnet, which means network traffic (including to App Service with delegated subnet) is uncontrolled at the subnet level. This violates the explicit requirement to use NSGs for controlling network access.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Create an NSG resource and associate it with &#x27;hub-subnet&#x27; to control the permissible inbound and outbound network traffic.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>instance-config.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 5</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Sensitive configuration values, including &#x27;cli_client_id&#x27;, &#x27;tenant_id&#x27;, and &#x27;monitoringGCSAuthId&#x27; are passed via the &#x27;specificConfig&#x27; parameter and appear potentially inline or from external JSON sources, but no use of Azure Key Vault or explicit secure referencing is present. This increases the risk of accidental disclosure of secrets or sensitive information.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Store all secrets, passwords, client IDs, and sensitive configuration data in Azure Key Vault. Reference them in the template using secure parameters or Key Vault references, rather than passing them inline or from unsecured files.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>ip-rules.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 3</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The file defines extremely broad corporate network IP ranges (e.g., &#x27;*******/8&#x27;, &#x27;********/8&#x27;), which are likely to contain numerous public address spaces. Allowing traffic from such wide public IP ranges (including /8s) significantly increases exposure to unauthorized access, violating the requirement to secure public endpoints.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Review and restrict IP address ranges to only those absolutely necessary and known to be under your direct control. Avoid using broad public IP ranges and instead allow only specific, trusted, and minimum necessary subnets as per NS-2 guidance.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>keyvault.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 22</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        No Network Security Groups (NSGs) are attached to the Key Vault subnet or referenced in the template. NSGs should be used in conjunction with virtual network service endpoints or private links to control inbound and outbound access to the Key Vault.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Associate an NSG with the &#x27;hubSubnetId&#x27; referenced by the Key Vault&#x27;s virtual network rules. Configure the NSG to restrict access only to approved sources and required ports. Explicitly manage traffic to/from the subnet hosting the Key Vault.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>operational-insights.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 67</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The output &#x27;appInsightsInstrumentationKey&#x27; exposes the Application Insights instrumentation key as a deployment output. Instrumentation keys are sensitive secrets and should not be returned in outputs as this risks accidental disclosure through portal, scripts, or CI/CD logs.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Remove the output of &#x27;appInsightsInstrumentationKey&#x27;. If access to Application Insights is required, configure RBAC for access. If the key must be provided, use Azure Key Vault to securely store and retrieve it.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>operational-insights.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 66</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The output &#x27;appInsightsAppId&#x27; exposes the Application Insights Application ID. Although less sensitive than the instrumentation key, exposing this value is not generally advisable unless strictly required by downstream automation, as it may aid attackers in reconnaissance.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Limit outputs to only those required by automated processes. Do not expose identifiers unless business needs demand it and ensure access to deployment outputs is tightly controlled.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>scaleset-networks.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        No Network Security Groups (NSGs) or Azure Firewall are associated with the virtual network or its subnet. This means there is no granular access control to protect the attached resources from potential network-based attacks.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Associate an NSG with the &#x27;scaleset&#x27; subnet and define rules according to the principle of least privilege. Optionally, deploy an Azure Firewall to further protect resources.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>scaleset-networks.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 34</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The &#x27;scaleset&#x27; subnet is missing a Network Security Group (NSG). Without an NSG, there is no control over inbound or outbound traffic to this subnet, which increases the risk of exposure to unauthorized access.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Define and associate a Network Security Group (NSG) with the &#x27;scaleset&#x27; subnet and specify rules to restrict inbound and outbound traffic as needed for your workloads.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>server-farms.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        There are no network security controls, such as Network Security Groups (NSGs) or Azure Firewall, protecting the deployed App Service or Key Vault resources. This leaves the resources potentially exposed to untrusted network traffic, in violation of ASB NS-1.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Implement NSGs or use Azure Firewall to restrict inbound and outbound traffic to only what is necessary. For App Services, configure Access Restrictions to limit access to the app. Protect Key Vaults with firewall rules or private endpoints.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>server-farms.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The App Service and Key Vault resources are configured without access restrictions or private endpoints, potentially exposing public endpoints to the internet and increasing the attack surface, which violates ASB NS-2.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Configure App Service access restrictions to only allow access from trusted IPs or subnets, and protect Key Vault by enabling firewall rules and/or private endpoints.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-5</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>server-farms.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The Key Vault referenced in the template (&#x27;genevaCertVaultId&#x27;) is accessed using a public resource ID, and the template does not deploy or require private endpoints for secure resource access, violating ASB NS-5.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Implement Azure Private Endpoints for Key Vault access, and configure App Service access restrictions to only allow connections via private endpoints or trusted networks.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">IM-8</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>server-farms.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The template does not configure Managed Identities for the App Service or App Service Plan, which is a best practice for secure authentication to Key Vault. This omission can lead to use of less secure authentication methods or hardcoded secrets.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Add a managed identity to the App Service (web app or function app) and grant it the required permissions to the Key Vault to securely access secrets and certificates.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>server-farms.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        There is no explicit configuration enforcing TLS 1.2 or higher for the App Service. Failing to enforce secure protocols puts in-transit data at risk.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Configure the App Service to require HTTPS only and enforce a minimum TLS version of 1.2 or above within the App Service configuration.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>signalR.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 4</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The Azure SignalR Service resource is being deployed without explicit network access controls or restrictions on public endpoints. By default, Azure SignalR exposes public endpoints, which could lead to unnecessary exposure if not properly restricted.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Restrict public network access to the SignalR Service by enabling private endpoints or setting &#x27;publicNetworkAccess&#x27; to &#x27;Disabled&#x27;. Additionally, configure IP ACLs to only allow trusted sources to access the service.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>storage-accounts.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 24</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Storage accounts have &#x27;networkAcls.defaultAction&#x27; set to &#x27;Allow&#x27;, meaning that any client not explicitly denied by IP or virtual network rule will have access. This may expose the storage account to the public internet, increasing risk of unauthorized access, violating secure public endpoint recommendations.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Set &#x27;networkAcls.defaultAction&#x27; to &#x27;Deny&#x27; to prevent public access by default. Explicitly allow only required IPs and subnets for business needs. Review and minimize the &#x27;bypass&#x27; setting.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>storage-accounts.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 24</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Storage accounts without network-level protections such as NSGs or firewalls allow access from broad networks. The use of &#x27;defaultAction: Allow&#x27; and relatively broad bypass settings (&#x27;AzureServices, Logging, Metrics&#x27;) weaken network security posture for storage accounts.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Enforce storage account access by setting &#x27;defaultAction&#x27; to &#x27;Deny&#x27; and only permitting trusted subnets or IP addresses. Consider an Azure Firewall or implementing NSGs on associated subnets.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon high">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>storage-accounts.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 15</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        There is no explicit specification of &#x27;minimumTlsVersion&#x27; in the storage account resources. The default Azure setting may allow older, less secure protocols. TLS 1.0/1.1 should be disabled, and only 1.2 or above should be enforced.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Explicitly set the &#x27;minimumTlsVersion&#x27; property to &#x27;TLS1_2&#x27; for all &#x27;Microsoft.Storage/storageAccounts&#x27; resources.
                        </div>
                    </div>
                </article>
            </div>
        </section>
        <section class="severity-group" data-severity="medium">
            <header class="severity-header medium">
                <div class="severity-header-left">
                    <div class="severity-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="severity-title">Medium Severity</div>
                    <div class="severity-count">19</div>
                </div>
                <i class="fas fa-chevron-down toggle-icon"></i>
            </header>
            <div class="findings-list">
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>autoscale-settings.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        No references to Network Security Groups (NSGs) or Azure Firewall are present to protect the resources (such as the storage account used in autoscale rules). Without NSG or Firewall configuration, resources like storage accounts or app service plans may be overly exposed.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Implement Network Security Groups or Azure Firewall rules to protect the underlying compute and storage resources. Restrict access to the storage account and any associated compute resource to only those networks that explicitly require it.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>autoscale-settings.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 42</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The autoscale rules reference a storage account queue endpoint (via func_storage_account_id), but there is no indication that public endpoints for this storage account are protected or restricted.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Restrict public network access to the referenced storage account by enabling &#x27;Allow trusted Microsoft services&#x27; only, using service endpoints, private endpoints, or explicitly blocking public network access.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-6</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>event-grid.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        No service endpoints are configured for the underlying Storage Accounts or Service Bus-like resources receiving the Event Grid events, increasing the attack surface.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Enable Virtual Network Service Endpoints for reliant storage resources to restrict traffic to only permitted subnets within your Azure Virtual Network.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>event-grid.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The template does not specify settings to enable encryption at rest for Storage Accounts or queues storing sensitive data connected to these Event Grid topics.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Ensure that the target Storage Accounts and Storage Queues have encryption at rest enabled by default; specify encryption parameters or require secure configurations at deployment.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>event-grid.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        No evidence that TLS 1.2 or above is enforced for data in transit between Event Grid, Storage Accounts, and Storage Queues. Lower versions can expose data to interception.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Configure all Storage Accounts and queue endpoints to enforce TLS 1.2 or higher for all API and data connections.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">IM-8</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>function-settings.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 70</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The template references use of a user-assigned managed identity for EasyAuth, but does not show that the Function App itself is enabled for system or user-assigned managed identity. Lack of managed identities can result in insecure credential management and poor resource-to-resource authentication practices.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Enable and configure managed identity for the Azure Function App to securely authenticate to dependent resources such as Key Vault or Storage Account, following the principle of least privilege.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>function.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 67</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The App Service site config does not specify the minimum TLS version for HTTPS endpoints. The default may allow TLS 1.0/1.1, which is not recommended per ASB DP-2.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Explicitly set &#x27;minTlsVersion&#x27; to &#x27;1.2&#x27; or higher in the function app siteConfig to ensure all HTTPS traffic uses a secure protocol.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>instance-config.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 23</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The &#x27;network_config&#x27; variable statically defines an address space and subnet, but there is no configuration or resource deployment in this file for Network Security Groups (NSGs) or Azure Firewalls to protect associated compute resources. This omits the baseline network protection recommended by ASB.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Define and associate Network Security Groups (NSGs) and/or Azure Firewall resources to segments referenced in &#x27;network_config&#x27;, specifying appropriate inbound and outbound rules to protect compute instances according to the principle of least privilege.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>instance-config.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 23</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        No Network Security Groups (NSGs) are defined or referenced for the specified &#x27;address_space&#x27; and &#x27;subnet&#x27;; this fails to implement granular traffic control around compute resources.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Implement NSGs to control traffic to and from the subnet defined in &#x27;network_config&#x27;. Specify restrictive rules based on actual application requirements.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-4</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>instance-config.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 15</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The template does not define or reference any policy or resource configuration to ensure that managed disks attached to compute resources are encrypted.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Explicitly enable encryption for all managed disks in compute resource definitions or apply an Azure Policy to enforce disk encryption for all VMs.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>instance-config.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 15</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        No configuration for encryption at rest is specified for compute resource attached storage or disks, risking non-compliance with encryption at rest requirements.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Ensure all managed disks or attached data/storage are encrypted at rest by specifying encryption settings in VM and disk resources or applying an organizational policy for enforcement.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>ip-rules.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The template only exports allow rules and does not show any default-deny or network security grouping mechanism (e.g., NSG or Azure Firewall) applied to resources. Without explicit use of NSG or firewall, resources may remain unprotected.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Ensure that these rules are applied as part of or in conjunction with a Network Security Group or Azure Firewall restricting traffic as per the principle of least privilege. Add resources to the template or reference NSGs explicitly to enforce these rules.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-5</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>ip-rules.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        There is no indication that private endpoints are being used for Azure resources (e.g., storage, LogicApps) to provide secure access. Relying only on IP allow lists may still expose resources publicly.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Implement Azure Private Endpoints for every supported resource to limit access to only traffic over the private virtual network interface and avoid inbound exposure via public IP addresses.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-6</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>keyvault.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 16</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The Key Vault does not enable customer-managed keys (CMK) for additional encryption control (e.g., through &#x27;properties.encryption&#x27; block). The default uses Microsoft-managed keys, which may not meet advanced compliance requirements.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            If compliance requires, configure the Key Vault to use customer-managed keys by specifying an &#x27;encryption&#x27; property referencing an Azure Key Vault key for key encryption. Store the CMK in a separate, secured Key Vault.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>operational-insights.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 70</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The output &#x27;logAnalyticsWorkspaceId&#x27; exposes the Log Analytics workspace customer ID. Though not a secret, overexposing identifiers can aid attackers in targeting services.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Only output values that are operationally necessary. Avoid exposing workspace identifiers unless required for integration. Ensure access to deployment outputs is restricted.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>scaleset-networks.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 9</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The deployment creates a Standard public IP for use with a NAT Gateway, exposing outbound traffic with a static public endpoint. There are no controls shown (such as NSG rules or Azure Firewall) to restrict or monitor this exposure.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Ensure proper egress control by associating NSGs with the subnet and restricting outbound rules as narrowly as possible. Optionally, monitor egress traffic with Azure Firewall or Network Watcher.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">IM-6</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>server-farms.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        There is no evidence of using Role-Based Access Control (RBAC) on the Key Vault or App Service resources, risking excessive or unnecessary privileges.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Assign carefully scoped RBAC roles to users and/or service principals only as necessary for operating with Key Vault and App Service. Regularly audit and review access assignments.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>signalR.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 4</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        There is no implementation of Network Security Groups (NSGs) or Azure Firewall to restrict access to the SignalR Service or control inbound/outbound traffic flows, which is recommended even for PaaS services with public endpoints.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Configure NSGs or Azure Firewall rules at the subnet or virtual network level for downstream services and use IP ACLs or private endpoints to further restrict SignalR Service access as per the principle of least privilege.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon medium">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>storage-accounts.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 24</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        There is no reference to NSGs protecting the subnets associated with the storage account network rules. Without NSGs, there&#x27;s no granular traffic filtering to/from the resources.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Deploy and associate network security groups (NSGs) with the subnets referenced in &#x27;hubSubnetId&#x27; to enforce granular traffic control in and out of the storage accounts.
                        </div>
                    </div>
                </article>
            </div>
        </section>
        <section class="severity-group" data-severity="low">
            <header class="severity-header low">
                <div class="severity-header-left">
                    <div class="severity-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="severity-title">Low Severity</div>
                    <div class="severity-count">15</div>
                </div>
                <i class="fas fa-chevron-down toggle-icon"></i>
            </header>
            <div class="findings-list">
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">AM-1</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>autoscale-settings.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The template does not specify access controls or role assignments related to who or what can modify or access the autoscale settings resource. This leaves least-privilege assignments up to post-deployment operations, risking overly broad access.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Explicitly define role assignments in your deployment or in linked deployments to ensure the autoscale settings and linked resources (such as storage accounts) are only modifiable by least-privileged service principals or users.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>autoscale-settings.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 6</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The template takes several parameters (like owner, workspaceId, autoscale_name, etc.), and there is no indication that secrets or sensitive parameters (such as potential connection strings or keys) are being referenced via secure parameters or Azure Key Vault. If a sensitive value were injected, it risks being managed insecurely.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            If any supplied parameter values represent secrets or keys, switch to Azure Key Vault references and ensure secret values are not passed inline to templates. Label parameters appropriately as secure (using Bicep&#x27;s &#x27;secure&#x27; modifier), and avoid direct inline secrets.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>event-grid.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        No evidence of sensitive parameters (such as secrets or connection strings) being included directly in the template; however, if future extensions include such parameters, they should use secure references (e.g., Azure Key Vault references).
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Ensure any sensitive information (secrets, connection strings) are referenced securely via Azure Key Vault instead of hardcoding in the template.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">IM-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>instance-config.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 5</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        There are no explicit configurations or parameters enforcing Multi-Factor Authentication (MFA) for administrators or users, increasing the risk of credential compromise.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Enforce MFA for all privileged accounts accessing the compute resources by integrating with Azure AD Conditional Access policies and documenting MFA in operational processes.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">IM-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>instance-config.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 5</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Conditional access policies are not referenced or surfaced in the configuration, leaving access control enforcement to external governance or policy.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Document and enforce the use of Azure AD Conditional Access policies for all compute resources, particularly for administrator access and sensitive operations.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-7</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>instance-config.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        There is no indication of support or enablement for Just-In-Time (JIT) VM Access, which would reduce the attack surface on management ports.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Implement JIT access for VMs at the resource or NSG level to ensure management ports are only accessible when needed for specific time windows.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-10</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>instance-config.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The template does not reference or suggest the use of Azure Bastion for secure VM management, implying possible exposure of SSH/RDP ports.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Deploy Azure Bastion and restrict direct SSH/RDP access to VMs; document usage procedures in deployment configuration.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-6</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>ip-rules.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        No use of Virtual Network Service Endpoints is indicated. Without service endpoints, traffic between services and Azure resources may traverse the public internet, increasing risk.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Use Azure Virtual Network Service Endpoints to confine service-to-service communications within the Azure backbone network.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-9</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>ip-rules.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        There are no logging or monitoring configurations (e.g., Azure Monitor, Network Watcher) in the template. Absence of network monitoring can hinder the detection of suspicious activity or unauthorized access.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Add resources or configuration settings to enable diagnostic logs and monitoring (such as Flow Logs) on all relevant network resources per NS-9.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>ip-rules.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 29</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The ingestionServiceIps array has a TODO placeholder and may be populated later. If this is left as-is, it may lead to insecure defaults or accidental overexposure if not properly reviewed.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Ensure that before deployment, all TODOs or empty rule sets are properly reviewed and populated with secure, validated entries. Remove placeholder comments and enforce code review checkpoints for such areas.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-2</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>keyvault.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 0</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The template does not explicitly configure minimum TLS version for the Key Vault, potentially allowing older insecure TLS protocols and exposing data in transit to downgrade or interception attacks.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Add &#x27;properties.enabledForTemplateDeployment: true&#x27; and set &#x27;properties.minimumTlsVersion&#x27; to &#x27;TLS1_2&#x27; or later within the Key Vault resource to enforce secure communications.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-5</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>scaleset-networks.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 41</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        Private endpoints are not configured for securely accessing PaaS resources within the virtual network or subnet. Direct subnet access without private endpoints may expose sensitive resources.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Use Azure Private Endpoints to connect critical resources to the subnet privately, minimizing public exposure.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-6</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>scaleset-networks.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 41</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        No service endpoints are enabled in the subnet configuration (&#x27;serviceEndpoints&#x27;: []), reducing the security of communication with Azure services.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Add service endpoints for required Azure services to the &#x27;serviceEndpoints&#x27; array in the subnet properties to securely route traffic and restrict access at the service level.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">NS-8</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>scaleset-networks.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 1</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        DDoS protection is not enabled for the virtual network, leaving resources susceptible to denial-of-service attacks.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            Enable Azure DDoS Protection Standard on the virtual network to mitigate volumetric, protocol, and resource-level DDoS attacks.
                        </div>
                    </div>
                </article>
                <article class="finding-item">
                    <header class="finding-header">
                        <div class="finding-icon low">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="finding-content">
                            <h4 class="finding-title">
                                Security Issue Detected
                                <span class="control-id">DP-3</span>
                            </h4>
                            <div class="finding-meta">
                                <div class="meta-item">
                                    <i class="fas fa-file-code meta-icon"></i>
                                    <span>server-farms.bicep</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt meta-icon"></i>
                                    <span>Line 81</span>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="finding-description">
                        The resource &#x27;serverFarms_AntMDS_CERTIFICATE_PASSWORD_GENEVACERT&#x27; sets the &#x27;CERTIFICATE_PASSWORD_GENEVACERT&#x27; value to an empty string, which suggests it may act as a placeholder for a sensitive secret but does not securely provision it. This can lead to mismanagement of secrets.
                    </div>
                    <div class="remediation-section">
                        <div class="remediation-title">
                            <i class="fas fa-tools"></i>
                            Recommended Solution
                        </div>
                        <div class="remediation-content">
                            If a certificate password is needed, store it securely in Azure Key Vault and set this value by referencing the Key Vault secret. Do not leave placeholders for secrets; handle all secrets with managed identities and Key Vault references.
                        </div>
                    </div>
                </article>
            </div>
        </section>
            <div class="no-findings" style="display: none;">
                <i class="fas fa-search no-findings-icon"></i>
                <h3>No findings match your search criteria</h3>
                <p>Try adjusting your search terms or filters</p>
            </div>
        </div>

        <!-- Footer Section -->
        <footer class="report-footer">
            <div class="export-actions">
                <button class="export-btn" onclick="window.print()">
                    <i class="fas fa-print"></i>
                    Print Report
                </button>
                <button class="export-btn" onclick="exportToJson()">
                    <i class="fas fa-download"></i>
                    Export JSON
                </button>
            </div>
            <div class="footer-info">
                <p><strong>Generated by IaC Guardian GPT</strong> • June 16, 2025 at 08:23 PM</p>
                <p><strong>Disclaimer:</strong> This report is AI-generated and should be reviewed by security professionals.</p>
            </div>
        </footer>
    </div>
</body>
</html>