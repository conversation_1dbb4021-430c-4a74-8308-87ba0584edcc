File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
app-config.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyValues' flows across template boundary,"Secure parameter 'keyValues' and validate its usage across template boundaries

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applicati...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),"Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applications to Azure AD when technically feasible.","Enhanced Implementation Context:
• Azure AD tenancy concepts: https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps
• Create and configure Azure AD: https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant
• Define Azure AD tenants: https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/
• External identity providers: https://docs.microsoft.com/azure/active-directory/b2b/identity-providers
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 6.7, 12.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8
• PCI-DSS v3.2.1: 7.2, 8.3

Azure Policy Examples:
• An Azure Active Directory administrator should be provisioned for SQL servers
• Service Fabric clusters should only use Azure Active Directory for client authentication
• Standardize identity provider across all applications and services",cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'app_insights_key' flows across template boundary,"Secure parameter 'app_insights_key' and validate its usage across template boundaries

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applicati...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),"Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applications to Azure AD when technically feasible.","Enhanced Implementation Context:
• Azure AD tenancy concepts: https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps
• Create and configure Azure AD: https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant
• Define Azure AD tenants: https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/
• External identity providers: https://docs.microsoft.com/azure/active-directory/b2b/identity-providers
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 6.7, 12.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8
• PCI-DSS v3.2.1: 7.2, 8.3

Azure Policy Examples:
• An Azure Active Directory administrator should be provisioned for SQL servers
• Service Fabric clusters should only use Azure Active Directory for client authentication
• Standardize identity provider across all applications and services",cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyvault_name' flows across template boundary,"Secure parameter 'keyvault_name' and validate its usage across template boundaries

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applicati...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),"Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applications to Azure AD when technically feasible.","Enhanced Implementation Context:
• Azure AD tenancy concepts: https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps
• Create and configure Azure AD: https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant
• Define Azure AD tenants: https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/
• External identity providers: https://docs.microsoft.com/azure/active-directory/b2b/identity-providers
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 6.7, 12.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8
• PCI-DSS v3.2.1: 7.2, 8.3

Azure Policy Examples:
• An Azure Active Directory administrator should be provisioned for SQL servers
• Service Fabric clusters should only use Azure Active Directory for client authentication
• Standardize identity provider across all applications and services",cross_reference_analysis,privilege_escalation,Validated
keyvault.bicep,NS-1,Network Security,Protect resources using network security groups,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,"Review template dependencies and ensure secure communication between templates

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'secrets' flows across template boundary,"Secure parameter 'secrets' and validate its usage across template boundaries

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applicati...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),"Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applications to Azure AD when technically feasible.","Enhanced Implementation Context:
• Azure AD tenancy concepts: https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps
• Create and configure Azure AD: https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant
• Define Azure AD tenants: https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/
• External identity providers: https://docs.microsoft.com/azure/active-directory/b2b/identity-providers
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 6.7, 12.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8
• PCI-DSS v3.2.1: 7.2, 8.3

Azure Policy Examples:
• An Azure Active Directory administrator should be provisioned for SQL servers
• Service Fabric clusters should only use Azure Active Directory for client authentication
• Standardize identity provider across all applications and services",cross_reference_analysis,privilege_escalation,Validated
storage-accounts.bicep,NS-1,Network Security,Protect resources using network security groups,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,"Review template dependencies and ensure secure communication between templates

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,DP-6,Data Protection,Use a secure key management process,HIGH,1.0,Parameter flow security risk: Sensitive parameters may be exposed through template dependencies,"Use Key Vault references for sensitive parameters and validate parameter flow security

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/key-vault/general/overview) | [Azure data encryption key hierarchy](https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#key-hierarchy) | [BYOK specification](https://docs.microsoft.com/azure/key-vault/keys/byok-specification) | [Key Vault security best practices](https://docs.microsoft.com/azure/key-vault/general/best-practices)

🔵 Azure Guidance: Use Azure Key Vault to create and control encryption key lifecycle including generation distribution storage rotation and revocation. Follow best practices for key hierarchy and BYOK scenarios. Ensure...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/key-vault/general/overview) | [Azure data encryption key hierarchy](https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#key-hierarchy) | [BYOK specification](https://docs.microsoft.com/azure/key-vault/keys/byok-specification) | [Key Vault security best practices](https://docs.microsoft.com/azure/key-vault/general/best-practices),Use Azure Key Vault to create and control encryption key lifecycle including generation distribution storage rotation and revocation. Follow best practices for key hierarchy and BYOK scenarios. Ensure FIPS compliance levels meet requirements.,"Enhanced Implementation Context:
• Azure Key Vault overview: https://docs.microsoft.com/azure/key-vault/general/overview
• Azure data encryption key hierarchy: https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#key-hierarchy
• BYOK specification: https://docs.microsoft.com/azure/key-vault/keys/byok-specification
• Key Vault security best practices: https://docs.microsoft.com/azure/key-vault/general/best-practices
• FIPS 140-2 compliance levels: Software-protected keys (Level 1) HSM-protected keys in vaults (Level 2) HSM-protected keys in Managed HSM (Level 3)

Compliance Mappings:
• CIS Controls v8: Not specified
• NIST SP800-53 r4: IA-5, SC-12, SC-28
• PCI-DSS v3.2.1: 3.6

Azure Policy Examples:
• Key Vault keys should have an expiration date
• Key Vault secrets should have an expiration date
• Implement key rotation policies
• Use separate data encryption keys (DEK) with key encryption keys (KEK)
• Register keys with Azure Key Vault using key IDs",cross_reference_analysis,parameter_flow,Validated
function.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,35.0,"The 'sasUrl' property on line 141 constructs a SAS URL for Azure Blob Storage logs using a SAS token generated with broad permissions ('rwdlacup') and resource types ('sco'), but there is no evidence in the template that 'funcStorage' (the storage account) enforces 'secure transfer required'. Without 'secure transfer required' enabled, attackers could intercept or manipulate log data in transit, leading to data exfiltration or tampering. The blast radius includes potential exposure of all application logs and sensitive operational data.","Ensure the storage account referenced by 'logs_storage' has 'secure transfer required' enabled. In the storage account resource definition, set 'supportsHttpsTrafficOnly' to true. This enforces HTTPS for all data in transit and mitigates interception or tampering risks.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account)

🔵 Azure Guidance: Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windo...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account),Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windows remote management.,"Enhanced Implementation Context:
• Double encryption for Azure data in transit: https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit
• Encryption in transit overview: https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit
• TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem
• Azure Storage secure transfer: https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account

Compliance Mappings:
• CIS Controls v8: 3.10
• NIST SP800-53 r4: SC-8
• PCI-DSS v3.2.1: 3.5, 3.6, 4.1

Azure Policy Examples:
• Kubernetes clusters should be accessible only over HTTPS
• Only secure connections to your Azure Cache for Redis should be enabled
• FTPS only should be required in your Function App
• Secure transfer to storage accounts should be enabled
• Function App should only be accessible over HTTPS
• Latest TLS version should be used in your API App
• Web Application should only be accessible over HTTPS
• Enforce SSL connection should be enabled for PostgreSQL database servers
• Latest TLS version should be used in your Web App",ai_analysis,,Validated
function.bicep,DP-4,Data Protection,Enable data at rest encryption by default,HIGH,133.0,"The 'funcStorage' resource on line 62 is referenced as an existing storage account, but there is no explicit enforcement of encryption at rest. If the storage account does not have encryption enabled, attackers with access to the underlying storage could read or modify sensitive data, including application logs and diagnostic information. The blast radius includes all data stored in this account.","Verify that the storage account referenced by 'logs_storage' has encryption at rest enabled. In the storage account resource definition, ensure the 'encryption' block is present and configured for all services, using either Microsoft-managed or customer-managed keys.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services) | [Data at rest double encryption](https://docs.microsoft.com/azure/security/fundamentals/encryption-models) | [Azure Disk Encryption](https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview) | [SQL Transparent Data Encryption](https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview)

🔵 Azure Guidance: Many Azure services have data-at-rest encryption enabled by default using service-managed keys. Where not enabled by default enable encryption in Azure services or VMs for storage-level file-level or ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services) | [Data at rest double encryption](https://docs.microsoft.com/azure/security/fundamentals/encryption-models) | [Azure Disk Encryption](https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview) | [SQL Transparent Data Encryption](https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview),Many Azure services have data-at-rest encryption enabled by default using service-managed keys. Where not enabled by default enable encryption in Azure services or VMs for storage-level file-level or database-level encryption.,"Enhanced Implementation Context:
• Encryption at rest in Azure: https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services
• Data at rest double encryption: https://docs.microsoft.com/azure/security/fundamentals/encryption-models
• Encryption model and key management: https://docs.microsoft.com/azure/security/fundamentals/encryption-models
• Azure Disk Encryption: https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview
• SQL Transparent Data Encryption: https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview

Compliance Mappings:
• CIS Controls v8: 3.11
• NIST SP800-53 r4: SC-28
• PCI-DSS v3.2.1: 3.4, 3.5

Azure Policy Examples:
• Virtual machines should encrypt temp disks caches and data flows between Compute and Storage resources
• Transparent Data Encryption on SQL databases should be enabled
• Automation account variables should be encrypted
• Service Fabric clusters should have the ClusterProtectionLevel property set to EncryptAndSign
• Azure Cosmos DB accounts should use customer-managed keys to encrypt data at rest",ai_analysis,,Validated
function.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,133.0,"The 'funcStorage' storage account on line 62 is referenced as an existing resource, but there is no evidence of network access restrictions such as Private Endpoints or service endpoints. If public network access is allowed, attackers could attempt to access storage data from the public internet, increasing the risk of data exfiltration and unauthorized access. The blast radius includes all data in the storage account.","Restrict public network access to the storage account by enabling 'publicNetworkAccess' set to 'Disabled' and configuring Private Endpoints for all required services. Alternatively, use service endpoints to limit access to trusted subnets only.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
hub-network.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,4.0,"The virtual network resource 'vnet' defined at line 003 does not include any Network Security Group (NSG) association for its subnets. Without NSGs, there is no network-level access control, enabling attackers to exploit open network paths for initial access, lateral movement, and data exfiltration. The blast radius includes all resources deployed in this VNet and its subnets, as unrestricted traffic could reach any attached service.","Associate a Network Security Group (NSG) with the 'hub-subnet' by adding an 'networkSecurityGroup' property referencing a properly configured NSG resource. Ensure the NSG has a default deny-all inbound rule and only allows explicitly required traffic.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
instance-config.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,24.0,"The 'address_space' property in 'network_config' (Line 025) is set to '10.0.0.0/8', which is an extremely broad network range. Without explicit evidence of Network Security Groups (NSGs) or deny-by-default rules, this configuration could allow lateral movement and unrestricted internal communication between resources, increasing the blast radius if an attacker gains a foothold. This violates Azure Security Benchmark NS-1, which requires network segmentation and default deny-all policies to minimize attack surface.","Restrict the 'address_space' to the minimum required CIDR range for your workloads. Explicitly define and associate NSGs with deny-by-default inbound rules to all subnets and network interfaces. Only allow necessary ports and protocols. Example: Set 'address_space' to a smaller range (e.g., '10.0.0.0/24') and ensure NSGs are deployed and configured for all subnets and VMs.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
instance-config.bicep,NS-3,Network Security,Use Network Security Groups (NSGs),CRITICAL,24.0,"The template defines a large 'address_space' ('10.0.0.0/8') and 'subnet' ('10.0.0.0/16') in 'network_config' (Lines 025-026) but does not specify any Network Security Group (NSG) rules or deny-by-default configuration. This omission enables an attacker to move laterally within the network if any resource is compromised, as there are no explicit network-level access controls to restrict traffic. This violates Azure Security Benchmark NS-3, which mandates deny-by-default NSG rules.","Define and associate NSGs with all subnets and network interfaces. Configure NSG rules to deny all inbound traffic by default and only allow explicitly required traffic. Example: Add NSG resources with default deny rules and associate them with the subnet defined in 'network_config'.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal) | [Virtual network traffic routing](https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview) | [Azure Firewall Manager](https://docs.microsoft.com/azure/firewall-manager/overview) | [Hub-spoke topology](https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Use Azure Firewall for stateful application layer traffic restriction and central management. Create user-defined routes (UDR) for complex topologies. Block known bad IPs and high-risk protocols.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal) | [Virtual network traffic routing](https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview) | [Azure Firewall Manager](https://docs.microsoft.com/azure/firewall-manager/overview) | [Hub-spoke topology](https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Use Azure Firewall for stateful application layer traffic restriction and central management. Create user-defined routes (UDR) for complex topologies. Block known bad IPs and high-risk protocols.,"Enhanced Implementation Context:
• Azure Firewall deployment: https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal
• Virtual network traffic routing: https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview
• Azure Firewall Manager: https://docs.microsoft.com/azure/firewall-manager/overview
• Hub-spoke topology: https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 4.4, 4.8, 13.10
• NIST SP800-53 r4: AC-4, SC-7, CM-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Management ports should be closed on your virtual machines
• Management ports of virtual machines should be protected with just-in-time network access control
• IP Forwarding on your virtual machine should be disabled
• All Internet traffic should be routed via your deployed Azure Firewall",ai_analysis,,Validated
keyvault.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,28.0,"The 'networkAcls.defaultAction' property is set to 'Allow' for the Key Vault resource. This configuration allows public network access to the Key Vault, enabling attackers to attempt initial access from any IP address. The blast radius includes potential exposure of all secrets and keys stored in the vault, and could allow brute-force or credential stuffing attacks if other controls are weak.","Set 'networkAcls.defaultAction' to 'Deny' to block public network access by default. Only allow access from explicitly defined IP addresses or virtual networks using 'ipRules' and 'virtualNetworkRules'. Consider enabling Private Endpoint for the Key Vault to eliminate public exposure.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
keyvault.bicep,DP-8,Data Protection,Ensure security of key and certificate repository,HIGH,29.0,"The 'networkAcls.bypass' property is set to 'AzureServices', which allows all Azure services to bypass network restrictions and access the Key Vault. This increases the attack surface and enables lateral movement from any compromised Azure service within the tenant, expanding the blast radius to all resources with access to the Key Vault.","Set 'networkAcls.bypass' to 'None' to prevent Azure services from bypassing network restrictions. Only allow access through explicitly defined network rules or private endpoints. Review and restrict access policies and RBAC assignments to enforce least privilege.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/key-vault/general/overview) | [Key Vault security best practices](https://docs.microsoft.com/azure/key-vault/general/best-practices) | [Managed identity Key Vault access](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad) | [Key Vault Private Link](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Key Vault logging and monitoring](https://docs.microsoft.com/azure/key-vault/general/logging)

🔵 Azure Guidance: Secure Azure Key Vault through access policies or Azure RBAC for least privilege. Use Private Link and Azure Firewall for minimal exposure. Implement separation of duties managed identity access and c...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/key-vault/general/overview) | [Key Vault security best practices](https://docs.microsoft.com/azure/key-vault/general/best-practices) | [Managed identity Key Vault access](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad) | [Key Vault Private Link](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Key Vault logging and monitoring](https://docs.microsoft.com/azure/key-vault/general/logging),Secure Azure Key Vault through access policies or Azure RBAC for least privilege. Use Private Link and Azure Firewall for minimal exposure. Implement separation of duties managed identity access and comprehensive logging.,"Enhanced Implementation Context:
• Azure Key Vault overview: https://docs.microsoft.com/azure/key-vault/general/overview
• Key Vault security best practices: https://docs.microsoft.com/azure/key-vault/general/best-practices
• Managed identity Key Vault access: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad
• Key Vault Private Link: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Key Vault logging and monitoring: https://docs.microsoft.com/azure/key-vault/general/logging

Compliance Mappings:
• CIS Controls v8: Not specified
• NIST SP800-53 r4: IA-5, SC-12, SC-17
• PCI-DSS v3.2.1: 3.6

Azure Policy Examples:
• Key vaults should have purge protection enabled
• Azure Defender for Key Vault should be enabled
• Key vaults should have soft delete enabled
• Azure Key Vault should disable public network access
• Private endpoint should be configured for Key Vault
• Resource logs in Key Vault should be enabled
• Implement separation of duties for key management and data access",ai_analysis,,Validated
keyvault.bicep,DP-8,Data Protection,Ensure security of key and certificate repository,HIGH,22.0,"The 'accessPolicies' property is set to an empty array while 'enableRbacAuthorization' is true. If RBAC is not properly scoped, this can result in overly broad access to Key Vault resources, enabling privilege escalation or unauthorized data access. Attackers compromising a privileged Azure AD role could gain full access to all secrets and keys.","Review and restrict Azure RBAC assignments for the Key Vault to ensure only required identities have access. Remove unnecessary Owner or Contributor roles at the subscription or resource group level. Implement least privilege by assigning only the minimum required Key Vault permissions to each identity.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/key-vault/general/overview) | [Key Vault security best practices](https://docs.microsoft.com/azure/key-vault/general/best-practices) | [Managed identity Key Vault access](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad) | [Key Vault Private Link](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Key Vault logging and monitoring](https://docs.microsoft.com/azure/key-vault/general/logging)

🔵 Azure Guidance: Secure Azure Key Vault through access policies or Azure RBAC for least privilege. Use Private Link and Azure Firewall for minimal exposure. Implement separation of duties managed identity access and c...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/key-vault/general/overview) | [Key Vault security best practices](https://docs.microsoft.com/azure/key-vault/general/best-practices) | [Managed identity Key Vault access](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad) | [Key Vault Private Link](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Key Vault logging and monitoring](https://docs.microsoft.com/azure/key-vault/general/logging),Secure Azure Key Vault through access policies or Azure RBAC for least privilege. Use Private Link and Azure Firewall for minimal exposure. Implement separation of duties managed identity access and comprehensive logging.,"Enhanced Implementation Context:
• Azure Key Vault overview: https://docs.microsoft.com/azure/key-vault/general/overview
• Key Vault security best practices: https://docs.microsoft.com/azure/key-vault/general/best-practices
• Managed identity Key Vault access: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad
• Key Vault Private Link: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Key Vault logging and monitoring: https://docs.microsoft.com/azure/key-vault/general/logging

Compliance Mappings:
• CIS Controls v8: Not specified
• NIST SP800-53 r4: IA-5, SC-12, SC-17
• PCI-DSS v3.2.1: 3.6

Azure Policy Examples:
• Key vaults should have purge protection enabled
• Azure Defender for Key Vault should be enabled
• Key vaults should have soft delete enabled
• Azure Key Vault should disable public network access
• Private endpoint should be configured for Key Vault
• Resource logs in Key Vault should be enabled
• Implement separation of duties for key management and data access",ai_analysis,,Validated
scaleset-networks.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,66.0,"The property 'defaultOutboundAccess: true' is set on the subnet configuration. This enables default outbound internet access for all resources in the subnet, bypassing explicit network security group (NSG) controls. Attackers who compromise a resource in this subnet can exfiltrate data or establish command and control channels directly to the internet, increasing the blast radius and enabling lateral movement. This configuration violates the Azure Security Benchmark guidance to restrict traffic between resources and enforce a default deny policy.","Set 'defaultOutboundAccess' to false or remove it entirely to disable default outbound internet access. Attach a Network Security Group (NSG) to the subnet and explicitly allow only required outbound traffic. Example: Remove 'defaultOutboundAccess: true' from the subnet configuration and associate an NSG with deny-by-default rules.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,168.0,"The 'defaultAction' property in the 'networkAcls' block for the 'storageAccountFunc' resource is set to 'Allow' (Line 031). This configuration allows public network access to the storage account unless explicitly denied by IP or VNet rules. Attackers can exploit this to gain initial access to storage resources from any network not explicitly blocked, increasing the risk of data exfiltration and lateral movement. The blast radius includes all data and services within the storage account.","Set 'networkAcls.defaultAction' to 'Deny' to block all public network access by default. Only allow access from explicitly defined IP addresses or virtual networks. Example: Change 'defaultAction' to 'Deny' on Line 031.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,199.0,"The 'defaultAction' property in the 'networkAcls' block for the 'fuzzStorageProperties' object (used by 'storageAccountFuzz' and 'storageAccountsCorpus') is set to 'Allow' (Line 065). This exposes all storage accounts using this property to public network access unless specifically restricted, enabling attackers to access or enumerate storage endpoints from untrusted networks. The blast radius includes all fuzz and corpus storage accounts.","Set 'networkAcls.defaultAction' to 'Deny' in 'fuzzStorageProperties' (Line 065) to ensure storage accounts are not accessible from public networks by default. Only allow access from trusted IPs or VNets.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 19,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 7,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-19T13:12:10.963867,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
