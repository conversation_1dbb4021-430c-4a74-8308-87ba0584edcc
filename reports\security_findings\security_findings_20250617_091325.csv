Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P1-Identity-HIGH,Identity Management,HIGH,IM-2,template.json,38,There is no explicit configuration to enforce Multi-Factor Authentication (MFA) for users or administrators accessing the App Service.,Enforce MFA for all users and administrators by configuring Azure AD authentication with MFA requirements.,N/A,AI,Generic
P1-Identity-MEDIUM,Identity Management,MEDIUM,IM-3,template.json,38,No conditional access policies are defined for the App Service.,Implement Azure AD conditional access policies to enforce secure access to the App Service.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,38,"App Service hostNameSslStates for 'onefuzz-daily-ui.azurewebsites.net' and 'onefuzz-daily-ui.scm.azurewebsites.net' have 'sslState' set to 'Disabled', exposing public endpoints over HTTP without encryption.",Enable HTTPS by setting 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates to secure public endpoints.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,153,"App Service 'ipSecurityRestrictions' allows access from 'Any' IP address with action 'Allow', exposing the application to the public internet.",Restrict 'ipSecurityRestrictions' to only allow trusted IP ranges and deny all others to minimize public exposure.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,163,"App Service 'scmIpSecurityRestrictions' allows access from 'Any' IP address with action 'Allow', exposing the SCM (deployment) endpoint to the public internet.",Restrict 'scmIpSecurityRestrictions' to only allow trusted IP ranges and deny all others to minimize public exposure.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,38,"App Service 'publicNetworkAccess' is set to 'Enabled', allowing public network access instead of using private endpoints.",Set 'publicNetworkAccess' to 'Disabled' and configure a private endpoint to restrict access to the App Service.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,153,"App Service config 'publicNetworkAccess' is set to 'Enabled', allowing public network access instead of using private endpoints.",Set 'publicNetworkAccess' to 'Disabled' in the site config and use private endpoints for secure access.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,153,App Service config 'azureStorageAccounts' is empty and there is no explicit configuration for encryption at rest for any attached storage.,Ensure all attached storage accounts have encryption at rest enabled and explicitly configure encryption settings in the template.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,38,"App Service hostNameSslStates for 'onefuzz-daily-ui.azurewebsites.net' and 'onefuzz-daily-ui.scm.azurewebsites.net' have 'sslState' set to 'Disabled', which means HTTP is allowed and encryption in transit is not enforced for these hostnames.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates to enforce HTTPS and ensure encryption in transit for all endpoints.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,153,"App Service config includes 'publishingUsername', which is sensitive information and should be stored securely in Azure Key Vault.",Remove 'publishingUsername' from the template and reference it securely from Azure Key Vault using Key Vault references.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-6,template.json,38,App Service does not specify the use of customer-managed keys (CMK) for encryption at rest.,Configure the App Service to use customer-managed keys for encryption at rest if handling sensitive or regulated data.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-8,template.json,38,No explicit backup and recovery configuration is present for the App Service.,"Implement backup and recovery strategies for the App Service, such as enabling daily backups.",N/A,AI,Generic
