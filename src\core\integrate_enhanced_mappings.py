#!/usr/bin/env python3
"""
Integration script to update security_opt.py with enhanced resource-control mappings
and URL link extraction from CSV files.
"""

import re
import logging
from pathlib import Path
from enhanced_resource_control_mappings import EnhancedResourceControlMapper

logger = logging.getLogger(__name__)

class SecurityOptIntegrator:
    """
    Integrates enhanced resource-control mappings into the existing security_opt.py file.
    """
    
    def __init__(self):
        self.security_opt_path = Path("security_opt.py")
        self.mapper = EnhancedResourceControlMapper()
        
    def integrate_enhanced_mappings(self):
        """Main integration method to update security_opt.py."""
        if not self.security_opt_path.exists():
            logger.error("security_opt.py file not found")
            return False
        
        try:
            # Read the current file
            with open(self.security_opt_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Update the resource mapping methods
            content = self._update_resource_mapping_methods(content)
            
            # Update the link extraction method
            content = self._update_link_extraction_method(content)
            
            # Add enhanced mapper initialization
            content = self._add_enhanced_mapper_initialization(content)
            
            # Write the updated content back
            with open(self.security_opt_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info("Successfully integrated enhanced mappings into security_opt.py")
            return True
            
        except Exception as e:
            logger.error(f"Error integrating enhanced mappings: {e}")
            return False
    
    def _update_resource_mapping_methods(self, content: str) -> str:
        """Update the resource mapping methods to use enhanced mappings."""
        
        # Generate the new get_controls_for_resource method
        new_method = '''    def get_controls_for_resource(self, resource_type: str) -> List[Dict]:
        """Get comprehensive ASB controls for Azure resource type using enhanced mappings."""
        try:
            if hasattr(self, 'enhanced_mapper') and self.enhanced_mapper:
                # Use enhanced mapper for comprehensive control coverage
                controls = self.enhanced_mapper.get_controls_for_resource_type(resource_type)
                
                # Convert to expected format and add additional details
                formatted_controls = []
                for control in controls:
                    control_details = {
                        "id": control['id'],
                        "domain": control['domain'],
                        "name": control['name'],
                        "security_principle": control.get('security_principle', ''),
                        "azure_guidance": control.get('azure_guidance', ''),
                        "implementation_context": control.get('implementation_context', ''),
                        "stakeholders": control.get('stakeholders', ''),
                        "urls": control.get('urls', [])
                    }
                    formatted_controls.append(control_details)
                
                logger.debug(f"Enhanced mapper returned {len(formatted_controls)} controls for {resource_type}")
                return formatted_controls
            else:
                logger.warning("Enhanced mapper not available, falling back to legacy mappings")
                return self._get_legacy_controls_for_resource(resource_type)
                
        except Exception as e:
            logger.error(f"Error getting controls for resource {resource_type}: {e}")
            return self._get_legacy_controls_for_resource(resource_type)
    
    def _get_legacy_controls_for_resource(self, resource_type: str) -> List[Dict]:
        """Legacy fallback method for resource-control mapping."""
        # Legacy hardcoded mappings as fallback
        legacy_mappings = {
            "Microsoft.Storage/storageAccounts": [
                "DP-1", "DP-2", "DP-3", "DP-4", "DP-5",  # Data protection
                "NS-1", "NS-2", "NS-3",                   # Network security
                "IM-1", "IM-3",                           # Identity management
            ],
            "Microsoft.Network/networkSecurityGroups": [
                "NS-1", "NS-2", "NS-3", "NS-4", "NS-7",   # Network security
                "IM-3",                                   # Identity management
            ],
            "Microsoft.KeyVault/vaults": [
                "DP-1", "DP-2", "DP-5", "DP-6", "DP-7",  # Data protection
                "IM-1", "IM-2", "IM-3",                   # Identity management
            ],
            "Microsoft.Compute/virtualMachines": [
                "IM-1", "IM-2", "IM-4",                   # Identity management
                "NS-1", "NS-3", "NS-7",                   # Network security
                "DP-1", "DP-4",                           # Data protection
            ]
        }
        
        control_ids = legacy_mappings.get(resource_type, [])
        return [{"id": cid, "domain": cid.split('-')[0], "name": f"Control {cid}"} for cid in control_ids]'''
        
        # Replace the existing method
        pattern = r'def get_controls_for_resource\(self, resource_type.*?(?=\n    def |\nclass |\n\n\ndef |\Z)'
        if re.search(pattern, content, re.DOTALL):
            content = re.sub(pattern, new_method, content, flags=re.DOTALL)
        else:
            # If method doesn't exist, add it before the run method
            run_method_pattern = r'(def run\(self\) -> None:)'
            content = re.sub(run_method_pattern, f'{new_method}\n\n    \\1', content)
        
        return content
    
    def _update_link_extraction_method(self, content: str) -> str:
        """Update the link extraction method to use enhanced CSV data."""
        
        new_method = '''    def _extract_control_links(self, control_id: str) -> Dict:
        """
        Extract links and references from enhanced CSV data for a specific control.
        Returns formatted links suitable for tooltips and CSV display.
        """
        try:
            if hasattr(self, 'enhanced_mapper') and self.enhanced_mapper:
                # Use enhanced mapper for comprehensive link extraction
                links_info = self.enhanced_mapper.get_control_links(control_id)
                
                # Ensure all required fields are present
                return {
                    "formatted_links": links_info.get("formatted_links", ""),
                    "azure_guidance": links_info.get("azure_guidance", ""),
                    "implementation_context": links_info.get("implementation_context", ""),
                    "raw_links": links_info.get("raw_links", [])
                }
            else:
                logger.warning("Enhanced mapper not available for link extraction")
                return self._legacy_extract_control_links(control_id)
                
        except Exception as e:
            logger.error(f"Error extracting links for control {control_id}: {e}")
            return {
                "formatted_links": "",
                "azure_guidance": "",
                "implementation_context": "",
                "raw_links": []
            }
    
    def _legacy_extract_control_links(self, control_id: str) -> Dict:
        """Legacy fallback method for link extraction."""
        # Fallback to existing benchmark data
        links_info = {
            "formatted_links": "",
            "azure_guidance": "",
            "implementation_context": "",
            "raw_links": []
        }
        
        if not self.benchmark_data:
            return links_info
        
        # Try to find control in existing benchmark data
        control_data = None
        if self.benchmark_data.get("controls"):
            for control in self.benchmark_data["controls"]:
                if control.get("id") == control_id:
                    control_data = control
                    break
        
        if control_data:
            # Extract basic information
            azure_guidance = control_data.get("azure_guidance", "")
            implementation = control_data.get("implementation", "")
            
            if azure_guidance:
                links_info["azure_guidance"] = azure_guidance.strip()
            
            if implementation:
                links_info["implementation_context"] = implementation.strip()
                
                # Extract URLs using regex
                url_pattern = r'https?://[^\\s\\n\\r]+'
                urls = re.findall(url_pattern, implementation)
                links_info["raw_links"] = urls
                
                if urls:
                    formatted_links = [f"[Reference {i}]({url})" for i, url in enumerate(urls, 1)]
                    links_info["formatted_links"] = " | ".join(formatted_links)
        
        return links_info'''
        
        # Replace the existing _extract_control_links method
        pattern = r'def _extract_control_links\(self, control_id: str\) -> Dict:.*?(?=\n    def |\nclass |\n\n\ndef |\Z)'
        if re.search(pattern, content, re.DOTALL):
            content = re.sub(pattern, new_method, content, flags=re.DOTALL)
        
        return content

    def _add_enhanced_mapper_initialization(self, content: str) -> str:
        """Add enhanced mapper initialization to the SecurityPRReviewer class."""

        # Add import statement at the top
        import_pattern = r'(import logging.*?\n)'
        if 'from enhanced_resource_control_mappings import EnhancedResourceControlMapper' not in content:
            import_addition = 'from enhanced_resource_control_mappings import EnhancedResourceControlMapper\n'
            content = re.sub(import_pattern, f'\\1{import_addition}', content)

        # Add enhanced mapper initialization in __init__ method
        init_pattern = r'(def __init__\(self.*?\n.*?)(self\.benchmark_data = \{\})'
        init_addition = '''        # Initialize enhanced resource-control mapper
        try:
            self.enhanced_mapper = EnhancedResourceControlMapper()
            logger.info("✅ Enhanced resource-control mapper initialized successfully")
        except Exception as e:
            logger.warning(f"⚠️ Enhanced mapper initialization failed: {e}")
            self.enhanced_mapper = None

        '''

        if re.search(init_pattern, content, re.DOTALL):
            content = re.sub(init_pattern, f'\\1{init_addition}\\2', content, flags=re.DOTALL)

        return content

    def create_backup(self):
        """Create a backup of the original security_opt.py file."""
        try:
            backup_path = Path("security_opt_backup.py")
            if self.security_opt_path.exists():
                with open(self.security_opt_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(content)

                logger.info(f"Backup created: {backup_path}")
                return True
        except Exception as e:
            logger.error(f"Error creating backup: {e}")
            return False

    def validate_integration(self):
        """Validate that the integration was successful."""
        try:
            with open(self.security_opt_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Check for required components
            checks = [
                'from enhanced_resource_control_mappings import EnhancedResourceControlMapper',
                'self.enhanced_mapper = EnhancedResourceControlMapper()',
                'def get_controls_for_resource(self, resource_type: str)',
                'def _extract_control_links(self, control_id: str)'
            ]

            results = {}
            for check in checks:
                results[check] = check in content

            all_passed = all(results.values())

            logger.info("Integration validation results:")
            for check, passed in results.items():
                status = "✅" if passed else "❌"
                logger.info(f"  {status} {check}")

            return all_passed

        except Exception as e:
            logger.error(f"Error validating integration: {e}")
            return False


def main():
    """Main function to integrate enhanced mappings."""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    print("🔧 Integrating Enhanced Resource-Control Mappings")
    print("=" * 60)

    try:
        integrator = SecurityOptIntegrator()

        # Create backup
        print("\n💾 Creating backup...")
        if integrator.create_backup():
            print("✅ Backup created successfully")
        else:
            print("❌ Failed to create backup")
            return

        # Perform integration
        print("\n🔧 Integrating enhanced mappings...")
        if integrator.integrate_enhanced_mappings():
            print("✅ Integration completed successfully")
        else:
            print("❌ Integration failed")
            return

        # Validate integration
        print("\n🔍 Validating integration...")
        if integrator.validate_integration():
            print("✅ Integration validation passed")
        else:
            print("⚠️ Integration validation failed - check logs")

        print("\n🎉 Enhanced Resource-Control Mapping Integration completed!")
        print("\nNext steps:")
        print("1. Test the updated security_opt.py")
        print("2. Run enhanced_resource_control_mappings.py to generate mappings")
        print("3. Verify URL links are working in reports")

    except Exception as e:
        print(f"❌ Error: {e}")
        logging.exception("Detailed error information:")


if __name__ == "__main__":
    main()
