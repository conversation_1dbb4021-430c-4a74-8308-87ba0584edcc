"""
Refactored async security analyzer demonstrating improved performance and architecture.
Shows how to implement parallel file analysis with proper error handling and monitoring.
"""

import asyncio
import time
import logging
from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any, Callable
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, Process<PERSON>oolExecutor
from contextlib import asynccontextmanager
from enum import Enum
import aiofiles
from pathlib import Path

# Import our refactored modules
from refactored_resource_detector import CachedResourceTypeAnalyzer, ResourceDetectionResult
from refactored_security_patterns import SecurityFinding, PatternManager, Severity

logger = logging.getLogger(__name__)

class AnalysisStatus(Enum):
    """Status of file analysis."""
    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    SKIPPED = "SKIPPED"

@dataclass
class FileAnalysisTask:
    """Represents a file analysis task."""
    file_path: str
    content: str
    resource_type: Optional[str] = None
    status: AnalysisStatus = AnalysisStatus.PENDING
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    error: Optional[str] = None
    findings: List[SecurityFinding] = field(default_factory=list)
    
    @property
    def duration(self) -> Optional[float]:
        """Get task duration in seconds."""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None

@dataclass
class AnalysisMetrics:
    """Metrics for analysis performance."""
    total_files: int = 0
    analyzed_files: int = 0
    failed_files: int = 0
    skipped_files: int = 0
    total_findings: int = 0
    findings_by_severity: Dict[Severity, int] = field(default_factory=dict)
    total_duration: float = 0.0
    average_file_duration: float = 0.0
    
    def update_from_task(self, task: FileAnalysisTask):
        """Update metrics from a completed task."""
        if task.status == AnalysisStatus.COMPLETED:
            self.analyzed_files += 1
            self.total_findings += len(task.findings)
            
            # Update severity counts
            for finding in task.findings:
                self.findings_by_severity[finding.severity] = \
                    self.findings_by_severity.get(finding.severity, 0) + 1
            
            # Update duration
            if task.duration:
                self.total_duration += task.duration
                self.average_file_duration = self.total_duration / self.analyzed_files
                
        elif task.status == AnalysisStatus.FAILED:
            self.failed_files += 1
        elif task.status == AnalysisStatus.SKIPPED:
            self.skipped_files += 1

class AsyncSecurityAnalyzer:
    """Asynchronous security analyzer with improved performance."""
    
    def __init__(
        self,
        max_workers: int = 5,
        resource_mappings: Dict = None,
        openai_client: Any = None,
        benchmark_data: Dict = None,
        use_process_pool: bool = False
    ):
        self.max_workers = max_workers
        self.resource_mappings = resource_mappings or {}
        self.openai_client = openai_client
        self.benchmark_data = benchmark_data
        
        # Choose executor type based on configuration
        if use_process_pool:
            self.executor = ProcessPoolExecutor(max_workers=max_workers)
        else:
            self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # Initialize components
        self.resource_analyzer = CachedResourceTypeAnalyzer(self.resource_mappings)
        self.pattern_manager = PatternManager()
        self.metrics = AnalysisMetrics()
        
        # Semaphore to limit concurrent analyses
        self.semaphore = asyncio.Semaphore(max_workers)
        
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    async def analyze_files(
        self,
        files: List[Dict],
        progress_callback: Optional[Callable[[FileAnalysisTask], None]] = None
    ) -> List[SecurityFinding]:
        """Analyze multiple files asynchronously."""
        start_time = time.time()
        
        # Create tasks for all files
        tasks = []
        for file_info in files:
            task = FileAnalysisTask(
                file_path=file_info.get("path", "unknown"),
                content=file_info.get("content", ""),
                resource_type=file_info.get("resource_type")
            )
            tasks.append(task)
        
        self.metrics.total_files = len(tasks)
        
        # Analyze files concurrently
        analysis_coroutines = [
            self._analyze_file_with_progress(task, progress_callback)
            for task in tasks
        ]
        
        # Wait for all analyses to complete
        await asyncio.gather(*analysis_coroutines, return_exceptions=True)
        
        # Collect all findings
        all_findings = []
        for task in tasks:
            if task.status == AnalysisStatus.COMPLETED:
                all_findings.extend(task.findings)
            
            # Update metrics
            self.metrics.update_from_task(task)
        
        # Log summary
        total_time = time.time() - start_time
        self.logger.info(
            f"Analysis complete: {self.metrics.analyzed_files}/{self.metrics.total_files} files "
            f"analyzed in {total_time:.2f}s ({self.metrics.average_file_duration:.2f}s avg)"
        )
        self.logger.info(
            f"Found {self.metrics.total_findings} issues: "
            f"{self.metrics.findings_by_severity}"
        )
        
        return all_findings
    
    async def _analyze_file_with_progress(
        self,
        task: FileAnalysisTask,
        progress_callback: Optional[Callable[[FileAnalysisTask], None]] = None
    ):
        """Analyze a single file with progress tracking."""
        async with self.semaphore:  # Limit concurrent analyses
            try:
                task.status = AnalysisStatus.IN_PROGRESS
                task.start_time = time.time()
                
                if progress_callback:
                    await asyncio.create_task(
                        asyncio.to_thread(progress_callback, task)
                    )
                
                # Perform analysis
                findings = await self._analyze_single_file(task)
                
                task.findings = findings
                task.status = AnalysisStatus.COMPLETED
                
            except Exception as e:
                task.status = AnalysisStatus.FAILED
                task.error = str(e)
                self.logger.error(f"Failed to analyze {task.file_path}: {e}")
                
            finally:
                task.end_time = time.time()
                
                if progress_callback:
                    await asyncio.create_task(
                        asyncio.to_thread(progress_callback, task)
                    )
    
    async def _analyze_single_file(self, task: FileAnalysisTask) -> List[SecurityFinding]:
        """Analyze a single file for security issues."""
        findings = []
        
        # Step 1: Determine resource type if not provided
        if not task.resource_type:
            loop = asyncio.get_event_loop()
            detection_result = await loop.run_in_executor(
                self.executor,
                self.resource_analyzer.analyze,
                task.file_path,
                task.content
            )
            task.resource_type = detection_result.detected_type
            
            self.logger.debug(
                f"Detected resource type for {task.file_path}: "
                f"{task.resource_type} (confidence: {detection_result.confidence:.2f})"
            )
        
        # Step 2: Run pattern-based detection
        loop = asyncio.get_event_loop()
        pattern_findings = await loop.run_in_executor(
            self.executor,
            self._run_pattern_detection,
            task
        )
        findings.extend(pattern_findings)
        
        # Step 3: Run AI-based analysis if configured
        if self.openai_client and self.benchmark_data:
            try:
                ai_findings = await self._run_ai_analysis(task)
                findings.extend(ai_findings)
            except Exception as e:
                self.logger.warning(f"AI analysis failed for {task.file_path}: {e}")
        
        # Remove duplicates
        findings = self._deduplicate_findings(findings)
        
        return findings
    
    def _run_pattern_detection(self, task: FileAnalysisTask) -> List[SecurityFinding]:
        """Run pattern-based security detection."""
        detector = self.pattern_manager.get_detector()
        return detector.detect_issues(
            task.file_path,
            task.content,
            task.resource_type
        )
    
    async def _run_ai_analysis(self, task: FileAnalysisTask) -> List[SecurityFinding]:
        """Run AI-based security analysis."""
        # This would integrate with Azure OpenAI
        # For now, returning empty list as placeholder
        return []
    
    def _deduplicate_findings(self, findings: List[SecurityFinding]) -> List[SecurityFinding]:
        """Remove duplicate findings based on file, line, and control ID."""
        seen = set()
        unique_findings = []
        
        for finding in findings:
            key = (finding.file_path, finding.line, finding.control_id)
            if key not in seen:
                seen.add(key)
                unique_findings.append(finding)
        
        return unique_findings
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit - cleanup resources."""
        self.executor.shutdown(wait=True)

class AnalysisOrchestrator:
    """Orchestrates the entire analysis workflow with monitoring."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    async def analyze_pr_files(
        self,
        files: List[Dict],
        progress_ui: Optional[Any] = None
    ) -> Dict[str, Any]:
        """Analyze PR files and return comprehensive results."""
        results = {
            "findings": [],
            "metrics": None,
            "errors": [],
            "summary": {}
        }
        
        # Create progress callback if UI is provided
        progress_callback = None
        if progress_ui:
            progress_callback = lambda task: progress_ui.update(task)
        
        try:
            # Create analyzer
            async with AsyncSecurityAnalyzer(
                max_workers=self.config.get("max_workers", 5),
                resource_mappings=self.config.get("resource_mappings"),
                openai_client=self.config.get("openai_client"),
                benchmark_data=self.config.get("benchmark_data")
            ) as analyzer:
                
                # Run analysis
                findings = await analyzer.analyze_files(files, progress_callback)
                
                # Store results
                results["findings"] = findings
                results["metrics"] = analyzer.metrics
                
                # Generate summary
                results["summary"] = self._generate_summary(findings, analyzer.metrics)
                
        except Exception as e:
            self.logger.error(f"Analysis orchestration failed: {e}")
            results["errors"].append(str(e))
        
        return results
    
    def _generate_summary(
        self,
        findings: List[SecurityFinding],
        metrics: AnalysisMetrics
    ) -> Dict[str, Any]:
        """Generate analysis summary."""
        return {
            "total_files": metrics.total_files,
            "analyzed_files": metrics.analyzed_files,
            "failed_files": metrics.failed_files,
            "total_findings": len(findings),
            "critical_findings": sum(1 for f in findings if f.severity == Severity.CRITICAL),
            "high_findings": sum(1 for f in findings if f.severity == Severity.HIGH),
            "medium_findings": sum(1 for f in findings if f.severity == Severity.MEDIUM),
            "low_findings": sum(1 for f in findings if f.severity == Severity.LOW),
            "analysis_duration": metrics.total_duration,
            "average_file_duration": metrics.average_file_duration
        }

# Example usage
async def main():
    """Example usage of async analyzer."""
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    
    # Example files
    files = [
        {
            "path": "storage1.tf",
            "content": """
            resource "azurerm_storage_account" "example1" {
                enable_https_traffic_only = false
            }
            """
        },
        {
            "path": "storage2.tf",
            "content": """
            resource "azurerm_storage_account" "example2" {
                enable_https_traffic_only = true
                min_tls_version = "TLS1_2"
            }
            """
        }
    ]
    
    # Create orchestrator
    config = {
        "max_workers": 3,
        "resource_mappings": {
            "Storage": {
                "terraform_types": ["azurerm_storage_account"],
                "keywords": ["storage"]
            }
        }
    }
    
    orchestrator = AnalysisOrchestrator(config)
    
    # Run analysis
    results = await orchestrator.analyze_pr_files(files)
    
    # Print results
    print("\nAnalysis Results:")
    print(f"Total findings: {results['summary']['total_findings']}")
    print(f"Critical: {results['summary']['critical_findings']}")
    print(f"High: {results['summary']['high_findings']}")
    print(f"Duration: {results['summary']['analysis_duration']:.2f}s")
    
    for finding in results['findings']:
        print(f"\n{finding.severity.value} - {finding.file_path}:{finding.line}")
        print(f"  {finding.description}")

if __name__ == "__main__":
    asyncio.run(main())
