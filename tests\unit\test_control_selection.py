#!/usr/bin/env python3
"""
Test script to debug control selection and verify why NS-5, NS-6, and Identity controls are missing
"""

import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_control_selection_for_resources():
    """Test what controls are being selected for different resource types."""
    print("🔍 Testing Control Selection for Azure Resource Types")
    print("=" * 80)
    
    try:
        from security_opt import SecurityPRReviewer
        
        reviewer = SecurityPRReviewer(local_folder=".")
        
        # Test different resource types that should trigger missing controls
        test_resources = [
            "Microsoft.Storage/storageAccounts",
            "Microsoft.Network/networkSecurityGroups", 
            "Microsoft.Network/virtualNetworks",
            "Microsoft.Network/applicationGateways",
            "Microsoft.Web/sites",
            "Microsoft.KeyVault/vaults",
            "Microsoft.Sql/servers",
            "Generic"  # Fallback resource type
        ]
        
        print("Testing control selection for each resource type:")
        print("-" * 80)
        
        for resource_type in test_resources:
            print(f"\n🔍 Resource Type: {resource_type}")
            print("-" * 50)
            
            try:
                # Test enhanced mapper directly
                if hasattr(reviewer, 'enhanced_mapper') and reviewer.enhanced_mapper:
                    enhanced_controls = reviewer.enhanced_mapper.get_controls_for_resource_type(resource_type)
                    print(f"📋 Enhanced Mapper: {len(enhanced_controls)} controls")
                    
                    # Show control IDs
                    control_ids = [c['id'] for c in enhanced_controls]
                    print(f"   Control IDs: {', '.join(sorted(control_ids)[:10])}{'...' if len(control_ids) > 10 else ''}")
                    
                    # Check for missing critical controls
                    missing_critical = []
                    critical_controls = ["IM-1", "IM-2", "IM-3", "IM-6", "IM-8", "NS-5", "NS-6", "DP-3", "DP-4"]
                    for critical in critical_controls:
                        if critical not in control_ids:
                            missing_critical.append(critical)
                    
                    if missing_critical:
                        print(f"   ❌ Missing Critical: {', '.join(missing_critical)}")
                    else:
                        print(f"   ✅ All critical controls present")
                else:
                    print("   ❌ Enhanced mapper not available")
                
                # Test the main control selection method
                relevant_controls = reviewer._find_relevant_controls(resource_type)
                print(f"📋 Final Selection: {len(relevant_controls)} controls")
                
                final_control_ids = [c['id'] for c in relevant_controls]
                print(f"   Final Control IDs: {', '.join(sorted(final_control_ids)[:10])}{'...' if len(final_control_ids) > 10 else ''}")
                
                # Check for missing critical controls in final selection
                missing_final = []
                for critical in critical_controls:
                    if critical not in final_control_ids:
                        missing_final.append(critical)
                
                if missing_final:
                    print(f"   ❌ Missing in Final: {', '.join(missing_final)}")
                else:
                    print(f"   ✅ All critical controls in final selection")
                
                # Check domain distribution
                domain_counts = {}
                for control in relevant_controls:
                    domain = control.get('domain', 'Unknown')
                    domain_counts[domain] = domain_counts.get(domain, 0) + 1
                
                print(f"   📊 Domain Distribution: {dict(domain_counts)}")
                
            except Exception as e:
                print(f"   ❌ Error testing {resource_type}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        logger.exception("Detailed error:")
        return False

def test_critical_controls_generation():
    """Test the critical controls generation logic."""
    print(f"\n🎯 Testing Critical Controls Generation")
    print("=" * 80)
    
    try:
        from security_opt import SecurityPRReviewer
        
        reviewer = SecurityPRReviewer(local_folder=".")
        
        # Test critical controls by domain
        critical_controls = reviewer._get_critical_controls_by_domain()
        
        print("Generated critical controls by domain:")
        print("-" * 50)
        
        expected_controls = {
            "Identity Management": ["IM-1", "IM-2", "IM-3", "IM-6", "IM-8"],
            "Network Security": ["NS-1", "NS-2", "NS-3", "NS-5", "NS-6"],
            "Data Protection": ["DP-1", "DP-2", "DP-3", "DP-4", "DP-6"]
        }
        
        for domain, controls in critical_controls.items():
            print(f"\n🔒 {domain}:")
            print(f"   Generated: {', '.join(controls) if controls else 'None'}")
            
            if domain in expected_controls:
                expected = expected_controls[domain]
                missing = [c for c in expected if c not in controls]
                extra = [c for c in controls if c not in expected]
                
                if missing:
                    print(f"   ❌ Missing: {', '.join(missing)}")
                if extra:
                    print(f"   ➕ Extra: {', '.join(extra)}")
                if not missing and not extra:
                    print(f"   ✅ Perfect match!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_domain_priority_config():
    """Test the domain priority configuration."""
    print(f"\n🔢 Testing Domain Priority Configuration")
    print("=" * 80)
    
    try:
        from security_opt import SecurityPRReviewer
        
        reviewer = SecurityPRReviewer(local_folder=".")
        
        print("Domain priority configuration:")
        print("-" * 40)
        
        for domain, config in reviewer.domain_priority_config.items():
            prefix = config['prefix']
            priority = config['priority']
            range_info = config['range']
            
            print(f"{priority}. {domain} ({prefix}-{range_info[0]} to {prefix}-{range_info[1]})")
            
            # Check if controls exist in control index
            controls_in_index = []
            for i in range(range_info[0], range_info[1] + 1):
                control_id = f"{prefix}-{i}"
                if control_id in reviewer.control_id_index:
                    controls_in_index.append(control_id)
            
            print(f"   📋 Controls in index: {', '.join(controls_in_index) if controls_in_index else 'None'}")
            
            if not controls_in_index:
                print(f"   ❌ No controls found in index for {domain}!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_benchmark_data_loading():
    """Test if benchmark data is loaded correctly."""
    print(f"\n📚 Testing Benchmark Data Loading")
    print("=" * 80)
    
    try:
        from security_opt import SecurityPRReviewer
        
        reviewer = SecurityPRReviewer(local_folder=".")
        
        if not reviewer.benchmark_data:
            print("❌ No benchmark data loaded!")
            return False
        
        controls = reviewer.benchmark_data.get("controls", [])
        print(f"📋 Total controls loaded: {len(controls)}")
        
        # Check for specific missing controls
        control_ids = [c.get('id', '') for c in controls]
        missing_controls = []
        critical_test_controls = ["IM-1", "IM-6", "IM-8", "NS-5", "NS-6", "DP-3", "DP-4"]
        
        for control_id in critical_test_controls:
            if control_id not in control_ids:
                missing_controls.append(control_id)
        
        if missing_controls:
            print(f"❌ Missing critical controls in benchmark data: {', '.join(missing_controls)}")
        else:
            print(f"✅ All critical test controls present in benchmark data")
        
        # Show domain distribution
        domain_counts = {}
        for control in controls:
            domain = control.get('domain', 'Unknown')
            domain_counts[domain] = domain_counts.get(domain, 0) + 1
        
        print(f"\n📊 Domain distribution in benchmark data:")
        for domain, count in sorted(domain_counts.items()):
            print(f"   {domain}: {count} controls")
        
        # Check control ID index
        print(f"\n🔍 Control ID Index: {len(reviewer.control_id_index)} controls")
        
        # Test specific controls
        test_controls = ["IM-6", "NS-5", "NS-6", "DP-3"]
        for control_id in test_controls:
            if control_id in reviewer.control_id_index:
                control_info = reviewer.control_id_index[control_id]
                print(f"   ✅ {control_id}: {control_info.get('name', 'No name')}")
            else:
                print(f"   ❌ {control_id}: Not in control index")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main test function."""
    print("🔍 CONTROL SELECTION DEBUGGING")
    print("=" * 80)
    print("Investigating why NS-5, NS-6, and Identity controls are missing from analysis")
    print("=" * 80)
    
    success = True
    
    # Test benchmark data loading
    if not test_benchmark_data_loading():
        print("❌ Benchmark data loading test failed")
        success = False
    
    # Test domain priority configuration
    if not test_domain_priority_config():
        print("❌ Domain priority configuration test failed")
        success = False
    
    # Test critical controls generation
    if not test_critical_controls_generation():
        print("❌ Critical controls generation test failed")
        success = False
    
    # Test control selection for resources
    if not test_control_selection_for_resources():
        print("❌ Control selection test failed")
        success = False
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 CONTROL SELECTION DEBUGGING COMPLETE!")
        print("✅ All tests passed - control selection should be working correctly")
        print()
        print("If you're still missing controls in your analysis, the issue might be:")
        print("1. AI prompt not generating recommendations for available controls")
        print("2. Resource type detection not working correctly")
        print("3. File content not triggering the right resource types")
    else:
        print("❌ CONTROL SELECTION ISSUES DETECTED!")
        print("Please review the test results above to identify the root cause")

if __name__ == "__main__":
    main()
