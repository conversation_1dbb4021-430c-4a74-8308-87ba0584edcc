[{"file_path": "test_storage.tf", "line": 10, "matching_content": "enable_https_traffic_only = false", "severity": "HIGH", "control_id": "DP-3", "description": "Storage account allows HTTP traffic, which is insecure. HTTPS should be enforced.", "remediation": "Set enable_https_traffic_only = true to enforce HTTPS."}, {"file_path": "test_storage.tf", "line": 11, "matching_content": "min_tls_version          = \"TLS1_0\"", "severity": "MEDIUM", "control_id": "DP-4", "description": "Storage account uses an outdated TLS version which has known vulnerabilities.", "remediation": "Set min_tls_version = \"TLS1_2\" to enforce stronger encryption."}, {"file_path": "test_storage.tf", "line": 13, "matching_content": "network_rules {", "severity": "HIGH", "control_id": "NS-1", "description": "Storage account allows access from all networks. This creates unnecessary security exposure.", "remediation": "Set default_action = \"Deny\" and explicitly allow only necessary networks."}, {"control_id": "DP-3", "severity": "HIGH", "line": 9, "description": "The 'enable_https_traffic_only' property is set to false, allowing unencrypted HTTP connections to the storage account. This exposes data in transit to interception and tampering.", "remediation": "Set 'enable_https_traffic_only' to true to enforce encrypted connections to the storage account.", "file_path": "test_storage.tf"}, {"control_id": "DP-2", "severity": "HIGH", "line": 10, "description": "The 'min_tls_version' is set to 'TLS1_0', which is an outdated and insecure protocol version vulnerable to multiple attacks.", "remediation": "Set 'min_tls_version' to 'TLS1_2' or higher to ensure secure communication.", "file_path": "test_storage.tf"}, {"control_id": "NS-2", "severity": "HIGH", "line": 12, "description": "The 'network_rules' block has 'default_action' set to 'Allow', which permits all network traffic (including from the public internet) to access the storage account. This increases the risk of unauthorized access.", "remediation": "Set 'default_action' to 'Deny' and explicitly allow only trusted networks or services to access the storage account.", "file_path": "test_storage.tf"}, {"file_path": "test_keyvault.bicep", "line": 13, "matching_content": "networkAcls: {", "severity": "HIGH", "control_id": "NS-1", "description": "Storage account allows access from all networks. This creates unnecessary security exposure.", "remediation": "Set defaultAction: 'Deny' and explicitly allow only necessary networks."}, {"control_id": "NS-2", "severity": "HIGH", "line": 13, "description": "The 'networkAcls.defaultAction' property is set to 'Allow', which permits public network access to the Key Vault. This exposes the Key Vault to potential unauthorized access from the internet.", "remediation": "Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only trusted networks or private endpoints.", "file_path": "test_keyvault.bicep"}, {"control_id": "DP-3", "severity": "HIGH", "line": 9, "description": "Soft delete is not enabled for the Key Vault. Without soft delete, deleted secrets, keys, and certificates cannot be recovered, increasing the risk of accidental or malicious data loss.", "remediation": "Set 'enableSoftDelete' to true to ensure deleted items can be recovered.", "file_path": "test_keyvault.bicep"}, {"control_id": "DP-3", "severity": "HIGH", "line": 10, "description": "Purge protection is not enabled for the Key Vault. Without purge protection, a malicious actor could permanently delete (purge) secrets, keys, or certificates, even if soft delete is enabled.", "remediation": "Set 'enablePurgeProtection' to true to prevent permanent deletion of Key Vault objects.", "file_path": "test_keyvault.bicep"}]