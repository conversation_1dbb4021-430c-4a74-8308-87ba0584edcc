Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
CRITICAL,NS-1,LacpGeo.Template.json,135,"The Key Vault resource does not restrict network access, lacking Virtual Network service endpoints or private endpoints. This leaves the Key Vault accessible from any public IP, which is not compliant with ASB network security guidelines and exposes sensitive secrets and keys.","Configure the Key Vault to be accessible only from approved private IP addresses, subnets, or via Private Endpoint. Add 'networkAcls' to the Key Vault properties to restrict public access and enable default action 'Deny' for non-specified networks.",N/A,AI,Generic
CRITICAL,NS-8,LacpGeo.Template.json,234,"Azure Cosmos DB has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to 'false', which means the database is accessible over the public Internet. This increases attack surface for data exfiltration and breaches.",Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to 'true' for Cosmos DB. Configure specific 'virtualNetworkRules' or use Private Endpoint to limit access to trusted networks only.,N/A,AI,Generic
CRITICAL,NS-2,LacpGeo.Template.json,237,"Azure Cosmos DB has no virtual network rules and no IP rules; therefore, the service is fully exposed to the public Internet, violating the requirement to protect public endpoints.",Configure 'virtualNetworkRules' and/or 'ipRules' on the Cosmos DB account. Limit access to required Azure subnets and specific trusted IP ranges only.,N/A,AI,Generic
CRITICAL,NS-1,LacpGlobal.Template.json,52,"The Cosmos DB account ('Microsoft.DocumentDB/databaseAccounts') has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to 'false', exposing the database to the public Internet without any virtual network restrictions. This leaves the database accessible from anywhere, violating network segmentation best practices.","Set 'publicNetworkAccess' to 'Disabled' or 'isVirtualNetworkFilterEnabled' to 'true', and define proper 'virtualNetworkRules' and/or 'ipRules' to restrict access to trusted networks only as required by ASB NS-1.",N/A,AI,Generic
CRITICAL,NS-2,LacpGlobal.Template.json,52,No virtual network restrictions or IP-based access rules are set for the Cosmos DB account; both 'virtualNetworkRules' and 'ipRules' arrays are empty. This means the resource is publicly accessible from any internet address by default.,"Add IP firewall rules under 'ipRules' to restrict access to only trusted IP addresses or enable 'virtualNetworkRules' to limit access to selected subnets, as mandated by ASB NS-2.",N/A,AI,Generic
CRITICAL,NS-1,LacpRegion.Template.json,1092,"The Cosmos DB account sets 'publicNetworkAccess' to 'Enabled' and 'isVirtualNetworkFilterEnabled' to false, meaning the account is open to the public internet with no network restrictions, violating the control to use NSGs or Azure Firewall for protection.",Set 'publicNetworkAccess' to 'Disabled' or 'isVirtualNetworkFilterEnabled' to 'true' and define appropriate 'virtualNetworkRules' to restrict access to trusted networks only. Implement appropriate NSG or firewall rules to reduce exposure.,N/A,AI,Generic
CRITICAL,NS-2,LacpRegion.Template.json,1092,Cosmos DB resource is exposed to the public Internet with no IP restrictions or VNet rules enforced. 'ipRules' and 'virtualNetworkRules' are empty with public access enabled.,"Restrict public access by setting 'publicNetworkAccess' to 'Disabled', defining allowed IP addresses in 'ipRules', or enforcing virtual network rules to ensure only trusted networks can access the Cosmos DB.",N/A,AI,Generic
CRITICAL,DP-3,LacpStamp.Parameters-LacpStampResources.json,67,"The parameter 'dasStorageAccountKey' contains a direct reference to a storage account key output, potentially exposing sensitive storage access credentials within deployment parameters. According to ASB DP-3, sensitive data such as storage keys must be securely managed using Azure Key Vault and not passed inline in templates or parameters.",Store the storage account key securely in Azure Key Vault and reference it from the template using a secureObject parameter type. Update your deployment process to retrieve sensitive values dynamically from Key Vault at runtime rather than supplying them directly in parameter files.,N/A,AI,Generic
CRITICAL,DP-3,LacpStamp.Parameters-LacpStampResources.json,66,"The parameter 'dasStorageAccountName' is defined using a service resource deployment output and may leak sensitive details about the storage account. While names are generally less sensitive than keys, exposing them in template parameters can aid attackers in reconnaissance.","Avoid exposing storage account names in publicly accessible or widely distributed files. If necessary, retrieve resource identifiers securely using Azure Key Vault references where possible.",N/A,AI,Generic
HIGH,NS-1,IngestionStorageAccount.Template.json,46,"The storage account resources do not have any network restrictions such as networkRules defined. By default, Azure Storage accounts allow traffic from all networks (public endpoint enabled), which exposes the storage accounts to the internet and increases the attack surface for unauthorized access.","Add the 'networkAcls' property to explicitly restrict access to the storage account. Set 'defaultAction' to 'Deny', and allow trusted subnets or private endpoints as appropriate. Example: 'networkAcls': { 'defaultAction': 'Deny', 'bypass': 'AzureServices', 'ipRules': [], 'virtualNetworkRules': [] }.",N/A,AI,Generic
HIGH,NS-2,IngestionStorageAccount.Template.json,46,"Public network access is not disabled for the storage accounts, which means the storage endpoints are accessible over the internet. This exposure can be exploited if authentication or other defenses fail.","Explicitly set 'publicNetworkAccess' to 'Disabled' in the storage account properties to prevent public access. If public access is necessary, restrict it to specific IP addresses or virtual networks using 'networkAcls'.",N/A,AI,Generic
HIGH,NS-1,LacpBilling.Template.json,619,"The storage accounts are deployed without any network restrictions. There is no use of private endpoints, network rules, or service endpoints, nor are there Network Security Groups (NSGs) or Azure Firewall mentioned to restrict access. Thus, the storage accounts may be accessible from any network, violating ASB control NS-1.","Configure the storage account with network rules to allow access only from approved virtual networks or IP address ranges. Use private endpoints where feasible, and consider integrating with Azure Firewall or NSGs to strictly control allowed traffic.",N/A,AI,Generic
HIGH,NS-6,LacpBilling.Template.json,619,"Storage account resources are created without configuring 'networkAcls' to restrict public network access, nor is there any mention of using private endpoints. This leaves the storage account endpoints potentially accessible over public internet, increasing the risk of data exposure.","Set the 'networkAcls' property on storage accounts to restrict public network access, allowing traffic only from specific trusted subnets. Consider enabling private endpoints to provide access only over your trusted virtual network.",N/A,AI,Generic
HIGH,NS-2,LacpBilling.Template.json,619,"The storage accounts have not explicitly enabled network rules to restrict access to public endpoints. If default network settings are not changed, the storage account endpoints might be public.","Set the storage account's 'networkAcls.defaultAction' to 'Deny' and explicitly allow access only from necessary, trusted subnets or public IPs. Where possible, use private endpoints to fully remove public access.",N/A,AI,Generic
HIGH,NS-1,LacpBillingExhaust.Template.json,44,"The Microsoft.Kusto cluster resource (ADX) is being deployed without any explicit network security controls (NSG, firewall rules, or private endpoints). By default, Azure Data Explorer (ADX) clusters are accessible over the public network unless network restrictions are configured.","Configure the ADX cluster with either a private endpoint or restrict public network access using IP firewall rules. If required, place the cluster within a VNET and apply Network Security Groups (NSGs) and disable public network access to minimize exposure.",N/A,AI,Generic
HIGH,NS-2,LacpBillingExhaust.Template.json,44,The ADX cluster resource does not specify any protection for public endpoints. This introduces the risk of exposing the service to the internet.,"Explicitly configure the ADX cluster's 'publicNetworkAccess' property to 'Disabled' or restrict allowed IPs to only trusted sources. Alternatively, use private endpoints for all access and ensure no public IP addresses are exposed.",N/A,AI,Generic
HIGH,DP-3,LacpBillingExhaustExport.Template.json,8,"Parameters 'adxExhaustDataIngestionUri' and 'adxExhaustUri' are defined as plain string parameters. If secrets such as keys, tokens, or connection strings are passed directly as parameter values, this may risk accidental exposure of sensitive information. This violates best practices for managing sensitive data, which recommend using Azure Key Vault.","Refactor the template to reference sensitive secrets (e.g., database URIs, connection strings, or authentication tokens) from Azure Key Vault using secure reference parameters instead of plain strings. Document for users that these parameters must not contain credentials unless secured.",N/A,AI,Generic
HIGH,NS-1,LacpGlobal.Template.json,193,"Storage accounts ('Microsoft.Storage/storageAccounts') lack network restriction properties (such as 'networkAcls'). There is no reference to any Network Security Groups (NSGs), virtual network rules, or firewall settings, making the storage accounts publicly accessible by default.","Implement 'networkAcls' to restrict access by IP address or virtual network, and consider limiting public network access by setting 'publicNetworkAccess' to 'Disabled', in line with ASB NS-1.",N/A,AI,Generic
HIGH,NS-2,LacpGlobal.Template.json,193,"Public network access is not restricted for Storage Accounts, and there are no NSGs or firewall rules specified, exposing the accounts to the internet.","Set 'publicNetworkAccess' to 'Disabled' for storage accounts unless absolutely necessary, and define 'networkAcls' to limit public endpoint exposure, as per ASB NS-2.",N/A,AI,Generic
HIGH,NS-2,LacpRegion.Parameters-LacpRegionResources.json,16,"The parameter 'isBoundariesRestricted' is set to 'false', indicating that network boundary restrictions are not enforced. This could result in Logic Apps and associated resources being accessible from the public internet, exposing them to unauthorized access or attacks.","Set 'isBoundariesRestricted' to 'true' to enforce network boundaries. Restrict public access to Logic Apps and associated storage accounts by enabling private endpoints, service endpoints, or using network security groups (NSGs) to restrict allowed IPs and subnets.",N/A,AI,Generic
HIGH,DP-3,LacpRegion.Parameters-LacpRegionResources.json,45,"Resource names related to storage and sensitive environments appear in clear text ('amsBackupStorageAccountName', 'dasCustomerConfigStorageAccountName', etc.), but there is no evidence that secrets, connection strings, or sensitive credentials are being securely managed via Azure Key Vault. Storing secrets or credentials in plain text parameters is a security risk.","Ensure all secrets, connection strings, and sensitive data required by Logic Apps and other resources are referenced from Azure Key Vault, not embedded in parameter files or code, even for deployment purposes. Use Key Vault references where needed.",N/A,AI,Generic
HIGH,NS-1,LacpRegion.Parameters-LacpRegionResources.json,1,"This parameter file does not include or reference any network security controls (such as Network Security Groups, Private Endpoints, or Azure Firewall) for protecting Logic Apps and related storage accounts. Lack of network controls may result in public exposure.","In the resource deployment template, ensure that NSGs, private endpoints, or Azure Firewall are in place to restrict access to Logic Apps and dependent services (storage, Key Vault, etc.). Update the parameters or associated templates to require secure network configuration.",N/A,AI,Generic
HIGH,NS-1,LacpRegion.Template.json,666,"Storage accounts (e.g., 'dataPullerEventHubStorageAccountName') are created with no network access restrictions (no 'networkAcls' block present), which means by default public network access is enabled, violating the requirement for network protections.","Add the 'networkAcls' property to each storage account, set 'defaultAction' to 'Deny', and explicitly allow only required subnets and trusted IP ranges via 'virtualNetworkRules' or 'ipRules'.",N/A,AI,Generic
HIGH,NS-2,LacpRegion.Template.json,666,Public endpoints for storage accounts are not protected; missing explicit restriction on network access leaves storage accounts publicly reachable.,"Restrict public network access for storage accounts by setting 'networkAcls.defaultAction' to 'Deny'. Allow access only from trusted VNets and IPs, or set 'publicNetworkAccess' to 'Disabled'.",N/A,AI,Generic
HIGH,DP-3,LacpRegion.Template.json,1803,"Storage account keys and Cosmos DB keys are retrieved with listKeys() and written as plaintext values in Key Vault secrets. Exposure of these connection strings increases the risk if Key Vault access policies are too permissive. Additionally, the connection string is constructed including the account key within the template logic.","Whenever possible, prefer use of Azure AD authentication for services rather than distributing Storage/Cosmos DB account keys anywhere, even in Key Vault. If keys are needed, restrict Key Vault access policies to only trusted identities following least privilege. Rotate keys regularly.",N/A,AI,Generic
HIGH,DP-3,LacpRegion.Template.json,2766,The template output section exports the storage account name and its primary key directly as outputs. This can potentially expose sensitive information to users or automation tools consuming the output.,"Remove storage account key from template outputs. Instead, retrieve secrets at runtime by authorized identities from Azure Key Vault. If output is necessary for automation, ensure output channels are secured and limit access only to required and trusted systems.",N/A,AI,Generic
HIGH,NS-3,LacpRegion.Template.json,666,There are no Network Security Groups (NSGs) deployed or associated with any subnets that might have access to storage accounts. This increases exposure to unintended inbound/outbound traffic.,"Deploy NSGs and associate them with relevant subnets. Define rules that restrict traffic to/from storage accounts and other sensitive resources, based on least-privilege access.",N/A,AI,Generic
HIGH,NS-1,LacpStamp.Template.json,605,"The Microsoft.Storage/storageAccounts resources do not employ any network restrictions, such as using 'networkAcls' to limit access to selected networks. Allowing unrestricted access exposes the storage accounts to the public internet and increases the risk of unauthorized access.","Update each storage account resource to define 'networkAcls' with 'defaultAction' set to 'Deny', and explicitly allow trusted virtual networks and IP addresses only. Consider leveraging Private Endpoints for sensitive storage resources per ASB NS-1.",N/A,AI,Generic
HIGH,NS-2,LacpStamp.Template.json,605,"Storage Accounts (Microsoft.Storage/storageAccounts) are created without network restrictions or private endpoints, resulting in publicly accessible endpoints by default. This exposes storage to internet traffic.","Restrict public network access by configuring 'networkAcls' with 'defaultAction':'Deny' and, where possible, use Azure Private Endpoints to allow access only through internal networks.",N/A,AI,Generic
HIGH,NS-1,ReadAdxExhaust.Template.json,17,"The Kusto cluster resource does not specify any network security configuration such as private endpoints, VNet integration, or network access restrictions. This means the cluster is exposed with default public endpoints, violating the control to protect critical resources using NSGs or Azure Firewall.","Configure the Kusto cluster to disable public network access and enable private endpoints, or restrict access using network security groups (NSGs) or Azure Firewall. Add the `publicNetworkAccess` property set to `Disabled` and integrate with a virtual network.",N/A,AI,Generic
HIGH,NS-2,ReadAdxExhaust.Template.json,17,"The deployment enables default public endpoints for the Kusto (ADX) cluster by omitting explicit endpoint configuration, which means cluster APIs may be accessible from the internet.","Explicitly disable public network access by setting the `publicNetworkAccess` property to 'Disabled', and provision a private endpoint to restrict access to authorized networks only.",N/A,AI,Generic
HIGH,IM-6,RoleAssignment.Template.json,60,The service principal parameter 'Ev2BuildoutServicePrincipalId' is assigned the 'Contributor' role at the subscription scope without evidence of required scope limitation or minimal permissions. Assigning broad 'Contributor' rights to a service principal may violate least privilege and RBAC best practices.,"Review the permissions required by the service principal and assign only the minimum necessary access at the lowest viable scope (e.g., specific resource group or resource instead of subscription level). Consider using custom roles if built-in roles are too broad.",N/A,AI,Generic
HIGH,IM-7,RoleAssignment.Template.json,60,Assigning 'Contributor' access to application or service principals ('Ev2BuildoutServicePrincipalId') increases security risk if the principal credentials are not properly protected or if excessive permissions are granted.,"Ensure that service principals follow strong credential hygiene (e.g., use certificate or managed identity authentication, restrict credential visibility, rotate credentials regularly). Limit role assignment to the minimum necessary permissions and regularly review application identities.",N/A,AI,Generic
HIGH,AM-1,RoleAssignment.Template.json,60,"The role assignment grants blanket 'Contributor' access to a principal, potentially violating the principle of least privilege.","Restrict access by splitting permissions into more targeted roles (e.g., use built-in roles with reduced permissions or custom roles) and assign only those necessary for task fulfillment. Apply at lowest possible scope.",N/A,AI,Generic
HIGH,NS-2,TrafficManagerEndpoints.Template.json,47,"Traffic Manager external endpoints are created with public DNS names derived from cluster parameters (e.g., '<prefix>-<index>-<suffix>.<region>.<domain>'). The template does not apply any access restriction, authentication, or validation of endpoint exposure, which could result in unintentional public exposure.","Validate whether these endpoints must be publicly accessible. Restrict access to necessary IP ranges or front-end these endpoints with additional security controls (e.g., Azure Firewall, Web Application Firewall, or authentication solutions). If possible, implement network-level restrictions at the target resource level to limit unwanted public access.",N/A,AI,Generic
MEDIUM,NS-7,LacpBilling.Template.json,619,No Network Security Groups (NSGs) are referenced or associated with any network resource related to the storage accounts. NSGs should be used to control the network traffic (inbound/outbound) to your resources.,Associate the subnets used by storage accounts or by any consumers of this storage with Network Security Groups to ensure only approved network traffic is allowed.,N/A,AI,Generic
MEDIUM,IM-8,LacpBilling.Template.json,680,Role assignments are granted to managed identities at the storage account scope but there is no documentation or RBAC configuration ensuring these identities have only the minimum permissions required. Principle of least privilege should be enforced for all role assignments.,"Review the managed identity and ensure the role assignment 'Storage Queue Data Contributor' is strictly necessary. Assign only the minimum set of roles and scopes necessary for the application's function, and avoid broad role assignments.",N/A,AI,Generic
MEDIUM,IM-8,LacpBillingExhaust.Template.json,84,"Principal assignments for Azure Data Explorer (ADX) are based on provided object IDs, and use principalType 'App', but there is no evidence that managed identities are used for these assignments. Hardcoding application/service principal IDs can be less secure than managed identities.","Prefer using managed identities for Azure resources for authentication where possible. If the consuming resource supports managed identity, assign role-based access to it instead of using generic service principal objectIDs.",N/A,AI,Generic
MEDIUM,AM-1,LacpBillingExhaust.Template.json,100,"Multiple assignments are made giving 'User' and 'Ingestor' roles to principal IDs at the database level, but the template does not limit the privileges or justify why these roles are necessary. There's a risk of excessive permissions.",Regularly review and limit privileged roles (such as 'User' and 'Ingestor') assigned to only those identities strictly requiring them. Remove unnecessary assignments and follow the principle of least privilege.,N/A,AI,Generic
MEDIUM,DP-1,LacpBillingExhaust.Template.json,44,"There is no explicit configuration ensuring encryption at rest for the Azure Data Explorer (ADX) cluster or associated resources. While encryption at rest is enabled by default in Azure, best practice is to use customer-managed keys (CMK) if regulated or sensitive data is processed.","If the cluster processes regulated or sensitive data, enable customer-managed keys (CMK) for encryption at rest by specifying 'keyVaultProperties' in the ADX cluster resource. Otherwise, document that default Microsoft-managed encryption is used.",N/A,AI,Generic
MEDIUM,DP-3,LacpBillingExhaust.Template.json,123,Sensitive group identity 'aadgroup=<EMAIL>' is assigned in-script for database access. Disclosure of this setting may expose sensitive details such as internal group names or email addresses.,"Do not expose sensitive group or identity details in code. If necessary, reference identities using Azure AD object IDs, and store sensitive settings in a secure parameters file or Key Vault.",N/A,AI,Generic
MEDIUM,NS-2,LacpBillingExhaustExport.Template.json,57,"The template provisions data exports to an Azure Data Explorer (ADX) cluster but does not specify any network security configuration. Without explicit controls for VNET integration or private endpoints, these resources or their data transfers might be exposed over public endpoints.","Ensure that ADX clusters and associated resources are configured with private endpoints or VNET integration, and restrict public network access. Update deployment templates to optionally accept subnet and private endpoint configuration parameters.",N/A,AI,Generic
MEDIUM,DP-1,LacpBillingExhaustExport.Template.json,57,"The template does not explicitly enforce encryption settings for data exports or at-rest storage in ADX or intermediate Azure resources. Without explicit settings, encryption defaults may be used but customer-managed keys (CMK) or advanced encryption options are not enforced.","Update resource configuration to specify encryption settings explicitly. If ADX or related storage is used, document and ensure that at-rest encryption is enabled per resource, and use customer-managed keys where applicable.",N/A,AI,Generic
MEDIUM,IM-6,LacpGeo.Template.json,143,"The Key Vault uses access policies instead of Azure RBAC, which does not align with the latest RBAC model and best practices for centralized and scalable access management.",Transition to using Azure RBAC for Key Vault to manage access at the Azure AD role level. Set 'enableRbacAuthorization': true in the Key Vault properties and migrate access control to role assignments.,N/A,AI,Generic
MEDIUM,AM-1,LacpGeo.Template.json,146,"Key Vault access policies grant both 'Get' and 'List' permissions for keys and secrets to multiple principals. Granting 'List' is broader than usually required, and least-privilege principle recommends only providing required access.",Review and restrict Key Vault access policies to only those permissions needed by each principal. Remove 'List' permissions unless explicitly required.,N/A,AI,Generic
MEDIUM,NS-3,LacpGlobal.Template.json,193,There is no evidence of Network Security Groups (NSGs) being implemented for the storage accounts to control access at the network layer.,Deploy NSGs with restrictive inbound and outbound rules on any subnet associated with storage accounts to enforce least privilege and network segmentation. Reference: ASB NS-3.,N/A,AI,Generic
MEDIUM,AM-1,LacpGlobal.Template.json,242,"Key Vault access policies for some principals grant broad permissions including 'Get', 'List', and for some, 'Set', 'Delete', 'Recover', 'Backup', and 'Restore' to keys and secrets. This may not follow the least privilege principle.","Review and minimize Key Vault access policy permissions. Only grant each identity the specific permissions required for its role. For example, service principals that only retrieve secrets should not have 'Set', 'Delete', 'Recover', or management rights. Reference: ASB AM-1.",N/A,AI,Generic
MEDIUM,DP-3,LacpGlobal.Template.json,456,"Cosmos DB primary master key and Storage Account connection strings, which are sensitive secrets, are programmatically stored into Key Vault via inline template code. If template source control or deployment logs are not properly protected, secrets exposure is possible.","Ensure template files are not stored in public or insecure repositories, and deployment logs/output are securely protected and not exposing the key/secret values. Consider using secure deployment practices, not exposing returned values in logs, and rotating credentials after automation runs. Reference: ASB DP-3.",N/A,AI,Generic
MEDIUM,DP-2,LacpRegion.Parameters-LacpRegionResources.json,39,"The parameter 'minimalCosmosDbTlsVersion' is set to 'Tls12', which is currently acceptable. However, there is no evidence in this parameter file that TLS enforcement settings are applied to all relevant endpoints, e.g., for Logic Apps, Storage Accounts, or other data resources (besides Cosmos DB).","Explicitly enforce TLS 1.2 or higher for all relevant resources (e.g., Logic Apps, Storage Accounts, Event Hubs) at resource configuration level in your deployment templates and policy. Periodically review supported TLS versions to phase out weaker protocols.",N/A,AI,Generic
MEDIUM,IM-8,LacpRegion.Parameters-LacpRegionResources.json,7,"The deployment parameters reference service principals directly (e.g., 'lacpAadServicePrincipal', 'ev2BuildoutAppObjectId', 'cosmosDbEsgPrincipalId', 'azureDeployAppId'). Managed Identities should be used for Logic Apps for secure and automatic identity management instead of direct use of service principals or their object IDs.",Configure Logic Apps and supporting resources to use System-Assigned or User-Assigned Managed Identities instead of embedding service principal object IDs. Assign required permissions using managed identities as per the Least Privilege principle.,N/A,AI,Generic
MEDIUM,DP-1,LacpRegion.Template.json,666,Storage account resources do not specify customer-managed keys (CMKs) for encryption at rest; only default encryption is assumed.,Enable customer-managed keys (CMK) for storage accounts by specifying 'encryption.keySource' as 'Microsoft.Keyvault' and referencing an Azure Key Vault. This is recommended for highly sensitive data.,N/A,AI,Generic
MEDIUM,AM-1,LacpRegion.Template.json,84,"Role assignments for storage/data access (e.g., StorageBlobDataContributor, StorageQueueDataContributor, StorageTableDataContributor) are applied to a managed identity at the resource group or subscription level (based on variables), which might exceed the least-privilege principle if not properly scoped.","Review and restrict the scope of role assignments so they apply to the minimum necessary resource (e.g., only to the specific storage account). Remove unused roles and regularly audit assignments.",N/A,AI,Generic
MEDIUM,DP-3,LacpStamp.Parameters-LacpStampResources.json,80,"The parameter 'globalKeyVaultName' contains an explicit Key Vault resource name. While this itself is not immediately a secret, referencing Key Vault names in clear text parameter files can aid attackers in targeting or reconnaissance.","Consider using environment-specific variables and obscured resource naming strategies. If scripts or deployments require the Key Vault name, retrieve it securely within CI/CD pipelines rather than exposing it in parameter files.",N/A,AI,Generic
MEDIUM,NS-3,LacpStamp.Template.json,605,"There are no Network Security Groups (NSGs) or subnet assignments present for the storage accounts, which means traffic is not controlled at the network layer.","Deploy storage accounts within subnets that are protected by NSGs configured with least-privilege rules, or, where Private Endpoints are used, ensure the private endpoint subnet is protected by an NSG.",N/A,AI,Generic
MEDIUM,DP-1,LacpStamp.Template.json,605,"There is no explicit configuration for enabling customer-managed keys (CMK) for encryption at rest in storage accounts, which could limit compliance with strict data protection requirements.","Where required by policy, enable encryption with customer-managed keys by setting up 'encryption' block in the storage account properties with appropriate keyVaultUri and keyName.",N/A,AI,Generic
MEDIUM,IM-1,LacpStamp.Template.json,605,"Storage account access is not integrated with Azure AD for data plane operations (e.g., RBAC or Azure AD authentication for blobs/queues/tables/files is not enabled), and access keys are distributed via Key Vault.","Enable Azure Active Directory authentication for storage services (blobs, queues, tables) by setting 'AzureADAuthentication' where possible, and grant access via RBAC instead of distributing account keys.",N/A,AI,Generic
MEDIUM,AM-1,LacpStamp.Template.json,358,"The Key Vault access policies and Role Assignments in the template grant extensive permissions to several identities, including 'lacpAadObjectId', with permissions such as 'Get', 'List', 'Update', 'Create', 'Import', 'Delete', and 'Recover' for keys and secrets. This may violate the least privilege principle if those permissions are not strictly necessary for operational requirements.","Review all Role Assignments and Key Vault access policies; grant only the minimum set of permissions required for each principal to perform its function. Limit destructive operations (e.g., 'Delete', 'Purge') to trusted administrators.",N/A,AI,Generic
MEDIUM,IM-8,ReadAdxExhaust.Template.json,17,"The template does not provision a managed identity for the Kusto cluster resource. Managed identities are recommended for secure authentication between Azure resources, reducing the risk associated with credentials in code.",Add a managed identity configuration to the Kusto cluster by including an `identity` block in the resource definition. Assign required permissions to the managed identity via Azure RBAC.,N/A,AI,Generic
MEDIUM,DP-1,ReadAdxExhaust.Template.json,17,"There is no explicit configuration for encryption at rest for the Kusto cluster. By default, Kusto uses platform-managed keys, but customer-managed keys (CMK) are recommended for greater control and compliance.",Enable customer-managed keys for encryption at rest by specifying the `keyVaultProperties` property referencing an Azure Key Vault and Key. Ensure Key Vault is properly secured.,N/A,AI,Generic
MEDIUM,IM-8,ReadUsageAccount.Template.json,34,"The Logic App deployment outputs a reference to a system-assigned managed identity principal ID, but there is no 'identity' property assigned to the 'Microsoft.UsageBilling/accounts' resource itself. This implies that managed identity is referenced but not actually enabled, which prevents secure resource-to-resource authentication using Managed Identity as recommended by the benchmark.",Add an 'identity' block with 'type': 'SystemAssigned' to the resource definition for 'Microsoft.UsageBilling/accounts' to enable a managed identity. Remove the output if the managed identity is not intended or ensure it is configured if required.,N/A,AI,Generic
MEDIUM,IM-5,RoleAssignment.Template.json,1,"Role assignments are being created, but there is no evidence in this template that identity or role assignment activities are being logged for audit and security purposes (e.g., via diagnostic settings).","Enable and configure Azure Activity Logs or resource diagnostic settings to capture and monitor identity and access changes, including role assignments. Regularly review logs for unusual activity.",N/A,AI,Generic
MEDIUM,AM-3,RoleAssignment.Template.json,60,Privileged role assignments (such as 'Contributor') are not integrated with Azure Privileged Identity Management (PIM) in this template. This increases the risk of permanent privileged access.,"Where possible, use Azure Privileged Identity Management (PIM) to make privileged role assignments (like Contributor) eligible and time-bound rather than permanent.",N/A,AI,Generic
LOW,DP-1,LacpBilling.Template.json,626,"Customer-managed keys (CMK) for encryption at rest are not configured for storage accounts, only the default encryption-at-rest is implicitly used. For highly confidential workloads, using customer-managed keys is recommended over Microsoft-managed keys.",Enable encryption using a customer-managed key by adding the 'encryption' property with the relevant Key Vault or managed HSM key details to the storage account resource.,N/A,AI,Generic
LOW,DP-3,LacpBilling.Template.json,0,"There is no evidence that sensitive data (such as secrets, keys, passwords) is being stored in Azure Key Vault. The template does not attempt to use key vaults for secure storage of secrets or keys.","Store any sensitive information required by the deployment, such as connection strings or secret data, in Azure Key Vault. Reference those secrets via secure parameters or access policies rather than inline or hardcoded within IaC.",N/A,AI,Generic
LOW,DP-2,LacpBilling.Template.json,626,"While the template enforces TLS1_2 for storage account minimum TLS version, it is critical to ensure that all communications to the storage account require encryption in transit and that older TLS versions are not allowed.",Continue to enforce 'minimumTlsVersion' as 'TLS1_2' and consider periodically auditing TLS settings for all storage accounts to ensure legacy protocols are blocked.,N/A,AI,Generic
LOW,DP-2,LacpBillingExhaust.Template.json,44,"The template does not specify policies for enforcing TLS 1.2+ for data in transit to the Azure Data Explorer cluster, which may allow lower version TLS connections.","Ensure all communications to Azure Data Explorer enforce TLS 1.2 or higher. If configurable, set allowed TLS versions to 1.2+ and update client services accordingly.",N/A,AI,Generic
LOW,DP-6,LacpGeo.Template.json,244,"Key Vault SKU is set to 'Standard,' and there is no evidence of Customer-Managed Keys (CMK) or HSM-backed keys for Cosmos DB encryption. CMK is recommended for sensitive workloads requiring stricter data control.","Consider using Key Vault Premium SKU for supporting HSM-protected keys, and enable Cosmos DB encryption with a customer-managed key stored in Key Vault if required by compliance or business needs.",N/A,AI,Generic
LOW,DP-1,LacpGeo.Template.json,244,Cosmos DB encryption at rest is not explicitly configured using customer-managed keys (CMK); it defaults to Microsoft-managed keys. Use of CMK provides additional control over encryption keys for data at rest.,Configure Cosmos DB to use customer-managed keys (CMK) stored in Azure Key Vault to encrypt data at rest according to your organization's security and compliance policies.,N/A,AI,Generic
LOW,DP-1,LacpGlobal.Template.json,193,"Encryption at rest is not explicitly referenced for Storage Accounts, though Azure enables this by default. If custom keys or key management is in scope, this should be specified.",Verify that customer-managed keys (CMK) are used for storage account encryption at rest where required for compliance. Reference: ASB DP-1.,N/A,AI,Generic
LOW,DP-2,LacpGlobal.Template.json,201,"The storage account specifies 'minimumTlsVersion' as 'TLS1_2' and 'supportsHttpsTrafficOnly' as true, which aligns with encryption in transit best practices. No issue found, but ensure for all deployed storage accounts these are enforced.",Continually enforce 'minimumTlsVersion' >= 'TLS1_2' and 'supportsHttpsTrafficOnly': true for all storage accounts.,N/A,AI,Generic
LOW,DP-2,LacpRegion.Template.json,678,"All configured storage accounts have 'minimumTlsVersion' set to 'TLS1_2' and 'supportsHttpsTrafficOnly' set to true, complying with best practices. No explicit violation found, but ensure all downstream services and clients connecting to storage enforce TLS 1.2+.",Confirm all client applications connecting to storage accounts enforce TLS 1.2 or higher.,N/A,AI,Generic
LOW,DP-2,LacpStamp.Template.json,609,"The storage accounts explicitly set 'minimumTlsVersion' to 'TLS1_2' and 'supportsHttpsTrafficOnly' to true, which is in line with best practice; no violation found in the provided configuration.",None required. Continue to enforce TLS 1.2 or greater and HTTPS-only traffic.,N/A,AI,Generic
LOW,DP-3,LacpStamp.Template.json,815,"Sensitive connection strings and account keys for storage accounts are being stored in Azure Key Vault as secrets, which aligns with best practices for secret management.",Ensure access to the Key Vault is tightly controlled and audited; periodically rotate secrets and review Key Vault access policies for least privilege.,N/A,AI,Generic
LOW,DP-1,LacpStamp.Template.json,884,"Azure Redis Cache resources do not explicitly specify customer-managed keys (CMK) for encryption. By default, Azure-managed keys are used, which meets baseline encryption requirements but may not satisfy stricter compliance needs.","If regulatory or enterprise policy requires, configure Azure Cache for Redis with customer-managed keys by referencing a Key Vault-managed key.",N/A,AI,Generic
LOW,DP-3,ReadAdxExhaust.Template.json,17,"The template outputs the cluster URI and data ingestion URI, but does not handle sensitive data such as keys, passwords, or secrets directly in the template. However, it is important to ensure that outputs do not inadvertently expose access tokens or other secrets if parameters are extended in future.",Review all parameter and output values to ensure sensitive information is managed via Azure Key Vault and never outputted or hardcoded in templates.,N/A,AI,Generic
