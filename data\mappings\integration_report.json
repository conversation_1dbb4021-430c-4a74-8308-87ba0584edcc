{"integration_status": "SUCCESS", "timestamp": "2025-06-19", "summary": {"total_controls": 27, "controls_with_urls": 27, "resource_categories": 8, "csv_files_processed": 3}, "domain_breakdown": {"Identity Management": 9, "Network Security": 10, "Data Protection": 8}, "resource_coverage": {"Storage": {"applicable_controls": 27, "arm_types": 13, "primary_domains": ["Data Protection", "Network Security", "Identity Management"]}, "KeyVault": {"applicable_controls": 17, "arm_types": 8, "primary_domains": ["Identity Management", "Data Protection"]}, "SQL": {"applicable_controls": 27, "arm_types": 33, "primary_domains": ["Data Protection", "Identity Management", "Network Security"]}, "Network": {"applicable_controls": 19, "arm_types": 34, "primary_domains": ["Network Security", "Identity Management"]}, "Compute": {"applicable_controls": 27, "arm_types": 21, "primary_domains": ["Identity Management", "Network Security", "Data Protection"]}, "AppService": {"applicable_controls": 27, "arm_types": 57, "primary_domains": ["Identity Management", "Network Security", "Data Protection"]}, "Container": {"applicable_controls": 27, "arm_types": 23, "primary_domains": ["Identity Management", "Network Security", "Data Protection"]}, "CosmosDB": {"applicable_controls": 27, "arm_types": 25, "primary_domains": ["Data Protection", "Network Security", "Identity Management"]}}, "url_statistics": {"total_urls": 132, "controls_with_urls": 27}}