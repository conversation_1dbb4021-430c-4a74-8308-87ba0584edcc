File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
network_demo.tf,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,24.0,"The security_rule in azurerm_network_security_group.demo allows inbound SSH (port 22) from any source (source_address_prefix = ""0.0.0.0/0""), exposing the resource to the entire internet. This enables initial access for attackers, allowing brute-force or credential stuffing attacks, and can be used as a foothold for lateral movement within the network. The blast radius includes any VM or resource associated with this NSG, potentially compromising the entire subnet or resource group.","Restrict the source_address_prefix for SSH to trusted IP ranges only (e.g., your corporate IP or a jump host). Change source_address_prefix from ""0.0.0.0/0"" to a specific CIDR block representing trusted sources. Additionally, consider removing direct SSH access and using Azure Bastion or Just-In-Time VM access for secure management.",,,,ai_analysis,,Validated
network_demo.tf,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,43.0,"The allow_blob_public_access property in azurerm_storage_account.demo is set to true, enabling public anonymous access to blobs. This exposes all data in public containers to the internet, allowing attackers to enumerate, read, and exfiltrate sensitive data without authentication. The blast radius includes all data stored in public containers within this storage account.",Set allow_blob_public_access to false to disable anonymous public access. Use private endpoints and Azure RBAC to restrict access to authorized users and services only. Regularly audit storage account access policies to ensure no public exposure.,,,,ai_analysis,,Validated
network_demo.tf,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,46.0,"The https_traffic_only property in azurerm_storage_account.demo is set to false, allowing unencrypted HTTP connections. This exposes data in transit to interception, man-in-the-middle attacks, and credential theft, enabling attackers to read or modify sensitive data as it traverses the network.",Set https_traffic_only to true to enforce encrypted HTTPS connections for all access to the storage account. This ensures data in transit is protected from interception and tampering.,,,,ai_analysis,,Validated
storage_demo.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,26.0,"The 'minimumTlsVersion' property is set to 'TLS1_0', which enables weak encryption for data in transit. Attackers can exploit known vulnerabilities in TLS 1.0 to intercept or modify data between clients and the storage account, leading to data exfiltration or tampering. The blast radius includes all data transferred to and from this storage account.",Set 'minimumTlsVersion' to 'TLS1_2' or higher in the storage account properties to enforce strong encryption for data in transit. Example: minimumTlsVersion: 'TLS1_2'.,,,,ai_analysis,,Validated
storage_demo.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,23.0,"The 'supportsHttpsTrafficOnly' property is set to false, allowing unencrypted HTTP traffic to the storage account. This exposes data in transit to interception and man-in-the-middle attacks, enabling attackers to steal or manipulate sensitive information. The blast radius includes all data accessed or uploaded via HTTP.",Set 'supportsHttpsTrafficOnly' to true in the storage account properties to enforce HTTPS-only access. Example: supportsHttpsTrafficOnly: true.,,,,ai_analysis,,Validated
storage_demo.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,17.0,"The 'allowBlobPublicAccess' property is set to true, enabling public anonymous access to blobs in the storage account. This allows any unauthenticated user on the internet to read data, creating a direct data exfiltration vector. The blast radius is all blob data in the storage account.",Set 'allowBlobPublicAccess' to false in the storage account properties to disable anonymous public access. Example: allowBlobPublicAccess: false.,,,,ai_analysis,,Validated
storage_demo.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,40.0,"The 'networkAcls.defaultAction' property is set to 'Allow', which permits all network traffic (including from the public internet) to access the storage account unless explicitly denied. This exposes the storage account to unauthorized access, lateral movement, and data exfiltration. The blast radius is the entire storage account and any data it contains.","Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only trusted networks or private endpoints. Example: networkAcls: { defaultAction: 'Deny', ... }.",,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 7,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 0,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T15:55:01.910630,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
