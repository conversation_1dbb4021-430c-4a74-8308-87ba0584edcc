#!/usr/bin/env python3
"""
Simple runner script for IaC Guardian security analysis.
This script handles the Python path setup and runs the analysis.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src" / "core"))

# Now import and run the security analysis
if __name__ == "__main__":
    # Change to the src/core directory for imports
    os.chdir(project_root / "src" / "core")
    from security_opt import SecurityPRReviewer
    import argparse
    
    parser = argparse.ArgumentParser(description='IaC Guardian Security Analysis')
    parser.add_argument('--local-folder', required=True, help='Path to folder containing IaC files')
    parser.add_argument('--output-format', default='html', choices=['html', 'csv', 'both'], 
                       help='Output format (default: html)')
    
    args = parser.parse_args()
    
    print(f"🔍 Starting IaC Guardian security analysis...")
    print(f"📁 Analyzing folder: {args.local_folder}")
    
    try:
        # Initialize the security reviewer in local mode
        reviewer = SecurityPRReviewer(local_folder=args.local_folder)
        
        # Run the analysis
        print("🚀 Running security analysis...")
        files = reviewer.analyze_folder(args.local_folder)

        if files:
            print(f"📁 Found {len(files)} files to analyze")

            # Use graph-enhanced analysis for better security recommendations
            print("🔗 Building resource dependency graph...")
            findings = reviewer.analyze_files_with_graph(files)

            if findings:
                print(f"📊 Found {len(findings)} security findings with dependency context")

                # Show cascade risk summary
                cascade_risks = [f.get("cascade_risk", "LOW") for f in findings]
                risk_summary = {risk: cascade_risks.count(risk) for risk in set(cascade_risks)}
                print(f"🔥 Cascade risk summary: {risk_summary}")

                # Export the findings to reports
                # Get project root (two levels up from src/core/)
                project_root = Path(__file__).parent
                output_dir = project_root / "reports" / "security_findings"

                print(f"📄 Generating reports with dependency graph...")
                reviewer.export_findings(findings, args.output_format, str(output_dir))
                print(f"✅ Graph-enhanced analysis completed successfully!")
                print(f"📄 Reports with dependency visualization generated in: {output_dir}")
            else:
                print("⚠️ No security findings detected")
        else:
            print("⚠️ No files found for analysis")
            
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        sys.exit(1)
