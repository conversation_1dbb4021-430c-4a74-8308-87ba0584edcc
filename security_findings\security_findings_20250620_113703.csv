File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
Parameters\LacpBillingExhaust.Parameters-LacpBillingExhaust.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'usageAccountSystemAssignedIdentityPrincipalId' flows across template boundary,Secure parameter 'usageAccountSystemAssignedIdentityPrincipalId' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyVaultSuffix' flows across template boundary,Secure parameter 'keyVaultSuffix' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'regionalCosmosAccountLocation' flows across template boundary,Secure parameter 'regionalCosmosAccountLocation' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'dasStorageAccountKey' flows across template boundary,Secure parameter 'dasStorageAccountKey' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'globalKeyVaultName' flows across template boundary,Secure parameter 'globalKeyVaultName' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'createSharedKeyVault' flows across template boundary,Secure parameter 'createSharedKeyVault' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,40.0,"The parameter 'isBoundariesRestricted' is set to 'false' on line 41, indicating that network boundaries or segmentation restrictions are disabled. This configuration enables a broad attack surface for lateral movement, as workloads are not isolated in segmented virtual networks. An attacker gaining access to one resource could potentially move laterally across the environment, increasing the blast radius and risk of compromise.",Set the 'isBoundariesRestricted' parameter to 'true' to enforce network segmentation boundaries. Ensure that all workloads are deployed in isolated virtual networks with appropriate Network Security Groups (NSGs) and deny-by-default rules. Review and update network architecture to align with enterprise segmentation strategy as per Azure Security Benchmark NS-1.,,,,ai_analysis,,Validated
Templates\IngestionStorageAccount.Template.json,DP-1,Data Protection,Discover classify and label sensitive data,MEDIUM,60.0,"The storage account resource at Line 060 lacks configuration for data discovery, classification, or sensitivity labeling. This increases the risk that sensitive data is not properly inventoried or protected, potentially leading to data leakage or regulatory violations.",Deploy Azure Purview or a similar data classification tool to scan and label data in all storage accounts. Apply sensitivity labels and ensure ongoing discovery and classification processes are in place.,,,,ai_analysis,,Validated
Templates\LacpGeo.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,225.0,"The CosmosDB resource at line 225 has 'publicNetworkAccess' set to 'Enabled', which allows access from the public internet. This configuration exposes the database to initial access attacks, brute force attempts, and data exfiltration, significantly increasing the blast radius if credentials are compromised. Attackers can directly target the CosmosDB endpoint from anywhere on the internet.",Set 'publicNetworkAccess' to 'Disabled' in the CosmosDB resource configuration to restrict access to private endpoints only. Implement Azure Private Link and configure virtual network rules to ensure only trusted networks can access the database. Example: 'publicNetworkAccess': 'Disabled'.,,,,ai_analysis,,Validated
Templates\LacpGeo.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,228.0,"The CosmosDB resource at line 228 has 'isVirtualNetworkFilterEnabled' set to 'false', which disables virtual network filtering. This allows connections from any network, including untrusted sources, enabling lateral movement and increasing the risk of unauthorized access and data exfiltration.",Set 'isVirtualNetworkFilterEnabled' to 'true' and define 'virtualNetworkRules' to restrict access to only approved subnets. This enforces network boundaries and reduces the attack surface. Example: 'isVirtualNetworkFilterEnabled': true.,,,,ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,105.0,"CosmosDB resource 'publicNetworkAccess' is set to 'Enabled' at line 105. This allows public network access to the CosmosDB account, exposing it to the internet and enabling initial access, lateral movement, and potential data exfiltration. Attackers can exploit this exposure to bypass internal network controls and directly target the database, increasing the blast radius to all data stored in the CosmosDB account.","Set 'publicNetworkAccess' to 'Disabled' in the CosmosDB resource definition at line 105. Additionally, configure private endpoints and restrict access to trusted networks only. Reference: Azure Security Benchmark NS-2 (Secure cloud services with network controls).",,,,ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,108.0,"CosmosDB resource 'isVirtualNetworkFilterEnabled' is set to 'false' at line 108. This disables virtual network filtering, allowing connections from any network, including the public internet. Attackers can exploit this to access the database from untrusted networks, increasing the risk of unauthorized access and data compromise.",Set 'isVirtualNetworkFilterEnabled' to 'true' in the CosmosDB resource definition at line 108. Define appropriate 'virtualNetworkRules' to restrict access to trusted subnets. Reference: Azure Security Benchmark NS-2 (Secure cloud services with network controls).,,,,ai_analysis,,Validated
Templates\LacpRegion.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,561.0,"The CosmosDB resource at line 558 has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false. This exposes the CosmosDB account to the public internet, allowing attackers to attempt direct access, brute force, or exploit vulnerabilities, increasing the risk of data exfiltration and lateral movement. The blast radius includes all data in the CosmosDB account and any services with access to it.","Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to true. Configure 'virtualNetworkRules' to allow only trusted subnets. Deploy a private endpoint for CosmosDB to restrict access to private networks only, following Azure guidance for NS-2.",,,,ai_analysis,,Validated
Templates\ReadAdxExhaust.Template.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,23.0,"The Microsoft.Kusto/clusters resource defined at line 023 does not specify any network controls such as private endpoints or restrictions on public network access. By default, Azure Data Explorer (Kusto) clusters are accessible over the public internet unless explicitly restricted. This configuration enables an initial access attack vector, allowing attackers to attempt unauthorized access, brute force, or exploit vulnerabilities over the public endpoint. The blast radius includes potential exposure of all data and services within the cluster, and could allow lateral movement to other resources if the cluster is compromised.",Restrict public network access by setting the 'publicNetworkAccess' property to 'Disabled' and configure a private endpoint for the Kusto cluster. Update the resource definition to include 'publicNetworkAccess': 'Disabled' and deploy a private endpoint resource to ensure only trusted networks can access the cluster. Reference Azure guidance for Private Link and network access restrictions for Kusto clusters.,,,,ai_analysis,,Validated
Templates\RoleAssignment.Template.json,IM-2,Identity Management,Protect identity and authentication systems,HIGH,5.0,"The 'roleDefinitionId' property on line 72 assigns the built-in 'Contributor' role to the principal specified by 'Ev2BuildoutServicePrincipalId'. Assigning Contributor at the subscription level to a service principal enables broad permissions, including the ability to modify, delete, or escalate privileges across all resources in the subscription. If this service principal is compromised, an attacker could gain full control over the environment, leading to privilege escalation, lateral movement, and potential data exfiltration. This configuration increases the blast radius and exposes the environment to significant risk.",Restrict the 'Contributor' role assignment to the minimum necessary scope (such as a specific resource group or resource) and use least privilege principles. Review the permissions required by 'Ev2BuildoutServicePrincipalId' and assign only the necessary roles. Implement Privileged Identity Management (PIM) and require multi-factor authentication (MFA) for all privileged accounts. Monitor high-risk activities and regularly audit role assignments. Reference ASB control IM-2.,,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 15,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 6,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T11:37:03.945588,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
