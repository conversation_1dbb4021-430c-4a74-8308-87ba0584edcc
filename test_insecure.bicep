// Test file with intentional security issues for testing JavaScript fix

param storageAccountName string = 'teststorage'
param location string = resourceGroup().location

// Storage account with security issues
resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {
  name: storageAccountName
  location: location
  kind: 'StorageV2'
  sku: {
    name: 'Standard_LRS'
  }
  properties: {
    allowBlobPublicAccess: true  // Security issue: allows public access
    minimumTlsVersion: 'TLS1_0'  // Security issue: outdated TLS version
    supportsHttpsTrafficOnly: false  // Security issue: allows HTTP traffic
    publicNetworkAccess: 'Enabled'  // Security issue: public network access
  }
}

// CosmosDB with security issues
resource cosmosAccount 'Microsoft.DocumentDB/databaseAccounts@2021-04-15' = {
  name: 'test-cosmos-${uniqueString(resourceGroup().id)}'
  location: location
  properties: {
    databaseAccountOfferType: 'Standard'
    publicNetworkAccess: 'Enabled'  // Security issue: public access
    isVirtualNetworkFilterEnabled: false  // Security issue: no VNet filtering
    ipRules: []  // Security issue: no IP restrictions
  }
}

// Output sensitive information
output storageAccountKey string = listKeys(storageAccount.id, storageAccount.apiVersion).keys[0].value  // Security issue: exposing keys
