File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
app-config.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyValues' flows across template boundary,"Secure parameter 'keyValues' and validate its usage across template boundaries

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applicati...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),"Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applications to Azure AD when technically feasible.","Enhanced Implementation Context:
• Azure AD tenancy concepts: https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps
• Create and configure Azure AD: https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant
• Define Azure AD tenants: https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/
• External identity providers: https://docs.microsoft.com/azure/active-directory/b2b/identity-providers
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 6.7, 12.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8
• PCI-DSS v3.2.1: 7.2, 8.3

Azure Policy Examples:
• An Azure Active Directory administrator should be provisioned for SQL servers
• Service Fabric clusters should only use Azure Active Directory for client authentication
• Standardize identity provider across all applications and services",cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'app_insights_key' flows across template boundary,"Secure parameter 'app_insights_key' and validate its usage across template boundaries

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applicati...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),"Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applications to Azure AD when technically feasible.","Enhanced Implementation Context:
• Azure AD tenancy concepts: https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps
• Create and configure Azure AD: https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant
• Define Azure AD tenants: https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/
• External identity providers: https://docs.microsoft.com/azure/active-directory/b2b/identity-providers
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 6.7, 12.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8
• PCI-DSS v3.2.1: 7.2, 8.3

Azure Policy Examples:
• An Azure Active Directory administrator should be provisioned for SQL servers
• Service Fabric clusters should only use Azure Active Directory for client authentication
• Standardize identity provider across all applications and services",cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyvault_name' flows across template boundary,"Secure parameter 'keyvault_name' and validate its usage across template boundaries

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applicati...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),"Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applications to Azure AD when technically feasible.","Enhanced Implementation Context:
• Azure AD tenancy concepts: https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps
• Create and configure Azure AD: https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant
• Define Azure AD tenants: https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/
• External identity providers: https://docs.microsoft.com/azure/active-directory/b2b/identity-providers
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 6.7, 12.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8
• PCI-DSS v3.2.1: 7.2, 8.3

Azure Policy Examples:
• An Azure Active Directory administrator should be provisioned for SQL servers
• Service Fabric clusters should only use Azure Active Directory for client authentication
• Standardize identity provider across all applications and services",cross_reference_analysis,privilege_escalation,Validated
keyvault.bicep,NS-1,Network Security,Establish network segmentation boundaries,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,"Review template dependencies and ensure secure communication between templates

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'secrets' flows across template boundary,"Secure parameter 'secrets' and validate its usage across template boundaries

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applicati...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),"Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applications to Azure AD when technically feasible.","Enhanced Implementation Context:
• Azure AD tenancy concepts: https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps
• Create and configure Azure AD: https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant
• Define Azure AD tenants: https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/
• External identity providers: https://docs.microsoft.com/azure/active-directory/b2b/identity-providers
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 6.7, 12.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8
• PCI-DSS v3.2.1: 7.2, 8.3

Azure Policy Examples:
• An Azure Active Directory administrator should be provisioned for SQL servers
• Service Fabric clusters should only use Azure Active Directory for client authentication
• Standardize identity provider across all applications and services",cross_reference_analysis,privilege_escalation,Validated
storage-accounts.bicep,NS-1,Network Security,Establish network segmentation boundaries,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,"Review template dependencies and ensure secure communication between templates

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,DP-6,Data Protection,Use a secure key management process,HIGH,1.0,Parameter flow security risk: Sensitive parameters may be exposed through template dependencies,"Use Key Vault references for sensitive parameters and validate parameter flow security

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/key-vault/general/overview) | [Azure data encryption key hierarchy](https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#key-hierarchy) | [BYOK specification](https://docs.microsoft.com/azure/key-vault/keys/byok-specification) | [Key Vault security best practices](https://docs.microsoft.com/azure/key-vault/general/best-practices)

🔵 Azure Guidance: Use Azure Key Vault to create and control encryption key lifecycle including generation distribution storage rotation and revocation. Follow best practices for key hierarchy and BYOK scenarios. Ensure...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/key-vault/general/overview) | [Azure data encryption key hierarchy](https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#key-hierarchy) | [BYOK specification](https://docs.microsoft.com/azure/key-vault/keys/byok-specification) | [Key Vault security best practices](https://docs.microsoft.com/azure/key-vault/general/best-practices),Use Azure Key Vault to create and control encryption key lifecycle including generation distribution storage rotation and revocation. Follow best practices for key hierarchy and BYOK scenarios. Ensure FIPS compliance levels meet requirements.,"Enhanced Implementation Context:
• Azure Key Vault overview: https://docs.microsoft.com/azure/key-vault/general/overview
• Azure data encryption key hierarchy: https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#key-hierarchy
• BYOK specification: https://docs.microsoft.com/azure/key-vault/keys/byok-specification
• Key Vault security best practices: https://docs.microsoft.com/azure/key-vault/general/best-practices
• FIPS 140-2 compliance levels: Software-protected keys (Level 1) HSM-protected keys in vaults (Level 2) HSM-protected keys in Managed HSM (Level 3)

Compliance Mappings:
• CIS Controls v8: Not specified
• NIST SP800-53 r4: IA-5, SC-12, SC-28
• PCI-DSS v3.2.1: 3.6

Azure Policy Examples:
• Key Vault keys should have an expiration date
• Key Vault secrets should have an expiration date
• Implement key rotation policies
• Use separate data encryption keys (DEK) with key encryption keys (KEK)
• Register keys with Azure Key Vault using key IDs",cross_reference_analysis,parameter_flow,Validated
app-config.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,13.0,"The App Configuration resource (Microsoft.AppConfiguration/configurationStores) at line 13 does not specify network access restrictions or private endpoints. By default, App Configuration endpoints are publicly accessible, which enables attackers to perform initial access, data exfiltration, or lateral movement if credentials are compromised. The blast radius includes potential exposure of all configuration data and secrets managed by this resource.","Restrict public network access by setting 'publicNetworkAccess' to 'Disabled' and configure a private endpoint for the App Configuration resource. This ensures only trusted networks can access the configuration store. Reference: Azure Security Benchmark NS-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
autoscale-settings.bicep,NS-1,Network Security,Establish network segmentation boundaries,HIGH,10.0,"The autoscaleSettings resource (Microsoft.Insights/autoscalesettings) is deployed without any explicit network segmentation, network security group (NSG), or application security group (ASG) configuration. This enables an attack vector where the autoscale resource could be accessible from unintended networks, increasing the risk of lateral movement or initial access if the underlying resource (e.g., server_farm_id) is exposed. The blast radius includes potential compromise of all resources managed by the autoscale policy.","Deploy the autoscaleSettings resource within a segmented virtual network and associate it with a Network Security Group (NSG) that enforces a deny-by-default policy. Explicitly define allowed inbound and outbound traffic. Reference: Azure Security Benchmark v3.0, NS-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
autoscale-settings.bicep,NS-2,Network Security,Secure cloud services with network controls,HIGH,16.0,"The targetResourceUri property (server_farm_id) does not specify the use of a private endpoint or restrict public network access. This enables an attack vector where the autoscale target (such as an App Service Plan or other resource) could be accessible from the public internet, increasing the risk of data exposure or remote exploitation. The blast radius includes all workloads running on the target resource.","Configure the target resource (referenced by server_farm_id) to use a private endpoint and disable public network access. Ensure that only trusted networks can access the autoscale target. Reference: Azure Security Benchmark v3.0, NS-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
feature-flags.bicep,DP-4,Data Protection,Enable data at rest encryption by default,CRITICAL,14.0,"The feature flag 'EnableBlobRetentionPolicy' is set to 'false', which disables automatic deletion of expired blobs in Azure Storage. Without an active retention policy, sensitive or regulated data may persist beyond its required lifecycle, increasing the risk of unauthorized access or data exposure if the storage account is compromised. Attackers gaining access to the storage account could exfiltrate or manipulate stale data, expanding the blast radius of a breach.","Set 'EnableBlobRetentionPolicy' to 'true' on line 16 to enforce automatic deletion of expired blobs. Additionally, configure Azure Storage account policies to ensure data at rest is encrypted and retention policies are enforced for all sensitive data. Review and monitor retention policy compliance regularly. Reference: DP-4.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services) | [Data at rest double encryption](https://docs.microsoft.com/azure/security/fundamentals/encryption-models) | [Azure Disk Encryption](https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview) | [SQL Transparent Data Encryption](https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview)

🔵 Azure Guidance: Many Azure services have data-at-rest encryption enabled by default using service-managed keys. Where not enabled by default enable encryption in Azure services or VMs for storage-level file-level or ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services) | [Data at rest double encryption](https://docs.microsoft.com/azure/security/fundamentals/encryption-models) | [Azure Disk Encryption](https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview) | [SQL Transparent Data Encryption](https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview),Many Azure services have data-at-rest encryption enabled by default using service-managed keys. Where not enabled by default enable encryption in Azure services or VMs for storage-level file-level or database-level encryption.,"Enhanced Implementation Context:
• Encryption at rest in Azure: https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services
• Data at rest double encryption: https://docs.microsoft.com/azure/security/fundamentals/encryption-models
• Encryption model and key management: https://docs.microsoft.com/azure/security/fundamentals/encryption-models
• Azure Disk Encryption: https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview
• SQL Transparent Data Encryption: https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview

Compliance Mappings:
• CIS Controls v8: 3.11
• NIST SP800-53 r4: SC-28
• PCI-DSS v3.2.1: 3.4, 3.5

Azure Policy Examples:
• Virtual machines should encrypt temp disks caches and data flows between Compute and Storage resources
• Transparent Data Encryption on SQL databases should be enabled
• Automation account variables should be encrypted
• Service Fabric clusters should have the ClusterProtectionLevel property set to EncryptAndSign
• Azure Cosmos DB accounts should use customer-managed keys to encrypt data at rest",ai_analysis,,Validated
feature-flags.bicep,DP-4,Data Protection,Enable data at rest encryption by default,HIGH,38.0,"The feature flag 'EnableGenevaAuditing' is set to 'false', disabling Geneva auditing for the application. Without auditing, there is no immutable log of access or modification events for sensitive data in Azure Storage or ServiceBus. This lack of monitoring allows attackers to perform data exfiltration or tampering without detection, increasing the potential blast radius and hindering incident response.","Set 'EnableGenevaAuditing' to 'true' on line 40 to enable comprehensive auditing. Ensure that audit logs are stored securely, monitored for anomalies, and integrated with SIEM solutions for real-time alerting. Reference: DP-4.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services) | [Data at rest double encryption](https://docs.microsoft.com/azure/security/fundamentals/encryption-models) | [Azure Disk Encryption](https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview) | [SQL Transparent Data Encryption](https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview)

🔵 Azure Guidance: Many Azure services have data-at-rest encryption enabled by default using service-managed keys. Where not enabled by default enable encryption in Azure services or VMs for storage-level file-level or ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services) | [Data at rest double encryption](https://docs.microsoft.com/azure/security/fundamentals/encryption-models) | [Azure Disk Encryption](https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview) | [SQL Transparent Data Encryption](https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview),Many Azure services have data-at-rest encryption enabled by default using service-managed keys. Where not enabled by default enable encryption in Azure services or VMs for storage-level file-level or database-level encryption.,"Enhanced Implementation Context:
• Encryption at rest in Azure: https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services
• Data at rest double encryption: https://docs.microsoft.com/azure/security/fundamentals/encryption-models
• Encryption model and key management: https://docs.microsoft.com/azure/security/fundamentals/encryption-models
• Azure Disk Encryption: https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview
• SQL Transparent Data Encryption: https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview

Compliance Mappings:
• CIS Controls v8: 3.11
• NIST SP800-53 r4: SC-28
• PCI-DSS v3.2.1: 3.4, 3.5

Azure Policy Examples:
• Virtual machines should encrypt temp disks caches and data flows between Compute and Storage resources
• Transparent Data Encryption on SQL databases should be enabled
• Automation account variables should be encrypted
• Service Fabric clusters should have the ClusterProtectionLevel property set to EncryptAndSign
• Azure Cosmos DB accounts should use customer-managed keys to encrypt data at rest",ai_analysis,,Validated
function-settings.bicep,IM-8,Identity Management,Restrict the exposure of credential and secrets,HIGH,42.0,"The parameter 'ado_access_client_id' on line 42 is assigned a hardcoded client ID value ('c77cd33b-8c5b-4d5c-a7a7-bdea70b062b1'). Hardcoding sensitive identifiers or secrets in infrastructure code can enable attackers with read access to the repository or deployment pipeline to gain initial access or perform lateral movement by impersonating the application or service principal. The blast radius includes potential unauthorized access to Azure resources associated with this client ID, especially if the corresponding secret is exposed elsewhere.","Remove the hardcoded client ID from the parameter definition on line 42. Instead, require this value to be provided securely at deployment time via a secure pipeline variable or reference it from Azure Key Vault. Implement Azure DevOps Credential Scanner or GitHub secret scanning to prevent accidental check-in of sensitive values. Reference: ASB IM-8.

📚 References: [Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops)

🔵 Azure Guidance: Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.",[Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops),Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.,"Enhanced Implementation Context:
• Azure DevOps Credential Scanner setup: https://secdevtools.azurewebsites.net/helpcredscan.html
• GitHub secret scanning: https://docs.github.com/github/administering-a-repository/about-secret-scanning
• Azure Key Vault developer guide: https://docs.microsoft.com/azure/key-vault/general/developers-guide
• Secure coding practices: https://docs.microsoft.com/azure/security/develop/secure-dev-overview
• Application security guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops

Compliance Mappings:
• CIS Controls v8: 16.9, 16.12
• NIST SP800-53 r4: IA-5
• PCI-DSS v3.2.1: 3.5, 6.3, 8.2

Azure Policy Examples:
• No applicable built-in policy (requires development process implementation)
• Implement credential scanning in CI/CD pipelines
• Enforce Azure Key Vault usage for secret storage
• Monitor and audit secret access patterns
• Require managed identities for Azure service authentication",ai_analysis,,Validated
function-settings.bicep,IM-3,Identity Management,Manage application identities securely and automatically,HIGH,50.0,"The 'Microsoft.Web/sites' resource (function app) at line 50 is declared as 'existing' without any evidence of a managed identity configuration. Absence of a managed identity increases the risk of credential exposure and lateral movement, as the function app may require secrets or credentials to access other Azure resources. Attackers who compromise the app could escalate privileges or move laterally by harvesting embedded credentials or misconfigured service principals.","Enable a system-assigned or user-assigned managed identity for the function app by adding the 'identity' property to the resource definition. Update the resource to include: identity: { type: 'SystemAssigned' } and refactor downstream resource access to use Azure AD authentication with managed identity. Reference: ASB IM-3.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview) | [Services supporting managed identities](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities) | [Azure service principal creation](https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps) | [Service principal with certificates](https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Use Azure managed identities for services supporting Azure AD authentication. For services without managed identity support use Azure AD service principals with certificate credentials and restricted ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview) | [Services supporting managed identities](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities) | [Azure service principal creation](https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps) | [Service principal with certificates](https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),Use Azure managed identities for services supporting Azure AD authentication. For services without managed identity support use Azure AD service principals with certificate credentials and restricted permissions.,"Enhanced Implementation Context:
• Azure managed identities overview: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview
• Services supporting managed identities: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities
• Azure service principal creation: https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps
• Service principal with certificates: https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: Not specified
• NIST SP800-53 r4: AC-2, AC-3, IA-4, IA-5, IA-9
• PCI-DSS v3.2.1: Not applicable

Azure Policy Examples:
• Managed identity should be used in your Function App
• Managed identity should be used in your Web App
• Service principals should be used to protect your subscriptions instead of management certificates
• Managed identity should be used in your API App
• Virtual machines' Guest Configuration extension should be deployed with system-assigned managed identity",ai_analysis,,Validated
function-settings.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,46.0,"The 'Microsoft.Storage/storageAccounts' resource (funcStorage) at line 46 is declared as 'existing' with no evidence of private endpoint enforcement or public network access restriction. If public network access is enabled, the storage account is exposed to the internet, enabling initial access, data exfiltration, and lateral movement. Attackers can exploit public endpoints to enumerate, brute-force, or exfiltrate data from the storage account.","Restrict public network access by setting 'publicNetworkAccess' to 'Disabled' and deploy a private endpoint for the storage account. Ensure only trusted VNets/subnets can access the storage account. Reference: ASB NS-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
function-settings.bicep,NS-8,Network Security,Detect and disable insecure services and protocols,CRITICAL,46.0,"The 'Microsoft.Storage/storageAccounts' resource (funcStorage) at line 46 is declared as 'existing' with no evidence of enforcing secure transfer (HTTPS/TLS 1.2+). If 'supportsHttpsTrafficOnly' is not enabled, data in transit can be intercepted or modified by attackers, leading to credential theft, data exposure, and session hijacking.","Set 'supportsHttpsTrafficOnly' to 'true' in the storage account configuration to enforce secure transfer. Additionally, configure the minimum TLS version to 1.2 or higher. Reference: ASB NS-8.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Secure protocol configuration](https://docs.microsoft.com/azure/security/fundamentals/network-best-practices) | [Network security monitoring](https://docs.microsoft.com/azure/security/fundamentals/network-monitoring) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Use Azure Sentinel's Insecure Protocol Workbook to discover insecure services like SSL/TLSv1 SSHv1 SMBv1 LM/NTLMv1. Disable insecure protocols or use compensating controls like NSG/Firewall blocking.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Secure protocol configuration](https://docs.microsoft.com/azure/security/fundamentals/network-best-practices) | [Network security monitoring](https://docs.microsoft.com/azure/security/fundamentals/network-monitoring) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Use Azure Sentinel's Insecure Protocol Workbook to discover insecure services like SSL/TLSv1 SSHv1 SMBv1 LM/NTLMv1. Disable insecure protocols or use compensating controls like NSG/Firewall blocking.,"Enhanced Implementation Context:
• Azure Sentinel insecure protocols workbook: https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks
• TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem
• Secure protocol configuration: https://docs.microsoft.com/azure/security/fundamentals/network-best-practices
• Network security monitoring: https://docs.microsoft.com/azure/security/fundamentals/network-monitoring
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 4.4, 4.8
• NIST SP800-53 r4: CM-2, CM-6, CM-7
• PCI-DSS v3.2.1: 4.1, A2.1, A2.2, A2.3

Azure Policy Examples:
• Latest TLS version should be used in your API App
• Latest TLS version should be used in your Web App
• Latest TLS version should be used in your Function App
• Secure transfer to storage accounts should be enabled",ai_analysis,,Validated
function-settings.bicep,IM-8,Identity Management,Restrict the exposure of credential and secrets,CRITICAL,66.0,"The configuration property 'APPINSIGHTS_INSTRUMENTATIONKEY' is set directly as an app setting. If this value is a secret (such as an Application Insights instrumentation key), storing it in plain text in app settings exposes it to anyone with read access to the App Service configuration. Attackers who gain access to the configuration can exfiltrate this key and use it to access telemetry data or inject malicious telemetry, increasing the blast radius to all monitored data and potentially enabling lateral movement.","Store all sensitive values, such as 'APPINSIGHTS_INSTRUMENTATIONKEY', in Azure Key Vault and reference them using Key Vault references in your app settings. Ensure that the App Service has a managed identity with access to the Key Vault. Update the configuration to use '@Microsoft.KeyVault(SecretUri=...)' syntax for secret values. Reference: Azure Security Benchmark IM-8.

📚 References: [Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops)

🔵 Azure Guidance: Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.",[Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops),Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.,"Enhanced Implementation Context:
• Azure DevOps Credential Scanner setup: https://secdevtools.azurewebsites.net/helpcredscan.html
• GitHub secret scanning: https://docs.github.com/github/administering-a-repository/about-secret-scanning
• Azure Key Vault developer guide: https://docs.microsoft.com/azure/key-vault/general/developers-guide
• Secure coding practices: https://docs.microsoft.com/azure/security/develop/secure-dev-overview
• Application security guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops

Compliance Mappings:
• CIS Controls v8: 16.9, 16.12
• NIST SP800-53 r4: IA-5
• PCI-DSS v3.2.1: 3.5, 6.3, 8.2

Azure Policy Examples:
• No applicable built-in policy (requires development process implementation)
• Implement credential scanning in CI/CD pipelines
• Enforce Azure Key Vault usage for secret storage
• Monitor and audit secret access patterns
• Require managed identities for Azure service authentication",ai_analysis,,Validated
function-settings.bicep,IM-8,Identity Management,Restrict the exposure of credential and secrets,CRITICAL,70.0,"The configuration property 'AzureWebJobsStorage__accountName' is set directly as an app setting. If this value is used in conjunction with a storage account key or connection string (not shown in this chunk), it may enable attackers with configuration access to enumerate or access storage resources. Exposure of storage account names, especially if keys are also exposed elsewhere, increases the risk of data exfiltration and lateral movement.","Store all sensitive storage connection information in Azure Key Vault and reference them using Key Vault references in your app settings. Ensure that the App Service uses managed identity to access the Key Vault. Do not store storage account names or keys in plain text in app settings. Reference: Azure Security Benchmark IM-8.

📚 References: [Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops)

🔵 Azure Guidance: Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.",[Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops),Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.,"Enhanced Implementation Context:
• Azure DevOps Credential Scanner setup: https://secdevtools.azurewebsites.net/helpcredscan.html
• GitHub secret scanning: https://docs.github.com/github/administering-a-repository/about-secret-scanning
• Azure Key Vault developer guide: https://docs.microsoft.com/azure/key-vault/general/developers-guide
• Secure coding practices: https://docs.microsoft.com/azure/security/develop/secure-dev-overview
• Application security guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops

Compliance Mappings:
• CIS Controls v8: 16.9, 16.12
• NIST SP800-53 r4: IA-5
• PCI-DSS v3.2.1: 3.5, 6.3, 8.2

Azure Policy Examples:
• No applicable built-in policy (requires development process implementation)
• Implement credential scanning in CI/CD pipelines
• Enforce Azure Key Vault usage for secret storage
• Monitor and audit secret access patterns
• Require managed identities for Azure service authentication",ai_analysis,,Validated
function-settings.bicep,IM-8,Identity Management,Restrict the exposure of credential and secrets,CRITICAL,78.0,"The configuration property 'AzureSignalRConnectionString__serviceUri' is set directly as an app setting. If this value is used in conjunction with a connection string or key (not shown in this chunk), it may expose sensitive service endpoints. Attackers with access to configuration could use this information to target the SignalR service, increasing the risk of data interception or service abuse.","Store all sensitive service connection information, such as SignalR connection strings, in Azure Key Vault and reference them using Key Vault references in your app settings. Ensure managed identity is used for Key Vault access. Do not store service URIs or keys in plain text in app settings. Reference: Azure Security Benchmark IM-8.

📚 References: [Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops)

🔵 Azure Guidance: Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.",[Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops),Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.,"Enhanced Implementation Context:
• Azure DevOps Credential Scanner setup: https://secdevtools.azurewebsites.net/helpcredscan.html
• GitHub secret scanning: https://docs.github.com/github/administering-a-repository/about-secret-scanning
• Azure Key Vault developer guide: https://docs.microsoft.com/azure/key-vault/general/developers-guide
• Secure coding practices: https://docs.microsoft.com/azure/security/develop/secure-dev-overview
• Application security guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops

Compliance Mappings:
• CIS Controls v8: 16.9, 16.12
• NIST SP800-53 r4: IA-5
• PCI-DSS v3.2.1: 3.5, 6.3, 8.2

Azure Policy Examples:
• No applicable built-in policy (requires development process implementation)
• Implement credential scanning in CI/CD pipelines
• Enforce Azure Key Vault usage for secret storage
• Monitor and audit secret access patterns
• Require managed identities for Azure service authentication",ai_analysis,,Validated
function-settings.bicep,IM-8,Identity Management,Restrict the exposure of credential and secrets,CRITICAL,87.0,"The configuration property 'ONEFUZZ_KEYVAULT' is set directly as an app setting. If this value exposes the name or URI of a Key Vault, it may assist attackers in targeting the Key Vault for enumeration or brute-force attacks, especially if other configuration or identity weaknesses exist. This increases the blast radius for potential secret exfiltration.","Do not expose Key Vault names or URIs in plain text in app settings. Use managed identity and Key Vault references for all secret and sensitive resource access. Limit access to Key Vault by network and identity controls. Reference: Azure Security Benchmark IM-8.

📚 References: [Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops)

🔵 Azure Guidance: Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.",[Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops),Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.,"Enhanced Implementation Context:
• Azure DevOps Credential Scanner setup: https://secdevtools.azurewebsites.net/helpcredscan.html
• GitHub secret scanning: https://docs.github.com/github/administering-a-repository/about-secret-scanning
• Azure Key Vault developer guide: https://docs.microsoft.com/azure/key-vault/general/developers-guide
• Secure coding practices: https://docs.microsoft.com/azure/security/develop/secure-dev-overview
• Application security guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops

Compliance Mappings:
• CIS Controls v8: 16.9, 16.12
• NIST SP800-53 r4: IA-5
• PCI-DSS v3.2.1: 3.5, 6.3, 8.2

Azure Policy Examples:
• No applicable built-in policy (requires development process implementation)
• Implement credential scanning in CI/CD pipelines
• Enforce Azure Key Vault usage for secret storage
• Monitor and audit secret access patterns
• Require managed identities for Azure service authentication",ai_analysis,,Validated
function-settings.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,112.0,"The configuration property 'NETWORK_CONFIG: instance_config.network_config' (line 112) does not explicitly enforce the use of Network Security Groups (NSGs) or a deny-by-default approach for network segmentation. Without explicit NSG association and deny-by-default rules, virtual machines or services may be exposed to unauthorized network traffic, enabling initial access, lateral movement, or remote exploitation. The blast radius includes potential compromise of all resources within the affected virtual network or subnet.","Explicitly define and associate Network Security Groups (NSGs) with all subnets and network interfaces in 'instance_config.network_config'. Ensure default inbound and outbound rules are set to deny, and only required ports and protocols are allowed. Review and apply adaptive network hardening recommendations. Reference: Azure Security Benchmark v3.0, Control NS-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
function-settings.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,112.0,"The configuration property 'NETWORK_CONFIG: instance_config.network_config' (line 112) does not specify the use of private endpoints or the disabling of public network access for AppService or Storage resources. Without private endpoints and with public access enabled, resources may be exposed to the public internet, increasing the risk of data exfiltration, unauthorized access, and exploitation. The blast radius includes potential compromise of all data and services accessible via public endpoints.","Update 'instance_config.network_config' to require private endpoints for all supported resources (e.g., Storage, AppService) and explicitly disable public network access. Enforce VNet integration for AppService and Storage accounts. Reference: Azure Security Benchmark v3.0, Control NS-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
function.bicep,IM-8,Identity Management,Restrict the exposure of credential and secrets,CRITICAL,7.0,"The parameter 'client_id' (line 7) is likely intended to receive a sensitive value (such as an Azure AD application client ID) but is defined as a plain string parameter without any secure or secret handling. If this parameter is set via deployment pipelines or parameter files, it could be inadvertently exposed in logs, source control, or to unauthorized users. Attackers who obtain this value may use it in combination with other leaked credentials for lateral movement or privilege escalation, especially if the client ID is used in authentication flows.","Redefine 'client_id' as a secure parameter using Azure Key Vault integration or a secure parameter type (if supported by your deployment tooling). Ensure that all secrets and sensitive identifiers are referenced from Azure Key Vault and never stored in plain text in code, parameter files, or deployment logs. Update deployment pipelines to retrieve 'client_id' securely at runtime.

📚 References: [Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops)

🔵 Azure Guidance: Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.",[Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops),Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.,"Enhanced Implementation Context:
• Azure DevOps Credential Scanner setup: https://secdevtools.azurewebsites.net/helpcredscan.html
• GitHub secret scanning: https://docs.github.com/github/administering-a-repository/about-secret-scanning
• Azure Key Vault developer guide: https://docs.microsoft.com/azure/key-vault/general/developers-guide
• Secure coding practices: https://docs.microsoft.com/azure/security/develop/secure-dev-overview
• Application security guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops

Compliance Mappings:
• CIS Controls v8: 16.9, 16.12
• NIST SP800-53 r4: IA-5
• PCI-DSS v3.2.1: 3.5, 6.3, 8.2

Azure Policy Examples:
• No applicable built-in policy (requires development process implementation)
• Implement credential scanning in CI/CD pipelines
• Enforce Azure Key Vault usage for secret storage
• Monitor and audit secret access patterns
• Require managed identities for Azure service authentication",ai_analysis,,Validated
function.bicep,IM-8,Identity Management,Restrict the exposure of credential and secrets,CRITICAL,14.0,"The parameter 'signedExpiry' (line 14) is likely used for time-limited access tokens or SAS tokens, which are highly sensitive. Defining this as a plain string parameter exposes the risk of accidental disclosure in source control, logs, or deployment artifacts. Attackers with access to this value could use it to gain unauthorized access to resources, leading to data exfiltration or privilege escalation.","Store 'signedExpiry' in Azure Key Vault and reference it securely in your template. Do not pass sensitive tokens or secrets as plain parameters. Update deployment processes to retrieve this value securely at runtime and ensure it is never written to logs or source control.

📚 References: [Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops)

🔵 Azure Guidance: Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.",[Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops),Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.,"Enhanced Implementation Context:
• Azure DevOps Credential Scanner setup: https://secdevtools.azurewebsites.net/helpcredscan.html
• GitHub secret scanning: https://docs.github.com/github/administering-a-repository/about-secret-scanning
• Azure Key Vault developer guide: https://docs.microsoft.com/azure/key-vault/general/developers-guide
• Secure coding practices: https://docs.microsoft.com/azure/security/develop/secure-dev-overview
• Application security guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops

Compliance Mappings:
• CIS Controls v8: 16.9, 16.12
• NIST SP800-53 r4: IA-5
• PCI-DSS v3.2.1: 3.5, 6.3, 8.2

Azure Policy Examples:
• No applicable built-in policy (requires development process implementation)
• Implement credential scanning in CI/CD pipelines
• Enforce Azure Key Vault usage for secret storage
• Monitor and audit secret access patterns
• Require managed identities for Azure service authentication",ai_analysis,,Validated
function.bicep,IM-8,Identity Management,Restrict the exposure of credential and secrets,CRITICAL,36.0,"The variable 'storage_account_sas' defined at line 33 is used to construct a SAS (Shared Access Signature) token for Azure Storage. If the values for 'signedExpiry', 'signedPermission', 'signedResourceTypes', or 'signedServices' are set using unprotected variables or are exposed in code, this can lead to credential leakage. Attackers who obtain these values can generate valid SAS tokens, granting unauthorized access to storage resources, enabling data exfiltration or modification, and significantly increasing the blast radius of a compromise.","Store all sensitive values used to construct SAS tokens (such as 'signedExpiry', 'signedPermission', 'signedResourceTypes', and 'signedServices') in Azure Key Vault and reference them securely at deployment time. Implement Azure DevOps Credential Scanner or GitHub secret scanning to ensure no secrets are embedded in code. Use managed identities for accessing Key Vault and never expose SAS token construction parameters in source code or variable files.

📚 References: [Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops)

🔵 Azure Guidance: Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.",[Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops),Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.,"Enhanced Implementation Context:
• Azure DevOps Credential Scanner setup: https://secdevtools.azurewebsites.net/helpcredscan.html
• GitHub secret scanning: https://docs.github.com/github/administering-a-repository/about-secret-scanning
• Azure Key Vault developer guide: https://docs.microsoft.com/azure/key-vault/general/developers-guide
• Secure coding practices: https://docs.microsoft.com/azure/security/develop/secure-dev-overview
• Application security guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops

Compliance Mappings:
• CIS Controls v8: 16.9, 16.12
• NIST SP800-53 r4: IA-5
• PCI-DSS v3.2.1: 3.5, 6.3, 8.2

Azure Policy Examples:
• No applicable built-in policy (requires development process implementation)
• Implement credential scanning in CI/CD pipelines
• Enforce Azure Key Vault usage for secret storage
• Monitor and audit secret access patterns
• Require managed identities for Azure service authentication",ai_analysis,,Validated
function.bicep,NS-1,Network Security,Establish network segmentation boundaries,HIGH,90.0,"The property 'virtualNetworkSubnetId' is set on the App Service (function) resource, but there is no evidence in this chunk that a Network Security Group (NSG) is associated with the subnet. Without an NSG, the subnet may be exposed to unrestricted network traffic, enabling lateral movement or direct access to the function app from untrusted sources. The blast radius includes potential compromise of the function app and any resources it can access.","Associate a Network Security Group (NSG) with the subnet referenced by 'hubSubnetId' and configure deny-by-default inbound rules. Only allow required traffic from trusted sources. Example: In the subnet configuration, add 'networkSecurityGroup: <nsgResourceId>'.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
function.bicep,NS-2,Network Security,Secure cloud services with network controls,HIGH,90.0,"The App Service (function) is integrated with a virtual network via 'virtualNetworkSubnetId', but there is no indication that public network access is disabled. If public access is not explicitly disabled, the function app may be reachable from the internet, increasing the risk of initial access and data exposure.","Explicitly disable public network access for the App Service by setting 'publicNetworkAccess: ""Disabled""' in the resource properties, or restrict access using access restrictions to only allow traffic from private endpoints or trusted IPs.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
function.bicep,NS-8,Network Security,Detect and disable insecure services and protocols,HIGH,86.0,"There is no explicit configuration enforcing the latest TLS version (TLS 1.2 or higher) for the App Service (function) in 'siteConfig'. Allowing older TLS versions (such as TLS 1.0/1.1) exposes the application to downgrade attacks and interception of data in transit, increasing the risk of credential theft and session hijacking.","In the 'siteConfig' for the App Service, set 'minTlsVersion' to '1.2' or higher. Example: 'siteConfig: { ..., minTlsVersion: ""1.2"" }'.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Secure protocol configuration](https://docs.microsoft.com/azure/security/fundamentals/network-best-practices) | [Network security monitoring](https://docs.microsoft.com/azure/security/fundamentals/network-monitoring) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Use Azure Sentinel's Insecure Protocol Workbook to discover insecure services like SSL/TLSv1 SSHv1 SMBv1 LM/NTLMv1. Disable insecure protocols or use compensating controls like NSG/Firewall blocking.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Secure protocol configuration](https://docs.microsoft.com/azure/security/fundamentals/network-best-practices) | [Network security monitoring](https://docs.microsoft.com/azure/security/fundamentals/network-monitoring) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Use Azure Sentinel's Insecure Protocol Workbook to discover insecure services like SSL/TLSv1 SSHv1 SMBv1 LM/NTLMv1. Disable insecure protocols or use compensating controls like NSG/Firewall blocking.,"Enhanced Implementation Context:
• Azure Sentinel insecure protocols workbook: https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks
• TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem
• Secure protocol configuration: https://docs.microsoft.com/azure/security/fundamentals/network-best-practices
• Network security monitoring: https://docs.microsoft.com/azure/security/fundamentals/network-monitoring
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 4.4, 4.8
• NIST SP800-53 r4: CM-2, CM-6, CM-7
• PCI-DSS v3.2.1: 4.1, A2.1, A2.2, A2.3

Azure Policy Examples:
• Latest TLS version should be used in your API App
• Latest TLS version should be used in your Web App
• Latest TLS version should be used in your Function App
• Secure transfer to storage accounts should be enabled",ai_analysis,,Validated
function.bicep,NS-8,Network Security,Detect and disable insecure services and protocols,HIGH,62.0,"The storage account resource 'funcStorage' is referenced as 'existing', but there is no evidence in this chunk that 'secure transfer required' is enabled. If secure transfer is not enforced, data sent to the storage account could be intercepted in transit, enabling credential theft or data exfiltration.","Ensure the storage account 'funcStorage' has 'supportsHttpsTrafficOnly' set to true. Example: In the storage account resource definition, add 'supportsHttpsTrafficOnly: true'.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Secure protocol configuration](https://docs.microsoft.com/azure/security/fundamentals/network-best-practices) | [Network security monitoring](https://docs.microsoft.com/azure/security/fundamentals/network-monitoring) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Use Azure Sentinel's Insecure Protocol Workbook to discover insecure services like SSL/TLSv1 SSHv1 SMBv1 LM/NTLMv1. Disable insecure protocols or use compensating controls like NSG/Firewall blocking.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Secure protocol configuration](https://docs.microsoft.com/azure/security/fundamentals/network-best-practices) | [Network security monitoring](https://docs.microsoft.com/azure/security/fundamentals/network-monitoring) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Use Azure Sentinel's Insecure Protocol Workbook to discover insecure services like SSL/TLSv1 SSHv1 SMBv1 LM/NTLMv1. Disable insecure protocols or use compensating controls like NSG/Firewall blocking.,"Enhanced Implementation Context:
• Azure Sentinel insecure protocols workbook: https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks
• TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem
• Secure protocol configuration: https://docs.microsoft.com/azure/security/fundamentals/network-best-practices
• Network security monitoring: https://docs.microsoft.com/azure/security/fundamentals/network-monitoring
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 4.4, 4.8
• NIST SP800-53 r4: CM-2, CM-6, CM-7
• PCI-DSS v3.2.1: 4.1, A2.1, A2.2, A2.3

Azure Policy Examples:
• Latest TLS version should be used in your API App
• Latest TLS version should be used in your Web App
• Latest TLS version should be used in your Function App
• Secure transfer to storage accounts should be enabled",ai_analysis,,Validated
function.bicep,IM-3,Identity Management,Manage application identities securely and automatically,HIGH,122.0,"The configuration at line 122 uses 'clientSecretSettingName' with a value ('OVERRIDE_USE_MI_FIC_ASSERTION_CLIENTID') that suggests a client secret may be required for Azure Active Directory authentication. If a managed identity is not explicitly enabled for the App Service or Function App, this could result in the use of static credentials, increasing the risk of credential exposure and lateral movement if secrets are leaked. Attackers who obtain these credentials could impersonate the application and access resources with its privileges, expanding the blast radius to all resources accessible by this identity.","Enable a system-assigned or user-assigned managed identity for the App Service or Function App and update the authentication configuration to use the managed identity for Azure AD authentication. Remove any static client secrets from the configuration and ensure all resource access is performed using managed identities. Reference: ASB IM-3.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview) | [Services supporting managed identities](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities) | [Azure service principal creation](https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps) | [Service principal with certificates](https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Use Azure managed identities for services supporting Azure AD authentication. For services without managed identity support use Azure AD service principals with certificate credentials and restricted ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview) | [Services supporting managed identities](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities) | [Azure service principal creation](https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps) | [Service principal with certificates](https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),Use Azure managed identities for services supporting Azure AD authentication. For services without managed identity support use Azure AD service principals with certificate credentials and restricted permissions.,"Enhanced Implementation Context:
• Azure managed identities overview: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview
• Services supporting managed identities: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities
• Azure service principal creation: https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps
• Service principal with certificates: https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: Not specified
• NIST SP800-53 r4: AC-2, AC-3, IA-4, IA-5, IA-9
• PCI-DSS v3.2.1: Not applicable

Azure Policy Examples:
• Managed identity should be used in your Function App
• Managed identity should be used in your Web App
• Service principals should be used to protect your subscriptions instead of management certificates
• Managed identity should be used in your API App
• Virtual machines' Guest Configuration extension should be deployed with system-assigned managed identity",ai_analysis,,Validated
function.bicep,IM-8,Identity Management,Restrict the exposure of credential and secrets,CRITICAL,133.0,"The variable 'sas' is assigned the result of 'funcStorage.listAccountSas', which likely generates or retrieves a Storage Account Shared Access Signature (SAS) token. Storing or generating SAS tokens directly in variables within infrastructure code can expose sensitive credentials if the codebase is leaked or insufficiently protected. Attackers with access to this variable could use the SAS token to gain unauthorized access to storage resources, enabling data exfiltration, privilege escalation, or lateral movement within the environment. The blast radius includes potential compromise of all data accessible via the SAS token, and may bypass network and identity controls if the token is overly permissive.","Do not generate or store SAS tokens directly in code or variables. Instead, use Azure Key Vault to securely store and retrieve sensitive credentials such as SAS tokens. Refactor the deployment to use managed identities for resource access wherever possible, and ensure that any required secrets are referenced securely via Key Vault integration. Implement credential scanning in CI/CD pipelines to prevent accidental exposure of secrets. Review and restrict SAS token permissions and lifetimes to the minimum necessary.

📚 References: [Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops)

🔵 Azure Guidance: Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.",[Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops),Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.,"Enhanced Implementation Context:
• Azure DevOps Credential Scanner setup: https://secdevtools.azurewebsites.net/helpcredscan.html
• GitHub secret scanning: https://docs.github.com/github/administering-a-repository/about-secret-scanning
• Azure Key Vault developer guide: https://docs.microsoft.com/azure/key-vault/general/developers-guide
• Secure coding practices: https://docs.microsoft.com/azure/security/develop/secure-dev-overview
• Application security guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops

Compliance Mappings:
• CIS Controls v8: 16.9, 16.12
• NIST SP800-53 r4: IA-5
• PCI-DSS v3.2.1: 3.5, 6.3, 8.2

Azure Policy Examples:
• No applicable built-in policy (requires development process implementation)
• Implement credential scanning in CI/CD pipelines
• Enforce Azure Key Vault usage for secret storage
• Monitor and audit secret access patterns
• Require managed identities for Azure service authentication",ai_analysis,,Validated
function.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,141.0,"The 'sasUrl' property on line 141 exposes an Azure Storage SAS token directly in the application logs configuration. If the SAS token is not configured with 'https only' or has overly broad permissions, attackers intercepting this URL could gain unauthorized access to storage data, enabling data exfiltration or modification. The blast radius includes potential compromise of all blobs accessible by the SAS token, risking sensitive log data and lateral movement to other storage resources.","Ensure that the SAS token referenced in 'sasUrl' enforces 'https only' and has the minimum required permissions and expiration. Additionally, configure the underlying storage account to require secure transfer (HTTPS) for all connections. Review and restrict the SAS token's scope and lifetime. Reference: ASB DP-3.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account)

🔵 Azure Guidance: Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windo...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account),Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windows remote management.,"Enhanced Implementation Context:
• Double encryption for Azure data in transit: https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit
• Encryption in transit overview: https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit
• TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem
• Azure Storage secure transfer: https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account

Compliance Mappings:
• CIS Controls v8: 3.10
• NIST SP800-53 r4: SC-8
• PCI-DSS v3.2.1: 3.5, 3.6, 4.1

Azure Policy Examples:
• Kubernetes clusters should be accessible only over HTTPS
• Only secure connections to your Azure Cache for Redis should be enabled
• FTPS only should be required in your Function App
• Secure transfer to storage accounts should be enabled
• Function App should only be accessible over HTTPS
• Latest TLS version should be used in your API App
• Web Application should only be accessible over HTTPS
• Enforce SSL connection should be enabled for PostgreSQL database servers
• Latest TLS version should be used in your Web App",ai_analysis,,Validated
function.bicep,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,148.0,"The output 'principalId' exposes the managed identity principalId of the Azure Function. If this output is accessible to unauthorized users or downstream systems, it can be used as an attack vector for reconnaissance, privilege escalation, or lateral movement. Attackers with access to this identifier may attempt to enumerate permissions or target the identity for abuse, increasing the blast radius of a potential compromise.","Restrict access to deployment outputs containing sensitive identity information. Only expose 'principalId' to trusted automation or deployment systems with strict RBAC controls. Consider omitting this output or masking it unless absolutely required for downstream automation. Review and classify outputs as sensitive data per Azure Security Benchmark DP-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources)

🔵 Azure Guidance: Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other loca...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources),Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other locations.,"Enhanced Implementation Context:
• Data classification overview: https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification
• Azure Purview sensitivity labels: https://docs.microsoft.com/azure/purview/create-sensitivity-label
• Azure Information Protection: https://docs.microsoft.com/azure/information-protection/what-is-information-protection
• Azure SQL Data Discovery: https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification
• Azure Purview data sources: https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources

Compliance Mappings:
• CIS Controls v8: 3.2, 3.7, 3.13
• NIST SP800-53 r4: RA-2, SC-28
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Sensitive data in your SQL databases should be classified
• Implement data discovery and classification across all data stores
• Deploy Azure Purview for enterprise data governance
• Configure sensitivity labels for all data assets",ai_analysis,,Validated
function.bicep,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,149.0,"The output 'userAssignedMsiClientId' exposes the clientId of a user-assigned managed identity. Disclosure of this identifier can facilitate targeted attacks, such as identity enumeration or unauthorized access attempts, especially if the output is accessible outside of trusted deployment pipelines. This increases the risk of identity compromise and broadens the potential blast radius.","Limit the exposure of 'userAssignedMsiClientId' in outputs to only those systems and users that require it for automation. Apply strict RBAC to outputs and consider removing or masking this value if not essential. Treat identity-related outputs as sensitive and classify them accordingly, following Azure Security Benchmark DP-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources)

🔵 Azure Guidance: Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other loca...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources),Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other locations.,"Enhanced Implementation Context:
• Data classification overview: https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification
• Azure Purview sensitivity labels: https://docs.microsoft.com/azure/purview/create-sensitivity-label
• Azure Information Protection: https://docs.microsoft.com/azure/information-protection/what-is-information-protection
• Azure SQL Data Discovery: https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification
• Azure Purview data sources: https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources

Compliance Mappings:
• CIS Controls v8: 3.2, 3.7, 3.13
• NIST SP800-53 r4: RA-2, SC-28
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Sensitive data in your SQL databases should be classified
• Implement data discovery and classification across all data stores
• Deploy Azure Purview for enterprise data governance
• Configure sensitivity labels for all data assets",ai_analysis,,Validated
function.bicep,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,150.0,"The output 'userAssignedMsiObjectId' reveals the objectId of a user-assigned managed identity. This information can be leveraged by attackers for reconnaissance, privilege escalation, or lateral movement within Azure AD. If exposed to unauthorized parties, it increases the risk of identity-based attacks and data exposure.","Ensure that outputs containing sensitive identity information such as 'userAssignedMsiObjectId' are only accessible to trusted automation or deployment systems. Apply least privilege access to outputs, and consider removing or obfuscating this output unless strictly necessary. Classify and protect outputs as sensitive data in accordance with Azure Security Benchmark DP-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources)

🔵 Azure Guidance: Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other loca...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources),Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other locations.,"Enhanced Implementation Context:
• Data classification overview: https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification
• Azure Purview sensitivity labels: https://docs.microsoft.com/azure/purview/create-sensitivity-label
• Azure Information Protection: https://docs.microsoft.com/azure/information-protection/what-is-information-protection
• Azure SQL Data Discovery: https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification
• Azure Purview data sources: https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources

Compliance Mappings:
• CIS Controls v8: 3.2, 3.7, 3.13
• NIST SP800-53 r4: RA-2, SC-28
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Sensitive data in your SQL databases should be classified
• Implement data discovery and classification across all data stores
• Deploy Azure Purview for enterprise data governance
• Configure sensitivity labels for all data assets",ai_analysis,,Validated
hub-network.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,14.0,"The subnet 'hub-subnet' defined at line 14 does not have an associated Network Security Group (NSG). Without an NSG, there is no explicit traffic filtering, which enables an attacker to exploit open network paths for initial access, lateral movement, or data exfiltration. The blast radius includes all resources deployed in this subnet, as unrestricted traffic could reach any service or VM within the subnet.","Associate a Network Security Group (NSG) with the 'hub-subnet' by adding the 'networkSecurityGroup' property to the subnet definition. Define explicit inbound and outbound rules to enforce a deny-by-default policy and only allow required traffic. Example: 'networkSecurityGroup: { id: <resourceId of NSG> }'. Reference: Azure Security Benchmark v3.0 NS-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
instance-config.bicep,IM-8,Identity Management,Restrict the exposure of credential and secrets,CRITICAL,46.0,"The variable 'monitoringGCSAuthId' at line 37 contains a value that appears to be a Key Vault DNS name ('airseccosinetest.geneva.keyvault.airaspcerts.cloudapp.net'). If this value is a secret, credential, or sensitive identifier, embedding it directly in configuration variables exposes it to anyone with access to the template, enabling attackers to discover and potentially misuse Key Vault endpoints for credential harvesting or lateral movement. The blast radius includes potential compromise of all secrets stored in the referenced Key Vault if access controls are weak or if the endpoint is exposed.","Store all secrets, credentials, and sensitive Key Vault references in Azure Key Vault and reference them securely using managed identities. Remove hardcoded Key Vault DNS names or secret identifiers from configuration variables. Implement Azure DevOps Credential Scanner or equivalent in your CI/CD pipeline to detect and block embedded secrets. Reference: ASB IM-8.

📚 References: [Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops)

🔵 Azure Guidance: Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.",[Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops),Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.,"Enhanced Implementation Context:
• Azure DevOps Credential Scanner setup: https://secdevtools.azurewebsites.net/helpcredscan.html
• GitHub secret scanning: https://docs.github.com/github/administering-a-repository/about-secret-scanning
• Azure Key Vault developer guide: https://docs.microsoft.com/azure/key-vault/general/developers-guide
• Secure coding practices: https://docs.microsoft.com/azure/security/develop/secure-dev-overview
• Application security guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops

Compliance Mappings:
• CIS Controls v8: 16.9, 16.12
• NIST SP800-53 r4: IA-5
• PCI-DSS v3.2.1: 3.5, 6.3, 8.2

Azure Policy Examples:
• No applicable built-in policy (requires development process implementation)
• Implement credential scanning in CI/CD pipelines
• Enforce Azure Key Vault usage for secret storage
• Monitor and audit secret access patterns
• Require managed identities for Azure service authentication",ai_analysis,,Validated
instance-config.bicep,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,64.0,"The output 'appConfigFields' at line 67 exposes the list of administrator identities (dynamicConfig.Admins) as a plain string in deployment outputs. Attackers with access to deployment logs or output artifacts can enumerate privileged accounts, enabling targeted phishing, privilege escalation, or lateral movement. The blast radius includes potential compromise of all admin accounts and associated resources.","Remove or redact sensitive administrator information from deployment outputs. Instead of outputting the full list of admin identities, output only non-sensitive metadata or use Azure Information Protection to classify and restrict access to sensitive outputs. Apply data classification and labeling to all outputs containing privileged or sensitive data. Reference: ASB DP-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources)

🔵 Azure Guidance: Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other loca...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources),Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other locations.,"Enhanced Implementation Context:
• Data classification overview: https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification
• Azure Purview sensitivity labels: https://docs.microsoft.com/azure/purview/create-sensitivity-label
• Azure Information Protection: https://docs.microsoft.com/azure/information-protection/what-is-information-protection
• Azure SQL Data Discovery: https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification
• Azure Purview data sources: https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources

Compliance Mappings:
• CIS Controls v8: 3.2, 3.7, 3.13
• NIST SP800-53 r4: RA-2, SC-28
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Sensitive data in your SQL databases should be classified
• Implement data discovery and classification across all data stores
• Deploy Azure Purview for enterprise data governance
• Configure sensitivity labels for all data assets",ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,5.0,"The 'corpNetIps' array includes extremely broad IP ranges such as '*******/8', '********/8', '20.0.0.0/8', and others. Allowing /8 and other large subnets in network rules can enable attackers from a vast range of external networks to access protected resources, significantly increasing the attack surface and blast radius. If any of these IPs are compromised or misattributed, an attacker could gain initial access or move laterally within the environment.","Restrict allowed IP ranges to the smallest possible subnets that are strictly necessary for business operations. Replace /8 and other large subnets with specific, validated IP addresses or much smaller CIDR blocks. Implement network security groups (NSGs) with a deny-by-default policy and only allow traffic from trusted, minimal IP ranges. Reference ASB control NS-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,3.0,"The 'corpNetRules' variable is constructed to allow all IPs in 'corpNetIps', which includes multiple /8 and /16 ranges. This configuration enables a massive attack surface, allowing initial access from a wide range of external networks. Attackers with access to any IP within these ranges could exploit this overly permissive rule to compromise network resources.","Review and reduce the IP ranges in 'corpNetIps' to only those that are absolutely required, using the smallest possible CIDR blocks. Apply a deny-by-default approach in NSGs and only allow traffic from explicitly trusted sources. Regularly audit and update allowed IPs to ensure minimal exposure. Reference ASB control NS-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
keyvault.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,27.0,"The 'defaultAction' property in the 'networkAcls' block for the Key Vault resource is set to 'Allow'. This configuration allows public network access to the Key Vault, enabling attackers to attempt initial access, brute force, or enumeration attacks from any IP address. The blast radius includes potential exposure of all secrets and keys stored in the vault, risking data exfiltration and privilege escalation.","Set 'networkAcls.defaultAction' to 'Deny' to block public network access. Only allow access via explicitly defined IP rules or virtual network rules. Additionally, consider enabling private endpoints for the Key Vault to restrict access to trusted networks only.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
keyvault.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,27.0,"The 'bypass' property in the 'networkAcls' block is set to 'AzureServices', which allows all Azure services to bypass network restrictions and access the Key Vault. This increases the attack surface, as any Azure service (potentially compromised or misconfigured) can access the vault, enabling lateral movement and privilege escalation within the Azure environment.","Set 'networkAcls.bypass' to 'None' to prevent all Azure services from bypassing network restrictions. Only allow access from explicitly defined trusted services or networks. Review and restrict the list of allowed services as per the principle of least privilege.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
keyvault.bicep,DP-8,Data Protection,Ensure security of key and certificate repository,CRITICAL,22.0,"The 'accessPolicies' property is set to an empty array while 'enableRbacAuthorization' is set to true. If RBAC is not properly configured, this can result in overly permissive or misconfigured access, potentially allowing unauthorized users to access or manage secrets and keys. Attackers gaining access to RBAC roles can escalate privileges or exfiltrate sensitive data.","Ensure that Azure RBAC roles assigned to the Key Vault are strictly limited to required identities and follow the principle of least privilege. Regularly audit RBAC assignments and remove unnecessary permissions. Consider using access policies in addition to RBAC for defense-in-depth.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/key-vault/general/overview) | [Key Vault security best practices](https://docs.microsoft.com/azure/key-vault/general/best-practices) | [Managed identity Key Vault access](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad) | [Key Vault Private Link](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Key Vault logging and monitoring](https://docs.microsoft.com/azure/key-vault/general/logging)

🔵 Azure Guidance: Secure Azure Key Vault through access policies or Azure RBAC for least privilege. Use Private Link and Azure Firewall for minimal exposure. Implement separation of duties managed identity access and c...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/key-vault/general/overview) | [Key Vault security best practices](https://docs.microsoft.com/azure/key-vault/general/best-practices) | [Managed identity Key Vault access](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad) | [Key Vault Private Link](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Key Vault logging and monitoring](https://docs.microsoft.com/azure/key-vault/general/logging),Secure Azure Key Vault through access policies or Azure RBAC for least privilege. Use Private Link and Azure Firewall for minimal exposure. Implement separation of duties managed identity access and comprehensive logging.,"Enhanced Implementation Context:
• Azure Key Vault overview: https://docs.microsoft.com/azure/key-vault/general/overview
• Key Vault security best practices: https://docs.microsoft.com/azure/key-vault/general/best-practices
• Managed identity Key Vault access: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad
• Key Vault Private Link: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Key Vault logging and monitoring: https://docs.microsoft.com/azure/key-vault/general/logging

Compliance Mappings:
• CIS Controls v8: Not specified
• NIST SP800-53 r4: IA-5, SC-12, SC-17
• PCI-DSS v3.2.1: 3.6

Azure Policy Examples:
• Key vaults should have purge protection enabled
• Azure Defender for Key Vault should be enabled
• Key vaults should have soft delete enabled
• Azure Key Vault should disable public network access
• Private endpoint should be configured for Key Vault
• Resource logs in Key Vault should be enabled
• Implement separation of duties for key management and data access",ai_analysis,,Validated
keyvault.bicep,DP-8,Data Protection,Ensure security of key and certificate repository,CRITICAL,22.0,"The 'enableRbacAuthorization' property is set to true, but there is no evidence of RBAC scoping or assignment in this template. Without explicit RBAC configuration, there is a risk of default or inherited permissions granting excessive access to the Key Vault, enabling privilege escalation or unauthorized data access.","Explicitly define and assign Azure RBAC roles for the Key Vault resource to only trusted identities. Regularly review and audit RBAC assignments to ensure no excessive or unintended permissions exist.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/key-vault/general/overview) | [Key Vault security best practices](https://docs.microsoft.com/azure/key-vault/general/best-practices) | [Managed identity Key Vault access](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad) | [Key Vault Private Link](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Key Vault logging and monitoring](https://docs.microsoft.com/azure/key-vault/general/logging)

🔵 Azure Guidance: Secure Azure Key Vault through access policies or Azure RBAC for least privilege. Use Private Link and Azure Firewall for minimal exposure. Implement separation of duties managed identity access and c...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/key-vault/general/overview) | [Key Vault security best practices](https://docs.microsoft.com/azure/key-vault/general/best-practices) | [Managed identity Key Vault access](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad) | [Key Vault Private Link](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Key Vault logging and monitoring](https://docs.microsoft.com/azure/key-vault/general/logging),Secure Azure Key Vault through access policies or Azure RBAC for least privilege. Use Private Link and Azure Firewall for minimal exposure. Implement separation of duties managed identity access and comprehensive logging.,"Enhanced Implementation Context:
• Azure Key Vault overview: https://docs.microsoft.com/azure/key-vault/general/overview
• Key Vault security best practices: https://docs.microsoft.com/azure/key-vault/general/best-practices
• Managed identity Key Vault access: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad
• Key Vault Private Link: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Key Vault logging and monitoring: https://docs.microsoft.com/azure/key-vault/general/logging

Compliance Mappings:
• CIS Controls v8: Not specified
• NIST SP800-53 r4: IA-5, SC-12, SC-17
• PCI-DSS v3.2.1: 3.6

Azure Policy Examples:
• Key vaults should have purge protection enabled
• Azure Defender for Key Vault should be enabled
• Key vaults should have soft delete enabled
• Azure Key Vault should disable public network access
• Private endpoint should be configured for Key Vault
• Resource logs in Key Vault should be enabled
• Implement separation of duties for key management and data access",ai_analysis,,Validated
operational-insights.bicep,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,5.0,"The parameter 'workbookData' (line 5) is defined as type 'object' without any input validation or data classification. This enables an attack vector where unvalidated or sensitive data could be injected into the deployment, potentially exposing sensitive information or allowing malicious payloads to be processed. The blast radius includes unauthorized access to sensitive data, data exfiltration, or privilege escalation if the object contains credentials or secrets.","Implement strict input validation and data classification for the 'workbookData' parameter. Use Azure Policy or template-level constraints to restrict the structure and content of the object. Integrate Azure Purview or Azure Information Protection to classify and label sensitive data. Reference: ASB DP-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources)

🔵 Azure Guidance: Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other loca...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources),Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other locations.,"Enhanced Implementation Context:
• Data classification overview: https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification
• Azure Purview sensitivity labels: https://docs.microsoft.com/azure/purview/create-sensitivity-label
• Azure Information Protection: https://docs.microsoft.com/azure/information-protection/what-is-information-protection
• Azure SQL Data Discovery: https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification
• Azure Purview data sources: https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources

Compliance Mappings:
• CIS Controls v8: 3.2, 3.7, 3.13
• NIST SP800-53 r4: RA-2, SC-28
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Sensitive data in your SQL databases should be classified
• Implement data discovery and classification across all data stores
• Deploy Azure Purview for enterprise data governance
• Configure sensitivity labels for all data assets",ai_analysis,,Validated
operational-insights.bicep,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,75.0,"The 'properties' block for the Operational Insights workspace (Microsoft.OperationalInsights/workspaces) does not specify any data classification, discovery, or labeling configuration. Without data classification, sensitive data stored or ingested into Log Analytics may be untracked, enabling attackers or unauthorized users to access or exfiltrate sensitive information without detection. The blast radius includes potential exposure of all logs and telemetry data collected in this workspace.","Integrate Azure Purview or Azure Information Protection with the workspace to enable automated data discovery, classification, and labeling. Apply sensitivity labels to all ingested data and configure policies to enforce data protection. Reference: ASB DP-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources)

🔵 Azure Guidance: Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other loca...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources),Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other locations.,"Enhanced Implementation Context:
• Data classification overview: https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification
• Azure Purview sensitivity labels: https://docs.microsoft.com/azure/purview/create-sensitivity-label
• Azure Information Protection: https://docs.microsoft.com/azure/information-protection/what-is-information-protection
• Azure SQL Data Discovery: https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification
• Azure Purview data sources: https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources

Compliance Mappings:
• CIS Controls v8: 3.2, 3.7, 3.13
• NIST SP800-53 r4: RA-2, SC-28
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Sensitive data in your SQL databases should be classified
• Implement data discovery and classification across all data stores
• Deploy Azure Purview for enterprise data governance
• Configure sensitivity labels for all data assets",ai_analysis,,Validated
operational-insights.bicep,DP-4,Data Protection,Enable data at rest encryption by default,CRITICAL,75.0,"The 'properties' block for the Operational Insights workspace does not explicitly enforce encryption at rest or specify customer-managed keys. If encryption at rest is not enabled or only uses default service-managed keys, attackers with access to underlying storage could read or tamper with log data. The blast radius includes all data stored in the workspace.","Explicitly configure encryption at rest for the workspace and, if required by compliance, specify customer-managed keys (CMK) using Azure Key Vault. Ensure that all data at rest is encrypted and key management policies are enforced. Reference: ASB DP-4.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services) | [Data at rest double encryption](https://docs.microsoft.com/azure/security/fundamentals/encryption-models) | [Azure Disk Encryption](https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview) | [SQL Transparent Data Encryption](https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview)

🔵 Azure Guidance: Many Azure services have data-at-rest encryption enabled by default using service-managed keys. Where not enabled by default enable encryption in Azure services or VMs for storage-level file-level or ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services) | [Data at rest double encryption](https://docs.microsoft.com/azure/security/fundamentals/encryption-models) | [Azure Disk Encryption](https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview) | [SQL Transparent Data Encryption](https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview),Many Azure services have data-at-rest encryption enabled by default using service-managed keys. Where not enabled by default enable encryption in Azure services or VMs for storage-level file-level or database-level encryption.,"Enhanced Implementation Context:
• Encryption at rest in Azure: https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services
• Data at rest double encryption: https://docs.microsoft.com/azure/security/fundamentals/encryption-models
• Encryption model and key management: https://docs.microsoft.com/azure/security/fundamentals/encryption-models
• Azure Disk Encryption: https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview
• SQL Transparent Data Encryption: https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview

Compliance Mappings:
• CIS Controls v8: 3.11
• NIST SP800-53 r4: SC-28
• PCI-DSS v3.2.1: 3.4, 3.5

Azure Policy Examples:
• Virtual machines should encrypt temp disks caches and data flows between Compute and Storage resources
• Transparent Data Encryption on SQL databases should be enabled
• Automation account variables should be encrypted
• Service Fabric clusters should have the ClusterProtectionLevel property set to EncryptAndSign
• Azure Cosmos DB accounts should use customer-managed keys to encrypt data at rest",ai_analysis,,Validated
operational-insights.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,75.0,"The configuration for the Operational Insights workspace does not restrict public network access or require private endpoints. Without private endpoints, the workspace may be accessible from the public internet, enabling initial access, lateral movement, or data exfiltration by attackers. The blast radius includes all telemetry and log data in the workspace.","Configure the workspace to disable public network access and require private endpoints for all access. Use Azure Private Link to ensure that only trusted VNets can connect to the workspace. Reference: ASB NS-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
operational-insights.bicep,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,173.0,"The output 'appInsightsInstrumentationKey' exposes the Application Insights Instrumentation Key in plain text. This key is a sensitive credential that, if leaked, allows attackers to send arbitrary telemetry, exfiltrate data, or disrupt monitoring. Attackers with this key can impersonate legitimate telemetry sources, inject malicious data, or use the key for reconnaissance, increasing the blast radius to all resources monitored by this Application Insights instance.","Do not output sensitive credentials such as Instrumentation Keys. Instead, store such secrets in Azure Key Vault and reference them securely. Remove the 'appInsightsInstrumentationKey' output or replace it with a reference to a secure secret store. Review all outputs for sensitive data exposure and apply data classification and labeling as per Azure Security Benchmark DP-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources)

🔵 Azure Guidance: Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other loca...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources),Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other locations.,"Enhanced Implementation Context:
• Data classification overview: https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification
• Azure Purview sensitivity labels: https://docs.microsoft.com/azure/purview/create-sensitivity-label
• Azure Information Protection: https://docs.microsoft.com/azure/information-protection/what-is-information-protection
• Azure SQL Data Discovery: https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification
• Azure Purview data sources: https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources

Compliance Mappings:
• CIS Controls v8: 3.2, 3.7, 3.13
• NIST SP800-53 r4: RA-2, SC-28
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Sensitive data in your SQL databases should be classified
• Implement data discovery and classification across all data stores
• Deploy Azure Purview for enterprise data governance
• Configure sensitivity labels for all data assets",ai_analysis,,Validated
scaleset-networks.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,66.0,"The subnet 'scaleset' (property: defaultOutboundAccess) is configured with 'defaultOutboundAccess: true', which enables default outbound internet connectivity for all resources in the subnet. This exposes all VMs and services in the subnet to the public internet, creating an initial access vector for attackers and significantly increasing the blast radius in the event of a compromise. Without explicit egress controls, malicious actors can exploit open outbound access for data exfiltration, command and control, or lateral movement.","Set 'defaultOutboundAccess' to 'false' for the subnet and associate a Network Security Group (NSG) with explicit outbound rules to restrict internet access. Only allow required outbound destinations and block all others by default. Example: Add an NSG resource and reference it in the subnet's 'networkSecurityGroup' property.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
scaleset-networks.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,59.0,"The subnet 'scaleset' is defined without an associated Network Security Group (NSG). Absence of an NSG means there are no explicit inbound or outbound traffic controls, leaving the subnet and its resources exposed to unauthorized access, lateral movement, and network-based attacks. This violates the deny-by-default principle and increases the risk of network compromise.","Define a Network Security Group (NSG) resource and associate it with the 'scaleset' subnet using the 'networkSecurityGroup' property. Configure the NSG with least-privilege inbound and outbound rules, explicitly denying all unnecessary traffic.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
scaleset-networks.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,59.0,"The subnet 'scaleset' (property: defaultOutboundAccess) is configured to allow default outbound internet access, and there is no evidence of private endpoints or explicit disabling of public network access for resources. This configuration enables public network exposure, increasing the risk of data exfiltration and unauthorized access to cloud resources.","Disable default outbound access by setting 'defaultOutboundAccess' to 'false' and implement private endpoints for all supported Azure resources. Ensure that public network access is explicitly disabled for all services where possible.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
scaleset-networks.bicep,NS-3,Network Security,Deploy firewall at the edge of enterprise network,CRITICAL,101.0,"The virtual network 'vnet' and its subnets are deployed without an Azure Firewall or any user-defined routes (UDRs) to enforce centralized traffic inspection and filtering. Without a firewall, there is no advanced filtering of traffic between internal segments or to/from external networks, leaving the environment vulnerable to lateral movement and external attacks.","Deploy an Azure Firewall resource in a dedicated subnet and configure user-defined routes (UDRs) to route all outbound and inter-subnet traffic through the firewall. Update the subnet's route table to enforce firewall inspection for all traffic.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal) | [Virtual network traffic routing](https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview) | [Azure Firewall Manager](https://docs.microsoft.com/azure/firewall-manager/overview) | [Hub-spoke topology](https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Use Azure Firewall for stateful application layer traffic restriction and central management. Create user-defined routes (UDR) for complex topologies. Block known bad IPs and high-risk protocols.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal) | [Virtual network traffic routing](https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview) | [Azure Firewall Manager](https://docs.microsoft.com/azure/firewall-manager/overview) | [Hub-spoke topology](https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Use Azure Firewall for stateful application layer traffic restriction and central management. Create user-defined routes (UDR) for complex topologies. Block known bad IPs and high-risk protocols.,"Enhanced Implementation Context:
• Azure Firewall deployment: https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal
• Virtual network traffic routing: https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview
• Azure Firewall Manager: https://docs.microsoft.com/azure/firewall-manager/overview
• Hub-spoke topology: https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 4.4, 4.8, 13.10
• NIST SP800-53 r4: AC-4, SC-7, CM-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Management ports should be closed on your virtual machines
• Management ports of virtual machines should be protected with just-in-time network access control
• IP Forwarding on your virtual machine should be disabled
• All Internet traffic should be routed via your deployed Azure Firewall",ai_analysis,,Validated
server-farms.bicep,IM-3,Identity Management,Manage application identities securely and automatically,HIGH,111.0,"The App Service Plan resource 'Microsoft.Web/serverfarms' defined at line 111 does not specify a managed identity. Without a managed identity, applications and services may require credentials to be stored in code or configuration, increasing the risk of credential exposure and enabling attackers to gain initial access or escalate privileges if secrets are compromised. The blast radius includes potential compromise of all resources accessed by the App Service.","Add an 'identity' block to the App Service Plan resource at line 111 to enable a system-assigned managed identity. Example: 'identity: { type: ""SystemAssigned"" }'. Then, use this managed identity for all downstream resource access (e.g., Key Vault, Storage) instead of secrets or credentials.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview) | [Services supporting managed identities](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities) | [Azure service principal creation](https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps) | [Service principal with certificates](https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Use Azure managed identities for services supporting Azure AD authentication. For services without managed identity support use Azure AD service principals with certificate credentials and restricted ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview) | [Services supporting managed identities](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities) | [Azure service principal creation](https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps) | [Service principal with certificates](https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),Use Azure managed identities for services supporting Azure AD authentication. For services without managed identity support use Azure AD service principals with certificate credentials and restricted permissions.,"Enhanced Implementation Context:
• Azure managed identities overview: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview
• Services supporting managed identities: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities
• Azure service principal creation: https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps
• Service principal with certificates: https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: Not specified
• NIST SP800-53 r4: AC-2, AC-3, IA-4, IA-5, IA-9
• PCI-DSS v3.2.1: Not applicable

Azure Policy Examples:
• Managed identity should be used in your Function App
• Managed identity should be used in your Web App
• Service principals should be used to protect your subscriptions instead of management certificates
• Managed identity should be used in your API App
• Virtual machines' Guest Configuration extension should be deployed with system-assigned managed identity",ai_analysis,,Validated
server-farms.bicep,IM-8,Identity Management,Restrict the exposure of credential and secrets,HIGH,168.0,"The property 'settingValue' for 'CERTIFICATE_PASSWORD_GENEVACERT' at line 168 is set to an empty string. This indicates that a certificate password is either missing or may be injected insecurely at runtime. If a password is required and not securely referenced from Azure Key Vault, this creates a risk of credential exposure, enabling attackers to gain unauthorized access to sensitive certificates and escalate privileges.","Store certificate passwords in Azure Key Vault and reference them securely in the template. Do not use empty strings or hardcoded values. Update the 'settingValue' at line 168 to securely reference a Key Vault secret, and ensure the App Service uses managed identity to access Key Vault.

📚 References: [Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops)

🔵 Azure Guidance: Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.",[Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops),Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.,"Enhanced Implementation Context:
• Azure DevOps Credential Scanner setup: https://secdevtools.azurewebsites.net/helpcredscan.html
• GitHub secret scanning: https://docs.github.com/github/administering-a-repository/about-secret-scanning
• Azure Key Vault developer guide: https://docs.microsoft.com/azure/key-vault/general/developers-guide
• Secure coding practices: https://docs.microsoft.com/azure/security/develop/secure-dev-overview
• Application security guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops

Compliance Mappings:
• CIS Controls v8: 16.9, 16.12
• NIST SP800-53 r4: IA-5
• PCI-DSS v3.2.1: 3.5, 6.3, 8.2

Azure Policy Examples:
• No applicable built-in policy (requires development process implementation)
• Implement credential scanning in CI/CD pipelines
• Enforce Azure Key Vault usage for secret storage
• Monitor and audit secret access patterns
• Require managed identities for Azure service authentication",ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,29.0,"The 'defaultAction' property in 'networkAcls' for storageAccountFunc is set to 'Allow', which permits public network access to the storage account. This enables an initial access attack vector, allowing attackers to reach the storage account from any network, increasing the blast radius for data exfiltration and lateral movement.","Set 'networkAcls.defaultAction' to 'Deny' on line 31 to restrict public network access. Only allow access from explicitly defined IP rules and virtual network rules. Additionally, deploy a private endpoint for the storage account and disable public network access to enforce private connectivity.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,29.0,"The 'defaultAction' property in 'networkAcls' for fuzzStorageProperties is set to 'Allow', which permits public network access to the storage account. This exposes the storage account to the internet, enabling attackers to attempt unauthorized access and increasing the risk of data compromise.","Set 'networkAcls.defaultAction' to 'Deny' on line 65 to block public network access. Ensure only trusted IPs and virtual networks are allowed. Implement private endpoints for the storage account and disable public network access to minimize exposure.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,29.0,"The 'networkAcls' property in fuzzStorageProperties is reused for storageAccountsCorpus, and 'defaultAction' is set to 'Allow'. This configuration allows public network access to all storage accounts deployed via storageAccountsCorpus, significantly increasing the attack surface and risk of data exposure.","Update the 'networkAcls.defaultAction' property in fuzzStorageProperties (referenced on line 119) to 'Deny'. Ensure all storage accounts use private endpoints and have public network access disabled to enforce network isolation.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 58,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 7,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T17:17:42.687872,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
