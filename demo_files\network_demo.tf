# Demo Network Security Group with Security Issues
# This file demonstrates real file content in code dialogs

resource "azurerm_resource_group" "demo" {
  name     = "rg-demo-security"
  location = "East US"
}

# Network Security Group with security issues
resource "azurerm_network_security_group" "demo" {
  name                = "nsg-demo-security"
  location            = azurerm_resource_group.demo.location
  resource_group_name = azurerm_resource_group.demo.name

  # 🚨 SECURITY ISSUE: SSH from anywhere (Line 16)
  security_rule {
    name                       = "AllowSSH"
    priority                   = 1001
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "22"
    source_address_prefix      = "0.0.0.0/0"  # CRITICAL: Open to internet
    destination_address_prefix = "*"
  }

  tags = {
    Environment = "Demo"
    Purpose     = "Real Content Testing"
  }
}

# Storage account with issues
resource "azurerm_storage_account" "demo" {
  name                     = "demostorageaccount"
  resource_group_name      = azurerm_resource_group.demo.name
  location                 = azurerm_resource_group.demo.location
  account_tier             = "Standard"
  account_replication_type = "LRS"

  # 🚨 SECURITY ISSUE: Public blob access (Line 40)
  allow_blob_public_access = true
  
  # 🚨 SECURITY ISSUE: HTTP allowed (Line 43)
  https_traffic_only = false

  tags = {
    Environment = "Demo"
    Purpose     = "Real Content Testing"
  }
}