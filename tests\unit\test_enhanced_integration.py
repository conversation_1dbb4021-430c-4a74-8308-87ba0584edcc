#!/usr/bin/env python3
"""
Test script to verify enhanced resource-control mapping integration
"""

import logging
import sys
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_enhanced_integration():
    """Test the enhanced integration with security_opt.py"""
    print("🧪 Testing Enhanced Integration with security_opt.py")
    print("=" * 60)
    
    try:
        # Import the SecurityPRReviewer class
        from security_opt import SecurityPRReviewer
        
        print("✅ Successfully imported SecurityPRReviewer")
        
        # Initialize the reviewer
        reviewer = SecurityPRReviewer(
            local_folder="."  # Current directory for local mode
        )
        
        print("✅ Successfully initialized SecurityPRReviewer")
        
        # Test enhanced mapper initialization
        if hasattr(reviewer, 'enhanced_mapper') and reviewer.enhanced_mapper:
            print("✅ Enhanced mapper is initialized")
            
            # Test control database
            control_count = len(reviewer.enhanced_mapper.control_database)
            print(f"📊 Enhanced mapper loaded {control_count} controls")
            
            # Test URL links
            links_count = len(reviewer.enhanced_mapper.url_links_database)
            print(f"🔗 Enhanced mapper has {links_count} controls with URLs")
            
        else:
            print("❌ Enhanced mapper is not initialized")
            return False
        
        # Test get_controls_for_resource method
        test_resources = [
            "Microsoft.Storage/storageAccounts",
            "Microsoft.KeyVault/vaults",
            "Microsoft.Network/networkSecurityGroups"
        ]
        
        print(f"\n🏗️ Testing get_controls_for_resource method:")
        for resource_type in test_resources:
            try:
                controls = reviewer.get_controls_for_resource(resource_type)
                control_ids = [c.get('id', 'UNKNOWN') for c in controls]
                print(f"   📋 {resource_type}: {len(controls)} controls")
                print(f"      Control IDs: {', '.join(sorted(control_ids))}")
                
                # Check if we have comprehensive coverage
                if len(controls) >= 15:  # Should have many more controls now
                    print(f"      ✅ Comprehensive coverage achieved")
                else:
                    print(f"      ⚠️ Limited coverage - only {len(controls)} controls")
                    
            except Exception as e:
                print(f"   ❌ Error testing {resource_type}: {e}")
                return False
        
        # Test _find_relevant_controls method
        print(f"\n🔍 Testing _find_relevant_controls method:")
        for resource_type in test_resources:
            try:
                controls = reviewer._find_relevant_controls(resource_type)
                control_ids = [c.get('id', 'UNKNOWN') for c in controls]
                print(f"   📋 {resource_type}: {len(controls)} controls")
                print(f"      Control IDs: {', '.join(sorted(control_ids))}")
                
                # Check for comprehensive coverage
                unique_domains = set()
                for control in controls:
                    domain = control.get('domain', 'Unknown')
                    unique_domains.add(domain)
                
                print(f"      🏷️ Domains covered: {', '.join(sorted(unique_domains))}")
                
                if len(unique_domains) >= 2:  # Should cover multiple domains
                    print(f"      ✅ Multi-domain coverage achieved")
                else:
                    print(f"      ⚠️ Limited domain coverage")
                    
            except Exception as e:
                print(f"   ❌ Error testing {resource_type}: {e}")
                return False
        
        # Test _extract_control_links method
        print(f"\n🔗 Testing _extract_control_links method:")
        test_controls = ["IM-1", "NS-1", "NS-2", "DP-1", "DP-3"]
        
        for control_id in test_controls:
            try:
                links = reviewer._extract_control_links(control_id)
                raw_links = links.get('raw_links', [])
                formatted_links = links.get('formatted_links', '')
                azure_guidance = links.get('azure_guidance', '')
                
                print(f"   📋 {control_id}:")
                print(f"      🔗 Raw links: {len(raw_links)}")
                print(f"      📝 Formatted links: {'Yes' if formatted_links else 'No'}")
                print(f"      🔵 Azure guidance: {'Yes' if azure_guidance else 'No'}")
                
                if raw_links:
                    print(f"      📎 Example URL: {raw_links[0][:60]}...")
                    print(f"      ✅ Link extraction working")
                else:
                    print(f"      ⚠️ No links found")
                    
            except Exception as e:
                print(f"   ❌ Error testing {control_id}: {e}")
                return False
        
        print(f"\n🎉 All integration tests passed!")
        print(f"\n📊 Summary:")
        print(f"   ✅ Enhanced mapper initialized with {control_count} controls")
        print(f"   ✅ URL links available for {links_count} controls")
        print(f"   ✅ Resource mapping working for all test resources")
        print(f"   ✅ Control selection providing comprehensive coverage")
        print(f"   ✅ Link extraction working for all test controls")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        logger.exception("Detailed error:")
        return False

def test_control_coverage():
    """Test that we're getting comprehensive control coverage"""
    print("\n🔍 Testing Control Coverage Comprehensiveness")
    print("=" * 60)
    
    try:
        from security_opt import SecurityPRReviewer
        
        reviewer = SecurityPRReviewer(
            local_folder="."
        )
        
        # Test a storage account to see all controls we get
        resource_type = "Microsoft.Storage/storageAccounts"
        controls = reviewer._find_relevant_controls(resource_type)
        
        print(f"📋 Controls for {resource_type}:")
        
        # Group by domain
        domain_controls = {}
        for control in controls:
            domain = control.get('domain', 'Unknown')
            if domain not in domain_controls:
                domain_controls[domain] = []
            domain_controls[domain].append(control.get('id', 'UNKNOWN'))
        
        total_controls = 0
        for domain, control_ids in sorted(domain_controls.items()):
            print(f"   🏷️ {domain}: {len(control_ids)} controls")
            print(f"      📋 {', '.join(sorted(control_ids))}")
            total_controls += len(control_ids)
        
        print(f"\n📊 Total controls: {total_controls}")
        
        # Check if we have the expected comprehensive coverage
        expected_domains = ["Identity Management", "Network Security", "Data Protection"]
        missing_domains = []
        
        for expected_domain in expected_domains:
            if expected_domain not in domain_controls:
                missing_domains.append(expected_domain)
        
        if missing_domains:
            print(f"⚠️ Missing domains: {', '.join(missing_domains)}")
        else:
            print(f"✅ All expected domains covered")
        
        # Check for specific controls that should be present
        all_control_ids = [c.get('id', 'UNKNOWN') for c in controls]
        expected_controls = [
            "IM-1", "IM-2", "IM-3",  # Identity Management
            "NS-1", "NS-2", "NS-3",  # Network Security  
            "DP-1", "DP-2", "DP-3"   # Data Protection
        ]
        
        missing_controls = []
        for expected_control in expected_controls:
            if expected_control not in all_control_ids:
                missing_controls.append(expected_control)
        
        if missing_controls:
            print(f"⚠️ Missing expected controls: {', '.join(missing_controls)}")
        else:
            print(f"✅ All expected controls present")
        
        # Success criteria
        if total_controls >= 20 and len(domain_controls) >= 3 and not missing_controls:
            print(f"\n🎉 Comprehensive control coverage achieved!")
            return True
        else:
            print(f"\n❌ Control coverage is insufficient")
            return False
            
    except Exception as e:
        print(f"❌ Error testing control coverage: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Enhanced Resource-Control Mapping Integration Test")
    print("=" * 80)
    
    success = True
    
    # Test basic integration
    if not test_enhanced_integration():
        print("❌ Enhanced integration test failed")
        success = False
    
    # Test control coverage
    if not test_control_coverage():
        print("❌ Control coverage test failed")
        success = False
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("Enhanced resource-control mapping is fully integrated and working.")
        print("\nThe system will now provide:")
        print("- Comprehensive control coverage (all 27 controls)")
        print("- Rich URL links in reports")
        print("- Multi-domain security analysis")
        print("- Professional-quality recommendations")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please review the errors above and fix the integration issues.")
        sys.exit(1)

if __name__ == "__main__":
    main()
