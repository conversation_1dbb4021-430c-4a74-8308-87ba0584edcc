File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
Parameters\LacpBillingExhaust.Parameters-LacpBillingExhaust.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'usageAccountSystemAssignedIdentityPrincipalId' flows across template boundary,Secure parameter 'usageAccountSystemAssignedIdentityPrincipalId' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyVaultSuffix' flows across template boundary,Secure parameter 'keyVaultSuffix' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpRegion.Parameters-LacpRegionResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'regionalCosmosAccountLocation' flows across template boundary,Secure parameter 'regionalCosmosAccountLocation' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'dasStorageAccountKey' flows across template boundary,Secure parameter 'dasStorageAccountKey' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'globalKeyVaultName' flows across template boundary,Secure parameter 'globalKeyVaultName' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Parameters\LacpStamp.Parameters-LacpStampResources.json,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'createSharedKeyVault' flows across template boundary,Secure parameter 'createSharedKeyVault' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
Templates\IngestionStorageAccount.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,44.0,"The storage account resource at line 044 sets 'allowBlobPublicAccess' to false, which is correct, but there is no explicit configuration for network access controls such as 'networkAcls', 'publicNetworkAccess', or private endpoints. By default, storage accounts are accessible from all networks unless restricted. This enables an attack vector for initial access and data exfiltration if the storage account is exposed to the public internet, increasing the blast radius to all data in the account.",Explicitly restrict public network access by setting 'publicNetworkAccess' to 'Disabled' and configuring 'networkAcls' to allow only required subnets or private endpoints. Implement Azure Private Link to ensure the storage account is only accessible from trusted networks.,,,,ai_analysis,,Validated
Templates\IngestionStorageAccount.Template.json,NS-5,Network Security,Use Private Endpoints,HIGH,44.0,"The storage account resource at line 044 does not configure a private endpoint. Without a private endpoint, the storage account is accessible over the public Azure network, increasing the attack surface for lateral movement and data exfiltration. Attackers who compromise any resource with internet access could attempt to access the storage account.",Configure a private endpoint for the storage account to ensure all access occurs over the Azure backbone network. Update the resource definition to include a 'Microsoft.Network/privateEndpoints' resource linked to the storage account.,,,,ai_analysis,,Validated
Templates\LacpBillingExhaust.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,61.0,"The 'trustedExternalTenants' property on the Microsoft.Kusto/clusters resource (Line 061) allows access from an external Azure AD tenant (value: '72f988bf-86f1-41af-91ab-2d7cd011db47'). This configuration enables cross-tenant access, which can expose the cluster to unauthorized access from outside the enterprise boundary, increasing the risk of initial access, lateral movement, and data exfiltration. The blast radius includes potential compromise of all data and operations within the Kusto cluster if the external tenant is breached or misconfigured.","Restrict 'trustedExternalTenants' to only those tenants that are explicitly required for business operations. Remove or limit external tenant IDs to reduce the attack surface. Where possible, use Private Link or service endpoints to further restrict access to the cluster. Review and update the configuration on Line 061 to minimize exposure in accordance with Azure Security Benchmark NS-2.",,,,ai_analysis,,Validated
Templates\LacpGeo.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,225.0,"The CosmosDB resource at line 225 has 'publicNetworkAccess' set to 'Enabled', which exposes the database account to the public internet. This enables direct attack vectors such as brute force, credential stuffing, and exploitation of service vulnerabilities, increasing the risk of unauthorized access and data exfiltration. The blast radius includes all data and operations within this CosmosDB account.",Set 'publicNetworkAccess' to 'Disabled' in the CosmosDB resource configuration to restrict access to private endpoints only. Implement Azure Private Link or service endpoints to limit exposure and ensure only trusted networks can access the database. Example: 'publicNetworkAccess': 'Disabled'.,,,,ai_analysis,,Validated
Templates\LacpGeo.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,228.0,"The CosmosDB resource at line 228 has 'isVirtualNetworkFilterEnabled' set to 'false', which disables virtual network filtering. This allows unrestricted access from any network, enabling lateral movement and data exfiltration by attackers who gain access to the public endpoint.","Set 'isVirtualNetworkFilterEnabled' to 'true' and define 'virtualNetworkRules' to restrict access to only approved subnets. This enforces network boundaries and reduces the attack surface. Example: 'isVirtualNetworkFilterEnabled': true, and specify required 'virtualNetworkRules'.",,,,ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,105.0,"CosmosDB resource 'publicNetworkAccess' is set to 'Enabled' on line 105, exposing the database account to the public internet. This enables initial access attack vectors, allowing attackers to attempt brute force, credential stuffing, or exploit vulnerabilities directly from the internet. The blast radius includes potential compromise of all data in the CosmosDB account and lateral movement to other resources.","Set 'publicNetworkAccess' to 'Disabled' to restrict access to private endpoints only. Implement Azure Private Link or service endpoints to ensure only trusted networks can access the CosmosDB account. Example: ""publicNetworkAccess"": ""Disabled"".",,,,ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,108.0,"CosmosDB resource 'isVirtualNetworkFilterEnabled' is set to 'false' on line 108, disabling virtual network filtering. This allows unrestricted access from any network, increasing the risk of unauthorized access and data exfiltration. Attackers can exploit this to access the database from untrusted networks, expanding the blast radius to all data in the account.","Set 'isVirtualNetworkFilterEnabled' to 'true' and define appropriate 'virtualNetworkRules' to restrict access to trusted subnets. Example: ""isVirtualNetworkFilterEnabled"": true.",,,,ai_analysis,,Validated
Templates\LacpGlobal.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,127.0,"CosmosDB resource 'ipRules' is an empty array on line 127, meaning no IP restrictions are enforced. Combined with public network access, this allows any IP to connect, enabling broad attack surface for initial access and data exfiltration.","Define 'ipRules' to restrict access to only required, trusted IP addresses or ranges. Example: ""ipRules"": [ { ""ipAddressOrRange"": ""x.x.x.x"" } ].",,,,ai_analysis,,Validated
Templates\LacpRegion.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,561.0,"The CosmosDB account resource at line 558 sets 'publicNetworkAccess' to 'Enabled', exposing the database to the public internet. Combined with 'isVirtualNetworkFilterEnabled' set to false (line 561) and an empty 'ipRules' array (line 580), this configuration allows unrestricted public access to the CosmosDB account. Attackers can exploit this exposure for initial access, data exfiltration, and lateral movement, significantly increasing the blast radius and risk of data compromise.",Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to true. Define appropriate 'virtualNetworkRules' or 'ipRules' to restrict access to trusted networks only. Consider implementing Private Endpoints for CosmosDB to eliminate public exposure. Reference: ASB NS-2 (Protect public endpoints).,,,,ai_analysis,,Validated
Templates\LacpRegion.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,561.0,"The CosmosDB account resource at line 561 has 'isVirtualNetworkFilterEnabled' set to false, disabling network-based access controls. This allows any client on the internet to access the database if public network access is enabled, enabling attackers to bypass network segmentation and directly target sensitive data.",Set 'isVirtualNetworkFilterEnabled' to true and define 'virtualNetworkRules' to restrict access to only trusted subnets. This will enforce network boundaries and reduce the attack surface. Reference: ASB NS-2 (Protect public endpoints).,,,,ai_analysis,,Validated
Templates\LacpRegion.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,580.0,"The CosmosDB account resource at line 580 has an empty 'ipRules' array, meaning no IP-based restrictions are in place. With public network access enabled, this allows any IP address to connect, increasing the risk of unauthorized access and data exfiltration.","Populate the 'ipRules' array with only trusted IP addresses or ranges that require access. Alternatively, disable public network access and use Private Endpoints. Reference: ASB NS-2 (Protect public endpoints).",,,,ai_analysis,,Validated
Templates\LacpStamp.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,164.0,"The 'target' property in the Microsoft.Network/trafficManagerProfiles/externalEndpoints resource exposes an endpoint using a public DNS name ('cloudapp.azure.com'). This enables direct public access to backend services, increasing the risk of initial access, data exfiltration, and lateral movement by attackers. Without IP restrictions or Private Link, the attack surface is unnecessarily broad.",Restrict public access by implementing Azure Private Link or service endpoints for backend services. Limit allowed IP ranges to only trusted sources. Consider using Azure Front Door or Application Gateway with Web Application Firewall (WAF) to control and monitor public exposure. Update the 'target' property to reference a private endpoint or restrict access via NSG/firewall rules.,,,,ai_analysis,,Validated
Templates\LacpStamp.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,164.0,"The 'target' property in the Microsoft.Network/trafficManagerProfiles/externalEndpoints resource exposes an endpoint using a public DNS name ('cloudapp.azure.com'). This configuration allows public access to backend resources, creating an attack vector for initial access, data exfiltration, and lateral movement. No evidence of IP restriction or Private Link is present.",Restrict public access by configuring Private Endpoints or service endpoints for backend services. Limit allowed IP ranges to trusted sources only. Use Azure Front Door or Application Gateway with WAF to control and monitor public exposure. Update the 'target' property to reference a private endpoint or restrict access via NSG/firewall rules.,,,,ai_analysis,,Validated
Templates\RoleAssignment.Template.json,IM-2,Identity Management,Protect identity and authentication systems,HIGH,5.0,"The 'roleDefinitionId' property on line 72 assigns the built-in 'Contributor' role to the principal specified by 'Ev2BuildoutServicePrincipalId'. Assigning Contributor at the subscription scope to a service principal enables broad permissions, including resource creation, modification, and deletion. If this service principal is compromised, an attacker could escalate privileges, create backdoors, or exfiltrate data across the entire subscription. This configuration increases the blast radius and enables privilege escalation and lateral movement.","Restrict the 'Contributor' role assignment to the minimum required scope (resource group or specific resources) and use least privilege roles. Require strong authentication (MFA) for the service principal, monitor its activity, and regularly review role assignments. Implement Privileged Identity Management (PIM) for just-in-time access. Reference: Azure Security Benchmark IM-2.",,,,ai_analysis,,Validated
Templates\TrafficManagerEndpoints.Template.json,NS-2,Network Security,Protect public endpoints,CRITICAL,44.0,"The resource 'Microsoft.Network/trafficManagerProfiles/externalEndpoints' defined on line 44 creates external endpoints that are, by default, publicly accessible. Without explicit access restrictions or use of Private Link/service endpoints, this exposes the service to the public internet, enabling initial access attack vectors and increasing the blast radius for data exfiltration and service disruption.","Restrict public access to the Traffic Manager external endpoints by configuring access controls to allow only required IP addresses. Where possible, use Azure Private Link or service endpoints to limit exposure. Review the 'properties.target' and ensure it does not resolve to a public endpoint unless strictly necessary. Reference: NS-2.",,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 21,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 6,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T10:05:46.489785,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
