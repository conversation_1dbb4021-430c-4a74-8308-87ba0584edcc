#!/usr/bin/env python3
"""
Test script to demonstrate real file content in Glass UI code dialogs.
Creates actual files with security issues and generates reports that show real content.
"""

import os
import sys
from pathlib import Path
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from security_opt import SecurityPRReviewer

def create_real_test_files():
    """Create real test files with actual security issues."""
    
    test_files = {
        'infrastructure/storage.bicep': '''// Azure Storage Account Configuration
param storageAccountName string = 'securitytest${uniqueString(resourceGroup().id)}'
param location string = resourceGroup().location
param environment string = 'dev'

// Storage Account Resource
resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {
  name: storageAccountName
  location: location
  kind: 'StorageV2'
  sku: {
    name: 'Standard_LRS'
  }
  properties: {
    // SECURITY ISSUE: Public blob access enabled (Line 15)
    allowBlobPublicAccess: true
    
    // SECURITY ISSUE: Weak TLS version (Line 18)
    minimumTlsVersion: 'TLS1_0'
    
    // SECURITY ISSUE: HTTP traffic allowed (Line 21)
    supportsHttpsTrafficOnly: false
    
    // Good: Encryption enabled
    encryption: {
      services: {
        blob: {
          enabled: true
        }
        file: {
          enabled: true
        }
      }
      keySource: 'Microsoft.Storage'
    }
    
    // Network access configuration
    networkAcls: {
      defaultAction: 'Allow'  // SECURITY ISSUE: Default allow (Line 37)
      bypass: 'AzureServices'
    }
  }
  
  tags: {
    Environment: environment
    Purpose: 'Security Testing'
    Owner: 'DevSecOps Team'
  }
}

// Output the storage account details
output storageAccountId string = storageAccount.id
output storageAccountName string = storageAccount.name
output primaryEndpoints object = storageAccount.properties.primaryEndpoints''',

        'infrastructure/network.tf': '''# Network Security Group Configuration
resource "azurerm_resource_group" "main" {
  name     = "rg-security-test"
  location = "East US"
}

# Network Security Group with security issues
resource "azurerm_network_security_group" "main" {
  name                = "nsg-security-test"
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name

  # SECURITY ISSUE: Allow SSH from anywhere (Line 14)
  security_rule {
    name                       = "SSH"
    priority                   = 1001
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "22"
    source_address_prefix      = "0.0.0.0/0"  # CRITICAL: Open to internet
    destination_address_prefix = "*"
  }

  # SECURITY ISSUE: Allow RDP from anywhere (Line 26)
  security_rule {
    name                       = "RDP"
    priority                   = 1002
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "3389"
    source_address_prefix      = "0.0.0.0/0"  # CRITICAL: Open to internet
    destination_address_prefix = "*"
  }

  # Good rule: Allow HTTP from load balancer only
  security_rule {
    name                       = "HTTP"
    priority                   = 1003
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "80"
    source_address_prefix      = "********/24"
    destination_address_prefix = "*"
  }

  tags = {
    Environment = "Development"
    Purpose     = "Security Testing"
  }
}

# Storage account with security issues
resource "azurerm_storage_account" "main" {
  name                     = "securityteststorage"
  resource_group_name      = azurerm_resource_group.main.name
  location                 = azurerm_resource_group.main.location
  account_tier             = "Standard"
  account_replication_type = "LRS"

  # SECURITY ISSUE: Public blob access (Line 60)
  allow_blob_public_access = true
  
  # SECURITY ISSUE: Weak TLS (Line 63)
  min_tls_version = "TLS1_0"
  
  # SECURITY ISSUE: HTTP allowed (Line 66)
  https_traffic_only = false

  tags = {
    Environment = "Development"
    Purpose     = "Security Testing"
  }
}''',

        'kubernetes/deployment.yaml': '''apiVersion: apps/v1
kind: Deployment
metadata:
  name: security-test-app
  namespace: default
  labels:
    app: security-test
    version: v1.0.0
spec:
  replicas: 3
  selector:
    matchLabels:
      app: security-test
  template:
    metadata:
      labels:
        app: security-test
    spec:
      containers:
      - name: web-server
        image: nginx:latest
        ports:
        - containerPort: 80
        # SECURITY ISSUE: Running as root (Line 22)
        securityContext:
          runAsUser: 0
          runAsGroup: 0
          # SECURITY ISSUE: Privileged container (Line 26)
          privileged: true
          # SECURITY ISSUE: Allow privilege escalation (Line 28)
          allowPrivilegeEscalation: true
        
        # SECURITY ISSUE: No resource limits (Line 31)
        # resources: {}
        
        env:
        - name: ENV
          value: "development"
        # SECURITY ISSUE: Hardcoded secret (Line 36)
        - name: API_KEY
          value: "sk-1234567890abcdef"
        
        volumeMounts:
        - name: data-volume
          mountPath: /data
          # SECURITY ISSUE: Read-write filesystem (Line 43)
          readOnly: false
      
      # SECURITY ISSUE: No security context at pod level
      # securityContext: {}
      
      volumes:
      - name: data-volume
        # SECURITY ISSUE: Host path volume (Line 50)
        hostPath:
          path: /var/lib/app-data
          type: DirectoryOrCreate
      
      # SECURITY ISSUE: No service account specified
      # serviceAccountName: security-test-sa
---
apiVersion: v1
kind: Service
metadata:
  name: security-test-service
spec:
  selector:
    app: security-test
  ports:
  - port: 80
    targetPort: 80
  # SECURITY ISSUE: LoadBalancer exposes to internet (Line 67)
  type: LoadBalancer'''
    }
    
    # Create directories and files
    created_files = []
    for file_path, content in test_files.items():
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # Write file content
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        created_files.append(file_path)
        print(f"✅ Created test file: {file_path}")
    
    return created_files

def create_sample_findings_with_real_files(created_files):
    """Create sample findings that reference the real files."""
    
    findings = [
        {
            "control_id": "IM-1",
            "severity": "CRITICAL",
            "file_path": "infrastructure/storage.bicep",
            "line": 15,
            "description": "Storage account allows public blob access. This exposes all blobs to the internet without authentication, creating a significant data exposure risk.",
            "remediation": "Set allowBlobPublicAccess to false and use private endpoints, SAS tokens, or Azure AD authentication for secure access.",
            "domain": "Identity Management"
        },
        {
            "control_id": "NS-1",
            "severity": "CRITICAL", 
            "file_path": "infrastructure/network.tf",
            "line": 23,
            "description": "Network Security Group allows SSH access from any source (0.0.0.0/0). This creates a critical security vulnerability by exposing SSH to the entire internet.",
            "remediation": "Restrict SSH access to specific IP ranges, use Azure Bastion for secure access, or implement just-in-time (JIT) access.",
            "domain": "Network Security"
        },
        {
            "control_id": "NS-2", 
            "severity": "CRITICAL",
            "file_path": "infrastructure/network.tf",
            "line": 35,
            "description": "Network Security Group allows RDP access from any source (0.0.0.0/0). This exposes Windows remote desktop to internet-based attacks.",
            "remediation": "Restrict RDP access to specific management networks or use Azure Bastion for secure remote access.",
            "domain": "Network Security"
        },
        {
            "control_id": "DP-1",
            "severity": "HIGH",
            "file_path": "infrastructure/storage.bicep", 
            "line": 18,
            "description": "Storage account configured with weak TLS version (TLS1_0). This version has known security vulnerabilities and should not be used.",
            "remediation": "Update minimumTlsVersion to 'TLS1_2' or higher to ensure secure data transmission.",
            "domain": "Data Protection"
        },
        {
            "control_id": "CS-1",
            "severity": "CRITICAL",
            "file_path": "kubernetes/deployment.yaml",
            "line": 26,
            "description": "Container is configured to run in privileged mode. This grants the container full access to the host system, bypassing security boundaries.",
            "remediation": "Remove 'privileged: true' and use specific capabilities instead. Implement proper security contexts with runAsNonRoot.",
            "domain": "Container Security"
        },
        {
            "control_id": "CS-2",
            "severity": "HIGH",
            "file_path": "kubernetes/deployment.yaml",
            "line": 36,
            "description": "Hardcoded API key found in environment variables. This exposes sensitive credentials in plain text within the deployment configuration.",
            "remediation": "Use Kubernetes Secrets or Azure Key Vault to securely manage API keys and sensitive configuration data.",
            "domain": "Container Security"
        },
        {
            "control_id": "DP-2",
            "severity": "MEDIUM",
            "file_path": "infrastructure/network.tf",
            "line": 63,
            "description": "Storage account allows HTTP traffic (https_traffic_only = false). This permits unencrypted data transmission.",
            "remediation": "Set https_traffic_only to true to enforce encrypted HTTPS connections for all storage operations.",
            "domain": "Data Protection"
        }
    ]
    
    return findings

def test_real_file_content_dialog():
    """Test the real file content functionality."""
    
    print("🧪 Testing Real File Content in Glass UI Code Dialogs...")
    
    # Create real test files
    created_files = create_real_test_files()
    
    try:
        # Create sample findings that reference real files
        sample_findings = create_sample_findings_with_real_files(created_files)
        
        # Initialize reviewer with local folder
        reviewer = SecurityPRReviewer(local_folder="./")
        
        # Generate HTML report with real file content
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        html_path = f"real_file_content_report_{timestamp}.html"
        
        print(f"📄 Generating report with {len(sample_findings)} findings...")
        print(f"📁 Files included: {len(created_files)} real files with embedded content")
        
        reviewer._export_findings_to_html(sample_findings, html_path)
        
        print(f"✅ Report generated: {html_path}")
        print(f"💾 File size: {os.path.getsize(html_path):,} bytes")
        
        # Display file information
        print(f"\n📋 Real Files Created for Testing:")
        for file_path in created_files:
            file_size = os.path.getsize(file_path)
            line_count = sum(1 for _ in open(file_path, 'r'))
            print(f"   📄 {file_path} ({line_count} lines, {file_size:,} bytes)")
        
        print(f"\n🎯 Test the Real File Content Feature:")
        print(f"   1. Open {html_path} in your browser")
        print(f"   2. Click any 'View Code' button next to line numbers")
        print(f"   3. See the ACTUAL file content with highlighted problematic lines")
        print(f"   4. Notice the real line numbers and surrounding context")
        print(f"   5. Compare with the file paths and line numbers in the findings")
        
        print(f"\n🔍 Expected Behavior:")
        print(f"   ✅ Real file content displayed (not sample code)")
        print(f"   ✅ Correct line highlighting based on actual file content")
        print(f"   ✅ Proper context lines showing surrounding code")
        print(f"   ✅ Accurate line numbers matching the security issues")
        print(f"   ✅ File information showing total lines")
        
        return html_path
        
    finally:
        # Clean up test files
        print(f"\n🧹 Cleaning up test files...")
        for file_path in created_files:
            try:
                os.remove(file_path)
                print(f"   🗑️ Removed: {file_path}")
            except Exception as e:
                print(f"   ⚠️ Could not remove {file_path}: {e}")
        
        # Remove test directories if empty
        for directory in ['infrastructure', 'kubernetes']:
            try:
                if os.path.exists(directory) and not os.listdir(directory):
                    os.rmdir(directory)
                    print(f"   🗑️ Removed empty directory: {directory}")
            except Exception as e:
                print(f"   ⚠️ Could not remove directory {directory}: {e}")

if __name__ == "__main__":
    try:
        report_path = test_real_file_content_dialog()
        
        # Optional: Open in browser
        import webbrowser
        user_input = input(f"\n🚀 Open the report in your browser? (y/n): ")
        if user_input.lower() in ['y', 'yes']:
            webbrowser.open(f"file://{os.path.abspath(report_path)}")
            print("🌐 Report opened in your default browser!")
        
        print("\n🎉 Real file content test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
