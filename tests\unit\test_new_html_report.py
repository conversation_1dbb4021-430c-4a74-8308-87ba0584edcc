#!/usr/bin/env python3
"""
Test script for the new responsive HTML report generation.
"""

import os
import sys
from pathlib import Path
from security_opt import SecurityPRR<PERSON>iewer

def create_sample_findings():
    """Create sample findings for testing the HTML report."""
    return [
        {
            "control_id": "NS-1",
            "severity": "CRITICAL",
            "file_path": "network.bicep",
            "line": 25,
            "description": "Network Security Group allows unrestricted inbound access from the internet on port 22 (SSH). This creates a significant security risk as it exposes the SSH service to potential brute force attacks and unauthorized access attempts.",
            "remediation": "Restrict SSH access to specific IP addresses or ranges. Consider using Azure Bastion for secure remote access instead of exposing SSH directly to the internet.",
            "matching_content": "securityRules: [\n  {\n    name: 'SSH'\n    properties: {\n      access: 'Allow'\n      direction: 'Inbound'\n      sourceAddressPrefix: '*'\n      destinationPortRange: '22'\n    }\n  }\n]"
        },
        {
            "control_id": "IM-1",
            "severity": "HIGH",
            "file_path": "storage.bicep",
            "line": 42,
            "description": "Storage account is configured to allow public blob access which could lead to unauthorized data exposure. Public access should be disabled unless specifically required.",
            "remediation": "Set 'allowBlobPublicAccess' to false in the storage account configuration. Use private endpoints or shared access signatures for controlled access.",
            "matching_content": "resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {\n  properties: {\n    allowBlobPublicAccess: true\n  }\n}"
        },
        {
            "control_id": "DP-3",
            "severity": "MEDIUM",
            "file_path": "webapp.bicep",
            "line": 18,
            "description": "Web application is not configured with HTTPS-only access. This allows unencrypted HTTP traffic which could expose sensitive data in transit.",
            "remediation": "Enable HTTPS-only access by setting 'httpsOnly' property to true in the web app configuration.",
            "matching_content": "resource webApp 'Microsoft.Web/sites@2021-02-01' = {\n  properties: {\n    httpsOnly: false\n  }\n}"
        },
        {
            "control_id": "LT-4",
            "severity": "LOW",
            "file_path": "keyvault.bicep",
            "line": 33,
            "description": "Key Vault does not have diagnostic logging enabled. This limits visibility into access patterns and potential security incidents.",
            "remediation": "Enable diagnostic settings for the Key Vault to log access events to Azure Monitor or a Log Analytics workspace.",
            "matching_content": "resource keyVault 'Microsoft.KeyVault/vaults@2021-06-01-preview' = {\n  properties: {\n    // Missing diagnostic settings\n  }\n}"
        },
        {
            "control_id": "NS-2",
            "severity": "HIGH",
            "file_path": "database.bicep",
            "line": 56,
            "description": "SQL Database server allows connections from all Azure services without IP restrictions. This broad access could be exploited if other Azure resources are compromised.",
            "remediation": "Configure specific firewall rules to allow only necessary Azure services and IP ranges. Disable 'Allow Azure services' if not required.",
            "matching_content": "resource sqlServer 'Microsoft.Sql/servers@2021-02-01-preview' = {\n  properties: {\n    firewallRules: [\n      {\n        name: 'AllowAllAzureServices'\n        startIpAddress: '0.0.0.0'\n        endIpAddress: '0.0.0.0'\n      }\n    ]\n  }\n}"
        }
    ]

def test_html_report():
    """Test the new HTML report generation."""
    print("🧪 Testing new responsive HTML report generation...")
    
    # Create a test directory
    test_dir = Path("test_reports")
    test_dir.mkdir(exist_ok=True)
    
    # Create sample findings
    sample_findings = create_sample_findings()
    
    # Initialize reviewer (using local mode for testing)
    reviewer = SecurityPRReviewer(local_folder="./test")
    
    # Generate HTML report
    html_path = test_dir / "test_responsive_report.html"
    reviewer._export_findings_to_html(sample_findings, str(html_path))
    
    print(f"✅ HTML report generated successfully!")
    print(f"📄 Report location: {html_path.absolute()}")
    print(f"📊 Report contains {len(sample_findings)} findings")
    print(f"💾 File size: {html_path.stat().st_size:,} bytes")
    
    # Print summary of findings by severity
    severity_counts = {}
    for finding in sample_findings:
        severity = finding.get("severity", "UNKNOWN")
        severity_counts[severity] = severity_counts.get(severity, 0) + 1
    
    print("\n📈 Findings Summary:")
    for severity, count in severity_counts.items():
        print(f"   {severity}: {count} finding(s)")
    
    print(f"\n🌐 Open the report in your browser:")
    print(f"   file://{html_path.absolute()}")
    
    return html_path

if __name__ == "__main__":
    try:
        html_path = test_html_report()
        
        # Optionally open the report in the default browser
        if len(sys.argv) > 1 and sys.argv[1] == "--open":
            import webbrowser
            webbrowser.open(f"file://{html_path.absolute()}")
            print("🚀 Report opened in your default browser!")
            
    except Exception as e:
        print(f"❌ Error testing HTML report: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
