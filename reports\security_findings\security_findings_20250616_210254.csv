Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
CRITICAL,DP-1,IngestionStorageAccount.Template.json,38,Storage account resource at line 38 does not explicitly enable encryption at rest. The 'encryption' property is missing from the resource definition.,"Add the 'encryption' property to the storage account resource at line 38, specifying 'services', 'keySource', and optionally customer-managed keys to ensure encryption at rest is enabled as per Azure Security Benchmark DP-1.",N/A,AI,Generic
CRITICAL,DP-1,IngestionStorageAccount.Template.json,59,Storage account resource at line 59 does not explicitly enable encryption at rest. The 'encryption' property is missing from the resource definition.,"Add the 'encryption' property to the storage account resource at line 59, specifying 'services', 'keySource', and optionally customer-managed keys to ensure encryption at rest is enabled as per Azure Security Benchmark DP-1.",N/A,AI,Generic
CRITICAL,NS-1,IngestionStorageAccount.Template.json,38,Storage account resource at line 38 does not restrict network access using network security groups (NSGs) or Azure Firewall. No 'networkAcls' property is defined to limit access.,"Add the 'networkAcls' property to the storage account resource at line 38 to restrict access to trusted networks and subnets, or integrate with Azure Firewall/NSGs as required by Azure Security Benchmark NS-1.",N/A,AI,Generic
CRITICAL,NS-1,IngestionStorageAccount.Template.json,59,Storage account resource at line 59 does not restrict network access using network security groups (NSGs) or Azure Firewall. No 'networkAcls' property is defined to limit access.,"Add the 'networkAcls' property to the storage account resource at line 59 to restrict access to trusted networks and subnets, or integrate with Azure Firewall/NSGs as required by Azure Security Benchmark NS-1.",N/A,AI,Generic
CRITICAL,NS-2,IngestionStorageAccount.Template.json,38,"Storage account resource at line 38 does not define 'networkAcls', which may result in a public endpoint being exposed by default.","Explicitly configure the 'networkAcls' property at line 38 to restrict public network access and allow only required private endpoints, as per Azure Security Benchmark NS-2.",N/A,AI,Generic
CRITICAL,NS-2,IngestionStorageAccount.Template.json,59,"Storage account resource at line 59 does not define 'networkAcls', which may result in a public endpoint being exposed by default.","Explicitly configure the 'networkAcls' property at line 59 to restrict public network access and allow only required private endpoints, as per Azure Security Benchmark NS-2.",N/A,AI,Generic
CRITICAL,NS-3,IngestionStorageAccount.Template.json,38,Storage account resource at line 38 does not implement Network Security Groups (NSGs) to control inbound and outbound traffic.,"Integrate the storage account at line 38 with NSGs by associating it with a subnet protected by an NSG, or configure 'networkAcls' to restrict traffic as per Azure Security Benchmark NS-3.",N/A,AI,Generic
CRITICAL,NS-3,IngestionStorageAccount.Template.json,59,Storage account resource at line 59 does not implement Network Security Groups (NSGs) to control inbound and outbound traffic.,"Integrate the storage account at line 59 with NSGs by associating it with a subnet protected by an NSG, or configure 'networkAcls' to restrict traffic as per Azure Security Benchmark NS-3.",N/A,AI,Generic
CRITICAL,NS-1,LacpBillingExhaust.Template.json,27,"Microsoft.Kusto/clusters resource at line 27 does not specify any network security group (NSG), private endpoint, or Azure Firewall configuration. This exposes the cluster to potential unauthorized network access, violating ASB NS-1.",Restrict network access to the Kusto cluster by configuring a private endpoint or associating the cluster with a subnet protected by a Network Security Group (NSG) or Azure Firewall.,N/A,AI,Generic
CRITICAL,NS-2,LacpBillingExhaust.Template.json,27,"Microsoft.Kusto/clusters resource at line 27 does not restrict public endpoints. By default, the cluster may be accessible from the public internet, violating ASB NS-2.",Disable public network access for the Kusto cluster and enable private endpoint connectivity to minimize exposure.,N/A,AI,Generic
CRITICAL,NS-3,LacpBillingExhaust.Template.json,27,"Microsoft.Kusto/clusters resource at line 27 does not implement any Network Security Groups (NSGs) to control inbound or outbound traffic, violating ASB NS-3.",Associate the Kusto cluster with a subnet that has an NSG configured to restrict traffic to only required sources and destinations.,N/A,AI,Generic
CRITICAL,DP-1,LacpBillingExhaust.Template.json,27,Microsoft.Kusto/clusters resource at line 27 does not explicitly enable encryption at rest. ASB DP-1 requires all data storage to be encrypted at rest.,"Enable encryption at rest for the Kusto cluster by specifying the appropriate encryption properties, such as customer-managed keys (CMK) if required.",N/A,AI,Generic
CRITICAL,DP-2,LacpBillingExhaust.Template.json,27,Microsoft.Kusto/clusters resource at line 27 does not explicitly enforce encryption in transit (TLS 1.2+). ASB DP-2 requires all data transfers to use strong encryption.,Configure the Kusto cluster to require TLS 1.2 or higher for all data in transit by setting the minimum TLS version property.,N/A,AI,Generic
CRITICAL,DP-3,LacpBillingExhaust.Template.json,27,Microsoft.Kusto/clusters resource at line 27 does not reference Azure Key Vault for storing sensitive information such as keys. ASB DP-3 requires sensitive data to be stored in Azure Key Vault.,Configure the Kusto cluster to use Azure Key Vault for key management and sensitive information storage.,N/A,AI,Generic
CRITICAL,DP-1,LacpBillingExhaustExport.Template.json,61,The resource 'Microsoft.UsageBilling/accounts/dataExports' at line 61 does not specify encryption at rest for exported data. Absence of explicit encryption settings violates ASB DP-1.,Configure the data export resource to enable encryption at rest. Specify encryption properties or ensure the target storage supports and enforces encryption at rest.,N/A,AI,Generic
CRITICAL,DP-2,LacpBillingExhaustExport.Template.json,61,The resource 'Microsoft.UsageBilling/accounts/dataExports' at line 61 does not specify encryption in transit (TLS 1.2+) for data transfers. No explicit secure transfer or protocol enforcement is present.,Ensure all data transfers use TLS 1.2 or higher. Add configuration to enforce secure transfer for all endpoints and data export operations.,N/A,AI,Generic
CRITICAL,DP-3,LacpBillingExhaustExport.Template.json,61,"Sensitive connection information (e.g., 'adxExhaustUri', 'adxExhaustDataIngestionUri') is passed as plain parameters at line 61 without Key Vault integration, violating ASB DP-3.",Store sensitive connection URIs and credentials in Azure Key Vault and reference them securely in the template using Key Vault references.,N/A,AI,Generic
CRITICAL,NS-1,LacpGeo.Template.json,74,"Key Vault resource 'Microsoft.KeyVault/vaults' at line 74 does not restrict network access using network security groups (NSGs) or Azure Firewall. No network rules are defined, which may expose the Key Vault to public access.","Configure the Key Vault with network ACLs to restrict access to trusted networks only. Add 'networkAcls' property with appropriate 'virtualNetworkRules' and 'ipRules', and set 'defaultAction' to 'Deny'.",N/A,AI,Generic
CRITICAL,NS-2,LacpGeo.Template.json,153,"Cosmos DB account 'Microsoft.DocumentDB/databaseAccounts' at line 153 has 'publicNetworkAccess' set to 'Enabled', exposing the database account to the public internet.",Set 'publicNetworkAccess' to 'Disabled' in the Cosmos DB account properties to prevent public exposure.,N/A,AI,Generic
CRITICAL,NS-1,LacpGeo.Template.json,153,Cosmos DB account 'Microsoft.DocumentDB/databaseAccounts' at line 153 does not restrict network access using NSGs or Azure Firewall. 'isVirtualNetworkFilterEnabled' is set to false and 'virtualNetworkRules' is empty.,"Enable 'isVirtualNetworkFilterEnabled' and define 'virtualNetworkRules' to restrict access to trusted subnets. Optionally, use Azure Firewall to further control access.",N/A,AI,Generic
CRITICAL,NS-1,LacpGlobal.Template.json,54,"CosmosDB account 'publicNetworkAccess' is set to 'Enabled' and 'isVirtualNetworkFilterEnabled' is false, exposing the resource to the public internet without network security groups or Azure Firewall.",Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to true. Restrict access using network security groups or Azure Firewall.,N/A,AI,Generic
CRITICAL,NS-2,LacpGlobal.Template.json,54,"CosmosDB account allows public network access ('publicNetworkAccess': 'Enabled'), exposing a public endpoint.",Set 'publicNetworkAccess' to 'Disabled' to prevent public endpoint exposure.,N/A,AI,Generic
CRITICAL,NS-3,LacpGlobal.Template.json,54,"CosmosDB account does not use network security groups (NSGs) or virtual network rules ('isVirtualNetworkFilterEnabled': false, 'virtualNetworkRules': []).",Enable 'isVirtualNetworkFilterEnabled' and define 'virtualNetworkRules' to restrict access via NSGs.,N/A,AI,Generic
CRITICAL,NS-1,LacpGlobal.Template.json,151,"Storage account does not specify network security groups or firewall rules, potentially exposing it to the public internet.",Restrict access to the storage account using network security groups or Azure Firewall. Configure 'networkAcls' to allow only trusted subnets or IPs.,N/A,AI,Generic
CRITICAL,NS-2,LacpGlobal.Template.json,151,Storage account does not restrict public network access; no 'networkAcls' or firewall rules are defined.,Configure 'networkAcls' to deny public network access and allow only trusted networks.,N/A,AI,Generic
CRITICAL,NS-3,LacpGlobal.Template.json,151,Storage account does not use network security groups (NSGs) or service endpoints to control access.,Implement NSGs or service endpoints to restrict access to the storage account.,N/A,AI,Generic
CRITICAL,NS-1,LacpGlobal.Template.json,180,"Key Vault does not specify network security groups, firewall rules, or private endpoint configuration, potentially exposing it to the public internet.","Restrict access to the Key Vault using network security groups, firewall rules, or private endpoints.",N/A,AI,Generic
CRITICAL,NS-2,LacpGlobal.Template.json,180,Key Vault does not restrict public network access; no 'networkAcls' or firewall rules are defined.,Configure 'networkAcls' to deny public network access and allow only trusted networks.,N/A,AI,Generic
CRITICAL,NS-3,LacpGlobal.Template.json,180,Key Vault does not use network security groups (NSGs) or service endpoints to control access.,Implement NSGs or service endpoints to restrict access to the Key Vault.,N/A,AI,Generic
CRITICAL,NS-1,LacpRegion.Template.json,1107,"CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1107 has 'publicNetworkAccess' set to 'Enabled' and 'isVirtualNetworkFilterEnabled' set to false, exposing the database to the public internet without network security controls. This violates NS-1: Protect resources using network security groups or Azure Firewall.",Set 'publicNetworkAccess' to 'Disabled' and 'isVirtualNetworkFilterEnabled' to true. Add appropriate 'virtualNetworkRules' to restrict access to trusted networks only.,N/A,AI,Generic
CRITICAL,NS-2,LacpRegion.Template.json,1107,CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1107 exposes a public endpoint by setting 'publicNetworkAccess' to 'Enabled' and not enabling virtual network filtering. This violates NS-2: Protect public endpoints.,Disable public network access by setting 'publicNetworkAccess' to 'Disabled' and enable virtual network filtering. Use private endpoints for secure access.,N/A,AI,Generic
CRITICAL,NS-21,LacpRegion.Template.json,1107,CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1107 does not implement Network Security Groups (NSGs) or equivalent network controls. 'isVirtualNetworkFilterEnabled' is false and 'virtualNetworkRules' is empty. This violates NS-3: Use Network Security Groups (NSGs).,Enable 'isVirtualNetworkFilterEnabled' and define 'virtualNetworkRules' to restrict access to the CosmosDB account from only trusted subnets.,N/A,AI,Generic
CRITICAL,DP-3,LacpStamp.Parameters-LacpStampResources.json,74,The parameter 'dasStorageAccountKey' at line 74 appears to reference a storage account key directly in the template. Storing sensitive information such as storage account keys in parameters violates data protection best practices. Sensitive data should be stored in Azure Key Vault.,Store the storage account key in Azure Key Vault and reference it securely from the template using a Key Vault reference.,N/A,AI,Generic
CRITICAL,NS-1,LacpStamp.Template.json,393,"Microsoft.Storage/storageAccounts resource at line 393 does not specify network security controls such as networkAcls, NSG, or firewall rules. Storage accounts should be protected using NSGs or Azure Firewall to restrict access.",Add networkAcls property to the storage account resource to restrict access to trusted networks and subnets. Consider integrating with Azure Firewall or NSG for additional protection as per ASB NS-1.,N/A,AI,Generic
CRITICAL,NS-1,LacpStamp.Template.json,429,"Microsoft.Storage/storageAccounts resource at line 429 does not specify network security controls such as networkAcls, NSG, or firewall rules. Storage accounts should be protected using NSGs or Azure Firewall to restrict access.",Add networkAcls property to the storage account resource to restrict access to trusted networks and subnets. Consider integrating with Azure Firewall or NSG for additional protection as per ASB NS-1.,N/A,AI,Generic
CRITICAL,NS-1,LacpStamp.Template.json,465,"Microsoft.Storage/storageAccounts resource at line 465 does not specify network security controls such as networkAcls, NSG, or firewall rules. Storage accounts should be protected using NSGs or Azure Firewall to restrict access.",Add networkAcls property to the storage account resource to restrict access to trusted networks and subnets. Consider integrating with Azure Firewall or NSG for additional protection as per ASB NS-1.,N/A,AI,Generic
CRITICAL,NS-20,LacpStamp.Template.json,393,Microsoft.Storage/storageAccounts resource at line 393 does not implement private endpoints or restrict public network access. Public endpoints are exposed by default.,Set networkAcls.defaultAction to 'Deny' and configure private endpoints for the storage account to eliminate public exposure as per ASB NS-20.,N/A,AI,Generic
CRITICAL,NS-20,LacpStamp.Template.json,429,Microsoft.Storage/storageAccounts resource at line 429 does not implement private endpoints or restrict public network access. Public endpoints are exposed by default.,Set networkAcls.defaultAction to 'Deny' and configure private endpoints for the storage account to eliminate public exposure as per ASB NS-20.,N/A,AI,Generic
CRITICAL,NS-20,LacpStamp.Template.json,465,Microsoft.Storage/storageAccounts resource at line 465 does not implement private endpoints or restrict public network access. Public endpoints are exposed by default.,Set networkAcls.defaultAction to 'Deny' and configure private endpoints for the storage account to eliminate public exposure as per ASB NS-20.,N/A,AI,Generic
CRITICAL,NS-1,ReadAdxExhaust.Template.json,23,Microsoft.Kusto/clusters resource does not specify any network security groups (NSGs) or Azure Firewall configuration. This exposes the resource to potential unauthorized network access.,Configure the Kusto cluster to use private endpoints or associate it with a virtual network protected by NSGs or Azure Firewall. Update the resource definition to include network isolation settings.,N/A,AI,Generic
CRITICAL,NS-2,ReadAdxExhaust.Template.json,23,"Microsoft.Kusto/clusters resource is deployed without explicit restriction of public endpoints. Public endpoints may be enabled by default, increasing exposure risk.","Explicitly disable public network access by setting 'publicNetworkAccess' property to 'Disabled' in the cluster properties, or configure only private endpoints.",N/A,AI,Generic
CRITICAL,DP-1,ReadUsageAccount.Template.json,17,The Microsoft.UsageBilling/accounts resource does not specify encryption at rest settings. ASB DP-1 requires all data storage to be encrypted at rest.,"Enable encryption at rest for the Microsoft.UsageBilling/accounts resource by specifying encryption settings in the properties section, or ensure the resource type enforces encryption by default.",N/A,AI,Generic
CRITICAL,DP-2,ReadUsageAccount.Template.json,17,The Microsoft.UsageBilling/accounts resource does not specify encryption in transit (TLS 1.2+) settings. ASB DP-2 requires all data transfers to use TLS 1.2 or higher.,"Configure the Microsoft.UsageBilling/accounts resource to enforce TLS 1.2 or higher for all data in transit, or verify that the resource type enforces this by default.",N/A,AI,Generic
CRITICAL,DP-3,ReadUsageAccount.Template.json,17,No configuration found for storing sensitive information such as keys or secrets in Azure Key Vault for the Microsoft.UsageBilling/accounts resource. ASB DP-3 requires sensitive data to be stored securely.,"Store any sensitive information, such as access keys or secrets, in Azure Key Vault and reference them securely in the template.",N/A,AI,Generic
CRITICAL,NS-10,TrafficManagerEndpoints.Template.json,38,"The resource 'Microsoft.Network/trafficManagerProfiles/externalEndpoints' at line 38 defines a public endpoint without explicit security controls to minimize exposure, violating ASB Control NS-10 (Protect public endpoints).","Restrict access to the public endpoint by implementing IP whitelisting, authentication, or moving the endpoint behind a secure gateway. Review the endpoint configuration to ensure only authorized traffic is allowed.",N/A,AI,Generic
HIGH,NS-21,LacpGeo.Template.json,153,Cosmos DB account 'Microsoft.DocumentDB/databaseAccounts' at line 153 does not use private endpoints. No 'privateEndpointConnections' or related configuration is present.,Add a 'privateEndpointConnections' property to the Cosmos DB account and configure a private endpoint to restrict access to internal networks only.,N/A,AI,Generic
HIGH,NS-5,LacpGlobal.Template.json,54,CosmosDB account does not use private endpoints; 'publicNetworkAccess' is enabled and no private endpoint configuration is present.,Implement a private endpoint for the CosmosDB account and disable public network access.,N/A,AI,Generic
HIGH,NS-5,LacpGlobal.Template.json,151,Storage account does not use private endpoints; no private endpoint configuration is present.,Implement a private endpoint for the storage account to restrict access to private networks.,N/A,AI,Generic
HIGH,NS-5,LacpGlobal.Template.json,180,Key Vault does not use private endpoints; no private endpoint configuration is present.,Implement a private endpoint for the Key Vault to restrict access to private networks.,N/A,AI,Generic
HIGH,NS-22,LacpRegion.Template.json,1107,CosmosDB account 'Microsoft.DocumentDB/databaseAccounts' at line 1107 does not use private endpoints. 'publicNetworkAccess' is enabled and no private endpoint configuration is present. This violates NS-5: Use Private Endpoints.,"Configure a private endpoint for the CosmosDB account and disable public network access to ensure secure, private connectivity.",N/A,AI,Generic
HIGH,NS-23,LacpStamp.Template.json,393,Microsoft.Storage/storageAccounts resource at line 393 does not use private endpoints. Access to the storage account is not restricted to private networks.,Configure a private endpoint for the storage account to restrict access to resources within a virtual network as per ASB NS-23.,N/A,AI,Generic
HIGH,NS-23,LacpStamp.Template.json,429,Microsoft.Storage/storageAccounts resource at line 429 does not use private endpoints. Access to the storage account is not restricted to private networks.,Configure a private endpoint for the storage account to restrict access to resources within a virtual network as per ASB NS-23.,N/A,AI,Generic
HIGH,NS-23,LacpStamp.Template.json,465,Microsoft.Storage/storageAccounts resource at line 465 does not use private endpoints. Access to the storage account is not restricted to private networks.,Configure a private endpoint for the storage account to restrict access to resources within a virtual network as per ASB NS-23.,N/A,AI,Generic
HIGH,AM-1,RoleAssignment.Template.json,49,"Role assignment at line 49 grants 'Contributor' role to the 'Ev2BuildoutServicePrincipalId' principal. The 'Contributor' role provides broad permissions, which may exceed least privilege requirements.","Review the permissions required by the 'Ev2BuildoutServicePrincipalId' principal and assign a more restrictive, custom role with only the necessary permissions as per ASB AM-1.",N/A,AI,Generic
