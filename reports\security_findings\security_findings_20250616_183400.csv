Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
CRITICAL,DP-3,function.bicep,111,"The Function App writes log data directly to a blob container using a SAS token constructed in-template and embedded in the App Service log configuration. If this template or deployment history is exposed, this could leak tokens with broad permissions, violating best practices for sensitive information disclosure.","Use a Key Vault reference or managed identity for accessing storage instead of embedding SAS tokens in code or configuration. Limit the scope and lifetime of any SAS tokens if used, and prefer Azure AD authentication.",N/A,AI,Generic
CRITICAL,NS-1,keyvault.bicep,18,"The Key Vault resource configures 'networkAcls.defaultAction' as 'Allow', which allows public network access from any source unless specifically restricted via rules. This defeats network-level isolation of sensitive resources such as Key Vault per ASB NS-1.","Set 'networkAcls.defaultAction' to 'Deny' to restrict public network access, allowing only explicitly defined networks, IPs, or subnets to access the Key Vault.",N/A,AI,Generic
CRITICAL,NS-2,keyvault.bicep,18,<PERSON> Vault is exposed to public networks by default due to 'networkAcls.defaultAction' set as 'Allow'. This increases the attack surface and risk of unauthorized access to secrets in the vault.,Change 'networkAcls.defaultAction' to 'Deny' and validate that only required subnets or IPs have been granted access via 'ipRules' and 'virtualNetworkRules'.,N/A,AI,Generic
CRITICAL,NS-2,storage-accounts.bicep,21,"All storage accounts ('storageAccountFunc', 'storageAccountFuzz', and 'storageAccountsCorpus') are configured with 'networkAcls.defaultAction' set to 'Allow', which permits public network access by default. This exposes storage account endpoints and increases the risk of unauthorized access, violating the control to protect public endpoints.","Set 'networkAcls.defaultAction' to 'Deny' for all storage accounts. Allow access only via required IP addresses, virtual networks, or private endpoints. Ensure required access rules are explicitly defined.",N/A,AI,Generic
HIGH,NS-5,app-config.bicep,7,"The App Configuration resource does not implement a private endpoint. Without a private endpoint, the service is accessible via a public endpoint, increasing exposure to potential attacks. This violates ASB NS-5, which recommends using private endpoints for secure access.",Add a Microsoft.Network/privateEndpoints resource to the template and associate it with the App Configuration store. This will limit access to the service to private networks only.,N/A,AI,Generic
HIGH,NS-2,app-config.bicep,7,"The App Configuration store is deployed without restricting public network access or IP firewall rules. Without explicit access restrictions, the endpoint becomes publicly accessible, violating ASB NS-2 requirements to secure all public endpoints.",Restrict public network access by configuring the App Configuration store with 'publicNetworkAccess: 'Disabled'' and implement appropriate firewall rules or use private endpoints.,N/A,AI,Generic
HIGH,DP-3,app-config.bicep,16,"The key-values are provisioned using plain property assignment (`item.value`), which may result in storing sensitive data (such as secrets or connection strings) in-line. ASB DP-3 requires secrets to be stored in Azure Key Vault, not directly in App Configuration or IaC files.","Do not store secrets, passwords, or sensitive connection strings directly in App Configuration or the IaC definition. Instead, store them in Azure Key Vault and reference them securely from your applications.",N/A,AI,Generic
HIGH,NS-5,event-grid.bicep,1,Private Endpoints are not configured for the Storage Account resources used as Event Grid sources (referenced by 'storageFuzzId' and 'storageCorpusIds'). This risks exposing Storage resources to the public internet.,"Configure Private Endpoints for all Storage Accounts used in blob event subscriptions to ensure that access is restricted to private networks only, minimizing attack surface.",N/A,AI,Generic
HIGH,NS-1,function-settings.bicep,48,"The configuration does not specify any network security groups (NSGs) or Azure Firewall protecting the referenced storage account 'funcStorage'. Protecting storage accounts with NSGs, firewall rules, or restricting access is required to minimize exposure.",Update the storage account configuration to implement network rules so that only authorized networks/subnets can access the storage account. Use Azure Firewall rules or associate the storage account with NSGs or configure network rules to deny public network access.,N/A,AI,Generic
HIGH,NS-6,function-settings.bicep,64,"No restrictions or access configurations are defined to protect the public endpoint of the Azure App Service (function app). By default, app services are publicly accessible.","Implement access restrictions on the Azure App Service, such as applying IP Allow/Deny rules using 'ipSecurityRestrictions', or using Azure Front Door, or restricting access via VNet integration. Enable authentication and require secure access to the app.",N/A,AI,Generic
HIGH,DP-3,function-settings.bicep,65,The App Insights instrumentation key ('app_insights_key') is set as an app setting and not configured for retrieval from a secure location such as Azure Key Vault. Sensitive data should not be stored in environment variables or app settings.,"Store the Application Insights instrumentation key in Azure Key Vault and use Key Vault references in your App Service configuration, or configure Application Insights with managed identity where possible to avoid explicit key exposure.",N/A,AI,Generic
HIGH,NS-1,function.bicep,62,"The storage account resource 'funcStorage' is referenced as 'existing', with no indication in the template that it is protected by Network Security Groups (NSGs) or Azure Firewall. Storage accounts should be protected from unauthorized network access.",Ensure the storage account referenced by 'logs_storage' is protected by appropriate NSG rules or is only accessible from trusted subnets or private endpoints. Consider documenting or verifying security controls on referenced resources.,N/A,AI,Generic
HIGH,NS-2,function.bicep,94,"The Azure Function App exposes an HTTP endpoint and the site config does not restrict inbound access. Without explicit IP restrictions or integration with Private Endpoints, the app may be accessible from the public internet.",Implement access restrictions for the Function App by defining allowed IP address ranges or integrating with Azure Private Endpoints. Review App Service Access Restrictions settings to minimize public exposure.,N/A,AI,Generic
HIGH,NS-5,function.bicep,99,"The Function App is integrated with a virtual network subnet ('virtualNetworkSubnetId'), but there is no use of Private Endpoints for accessing the storage account logs, leaving the possibility of exposure over public network paths.","Implement Private Endpoints for the storage account to enable secure, private access from the Function App. Update the logs configuration to use the storage account's private endpoint DNS.",N/A,AI,Generic
HIGH,NS-1,hub-network.bicep,2,"The virtual network and subnet defined in this template do not reference or associate any Network Security Group (NSG). This exposes all resources in the 'hub-subnet' to unrestricted network access, violating controls to protect resources with NSGs or Azure Firewall.",Define and associate a Network Security Group (NSG) with the 'hub-subnet' to restrict and control inbound and outbound traffic according to the principle of least privilege.,N/A,AI,Generic
HIGH,NS-3,hub-network.bicep,2,"The template lacks implementation of Network Security Groups (NSGs) to restrict network traffic, leaving the subnet unprotected from unwanted and potentially malicious traffic.",Create an NSG resource and associate it with 'hub-subnet' to enforce explicit allow/deny rules for inbound and outbound traffic at the subnet level.,N/A,AI,Generic
HIGH,NS-1,instance-config.bicep,32,"The network configuration for this Compute deployment defines an 'address_space' and 'subnet', but the template does not include any definition or reference to Network Security Groups (NSGs) or Azure Firewall to protect the VM resources. According to ASB NS-1, critical resources such as Compute instances must be protected with NSG or firewall rules to reduce attack surface.",Explicitly define and associate Network Security Groups with the relevant subnets or network interfaces for all VM resources. Ensure that only required inbound and outbound traffic is permitted by the NSG rules. Consider using Azure Firewall for additional control if appropriate.,N/A,AI,Generic
HIGH,NS-3,instance-config.bicep,32,"There are no Network Security Groups (NSG) implemented or referenced for the defined subnet ('10.0.0.0/16'), which is required by ASB NS-3 to control inbound and outbound VM traffic. The absence of NSGs can leave Compute resources exposed to unwanted traffic.","Add NSG resource definitions to this template or its dependencies, and associate NSGs with the specified subnet and/or VM network interfaces. Configure rules to allow only required management and application traffic, and deny all others.",N/A,AI,Generic
HIGH,DP-4,instance-config.bicep,37,"The template does not specify any managed disk encryption configuration for the default VM images or attached storage, violating ASB DP-4. Encryption at rest for managed disks is not guaranteed by omission.","Ensure VM resource definitions configure encryption for OS and data disks, either by using encrypted managed disks or disk encryption sets integrated with Azure Key Vault.",N/A,AI,Generic
HIGH,DP-1,instance-config.bicep,37,The template does not ensure encryption at rest for data associated with the Compute instances or their disks as required by ASB DP-1. Explicit configuration is needed to ensure all persisted data is encrypted.,"Add explicit configuration to all VM and managed disk resources to enforce encryption at rest, leveraging Azure-managed or customer-managed keys where appropriate.",N/A,AI,Generic
HIGH,NS-2,ip-rules.bicep,3,"The corpNetIps and sawVnetIps lists include large public IP ranges (such as '*******/8' and '********/8'), which, if applied as allow rules for access to Azure resources (e.g., storage accounts or Logic Apps), could result in significant overexposure of public endpoints and defeat network isolation best practices.","Restrict allowed IP address ranges to only those necessary for access, avoiding overly broad network ranges. Use specific subnets, trusted enterprise VPN endpoints, or private networking (Private Endpoints or Service Endpoints) to minimize exposure.",N/A,AI,Generic
HIGH,NS-5,ip-rules.bicep,1,"The template only defines IP allow rules and does not leverage Azure Private Endpoints, which are strongly recommended to securely access resources and to avoid exposure via public IPs.","Wherever possible, use Private Endpoints for Azure resources instead of public IP allow lists. This ensures network traffic remains within the Azure backbone network and is not exposed to the public internet.",N/A,AI,Generic
HIGH,DP-3,keyvault.bicep,37,"Secrets are provisioned into Key Vault via parameter values. If the deployment parameter 'secrets' contains plaintext secrets (in deployment files or parameters), this risks accidental exposure through IaC, logs, or deployment history, violating best practices for managing sensitive information.","Ensure all secret values are passed using secure mechanisms, such as ARM secureString, Bicep @secure(), or Key Vault references, and never check plaintext secrets into source code or parameter files. Regularly audit deployment process for exposure.",N/A,AI,Generic
HIGH,DP-3,operational-insights.bicep,85,The output 'appInsightsInstrumentationKey' exposes the Application Insights Instrumentation Key directly. Instrumentation Keys are sensitive secrets that can allow an attacker to push arbitrary telemetry or exfiltrate data if leaked.,Do not output or expose sensitive secrets such as Instrumentation Keys in outputs or logs. Use Azure Key Vault references to manage and access secrets securely.,N/A,AI,Generic
HIGH,NS-1,scaleset-networks.bicep,1,No Network Security Group (NSG) or Azure Firewall is defined or associated with the subnet 'scaleset' or the virtual network. This leaves resources unprotected against unwanted or malicious network traffic.,"Define and associate an NSG with the 'scaleset' subnet or configure Azure Firewall to restrict and monitor network traffic, only allowing required ports/protocols.",N/A,AI,Generic
HIGH,NS-2,scaleset-networks.bicep,12,"A public IP address is allocated for outbound network traffic via a NAT Gateway, but there are no controls shown restricting which resources or subnets can access the public endpoint. Public exposure is not minimized.","Apply NSGs or Firewall rules to restrict outbound traffic to necessary destinations, and limit which resources/subnets can access the public IP address. Only expose public endpoints when absolutely required.",N/A,AI,Generic
HIGH,NS-3,scaleset-networks.bicep,32,The subnet 'scaleset' has no Network Security Group (NSG) associated to control inbound/outbound traffic. Absence of NSGs allows unrestricted communication to and from the subnet.,"Associate a Network Security Group with the 'scaleset' subnet, defining explicit rules for allowed and denied traffic, following least privilege.",N/A,AI,Generic
HIGH,NS-2,server-farms.bicep,60,"The App Service Plan resource 'Microsoft.Web/serverfarms' does not specify access restrictions, private endpoints, or networking rules, which may result in the App Service exposing public endpoints by default. This violates the principle of minimizing public exposure as per ASB NS-2.",Configure access restrictions for App Service or use App Service Environments (ASE) for private deployments. Implement IP Restrictions to allow only trusted sources and restrict public exposure.,N/A,AI,Generic
HIGH,NS-1,server-farms.bicep,60,"There are no Network Security Groups (NSGs), firewalls, or explicit network protections implemented on resources such as the App Service or Key Vault reference. Sensitive resources are not adequately protected as per ASB NS-1.","Deploy NSGs or Azure Firewall to protect the subnet/app service environment where resources reside. For App Service, use VNet integration and restrict networks for Key Vault where possible.",N/A,AI,Generic
HIGH,NS-2,signalR.bicep,4,"The SignalR Service resource is created with default networking, which exposes a public endpoint. There is no restriction or access control to limit exposure. This may allow unintended public access to the service, violating the control's guidance to secure all public endpoints.","Configure the SignalR resource with 'publicNetworkAccess: 'Disabled'' property, or use private endpoints and IP firewall restrictions to limit inbound access to trusted sources only.",N/A,AI,Generic
HIGH,NS-1,storage-accounts.bicep,23,"'networkAcls.bypass' is set to 'AzureServices, Logging, Metrics', which allows certain Azure service traffic to bypass network restrictions to the storage accounts. While sometimes necessary, this can unintentionally widen access to critical resources.","Re-evaluate which services are required to bypass network rules. Limit 'bypass' values to only necessary services and document business justifications. Ideally, use private endpoints for trusted services.",N/A,AI,Generic
HIGH,IM-1,storage-accounts.bicep,15,"Storage accounts have 'allowSharedKeyAccess' set to false (good), but there is no indication that Azure Active Directory (Azure AD)-based authentication (e.g., Azure AD integration for Blob/Queue) is enforced for resource access. Relying solely on key-based access without enabling Azure AD can weaken identity management.",Enable Azure AD authentication (via 'azureActiveDirectoryProperties') for Blob and Queue services on all storage accounts to ensure strong identity controls and centralized access management.,N/A,AI,Generic
MEDIUM,IM-6,app-config.bicep,7,The template does not define any RBAC assignments or restrict access using defined roles. Absence of RBAC assignments can lead to excessive permissions in violation of ASB IM-6.,Add 'Microsoft.Authorization/roleAssignments' resources to assign granular access to the App Configuration store based on least privilege principles.,N/A,AI,Generic
MEDIUM,IM-8,app-config.bicep,7,There is no explicit assignment or usage of managed identities for resource-to-resource authentication in the template. ASB IM-8 recommends using managed identities for secure authentication between resources.,"Enable a managed identity on dependent resources (such as apps consuming this App Configuration) and grant appropriate permissions. While not directly configured here, add guidance or examples for secure consumption.",N/A,AI,Generic
MEDIUM,DP-1,app-config.bicep,7,"The template does not specify encryption settings for data at rest for the App Configuration store. While encryption at rest is provided by default, the lack of customer-managed key (CMK) settings may violate strict interpretations of ASB DP-1.","If regulatory requirements mandate, configure the App Configuration store with customer-managed keys (CMK) for encryption at rest.",N/A,AI,Generic
MEDIUM,NS-1,autoscale-settings.bicep,1,"No network security controls (such as Network Security Groups or Azure Firewall) are defined or referenced to protect the resources (e.g., the referenced Storage Account via func_storage_account_id or the App Service Plan via server_farm_id). This omission may expose sensitive backend services to unrestricted network access.","Ensure that the references for func_storage_account_id and server_farm_id point to resources protected by Network Security Groups (for supported types) or are otherwise restricted (using firewall rules, service endpoints, or private endpoints as appropriate). Explicitly deploy or reference NSGs or other network controls in templates that provision infrastructure involving sensitive resources such as Storage Accounts.",N/A,AI,Generic
MEDIUM,DP-3,autoscale-settings.bicep,1,"The template allows for sensitive parameters (such as workspaceId, func_storage_account_id, and server_farm_id) to be provided directly, but does not enforce use of secure parameter types, Key Vault references, or provide guidance on protecting secrets. This can lead to accidental disclosure if parameter values are hard-coded or improperly managed.","Use parameter metadata to indicate which parameters are sensitive and should be provided via secure mechanism (e.g., Key Vault references). Avoid hard-coding identifiers that could potentially reveal sensitive resource details or access patterns. Add guidance or enforce secure parameter passing in deployment pipelines.",N/A,AI,Generic
MEDIUM,NS-6,event-grid.bicep,1,"Virtual Network Service Endpoints are not specified for the Storage Accounts being subscribed to in Event Grid System Topics. Without service endpoints, access might be permitted from outside trusted VNets.",Enable and restrict Storage Account access to selected Virtual Networks by configuring Service Endpoints. This reduces exposure to untrusted networks.,N/A,AI,Generic
MEDIUM,DP-1,function-settings.bicep,48,"The storage account resource block does not explicitly specify encryption settings. Although encryption at rest is enabled by default for most Azure storage resources, not specifying this explicitly risks accidental misconfiguration.","Explicitly define encryption settings for the storage account resource with 'encryption' properties, specifying the use of Microsoft-managed keys or customer-managed keys as appropriate.",N/A,AI,Generic
MEDIUM,NS-3,function.bicep,62,There is no deployment or linkage of a Network Security Group (NSG) applied to the subnet referenced in 'hubSubnetId' for the Function App integration. NSGs are recommended to control traffic to subnets used by critical services.,Deploy and associate a suitable NSG to the subnet referenced by 'hubSubnetId'. Ensure ingress and egress rules restrict access to only required sources and destinations.,N/A,AI,Generic
MEDIUM,DP-2,function.bicep,62,There is no configuration shown to enforce encryption in transit (TLS 1.2+) for the storage account used for application logs. Data in transit to blob endpoints should be encrypted with strong TLS.,"Ensure the storage account enforces 'minimumTlsVersion' to TLS1_2 or higher. If possible, restrict blob access to HTTPS only and prohibit HTTP access in the resource configuration.",N/A,AI,Generic
MEDIUM,IM-6,function.bicep,87,"There are no explicit RBAC role definitions or assignments in this template for App Service, Storage, or Managed Identity resources. Without RBAC, access rights may be over-provisioned or unclear.","Define and deploy explicit Role Assignments granting only necessary roles (e.g., 'Storage Blob Data Contributor') to identities such as the Function App's Managed Identity for the resources they access.",N/A,AI,Generic
MEDIUM,NS-4,hub-network.bicep,2,No Azure Firewall or third-party firewall resource is defined or referenced for inspecting or securing traffic into or out of the hub virtual network.,Deploy an Azure Firewall or a third-party firewall appliance in the hub network to inspect and control egress and ingress traffic as appropriate for your network architecture.,N/A,AI,Generic
MEDIUM,NS-5,hub-network.bicep,17,"The subnet is configured with service endpoints for 'Microsoft.Storage.Global' and 'Microsoft.KeyVault', but there is no evidence of private endpoints for accessing these services, which would provide enhanced security over service endpoints.","Where possible, use Azure Private Endpoints to connect securely to Storage and Key Vault, ensuring traffic remains on the Microsoft backbone and cannot be accessed from public networks.",N/A,AI,Generic
MEDIUM,NS-2,instance-config.bicep,32,"The template does not specify network restrictions or private endpoints, nor does it ensure that public endpoints are protected for Compute instances. According to ASB NS-2, public exposure must be minimized and endpoints restricted. Without explicit configuration, there may be risk of accidental exposure.","Explicitly configure VM and subnet settings to avoid public IP assignment unless strictly required. For any services requiring internet access, use Azure Private Link or Application Gateway with WAF, and ensure that NSG rules restrict public ingress.",N/A,AI,Generic
MEDIUM,NS-7,instance-config.bicep,1,The template does not define resources or policies for Just-in-Time (JIT) VM Access. JIT access reduces exposure of management (RDP/SSH) ports as required by ASB NS-7.,Implement JIT VM Access for all Compute resources using Azure Security Center or specify the enabling of JIT in the resource manager template. Restrict management port exposure to as-needed basis only.,N/A,AI,Generic
MEDIUM,NS-10,instance-config.bicep,1,"There is no mention of using Azure Bastion or jump host configuration for secure management of Compute VMs, as recommended in ASB NS-10. Direct SSH/RDP access may be inadvertently permitted.",Integrate Azure Bastion or designate a jump host for secure administrative access to VMs. Avoid exposing management ports directly to the internet.,N/A,AI,Generic
MEDIUM,NS-6,ip-rules.bicep,1,"The current approach does not include use of Virtual Network Service Endpoints, which further restricts traffic to Azure services to only come from permitted VNets/subnets. Relying solely on IP allow lists is less secure and more difficult to manage than VNet-based restrictions.","Implement VNet Service Endpoints for supported services to restrict access to trusted virtual networks/subnets, in addition to or instead of IP-based rules.",N/A,AI,Generic
MEDIUM,NS-1,ip-rules.bicep,1,There is no evidence of the use of Network Security Groups (NSGs) or Azure Firewall in the template to protect network-accessible resources. Solely relying on IP allow lists (such as for Storage Accounts or Logic Apps) may leave gaps in network security.,"Ensure that NSGs and/or Azure Firewall rules are configured to provide robust, layered control of network traffic to and from sensitive resources. Use them in conjunction with resource-level access controls.",N/A,AI,Generic
MEDIUM,IM-6,keyvault.bicep,22,"The Key Vault enables RBAC authorization by setting 'enableRbacAuthorization' to true, but there are no role assignments included in the template. This may result in no access being granted or requiring out-of-band access configuration.","Add explicit role assignments for required users, groups, or applications to ensure least privilege access is configured using RBAC, instead of relying on default or external access configuration.",N/A,AI,Generic
MEDIUM,DP-6,keyvault.bicep,23,"The Key Vault is deployed with the default 'Standard' SKU, which does not support dedicated HSM or Customer Managed Keys for encrypting keys/secrets at rest. This may not meet compliance for sensitive data that requires customer-managed encryption.","Consider using the 'Premium' SKU for dedicated HSM support and/or configure Key Vault to use CMK for encryption if regulatory, compliance, or data protection needs require it.",N/A,AI,Generic
MEDIUM,DP-3,operational-insights.bicep,84,"The output 'appInsightsAppId' exposes the Application Insights AppId, which can be useful for attackers in reconnaissance, although less critical than Instrumentation Key.",Avoid outputting internal resource identifiers if not necessary. Restrict output to information strictly required by downstream processes.,N/A,AI,Generic
MEDIUM,DP-1,operational-insights.bicep,35,The resource 'Microsoft.OperationalInsights/workspaces' does not specify the use of customer-managed or service-managed encryption keys. The template does not enforce encryption at rest configuration for the workspace.,Explicitly set 'encryption' properties within the workspace resource and consider using customer-managed keys (CMK) for enhanced data protection.,N/A,AI,Generic
MEDIUM,DP-6,operational-insights.bicep,35,"No configuration for customer-managed keys (CMK) for encryption is present for the Log Analytics workspace, which could be required for handling highly sensitive data.",Add and configure the 'encryption' block under the workspace resource to leverage customer-managed keys from Azure Key Vault.,N/A,AI,Generic
MEDIUM,NS-1,operational-insights.bicep,35,"The Log Analytics workspace is not protected by any network security group (NSG), firewall, or private endpoint configuration. This may expose logging data to unauthorized access.",Restrict access to the workspace using private endpoints and/or NSGs. Consider workspace networking features to allow access only from trusted VNETs.,N/A,AI,Generic
MEDIUM,NS-2,operational-insights.bicep,35,"No configuration to restrict public network access for the Log Analytics workspace. By default, the workspace may be publicly accessible over the internet.",Set the 'publicNetworkAccessForIngestion' and 'publicNetworkAccessForQuery' properties to 'Disabled' and configure private endpoints for secure access.,N/A,AI,Generic
MEDIUM,NS-4,scaleset-networks.bicep,1,There is no reference to Azure Firewall or a third-party firewall appliance to provide centralized network traffic filtering/protection between segments or from/to the internet.,"Include and configure Azure Firewall or a supported third-party firewall to inspect and protect traffic between subnets, to/from the internet, and enforce security policies.",N/A,AI,Generic
MEDIUM,DP-3,server-farms.bicep,111,"The property 'CERTIFICATE_PASSWORD_GENEVACERT' for the AntMDS component is created with an empty string. While sensitive values for certificates are referenced from Key Vault, setting a password field (even empty) in configuration may risk later misconfigurations or accidental exposure.","Do not provision empty or placeholder sensitive settings. If certificate passwords are needed, always retrieve them securely from Azure Key Vault, not as inline configuration values.",N/A,AI,Generic
MEDIUM,IM-8,server-farms.bicep,103,The Key Vault integration for Geneva certificates references the vault and secret but does not specify use of a managed identity for accessing Key Vault. Secure resource-to-resource authentication should use managed identity.,"Enable and assign a system-assigned or user-assigned managed identity to the App Service Plan, and configure Key Vault access policies/rbac to grant appropriate secret/certificate access via that managed identity.",N/A,AI,Generic
MEDIUM,NS-5,server-farms.bicep,103,Key Vault is referenced using its full resource ID but there is no evidence that private endpoint connectivity is used. Exposing Key Vault via public endpoints increases risk of data exposure.,"Configure Azure Key Vault to use private endpoints, and restrict public network access. Update reference patterns and access methods to align with private networking.",N/A,AI,Generic
MEDIUM,NS-3,storage-accounts.bicep,21,No Network Security Groups (NSGs) are defined or enforced for the subnet (referenced via 'hubSubnetId') associated with storage account virtual network rules. NSGs are required to restrict traffic at the subnet/network level in addition to storage account ACLs.,Define and associate NSGs to the referenced subnet(s) to explicitly control allowed inbound and outbound traffic. This ensures a defense-in-depth network security posture.,N/A,AI,Generic
LOW,DP-1,function.bicep,62,"The template does not show explicit enforcement of encryption at rest on the referenced storage account. While storage accounts are encrypted by default, explicit configuration is recommended to align with policy and future-proof templates.","Set the 'enableHttpsTrafficOnly' and 'encryption' properties explicitly on the storage account in its defining template. If not owned by this template, confirm those settings are present in the storage account's deployment.",N/A,AI,Generic
LOW,NS-9,ip-rules.bicep,1,The template does not define logging or monitoring settings for the network rules or Logic Apps. Lack of network traffic monitoring can reduce visibility into potential threats or misconfigurations.,"Enable diagnostics and logging (such as Azure Monitor, Network Watcher, and resource-level diagnostic settings) for all resources to monitor network access and rule effectiveness.",N/A,AI,Generic
LOW,NS-5,scaleset-networks.bicep,40,Private Endpoints are not implemented for the subnet or any referenced resource. Absence of private endpoints can lead to unintentional public or wide network exposure for PaaS resources.,"Implement Azure Private Endpoints for services like Azure Storage, SQL, or Key Vault that are accessed from this subnet, to ensure network traffic remains on the Microsoft backbone.",N/A,AI,Generic
LOW,NS-6,scaleset-networks.bicep,40,"No Network Service Endpoints are enabled in the subnet configuration, which could help restrict access to Azure PaaS services (e.g., Storage, SQL) to only this subnet.","Enable required service endpoints (e.g., for Microsoft.Storage, Microsoft.Sql, etc.) on the subnet for Azure PaaS resources, and enforce appropriate access policies.",N/A,AI,Generic
