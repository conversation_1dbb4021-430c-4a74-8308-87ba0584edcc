File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
app-config.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyValues' flows across template boundary,"Secure parameter 'keyValues' and validate its usage across template boundaries

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",,Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'app_insights_key' flows across template boundary,"Secure parameter 'app_insights_key' and validate its usage across template boundaries

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",,Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyvault_name' flows across template boundary,"Secure parameter 'keyvault_name' and validate its usage across template boundaries

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",,Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
keyvault.bicep,NS-1,Network Security,Protect resources using network security groups,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,"Review template dependencies and ensure secure communication between templates

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'secrets' flows across template boundary,"Secure parameter 'secrets' and validate its usage across template boundaries

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",,Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,privilege_escalation,Validated
storage-accounts.bicep,NS-1,Network Security,Protect resources using network security groups,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,"Review template dependencies and ensure secure communication between templates

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,DP-6,Data Protection,Secure Data with Customer-Managed Keys (CMK),HIGH,1.0,Parameter flow security risk: Sensitive parameters may be exposed through template dependencies,"Use Key Vault references for sensitive parameters and validate parameter flow security

🔵 Azure Guidance: Use customer-managed keys for critical data.",,Use customer-managed keys for critical data.,"Configure Key Vault for key management. Rotate keys regularly.

This control is implemented through 3 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",cross_reference_analysis,parameter_flow,Validated
app-config.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,7.0,"The App Configuration resource 'Microsoft.AppConfiguration/configurationStores' at line 7 does not restrict public network access. By default, App Configuration endpoints are public unless network rules or Private Link are explicitly configured.","Restrict public access to the App Configuration resource by configuring private endpoints or setting network ACLs to allow only required IPs. Add the 'publicNetworkAccess' property set to 'Disabled' and configure 'networkAcls' to restrict access.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
event-grid.bicep,IM-8,Identity Management,Use Managed Identities for Azure Resources,HIGH,32.0,"The event subscription destination uses a resourceId parameter (storageFuncId) for StorageQueue endpoint, but there is no evidence of managed identities being configured for secure access. Managed identities should be used to avoid storing credentials in code or parameters.","Configure a managed identity for the function or resource that interacts with the StorageQueue. Update the template to assign and reference a managed identity for secure access to the storage resource, and ensure the StorageQueue access policy allows this identity.

🔵 Azure Guidance: Enable system/user-assigned managed identities for Azure services.",,Enable system/user-assigned managed identities for Azure services.,"Configure managed identities for VMs apps and Azure Functions. Use for Key Vault access.

This control is implemented through 3 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
function-settings.bicep,DP-3,Data Protection,Manage sensitive information disclosure,CRITICAL,38.0,The parameter 'ado_access_client_id' is assigned a hardcoded client ID value directly in the template. Storing sensitive identifiers or secrets in code violates DP-3: Manage sensitive information disclosure.,"Remove the hardcoded value from the 'ado_access_client_id' parameter and retrieve it securely from Azure Key Vault using a Key Vault reference.

🔵 Azure Guidance: Store secrets in Key Vault. Use access policies.",,Store secrets in Key Vault. Use access policies.,"Never store secrets in code. Use Key Vault references. Enable purge protection.

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
function.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,61.0,"The App Service 'function' resource at line 61 does not restrict public access or IPs. By default, Azure App Service endpoints are public unless access restrictions are explicitly configured.","Configure access restrictions for the App Service to allow only required IP addresses or use Azure Front Door/Application Gateway. Consider using Private Endpoints to eliminate public exposure.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
function.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,61.0,The App Service 'function' resource at line 61 does not use Private Endpoints. Public endpoints increase the attack surface.,"Configure a Private Endpoint for the App Service to restrict access to internal networks only.

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",,Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
function.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,49.0,"The Storage Account 'funcStorage' at line 49 is referenced as 'existing' and there is no evidence of access restrictions or Private Endpoint configuration. By default, storage accounts are accessible over public endpoints unless restricted.","Ensure the referenced Storage Account has network access restrictions in place and consider configuring a Private Endpoint to eliminate public exposure.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
function.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,49.0,The Storage Account 'funcStorage' at line 49 does not have a Private Endpoint configured in this template. Public endpoints increase the attack surface.,"Configure a Private Endpoint for the Storage Account to restrict access to internal networks only.

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",,Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
hub-network.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,6.0,The subnet 'hub-subnet' defined at line 6 does not have a Network Security Group (NSG) associated. NSGs are required to segment and protect network resources as per ASB NS-1.,"Associate a Network Security Group (NSG) with the 'hub-subnet' by adding a 'networkSecurityGroup' property referencing an NSG resource. Define the NSG resource in the template and link it to the subnet.

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
hub-network.bicep,NS-3,Network Security,Use Network Security Groups (NSGs),CRITICAL,6.0,No Network Security Group (NSG) is configured for the 'hub-subnet' at line 6. NSGs are required to enforce network-level access control and deny all inbound traffic by default as per ASB NS-3.,"Create an NSG resource with default deny rules for inbound traffic and associate it with the 'hub-subnet' by specifying the 'networkSecurityGroup' property in the subnet definition.

📚 References: [Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules)

🔵 Azure Guidance: Implement NSGs with deny-by-default rules.",[Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules),Implement NSGs with deny-by-default rules.,"Configure NSGs to deny all inbound traffic by default. Allow only necessary traffic.
NSG rules documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works
Security rules best practices: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
hub-network.bicep,NS-5,Network Security,Use Private Endpoints,HIGH,19.0,"The subnet 'hub-subnet' at line 19 uses service endpoints for 'Microsoft.Storage.Global' and 'Microsoft.Keyvault', but does not use Private Endpoints. ASB NS-5 recommends using Private Endpoints for Storage and Key Vault to reduce attack surface.","Replace service endpoints with Private Endpoints for Azure Storage and Key Vault. Define Private Endpoint resources for these services and associate them with the subnet.

🔵 Azure Guidance: Implement Private Endpoints for Azure PaaS services.",,Implement Private Endpoints for Azure PaaS services.,"Configure Private Endpoints for Storage Key Vault SQL and other PaaS services.

This control is implemented through 1 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
instance-config.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,20.0,"The network_config object at line 20 defines an address_space ('10.0.0.0/8') and subnet ('10.0.0.0/16') but does not reference or configure any Network Security Groups (NSGs) to protect resources at the subnet or NIC level, as required by ASB NS-1.","Define and associate appropriate Network Security Groups (NSGs) with the subnet and/or NICs in your network configuration. Restrict inbound and outbound traffic to only required ports and protocols. Refer to Azure NSG documentation for implementation details.

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
ip-rules.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,3.0,"IP rules in 'corpNetIps' (line 3) include broad address ranges such as '*******/8', which may allow excessive public access and violate the requirement to restrict public endpoints to required IPs only.","Restrict allowed IP ranges in 'corpNetIps' to only those strictly necessary for business operations. Remove or narrow broad ranges like '*******/8' and consider using Azure Front Door, Application Gateway, or Private Link to further secure public endpoints as per ASB NS-2.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
keyvault.bicep,DP-6,Data Protection,Secure Data with Customer-Managed Keys (CMK),HIGH,18.0,"Key Vault 'networkAcls.defaultAction' is set to 'Allow', which permits public network access. This does not enforce secure access to encryption keys as required by DP-6.","Set 'networkAcls.defaultAction' to 'Deny' to restrict public network access. Only allow trusted networks or IPs via 'ipRules' and 'virtualNetworkRules'.

🔵 Azure Guidance: Use customer-managed keys for critical data.",,Use customer-managed keys for critical data.,"Configure Key Vault for key management. Rotate keys regularly.

This control is implemented through 3 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
operational-insights.bicep,IM-8,Identity Management,Use Managed Identities for Azure Resources,HIGH,1.0,"No managed identities are configured for any resources in the template. Managed identities should be used to avoid storing credentials in code, as required by ASB IM-8.","Configure managed identities for all applicable resources (e.g., add 'identity' property to resources such as Microsoft.OperationalInsights/workspaces and Microsoft.Insights/components) and use these identities for secure access to dependent services.

🔵 Azure Guidance: Enable system/user-assigned managed identities for Azure services.",,Enable system/user-assigned managed identities for Azure services.,"Configure managed identities for VMs apps and Azure Functions. Use for Key Vault access.

This control is implemented through 3 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
scaleset-networks.bicep,NS-1,Network Security,Protect resources using network security groups,CRITICAL,38.0,The 'scaleset' subnet in the 'vnet' resource (line 38) does not have a Network Security Group (NSG) associated. NSGs are required to segment and protect network resources.,"Associate a Network Security Group (NSG) with the 'scaleset' subnet by adding the 'networkSecurityGroup' property referencing an NSG resource. Define and attach an NSG with appropriate rules to the subnet.

📚 References: [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices)

🔵 Azure Guidance: Use NSGs to restrict traffic between resources. Default deny all.",[Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview) | [Network Best Practices](https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices),Use NSGs to restrict traffic between resources. Default deny all.,"Implement NSGs at subnet and NIC levels. Allow only required ports/protocols.
Azure NSG documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview
NSG best practices: https://docs.microsoft.com/en-us/azure/security/fundamentals/network-best-practices

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
scaleset-networks.bicep,NS-3,Network Security,Use Network Security Groups (NSGs),CRITICAL,38.0,"The 'scaleset' subnet in the 'vnet' resource (line 38) lacks an associated Network Security Group (NSG), which is required to enforce network-level access controls.","Create a Network Security Group (NSG) resource and associate it with the 'scaleset' subnet by adding the 'networkSecurityGroup' property to the subnet configuration. Ensure the NSG denies all inbound traffic by default and only allows necessary traffic.

📚 References: [Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules)

🔵 Azure Guidance: Implement NSGs with deny-by-default rules.",[Network Security Group How It Works](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works) | [Network Security Groups Overview](https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules),Implement NSGs with deny-by-default rules.,"Configure NSGs to deny all inbound traffic by default. Allow only necessary traffic.
NSG rules documentation: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-group-how-it-works
Security rules best practices: https://docs.microsoft.com/en-us/azure/virtual-network/network-security-groups-overview#security-rules

This control is implemented through 4 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
scaleset-networks.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,13.0,The 'scalesetOutboundIp' public IP address resource (line 13) creates a public endpoint without any explicit access restrictions. Public endpoints must be strictly controlled.,"Restrict access to the public IP by associating it with a firewall or NSG that limits allowed source IPs. Consider using Azure Firewall, Application Gateway, or Private Link to further restrict access.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
server-farms.bicep,DP-2,Data Protection,Enable encryption in transit,CRITICAL,74.0,"App Service resource 'serverFarms' (Microsoft.Web/serverfarms) does not explicitly enforce HTTPS-only or specify a minimum TLS version, violating encryption in transit requirements.","Add 'httpsOnly': true and specify 'minimumTlsVersion' (e.g., '1.2') in the 'properties' block of the App Service resource to enforce secure communication in transit.

🔵 Azure Guidance: Use TLS 1.2+ for all data transfers.",,Use TLS 1.2+ for all data transfers.,"Enable HTTPS-only. Configure minimum TLS version. Use secure protocols.

This control is implemented through 6 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
server-farms.bicep,IM-1,Identity Management,Use Azure Active Directory for Identity Management,HIGH,74.0,"App Service resource 'serverFarms' (Microsoft.Web/serverfarms) does not configure a managed identity, violating the requirement to use Azure AD for identity management.","Add an 'identity' block with 'type': 'SystemAssigned' or 'UserAssigned' to the App Service resource to enable managed identity and integrate with Azure AD.

🔵 Azure Guidance: Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.",,Ensure Azure AD is configured as the identity provider. Use managed identities for Azure resources.,"Configure Azure AD as your primary identity provider. For Azure resources use managed identities instead of credentials stored in code.

This control is implemented through 15 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
signalR.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,5.0,"The Microsoft.SignalRService/signalR resource does not restrict public network access or configure private endpoints. By default, SignalR Service is accessible over the public internet, which violates the requirement to protect public endpoints.","Restrict public network access by configuring the SignalR Service with private endpoints or IP ACLs. Alternatively, use Azure Front Door or Application Gateway to control access. Refer to Azure Private Link or service endpoint documentation for implementation.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,22.0,"The 'networkAcls' configuration for 'storageAccountFunc' sets 'defaultAction' to 'Allow', which permits public network access to the storage account. This exposes the storage account to the public internet, violating ASB NS-2 (Protect public endpoints).","Set 'networkAcls.defaultAction' to 'Deny' for 'storageAccountFunc' and explicitly allow only required IPs or subnets. Example: change 'defaultAction: ""Allow""' to 'defaultAction: ""Deny""' and ensure only trusted networks are listed in 'ipRules' and 'virtualNetworkRules'.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Protect public endpoints,CRITICAL,49.0,"The 'networkAcls' configuration in 'fuzzStorageProperties' (used by 'storageAccountFuzz' and 'storageAccountsCorpus') sets 'defaultAction' to 'Allow', which permits public network access to the storage accounts. This exposes the storage accounts to the public internet, violating ASB NS-2 (Protect public endpoints).","Set 'networkAcls.defaultAction' to 'Deny' in 'fuzzStorageProperties' and explicitly allow only required IPs or subnets. Example: change 'defaultAction: ""Allow""' to 'defaultAction: ""Deny""' and ensure only trusted networks are listed in 'ipRules' and 'virtualNetworkRules'.

📚 References: [Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers)

🔵 Azure Guidance: Use Private Link or service endpoints where possible.",[Private Link Overview](https://docs.microsoft.com/en-us/azure/private-link/private-link-overview) | [Virtual Network Service Endpoints Overview](https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview) | [Front Door Security Headers](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers),Use Private Link or service endpoints where possible.,"Restrict public access to required IPs only. Consider using Azure Front Door or Application Gateway.
Private Link documentation: https://docs.microsoft.com/en-us/azure/private-link/private-link-overview
Service Endpoints guide: https://docs.microsoft.com/en-us/azure/virtual-network/virtual-network-service-endpoints-overview
Azure Front Door security: https://docs.microsoft.com/en-us/azure/frontdoor/front-door-security-headers

This control is implemented through 40 Azure Security Center policies. For compliance, ensure your resources meet the requirements specified in these policies.",ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 29,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 7,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-17T14:07:38.807771,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
