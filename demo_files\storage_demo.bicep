// Demo Azure Storage Account with Security Issues
// This file demonstrates real file content in code dialogs

param storageAccountName string = 'demostorage${uniqueString(resourceGroup().id)}'
param location string = resourceGroup().location

// Storage Account with intentional security issues for demonstration
resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {
  name: storageAccountName
  location: location
  kind: 'StorageV2'
  sku: {
    name: 'Standard_LRS'
  }
  properties: {
    // 🚨 SECURITY ISSUE: Public blob access enabled (Line 16)
    allowBlobPublicAccess: true
    
    // 🚨 SECURITY ISSUE: Weak TLS version (Line 19)
    minimumTlsVersion: 'TLS1_0'
    
    // 🚨 SECURITY ISSUE: HTTP traffic allowed (Line 22)
    supportsHttpsTrafficOnly: false
    
    // Good configuration
    encryption: {
      services: {
        blob: {
          enabled: true
        }
        file: {
          enabled: true
        }
      }
      keySource: 'Microsoft.Storage'
    }
    
    // 🚨 SECURITY ISSUE: Default network access (Line 37)
    networkAcls: {
      defaultAction: 'Allow'
    }
  }
  
  tags: {
    Environment: 'Demo'
    Purpose: 'Real Content Testing'
    CreatedBy: 'IaC Guardian'
  }
}

// Output
output storageAccountId string = storageAccount.id
output storageAccountName string = storageAccount.name