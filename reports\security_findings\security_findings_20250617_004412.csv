Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,120,"App Service 'onefuzz-daily-ui' has 'publicNetworkAccess' set to 'Enabled', exposing the application to the public internet. This increases the attack surface and violates the requirement to secure all public endpoints.",Set 'publicNetworkAccess' to 'Disabled' or restrict access using access restrictions to minimize public exposure.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,196,"App Service configuration allows public network access ('publicNetworkAccess': 'Enabled'), exposing the application to the public internet. This increases the attack surface and violates the requirement to secure all public endpoints.",Set 'publicNetworkAccess' to 'Disabled' or configure IP restrictions to limit public exposure.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,197,"App Service configuration 'ipSecurityRestrictions' allows access from 'Any' IP address with action 'Allow', which exposes the application to the public internet. This violates the requirement to secure all public endpoints.",Restrict 'ipSecurityRestrictions' to only allow trusted IP ranges and remove 'Allow all' rules.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,197,"App Service configuration 'scmIpSecurityRestrictions' allows access from 'Any' IP address with action 'Allow', which exposes the SCM endpoint to the public internet. This violates the requirement to secure all public endpoints.",Restrict 'scmIpSecurityRestrictions' to only allow trusted IP ranges and remove 'Allow all' rules.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,222,"App Service configuration 'ipSecurityRestrictions' allows access from 'Any' IP address with action 'Allow', which exposes the application to the public internet. This violates the requirement to secure all public endpoints.",Restrict 'ipSecurityRestrictions' to only allow trusted IP ranges and remove 'Allow all' rules.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,231,"App Service configuration 'scmIpSecurityRestrictions' allows access from 'Any' IP address with action 'Allow', which exposes the SCM endpoint to the public internet. This violates the requirement to secure all public endpoints.",Restrict 'scmIpSecurityRestrictions' to only allow trusted IP ranges and remove 'Allow all' rules.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,120,"App Service 'onefuzz-daily-ui' does not use private endpoints and has 'publicNetworkAccess' enabled, which does not follow the recommendation to use private endpoints for secure access.",Configure a private endpoint for the App Service and set 'publicNetworkAccess' to 'Disabled' to restrict access to private networks only.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,196,"App Service configuration does not use private endpoints and has 'publicNetworkAccess' enabled, which does not follow the recommendation to use private endpoints for secure access.",Configure a private endpoint for the App Service and set 'publicNetworkAccess' to 'Disabled' to restrict access to private networks only.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,197,"App Service configuration does not use private endpoints and has 'publicNetworkAccess' enabled, which does not follow the recommendation to use private endpoints for secure access.",Configure a private endpoint for the App Service and set 'publicNetworkAccess' to 'Disabled' to restrict access to private networks only.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,120,App Service 'onefuzz-daily-ui' does not specify the use of encryption at rest or customer-managed keys. This violates the requirement to enable encryption at rest for all data storage.,Enable encryption at rest for the App Service and configure customer-managed keys if required for sensitive data.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,196,App Service configuration does not specify the use of encryption at rest or customer-managed keys. This violates the requirement to enable encryption at rest for all data storage.,Enable encryption at rest for the App Service and configure customer-managed keys if required for sensitive data.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,197,App Service configuration does not specify the use of encryption at rest or customer-managed keys. This violates the requirement to enable encryption at rest for all data storage.,Enable encryption at rest for the App Service and configure customer-managed keys if required for sensitive data.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,197,App Service configuration does not specify the use of encryption at rest or customer-managed keys. This violates the requirement to enable encryption at rest for all data storage.,Enable encryption at rest for the App Service and configure customer-managed keys if required for sensitive data.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,54,"App Service 'onefuzz-daily-ui' has 'sslState' set to 'Disabled' for hostName 'onefuzz-daily-ui.azurewebsites.net', which allows unencrypted HTTP traffic. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates entries to enforce HTTPS and ensure encryption in transit.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,62,"App Service 'onefuzz-daily-ui' has 'sslState' set to 'Disabled' for hostName 'onefuzz-daily-ui.scm.azurewebsites.net', which allows unencrypted HTTP traffic. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates entries to enforce HTTPS and ensure encryption in transit.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,197,"App Service configuration does not enforce HTTP/2 ('http20Enabled': false), which is recommended for secure and efficient encrypted communication.",Set 'http20Enabled' to true in the App Service configuration to enforce HTTP/2 for secure communication.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,197,"App Service configuration does not enforce HTTP/2 for SCM endpoint ('http20Enabled': false), which is recommended for secure and efficient encrypted communication.",Set 'http20Enabled' to true in the App Service configuration to enforce HTTP/2 for SCM endpoint.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,197,"App Service configuration does not enforce minimum TLS version for SCM endpoint ('scmMinTlsVersion': '1.2'), which is correct, but ensure all endpoints enforce TLS 1.2 or higher.",Verify all endpoints enforce TLS 1.2 or higher for secure communication.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,197,"App Service configuration does not enforce minimum TLS version for main endpoint ('minTlsVersion': '1.2'), which is correct, but ensure all endpoints enforce TLS 1.2 or higher.",Verify all endpoints enforce TLS 1.2 or higher for secure communication.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,197,"App Service configuration does not enforce HTTP/2 ('http20Enabled': false), which is recommended for secure and efficient encrypted communication.",Set 'http20Enabled' to true in the App Service configuration to enforce HTTP/2 for secure communication.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,197,"App Service configuration does not enforce HTTP/2 for SCM endpoint ('http20Enabled': false), which is recommended for secure and efficient encrypted communication.",Set 'http20Enabled' to true in the App Service configuration to enforce HTTP/2 for SCM endpoint.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,120,App Service 'onefuzz-daily-ui' does not reference Azure Key Vault for storing sensitive information such as secrets or connection strings. This violates the requirement to store sensitive data in Azure Key Vault.,"Store all sensitive information, such as secrets and connection strings, in Azure Key Vault and reference them securely in the App Service configuration.",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,196,App Service configuration does not reference Azure Key Vault for storing sensitive information such as secrets or connection strings. This violates the requirement to store sensitive data in Azure Key Vault.,"Store all sensitive information, such as secrets and connection strings, in Azure Key Vault and reference them securely in the App Service configuration.",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,197,App Service configuration does not reference Azure Key Vault for storing sensitive information such as secrets or connection strings. This violates the requirement to store sensitive data in Azure Key Vault.,"Store all sensitive information, such as secrets and connection strings, in Azure Key Vault and reference them securely in the App Service configuration.",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,197,App Service configuration does not reference Azure Key Vault for storing sensitive information such as secrets or connection strings. This violates the requirement to store sensitive data in Azure Key Vault.,"Store all sensitive information, such as secrets and connection strings, in Azure Key Vault and reference them securely in the App Service configuration.",N/A,AI,Generic
