ASB ID,Control Domain,Recommendation,Security Principle,Azure Guidance,Implementation and additional context,Customer Security Stakeholders,Azure Policy Mapping
NS-1,Network Security,Establish network segmentation boundaries,Ensure that your virtual network deployment aligns to your enterprise segmentation strategy. Any workload that could incur higher risk should be in isolated virtual networks.,"Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.","Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",Network Admins; Security Architecture,RequireNSG;DenyDefaultInbound;AdaptiveNetworkHardening
NS-2,Network Security,Secure cloud services with network controls,Secure cloud services by establishing private access points and disabling/restricting public network access when possible.,"Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.","Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",Network Admins; Security Architecture,RequirePrivateEndpoint;DisablePublicAccess
NS-3,Network Security,Deploy firewall at the edge of enterprise network,Deploy a firewall to perform advanced filtering on network traffic to and from external networks. Use firewalls between internal segments to support segmentation strategy.,"Use Azure Firewall for stateful application layer traffic restriction and central management. Create user-defined routes (UDR) for complex topologies. Block known bad IPs and high-risk protocols.","Enhanced Implementation Context:
• Azure Firewall deployment: https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal
• Virtual network traffic routing: https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview
• Azure Firewall Manager: https://docs.microsoft.com/azure/firewall-manager/overview
• Hub-spoke topology: https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 4.4, 4.8, 13.10
• NIST SP800-53 r4: AC-4, SC-7, CM-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Management ports should be closed on your virtual machines
• Management ports of virtual machines should be protected with just-in-time network access control
• IP Forwarding on your virtual machine should be disabled
• All Internet traffic should be routed via your deployed Azure Firewall",Network Admins; Security Architecture,RequireFirewall;EnableThreatIntel;BlockHighRiskProtocols
NS-4,Network Security,Deploy intrusion detection/intrusion prevention systems (IDS/IPS),Use network intrusion detection and prevention systems to inspect network and payload traffic. Ensure IDS/IPS provides high-quality alerts to your SIEM solution.,"Use Azure Firewall's IDPS capability to alert on and block traffic to/from known malicious IP addresses and domains. Deploy host-based EDR solutions like Microsoft Defender for Endpoint for comprehensive protection.","Enhanced Implementation Context:
• Azure Firewall IDPS: https://docs.microsoft.com/azure/firewall/premium-features#idps
• Microsoft Defender for Endpoint: https://docs.microsoft.com/windows/security/threat-protection/microsoft-defender-atp/overview-endpoint-detection-response
• Azure Sentinel integration: https://docs.microsoft.com/azure/sentinel/connect-azure-firewall
• Network security monitoring: https://docs.microsoft.com/azure/security/fundamentals/network-monitoring
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 13.2, 13.3, 13.7, 13.8
• NIST SP800-53 r4: SC-7, SI-4
• PCI-DSS v3.2.1: 11.4

Azure Policy Examples:
• No applicable built-in policy (requires custom implementation)
• Consider using Azure Security Center recommendations for network protection
• Enable Microsoft Defender for Endpoint on all virtual machines",Network Admins; Security Operations,EnableIDPS;DeployEDR;IntegrateSIEM
NS-5,Network Security,Deploy DDoS protection,Deploy distributed denial of service (DDoS) protection to protect your network and applications from attacks.,"Enable DDoS Standard protection plan on your VNet to protect resources exposed to public networks. Configure DDoS policies and monitoring.","Enhanced Implementation Context:
• Azure DDoS Protection Standard: https://docs.microsoft.com/azure/virtual-network/manage-ddos-protection
• DDoS response strategy: https://docs.microsoft.com/azure/ddos-protection/ddos-response-strategy
• DDoS monitoring and alerting: https://docs.microsoft.com/azure/ddos-protection/telemetry-monitoring-alerting
• DDoS best practices: https://docs.microsoft.com/azure/security/fundamentals/ddos-best-practices
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 13.10
• NIST SP800-53 r4: SC-5, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3, 6.6

Azure Policy Examples:
• Azure DDoS Protection Standard should be enabled
• Monitor DDoS attack metrics and configure alerts
• Implement DDoS response procedures",Network Admins; Security Operations,EnableDDoSProtection;ConfigureDDoSMonitoring
NS-6,Network Security,Deploy web application firewall,Deploy a web application firewall (WAF) and configure appropriate rules to protect web applications and APIs from application-specific attacks.,"Use WAF capabilities in Azure Application Gateway Azure Front Door and Azure CDN. Set WAF in detection or prevention mode. Choose built-in rulesets like OWASP Top 10 and tune to your application.","Enhanced Implementation Context:
• Azure WAF overview: https://docs.microsoft.com/azure/web-application-firewall/overview
• Application Gateway WAF: https://docs.microsoft.com/azure/web-application-firewall/ag/ag-overview
• Front Door WAF: https://docs.microsoft.com/azure/web-application-firewall/afds/afds-overview
• CDN WAF: https://docs.microsoft.com/azure/web-application-firewall/cdn/cdn-overview
• OWASP Top 10 protection: https://docs.microsoft.com/azure/web-application-firewall/ag/application-gateway-crs-rulegroups-rules
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 13.10
• NIST SP800-53 r4: SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3, 6.6

Azure Policy Examples:
• Web Application Firewall (WAF) should be enabled for Azure Front Door Service
• Web Application Firewall (WAF) should be enabled for Application Gateway
• Configure WAF policies with appropriate rule sets",Network Admins; Security Architecture; Application Security,RequireWAF;EnableOWASPRules;ConfigureWAFPolicies
NS-7,Network Security,Simplify network security configuration,When managing complex network environments use tools to simplify centralize and enhance network security management.,"Use Microsoft Defender for Cloud Adaptive Network Hardening for NSG recommendations. Use Azure Firewall Manager for centralized policy and route management. Implement ARM templates for consistent deployment.","Enhanced Implementation Context:
• Adaptive Network Hardening: https://docs.microsoft.com/azure/security-center/security-center-adaptive-network-hardening
• Azure Firewall Manager: https://docs.microsoft.com/azure/firewall-manager/overview
• ARM template for Firewall policy: https://docs.microsoft.com/azure/firewall-manager/quick-firewall-policy
• Network security automation: https://docs.microsoft.com/azure/automation/automation-network-configuration
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 4.4, 4.8
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• Use Azure Firewall Manager for centralized policy management
• Implement consistent network security configurations",Network Admins; Security Architecture,EnableAdaptiveHardening;CentralizeFirewallManagement
NS-8,Network Security,Detect and disable insecure services and protocols,Detect and disable insecure services and protocols at the OS application or software package layer. Deploy compensating controls if disabling is not possible.,"Use Azure Sentinel's Insecure Protocol Workbook to discover insecure services like SSL/TLSv1 SSHv1 SMBv1 LM/NTLMv1. Disable insecure protocols or use compensating controls like NSG/Firewall blocking.","Enhanced Implementation Context:
• Azure Sentinel insecure protocols workbook: https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks
• TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem
• Secure protocol configuration: https://docs.microsoft.com/azure/security/fundamentals/network-best-practices
• Network security monitoring: https://docs.microsoft.com/azure/security/fundamentals/network-monitoring
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 4.4, 4.8
• NIST SP800-53 r4: CM-2, CM-6, CM-7
• PCI-DSS v3.2.1: 4.1, A2.1, A2.2, A2.3

Azure Policy Examples:
• Latest TLS version should be used in your API App
• Latest TLS version should be used in your Web App
• Latest TLS version should be used in your Function App
• Secure transfer to storage accounts should be enabled",Network Admins; Security Operations,DisableInsecureProtocols;EnforceTLS12;MonitorProtocolUsage
NS-9,Network Security,Connect on-premises or cloud network privately,Use private connections for secure communication between different networks such as cloud service provider datacenters and on-premises infrastructure.,"Use Azure VPN for lightweight site-to-site or point-to-site connectivity. Use Azure ExpressRoute for enterprise-level high performance connections. Use virtual network peering for Azure VNet connections.","Enhanced Implementation Context:
• Azure VPN Gateway overview: https://docs.microsoft.com/azure/vpn-gateway/vpn-gateway-about-vpngateways
• ExpressRoute connectivity models: https://docs.microsoft.com/azure/expressroute/expressroute-connectivity-models
• Virtual network peering: https://docs.microsoft.com/azure/virtual-network/virtual-network-peering-overview
• Azure Virtual WAN: https://docs.microsoft.com/azure/virtual-wan/virtual-wan-about
• Hybrid network architecture: https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 12.7
• NIST SP800-53 r4: CA-3, AC-17, AC-4
• PCI-DSS v3.2.1: Not specified

Azure Policy Examples:
• No applicable built-in policy (requires architectural decisions)
• Implement network connectivity governance policies
• Monitor and audit network connections",Network Admins; Security Architecture,RequirePrivateConnectivity;MonitorNetworkConnections
NS-10,Network Security,Ensure Domain Name System (DNS) security,Ensure DNS security configuration protects against known risks. Use trusted authoritative and recursive DNS services. Separate public and private DNS resolution.,"Use Azure recursive DNS or trusted external DNS servers. Use Azure Private DNS for private zones. Use Azure Defender for DNS for advanced protection against DNS threats including data exfiltration malware communication and DNS attacks.","Enhanced Implementation Context:
• Azure DNS overview: https://docs.microsoft.com/azure/dns/dns-overview
• NIST DNS Deployment Guide: https://csrc.nist.gov/publications/detail/sp/800-81/2/final
• Azure Private DNS: https://docs.microsoft.com/azure/dns/private-dns-overview
• Azure Defender for DNS: https://docs.microsoft.com/azure/security-center/defender-for-dns-introduction
• Prevent dangling DNS entries: https://docs.microsoft.com/azure/security/fundamentals/subdomain-takeover
• DNS security best practices: https://docs.microsoft.com/azure/security/fundamentals/network-best-practices#secure-name-resolution
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 4.9, 9.2
• NIST SP800-53 r4: SC-20, SC-21
• PCI-DSS v3.2.1: Not specified

Azure Policy Examples:
• Azure Defender for DNS should be enabled
• Configure trusted DNS servers for virtual networks
• Monitor DNS queries for malicious activity
• Implement DNS filtering and threat protection",Network Admins; Security Operations,EnableDNSDefender;ConfigurePrivateDNS;MonitorDNSThreats
