# Master Database Architecture Design

## Overview
This document presents a comprehensive architecture design for the unified master security controls database system that will replace the current static file approach and address all identified limitations.

## Architecture Principles

### 1. Single Source of Truth
- **Unified Data Model**: All security control information consolidated into one authoritative database
- **Control ID as Primary Key**: Consistent identification across all frameworks and sources
- **Source Attribution**: Track origin and lineage of all control data
- **Version Management**: Full audit trail of control evolution

### 2. AI-Powered Intelligence
- **Intelligent Data Merging**: AI-driven consolidation of conflicting information
- **Context-Aware Validation**: Reduce false positives through semantic understanding
- **Dynamic Control Selection**: Smart relevance scoring for resource-control matching
- **Conflict Resolution**: Automated resolution of data inconsistencies

### 3. Dynamic Extensibility
- **Framework Agnostic**: Support multiple security frameworks (ASB, CIS, NIST, custom)
- **API-Driven Extensions**: Programmatic addition of custom controls
- **Schema Evolution**: Support for new control types and metadata
- **Organization Customization**: Tenant-specific control definitions

## System Architecture

### 1. Database Layer

#### Core Database Schema
```sql
-- Security Controls Master Table
CREATE TABLE security_controls (
    control_id VARCHAR(50) PRIMARY KEY,
    framework_id VARCHAR(50) NOT NULL,
    domain VARCHAR(100) NOT NULL,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    severity ENUM('CRITICAL', 'HIGH', 'MEDIUM', 'LOW') NOT NULL,
    implementation_guidance TEXT,
    azure_guidance TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    version INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    
    INDEX idx_framework_domain (framework_id, domain),
    INDEX idx_severity (severity),
    INDEX idx_active (is_active)
);

-- Security Frameworks
CREATE TABLE security_frameworks (
    framework_id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    version VARCHAR(50),
    description TEXT,
    official_url VARCHAR(500),
    priority INTEGER DEFAULT 100,
    is_active BOOLEAN DEFAULT TRUE
);

-- Control Sources (for audit trail)
CREATE TABLE control_sources (
    source_id VARCHAR(50) PRIMARY KEY,
    control_id VARCHAR(50),
    source_type ENUM('CSV', 'JSON', 'EXCEL', 'API', 'MANUAL') NOT NULL,
    source_file VARCHAR(500),
    source_url VARCHAR(500),
    imported_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_quality_score DECIMAL(3,2),
    
    FOREIGN KEY (control_id) REFERENCES security_controls(control_id)
);

-- Resource Type Mappings
CREATE TABLE resource_control_mappings (
    mapping_id VARCHAR(50) PRIMARY KEY,
    resource_type VARCHAR(100) NOT NULL,
    control_id VARCHAR(50) NOT NULL,
    relevance_score DECIMAL(3,2) DEFAULT 1.0,
    mapping_type ENUM('DIRECT', 'INFERRED', 'CUSTOM') DEFAULT 'DIRECT',
    created_by VARCHAR(100),
    
    FOREIGN KEY (control_id) REFERENCES security_controls(control_id),
    INDEX idx_resource_type (resource_type),
    INDEX idx_relevance (relevance_score DESC)
);

-- Custom Controls (organization-specific)
CREATE TABLE custom_controls (
    custom_control_id VARCHAR(50) PRIMARY KEY,
    organization_id VARCHAR(50) NOT NULL,
    base_control_id VARCHAR(50),
    custom_name TEXT NOT NULL,
    custom_description TEXT NOT NULL,
    custom_implementation TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (base_control_id) REFERENCES security_controls(control_id),
    INDEX idx_organization (organization_id)
);
```

#### Extended Metadata Tables
```sql
-- Control Relationships (for cross-validation)
CREATE TABLE control_relationships (
    relationship_id VARCHAR(50) PRIMARY KEY,
    parent_control_id VARCHAR(50) NOT NULL,
    child_control_id VARCHAR(50) NOT NULL,
    relationship_type ENUM('DEPENDS_ON', 'CONFLICTS_WITH', 'COMPLEMENTS', 'SUPERSEDES'),
    strength DECIMAL(3,2) DEFAULT 1.0,
    
    FOREIGN KEY (parent_control_id) REFERENCES security_controls(control_id),
    FOREIGN KEY (child_control_id) REFERENCES security_controls(control_id)
);

-- Control Links and References
CREATE TABLE control_references (
    reference_id VARCHAR(50) PRIMARY KEY,
    control_id VARCHAR(50) NOT NULL,
    reference_type ENUM('DOCUMENTATION', 'POLICY', 'IMPLEMENTATION', 'EXAMPLE'),
    title VARCHAR(500),
    url VARCHAR(1000),
    description TEXT,
    
    FOREIGN KEY (control_id) REFERENCES security_controls(control_id)
);

-- Analysis History (for learning and optimization)
CREATE TABLE analysis_history (
    analysis_id VARCHAR(50) PRIMARY KEY,
    resource_type VARCHAR(100),
    control_id VARCHAR(50),
    finding_type ENUM('TRUE_POSITIVE', 'FALSE_POSITIVE', 'FALSE_NEGATIVE'),
    confidence_score DECIMAL(3,2),
    user_feedback ENUM('CONFIRMED', 'REJECTED', 'MODIFIED'),
    analyzed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (control_id) REFERENCES security_controls(control_id),
    INDEX idx_resource_analysis (resource_type, analyzed_at),
    INDEX idx_feedback (user_feedback)
);
```

### 2. Data Access Layer

#### Database Abstraction Interface
```python
from abc import ABC, abstractmethod
from typing import List, Dict, Optional
from dataclasses import dataclass

@dataclass
class SecurityControl:
    control_id: str
    framework_id: str
    domain: str
    name: str
    description: str
    severity: str
    implementation_guidance: Optional[str] = None
    azure_guidance: Optional[str] = None
    relevance_score: Optional[float] = None
    custom_fields: Optional[Dict] = None

class ControlDataProvider(ABC):
    """Abstract interface for security control data access."""
    
    @abstractmethod
    async def get_controls_by_resource_type(
        self, 
        resource_type: str, 
        frameworks: List[str] = None,
        max_controls: int = 15
    ) -> List[SecurityControl]:
        """Get relevant controls for a resource type."""
        pass
    
    @abstractmethod
    async def get_control_by_id(self, control_id: str) -> Optional[SecurityControl]:
        """Get a specific control by ID."""
        pass
    
    @abstractmethod
    async def add_custom_control(
        self, 
        control: SecurityControl, 
        organization_id: str
    ) -> str:
        """Add a custom control for an organization."""
        pass
    
    @abstractmethod
    async def validate_control_relationships(
        self, 
        control_ids: List[str]
    ) -> Dict[str, List[str]]:
        """Validate relationships between controls."""
        pass
```

#### Database Implementation
```python
import asyncpg
from typing import List, Dict, Optional
import json

class PostgreSQLControlProvider(ControlDataProvider):
    """PostgreSQL implementation of control data provider."""
    
    def __init__(self, connection_string: str):
        self.connection_string = connection_string
        self.pool = None
    
    async def initialize(self):
        """Initialize connection pool."""
        self.pool = await asyncpg.create_pool(self.connection_string)
    
    async def get_controls_by_resource_type(
        self, 
        resource_type: str, 
        frameworks: List[str] = None,
        max_controls: int = 15
    ) -> List[SecurityControl]:
        """Get relevant controls with intelligent ranking."""
        
        query = """
        SELECT DISTINCT
            sc.control_id,
            sc.framework_id,
            sc.domain,
            sc.name,
            sc.description,
            sc.severity,
            sc.implementation_guidance,
            sc.azure_guidance,
            rcm.relevance_score,
            sf.priority as framework_priority
        FROM security_controls sc
        JOIN resource_control_mappings rcm ON sc.control_id = rcm.control_id
        JOIN security_frameworks sf ON sc.framework_id = sf.framework_id
        WHERE rcm.resource_type = $1
        AND sc.is_active = TRUE
        AND sf.is_active = TRUE
        """
        
        params = [resource_type]
        
        if frameworks:
            query += " AND sc.framework_id = ANY($2)"
            params.append(frameworks)
        
        query += """
        ORDER BY 
            sf.priority ASC,
            CASE sc.severity 
                WHEN 'CRITICAL' THEN 1
                WHEN 'HIGH' THEN 2
                WHEN 'MEDIUM' THEN 3
                WHEN 'LOW' THEN 4
            END,
            rcm.relevance_score DESC,
            sc.domain,
            sc.control_id
        LIMIT ${}
        """.format(len(params) + 1)
        
        params.append(max_controls)
        
        async with self.pool.acquire() as conn:
            rows = await conn.fetch(query, *params)
            
        return [
            SecurityControl(
                control_id=row['control_id'],
                framework_id=row['framework_id'],
                domain=row['domain'],
                name=row['name'],
                description=row['description'],
                severity=row['severity'],
                implementation_guidance=row['implementation_guidance'],
                azure_guidance=row['azure_guidance'],
                relevance_score=float(row['relevance_score']) if row['relevance_score'] else None
            )
            for row in rows
        ]
```

### 3. AI-Powered Data Processing Engine

#### Intelligent Data Merger
```python
from openai import AsyncAzureOpenAI
import asyncio

class AIDataProcessor:
    """AI-powered data processing for control consolidation."""
    
    def __init__(self, openai_client: AsyncAzureOpenAI):
        self.openai_client = openai_client
    
    async def merge_conflicting_controls(
        self, 
        control_id: str, 
        sources: List[Dict]
    ) -> Dict:
        """Intelligently merge control data from multiple sources."""
        
        merge_prompt = f"""
        You are a security expert tasked with merging control information from multiple sources.
        
        Control ID: {control_id}
        
        Sources:
        {json.dumps(sources, indent=2)}
        
        Please provide the best merged version considering:
        1. Most comprehensive and accurate description
        2. Most actionable implementation guidance
        3. Highest quality Azure-specific guidance
        4. Appropriate severity level
        
        Return JSON with: name, description, implementation_guidance, azure_guidance, severity, confidence_score
        """
        
        response = await self.openai_client.chat.completions.create(
            model="gpt-4",
            messages=[{"role": "user", "content": merge_prompt}],
            temperature=0.1,
            response_format={"type": "json_object"}
        )
        
        return json.loads(response.choices[0].message.content)
    
    async def validate_control_relevance(
        self, 
        control: SecurityControl, 
        resource_type: str,
        context: str
    ) -> float:
        """Calculate relevance score for control-resource pairing."""
        
        relevance_prompt = f"""
        Evaluate the relevance of this security control for the given resource type and context.
        
        Control: {control.name}
        Description: {control.description}
        Resource Type: {resource_type}
        Context: {context}
        
        Return a relevance score from 0.0 to 1.0 where:
        - 1.0 = Highly relevant and directly applicable
        - 0.7-0.9 = Relevant with some applicability
        - 0.4-0.6 = Moderately relevant
        - 0.1-0.3 = Low relevance
        - 0.0 = Not relevant
        
        Return only the numeric score.
        """
        
        response = await self.openai_client.chat.completions.create(
            model="gpt-4",
            messages=[{"role": "user", "content": relevance_prompt}],
            temperature=0.1
        )
        
        try:
            return float(response.choices[0].message.content.strip())
        except ValueError:
            return 0.5  # Default moderate relevance
```

### 4. API Layer and Service Architecture

#### RESTful API Design
```python
from fastapi import FastAPI, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional

app = FastAPI(title="IaC Guardian Control Database API")

class ControlRequest(BaseModel):
    resource_type: str
    frameworks: Optional[List[str]] = None
    max_controls: int = 15
    organization_id: Optional[str] = None

class CustomControlRequest(BaseModel):
    name: str
    description: str
    implementation_guidance: str
    severity: str
    resource_types: List[str]
    organization_id: str

@app.get("/api/v1/controls/resource/{resource_type}")
async def get_controls_for_resource(
    resource_type: str,
    frameworks: Optional[str] = None,
    max_controls: int = 15,
    provider: ControlDataProvider = Depends(get_control_provider)
):
    """Get relevant controls for a resource type."""
    framework_list = frameworks.split(',') if frameworks else None
    
    controls = await provider.get_controls_by_resource_type(
        resource_type=resource_type,
        frameworks=framework_list,
        max_controls=max_controls
    )
    
    return {"controls": controls, "count": len(controls)}

@app.post("/api/v1/controls/custom")
async def create_custom_control(
    request: CustomControlRequest,
    provider: ControlDataProvider = Depends(get_control_provider)
):
    """Create a custom control for an organization."""
    control = SecurityControl(
        control_id=f"CUSTOM-{request.organization_id}-{generate_id()}",
        framework_id="CUSTOM",
        domain="Custom",
        name=request.name,
        description=request.description,
        severity=request.severity,
        implementation_guidance=request.implementation_guidance
    )
    
    control_id = await provider.add_custom_control(control, request.organization_id)
    return {"control_id": control_id, "status": "created"}

@app.post("/api/v1/controls/validate")
async def validate_control_set(
    control_ids: List[str],
    provider: ControlDataProvider = Depends(get_control_provider)
):
    """Validate relationships between controls."""
    relationships = await provider.validate_control_relationships(control_ids)
    return {"relationships": relationships}
```

## Integration Strategy

### 1. Backward Compatibility Layer
```python
class LegacyCompatibilityAdapter:
    """Adapter to maintain compatibility with existing SecurityPRReviewer interface."""
    
    def __init__(self, control_provider: ControlDataProvider):
        self.control_provider = control_provider
        self._cache = {}
    
    async def prepare_benchmark(self) -> Dict:
        """Legacy method that now uses database."""
        if 'benchmark_data' not in self._cache:
            # Load all active controls from database
            controls = await self.control_provider.get_all_active_controls()
            
            # Convert to legacy format
            self._cache['benchmark_data'] = {
                "controls": [self._to_legacy_format(c) for c in controls],
                "metadata": {
                    "source": "master_database",
                    "version": "1.0",
                    "loaded_at": datetime.now().isoformat()
                }
            }
        
        return self._cache['benchmark_data']
    
    def _to_legacy_format(self, control: SecurityControl) -> Dict:
        """Convert new control format to legacy format."""
        return {
            "id": control.control_id,
            "name": control.name,
            "description": control.description,
            "domain": control.domain,
            "severity": control.severity,
            "implementation": control.implementation_guidance,
            "azure_guidance": control.azure_guidance
        }
```

### 2. Migration Strategy

#### Phase 1: Database as Additional Source
- Add database as new source in `BENCHMARK_SOURCE_PRIORITY`
- Implement database provider alongside existing file providers
- Gradual testing and validation

#### Phase 2: Primary Database Usage
- Make database the primary source
- Keep file sources as fallback
- Monitor performance and accuracy

#### Phase 3: File Source Deprecation
- Remove file-based loading logic
- Optimize for database-only access
- Clean up legacy code

#### Phase 4: Advanced Features
- Implement AI-powered features
- Add custom control management
- Enable advanced analytics

## Technology Recommendations

### 1. Database Technology: PostgreSQL
**Rationale:**
- **ACID Compliance**: Ensures data consistency
- **JSON Support**: Flexible schema evolution
- **Full-Text Search**: Advanced control search capabilities
- **Performance**: Excellent for read-heavy workloads
- **Ecosystem**: Rich tooling and library support

### 2. Caching Layer: Redis
**Benefits:**
- **Fast Lookups**: Sub-millisecond control retrieval
- **Session Storage**: Cache user-specific customizations
- **Rate Limiting**: API protection
- **Pub/Sub**: Real-time updates

### 3. API Framework: FastAPI
**Advantages:**
- **Async Support**: High-performance async operations
- **Auto Documentation**: OpenAPI/Swagger integration
- **Type Safety**: Pydantic model validation
- **Modern Python**: Latest language features

## Success Metrics

### 1. Performance Improvements
- **Initialization Time**: < 100ms (vs current 2-5 seconds)
- **Control Retrieval**: < 10ms per query
- **Analysis Throughput**: 10x improvement in files/second

### 2. Quality Improvements
- **Consistency Rate**: > 99% identical results across runs
- **False Positive Reduction**: 50% reduction in false positives
- **Coverage Increase**: 100% control coverage across all domains

### 3. Operational Improvements
- **Zero Manual File Management**: Eliminate static file updates
- **Custom Control Addition**: < 5 minutes via API
- **Deployment Simplification**: Single database deployment

This architecture provides a solid foundation for the master database system that addresses all current limitations while enabling future enhancements and scalability.
