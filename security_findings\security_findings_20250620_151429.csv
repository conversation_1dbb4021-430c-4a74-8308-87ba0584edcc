File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
app-config.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyValues' flows across template boundary,Secure parameter 'keyValues' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'app_insights_key' flows across template boundary,Secure parameter 'app_insights_key' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyvault_name' flows across template boundary,Secure parameter 'keyvault_name' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
keyvault.bicep,NS-1,Network Security,Establish network segmentation boundaries,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,Review template dependencies and ensure secure communication between templates,,,,cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'secrets' flows across template boundary,Secure parameter 'secrets' and validate its usage across template boundaries,,,,cross_reference_analysis,privilege_escalation,Validated
storage-accounts.bicep,NS-1,Network Security,Establish network segmentation boundaries,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,Review template dependencies and ensure secure communication between templates,,,,cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,DP-6,Data Protection,Use a secure key management process,HIGH,1.0,Parameter flow security risk: Sensitive parameters may be exposed through template dependencies,Use Key Vault references for sensitive parameters and validate parameter flow security,,,,cross_reference_analysis,parameter_flow,Validated
function.bicep,NS-8,Network Security,Detect and disable insecure services and protocols,HIGH,48.0,"The 'ftpsState' property is set to 'Disabled' in the App Service site configuration. This disables FTPS, which is the only secure protocol for file transfers to Azure App Service. Disabling FTPS allows only insecure FTP or no file transfer protocol at all, potentially enabling attackers to intercept credentials or inject malicious files if FTP is enabled elsewhere. This weakens the security posture by not enforcing secure protocols for management operations, increasing the risk of credential theft and lateral movement.","Set 'ftpsState' to 'FtpsOnly' in the site configuration to enforce secure file transfer. Update line 48 to: ftpsState: 'FtpsOnly'. This ensures only encrypted FTPS connections are allowed, in line with Azure security best practices and ASB NS-8.",,,,ai_analysis,,Validated
hub-network.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,14.0,"The subnet 'hub-subnet' defined at line 14 does not specify a networkSecurityGroup property. Without an associated Network Security Group (NSG), the subnet lacks explicit traffic filtering, enabling potential attack vectors such as unrestricted lateral movement, initial access via open ports, and increased blast radius if a resource is compromised. Attackers could exploit this to move laterally within the VNet or access resources without restriction.",Associate a Network Security Group (NSG) with the 'hub-subnet' by adding the 'networkSecurityGroup' property to the subnet definition. Define restrictive inbound and outbound rules to enforce a deny-by-default policy and only allow required traffic. Example: 'networkSecurityGroup: { id: <resourceId of NSG> }'.,,,,ai_analysis,,Validated
instance-config.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,24.0,"The 'address_space' property in 'network_config' (Line 025) is set to '10.0.0.0/8', which defines a very large flat network. This lack of segmentation increases the blast radius for lateral movement, as a compromise of any resource within the address space could allow an attacker to move laterally across a broad range of resources. This violates Azure Security Benchmark NS-1, which requires network segmentation boundaries to limit risk and contain breaches.","Redesign the 'address_space' in 'network_config' to use smaller, segmented address spaces aligned with workload isolation and security boundaries. For example, use multiple VNets or subnets with more restrictive CIDR ranges (e.g., '********/16', '********/16') and apply Network Security Groups (NSGs) to enforce segmentation. Ensure each subnet is associated with an NSG to restrict traffic according to least privilege.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,4.0,"The 'corpNetIps' variable on line 004 includes the IP range '*******/8', which is an extremely broad public IP range. Allowing such a wide range in network rules can enable initial access for attackers from any address within this /8, significantly increasing the attack surface and blast radius. This configuration undermines network segmentation and exposes resources to potential compromise from a large portion of the public internet.","Restrict the 'corpNetIps' variable to only trusted, minimal, and well-defined corporate IP ranges. Remove or replace '*******/8' with specific, validated corporate subnets. Ensure all allowed IP ranges are tightly scoped to reduce exposure and align with enterprise segmentation strategy as per NS-1.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,5.0,"The 'corpNetIps' variable on line 005 includes the IP range '********/8', which is a very broad public IP range. Allowing such a large range in network rules can enable initial access for attackers from any address within this /8, greatly increasing the attack surface and blast radius. This configuration violates network segmentation best practices and exposes resources to compromise from a significant portion of the public internet.","Limit the 'corpNetIps' variable to only necessary, trusted, and specific corporate IP ranges. Remove or replace '********/8' with precise, validated subnets. Ensure all allowed IPs are strictly controlled to minimize exposure, following NS-1 guidance.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,6.0,"The 'corpNetIps' variable on line 006 includes the IP range '20.0.0.0/8', which is a massive public IP block. Allowing this range in network rules can provide initial access to attackers from any address within this /8, drastically increasing the attack surface and potential blast radius. This undermines network segmentation and exposes resources to widespread compromise.","Update the 'corpNetIps' variable to only include specific, trusted corporate IP ranges. Remove or replace '20.0.0.0/8' with tightly scoped, validated subnets. Follow NS-1 by ensuring all allowed IPs are minimal and necessary for business operations.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,7.0,"The 'corpNetIps' variable on line 007 includes the IP range '40.0.0.0/8', which is an extremely broad public IP range. Allowing this in network rules can enable initial access for attackers from any address within this /8, significantly increasing the attack surface and blast radius. This configuration violates network segmentation principles and exposes resources to compromise from a large portion of the public internet.","Restrict the 'corpNetIps' variable to only trusted, minimal, and well-defined corporate IP ranges. Remove or replace '40.0.0.0/8' with specific, validated corporate subnets. Ensure all allowed IP ranges are tightly scoped to reduce exposure and align with NS-1.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,8.0,"The 'corpNetIps' variable on line 008 includes the IP range '********/8', which is a very broad public IP range. Allowing such a large range in network rules can enable initial access for attackers from any address within this /8, greatly increasing the attack surface and blast radius. This configuration undermines network segmentation and exposes resources to compromise from a significant portion of the public internet.","Limit the 'corpNetIps' variable to only necessary, trusted, and specific corporate IP ranges. Remove or replace '********/8' with precise, validated subnets. Ensure all allowed IPs are strictly controlled to minimize exposure, following NS-1 guidance.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,9.0,"The 'corpNetIps' variable on line 009 includes the IP range '********/8', which is a massive public IP block. Allowing this range in network rules can provide initial access to attackers from any address within this /8, drastically increasing the attack surface and potential blast radius. This undermines network segmentation and exposes resources to widespread compromise.","Update the 'corpNetIps' variable to only include specific, trusted corporate IP ranges. Remove or replace '********/8' with tightly scoped, validated subnets. Follow NS-1 by ensuring all allowed IPs are minimal and necessary for business operations.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,10.0,"The 'corpNetIps' variable on line 010 includes the IP range '********/8', which is an extremely broad public IP range. Allowing this in network rules can enable initial access for attackers from any address within this /8, significantly increasing the attack surface and blast radius. This configuration violates network segmentation principles and exposes resources to compromise from a large portion of the public internet.","Restrict the 'corpNetIps' variable to only trusted, minimal, and well-defined corporate IP ranges. Remove or replace '********/8' with specific, validated corporate subnets. Ensure all allowed IP ranges are tightly scoped to reduce exposure and align with NS-1.",,,,ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,11.0,"The 'corpNetIps' variable on line 011 includes the IP range '70.0.0.0/8', which is a very broad public IP range. Allowing such a large range in network rules can enable initial access for attackers from any address within this /8, greatly increasing the attack surface and blast radius. This configuration undermines network segmentation and exposes resources to compromise from a significant portion of the public internet.","Limit the 'corpNetIps' variable to only necessary, trusted, and specific corporate IP ranges. Remove or replace '70.0.0.0/8' with precise, validated subnets. Ensure all allowed IPs are strictly controlled to minimize exposure, following NS-1 guidance.",,,,ai_analysis,,Validated
keyvault.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,28.0,"The 'networkAcls.defaultAction' property is set to 'Allow' for the Key Vault resource. This configuration allows all network traffic by default, including from the public internet, which exposes the Key Vault to potential unauthorized access and data exfiltration. Attackers could exploit this open network policy to access sensitive secrets and keys, increasing the blast radius to all resources protected by this Key Vault.","Set 'networkAcls.defaultAction' to 'Deny' to restrict public network access. Only explicitly allow trusted IP addresses and virtual networks via 'ipRules' and 'virtualNetworkRules'. Example: networkAcls: { defaultAction: 'Deny', ... }",,,,ai_analysis,,Validated
keyvault.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,20.0,"The 'enabledForDeployment' property is set to 'true', which allows Azure virtual machines to retrieve secrets from the Key Vault during deployment. If network access is not tightly restricted (as is the case with 'defaultAction: Allow'), this increases the risk of unauthorized access to secrets by any Azure resource, enabling lateral movement and privilege escalation.","Set 'enabledForDeployment' to 'false' unless absolutely required, and ensure network access is restricted. Example: enabledForDeployment: false",,,,ai_analysis,,Validated
keyvault.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,21.0,"The 'enabledForTemplateDeployment' property is set to 'true', which allows Azure Resource Manager templates to access secrets in the Key Vault. Combined with an open network policy ('defaultAction: Allow'), this increases the risk of unauthorized access and data exfiltration during deployments.","Set 'enabledForTemplateDeployment' to 'false' unless strictly necessary, and always restrict network access. Example: enabledForTemplateDeployment: false",,,,ai_analysis,,Validated
scaleset-networks.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,66.0,"The property 'defaultOutboundAccess: true' is set on the subnet configuration. This enables default outbound internet access for all resources in the subnet, creating a direct attack vector for initial access, lateral movement, and data exfiltration. Without explicit egress controls, any compromised resource can reach the public internet, increasing the blast radius and bypassing network segmentation and deny-by-default principles required by NS-1.","Set 'defaultOutboundAccess' to false or remove it entirely to disable default outbound internet access. Instead, explicitly control outbound traffic using a NAT Gateway and associate a Network Security Group (NSG) with the subnet to enforce deny-by-default and allow only required egress destinations. Reference: NS-1 (Establish network segmentation boundaries).",,,,ai_analysis,,Validated
scaleset-networks.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,62.0,"The subnet property 'privateEndpointNetworkPolicies: ""Enabled""' allows private endpoint network policies, but there is no Network Security Group (NSG) associated with the subnet. Lack of an NSG means there are no explicit inbound or outbound traffic controls, enabling lateral movement and increasing the attack surface for all resources in the subnet.",Associate a Network Security Group (NSG) with the subnet to enforce deny-by-default rules and explicitly allow only required traffic. This will reduce the risk of lateral movement and unauthorized access. Reference: NS-1 (Establish network segmentation boundaries).,,,,ai_analysis,,Validated
server-farms.bicep,IM-3,Identity Management,Manage application identities securely and automatically,MEDIUM,173.0,"The App Service Plan resource 'serverFarms' (Microsoft.Web/serverfarms) defined on line 111 does not specify a managed identity. Without a managed identity, applications running in this App Service cannot securely access Azure resources (such as Key Vault) without embedding credentials or secrets, increasing the risk of credential exposure and enabling attackers to escalate privileges or move laterally if they compromise the app environment.","Add an 'identity' block to the App Service Plan resource to enable a system-assigned managed identity. Example: identity: { type: 'SystemAssigned' }. Then, use this managed identity for secure access to Azure resources instead of hardcoded credentials.",,,,ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,168.0,"The 'defaultAction' property in 'networkAcls' for the 'storageAccountFunc' resource is set to 'Allow' (Line 031). This configuration allows public network access to the storage account, enabling attackers to access storage resources from any network not explicitly denied. This significantly increases the attack surface for initial access, data exfiltration, and lateral movement, as the storage account is not restricted to private endpoints or trusted networks.","Set 'networkAcls.defaultAction' to 'Deny' on Line 031 to block public network access. Only allow access via explicit 'ipRules' or 'virtualNetworkRules'. Additionally, consider enabling private endpoints for the storage account to further restrict access to trusted networks.",,,,ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,29.0,"The 'defaultAction' property in 'networkAcls' for the 'fuzzStorageProperties' object is set to 'Allow' (Line 065). This configuration is used by multiple storage accounts and allows public network access, exposing all associated storage accounts to potential unauthorized access from the internet. Attackers can exploit this to gain initial access, exfiltrate data, or move laterally within the environment.",Set 'networkAcls.defaultAction' to 'Deny' on Line 065 to restrict public network access. Ensure that only trusted 'ipRules' and 'virtualNetworkRules' are permitted. Implement private endpoints for all storage accounts using this configuration to enforce private access.,,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 26,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 7,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T15:14:29.621860,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
