"""
Comprehensive Test Suite for IaC Guardian Template System

This module provides comprehensive testing for template validation, loading,
performance monitoring, and all enhanced features.
"""

import unittest
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from src.utils.template_loader import <PERSON>hanced<PERSON><PERSON><PERSON><PERSON>oa<PERSON>, TemplateLoader
    from src.utils.template_validator import TemplateValidator, ValidationSeverity, ValidationResult
    from src.utils.template_metrics import TemplateMetricsCollector, PerformanceTimer
    from src.utils.template_versioning import TemplateVersionManager, TemplateVersion
    from src.utils.template_hot_reload import TemplateHotReloader
    from src.utils.template_dashboard import TemplateDashboard
    IMPORTS_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Could not import template modules: {e}")
    IMPORTS_AVAILABLE = False

class TestTemplateValidator(unittest.TestCase):
    """Test template validation functionality"""
    
    def setUp(self):
        """Set up test environment"""
        if not IMPORTS_AVAILABLE:
            self.skipTest("Template modules not available")
        
        self.config = {
            'validation': {
                'enabled': True,
                'strict_mode': False,
                'validate_variables': True,
                'forbidden_patterns': ['eval\\(', 'exec\\('],
                'max_template_size_mb': 1
            }
        }
        self.validator = TemplateValidator(self.config['validation'])
    
    def test_validate_html_template(self):
        """Test HTML template validation"""
        valid_html = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Test</title>
</head>
<body>
    <h1>{title}</h1>
    <p>{content}</p>
</body>
</html>"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
            f.write(valid_html)
            f.flush()
            
            result = self.validator.validate_template(Path(f.name), valid_html, 'html')
            
            self.assertIsInstance(result, ValidationResult)
            self.assertTrue(result.is_valid)
            self.assertEqual(result.template_type, 'html')
            
        os.unlink(f.name)
    
    def test_validate_invalid_html(self):
        """Test validation of invalid HTML"""
        invalid_html = """<html>
<head>
    <title>Test
</head>
<body>
    <h1>Unclosed header
    <p>Missing closing tags
</body>"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
            f.write(invalid_html)
            f.flush()
            
            result = self.validator.validate_template(Path(f.name), invalid_html, 'html')
            
            self.assertFalse(result.is_valid)
            self.assertGreater(result.warning_count, 0)
            
        os.unlink(f.name)
    
    def test_validate_css_template(self):
        """Test CSS template validation"""
        valid_css = """.container {
    display: flex;
    background: {primary_color};
}

.header {
    font-size: 2em;
    color: {text_color};
}"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.css', delete=False) as f:
            f.write(valid_css)
            f.flush()
            
            result = self.validator.validate_template(Path(f.name), valid_css, 'css')
            
            self.assertTrue(result.is_valid)
            
        os.unlink(f.name)
    
    def test_security_validation(self):
        """Test security pattern detection"""
        malicious_content = """
<script>
    eval("malicious code");
    exec("dangerous command");
</script>
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
            f.write(malicious_content)
            f.flush()
            
            result = self.validator.validate_template(Path(f.name), malicious_content, 'html')
            
            self.assertFalse(result.is_valid)
            self.assertGreater(result.error_count, 0)
            
            # Check that security issues were detected
            security_errors = [issue for issue in result.issues 
                             if issue.rule == 'security_pattern']
            self.assertGreater(len(security_errors), 0)
            
        os.unlink(f.name)

class TestTemplateLoader(unittest.TestCase):
    """Test template loading functionality"""
    
    def setUp(self):
        """Set up test environment"""
        if not IMPORTS_AVAILABLE:
            self.skipTest("Template modules not available")
        
        # Create temporary templates directory
        self.temp_dir = tempfile.mkdtemp()
        self.templates_dir = Path(self.temp_dir) / "templates"
        self.templates_dir.mkdir(parents=True)
        
        # Create subdirectories
        (self.templates_dir / "html").mkdir()
        (self.templates_dir / "css").mkdir()
        (self.templates_dir / "js").mkdir()
        (self.templates_dir / "prompts" / "system").mkdir(parents=True)
        (self.templates_dir / "config").mkdir()
        
        # Create test templates
        self._create_test_templates()
        
        # Create config
        config = {
            'validation': {'enabled': True},
            'performance': {'monitoring_enabled': True},
            'hot_reloading': {'enabled': False},
            'versioning': {'enabled': True}
        }
        
        self.loader = EnhancedTemplateLoader(self.templates_dir, config)
    
    def _create_test_templates(self):
        """Create test template files"""
        # HTML template
        html_content = """<!DOCTYPE html>
<html>
<head>
    <title>{title}</title>
</head>
<body>
    <h1>{heading}</h1>
    <div>{content}</div>
</body>
</html>"""
        with open(self.templates_dir / "html" / "test.html", 'w') as f:
            f.write(html_content)
        
        # CSS template
        css_content = """.container {
    background: {primary_color};
    color: {text_color};
}"""
        with open(self.templates_dir / "css" / "test.css", 'w') as f:
            f.write(css_content)
        
        # JS template
        js_content = """function init() {
    console.log('{message}');
}"""
        with open(self.templates_dir / "js" / "test.js", 'w') as f:
            f.write(js_content)
        
        # Prompt template
        prompt_content = """You are a {role}. Analyze the following:
{content}
Provide {output_format} response."""
        with open(self.templates_dir / "prompts" / "system" / "test.txt", 'w') as f:
            f.write(prompt_content)
        
        # Version file
        with open(self.templates_dir / "VERSION", 'w') as f:
            f.write("1.0.0")
    
    def test_load_html_template(self):
        """Test HTML template loading"""
        content = self.loader.load_html_template("test.html")
        self.assertIn("<!DOCTYPE html>", content)
        self.assertIn("{title}", content)
        self.assertIn("{content}", content)
    
    def test_load_css_template(self):
        """Test CSS template loading"""
        content = self.loader.load_css_template("test.css")
        self.assertIn(".container", content)
        self.assertIn("{primary_color}", content)
    
    def test_load_js_template(self):
        """Test JavaScript template loading"""
        content = self.loader.load_js_template("test.js")
        self.assertIn("function init()", content)
        self.assertIn("{message}", content)
    
    def test_load_prompt_template(self):
        """Test prompt template loading"""
        content = self.loader.load_prompt_template("system", "test.txt")
        self.assertIn("You are a {role}", content)
        self.assertIn("{content}", content)
    
    def test_template_caching(self):
        """Test template caching functionality"""
        # Load template twice
        content1 = self.loader.load_html_template("test.html")
        content2 = self.loader.load_html_template("test.html")
        
        # Should be identical (from cache)
        self.assertEqual(content1, content2)
        
        # Check cache info
        cache_info = self.loader.get_cache_info()
        self.assertGreater(cache_info['cached_templates'], 0)
    
    def test_template_variable_substitution(self):
        """Test template variable substitution"""
        template = "Hello {name}, welcome to {app}!"
        variables = {"name": "John", "app": "IaC Guardian"}
        
        result = self.loader._apply_template_variables(template, variables)
        self.assertEqual(result, "Hello John, welcome to IaC Guardian!")
    
    def test_missing_template_handling(self):
        """Test handling of missing templates"""
        content = self.loader.load_html_template("nonexistent.html")
        self.assertEqual(content, "")
    
    def tearDown(self):
        """Clean up test environment"""
        import shutil
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)

class TestTemplateMetrics(unittest.TestCase):
    """Test template performance metrics"""
    
    def setUp(self):
        """Set up test environment"""
        if not IMPORTS_AVAILABLE:
            self.skipTest("Template modules not available")
        
        self.config = {
            'monitoring_enabled': True,
            'slow_load_threshold_ms': 100,
            'metrics_file': 'test_metrics.json'
        }
        self.metrics = TemplateMetricsCollector(self.config)
    
    def test_record_metric(self):
        """Test metric recording"""
        self.metrics.record_metric(
            template_path="test.html",
            operation="load",
            duration_ms=50.0,
            success=True,
            template_size_bytes=1024
        )
        
        stats = self.metrics.get_stats()
        self.assertEqual(stats.total_operations, 1)
        self.assertEqual(stats.avg_duration_ms, 50.0)
        self.assertEqual(stats.success_rate, 100.0)
    
    def test_performance_timer(self):
        """Test performance timer context manager"""
        with PerformanceTimer(self.metrics, "test.html", "load") as timer:
            # Simulate some work
            import time
            time.sleep(0.01)  # 10ms
        
        stats = self.metrics.get_stats()
        self.assertEqual(stats.total_operations, 1)
        self.assertGreater(stats.avg_duration_ms, 5)  # Should be > 5ms
    
    def test_slow_operation_detection(self):
        """Test slow operation detection"""
        # Record a slow operation
        self.metrics.record_metric(
            template_path="slow.html",
            operation="load",
            duration_ms=200.0,  # Above threshold
            success=True
        )
        
        slow_ops = self.metrics.get_slow_operations(limit=5)
        self.assertEqual(len(slow_ops), 1)
        self.assertEqual(slow_ops[0].duration_ms, 200.0)

class TestTemplateVersioning(unittest.TestCase):
    """Test template versioning functionality"""
    
    def setUp(self):
        """Set up test environment"""
        if not IMPORTS_AVAILABLE:
            self.skipTest("Template modules not available")
        
        self.temp_dir = tempfile.mkdtemp()
        self.templates_dir = Path(self.temp_dir)
        
        # Create version file
        with open(self.templates_dir / "VERSION", 'w') as f:
            f.write("1.2.3")
        
        config = {
            'enabled': True,
            'version_file': 'VERSION',
            'min_supported_version': '1.0.0'
        }
        
        self.version_manager = TemplateVersionManager(self.templates_dir, config)
    
    def test_version_parsing(self):
        """Test version string parsing"""
        version = TemplateVersion.from_string("1.2.3")
        self.assertEqual(version.major, 1)
        self.assertEqual(version.minor, 2)
        self.assertEqual(version.patch, 3)
        self.assertEqual(str(version), "1.2.3")
    
    def test_version_comparison(self):
        """Test version comparison"""
        v1 = TemplateVersion.from_string("1.0.0")
        v2 = TemplateVersion.from_string("1.1.0")
        v3 = TemplateVersion.from_string("2.0.0")
        
        self.assertTrue(v1 < v2)
        self.assertTrue(v2 < v3)
        self.assertTrue(v1 < v3)
        self.assertFalse(v2 < v1)
    
    def test_compatibility_check(self):
        """Test version compatibility checking"""
        # Should be compatible (current is 1.2.3)
        compatible, messages = self.version_manager.check_compatibility("1.1.0")
        self.assertTrue(compatible)
        
        # Should be incompatible (requiring newer version)
        compatible, messages = self.version_manager.check_compatibility("2.0.0")
        self.assertFalse(compatible)
        self.assertGreater(len(messages), 0)
    
    def test_version_extraction(self):
        """Test version extraction from template content"""
        content_with_version = """<!-- Template Version: 1.5.0 -->
<!DOCTYPE html>
<html>
<head><title>Test</title></head>
</html>"""
        
        version = self.version_manager.extract_template_version(content_with_version)
        self.assertIsNotNone(version)
        self.assertEqual(str(version), "1.5.0")
    
    def tearDown(self):
        """Clean up test environment"""
        import shutil
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)

class TestTemplateIntegration(unittest.TestCase):
    """Test complete template system integration"""
    
    def setUp(self):
        """Set up integration test environment"""
        if not IMPORTS_AVAILABLE:
            self.skipTest("Template modules not available")
        
        # Create temporary templates directory with full structure
        self.temp_dir = tempfile.mkdtemp()
        self.templates_dir = Path(self.temp_dir) / "templates"
        
        # Create directory structure
        dirs = [
            "html", "css", "js", "prompts/system", "prompts/security", 
            "prompts/context", "config", "output"
        ]
        for dir_path in dirs:
            (self.templates_dir / dir_path).mkdir(parents=True)
        
        self._create_full_template_set()
        
        # Create comprehensive config
        config = {
            'validation': {
                'enabled': True,
                'strict_mode': False,
                'validate_variables': True
            },
            'performance': {
                'monitoring_enabled': True,
                'slow_load_threshold_ms': 100
            },
            'versioning': {
                'enabled': True,
                'version_file': 'VERSION'
            },
            'hot_reloading': {
                'enabled': False  # Disabled for testing
            }
        }
        
        self.loader = EnhancedTemplateLoader(self.templates_dir, config)
    
    def _create_full_template_set(self):
        """Create a complete set of templates for testing"""
        # Base HTML template
        base_html = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{title}</title>
    <link rel="stylesheet" href="../css/glass-ui.css">
</head>
<body>
    <div class="container">
        <h1>{heading}</h1>
        <div class="content">{content}</div>
    </div>
    <script src="../js/report.js"></script>
</body>
</html>"""
        with open(self.templates_dir / "html" / "base.html", 'w') as f:
            f.write(base_html)
        
        # Glass UI CSS
        glass_css = """.container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 10px;
    padding: 20px;
}"""
        with open(self.templates_dir / "css" / "glass-ui.css", 'w') as f:
            f.write(glass_css)
        
        # Report JavaScript
        report_js = """function initReport() {
    console.log('Report initialized');
}"""
        with open(self.templates_dir / "js" / "report.js", 'w') as f:
            f.write(report_js)
        
        # Security analysis prompt
        security_prompt = """Analyze the following template for security issues:
{numbered_content}

Apply these controls:
{controls_section}

Return findings in JSON format."""
        with open(self.templates_dir / "prompts" / "security" / "main_analysis_prompt.txt", 'w') as f:
            f.write(security_prompt)
        
        # Version file
        with open(self.templates_dir / "VERSION", 'w') as f:
            f.write("1.0.0")
        
        # Config file
        config_data = {
            "template_system": {
                "validation": {"enabled": True},
                "performance": {"monitoring_enabled": True}
            }
        }
        with open(self.templates_dir / "config" / "template_config.json", 'w') as f:
            json.dump(config_data, f, indent=2)
    
    def test_complete_html_report_generation(self):
        """Test complete HTML report generation"""
        template_vars = {
            'title': 'Security Report',
            'heading': 'IaC Guardian Analysis',
            'content': 'Test findings content',
            'timestamp': '2024-01-01T00:00:00'
        }
        
        html_report = self.loader.load_complete_html_report(template_vars)
        
        # Verify HTML structure
        self.assertIn('<!DOCTYPE html>', html_report)
        self.assertIn('Security Report', html_report)
        self.assertIn('IaC Guardian Analysis', html_report)
        
        # Verify CSS is embedded
        self.assertIn('backdrop-filter: blur(10px)', html_report)
        
        # Verify JS is embedded
        self.assertIn('function initReport()', html_report)
        
        # Verify no external references remain
        self.assertNotIn('href="../css/glass-ui.css"', html_report)
        self.assertNotIn('src="../js/report.js"', html_report)
    
    def test_security_analysis_prompt_generation(self):
        """Test security analysis prompt generation"""
        template_vars = {
            'numbered_content': 'Line 1: resource "test" {\n  name = "example"\n}',
            'controls_section': 'Control NS-1: Network Security',
            'total_controls': 5
        }
        
        prompt = self.loader.load_security_analysis_prompt(template_vars)
        
        self.assertIn('Analyze the following template', prompt)
        self.assertIn('Line 1: resource "test"', prompt)
        self.assertIn('Control NS-1', prompt)
    
    def test_system_status_reporting(self):
        """Test comprehensive system status"""
        status = self.loader.get_system_status()
        
        self.assertEqual(status['template_loader'], 'active')
        self.assertTrue(status['templates_directory_exists'])
        self.assertTrue(status['enhanced_features'])
        self.assertTrue(status['validator_enabled'])
        self.assertTrue(status['metrics_collector_enabled'])
        self.assertTrue(status['version_manager_enabled'])
    
    def test_template_validation_suite(self):
        """Test validation of all templates"""
        validation_results = self.loader.validate_all_templates()
        
        self.assertGreater(validation_results['total_templates'], 0)
        self.assertGreaterEqual(validation_results['valid_templates'], 0)
        self.assertIsInstance(validation_results['validation_results'], list)
    
    def tearDown(self):
        """Clean up integration test environment"""
        import shutil
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)

if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestTemplateValidator,
        TestTemplateLoader,
        TestTemplateMetrics,
        TestTemplateVersioning,
        TestTemplateIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\n{'='*60}")
    print(f"Template System Test Results")
    print(f"{'='*60}")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print(f"\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print(f"\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
