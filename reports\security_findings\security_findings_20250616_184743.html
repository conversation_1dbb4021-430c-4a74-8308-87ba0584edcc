
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Security Findings Report - IaC Guardian</title>
                <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
                <style>
                    :root {
                        --primary-color: #2c3e50;
                        --secondary-color: #3498db;
                        --success-color: #27ae60;
                        --warning-color: #f39c12;
                        --danger-color: #e74c3c;
                        --info-color: #17a2b8;
                        --light-bg: #f8f9fa;
                        --dark-bg: #343a40;
                        --border-color: #dee2e6;
                        --text-color: #495057;
                        --shadow: 0 2px 4px rgba(0,0,0,0.1);
                        --border-radius: 8px;
                    }

                    * {
                        margin: 0;
                        padding: 0;
                        box-sizing: border-box;
                    }

                    body {
                        font-family: 'Se<PERSON>e <PERSON>', Tahoma, Geneva, Verdana, sans-serif;
                        line-height: 1.6;
                        color: var(--text-color);
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        min-height: 100vh;
                    }

                    .container {
                        max-width: 1200px;
                        margin: 0 auto;
                        padding: 20px;
                    }

                    .header {
                        background: white;
                        border-radius: var(--border-radius);
                        padding: 30px;
                        margin-bottom: 30px;
                        box-shadow: var(--shadow);
                        text-align: center;
                    }

                    .header h1 {
                        color: var(--primary-color);
                        font-size: 2.5rem;
                        margin-bottom: 10px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 15px;
                    }

                    .header .subtitle {
                        color: var(--text-color);
                        font-size: 1.1rem;
                        opacity: 0.8;
                    }

                    .controls {
                        background: white;
                        border-radius: var(--border-radius);
                        padding: 20px;
                        margin-bottom: 30px;
                        box-shadow: var(--shadow);
                        display: flex;
                        flex-wrap: wrap;
                        gap: 15px;
                        align-items: center;
                    }

                    .search-box {
                        flex: 1;
                        min-width: 250px;
                        position: relative;
                    }

                    .search-box input {
                        width: 100%;
                        padding: 12px 45px 12px 15px;
                        border: 2px solid var(--border-color);
                        border-radius: var(--border-radius);
                        font-size: 14px;
                        transition: border-color 0.3s;
                    }

                    .search-box input:focus {
                        outline: none;
                        border-color: var(--secondary-color);
                    }

                    .search-box i {
                        position: absolute;
                        right: 15px;
                        top: 50%;
                        transform: translateY(-50%);
                        color: var(--text-color);
                        opacity: 0.5;
                    }

                    .filter-buttons {
                        display: flex;
                        gap: 10px;
                        flex-wrap: wrap;
                    }

                    .filter-btn {
                        padding: 8px 16px;
                        border: 2px solid;
                        border-radius: 20px;
                        background: white;
                        cursor: pointer;
                        transition: all 0.3s;
                        font-size: 12px;
                        font-weight: 600;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                    }

                    .filter-btn.active {
                        color: white !important;
                    }

                    .filter-btn.critical {
                        border-color: var(--danger-color);
                        color: var(--danger-color);
                    }

                    .filter-btn.critical.active {
                        background: var(--danger-color);
                    }

                    .filter-btn.high {
                        border-color: var(--warning-color);
                        color: var(--warning-color);
                    }

                    .filter-btn.high.active {
                        background: var(--warning-color);
                    }

                    .filter-btn.medium {
                        border-color: #ffc107;
                        color: #856404;
                    }

                    .filter-btn.medium.active {
                        background: #ffc107;
                        color: #856404;
                    }

                    .filter-btn.low {
                        border-color: var(--info-color);
                        color: var(--info-color);
                    }

                    .filter-btn.low.active {
                        background: var(--info-color);
                    }

                    .filter-btn.all {
                        border-color: var(--secondary-color);
                        color: var(--secondary-color);
                    }

                    .filter-btn.all.active {
                        background: var(--secondary-color);
                    }

                    .summary {
                        background: white;
                        border-radius: var(--border-radius);
                        padding: 30px;
                        margin-bottom: 30px;
                        box-shadow: var(--shadow);
                    }

                    .summary h2 {
                        color: var(--primary-color);
                        margin-bottom: 20px;
                        font-size: 1.5rem;
                    }

                    .stats-grid {
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                        gap: 20px;
                        margin-bottom: 20px;
                    }

                    .stat-card {
                        background: var(--light-bg);
                        padding: 20px;
                        border-radius: var(--border-radius);
                        text-align: center;
                        border-left: 4px solid var(--secondary-color);
                    }

                    .stat-number {
                        font-size: 2rem;
                        font-weight: bold;
                        color: var(--primary-color);
                    }

                    .stat-label {
                        color: var(--text-color);
                        font-size: 0.9rem;
                        margin-top: 5px;
                    }

                    .severity-breakdown {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 15px;
                        margin-top: 20px;
                    }

                    .severity-stat {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        padding: 10px 15px;
                        border-radius: 20px;
                        background: var(--light-bg);
                        font-weight: 600;
                    }

                    .severity-stat.critical {
                        background: rgba(231, 76, 60, 0.1);
                        color: var(--danger-color);
                    }

                    .severity-stat.high {
                        background: rgba(243, 156, 18, 0.1);
                        color: var(--warning-color);
                    }

                    .severity-stat.medium {
                        background: rgba(255, 193, 7, 0.1);
                        color: #856404;
                    }

                    .severity-stat.low {
                        background: rgba(23, 162, 184, 0.1);
                        color: var(--info-color);
                    }

                    .findings-container {
                        background: white;
                        border-radius: var(--border-radius);
                        box-shadow: var(--shadow);
                        overflow: hidden;
                    }

                    .severity-group {
                        margin-bottom: 0;
                    }

                    .severity-header {
                        padding: 20px 30px;
                        color: white;
                        font-weight: bold;
                        font-size: 1.2rem;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        cursor: pointer;
                        transition: all 0.3s;
                        position: relative;
                    }

                    .severity-header:hover {
                        opacity: 0.9;
                    }

                    .severity-header .toggle-icon {
                        margin-left: auto;
                        transition: transform 0.3s;
                    }

                    .severity-header.collapsed .toggle-icon {
                        transform: rotate(-90deg);
                    }

                    .severity-header .count {
                        background: rgba(255, 255, 255, 0.2);
                        padding: 4px 12px;
                        border-radius: 15px;
                        font-size: 0.9rem;
                    }

                    .critical {
                        background: linear-gradient(135deg, #e74c3c, #c0392b);
                    }

                    .high {
                        background: linear-gradient(135deg, #f39c12, #e67e22);
                    }

                    .medium {
                        background: linear-gradient(135deg, #ffc107, #f39c12);
                        color: #856404 !important;
                    }

                    .low {
                        background: linear-gradient(135deg, #17a2b8, #138496);
                    }

                    .findings-list {
                        max-height: 600px;
                        overflow-y: auto;
                        transition: max-height 0.3s ease-out;
                    }

                    .findings-list.collapsed {
                        max-height: 0;
                        overflow: hidden;
                    }

                    .finding {
                        border-bottom: 1px solid var(--border-color);
                        padding: 25px 30px;
                        transition: background-color 0.3s;
                        position: relative;
                    }

                    .finding:last-child {
                        border-bottom: none;
                    }

                    .finding:hover {
                        background: var(--light-bg);
                    }

                    .finding-header {
                        display: flex;
                        align-items: flex-start;
                        gap: 15px;
                        margin-bottom: 15px;
                    }

                    .finding-icon {
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: white;
                        font-size: 1.2rem;
                        flex-shrink: 0;
                    }

                    .finding-icon.critical {
                        background: var(--danger-color);
                    }

                    .finding-icon.high {
                        background: var(--warning-color);
                    }

                    .finding-icon.medium {
                        background: #ffc107;
                        color: #856404;
                    }

                    .finding-icon.low {
                        background: var(--info-color);
                    }

                    .finding-content {
                        flex: 1;
                    }

                    .finding-title {
                        font-size: 1.1rem;
                        font-weight: 600;
                        color: var(--primary-color);
                        margin-bottom: 8px;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                    }

                    .control-badge {
                        background: var(--secondary-color);
                        color: white;
                        padding: 2px 8px;
                        border-radius: 12px;
                        font-size: 0.8rem;
                        font-weight: 500;
                    }

                    .finding-meta {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 15px;
                        margin-bottom: 15px;
                        font-size: 0.9rem;
                        color: var(--text-color);
                    }

                    .meta-item {
                        display: flex;
                        align-items: center;
                        gap: 5px;
                    }

                    .meta-item i {
                        color: var(--secondary-color);
                    }

                    .finding-description {
                        margin-bottom: 15px;
                        line-height: 1.6;
                    }

                    .finding-remediation {
                        background: var(--light-bg);
                        padding: 15px;
                        border-radius: var(--border-radius);
                        border-left: 4px solid var(--success-color);
                    }

                    .remediation-title {
                        font-weight: 600;
                        color: var(--success-color);
                        margin-bottom: 8px;
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    }

                    .code-snippet {
                        background: #2d3748;
                        color: #e2e8f0;
                        padding: 15px;
                        border-radius: var(--border-radius);
                        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                        font-size: 0.9rem;
                        line-height: 1.4;
                        white-space: pre-wrap;
                        margin: 15px 0;
                        overflow-x: auto;
                        border: 1px solid #4a5568;
                    }

                    .no-findings {
                        text-align: center;
                        padding: 60px 30px;
                        color: var(--text-color);
                    }

                    .no-findings i {
                        font-size: 3rem;
                        color: var(--border-color);
                        margin-bottom: 20px;
                    }

                    .footer {
                        background: white;
                        border-radius: var(--border-radius);
                        padding: 20px 30px;
                        margin-top: 30px;
                        box-shadow: var(--shadow);
                        text-align: center;
                        color: var(--text-color);
                        font-size: 0.9rem;
                    }

                    .export-buttons {
                        display: flex;
                        gap: 10px;
                        justify-content: center;
                        margin-bottom: 15px;
                    }

                    .export-btn {
                        padding: 8px 16px;
                        border: none;
                        border-radius: var(--border-radius);
                        background: var(--secondary-color);
                        color: white;
                        cursor: pointer;
                        transition: background-color 0.3s;
                        font-size: 0.9rem;
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    }

                    .export-btn:hover {
                        background: #2980b9;
                    }

                    @media (max-width: 768px) {
                        .container {
                            padding: 10px;
                        }

                        .header h1 {
                            font-size: 2rem;
                            flex-direction: column;
                            gap: 10px;
                        }

                        .controls {
                            flex-direction: column;
                            align-items: stretch;
                        }

                        .filter-buttons {
                            justify-content: center;
                        }

                        .stats-grid {
                            grid-template-columns: 1fr;
                        }

                        .severity-breakdown {
                            justify-content: center;
                        }

                        .finding {
                            padding: 20px 15px;
                        }

                        .finding-header {
                            flex-direction: column;
                            align-items: flex-start;
                        }

                        .finding-meta {
                            flex-direction: column;
                            gap: 8px;
                        }
                    }

                    @media print {
                        body {
                            background: white;
                        }

                        .container {
                            max-width: none;
                            padding: 0;
                        }

                        .controls, .export-buttons {
                            display: none;
                        }

                        .findings-list {
                            max-height: none !important;
                        }

                        .finding {
                            break-inside: avoid;
                            page-break-inside: avoid;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>
                            <i class="fas fa-shield-alt"></i>
                            Security Findings Report
                        </h1>
                        <div class="subtitle">Infrastructure as Code Security Analysis</div>
                    </div>

                    <div class="controls">
                        <div class="search-box">
                            <input type="text" id="searchInput" placeholder="Search findings by description, file, or control ID...">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="filter-buttons">
                            <button class="filter-btn all active" data-severity="all">
                                <i class="fas fa-list"></i> All
                            </button>
                            <button class="filter-btn critical" data-severity="critical">
                                🔴 Critical
                            </button>
                            <button class="filter-btn high" data-severity="high">
                                🟠 High
                            </button>
                            <button class="filter-btn medium" data-severity="medium">
                                🟡 Medium
                            </button>
                            <button class="filter-btn low" data-severity="low">
                                🔵 Low
                            </button>
                        </div>
                    </div>

                    
                <div class="summary">
                    <h2><i class="fas fa-chart-bar"></i> Executive Summary</h2>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">61</div>
                            <div class="stat-label">Total Findings</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">14</div>
                            <div class="stat-label">Files Affected</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">34</div>
                            <div class="stat-label">High Priority Issues</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">17</div>
                            <div class="stat-label">Security Controls</div>
                        </div>
                    </div>
                    <div class="severity-breakdown">
                        <div class="severity-stat critical">
                            <span>🔴</span>
                            <span><strong>4</strong> Critical</span>
                        </div>
                        <div class="severity-stat high">
                            <span>🟠</span>
                            <span><strong>30</strong> High</span>
                        </div>
                        <div class="severity-stat medium">
                            <span>🟡</span>
                            <span><strong>22</strong> Medium</span>
                        </div>
                        <div class="severity-stat low">
                            <span>🔵</span>
                            <span><strong>5</strong> Low</span>
                        </div>
                    </div>
                </div>

                    <div class="findings-container">
                        
                    <div class="severity-group" data-severity="critical">
                        <div class="severity-header critical">
                            <span>🔴 CRITICAL Severity Findings</span>
                            <span class="count">4</span>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                        <div class="findings-list">
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon critical">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">DP-3</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: function-settings.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 66</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The Application Insights instrumentation key (&#x27;app_insights_key&#x27;) is being set as an app setting in plain text, albeit as a @secure() parameter. According to ASB DP-3, sensitive secrets such as instrumentation keys should be stored in Azure Key Vault and referenced securely, not included inline in app settings.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Store the Application Insights instrumentation key in Azure Key Vault and reference it using a Key Vault reference in the App Service app settings.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon critical">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-1</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: keyvault.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 18</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The Key Vault &#x27;networkAcls&#x27; configuration sets &#x27;defaultAction&#x27; to &#x27;Allow&#x27;, which makes the vault accessible from any public IP unless explicitly denied. This violates network security best practices for restricting sensitive resources to only trusted networks.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Set &#x27;defaultAction&#x27; to &#x27;Deny&#x27; in the Key Vault &#x27;networkAcls&#x27; configuration. Explicitly permit access only from approved networks, IP addresses, or Azure services that require it.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon critical">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-2</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: keyvault.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 18</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The Key Vault&#x27;s public endpoint is exposed due to &#x27;defaultAction&#x27; being &#x27;Allow&#x27;, enabling access over the Internet. This increases risk of unauthorized access/exploitation.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Configure &#x27;networkAcls.defaultAction&#x27; to &#x27;Deny&#x27;, use &#x27;ipRules&#x27; and &#x27;virtualNetworkRules&#x27; to restrict access, and consider disabling the public endpoint if not needed.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon critical">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-2</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: storage-accounts.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 22</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The &#x27;networkAcls&#x27; configuration for all storage accounts sets &#x27;defaultAction&#x27; to &#x27;Allow&#x27;. This permits access from all public networks except for explicitly denied addresses, exposing the storage account to the internet or unintended sources. This violates the control requiring the minimization of public endpoint exposure.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Set &#x27;networkAcls.defaultAction&#x27; to &#x27;Deny&#x27; to ensure that only explicitly allowed IP ranges and subnets can access the storage account. Review and restrict &#x27;bypass&#x27; settings as well.
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="severity-group" data-severity="high">
                        <div class="severity-header high">
                            <span>🟠 HIGH Severity Findings</span>
                            <span class="count">30</span>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                        <div class="findings-list">
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-5</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: app-config.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 7</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The Azure App Configuration instance is not configured with a private endpoint, leaving the service accessible over public internet by default. This increases the risk of unauthorized or malicious access.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Configure a private endpoint (Microsoft.Network/privateEndpoints) for the App Configuration resource. This will ensure traffic to the resource remains on the Azure backbone and is accessible only within your virtual network.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-2</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: app-config.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 7</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> There is no restriction or secured access on the public endpoint of Azure App Configuration. Without IP restrictions or firewall configuration, the endpoint is publicly accessible, violating best practices for minimizing exposure.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Restrict public access by enabling public network access to &#x27;Disabled&#x27;, using firewall rules to allow only trusted sources, or enforce private endpoint use.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">DP-3</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: app-config.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 15</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> App Configuration key-value pairs (&#x27;keyValues&#x27;) may contain sensitive configuration data, but the template permits direct inline assignment of secrets or sensitive values. This risks accidental disclosure if secrets are hardcoded or improperly referenced.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Ensure that sensitive values (such as secrets, keys, or connection strings) are stored in Azure Key Vault and referenced securely via Key Vault references rather than being set directly or hardcoded in parameter files or templates.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-2</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: autoscale-settings.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 1</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The template references a storage account (func_storage_account_id) and possibly other Azure resources (server_farm_id) by resource ID, but there are no controls in place to restrict public endpoint exposure. Storage Accounts, by default, expose public endpoints, which can increase the risk of unauthorized access or data leaks.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Verify that the referenced storage account and other service resources deny public network access unless specifically required. For Storage Accounts, configure the &#x27;networkAcls&#x27; property to allow only trusted subnets or private endpoints.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-5</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: event-grid.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 19</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The Event Grid System Topics are configured to receive events from storage accounts, but there is no evidence of private endpoints being used for the associated storage account(s) or Event Grid topic(s). This increases the risk of exposure to public network traffic.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Configure Azure Private Endpoints for all associated storage accounts and, if possible, for Event Grid endpoints. Ensure access to these resources is only possible from authorized virtual networks.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-1</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: event-grid.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 19</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> There is no evidence that network security measures such as Network Security Groups (NSGs) or Azure Firewall are implemented to restrict access to the underlying storage accounts or Event Grid endpoints. Without these protections, services could be exposed to unrestricted network traffic.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Apply NSGs to restrict inbound and outbound access to the storage accounts referenced by &#x27;storageFuzzId&#x27;, &#x27;storageCorpusIds&#x27;, and monitor for access anomalies. Consider using Azure Firewall for additional network layer protection.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-2</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: event-grid.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 19</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The template does not show any configuration to restrict or protect public endpoints for the storage accounts or Event Grid system topics. Unsecured public endpoints may allow unauthorized access.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Ensure that public network access is disabled or restricted for the storage accounts and Event Grid endpoints, and implement access controls and private endpoints to minimize exposure.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-1</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: function-settings.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 40</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The Storage Account resource &#x27;funcStorage&#x27; is defined as &#x27;existing&#x27;, but there is no evidence in this file of any network security measures such as Network Security Groups (NSGs) or Firewall rules protecting the storage account, as required by ASB NS-1.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Ensure that the referenced storage account is protected with NSGs and/or Azure Firewall by restricting access to only required networks and clients. Consider adding private endpoint configuration or explicitly setting network rules.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-2</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: function-settings.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 40</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The Storage Account resource &#x27;funcStorage&#x27; does not have any indication of public endpoint restrictions or private endpoints. By default, storage accounts have public endpoints enabled, risking exposure to the internet.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Update the storage account configuration to disable public network access or configure a Private Endpoint to restrict access to only trusted networks.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-5</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: function-settings.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 40</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The Storage Account resource &#x27;funcStorage&#x27; is not configured with a Private Endpoint, nor are there settings present to enforce access over private networks. This leaves the resource potentially accessible over public networks.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Implement an Azure Private Endpoint for the storage account to ensure all access is performed securely over private networks only.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-1</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: function.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 85</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The storage account resource &#x27;funcStorage&#x27; is referenced as &#x27;existing&#x27;, but there is no evidence in this template that it is protected by a Network Security Group (NSG), Azure Firewall, nor does it use Private Endpoints. This leaves the storage account potentially exposed to public network access, violating the requirement to protect sensitive resources.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Ensure that the referenced storage account restricts public network access using NSGs, Azure Firewall, or private endpoints. If not managed within this template, verify configurations in the referenced resource.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-2</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: function.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 85</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The referenced storage account &#x27;funcStorage&#x27; may have a publicly accessible endpoint, as there is no configuration to disable public network access or use private endpoints. Application logs are uploaded using a SAS token to a potentially public endpoint, increasing risk of unauthorized access.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Configure the storage account to deny public network access and require access via private endpoints. Regenerate or rotate SAS tokens if exposure is suspected.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">DP-3</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: function.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 109</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> A shared access signature (SAS) token is generated programmatically and inlined into the application logging configuration (&#x27;sasUrl&#x27;). This exposes sensitive SAS tokens within the deployment, increasing the risk of secret leakage and unauthorized access to storage data.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Avoid inlining SAS tokens within code or IaC. Store secrets in Azure Key Vault and reference them securely. Also, use managed identity and RBAC for storage access where possible.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-5</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: function.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 85</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The storage account used for logging does not use private endpoints in the context of this deployment. Using SAS tokens to write logs to public storage endpoints increases exposure to data exfiltration risks.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Integrate the storage account with Azure Private Endpoint to restrict access to trusted networks only. Update the function to use the private endpoint URL for uploads.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-1</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: hub-network.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 2</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The virtual network &#x27;hub-vnet&#x27; and its subnet &#x27;hub-subnet&#x27; are defined without any associated Network Security Group (NSG) or Azure Firewall, leaving all network traffic unfiltered at the subnet level. This exposes connected resources to potential unauthorized network access and traffic, violating network protection standards.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Associate an appropriate NSG to the &#x27;hub-subnet&#x27; to define and enforce allowed inbound and outbound traffic. Where appropriate, consider deploying an Azure Firewall or a similar firewall solution to centrally control and monitor network traffic.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-3</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: hub-network.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 2</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> No Network Security Group (NSG) is applied to the &#x27;hub-subnet&#x27;, meaning there are no access control rules restricting network access to resources deployed in this subnet.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Create an NSG with least privilege rules and associate it with the &#x27;hub-subnet&#x27; to ensure that only approved traffic is allowed into and out of the subnet.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">DP-3</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: instance-config.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 10</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The parameter &#x27;specificConfig&#x27; and its subfields, such as &#x27;Admins&#x27;, &#x27;cli_client_id&#x27;, and possible others, are ingested directly from external JSON files and handled in clear text in both variables and output objects. Keys, secrets, administrative identifiers, or sensitive configuration parameters may be exposed in source or output, rather than being stored in Azure Key Vault as per ASB DP-3.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Ensure that any sensitive values or secrets referenced in the external JSON files or Bicep parameters (such as client IDs, admin usernames, or any credentials) are instead stored securely in Azure Key Vault, and use Key Vault references or secret pull at deploy time. Do not expose sensitive data as plain parameters or outputs in storage or logs.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-1</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: instance-config.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 23</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The network configuration directly defines address spaces and subnets (address_space: &#x27;10.0.0.0/8&#x27;, subnet: &#x27;10.0.0.0/16&#x27;), but does not reference, create, or enforce any Network Security Groups (NSGs), firewalls, or other network-level controls on these subnets/resources. ASB NS-1 requires protecting resources using NSGs or Azure Firewall to restrict network access.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Explicitly define and associate NSGs with relevant subnets and/or compute resources to control inbound and outbound traffic per the principle of least privilege. Define firewall rules as appropriate to segment and secure workloads in these networks.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-3</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: instance-config.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 23</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> There are no references to Network Security Groups (NSGs) or implementation of network filtering on the defined subnets or resources. ASB NS-3 recommends the use of NSGs to restrict both inbound and outbound traffic to and from VMs/subnets.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Create and attach NSGs to all subnets and/or VM network interfaces with rules limiting traffic to only what is strictly required (administration, application communication) and deny everything else by default.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-2</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: ip-rules.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 3</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The &#x27;corpNetIps&#x27; variable grants &#x27;Allow&#x27; access to very large public IP ranges (e.g., &#x27;*******/8&#x27;, &#x27;********/8&#x27;), which encompass millions of potentially untrusted addresses. Allowing access from such broad public IP ranges exposes resources to a wide attack surface and does not properly restrict public endpoint access.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Restrict allowed IP addresses to specific, trusted public IPs or ranges that are actually required for business needs. Avoid using broad ranges like /8. Where possible, use private network connections (VPN, ExpressRoute, Private Endpoints) and lock down public endpoints to only need-to-access addresses.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">AM-1</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: keyvault.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 29</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> No access policies (accessPolicies is empty) are defined for the Key Vault, and RBAC authorization is enabled. Without assignment of least-privilege roles, users or services may end up with inappropriate permissions.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Define and assign least-privilege RBAC roles to users/applications that require access to the Key Vault, limiting permissions to the minimum necessary. Regularly review role assignments to remove excess privileges.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">DP-3</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: operational-insights.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 70</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The output &#x27;appInsightsInstrumentationKey&#x27; exposes the Application Insights instrumentation key, which is a sensitive credential that could allow unauthorized data ingestion or leakage if accessed by untrusted parties.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Do not output sensitive values like instrumentation keys. Store secrets and sensitive keys in Azure Key Vault and access them securely from trusted applications only.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">DP-3</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: operational-insights.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 69</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The output &#x27;appInsightsAppId&#x27; exposes the Application Insights App ID. While this is less sensitive than the instrumentation key, it can still assist attackers in identifying targets or performing information gathering if outputs are broadly accessible.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Restrict the exposure of identifiers such as App IDs to only trusted users and systems. Avoid outputting such values unless necessary and ensure output scopes are limited.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-1</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: scaleset-networks.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 1</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> No Network Security Group (NSG) is associated with the scaleset subnet or any part of the network. Without NSGs, critical resources such as VMs in the subnet are unprotected from unauthorized or malicious network access.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Deploy and associate a Network Security Group to the &#x27;scaleset&#x27; subnet to restrict and control inbound and outbound network traffic according to least privilege.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-3</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: scaleset-networks.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 1</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> Network Security Groups (NSGs) are not defined or applied to any subnet in this template, leaving the virtual network and its resources exposed to unrestricted network communication.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Define and attach NSGs to restrict inbound and outbound access to only required ports and protocols for each subnet, especially the scaleset subnet.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-1</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: server-farms.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 1</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> No network security measures (NSGs or Azure Firewall) are defined for the App Service or referenced Key Vaults. App Service plans by default allow public network access, which may expose resources to external threats if not properly restricted.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Use service endpoints, access restrictions for the App Service, or deploy within a subnet protected by NSGs or an Azure Firewall. For Key Vaults, ensure network ACLs restrict access to only required addresses or subnets.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-2</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: server-farms.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 1</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> App Service and referenced Key Vaults are deployed without explicit restrictions on public endpoints, potentially exposing them to the public internet if default settings remain.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Implement App Service Access Restrictions to only allow trusted IPs or vNET integration, and configure Key Vault firewalls to only allow required access.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-2</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: signalR.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 4</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The Azure SignalR Service is deployed without specifying any network ACLs or private endpoint configuration. By default, SignalR Service exposes a public endpoint, which increases the risk of unauthorized access and exposure to the internet.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Restrict public network access by configuring the SignalR resource with private endpoint connectivity or service endpoints. Use `networkACLs` property to limit allowed IP address ranges or set to &#x27;Private&#x27; network mode where possible. Reference: https://learn.microsoft.com/en-us/azure/azure-signalr/signalr-overview-security#restrict-public-network-access
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-2</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: storage-accounts.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 25</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The &#x27;bypass&#x27; property is set to &#x27;AzureServices, Logging, Metrics&#x27;, which allows Azure services to bypass network restrictions. This broad bypass may unnecessarily expose storage accounts if not justified by a business need.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Minimize the &#x27;bypass&#x27; scope. Only include &#x27;AzureServices&#x27; if required for functionality. Prefer restricting bypass where possible and document the justification for each allowed bypass.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon high">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">DP-2</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: storage-accounts.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 82</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The CORS rules on the storage account blob services specify &#x27;allowedHeaders&#x27; and &#x27;exposedHeaders&#x27; as [&#x27;*&#x27;], which allows all headers to be sent and exposed, and &#x27;allowedOrigins&#x27; is driven by the parameter &#x27;cors_origins&#x27;. This could expose sensitive data during transit and enables broader client access than necessary, raising the risk of downgrade or data leak.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Restrict &#x27;allowedHeaders&#x27; and &#x27;exposedHeaders&#x27; to only those specifically needed. Ensure &#x27;allowedOrigins&#x27; only includes trusted, business-required domains and not &#x27;*&#x27;.
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="severity-group" data-severity="medium">
                        <div class="severity-header medium">
                            <span>🟡 MEDIUM Severity Findings</span>
                            <span class="count">22</span>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                        <div class="findings-list">
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon medium">
                                        <i class="fas fa-exclamation"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-1</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: autoscale-settings.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 1</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> There is no evidence in the template of any network security controls (such as Network Security Groups or Azure Firewall) being applied to the resources defined or referenced. This means that the autoscale operation target (e.g., an App Service Plan or related server farm) and the associated Storage Account could potentially be left unprotected from unauthorized network access.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Review the network architecture and ensure that the target resources (including server farms and storage accounts) are protected with appropriate NSG (for VMs or subnets) or integrated with Azure Firewall/perimeter controls to restrict unnecessary traffic.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon medium">
                                        <i class="fas fa-exclamation"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">DP-3</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: autoscale-settings.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 1</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> Sensitive values (such as &#x27;server_farm_id&#x27;, &#x27;func_storage_account_id&#x27;, and &#x27;workspaceId&#x27;) are provided as plain parameters, but it is unclear from the template if any Key Vault reference or secure parameter mechanism is used to protect these identifiers and potential secrets. While no direct secrets are exposed, best practices require storage of sensitive values in Azure Key Vault.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Parameterize sensitive identifiers and reference them securely from Azure Key Vault wherever possible, especially if these values could be used to escalate access or enumerate the environment.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon medium">
                                        <i class="fas fa-exclamation"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">DP-3</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: event-grid.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 2</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The parameters referencing resource IDs (&#x27;storageFuncId&#x27;, &#x27;storageFuzzId&#x27;, etc.) are passed into the template potentially from upstream sources. There is no evidence that sensitive or secret values (such as keys or connection strings) are included here, but if they are, they must be stored in Azure Key Vault and never hardcoded or passed as plain parameters.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Store any sensitive information such as keys, secrets, or connection strings in Azure Key Vault. Ensure template parameters do not include sensitive data directly.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon medium">
                                        <i class="fas fa-exclamation"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-3</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: function-settings.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 40</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> There is no evidence of Network Security Groups (NSGs) being associated with the Storage Account (funcStorage) or the App Service resources. Without NSGs, there are no granular controls over inbound/outbound traffic.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Create and associate appropriate Network Security Groups (NSGs) to control inbound and outbound traffic to related subnets or VMs. For Storage, also consider using service endpoints or private endpoints together with NSGs.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon medium">
                                        <i class="fas fa-exclamation"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">DP-2</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: function.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 85</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The storage account resource configuration does not specify whether encryption at rest is enabled. Azure Storage enables encryption by default, but this should be explicitly set or verified, especially for existing resources.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Explicitly enable and monitor encryption at rest for the storage account. If the resource is managed outside this template, ensure encryption is configured accordingly.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon medium">
                                        <i class="fas fa-exclamation"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">DP-1</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: function.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 85</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> Encryption at rest is not defined for the referenced storage account &#x27;funcStorage&#x27;. Although encryption is enabled by default on Azure Storage, explicit configuration is recommended to ensure compliance and transparency.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Explicitly configure and enforce encryption at rest for the storage account via the template or by validating the existing resource&#x27;s configuration.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon medium">
                                        <i class="fas fa-exclamation"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-4</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: hub-network.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 2</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> There is no indication of an Azure Firewall or third-party firewall protecting the resources within the virtual network. This leaves the network lacking robust centralized traffic inspection and threat protection.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Deploy an Azure Firewall or third-party firewall appliance to the hub network to provide centralized policy enforcement, monitoring, and protection against known threats. Integrate it in the network topology as required.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon medium">
                                        <i class="fas fa-exclamation"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-8</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: hub-network.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 2</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The configuration does not explicitly enable or reference Azure DDoS Protection for the virtual network. Without this, resources may be vulnerable to distributed denial-of-service attacks.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Enable Azure DDoS Protection Standard on the virtual network to provide enhanced mitigation against DDoS attacks targeting cloud resources.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon medium">
                                        <i class="fas fa-exclamation"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-9</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: hub-network.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 2</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> There is no configuration for network traffic monitoring, such as enabling Network Watcher or diagnostic settings for flow logs. Lack of monitoring limits threat detection and forensic capability.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Enable Azure Network Watcher and configure diagnostic settings to collect and analyze flow logs and other relevant network events for the virtual network and subnets.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon medium">
                                        <i class="fas fa-exclamation"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-10</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: instance-config.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 23</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The configuration does not mention use of Azure Bastion for secure VM management, leaving a risk that RDP/SSH might be exposed or managed insecurely. ASB NS-10 requires the use of Bastion hosts to avoid direct exposure of management ports.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Use Azure Bastion for remote management of all VMs. Do not expose SSH/RDP ports directly to the internet, and document Bastion deployment in your IaC if VMs need management access.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon medium">
                                        <i class="fas fa-exclamation"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-7</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: instance-config.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 23</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> There is no implementation of Just-in-Time (JIT) VM access or a mechanism to reduce exposure time of management ports (SSH/RDP). ASB NS-7 requires enabling JIT VM access.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Enable JIT VM access via Azure Security Center or corresponding policy in your pipeline to ensure management ports are accessible only for limited time to authorized users on demand.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon medium">
                                        <i class="fas fa-exclamation"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">DP-1</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: instance-config.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 32</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> VM image definitions and compute resources are referenced but there is no explicit configuration for encryption at rest on VM disks, as required by ASB DP-1.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Explicitly enable encryption at rest for all managed disks attached to VMs. This can be achieved by setting encryption-enabled properties on VM/VMSS disk resources or attaching encrypted disks by default.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon medium">
                                        <i class="fas fa-exclamation"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">DP-4</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: instance-config.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 32</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The template omits controlled configuration or enforcement of managed disk encryption (e.g., no &#x27;encryptionSettings&#x27; for VM disks or no mention of managed disks at all in staticConfig), violating ASB DP-4.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Ensure all VM disks are Azure managed disks with platform-managed or customer-managed encryption keys enabled by default for any VM created by this configuration.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon medium">
                                        <i class="fas fa-exclamation"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-5</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: ip-rules.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 3</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The template only defines IP allow rules for network connection but does not leverage Private Endpoints for resource access, missing an opportunity to fully isolate resources from the public internet as recommended for sensitive resources.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Implement Azure Private Endpoints to ensure sensitive resources are only accessible within the private network, eliminating exposure to the public internet. Combine with restrictive IP rules for additional defense.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon medium">
                                        <i class="fas fa-exclamation"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-6</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: ip-rules.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 3</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> No usage of Virtual Network Service Endpoints is apparent in the template. Relying solely on IP-based rules does not provide the strongest binding of resources to designated VNets.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Consider implementing Virtual Network Service Endpoints to tie Azure services (such as Storage or SQL) directly to your VNets, reducing exposure to other Azure tenants and additional risks from IP-based rules.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon medium">
                                        <i class="fas fa-exclamation"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-1</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: ip-rules.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 3</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> No use of Network Security Groups (NSGs) or Azure Firewall is specified to enforce granular traffic restrictions beyond IP allow-lists. Relying solely on IP rules may not provide sufficient layer-4/layer-7 protection for critical resources.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Deploy Network Security Groups or Azure Firewall in conjunction with existing IP rules to provide deeper traffic inspection and enforce granular security policies at both subnet and resource levels.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon medium">
                                        <i class="fas fa-exclamation"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-2</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: scaleset-networks.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 7</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> A public IP address (&#x27;scalesetOutboundIp&#x27;) is being provisioned without any restriction or mention of NSG/firewall controls. Exposing public endpoints without sufficient protection increases the risk of unauthorized access and attacks.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Ensure all public-facing IPs are protected by NSGs and/or an Azure Firewall, and restrict access to necessary sources only.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon medium">
                                        <i class="fas fa-exclamation"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-3</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: server-farms.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 1</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> No Network Security Groups (NSGs) are defined or referenced for associated resources. This may lead to insufficient isolation or protection for network traffic related to the App Service or Key Vault.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Deploy your App Service within a vNET subnet governed by an NSG with least privilege rules, and ensure any supporting resources (like the Key Vault) are similarly protected, where possible.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon medium">
                                        <i class="fas fa-exclamation"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-5</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: server-farms.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 1</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> There is no configuration for private endpoints for the App Service or referenced Key Vaults, risking exposure of sensitive operations over public infrastructure.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Enable Private Endpoints for the referenced Key Vaults and consider App Service vNET integration with private endpoints or access only.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon medium">
                                        <i class="fas fa-exclamation"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">IM-8</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: server-farms.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 1</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> No managed identities are configured for the App Service or referenced in the template. This may result in use of less secure credential management practices when accessing Key Vault secrets (used for certificates).
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Configure a user-assigned or system-assigned managed identity for the App Service and assign it appropriate Key Vault access policies.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon medium">
                                        <i class="fas fa-exclamation"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">DP-6</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: server-farms.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 81</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> There is no indication of Customer-Managed Key (CMK) encryption for resources such as App Service or Key Vault, which is recommended for strong data protection in sensitive environments.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Wherever CMK is available, configure your App Service and Key Vault to use Customer-Managed Keys for encryption at rest.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon medium">
                                        <i class="fas fa-exclamation"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">DP-3</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: storage-accounts.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 82</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The CORS configuration allows any header (&#x27;*&#x27;) and origin is parameterized. While parameters are used, this may inadvertently disclose sensitive data through headers or enable data exfiltration if misconfigured.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Review and minimize the list of allowed origins. Limit allowed and exposed headers to those explicitly required for application functionality, and avoid using &#x27;*&#x27;.
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="severity-group" data-severity="low">
                        <div class="severity-header low">
                            <span>🔵 LOW Severity Findings</span>
                            <span class="count">5</span>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                        <div class="findings-list">
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon low">
                                        <i class="fas fa-info-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-8</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: ip-rules.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 0</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The template does not reference enabling Azure DDoS Protection, which is recommended to safeguard resources exposed to the internet, especially when public-facing IP rules are used.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Enable Azure DDoS Protection on the relevant virtual networks to mitigate the risk of distributed denial-of-service attacks.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon low">
                                        <i class="fas fa-info-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">NS-6</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: scaleset-networks.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 38</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> Service endpoints are not configured for the scaleset subnet, missing an opportunity to secure Azure service traffic directly within the Azure backbone network.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    If this subnet will communicate with Azure PaaS services (e.g., Storage, SQL), explicitly define serviceEndpoints for those services in the subnet&#x27;s configuration.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon low">
                                        <i class="fas fa-info-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">DP-3</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: server-farms.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 98</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> The property &#x27;settingValue&#x27; in resource &#x27;serverFarms_AntMDS_CERTIFICATE_PASSWORD_GENEVACERT&#x27; is explicitly set as an empty string, which indicates certificate password might not be set or could be handled insecurely in the future.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Never store certificate passwords in plaintext or as empty values. Securely reference such values from Azure Key Vault and ensure password protection is enforced for private keys when exporting or handling certificates.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon low">
                                        <i class="fas fa-info-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">DP-2</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: server-farms.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 1</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> No explicit configuration for enforcing TLS 1.2+ for encryption in transit on the App Service is provided. This may allow weaker protocols by default.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Set &#x27;minTlsVersion&#x27; property in App Service configuration to &#x27;1.2&#x27; or higher to enforce TLS 1.2+ for all inbound connections.
                                </div>
                            </div>
                            <div class="finding">
                                <div class="finding-header">
                                    <div class="finding-icon low">
                                        <i class="fas fa-info-circle"></i>
                                    </div>
                                    <div class="finding-content">
                                        <div class="finding-title">
                                            Security Issue Found
                                            <span class="control-badge">DP-1</span>
                                        </div>
                                        <div class="finding-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code"></i>
                                                <span>File: storage-accounts.bicep</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Line: 16</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="finding-description">
                                    <strong>Issue:</strong> Encryption at rest is not explicitly configured for any storage account. Although Azure Storage is encrypted at rest by default, best practices recommend explicit configuration, such as customer-managed keys, for greater control and compliance.
                                </div>
                                <div class="finding-remediation">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </div>
                                    Specify explicit encryption configuration under the &#x27;properties.encryption&#x27; section, and consider using customer-managed keys if compliance requires.
                                </div>
                            </div>
                        </div>
                    </div>
                        <div id="noFindings" class="no-findings" style="display: none;">
                            <i class="fas fa-search"></i>
                            <h3>No findings match your search criteria</h3>
                            <p>Try adjusting your search terms or filters</p>
                        </div>
                    </div>

                    <div class="footer">
                        <div class="export-buttons">
                            <button class="export-btn" onclick="window.print()">
                                <i class="fas fa-print"></i> Print Report
                            </button>
                            <button class="export-btn" onclick="exportToJson()">
                                <i class="fas fa-download"></i> Export JSON
                            </button>
                        </div>
                        <p>Generated by IaC Guardian GPT • June 16, 2025 at 06:47 PM</p>
                        <p><strong>Disclaimer:</strong> This report is AI-generated and should be reviewed by security professionals.</p>
                    </div>
                </div>

                <script>
                    // JavaScript for interactivity
                    document.addEventListener('DOMContentLoaded', function() {
                        const searchInput = document.getElementById('searchInput');
                        const filterButtons = document.querySelectorAll('.filter-btn');
                        const severityHeaders = document.querySelectorAll('.severity-header');
                        const findings = document.querySelectorAll('.finding');
                        const noFindings = document.getElementById('noFindings');

                        let currentFilter = 'all';
                        let currentSearch = '';

                        // Search functionality
                        searchInput.addEventListener('input', function() {
                            currentSearch = this.value.toLowerCase();
                            filterFindings();
                        });

                        // Filter functionality
                        filterButtons.forEach(button => {
                            button.addEventListener('click', function() {
                                filterButtons.forEach(btn => btn.classList.remove('active'));
                                this.classList.add('active');
                                currentFilter = this.dataset.severity;
                                filterFindings();
                            });
                        });

                        // Collapsible sections
                        severityHeaders.forEach(header => {
                            header.addEventListener('click', function() {
                                this.classList.toggle('collapsed');
                                const findingsList = this.nextElementSibling;
                                findingsList.classList.toggle('collapsed');
                            });
                        });

                        function filterFindings() {
                            let visibleCount = 0;
                            const severityGroups = document.querySelectorAll('.severity-group');

                            severityGroups.forEach(group => {
                                const severity = group.dataset.severity;
                                const groupFindings = group.querySelectorAll('.finding');
                                let groupVisibleCount = 0;

                                groupFindings.forEach(finding => {
                                    const text = finding.textContent.toLowerCase();
                                    const matchesSearch = currentSearch === '' || text.includes(currentSearch);
                                    const matchesFilter = currentFilter === 'all' || severity === currentFilter;

                                    if (matchesSearch && matchesFilter) {
                                        finding.style.display = 'block';
                                        groupVisibleCount++;
                                        visibleCount++;
                                    } else {
                                        finding.style.display = 'none';
                                    }
                                });

                                // Show/hide entire severity group
                                if (groupVisibleCount > 0 && (currentFilter === 'all' || severity === currentFilter)) {
                                    group.style.display = 'block';
                                } else {
                                    group.style.display = 'none';
                                }
                            });

                            // Show/hide no findings message
                            if (visibleCount === 0) {
                                noFindings.style.display = 'block';
                            } else {
                                noFindings.style.display = 'none';
                            }
                        }
                    });

                    function exportToJson() {
                        const findings = [];
                        document.querySelectorAll('.finding').forEach(finding => {
                            const control = finding.querySelector('.control-badge')?.textContent || '';
                            const description = finding.querySelector('.finding-description')?.textContent || '';
                            const remediation = finding.querySelector('.finding-remediation')?.textContent || '';
                            const file = finding.querySelector('.meta-item:nth-child(1)')?.textContent.replace('File: ', '') || '';
                            const line = finding.querySelector('.meta-item:nth-child(2)')?.textContent.replace('Line: ', '') || '';

                            findings.push({
                                control_id: control,
                                description: description.trim(),
                                remediation: remediation.trim(),
                                file_path: file,
                                line: line
                            });
                        });

                        const dataStr = JSON.stringify(findings, null, 2);
                        const dataBlob = new Blob([dataStr], {type: 'application/json'});
                        const url = URL.createObjectURL(dataBlob);
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = 'security_findings.json';
                        link.click();
                        URL.revokeObjectURL(url);
                    }
                </script>
            </body>
            </html>
            