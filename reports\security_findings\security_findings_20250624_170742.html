<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Assessment Report - IaC Guardian</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
/* Glass UI Framework Styles */
/* Glass UI Framework - IaC Guardian Security Reports */

:root {
    /* 🎨 Primary Color Palette (Blue tone) */
    --hue-primary: 223;
    --primary500: hsl(var(--hue-primary), 90%, 50%);
    --primary600: hsl(var(--hue-primary), 90%, 60%);
    --primary700: hsl(var(--hue-primary), 90%, 70%);

    /* 🟢 Secondary Color Palette (Teal tone) */
    --hue-secondary: 178;
    --secondary800: hsl(var(--hue-secondary), 90%, 80%);

    /* 🌑 Dark Grays (used for dark backgrounds) */
    --dark-gray50: hsl(var(--hue-primary), 90%, 5%);
    --dark-gray100: hsl(var(--hue-primary), 90%, 10%);

    /* ⚪ White Transparency Palette (used for glass effects, overlays) */
    --white0: hsla(0, 0%, 100%, 0);
    --white50: hsla(0, 0%, 100%, 0.05);
    --white100: hsla(0, 0%, 100%, 0.1);
    --white200: hsla(0, 0%, 100%, 0.2);
    --white300: hsla(0, 0%, 100%, 0.3);
    --white400: hsla(0, 0%, 100%, 0.4);
    --white500: hsla(0, 0%, 100%, 0.5);
    --white: hsl(0, 0%, 100%);

    /* 🧮 Base font scaling */
    font-size: clamp(0.75rem, 0.65rem + 0.5vw, 1.25rem);

    /* Glass UI Semantic Colors with New Palette */
    --success-green: hsl(142, 76%, 36%);
    --warning-amber: hsl(38, 92%, 50%);
    --danger-red: hsl(0, 84%, 60%);
    --info-cyan: var(--secondary800);

    /* Glass UI Components using White Transparency */
    --glass-white: var(--white200);
    --glass-white-light: var(--white100);
    --glass-white-strong: var(--white400);
    --glass-border: var(--white200);

    /* 📝 Text Color Palette - Optimized for Glass UI */
    --text-primary: var(--white);
    --text-secondary: hsla(0, 0%, 100%, 0.85);
    --text-muted: hsla(0, 0%, 100%, 0.65);
    --text-accent: hsl(var(--hue-secondary), 90%, 85%);
    --text-on-glass: hsla(0, 0%, 100%, 0.95);
    --text-on-dark: var(--white);
    --text-on-light: hsl(var(--hue-primary), 90%, 15%);
    --text-interactive: hsl(var(--hue-primary), 90%, 85%);
    --text-hover: hsl(var(--hue-secondary), 90%, 90%);

    /* Semantic Colors with Glass Effects */
    --critical-glass: hsla(0, 84%, 60%, 0.2);
    --critical-border: hsla(0, 84%, 60%, 0.3);
    --critical-text: hsl(0, 84%, 80%);
    --high-glass: hsla(38, 92%, 50%, 0.2);
    --high-border: hsla(38, 92%, 50%, 0.3);
    --high-text: hsl(38, 92%, 75%);
    --medium-glass: hsla(45, 93%, 47%, 0.2);
    --medium-border: hsla(45, 93%, 47%, 0.3);
    --medium-text: hsl(45, 93%, 75%);
    --low-glass: hsla(var(--hue-secondary), 90%, 80%, 0.2);
    --low-border: hsla(var(--hue-secondary), 90%, 80%, 0.3);
    --low-text: var(--secondary800);

    /* Glass UI Layout */
    --max-width: 1400px;
    --border-radius: 16px;
    --border-radius-sm: 12px;
    --border-radius-lg: 24px;
    --glass-blur: blur(16px);
    --glass-blur-strong: blur(24px);
    --glass-shadow: 0 8px 32px hsla(var(--hue-primary), 90%, 5%, 0.12);
    --glass-shadow-lg: 0 16px 64px hsla(var(--hue-primary), 90%, 5%, 0.16);
    --glass-shadow-xl: 0 24px 96px hsla(var(--hue-primary), 90%, 5%, 0.2);
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-on-glass);
    background: linear-gradient(135deg,
        var(--dark-gray50) 0%,
        var(--dark-gray100) 30%,
        hsl(var(--hue-primary), 60%, 15%) 70%,
        hsl(var(--hue-secondary), 40%, 20%) 100%);
    background-attachment: fixed;
    min-height: 100vh;
    overflow-x: hidden;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, hsla(var(--hue-primary), 90%, 50%, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, hsla(var(--hue-secondary), 90%, 60%, 0.12) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, var(--white50) 0%, transparent 70%);
    pointer-events: none;
    z-index: -1;
}

.main-container {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 2rem 1rem;
    position: relative;
    z-index: 1;
}

/* Glass UI Header Section */
.report-header {
    background: var(--glass-white);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    padding: 3rem 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--glass-shadow-lg);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.report-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary500), var(--primary600), var(--secondary800));
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.report-header::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
    pointer-events: none;
    border-radius: inherit;
}

.report-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-on-glass);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    text-shadow: 0 2px 8px hsla(var(--hue-primary), 90%, 5%, 0.3);
    position: relative;
    z-index: 2;
}

.report-subtitle {
    font-size: 1.125rem;
    color: var(--text-accent);
    font-weight: 400;
    margin-bottom: 1rem;
    position: relative;
    z-index: 2;
    text-shadow: 0 1px 4px hsla(var(--hue-primary), 90%, 5%, 0.2);
}

.report-meta {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 1.5rem;
    font-size: 0.875rem;
    color: var(--text-interactive);
    position: relative;
    z-index: 2;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--glass-white-light);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

/* Glass UI Controls Section */
.controls-section {
    background: var(--glass-white);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--glass-shadow);
}

.controls-grid {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 1.5rem;
    align-items: center;
}

.search-container {
    position: relative;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-sm);
    font-size: 0.875rem;
    transition: all 0.3s ease;
    background: var(--glass-white-light);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    color: var(--text-on-glass);
}

.search-input::placeholder {
    color: var(--text-interactive);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary500);
    background: var(--glass-white-strong);
    box-shadow: 0 0 0 3px hsla(var(--hue-primary), 90%, 50%, 0.2);
    transform: translateY(-1px);
    color: var(--text-on-glass);
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-interactive);
}

.filter-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--glass-border);
    border-radius: 2rem;
    background: var(--glass-white-light);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    color: var(--text-interactive);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    white-space: nowrap;
}

.filter-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--glass-shadow);
    background: var(--glass-white-strong);
    color: var(--text-hover);
}

.filter-btn.active {
    color: var(--text-on-dark);
    transform: translateY(-2px);
    box-shadow: var(--glass-shadow-lg);
    border-color: transparent;
}

.filter-btn.all.active {
    background: linear-gradient(135deg, var(--primary500), var(--primary600));
}
.filter-btn.critical.active {
    background: linear-gradient(135deg, var(--danger-red), var(--critical-text));
}
.filter-btn.high.active {
    background: linear-gradient(135deg, var(--warning-amber), var(--high-text));
}
.filter-btn.medium.active {
    background: linear-gradient(135deg, hsl(45, 93%, 47%), var(--medium-text));
}
.filter-btn.low.active {
    background: linear-gradient(135deg, var(--info-cyan), var(--secondary800));
}

/* Multi-select filter enhancements */
.filter-btn.active {
    position: relative;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.multi-select-info {
    text-align: center;
    margin-top: 0.5rem;
}

.filter-summary {
    text-align: center;
    font-weight: 500;
}

/* Animation for filter changes */
.severity-group {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.severity-group[style*="display: none"] {
    opacity: 0;
    transform: translateY(-10px);
}

.finding-item {
    transition: opacity 0.2s ease;
}

.finding-item[style*="display: none"] {
    opacity: 0;
}

/* Glass UI Summary Section */
.summary-section {
    background: var(--glass-white);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--glass-shadow);
}

.summary-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-on-glass);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-shadow: 0 2px 8px hsla(var(--hue-primary), 90%, 5%, 0.3);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--glass-white-light);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--glass-shadow-lg);
    background: var(--glass-white-strong);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary500), var(--secondary800));
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.stat-card::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.06) 0%, transparent 60%);
    pointer-events: none;
    border-radius: inherit;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-on-glass);
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 8px hsla(var(--hue-primary), 90%, 5%, 0.3);
    position: relative;
    z-index: 2;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-accent);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: relative;
    z-index: 2;
}

.severity-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1.5rem;
}

.severity-badge {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    transition: all 0.3s ease;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid var(--glass-border);
    position: relative;
    overflow: hidden;
}

.severity-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: inherit;
    opacity: 0.1;
    z-index: -1;
}

.severity-badge:hover {
    transform: translateY(-2px);
    box-shadow: var(--glass-shadow);
}

.severity-badge.critical {
    background: var(--critical-glass);
    border-color: var(--critical-border);
    color: var(--critical-text);
}

.severity-badge.high {
    background: var(--high-glass);
    border-color: var(--high-border);
    color: var(--high-text);
}

.severity-badge.medium {
    background: var(--medium-glass);
    border-color: var(--medium-border);
    color: var(--medium-text);
}

.severity-badge.low {
    background: var(--low-glass);
    border-color: var(--low-border);
    color: var(--low-text);
}

/* Glass UI Domain Section Styles */
.domain-section {
    margin-bottom: 2rem;
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    background: var(--glass-white);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    box-shadow: var(--glass-shadow);
    transition: all 0.3s ease;
}

.domain-section:hover {
    transform: translateY(-2px);
    box-shadow: var(--glass-shadow-lg);
}

.domain-header {
    background: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 50%, #03A9F4 100%);
    color: var(--white);
    padding: 1.5rem 2rem;
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(79, 195, 247, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;
}

.domain-header:hover {
    background: linear-gradient(135deg, #29B6F6 0%, #03A9F4 50%, #0288D1 100%);
    transform: translateY(-1px);
}

.domain-header-left {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.domain-toggle-icon {
    transition: transform 0.3s ease;
    font-size: 1rem;
    opacity: 0.8;
}

.domain-header.collapsed .domain-toggle-icon {
    transform: rotate(-90deg);
}

.domain-header.collapsed {
    border-radius: var(--border-radius);
}

/* Domain-specific color schemes */
.domain-section[data-domain="identity-management"] .domain-header {
    background: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 50%, #03A9F4 100%);
    box-shadow: 0 4px 12px rgba(79, 195, 247, 0.3);
}

.domain-section[data-domain="identity-management"] .domain-header:hover {
    background: linear-gradient(135deg, #29B6F6 0%, #03A9F4 50%, #0288D1 100%);
}

.domain-section[data-domain="network-security"] .domain-header {
    background: linear-gradient(135deg, #66BB6A 0%, #4CAF50 50%, #388E3C 100%);
    box-shadow: 0 4px 12px rgba(102, 187, 106, 0.3);
}

.domain-section[data-domain="network-security"] .domain-header:hover {
    background: linear-gradient(135deg, #4CAF50 0%, #388E3C 50%, #2E7D32 100%);
}

.domain-section[data-domain="data-protection"] .domain-header {
    background: linear-gradient(135deg, #FF7043 0%, #FF5722 50%, #D84315 100%);
    box-shadow: 0 4px 12px rgba(255, 112, 67, 0.3);
}

.domain-section[data-domain="data-protection"] .domain-header:hover {
    background: linear-gradient(135deg, #FF5722 0%, #D84315 50%, #BF360C 100%);
}

/* Glass UI Severity Group Styles */
.severity-group {
    background: var(--glass-white-light);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    border-radius: 0;
    margin: 0;
    box-shadow: none;
    transition: all 0.3s ease;
    overflow: hidden;
}

.domain-section .severity-group {
    border-radius: 0;
    border: none;
    border-bottom: 1px solid var(--glass-border);
    box-shadow: none;
    background: var(--glass-white-light);
}

.domain-section .severity-group:last-child {
    border-bottom: none;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* Severity Header Styles */
.severity-header {
    padding: 1.25rem 1.5rem;
    font-weight: 600;
    font-size: 1.125rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 1px solid var(--glass-border);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.severity-header:hover {
    background: var(--glass-white-strong);
    transform: translateY(-1px);
}

.severity-header-left {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.severity-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.severity-header.critical {
    background: var(--critical-glass);
    color: var(--critical-text);
    border-left: 4px solid var(--critical-text);
}

.severity-header.critical .severity-icon {
    background: linear-gradient(135deg, var(--danger-red), var(--critical-text));
}

.severity-header.high {
    background: var(--high-glass);
    color: var(--high-text);
    border-left: 4px solid var(--high-text);
}

.severity-header.high .severity-icon {
    background: linear-gradient(135deg, var(--warning-amber), var(--high-text));
}

.severity-header.medium {
    background: var(--medium-glass);
    color: var(--medium-text);
    border-left: 4px solid var(--medium-text);
}

.severity-header.medium .severity-icon {
    background: linear-gradient(135deg, hsl(45, 93%, 47%), var(--medium-text));
}

.severity-header.low {
    background: var(--low-glass);
    color: var(--low-text);
    border-left: 4px solid var(--low-text);
}

.severity-header.low .severity-icon {
    background: linear-gradient(135deg, var(--info-cyan), var(--secondary800));
}

.severity-title {
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.severity-count {
    background: rgba(255, 255, 255, 0.9);
    color: inherit;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 700;
    margin-left: 0.5rem;
}

/* Toggle Icon Styles */
.toggle-icon {
    transition: transform 0.3s ease;
    color: var(--text-interactive);
    opacity: 0.8;
}

.severity-header.collapsed .toggle-icon {
    transform: rotate(-90deg);
}

/* Findings List Styles */
.findings-list {
    transition: all 0.3s ease;
    overflow: hidden;
    max-height: none;
}

.findings-list.collapsed {
    max-height: 0;
}

.finding-item {
    border-bottom: 1px solid var(--glass-border);
    padding: 1.5rem;
    transition: all 0.3s ease;
    background: var(--glass-white-light);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.finding-item:last-child {
    border-bottom: none;
}

.finding-item:hover {
    background: var(--glass-white-strong);
    transform: translateY(-1px);
    box-shadow: var(--glass-shadow);
}

/* Finding Header and Content Styles */
.finding-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
}

.finding-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.finding-icon.critical {
    background: linear-gradient(135deg, var(--danger-red), var(--critical-text));
}

.finding-icon.high {
    background: linear-gradient(135deg, var(--warning-amber), var(--high-text));
}

.finding-icon.medium {
    background: linear-gradient(135deg, hsl(45, 93%, 47%), var(--medium-text));
}

.finding-icon.low {
    background: linear-gradient(135deg, var(--info-cyan), var(--secondary800));
}

.finding-content {
    flex: 1;
    min-width: 0;
}

.finding-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-on-glass);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.control-id {
    background: linear-gradient(135deg, var(--primary500), var(--primary600));
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.finding-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    color: var(--text-interactive);
    flex-wrap: wrap;
}

.finding-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.finding-recommendation {
    background: var(--glass-white);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-sm);
    padding: 1rem;
    margin-top: 1rem;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.finding-recommendation h4 {
    color: var(--text-accent);
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.finding-recommendation p {
    color: var(--text-secondary);
    line-height: 1.5;
    margin: 0;
}

/* Action Buttons */
.finding-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.action-btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-sm);
    background: var(--glass-white-light);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    color: var(--text-interactive);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.75rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.action-btn:hover {
    background: var(--glass-white-strong);
    transform: translateY(-1px);
    box-shadow: var(--glass-shadow);
    color: var(--text-hover);
}

.action-btn.primary {
    background: linear-gradient(135deg, var(--primary500), var(--primary600));
    color: var(--white);
    border-color: transparent;
}

.action-btn.primary:hover {
    background: linear-gradient(135deg, var(--primary600), var(--primary700));
    color: var(--white);
}


/* Responsive Design Styles */
/* Responsive Design for Glass UI Framework */

/* Large Desktop (1200px+) - Enhanced Glass Effects */
@media (min-width: 1200px) {
    .main-container {
        padding: 3rem 2rem;
    }

    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .severity-overview {
        grid-template-columns: repeat(4, 1fr);
    }

    /* Enhanced glass blur for larger screens */
    .report-header,
    .controls-section,
    .summary-section,
    .severity-group,
    .domain-section {
        backdrop-filter: var(--glass-blur-strong);
        -webkit-backdrop-filter: var(--glass-blur-strong);
    }
}

/* Desktop (992px - 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
    .main-container {
        padding: 2rem 1.5rem;
    }

    .report-title {
        font-size: 2.25rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .severity-overview {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Tablet (768px - 991px) */
@media (min-width: 768px) and (max-width: 991px) {
    .main-container {
        padding: 1.5rem 1rem;
    }

    .report-header {
        padding: 2rem 1.5rem;
    }

    .report-title {
        font-size: 2rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .report-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .controls-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .filter-buttons {
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .severity-overview {
        grid-template-columns: repeat(2, 1fr);
    }

    .finding-title {
        font-size: 1rem;
    }

    .finding-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Mobile Large (576px - 767px) */
@media (min-width: 576px) and (max-width: 767px) {
    .main-container {
        padding: 1rem 0.75rem;
    }

    .report-header {
        padding: 1.5rem 1rem;
    }

    .report-title {
        font-size: 1.75rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .report-subtitle {
        font-size: 1rem;
    }

    .report-meta {
        flex-direction: column;
        gap: 0.25rem;
        font-size: 0.8125rem;
    }

    .controls-section {
        padding: 1rem;
    }

    .controls-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .filter-buttons {
        justify-content: center;
        gap: 0.375rem;
    }

    .filter-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.6875rem;
    }

    .summary-section {
        padding: 1.5rem 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .severity-overview {
        grid-template-columns: 1fr;
    }

    .finding-item {
        padding: 1rem;
    }

    .finding-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .finding-title {
        font-size: 0.9375rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .export-actions {
        flex-direction: column;
        align-items: center;
    }
}

/* Mobile Small (up to 575px) */
@media (max-width: 575px) {
    .main-container {
        padding: 0.75rem 0.5rem;
    }

    .report-header {
        padding: 1.25rem 0.75rem;
        margin-bottom: 1rem;
    }

    .report-title {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.375rem;
    }

    .report-subtitle {
        font-size: 0.9375rem;
    }

    .report-meta {
        flex-direction: column;
        gap: 0.25rem;
        font-size: 0.75rem;
    }

    .controls-section {
        padding: 0.75rem;
    }

    .search-input {
        font-size: 1rem; /* Prevents zoom on iOS */
        padding: 0.75rem 1rem 0.75rem 2.25rem;
    }

    .filter-buttons {
        gap: 0.25rem;
    }

    .filter-btn {
        padding: 0.375rem 0.625rem;
        font-size: 0.625rem;
        min-width: auto;
    }

    .summary-section {
        padding: 1.25rem 0.75rem;
    }

    .summary-title {
        font-size: 1.25rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .severity-overview {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .severity-header {
        padding: 1rem;
        font-size: 1rem;
    }

    .finding-item {
        padding: 0.75rem;
    }

    .finding-icon {
        width: 2rem;
        height: 2rem;
        font-size: 0.875rem;
    }

    .finding-title {
        font-size: 0.875rem;
    }

    .control-id {
        font-size: 0.6875rem;
        padding: 0.1875rem 0.5rem;
    }

    .finding-meta {
        font-size: 0.8125rem;
    }

    .finding-description {
        font-size: 0.875rem;
    }

    .code-snippet {
        font-size: 0.75rem;
        padding: 0.75rem;
    }

    .export-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .export-btn {
        width: 100%;
        justify-content: center;
    }

    /* Mobile Line Number Fixes - Simplified Structure */
    .meta-item.line-number {
        width: auto !important;
        min-width: max-content !important;
        flex-shrink: 0 !important;
        font-size: 0.75rem !important;
        padding: 0.3rem 0.6rem !important;
        gap: 0.25rem !important;
        white-space: nowrap !important;
        overflow: visible !important;
        box-sizing: border-box !important;
    }
}

/* Print Styles */
@media print {
    body {
        background: white !important;
    }

    .main-container {
        max-width: none;
        padding: 0;
    }

    .report-header,
    .controls-section,
    .summary-section,
    .severity-group,
    .report-footer {
        box-shadow: none !important;
        break-inside: avoid;
    }

    .controls-section,
    .export-actions {
        display: none !important;
    }

    .findings-list {
        max-height: none !important;
    }
}

    </style>
    
</head>

<body>
    <div class="main-container">
        <!-- Header Section -->
        <header class="report-header">
            <h1 class="report-title">
                <i class="fas fa-shield-alt"></i>
                Security Assessment Report
            </h1>
            <p class="report-subtitle">Infrastructure as Code Security Analysis</p>
            <div class="report-meta">
                <div class="meta-item">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Generated: June 24, 2025 at 05:07 PM</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-robot"></i>
                    <span>IaC Guardian</span>
                </div>
            </div>
        </header>

        <!-- Controls Section -->
        <section class="controls-section">
            <div class="controls-grid">
                <div class="search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="Search findings...">
                </div>
                <div class="filter-buttons">
                    <button class="filter-btn all active" data-severity="all">
                        <i class="fas fa-list"></i>
                        All
                    </button>
                    <button class="filter-btn critical" data-severity="critical">
                        <i class="fas fa-exclamation-triangle"></i>
                        Critical
                    </button>
                    <button class="filter-btn high" data-severity="high">
                        <i class="fas fa-exclamation-circle"></i>
                        High
                    </button>
                    <button class="filter-btn medium" data-severity="medium">
                        <i class="fas fa-exclamation"></i>
                        Medium
                    </button>
                    <button class="filter-btn low" data-severity="low">
                        <i class="fas fa-info-circle"></i>
                        Low
                    </button>
                </div>
            </div>
        </section>

        <!-- Summary Section -->
        
        <!-- Summary Section -->
        <section class="summary-section">
            <h2 class="summary-title">
                <i class="fas fa-chart-bar"></i>
                Executive Summary
            </h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">17</div>
                    <div class="stat-label">Total Findings</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">3</div>
                    <div class="stat-label">Files Affected</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">17</div>
                    <div class="stat-label">High Priority</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">12</div>
                    <div class="stat-label">Security Controls</div>
                </div>
            </div>
            <div class="severity-overview">
                <div class="severity-badge critical">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        Critical
                    </div>
                    <div class="severity-count">10</div>
                </div>
                <div class="severity-badge high">
                    <div class="severity-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        High
                    </div>
                    <div class="severity-count">7</div>
                </div>
            </div>
        </section>

        <!-- Graph Section -->
        

        <!-- Findings Section -->
        <div class="findings-container">
            
            <div class="domain-section" data-domain="identity-management">
                <h3 class="domain-header">
                    <div class="domain-header-left">
                        <i class="fas fa-shield-alt"></i>
                        <span class="domain-title">Identity Management (5 findings)</span>
                    </div>
                    <i class="fas fa-chevron-down domain-toggle-icon"></i>
                </h3>
                <section class="severity-group" data-severity="high" data-domain="identity-management">
                    <header class="severity-header high">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">High Severity</div>
                            <div class="severity-count">5</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item" data-severity="high">
                        <header class="finding-header">
                            <div class="finding-icon high">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">IM-1</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>KustoScripts.template.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 237</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('KustoScripts.template.json', 237, 'IM-1', 'high')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The function call 'fun_LookupByUniqueAttribute_Region' at line 237 does not specify any authentication or access control mechanism. If this function is exposed without Azure AD authentication, attackers could gain unauthorized access to region metadata, enabling lateral movement or privilege escalation within the environment.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Enforce Azure AD authentication for all Kusto functions, including 'fun_LookupByUniqueAttribute_Region'. Restrict function access to authorized identities only. Reference: ASB IM-1.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="high">
                        <header class="finding-header">
                            <div class="finding-icon high">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">IM-2</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>roleAssignment.deploymentTemplate.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 11</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('roleAssignment.deploymentTemplate.json', 11, 'IM-2', 'high')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The 'builtInRoleType' parameter has a default value of 'Owner', which grants full administrative privileges to the assigned principal. Assigning the Owner role by default enables an attacker who compromises the principalId to gain unrestricted access to all resources within the subscription, facilitating privilege escalation, lateral movement, and potential full environment compromise. The blast radius includes all resources under the subscription, making this a critical access control weakness.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Change the 'builtInRoleType' default value to the minimum required privilege (e.g., 'Reader' or a custom least-privilege role). Require explicit assignment of high-privilege roles and implement approval workflows for Owner or Contributor assignments. Reference: Azure Security Benchmark v3.0, Control IM-2.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="high">
                        <header class="finding-header">
                            <div class="finding-icon high">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">IM-2</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>roleAssignment.deploymentTemplate.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 13</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('roleAssignment.deploymentTemplate.json', 13, 'IM-2', 'high')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The 'roleDefinitionId' property is set to the GUID for the 'Owner' role, which grants full administrative rights over the subscription. Assigning this role to a principal (especially via automation or default configuration) creates a significant attack vector: if the principalId is compromised, an attacker can take full control of all Azure resources, escalate privileges, and disable security controls. The blast radius is the entire subscription.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Restrict assignment of the 'Owner' role to only those principals that require it for operational necessity. Use least-privilege roles wherever possible and implement Privileged Identity Management (PIM) to require just-in-time elevation for Owner access. Reference: Azure Security Benchmark v3.0, Control IM-2.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="high">
                        <header class="finding-header">
                            <div class="finding-icon high">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">IM-3</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>KustoScripts.template.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 237</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('KustoScripts.template.json', 237, 'IM-3', 'high')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The script at line 237 does not indicate the use of managed identities or service principals for function execution. Absence of managed identities increases the risk of credential exposure and unauthorized access, as attackers could exploit hardcoded or weak credentials to execute privileged operations.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Configure the Logic App and Kusto functions to use Azure Managed Identities for all service-to-service authentication. Eliminate any use of hardcoded credentials or shared accounts. Reference: ASB IM-3.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="high">
                        <header class="finding-header">
                            <div class="finding-icon high">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">IM-6</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>roleAssignment.deploymentTemplate.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 28</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('roleAssignment.deploymentTemplate.json', 28, 'IM-6', 'high')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The 'roleDefinitionName' property is set to 'Owner', which grants the highest level of permissions in Azure. Assigning this role without enforcing strong authentication (such as MFA) for the associated principalId exposes the environment to account takeover and privilege escalation attacks. Attackers can exploit weak authentication to gain full control over the subscription.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Enforce multi-factor authentication (MFA) for all accounts assigned the 'Owner' role. Use Conditional Access policies to require strong authentication and monitor for suspicious sign-in activity. Reference: Azure Security Benchmark v3.0, Control IM-6.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    </div>
                </section>
            </div>
            <div class="domain-section" data-domain="network-security">
                <h3 class="domain-header">
                    <div class="domain-header-left">
                        <i class="fas fa-shield-alt"></i>
                        <span class="domain-title">Network Security (5 findings)</span>
                    </div>
                    <i class="fas fa-chevron-down domain-toggle-icon"></i>
                </h3>
                <section class="severity-group" data-severity="critical" data-domain="network-security">
                    <header class="severity-header critical">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-skull-crossbones"></i>
                            </div>
                            <div class="severity-title">Critical Severity</div>
                            <div class="severity-count">3</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-1</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>Grafana.deploymentTemplate.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 38</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('Grafana.deploymentTemplate.json', 38, 'NS-1', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The Grafana instance is configured with 'publicNetworkAccess' set to 'Enabled', which allows unrestricted inbound and outbound network traffic from the public internet. This violates network segmentation and deny-by-default principles, increasing the risk of network compromise and unauthorized access.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Restrict network access by disabling public network access ('publicNetworkAccess': 'Disabled') and associating the resource with a virtual network and appropriate Network Security Groups (NSGs) to enforce least privilege network segmentation. Reference: Azure Security Benchmark NS-1.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-2</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>Grafana.deploymentTemplate.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 38</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('Grafana.deploymentTemplate.json', 38, 'NS-2', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The 'publicNetworkAccess' property is set to 'Enabled' for the Microsoft.Dashboard/grafana resource. This exposes the Grafana instance to the public internet, creating an initial access vector for attackers. If compromised, attackers could gain access to monitoring data, credentials, or use the service as a pivot point for lateral movement within the environment. The blast radius includes potential data exposure and compromise of connected Azure resources.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Set 'publicNetworkAccess' to 'Disabled' to restrict access to private endpoints only. Implement Azure Private Link and ensure only trusted networks can access the Grafana instance. Example: "publicNetworkAccess": "Disabled". Reference: Azure Security Benchmark NS-2.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-3</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>Grafana.deploymentTemplate.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 38</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('Grafana.deploymentTemplate.json', 38, 'NS-3', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">No Azure Firewall or equivalent network filtering is defined for the Grafana instance with 'publicNetworkAccess' enabled. This allows direct access from the internet without advanced filtering, increasing the risk of exploitation via known vulnerabilities or brute-force attacks.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Deploy Azure Firewall or a similar solution to filter and inspect traffic to the Grafana instance. Route all inbound and outbound traffic through the firewall and configure rules to allow only necessary traffic. Reference: Azure Security Benchmark NS-3.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    </div>
                </section>
                <section class="severity-group" data-severity="high" data-domain="network-security">
                    <header class="severity-header high">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="severity-title">High Severity</div>
                            <div class="severity-count">2</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item" data-severity="high">
                        <header class="finding-header">
                            <div class="finding-icon high">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-5</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>Grafana.deploymentTemplate.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 38</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('Grafana.deploymentTemplate.json', 38, 'NS-5', 'high')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">With 'publicNetworkAccess' enabled, the Grafana instance is exposed to potential Distributed Denial of Service (DDoS) attacks. No DDoS protection is specified, increasing the risk of service disruption and potential exploitation during an attack.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Enable Azure DDoS Protection Standard on the virtual network hosting the Grafana instance. Monitor DDoS metrics and configure alerts for attack detection. Reference: Azure Security Benchmark NS-5.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="high">
                        <header class="finding-header">
                            <div class="finding-icon high">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">NS-8</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>Grafana.deploymentTemplate.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 38</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('Grafana.deploymentTemplate.json', 38, 'NS-8', 'high')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The template does not specify enforcement of secure protocols (e.g., TLS 1.2+) for the public endpoint. With 'publicNetworkAccess' enabled, this omission could allow insecure protocol usage, exposing the service to downgrade and man-in-the-middle attacks.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Explicitly enforce the latest TLS version (1.2 or higher) for all public endpoints. Disable legacy and insecure protocols. Reference: Azure Security Benchmark NS-8.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    </div>
                </section>
            </div>
            <div class="domain-section" data-domain="data-protection">
                <h3 class="domain-header">
                    <div class="domain-header-left">
                        <i class="fas fa-shield-alt"></i>
                        <span class="domain-title">Data Protection (7 findings)</span>
                    </div>
                    <i class="fas fa-chevron-down domain-toggle-icon"></i>
                </h3>
                <section class="severity-group" data-severity="critical" data-domain="data-protection">
                    <header class="severity-header critical">
                        <div class="severity-header-left">
                            <div class="severity-icon">
                                <i class="fas fa-skull-crossbones"></i>
                            </div>
                            <div class="severity-title">Critical Severity</div>
                            <div class="severity-count">7</div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </header>
                    <div class="findings-list">
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-1</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>KustoScripts.template.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 225</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('KustoScripts.template.json', 225, 'DP-1', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The script at line 232 uses 'mv-expand' on a dynamic input '_input_monikers' without any data classification or validation. If sensitive or unclassified data is passed in '_input_monikers', it could be exposed or processed without proper controls, enabling data exfiltration or unauthorized access. Attackers could exploit this to enumerate or extract sensitive data, increasing the blast radius of a compromise.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Implement Azure Purview or Azure Information Protection to classify and label all data processed by '_input_monikers'. Ensure that only non-sensitive, classified data is accepted as input. Add validation logic to reject or mask sensitive data before expansion. Reference: ASB DP-1.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-2</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>KustoScripts.template.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 55</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('KustoScripts.template.json', 55, 'DP-2', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The property 'continueOnErrors' is set to true for the Kusto script deployment. This setting allows the deployment to proceed even if errors occur during script execution, which can mask unauthorized data access or exfiltration attempts. Attackers could exploit this by injecting or triggering errors to bypass detection, leading to potential data loss or exposure. The blast radius includes undetected data exfiltration or corruption in the Kusto database.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Set 'continueOnErrors' to false to ensure that any error during script execution halts the deployment and triggers alerts. Implement monitoring and alerting for failed script executions to detect and respond to anomalous activities. Reference: Azure Security Benchmark DP-2.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-2</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>KustoScripts.template.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 176</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('KustoScripts.template.json', 176, 'DP-2', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The property 'continueOnErrors' is set to true for the Microsoft.Kusto/clusters/databases/scripts resource. This configuration allows the deployment to proceed even if script execution fails, which can result in incomplete or inconsistent data processing. Attackers could exploit this by introducing errors or tampering with scripts, causing data exfiltration or bypassing critical data validation steps. The blast radius includes potential unauthorized data access, data integrity compromise, and undetected data exfiltration.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Set 'continueOnErrors' to false to ensure that any script execution failure halts the deployment process. Implement monitoring and alerting for script execution failures. Regularly review and validate all scripts for integrity and security. Reference: Azure Security Benchmark DP-2.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-2</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>KustoScripts.template.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 225</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('KustoScripts.template.json', 225, 'DP-2', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The use of 'mv-expand' on '_input_monikers' at line 232 lacks monitoring for anomalous or unauthorized data access. Without monitoring, attackers could exploit this function to exfiltrate large volumes of data or perform unauthorized queries, bypassing detection and increasing the risk of data breaches.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Enable Azure Defender for SQL/Data Explorer and configure monitoring for anomalous data access patterns on the Kusto cluster. Set up alerts for unusual query volumes or access to sensitive data. Reference: ASB DP-2.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-2</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>KustoScripts.template.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 319</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('KustoScripts.template.json', 319, 'DP-2', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The property 'continueOnErrors' is set to true, which allows the Logic App to proceed even if errors occur during script execution. This can enable attackers to suppress error signals during data operations, potentially masking unauthorized data access or exfiltration attempts. The blast radius includes undetected data exfiltration or manipulation, as monitoring and alerting systems may not be triggered by failed operations.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Set 'continueOnErrors' to false to ensure that any error in the script execution halts the process and triggers appropriate monitoring and alerting. Implement robust error handling and ensure that all failed operations are logged and reviewed. Reference: Azure Security Benchmark DP-2 (Monitor and alert on anomalous data activities).</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-3</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>KustoScripts.template.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 105</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('KustoScripts.template.json', 105, 'DP-3', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The property 'continueOnErrors' is set to true for the Kusto script resource. This setting allows the deployment to proceed even if the script fails, which can result in incomplete or inconsistent data processing. Attackers could exploit this by causing intentional script failures to bypass security controls or data validation steps, increasing the risk of data corruption or unauthorized data exposure. The blast radius includes potential data integrity loss and undetected misconfigurations in production environments.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Set 'continueOnErrors' to false for all critical Kusto script deployments to ensure that any failure halts the deployment process. This enforces strict error handling and prevents partial or insecure configurations from being applied. Review all script resources and update the configuration as follows: "continueOnErrors": false. Reference: Azure Security Benchmark DP-3.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    <article class="finding-item" data-severity="critical">
                        <header class="finding-header">
                            <div class="finding-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="finding-content">
                                <div class="finding-title">
                                    <span class="control-id">DP-3</span>
                                </div>
                                <div class="finding-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-file-code meta-icon"></i>
                                        <span>KustoScripts.template.json</span>
                                    </div>
                                    <div class="meta-item line-number" title="Click to copy line reference">
                                        <i class="fas fa-code meta-icon"></i>
                                        <span>Line 291</span>
                                    </div>
                                    <div class="meta-item code-preview" title="Click to view code snippet"
                                         onclick="showCodeDialog('KustoScripts.template.json', 291, 'DP-3', 'critical')">
                                        <i class="fas fa-eye meta-icon"></i>
                                        <span class="code-preview-badge">
                                            <i class="fas fa-file-code"></i>
                                            View Code
                                        </span>
                                    </div>
                                </div>
                                <div class="finding-description">The property 'continueOnErrors' is set to true, which may allow the deployment to proceed even if critical errors occur in script execution. This can result in incomplete or inconsistent data processing, potentially exposing sensitive data in transit to attackers if validation or security steps are skipped. Attackers could exploit this to inject malicious data or bypass security controls, increasing the risk of data exposure or manipulation.</div>
                                <div class="remediation-section">
                                    <h4 class="remediation-title">
                                        <i class="fas fa-tools"></i>
                                        Recommended Fix
                                    </h4>
                                    <div class="remediation-content">Set 'continueOnErrors' to false to ensure that any error during script execution halts the deployment process. This enforces strict validation and prevents the risk of data-in-transit exposure due to incomplete or insecure operations. Review all deployment scripts to ensure secure error handling and data validation in accordance with Azure Security Benchmark DP-3.</div>
                                </div>
                            </div>
                        </header>
                    </article>
                    </div>
                </section>
            </div>
        </div>

        <!-- Footer Section -->
        <footer class="report-footer">
            <div class="export-actions">
                <button class="export-btn" onclick="exportToJson()">
                    <i class="fas fa-download"></i>
                    Export JSON
                </button>
                <button class="export-btn" onclick="window.print()">
                    <i class="fas fa-print"></i>
                    Print Report
                </button>
            </div>
            <div class="footer-info">
                <p><strong>IaC Guardian</strong> - Infrastructure as Code Security Analysis</p>
                <p>Generated with Azure Security Benchmark v3.0 compliance checks</p>
                <p>Report generated on June 24, 2025 at 05:07 PM</p>
            </div>
        </footer>
    </div>

    <!-- No Results Message -->
    <div class="no-findings" style="display: none;">
        <div class="no-findings-icon">
            <i class="fas fa-search"></i>
        </div>
        <h3>No findings match your criteria</h3>
        <p>Try adjusting your search terms or filters</p>
    </div>

    <!-- Embedded File Content for Code Dialog -->
    
                <script type="application/json" data-finding-context="Grafana.deploymentTemplate.json_38" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;Grafana.deploymentTemplate.json&quot;, &quot;line_number&quot;: 38, &quot;total_lines&quot;: 43, &quot;start_line&quot;: 1, &quot;end_line&quot;: 43, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;{&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;  \&quot;$schema\&quot;: \&quot;https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;  \&quot;contentVersion\&quot;: \&quot;*******\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;  \&quot;parameters\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;    \&quot;grafanaInstanceName\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;string\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;    \&quot;location\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;string\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;      \&quot;defaultValue\&quot;: \&quot;[resourceGroup().location]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;  },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;  \&quot;resources\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Dashboard/grafana\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-09-01\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[parameters('grafanaInstanceName')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;      \&quot;location\&quot;: \&quot;[parameters('location')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;      \&quot;sku\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;        \&quot;name\&quot;: \&quot;Standard\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;      },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;      \&quot;identity\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;        \&quot;type\&quot;: \&quot;SystemAssigned\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;      },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;        \&quot;apiKey\&quot;: \&quot;Disabled\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;        \&quot;autoGeneratedDomainNameLabelScope\&quot;: \&quot;TenantReuse\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;        \&quot;deterministicOutboundIP\&quot;: \&quot;Disabled\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;        \&quot;grafanaConfigurations\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;          \&quot;smtp\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;            \&quot;enabled\&quot;: false&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;          }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;        },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;        \&quot;grafanaIntegrations\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;          \&quot;azureMonitorWorkspaceIntegrations\&quot;: []&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;        },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;        \&quot;grafanaMajorVersion\&quot;: \&quot;10\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;        \&quot;publicNetworkAccess\&quot;: \&quot;Enabled\&quot;,&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;        \&quot;zoneRedundancy\&quot;: \&quot;Disabled\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;  ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: {\n       2:   \&quot;$schema\&quot;: \&quot;https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#\&quot;,\n       3:   \&quot;contentVersion\&quot;: \&quot;*******\&quot;,\n       4:   \&quot;parameters\&quot;: {\n       5:     \&quot;grafanaInstanceName\&quot;: {\n       6:       \&quot;type\&quot;: \&quot;string\&quot;\n       7:     },\n       8:     \&quot;location\&quot;: {\n       9:       \&quot;type\&quot;: \&quot;string\&quot;,\n      10:       \&quot;defaultValue\&quot;: \&quot;[resourceGroup().location]\&quot;\n      11:     }\n      12:   },\n      13:   \&quot;resources\&quot;: [\n      14:     {\n      15:       \&quot;type\&quot;: \&quot;Microsoft.Dashboard/grafana\&quot;,\n      16:       \&quot;apiVersion\&quot;: \&quot;2023-09-01\&quot;,\n      17:       \&quot;name\&quot;: \&quot;[parameters('grafanaInstanceName')]\&quot;,\n      18:       \&quot;location\&quot;: \&quot;[parameters('location')]\&quot;,\n      19:       \&quot;sku\&quot;: {\n      20:         \&quot;name\&quot;: \&quot;Standard\&quot;\n      21:       },\n      22:       \&quot;identity\&quot;: {\n      23:         \&quot;type\&quot;: \&quot;SystemAssigned\&quot;\n      24:       },\n      25:       \&quot;properties\&quot;: {\n      26:         \&quot;apiKey\&quot;: \&quot;Disabled\&quot;,\n      27:         \&quot;autoGeneratedDomainNameLabelScope\&quot;: \&quot;TenantReuse\&quot;,\n      28:         \&quot;deterministicOutboundIP\&quot;: \&quot;Disabled\&quot;,\n      29:         \&quot;grafanaConfigurations\&quot;: {\n      30:           \&quot;smtp\&quot;: {\n      31:             \&quot;enabled\&quot;: false\n      32:           }\n      33:         },\n      34:         \&quot;grafanaIntegrations\&quot;: {\n      35:           \&quot;azureMonitorWorkspaceIntegrations\&quot;: []\n      36:         },\n      37:         \&quot;grafanaMajorVersion\&quot;: \&quot;10\&quot;,\n&gt;&gt;&gt;   38:         \&quot;publicNetworkAccess\&quot;: \&quot;Enabled\&quot;,\n      39:         \&quot;zoneRedundancy\&quot;: \&quot;Disabled\&quot;\n      40:       }\n      41:     }\n      42:   ]\n      43: }&quot;, &quot;highlighted_line_content&quot;: &quot;\&quot;publicNetworkAccess\&quot;: \&quot;Enabled\&quot;,&quot;}
                </script>
                <script type="application/json" data-finding-context="KustoScripts.template.json_55" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;KustoScripts.template.json&quot;, &quot;line_number&quot;: 55, &quot;total_lines&quot;: 185, &quot;start_line&quot;: 1, &quot;end_line&quot;: 155, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;{&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;  \&quot;$schema\&quot;: \&quot;https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;  \&quot;contentVersion\&quot;: \&quot;*******\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;  \&quot;metadata\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;    \&quot;_generator\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;bicep\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;      \&quot;version\&quot;: \&quot;0.34.44.8038\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;      \&quot;templateHash\&quot;: \&quot;1805723795016029729\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;  },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;  \&quot;parameters\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;    \&quot;kustoClusterName\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;string\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;      \&quot;defaultValue\&quot;: \&quot;cloudsprouttest\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;    \&quot;kustoDatabaseName\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;string\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;      \&quot;defaultValue\&quot;: \&quot;Telemetry\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;    \&quot;deploymentStartTime\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;string\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;      \&quot;defaultValue\&quot;: \&quot;[utcNow()]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;    \&quot;effectiveDateTime\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;string\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;      \&quot;defaultValue\&quot;: \&quot;[dateTimeAdd(utcNow(), '-P14D')]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;    \&quot;effectiveDateTimePlaceholder\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;string\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;      \&quot;defaultValue\&quot;: \&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;  },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;  \&quot;variables\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;    \&quot;$fxv#0\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient retrieval of the latest LabelData records\\\&quot;\\r\\n  ) LabelDataView on table Snapshots\\r\\n{\\r\\n  Snapshots\\r\\n    | where entity == \\\&quot;LabelData\\\&quot; and Role =~ \\\&quot;BE\\\&quot;\\r\\n    | extend moniker = strcat(namespace, \\\&quot;.\\\&quot;, label)\\r\\n    | project-rename exported_at = env_time\\r\\n    | project \\r\\n      exported_at\\r\\n    , moniker\\r\\n    , label\\r\\n    , deprecated\\r\\n    , namespace\\r\\n    , last_updated\\r\\n    , date_created\\r\\n    , version\\r\\n    , tags\\r\\n    , label_imports\\r\\n    , schema_type\\r\\n    , type_constraint\\r\\n    , integrations\\r\\n    , allow_chaining\\r\\n    , allow_empty_string\\r\\n    , has_projected_labels\\r\\n    , is_projected_label\\r\\n    | extend _version = parse_version(version)\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by moniker, version\\r\\n}\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;    \&quot;$fxv#1\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient debugging of the unique incoming snapshot events\\\&quot;\\r\\n  ) LabelDataViewDebug on table Snapshots\\r\\n{\\r\\n  Snapshots\\r\\n    | where entity == \\\&quot;LabelData\\\&quot; and Role =~ \\\&quot;BE\\\&quot;\\r\\n    | extend moniker = strcat(namespace, \\\&quot;.\\\&quot;, label)\\r\\n    | project-rename exported_at = env_time\\r\\n    | project \\r\\n      exported_at\\r\\n    , moniker\\r\\n    , label\\r\\n    , deprecated\\r\\n    , namespace\\r\\n    , last_updated\\r\\n    , date_created\\r\\n    , version\\r\\n    , tags\\r\\n    , label_imports\\r\\n    , schema_type\\r\\n    , type_constraint\\r\\n    , integrations\\r\\n    , allow_chaining\\r\\n    , allow_empty_string\\r\\n    , has_projected_labels\\r\\n    , is_projected_label\\r\\n    , kusto_export_mechanism\\r\\n    , Environment\\r\\n    , RoleInstance\\r\\n    | extend _version = parse_version(version)\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by moniker, version, kusto_export_mechanism, Environment, RoleInstance\\r\\n}\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;    \&quot;$fxv#10\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n// data upstream source: https://msazure.visualstudio.com/One/_git/OneDCMT?path=/src/Datacenter\\r\\n.create-or-alter function with (\\r\\n  docstring = \\\&quot;Given a list of monikers and a specified region, return the latest value for each moniker. If a region-specific value is not available, the cloud-scoped value is returned. Region can be referenced by using any of its unique aliases. If no environment scope is provided, 'prod' is assumed.\\\&quot;,\\r\\n  folder = \\\&quot;LabelValuesByUniqueRegionEntity\\\&quot;\\r\\n) fun_FetchLabelValueByRegionsInJson (\\r\\n  _input_monikers:dynamic,\\r\\n  _input_scope_region:string,\\r\\n  _input_scope_env:string=\\\&quot;prod\\\&quot;\\r\\n)\\r\\n{\\r\\n  // Convert input dynamic into a datatable\\r\\n  let _input_monikers_data = print _input_monikers\\r\\n      | mv-expand moniker = _input_monikers\\r\\n      | extend moniker = tolower(tostring(moniker))\\r\\n      | project moniker;\\r\\n  // Normalize the inputs\\r\\n  let region_record = materialize(\\r\\n      fun_LookupByUniqueAttribute_Region(_input_scope_region)\\r\\n          | take 1\\r\\n      );\\r\\n  let _normalized_scope_region = toscalar(\\r\\n      region_record\\r\\n          | project ID\\r\\n      );\\r\\n  // derive the cloud name from the input region:\\r\\n  let _normalized_scope_cloud = toscalar(\\r\\n      region_record\\r\\n          | project ParentCloud\\r\\n      );\\r\\n  // Step 1: LabelValueDataView contains every version of every moniker at every scope. \\r\\n  // first thing we need to do is only grab the latest version of each moniker:\\r\\n  let latest_values = materialize(\\r\\n      materialized_view('LabelValueDataView')\\r\\n          | extend scope_geo_type=tolower(scope_geo_type)\\r\\n          , scope_geo_value=tolower(scope_geo_value)\\r\\n          | where isnotempty(scope_geo_type) and isnotempty(scope_geo_value)\\r\\n          | extend _version = parse_version(version)\\r\\n          | summarize arg_max(_version, *) by moniker, scope_geo_type, scope_geo_value, scope_env\\r\\n      );\\r\\n  // Step 2: partition cloud-scoped values into a separate table:\\r\\n  let cloud_values = materialize(\\r\\n      latest_values\\r\\n          | where scope_geo_type =~ \\\&quot;cloud\\\&quot; and scope_geo_value =~ _normalized_scope_cloud and scope_env =~ _input_scope_env\\r\\n      );\\r\\n  // Step 3: partition region-scoped values into a separate table:\\r\\n  let region_values = materialize(\\r\\n      latest_values\\r\\n          | where scope_geo_type =~ \\\&quot;region\\\&quot; and scope_geo_value =~ _normalized_scope_region and scope_env =~ _input_scope_env\\r\\n      );\\r\\n  // Step 4: Perform the actual query. At a high level, we will give precedence to region-scoped values. If\\r\\n  // a moniker does not have a value for the region, it defaults to the cloud-scoped value:\\r\\n  _input_monikers_data\\r\\n      // Obtain the region-scoped value for each moniker, if present\\r\\n      | lookup kind=leftouter (region_values) on moniker\\r\\n      // Obtain the cloud-scoped value for each moniker, if present. Since this is a second lookup, we'll get\\r\\n      // a second column with \\\&quot;1\\\&quot; appended to the name\\r\\n      | lookup kind=leftouter (cloud_values) on moniker\\r\\n      // Merge the columns. If value is present in \\\&quot;region\\\&quot; column, use it. Otherwise, use the \\\&quot;cloud\\\&quot; column (columns with \\\&quot;1\\\&quot; suffix):\\r\\n      | extend value = iff(isnotempty(rendered_value), rendered_value, rendered_value1)\\r\\n      | extend scope_env = iff(isnotempty(scope_env), scope_env, scope_env1)\\r\\n      | extend version = iff(isnotempty(version), version, version1)\\r\\n      | extend scope_geo_type = iff(isnotempty(scope_geo_type), scope_geo_type, scope_geo_type1)\\r\\n      | extend scope_geo_value = iff(isnotempty(scope_geo_value), scope_geo_value, scope_geo_value1)\\r\\n      | project moniker, scope_geo_type, scope_geo_value, scope_env, value, version\\r\\n      | order by moniker asc\\r\\n      // Convert to a single json blob\\r\\n      | project moniker, value\\r\\n      | summarize jsonResult = make_list(bag_pack(\\\&quot;key\\\&quot;, moniker, \\\&quot;value\\\&quot;, value))\\r\\n      | project jsonResult\\r\\n}\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;    \&quot;$fxv#11\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n// data upstream source: https://msazure.visualstudio.com/One/_git/OneDCMT?path=/src/Datacenter\\r\\n.create-or-alter function with (\\r\\n  docstring = \\\&quot;Given a list of monikers and a specified region, return the latest value for each moniker. If a region-specific value is not available, the cloud-scoped value is returned. Region can be referenced by using any of its unique aliases. If no environment scope is provided, 'prod' is assumed.\\\&quot;,\\r\\n  folder = \\\&quot;LabelValuesByUniqueRegionEntity\\\&quot;\\r\\n) fetchLabelValueByRegionsInJson (\\r\\n  _input_monikers:dynamic,\\r\\n  _input_scope_region:string,\\r\\n  _input_scope_env:string=\\\&quot;prod\\\&quot;\\r\\n)\\r\\n{\\r\\n    // Note: the function \\\&quot;fetchLabelValuesByRegionsInJson\\\&quot; is currently used in SharedSettings, \\r\\n    // wrapping it into the new naming convention for compatibility:\\r\\n    fun_FetchLabelValueByRegionsInJson(_input_monikers, _input_scope_region, _input_scope_env);\\r\\n}\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;    \&quot;$fxv#2\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient retrieval of each LabelValueData record\\\&quot;\\r\\n  ) LabelValueDataView on table Snapshots\\r\\n{\\r\\n  Snapshots\\r\\n    | where entity == \\\&quot;LabelValueData\\\&quot; and Role =~ \\\&quot;BE\\\&quot;\\r\\n    | extend moniker = strcat(namespace, \\\&quot;.\\\&quot;, label)\\r\\n    | extend scope_env = tolower(scope_env)\\r\\n    | extend scope_geo_type = tolower(scope_geo_type)\\r\\n    | extend scope_geo_value = tolower(scope_geo_value)\\r\\n    | project-rename exported_at = env_time\\r\\n    | project \\r\\n      moniker\\r\\n    , exported_at\\r\\n    , env_dt_traceId\\r\\n    , env_dt_spanId\\r\\n    , namespace\\r\\n    , label\\r\\n    , scope_cloud\\r\\n    , scope_env\\r\\n    , scope_geo_type\\r\\n    , scope_geo_value\\r\\n    , last_updated\\r\\n    , templated_value\\r\\n    , rendered_value\\r\\n    , modified_by\\r\\n    , version\\r\\n    , value_type\\r\\n    | extend _version = parse_version(version)\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by moniker, scope_env, scope_geo_type, scope_geo_value, version\\r\\n}\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;    \&quot;$fxv#3\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient debugging of the unique incoming snapshot events\\\&quot;\\r\\n  ) LabelValueDataViewDebug on table Snapshots\\r\\n{\\r\\n  Snapshots\\r\\n    | where entity == \\\&quot;LabelValueData\\\&quot; and Role =~ \\\&quot;BE\\\&quot;\\r\\n    | extend moniker = strcat(namespace, \\\&quot;.\\\&quot;, label)\\r\\n    | extend scope_env = tolower(scope_env)\\r\\n    | extend scope_geo_type = tolower(scope_geo_type)\\r\\n    | extend scope_geo_value = tolower(scope_geo_value)\\r\\n    | project-rename exported_at = env_time\\r\\n    | project \\r\\n      moniker\\r\\n    , exported_at\\r\\n    , env_dt_traceId\\r\\n    , env_dt_spanId\\r\\n    , namespace\\r\\n    , label\\r\\n    , scope_cloud\\r\\n    , scope_env\\r\\n    , scope_geo_type\\r\\n    , scope_geo_value\\r\\n    , last_updated\\r\\n    , templated_value\\r\\n    , rendered_value\\r\\n    , modified_by\\r\\n    , version\\r\\n    , value_type\\r\\n    , kusto_export_mechanism\\r\\n    , Environment\\r\\n    , RoleInstance\\r\\n    | extend _version = parse_version(version)\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by moniker, scope_env, scope_geo_type, scope_geo_value, version, kusto_export_mechanism, Environment, RoleInstance\\r\\n}\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;    \&quot;$fxv#4\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient retrieval of the latest NamespaceData records\\\&quot;\\r\\n  ) NamespaceDataView on table Snapshots\\r\\n{\\r\\n  Snapshots\\r\\n    | where entity == \\\&quot;NamespaceData\\\&quot; and Role =~ \\\&quot;BE\\\&quot;\\r\\n    | project-rename exported_at = env_time\\r\\n    | project \\r\\n      exported_at\\r\\n    , namespace\\r\\n    , owner_service_tree_id\\r\\n    , enabled\\r\\n    , version\\r\\n    , email\\r\\n    , icm_teams\\r\\n    , documentation_urls\\r\\n    , service_owners\\r\\n    | extend _version = parse_version(version)\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by namespace, version\\r\\n}\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;    \&quot;$fxv#5\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient debugging of the unique incoming snapshot events\\\&quot;\\r\\n  ) NamespaceDataViewDebug on table Snapshots\\r\\n{\\r\\n  Snapshots\\r\\n    | where entity == \\\&quot;NamespaceData\\\&quot; and Role =~ \\\&quot;BE\\\&quot;\\r\\n    | project-rename exported_at = env_time\\r\\n    | project \\r\\n      exported_at\\r\\n    , namespace\\r\\n    , owner_service_tree_id\\r\\n    , enabled\\r\\n    , version\\r\\n    , email\\r\\n    , icm_teams\\r\\n    , documentation_urls\\r\\n    , service_owners\\r\\n    , kusto_export_mechanism\\r\\n    , Environment\\r\\n    , RoleInstance\\r\\n    | extend _version = parse_version(version)\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by namespace, version, kusto_export_mechanism, Environment, RoleInstance\\r\\n}\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;    \&quot;$fxv#6\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n// data upstream source: https://msazure.visualstudio.com/One/_git/OneDCMT?path=/src/Datacenter\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient lookup of the latest DCMT GeoX Metadata entities\\\&quot;\\r\\n  ) TopologyMetadataView on table TopologyMetadata\\r\\n{\\r\\n  TopologyMetadata\\r\\n    | where Role =~ \\\&quot;BE\\\&quot;\\r\\n    | project-rename exported_at = env_time\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by GUID\\r\\n}\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;    \&quot;$fxv#7\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n// data upstream source: https://msazure.visualstudio.com/One/_git/OneDCMT?path=/src/Datacenter\\r\\n.create-or-alter function with (\\r\\n  docstring = \\\&quot;Given a input string, search all known globally-unique columns for a match\\\&quot;,\\r\\n  folder = \\\&quot;Topology\\\&quot;\\r\\n) fun_LookupByUniqueAttribute_Cloud (\\r\\n  _input_attribute_value:string\\r\\n) \\r\\n{\\r\\n  materialized_view(\\\&quot;TopologyMetadataView\\\&quot;)\\r\\n    | where Entity =~ \\\&quot;CloudMetadata\\\&quot;\\r\\n    // shim: AlternateName is not defined for most clouds. In clouds like Mooncake, since we never\\r\\n    // have a value for this column, geneva never shows it in GCS in the TopologyMetadata event, thus\\r\\n    // GDS's auto schema never creates a column for it. This is a way to gracefully handle missing columns:\\r\\n    | extend EmptyColumn = \\\&quot;\\\&quot;\\r\\n    | extend AlternateName = column_ifexists(\\\&quot;AlternateName\\\&quot;, EmptyColumn)\\r\\n    | project-away EmptyColumn\\r\\n    // search all columns for a case-insensitive match\\r\\n    | where Name =~ _input_attribute_value\\r\\n      or GUID =~ _input_attribute_value\\r\\n      or AlternateName =~ _input_attribute_value\\r\\n}\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 44, &quot;content&quot;: &quot;    \&quot;$fxv#8\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n// data upstream source: https://msazure.visualstudio.com/One/_git/OneDCMT?path=/src/Datacenter\\r\\n.create-or-alter function with (\\r\\n  docstring = \\\&quot;Given a input string, search all known globally-unique columns for a match. If _input_scope_cloud is specified, the search will be targeted to all records defined within the matching ParentCloud\\\&quot;,\\r\\n  folder = \\\&quot;Topology\\\&quot;\\r\\n) fun_LookupByUniqueAttribute_Geography (\\r\\n  _input_attribute_value:string,\\r\\n  _input_scope_cloud:string=\\\&quot;\\\&quot;\\r\\n) \\r\\n{\\r\\n  materialized_view(\\\&quot;TopologyMetadataView\\\&quot;)\\r\\n    | where Entity =~ \\\&quot;GeographyMetadata\\\&quot;\\r\\n    // If scope cloud is provided, only search for unique records within that cloud\\r\\n    | where isempty(_input_scope_cloud) or ParentCloud =~ _input_scope_cloud\\r\\n    // search all columns for a case-insensitive match\\r\\n    | where Name =~ _input_attribute_value\\r\\n      or GUID =~ _input_attribute_value\\r\\n}\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 45, &quot;content&quot;: &quot;    \&quot;$fxv#9\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n// data upstream source: https://msazure.visualstudio.com/One/_git/OneDCMT?path=/src/Datacenter\\r\\n.create-or-alter function with (\\r\\n  docstring = \\\&quot;Given a input string, search all known globally-unique columns for a match. If _input_scope_cloud is specified, the search will be targeted to all records defined within the matching ParentCloud\\\&quot;,\\r\\n  folder = \\\&quot;Topology\\\&quot;\\r\\n) fun_LookupByUniqueAttribute_Region (\\r\\n  _input_attribute_value:string,\\r\\n  _input_scope_cloud:string=\\\&quot;\\\&quot;\\r\\n) \\r\\n{\\r\\n  materialized_view(\\\&quot;TopologyMetadataView\\\&quot;)\\r\\n    | where Entity =~ \\\&quot;RegionMetadata\\\&quot;\\r\\n    // If scope cloud is provided, only search for unique records within that cloud\\r\\n    | where isempty(_input_scope_cloud) or ParentCloud =~ _input_scope_cloud\\r\\n    // search all columns for a case-insensitive match\\r\\n    | where ID =~ _input_attribute_value\\r\\n      or ArmLocation =~ _input_attribute_value\\r\\n      or FriendlyName =~ _input_attribute_value\\r\\n      or GUID =~ _input_attribute_value\\r\\n}\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 46, &quot;content&quot;: &quot;  },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 47, &quot;content&quot;: &quot;  \&quot;resources\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 48, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 49, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 50, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 51, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'LabelDataView')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 52, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 53, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#0'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 54, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 55, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 56, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 57, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 58, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 59, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 60, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 61, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'LabelDataViewDebug')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 62, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 63, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#1'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 64, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 65, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 66, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 67, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 68, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 69, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 70, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 71, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'LabelValueDataView')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 72, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 73, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#2'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 74, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 75, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 76, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 77, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 78, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 79, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 80, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 81, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'LabelValueDataViewDebug')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 82, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 83, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#3'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 84, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 85, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 86, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 87, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 88, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 89, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 90, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 91, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'NamespaceDataView')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 92, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 93, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#4'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 94, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 95, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 96, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 97, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 98, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 99, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 100, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 101, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'NamespaceDataViewDebug')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 102, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 103, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#5'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 104, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 105, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 106, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 107, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 108, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 109, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 110, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 111, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'TopologyMetadataView')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 112, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 113, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#6'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 114, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 115, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 116, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 117, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 118, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 119, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 120, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 121, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_LookupByUniqueAttribute_Cloud')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 122, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 123, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#7')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 124, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 125, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 126, &quot;content&quot;: &quot;      },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 127, &quot;content&quot;: &quot;      \&quot;dependsOn\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 128, &quot;content&quot;: &quot;        \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'TopologyMetadataView')]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 129, &quot;content&quot;: &quot;      ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 130, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 131, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 132, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 133, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 134, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_LookupByUniqueAttribute_Geography')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 135, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 136, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#8')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 137, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 138, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 139, &quot;content&quot;: &quot;      },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 140, &quot;content&quot;: &quot;      \&quot;dependsOn\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 141, &quot;content&quot;: &quot;        \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'TopologyMetadataView')]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 142, &quot;content&quot;: &quot;      ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 143, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 144, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 145, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 146, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 147, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_LookupByUniqueAttribute_Region')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 148, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 149, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#9')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 150, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 151, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 152, &quot;content&quot;: &quot;      },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 153, &quot;content&quot;: &quot;      \&quot;dependsOn\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 154, &quot;content&quot;: &quot;        \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'TopologyMetadataView')]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 155, &quot;content&quot;: &quot;      ]&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: {\n       2:   \&quot;$schema\&quot;: \&quot;https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#\&quot;,\n       3:   \&quot;contentVersion\&quot;: \&quot;*******\&quot;,\n       4:   \&quot;metadata\&quot;: {\n       5:     \&quot;_generator\&quot;: {\n       6:       \&quot;name\&quot;: \&quot;bicep\&quot;,\n       7:       \&quot;version\&quot;: \&quot;0.34.44.8038\&quot;,\n       8:       \&quot;templateHash\&quot;: \&quot;1805723795016029729\&quot;\n       9:     }\n      10:   },\n      11:   \&quot;parameters\&quot;: {\n      12:     \&quot;kustoClusterName\&quot;: {\n      13:       \&quot;type\&quot;: \&quot;string\&quot;,\n      14:       \&quot;defaultValue\&quot;: \&quot;cloudsprouttest\&quot;\n      15:     },\n      16:     \&quot;kustoDatabaseName\&quot;: {\n      17:       \&quot;type\&quot;: \&quot;string\&quot;,\n      18:       \&quot;defaultValue\&quot;: \&quot;Telemetry\&quot;\n      19:     },\n      20:     \&quot;deploymentStartTime\&quot;: {\n      21:       \&quot;type\&quot;: \&quot;string\&quot;,\n      22:       \&quot;defaultValue\&quot;: \&quot;[utcNow()]\&quot;\n      23:     },\n      24:     \&quot;effectiveDateTime\&quot;: {\n      25:       \&quot;type\&quot;: \&quot;string\&quot;,\n      26:       \&quot;defaultValue\&quot;: \&quot;[dateTimeAdd(utcNow(), '-P14D')]\&quot;\n      27:     },\n      28:     \&quot;effectiveDateTimePlaceholder\&quot;: {\n      29:       \&quot;type\&quot;: \&quot;string\&quot;,\n      30:       \&quot;defaultValue\&quot;: \&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\&quot;\n      31:     }\n      32:   },\n      33:   \&quot;variables\&quot;: {\n      34:     \&quot;$fxv#0\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient retrieval of the latest LabelData records\\\&quot;\\r\\n  ) LabelDataView on table Snapshots\\r\\n{\\r\\n  Snapshots\\r\\n    | where entity == \\\&quot;LabelData\\\&quot; and Role =~ \\\&quot;BE\\\&quot;\\r\\n    | extend moniker = strcat(namespace, \\\&quot;.\\\&quot;, label)\\r\\n    | project-rename exported_at = env_time\\r\\n    | project \\r\\n      exported_at\\r\\n    , moniker\\r\\n    , label\\r\\n    , deprecated\\r\\n    , namespace\\r\\n    , last_updated\\r\\n    , date_created\\r\\n    , version\\r\\n    , tags\\r\\n    , label_imports\\r\\n    , schema_type\\r\\n    , type_constraint\\r\\n    , integrations\\r\\n    , allow_chaining\\r\\n    , allow_empty_string\\r\\n    , has_projected_labels\\r\\n    , is_projected_label\\r\\n    | extend _version = parse_version(version)\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by moniker, version\\r\\n}\&quot;,\n      35:     \&quot;$fxv#1\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient debugging of the unique incoming snapshot events\\\&quot;\\r\\n  ) LabelDataViewDebug on table Snapshots\\r\\n{\\r\\n  Snapshots\\r\\n    | where entity == \\\&quot;LabelData\\\&quot; and Role =~ \\\&quot;BE\\\&quot;\\r\\n    | extend moniker = strcat(namespace, \\\&quot;.\\\&quot;, label)\\r\\n    | project-rename exported_at = env_time\\r\\n    | project \\r\\n      exported_at\\r\\n    , moniker\\r\\n    , label\\r\\n    , deprecated\\r\\n    , namespace\\r\\n    , last_updated\\r\\n    , date_created\\r\\n    , version\\r\\n    , tags\\r\\n    , label_imports\\r\\n    , schema_type\\r\\n    , type_constraint\\r\\n    , integrations\\r\\n    , allow_chaining\\r\\n    , allow_empty_string\\r\\n    , has_projected_labels\\r\\n    , is_projected_label\\r\\n    , kusto_export_mechanism\\r\\n    , Environment\\r\\n    , RoleInstance\\r\\n    | extend _version = parse_version(version)\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by moniker, version, kusto_export_mechanism, Environment, RoleInstance\\r\\n}\&quot;,\n      36:     \&quot;$fxv#10\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n// data upstream source: https://msazure.visualstudio.com/One/_git/OneDCMT?path=/src/Datacenter\\r\\n.create-or-alter function with (\\r\\n  docstring = \\\&quot;Given a list of monikers and a specified region, return the latest value for each moniker. If a region-specific value is not available, the cloud-scoped value is returned. Region can be referenced by using any of its unique aliases. If no environment scope is provided, 'prod' is assumed.\\\&quot;,\\r\\n  folder = \\\&quot;LabelValuesByUniqueRegionEntity\\\&quot;\\r\\n) fun_FetchLabelValueByRegionsInJson (\\r\\n  _input_monikers:dynamic,\\r\\n  _input_scope_region:string,\\r\\n  _input_scope_env:string=\\\&quot;prod\\\&quot;\\r\\n)\\r\\n{\\r\\n  // Convert input dynamic into a datatable\\r\\n  let _input_monikers_data = print _input_monikers\\r\\n      | mv-expand moniker = _input_monikers\\r\\n      | extend moniker = tolower(tostring(moniker))\\r\\n      | project moniker;\\r\\n  // Normalize the inputs\\r\\n  let region_record = materialize(\\r\\n      fun_LookupByUniqueAttribute_Region(_input_scope_region)\\r\\n          | take 1\\r\\n      );\\r\\n  let _normalized_scope_region = toscalar(\\r\\n      region_record\\r\\n          | project ID\\r\\n      );\\r\\n  // derive the cloud name from the input region:\\r\\n  let _normalized_scope_cloud = toscalar(\\r\\n      region_record\\r\\n          | project ParentCloud\\r\\n      );\\r\\n  // Step 1: LabelValueDataView contains every version of every moniker at every scope. \\r\\n  // first thing we need to do is only grab the latest version of each moniker:\\r\\n  let latest_values = materialize(\\r\\n      materialized_view('LabelValueDataView')\\r\\n          | extend scope_geo_type=tolower(scope_geo_type)\\r\\n          , scope_geo_value=tolower(scope_geo_value)\\r\\n          | where isnotempty(scope_geo_type) and isnotempty(scope_geo_value)\\r\\n          | extend _version = parse_version(version)\\r\\n          | summarize arg_max(_version, *) by moniker, scope_geo_type, scope_geo_value, scope_env\\r\\n      );\\r\\n  // Step 2: partition cloud-scoped values into a separate table:\\r\\n  let cloud_values = materialize(\\r\\n      latest_values\\r\\n          | where scope_geo_type =~ \\\&quot;cloud\\\&quot; and scope_geo_value =~ _normalized_scope_cloud and scope_env =~ _input_scope_env\\r\\n      );\\r\\n  // Step 3: partition region-scoped values into a separate table:\\r\\n  let region_values = materialize(\\r\\n      latest_values\\r\\n          | where scope_geo_type =~ \\\&quot;region\\\&quot; and scope_geo_value =~ _normalized_scope_region and scope_env =~ _input_scope_env\\r\\n      );\\r\\n  // Step 4: Perform the actual query. At a high level, we will give precedence to region-scoped values. If\\r\\n  // a moniker does not have a value for the region, it defaults to the cloud-scoped value:\\r\\n  _input_monikers_data\\r\\n      // Obtain the region-scoped value for each moniker, if present\\r\\n      | lookup kind=leftouter (region_values) on moniker\\r\\n      // Obtain the cloud-scoped value for each moniker, if present. Since this is a second lookup, we'll get\\r\\n      // a second column with \\\&quot;1\\\&quot; appended to the name\\r\\n      | lookup kind=leftouter (cloud_values) on moniker\\r\\n      // Merge the columns. If value is present in \\\&quot;region\\\&quot; column, use it. Otherwise, use the \\\&quot;cloud\\\&quot; column (columns with \\\&quot;1\\\&quot; suffix):\\r\\n      | extend value = iff(isnotempty(rendered_value), rendered_value, rendered_value1)\\r\\n      | extend scope_env = iff(isnotempty(scope_env), scope_env, scope_env1)\\r\\n      | extend version = iff(isnotempty(version), version, version1)\\r\\n      | extend scope_geo_type = iff(isnotempty(scope_geo_type), scope_geo_type, scope_geo_type1)\\r\\n      | extend scope_geo_value = iff(isnotempty(scope_geo_value), scope_geo_value, scope_geo_value1)\\r\\n      | project moniker, scope_geo_type, scope_geo_value, scope_env, value, version\\r\\n      | order by moniker asc\\r\\n      // Convert to a single json blob\\r\\n      | project moniker, value\\r\\n      | summarize jsonResult = make_list(bag_pack(\\\&quot;key\\\&quot;, moniker, \\\&quot;value\\\&quot;, value))\\r\\n      | project jsonResult\\r\\n}\&quot;,\n      37:     \&quot;$fxv#11\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n// data upstream source: https://msazure.visualstudio.com/One/_git/OneDCMT?path=/src/Datacenter\\r\\n.create-or-alter function with (\\r\\n  docstring = \\\&quot;Given a list of monikers and a specified region, return the latest value for each moniker. If a region-specific value is not available, the cloud-scoped value is returned. Region can be referenced by using any of its unique aliases. If no environment scope is provided, 'prod' is assumed.\\\&quot;,\\r\\n  folder = \\\&quot;LabelValuesByUniqueRegionEntity\\\&quot;\\r\\n) fetchLabelValueByRegionsInJson (\\r\\n  _input_monikers:dynamic,\\r\\n  _input_scope_region:string,\\r\\n  _input_scope_env:string=\\\&quot;prod\\\&quot;\\r\\n)\\r\\n{\\r\\n    // Note: the function \\\&quot;fetchLabelValuesByRegionsInJson\\\&quot; is currently used in SharedSettings, \\r\\n    // wrapping it into the new naming convention for compatibility:\\r\\n    fun_FetchLabelValueByRegionsInJson(_input_monikers, _input_scope_region, _input_scope_env);\\r\\n}\&quot;,\n      38:     \&quot;$fxv#2\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient retrieval of each LabelValueData record\\\&quot;\\r\\n  ) LabelValueDataView on table Snapshots\\r\\n{\\r\\n  Snapshots\\r\\n    | where entity == \\\&quot;LabelValueData\\\&quot; and Role =~ \\\&quot;BE\\\&quot;\\r\\n    | extend moniker = strcat(namespace, \\\&quot;.\\\&quot;, label)\\r\\n    | extend scope_env = tolower(scope_env)\\r\\n    | extend scope_geo_type = tolower(scope_geo_type)\\r\\n    | extend scope_geo_value = tolower(scope_geo_value)\\r\\n    | project-rename exported_at = env_time\\r\\n    | project \\r\\n      moniker\\r\\n    , exported_at\\r\\n    , env_dt_traceId\\r\\n    , env_dt_spanId\\r\\n    , namespace\\r\\n    , label\\r\\n    , scope_cloud\\r\\n    , scope_env\\r\\n    , scope_geo_type\\r\\n    , scope_geo_value\\r\\n    , last_updated\\r\\n    , templated_value\\r\\n    , rendered_value\\r\\n    , modified_by\\r\\n    , version\\r\\n    , value_type\\r\\n    | extend _version = parse_version(version)\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by moniker, scope_env, scope_geo_type, scope_geo_value, version\\r\\n}\&quot;,\n      39:     \&quot;$fxv#3\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient debugging of the unique incoming snapshot events\\\&quot;\\r\\n  ) LabelValueDataViewDebug on table Snapshots\\r\\n{\\r\\n  Snapshots\\r\\n    | where entity == \\\&quot;LabelValueData\\\&quot; and Role =~ \\\&quot;BE\\\&quot;\\r\\n    | extend moniker = strcat(namespace, \\\&quot;.\\\&quot;, label)\\r\\n    | extend scope_env = tolower(scope_env)\\r\\n    | extend scope_geo_type = tolower(scope_geo_type)\\r\\n    | extend scope_geo_value = tolower(scope_geo_value)\\r\\n    | project-rename exported_at = env_time\\r\\n    | project \\r\\n      moniker\\r\\n    , exported_at\\r\\n    , env_dt_traceId\\r\\n    , env_dt_spanId\\r\\n    , namespace\\r\\n    , label\\r\\n    , scope_cloud\\r\\n    , scope_env\\r\\n    , scope_geo_type\\r\\n    , scope_geo_value\\r\\n    , last_updated\\r\\n    , templated_value\\r\\n    , rendered_value\\r\\n    , modified_by\\r\\n    , version\\r\\n    , value_type\\r\\n    , kusto_export_mechanism\\r\\n    , Environment\\r\\n    , RoleInstance\\r\\n    | extend _version = parse_version(version)\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by moniker, scope_env, scope_geo_type, scope_geo_value, version, kusto_export_mechanism, Environment, RoleInstance\\r\\n}\&quot;,\n      40:     \&quot;$fxv#4\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient retrieval of the latest NamespaceData records\\\&quot;\\r\\n  ) NamespaceDataView on table Snapshots\\r\\n{\\r\\n  Snapshots\\r\\n    | where entity == \\\&quot;NamespaceData\\\&quot; and Role =~ \\\&quot;BE\\\&quot;\\r\\n    | project-rename exported_at = env_time\\r\\n    | project \\r\\n      exported_at\\r\\n    , namespace\\r\\n    , owner_service_tree_id\\r\\n    , enabled\\r\\n    , version\\r\\n    , email\\r\\n    , icm_teams\\r\\n    , documentation_urls\\r\\n    , service_owners\\r\\n    | extend _version = parse_version(version)\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by namespace, version\\r\\n}\&quot;,\n      41:     \&quot;$fxv#5\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient debugging of the unique incoming snapshot events\\\&quot;\\r\\n  ) NamespaceDataViewDebug on table Snapshots\\r\\n{\\r\\n  Snapshots\\r\\n    | where entity == \\\&quot;NamespaceData\\\&quot; and Role =~ \\\&quot;BE\\\&quot;\\r\\n    | project-rename exported_at = env_time\\r\\n    | project \\r\\n      exported_at\\r\\n    , namespace\\r\\n    , owner_service_tree_id\\r\\n    , enabled\\r\\n    , version\\r\\n    , email\\r\\n    , icm_teams\\r\\n    , documentation_urls\\r\\n    , service_owners\\r\\n    , kusto_export_mechanism\\r\\n    , Environment\\r\\n    , RoleInstance\\r\\n    | extend _version = parse_version(version)\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by namespace, version, kusto_export_mechanism, Environment, RoleInstance\\r\\n}\&quot;,\n      42:     \&quot;$fxv#6\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n// data upstream source: https://msazure.visualstudio.com/One/_git/OneDCMT?path=/src/Datacenter\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient lookup of the latest DCMT GeoX Metadata entities\\\&quot;\\r\\n  ) TopologyMetadataView on table TopologyMetadata\\r\\n{\\r\\n  TopologyMetadata\\r\\n    | where Role =~ \\\&quot;BE\\\&quot;\\r\\n    | project-rename exported_at = env_time\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by GUID\\r\\n}\&quot;,\n      43:     \&quot;$fxv#7\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n// data upstream source: https://msazure.visualstudio.com/One/_git/OneDCMT?path=/src/Datacenter\\r\\n.create-or-alter function with (\\r\\n  docstring = \\\&quot;Given a input string, search all known globally-unique columns for a match\\\&quot;,\\r\\n  folder = \\\&quot;Topology\\\&quot;\\r\\n) fun_LookupByUniqueAttribute_Cloud (\\r\\n  _input_attribute_value:string\\r\\n) \\r\\n{\\r\\n  materialized_view(\\\&quot;TopologyMetadataView\\\&quot;)\\r\\n    | where Entity =~ \\\&quot;CloudMetadata\\\&quot;\\r\\n    // shim: AlternateName is not defined for most clouds. In clouds like Mooncake, since we never\\r\\n    // have a value for this column, geneva never shows it in GCS in the TopologyMetadata event, thus\\r\\n    // GDS's auto schema never creates a column for it. This is a way to gracefully handle missing columns:\\r\\n    | extend EmptyColumn = \\\&quot;\\\&quot;\\r\\n    | extend AlternateName = column_ifexists(\\\&quot;AlternateName\\\&quot;, EmptyColumn)\\r\\n    | project-away EmptyColumn\\r\\n    // search all columns for a case-insensitive match\\r\\n    | where Name =~ _input_attribute_value\\r\\n      or GUID =~ _input_attribute_value\\r\\n      or AlternateName =~ _input_attribute_value\\r\\n}\&quot;,\n      44:     \&quot;$fxv#8\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n// data upstream source: https://msazure.visualstudio.com/One/_git/OneDCMT?path=/src/Datacenter\\r\\n.create-or-alter function with (\\r\\n  docstring = \\\&quot;Given a input string, search all known globally-unique columns for a match. If _input_scope_cloud is specified, the search will be targeted to all records defined within the matching ParentCloud\\\&quot;,\\r\\n  folder = \\\&quot;Topology\\\&quot;\\r\\n) fun_LookupByUniqueAttribute_Geography (\\r\\n  _input_attribute_value:string,\\r\\n  _input_scope_cloud:string=\\\&quot;\\\&quot;\\r\\n) \\r\\n{\\r\\n  materialized_view(\\\&quot;TopologyMetadataView\\\&quot;)\\r\\n    | where Entity =~ \\\&quot;GeographyMetadata\\\&quot;\\r\\n    // If scope cloud is provided, only search for unique records within that cloud\\r\\n    | where isempty(_input_scope_cloud) or ParentCloud =~ _input_scope_cloud\\r\\n    // search all columns for a case-insensitive match\\r\\n    | where Name =~ _input_attribute_value\\r\\n      or GUID =~ _input_attribute_value\\r\\n}\&quot;,\n      45:     \&quot;$fxv#9\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n// data upstream source: https://msazure.visualstudio.com/One/_git/OneDCMT?path=/src/Datacenter\\r\\n.create-or-alter function with (\\r\\n  docstring = \\\&quot;Given a input string, search all known globally-unique columns for a match. If _input_scope_cloud is specified, the search will be targeted to all records defined within the matching ParentCloud\\\&quot;,\\r\\n  folder = \\\&quot;Topology\\\&quot;\\r\\n) fun_LookupByUniqueAttribute_Region (\\r\\n  _input_attribute_value:string,\\r\\n  _input_scope_cloud:string=\\\&quot;\\\&quot;\\r\\n) \\r\\n{\\r\\n  materialized_view(\\\&quot;TopologyMetadataView\\\&quot;)\\r\\n    | where Entity =~ \\\&quot;RegionMetadata\\\&quot;\\r\\n    // If scope cloud is provided, only search for unique records within that cloud\\r\\n    | where isempty(_input_scope_cloud) or ParentCloud =~ _input_scope_cloud\\r\\n    // search all columns for a case-insensitive match\\r\\n    | where ID =~ _input_attribute_value\\r\\n      or ArmLocation =~ _input_attribute_value\\r\\n      or FriendlyName =~ _input_attribute_value\\r\\n      or GUID =~ _input_attribute_value\\r\\n}\&quot;\n      46:   },\n      47:   \&quot;resources\&quot;: [\n      48:     {\n      49:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n      50:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n      51:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'LabelDataView')]\&quot;,\n      52:       \&quot;properties\&quot;: {\n      53:         \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#0'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,\n      54:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n&gt;&gt;&gt;   55:         \&quot;continueOnErrors\&quot;: true\n      56:       }\n      57:     },\n      58:     {\n      59:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n      60:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n      61:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'LabelDataViewDebug')]\&quot;,\n      62:       \&quot;properties\&quot;: {\n      63:         \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#1'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,\n      64:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n      65:         \&quot;continueOnErrors\&quot;: true\n      66:       }\n      67:     },\n      68:     {\n      69:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n      70:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n      71:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'LabelValueDataView')]\&quot;,\n      72:       \&quot;properties\&quot;: {\n      73:         \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#2'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,\n      74:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n      75:         \&quot;continueOnErrors\&quot;: true\n      76:       }\n      77:     },\n      78:     {\n      79:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n      80:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n      81:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'LabelValueDataViewDebug')]\&quot;,\n      82:       \&quot;properties\&quot;: {\n      83:         \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#3'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,\n      84:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n      85:         \&quot;continueOnErrors\&quot;: true\n      86:       }\n      87:     },\n      88:     {\n      89:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n      90:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n      91:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'NamespaceDataView')]\&quot;,\n      92:       \&quot;properties\&quot;: {\n      93:         \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#4'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,\n      94:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n      95:         \&quot;continueOnErrors\&quot;: true\n      96:       }\n      97:     },\n      98:     {\n      99:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n     100:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n     101:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'NamespaceDataViewDebug')]\&quot;,\n     102:       \&quot;properties\&quot;: {\n     103:         \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#5'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,\n     104:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n     105:         \&quot;continueOnErrors\&quot;: true\n     106:       }\n     107:     },\n     108:     {\n     109:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n     110:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n     111:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'TopologyMetadataView')]\&quot;,\n     112:       \&quot;properties\&quot;: {\n     113:         \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#6'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,\n     114:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n     115:         \&quot;continueOnErrors\&quot;: true\n     116:       }\n     117:     },\n     118:     {\n     119:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n     120:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n     121:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_LookupByUniqueAttribute_Cloud')]\&quot;,\n     122:       \&quot;properties\&quot;: {\n     123:         \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#7')]\&quot;,\n     124:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n     125:         \&quot;continueOnErrors\&quot;: true\n     126:       },\n     127:       \&quot;dependsOn\&quot;: [\n     128:         \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'TopologyMetadataView')]\&quot;\n     129:       ]\n     130:     },\n     131:     {\n     132:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n     133:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n     134:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_LookupByUniqueAttribute_Geography')]\&quot;,\n     135:       \&quot;properties\&quot;: {\n     136:         \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#8')]\&quot;,\n     137:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n     138:         \&quot;continueOnErrors\&quot;: true\n     139:       },\n     140:       \&quot;dependsOn\&quot;: [\n     141:         \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'TopologyMetadataView')]\&quot;\n     142:       ]\n     143:     },\n     144:     {\n     145:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n     146:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n     147:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_LookupByUniqueAttribute_Region')]\&quot;,\n     148:       \&quot;properties\&quot;: {\n     149:         \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#9')]\&quot;,\n     150:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n     151:         \&quot;continueOnErrors\&quot;: true\n     152:       },\n     153:       \&quot;dependsOn\&quot;: [\n     154:         \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'TopologyMetadataView')]\&quot;\n     155:       ]&quot;, &quot;highlighted_line_content&quot;: &quot;\&quot;continueOnErrors\&quot;: true&quot;}
                </script>
                <script type="application/json" data-finding-context="KustoScripts.template.json_105" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;KustoScripts.template.json&quot;, &quot;line_number&quot;: 105, &quot;total_lines&quot;: 185, &quot;start_line&quot;: 5, &quot;end_line&quot;: 185, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 5, &quot;content&quot;: &quot;    \&quot;_generator\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;bicep\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;      \&quot;version\&quot;: \&quot;0.34.44.8038\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;      \&quot;templateHash\&quot;: \&quot;1805723795016029729\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;  },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;  \&quot;parameters\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;    \&quot;kustoClusterName\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;string\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;      \&quot;defaultValue\&quot;: \&quot;cloudsprouttest\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;    \&quot;kustoDatabaseName\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;string\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;      \&quot;defaultValue\&quot;: \&quot;Telemetry\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;    \&quot;deploymentStartTime\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;string\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;      \&quot;defaultValue\&quot;: \&quot;[utcNow()]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;    \&quot;effectiveDateTime\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;string\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;      \&quot;defaultValue\&quot;: \&quot;[dateTimeAdd(utcNow(), '-P14D')]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;    \&quot;effectiveDateTimePlaceholder\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;string\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;      \&quot;defaultValue\&quot;: \&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;  },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 33, &quot;content&quot;: &quot;  \&quot;variables\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 34, &quot;content&quot;: &quot;    \&quot;$fxv#0\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient retrieval of the latest LabelData records\\\&quot;\\r\\n  ) LabelDataView on table Snapshots\\r\\n{\\r\\n  Snapshots\\r\\n    | where entity == \\\&quot;LabelData\\\&quot; and Role =~ \\\&quot;BE\\\&quot;\\r\\n    | extend moniker = strcat(namespace, \\\&quot;.\\\&quot;, label)\\r\\n    | project-rename exported_at = env_time\\r\\n    | project \\r\\n      exported_at\\r\\n    , moniker\\r\\n    , label\\r\\n    , deprecated\\r\\n    , namespace\\r\\n    , last_updated\\r\\n    , date_created\\r\\n    , version\\r\\n    , tags\\r\\n    , label_imports\\r\\n    , schema_type\\r\\n    , type_constraint\\r\\n    , integrations\\r\\n    , allow_chaining\\r\\n    , allow_empty_string\\r\\n    , has_projected_labels\\r\\n    , is_projected_label\\r\\n    | extend _version = parse_version(version)\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by moniker, version\\r\\n}\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 35, &quot;content&quot;: &quot;    \&quot;$fxv#1\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient debugging of the unique incoming snapshot events\\\&quot;\\r\\n  ) LabelDataViewDebug on table Snapshots\\r\\n{\\r\\n  Snapshots\\r\\n    | where entity == \\\&quot;LabelData\\\&quot; and Role =~ \\\&quot;BE\\\&quot;\\r\\n    | extend moniker = strcat(namespace, \\\&quot;.\\\&quot;, label)\\r\\n    | project-rename exported_at = env_time\\r\\n    | project \\r\\n      exported_at\\r\\n    , moniker\\r\\n    , label\\r\\n    , deprecated\\r\\n    , namespace\\r\\n    , last_updated\\r\\n    , date_created\\r\\n    , version\\r\\n    , tags\\r\\n    , label_imports\\r\\n    , schema_type\\r\\n    , type_constraint\\r\\n    , integrations\\r\\n    , allow_chaining\\r\\n    , allow_empty_string\\r\\n    , has_projected_labels\\r\\n    , is_projected_label\\r\\n    , kusto_export_mechanism\\r\\n    , Environment\\r\\n    , RoleInstance\\r\\n    | extend _version = parse_version(version)\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by moniker, version, kusto_export_mechanism, Environment, RoleInstance\\r\\n}\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 36, &quot;content&quot;: &quot;    \&quot;$fxv#10\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n// data upstream source: https://msazure.visualstudio.com/One/_git/OneDCMT?path=/src/Datacenter\\r\\n.create-or-alter function with (\\r\\n  docstring = \\\&quot;Given a list of monikers and a specified region, return the latest value for each moniker. If a region-specific value is not available, the cloud-scoped value is returned. Region can be referenced by using any of its unique aliases. If no environment scope is provided, 'prod' is assumed.\\\&quot;,\\r\\n  folder = \\\&quot;LabelValuesByUniqueRegionEntity\\\&quot;\\r\\n) fun_FetchLabelValueByRegionsInJson (\\r\\n  _input_monikers:dynamic,\\r\\n  _input_scope_region:string,\\r\\n  _input_scope_env:string=\\\&quot;prod\\\&quot;\\r\\n)\\r\\n{\\r\\n  // Convert input dynamic into a datatable\\r\\n  let _input_monikers_data = print _input_monikers\\r\\n      | mv-expand moniker = _input_monikers\\r\\n      | extend moniker = tolower(tostring(moniker))\\r\\n      | project moniker;\\r\\n  // Normalize the inputs\\r\\n  let region_record = materialize(\\r\\n      fun_LookupByUniqueAttribute_Region(_input_scope_region)\\r\\n          | take 1\\r\\n      );\\r\\n  let _normalized_scope_region = toscalar(\\r\\n      region_record\\r\\n          | project ID\\r\\n      );\\r\\n  // derive the cloud name from the input region:\\r\\n  let _normalized_scope_cloud = toscalar(\\r\\n      region_record\\r\\n          | project ParentCloud\\r\\n      );\\r\\n  // Step 1: LabelValueDataView contains every version of every moniker at every scope. \\r\\n  // first thing we need to do is only grab the latest version of each moniker:\\r\\n  let latest_values = materialize(\\r\\n      materialized_view('LabelValueDataView')\\r\\n          | extend scope_geo_type=tolower(scope_geo_type)\\r\\n          , scope_geo_value=tolower(scope_geo_value)\\r\\n          | where isnotempty(scope_geo_type) and isnotempty(scope_geo_value)\\r\\n          | extend _version = parse_version(version)\\r\\n          | summarize arg_max(_version, *) by moniker, scope_geo_type, scope_geo_value, scope_env\\r\\n      );\\r\\n  // Step 2: partition cloud-scoped values into a separate table:\\r\\n  let cloud_values = materialize(\\r\\n      latest_values\\r\\n          | where scope_geo_type =~ \\\&quot;cloud\\\&quot; and scope_geo_value =~ _normalized_scope_cloud and scope_env =~ _input_scope_env\\r\\n      );\\r\\n  // Step 3: partition region-scoped values into a separate table:\\r\\n  let region_values = materialize(\\r\\n      latest_values\\r\\n          | where scope_geo_type =~ \\\&quot;region\\\&quot; and scope_geo_value =~ _normalized_scope_region and scope_env =~ _input_scope_env\\r\\n      );\\r\\n  // Step 4: Perform the actual query. At a high level, we will give precedence to region-scoped values. If\\r\\n  // a moniker does not have a value for the region, it defaults to the cloud-scoped value:\\r\\n  _input_monikers_data\\r\\n      // Obtain the region-scoped value for each moniker, if present\\r\\n      | lookup kind=leftouter (region_values) on moniker\\r\\n      // Obtain the cloud-scoped value for each moniker, if present. Since this is a second lookup, we'll get\\r\\n      // a second column with \\\&quot;1\\\&quot; appended to the name\\r\\n      | lookup kind=leftouter (cloud_values) on moniker\\r\\n      // Merge the columns. If value is present in \\\&quot;region\\\&quot; column, use it. Otherwise, use the \\\&quot;cloud\\\&quot; column (columns with \\\&quot;1\\\&quot; suffix):\\r\\n      | extend value = iff(isnotempty(rendered_value), rendered_value, rendered_value1)\\r\\n      | extend scope_env = iff(isnotempty(scope_env), scope_env, scope_env1)\\r\\n      | extend version = iff(isnotempty(version), version, version1)\\r\\n      | extend scope_geo_type = iff(isnotempty(scope_geo_type), scope_geo_type, scope_geo_type1)\\r\\n      | extend scope_geo_value = iff(isnotempty(scope_geo_value), scope_geo_value, scope_geo_value1)\\r\\n      | project moniker, scope_geo_type, scope_geo_value, scope_env, value, version\\r\\n      | order by moniker asc\\r\\n      // Convert to a single json blob\\r\\n      | project moniker, value\\r\\n      | summarize jsonResult = make_list(bag_pack(\\\&quot;key\\\&quot;, moniker, \\\&quot;value\\\&quot;, value))\\r\\n      | project jsonResult\\r\\n}\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 37, &quot;content&quot;: &quot;    \&quot;$fxv#11\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n// data upstream source: https://msazure.visualstudio.com/One/_git/OneDCMT?path=/src/Datacenter\\r\\n.create-or-alter function with (\\r\\n  docstring = \\\&quot;Given a list of monikers and a specified region, return the latest value for each moniker. If a region-specific value is not available, the cloud-scoped value is returned. Region can be referenced by using any of its unique aliases. If no environment scope is provided, 'prod' is assumed.\\\&quot;,\\r\\n  folder = \\\&quot;LabelValuesByUniqueRegionEntity\\\&quot;\\r\\n) fetchLabelValueByRegionsInJson (\\r\\n  _input_monikers:dynamic,\\r\\n  _input_scope_region:string,\\r\\n  _input_scope_env:string=\\\&quot;prod\\\&quot;\\r\\n)\\r\\n{\\r\\n    // Note: the function \\\&quot;fetchLabelValuesByRegionsInJson\\\&quot; is currently used in SharedSettings, \\r\\n    // wrapping it into the new naming convention for compatibility:\\r\\n    fun_FetchLabelValueByRegionsInJson(_input_monikers, _input_scope_region, _input_scope_env);\\r\\n}\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 38, &quot;content&quot;: &quot;    \&quot;$fxv#2\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient retrieval of each LabelValueData record\\\&quot;\\r\\n  ) LabelValueDataView on table Snapshots\\r\\n{\\r\\n  Snapshots\\r\\n    | where entity == \\\&quot;LabelValueData\\\&quot; and Role =~ \\\&quot;BE\\\&quot;\\r\\n    | extend moniker = strcat(namespace, \\\&quot;.\\\&quot;, label)\\r\\n    | extend scope_env = tolower(scope_env)\\r\\n    | extend scope_geo_type = tolower(scope_geo_type)\\r\\n    | extend scope_geo_value = tolower(scope_geo_value)\\r\\n    | project-rename exported_at = env_time\\r\\n    | project \\r\\n      moniker\\r\\n    , exported_at\\r\\n    , env_dt_traceId\\r\\n    , env_dt_spanId\\r\\n    , namespace\\r\\n    , label\\r\\n    , scope_cloud\\r\\n    , scope_env\\r\\n    , scope_geo_type\\r\\n    , scope_geo_value\\r\\n    , last_updated\\r\\n    , templated_value\\r\\n    , rendered_value\\r\\n    , modified_by\\r\\n    , version\\r\\n    , value_type\\r\\n    | extend _version = parse_version(version)\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by moniker, scope_env, scope_geo_type, scope_geo_value, version\\r\\n}\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 39, &quot;content&quot;: &quot;    \&quot;$fxv#3\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient debugging of the unique incoming snapshot events\\\&quot;\\r\\n  ) LabelValueDataViewDebug on table Snapshots\\r\\n{\\r\\n  Snapshots\\r\\n    | where entity == \\\&quot;LabelValueData\\\&quot; and Role =~ \\\&quot;BE\\\&quot;\\r\\n    | extend moniker = strcat(namespace, \\\&quot;.\\\&quot;, label)\\r\\n    | extend scope_env = tolower(scope_env)\\r\\n    | extend scope_geo_type = tolower(scope_geo_type)\\r\\n    | extend scope_geo_value = tolower(scope_geo_value)\\r\\n    | project-rename exported_at = env_time\\r\\n    | project \\r\\n      moniker\\r\\n    , exported_at\\r\\n    , env_dt_traceId\\r\\n    , env_dt_spanId\\r\\n    , namespace\\r\\n    , label\\r\\n    , scope_cloud\\r\\n    , scope_env\\r\\n    , scope_geo_type\\r\\n    , scope_geo_value\\r\\n    , last_updated\\r\\n    , templated_value\\r\\n    , rendered_value\\r\\n    , modified_by\\r\\n    , version\\r\\n    , value_type\\r\\n    , kusto_export_mechanism\\r\\n    , Environment\\r\\n    , RoleInstance\\r\\n    | extend _version = parse_version(version)\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by moniker, scope_env, scope_geo_type, scope_geo_value, version, kusto_export_mechanism, Environment, RoleInstance\\r\\n}\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 40, &quot;content&quot;: &quot;    \&quot;$fxv#4\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient retrieval of the latest NamespaceData records\\\&quot;\\r\\n  ) NamespaceDataView on table Snapshots\\r\\n{\\r\\n  Snapshots\\r\\n    | where entity == \\\&quot;NamespaceData\\\&quot; and Role =~ \\\&quot;BE\\\&quot;\\r\\n    | project-rename exported_at = env_time\\r\\n    | project \\r\\n      exported_at\\r\\n    , namespace\\r\\n    , owner_service_tree_id\\r\\n    , enabled\\r\\n    , version\\r\\n    , email\\r\\n    , icm_teams\\r\\n    , documentation_urls\\r\\n    , service_owners\\r\\n    | extend _version = parse_version(version)\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by namespace, version\\r\\n}\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 41, &quot;content&quot;: &quot;    \&quot;$fxv#5\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient debugging of the unique incoming snapshot events\\\&quot;\\r\\n  ) NamespaceDataViewDebug on table Snapshots\\r\\n{\\r\\n  Snapshots\\r\\n    | where entity == \\\&quot;NamespaceData\\\&quot; and Role =~ \\\&quot;BE\\\&quot;\\r\\n    | project-rename exported_at = env_time\\r\\n    | project \\r\\n      exported_at\\r\\n    , namespace\\r\\n    , owner_service_tree_id\\r\\n    , enabled\\r\\n    , version\\r\\n    , email\\r\\n    , icm_teams\\r\\n    , documentation_urls\\r\\n    , service_owners\\r\\n    , kusto_export_mechanism\\r\\n    , Environment\\r\\n    , RoleInstance\\r\\n    | extend _version = parse_version(version)\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by namespace, version, kusto_export_mechanism, Environment, RoleInstance\\r\\n}\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 42, &quot;content&quot;: &quot;    \&quot;$fxv#6\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n// data upstream source: https://msazure.visualstudio.com/One/_git/OneDCMT?path=/src/Datacenter\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient lookup of the latest DCMT GeoX Metadata entities\\\&quot;\\r\\n  ) TopologyMetadataView on table TopologyMetadata\\r\\n{\\r\\n  TopologyMetadata\\r\\n    | where Role =~ \\\&quot;BE\\\&quot;\\r\\n    | project-rename exported_at = env_time\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by GUID\\r\\n}\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 43, &quot;content&quot;: &quot;    \&quot;$fxv#7\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n// data upstream source: https://msazure.visualstudio.com/One/_git/OneDCMT?path=/src/Datacenter\\r\\n.create-or-alter function with (\\r\\n  docstring = \\\&quot;Given a input string, search all known globally-unique columns for a match\\\&quot;,\\r\\n  folder = \\\&quot;Topology\\\&quot;\\r\\n) fun_LookupByUniqueAttribute_Cloud (\\r\\n  _input_attribute_value:string\\r\\n) \\r\\n{\\r\\n  materialized_view(\\\&quot;TopologyMetadataView\\\&quot;)\\r\\n    | where Entity =~ \\\&quot;CloudMetadata\\\&quot;\\r\\n    // shim: AlternateName is not defined for most clouds. In clouds like Mooncake, since we never\\r\\n    // have a value for this column, geneva never shows it in GCS in the TopologyMetadata event, thus\\r\\n    // GDS's auto schema never creates a column for it. This is a way to gracefully handle missing columns:\\r\\n    | extend EmptyColumn = \\\&quot;\\\&quot;\\r\\n    | extend AlternateName = column_ifexists(\\\&quot;AlternateName\\\&quot;, EmptyColumn)\\r\\n    | project-away EmptyColumn\\r\\n    // search all columns for a case-insensitive match\\r\\n    | where Name =~ _input_attribute_value\\r\\n      or GUID =~ _input_attribute_value\\r\\n      or AlternateName =~ _input_attribute_value\\r\\n}\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 44, &quot;content&quot;: &quot;    \&quot;$fxv#8\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n// data upstream source: https://msazure.visualstudio.com/One/_git/OneDCMT?path=/src/Datacenter\\r\\n.create-or-alter function with (\\r\\n  docstring = \\\&quot;Given a input string, search all known globally-unique columns for a match. If _input_scope_cloud is specified, the search will be targeted to all records defined within the matching ParentCloud\\\&quot;,\\r\\n  folder = \\\&quot;Topology\\\&quot;\\r\\n) fun_LookupByUniqueAttribute_Geography (\\r\\n  _input_attribute_value:string,\\r\\n  _input_scope_cloud:string=\\\&quot;\\\&quot;\\r\\n) \\r\\n{\\r\\n  materialized_view(\\\&quot;TopologyMetadataView\\\&quot;)\\r\\n    | where Entity =~ \\\&quot;GeographyMetadata\\\&quot;\\r\\n    // If scope cloud is provided, only search for unique records within that cloud\\r\\n    | where isempty(_input_scope_cloud) or ParentCloud =~ _input_scope_cloud\\r\\n    // search all columns for a case-insensitive match\\r\\n    | where Name =~ _input_attribute_value\\r\\n      or GUID =~ _input_attribute_value\\r\\n}\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 45, &quot;content&quot;: &quot;    \&quot;$fxv#9\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n// data upstream source: https://msazure.visualstudio.com/One/_git/OneDCMT?path=/src/Datacenter\\r\\n.create-or-alter function with (\\r\\n  docstring = \\\&quot;Given a input string, search all known globally-unique columns for a match. If _input_scope_cloud is specified, the search will be targeted to all records defined within the matching ParentCloud\\\&quot;,\\r\\n  folder = \\\&quot;Topology\\\&quot;\\r\\n) fun_LookupByUniqueAttribute_Region (\\r\\n  _input_attribute_value:string,\\r\\n  _input_scope_cloud:string=\\\&quot;\\\&quot;\\r\\n) \\r\\n{\\r\\n  materialized_view(\\\&quot;TopologyMetadataView\\\&quot;)\\r\\n    | where Entity =~ \\\&quot;RegionMetadata\\\&quot;\\r\\n    // If scope cloud is provided, only search for unique records within that cloud\\r\\n    | where isempty(_input_scope_cloud) or ParentCloud =~ _input_scope_cloud\\r\\n    // search all columns for a case-insensitive match\\r\\n    | where ID =~ _input_attribute_value\\r\\n      or ArmLocation =~ _input_attribute_value\\r\\n      or FriendlyName =~ _input_attribute_value\\r\\n      or GUID =~ _input_attribute_value\\r\\n}\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 46, &quot;content&quot;: &quot;  },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 47, &quot;content&quot;: &quot;  \&quot;resources\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 48, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 49, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 50, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 51, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'LabelDataView')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 52, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 53, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#0'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 54, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 55, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 56, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 57, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 58, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 59, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 60, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 61, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'LabelDataViewDebug')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 62, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 63, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#1'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 64, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 65, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 66, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 67, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 68, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 69, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 70, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 71, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'LabelValueDataView')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 72, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 73, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#2'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 74, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 75, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 76, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 77, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 78, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 79, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 80, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 81, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'LabelValueDataViewDebug')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 82, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 83, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#3'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 84, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 85, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 86, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 87, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 88, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 89, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 90, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 91, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'NamespaceDataView')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 92, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 93, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#4'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 94, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 95, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 96, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 97, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 98, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 99, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 100, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 101, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'NamespaceDataViewDebug')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 102, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 103, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#5'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 104, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 105, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 106, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 107, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 108, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 109, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 110, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 111, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'TopologyMetadataView')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 112, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 113, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#6'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 114, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 115, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 116, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 117, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 118, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 119, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 120, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 121, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_LookupByUniqueAttribute_Cloud')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 122, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 123, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#7')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 124, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 125, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 126, &quot;content&quot;: &quot;      },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 127, &quot;content&quot;: &quot;      \&quot;dependsOn\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 128, &quot;content&quot;: &quot;        \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'TopologyMetadataView')]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 129, &quot;content&quot;: &quot;      ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 130, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 131, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 132, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 133, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 134, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_LookupByUniqueAttribute_Geography')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 135, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 136, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#8')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 137, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 138, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 139, &quot;content&quot;: &quot;      },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 140, &quot;content&quot;: &quot;      \&quot;dependsOn\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 141, &quot;content&quot;: &quot;        \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'TopologyMetadataView')]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 142, &quot;content&quot;: &quot;      ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 143, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 144, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 145, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 146, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 147, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_LookupByUniqueAttribute_Region')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 148, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 149, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#9')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 150, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 151, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 152, &quot;content&quot;: &quot;      },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 153, &quot;content&quot;: &quot;      \&quot;dependsOn\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 154, &quot;content&quot;: &quot;        \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'TopologyMetadataView')]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 155, &quot;content&quot;: &quot;      ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 156, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 157, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 158, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 159, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 160, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_FetchLabelValueByRegionsInJson')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 161, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 162, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#10')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 163, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 164, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 165, &quot;content&quot;: &quot;      },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 166, &quot;content&quot;: &quot;      \&quot;dependsOn\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 167, &quot;content&quot;: &quot;        \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_LookupByUniqueAttribute_Region')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 168, &quot;content&quot;: &quot;        \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'LabelValueDataView')]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 169, &quot;content&quot;: &quot;      ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 170, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 171, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 172, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 173, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 174, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fetchLabelValueByRegionsInJson')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 175, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 176, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#11')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 177, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 178, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 179, &quot;content&quot;: &quot;      },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 180, &quot;content&quot;: &quot;      \&quot;dependsOn\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 181, &quot;content&quot;: &quot;        \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_FetchLabelValueByRegionsInJson')]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 182, &quot;content&quot;: &quot;      ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 183, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 184, &quot;content&quot;: &quot;  ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 185, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       5:     \&quot;_generator\&quot;: {\n       6:       \&quot;name\&quot;: \&quot;bicep\&quot;,\n       7:       \&quot;version\&quot;: \&quot;0.34.44.8038\&quot;,\n       8:       \&quot;templateHash\&quot;: \&quot;1805723795016029729\&quot;\n       9:     }\n      10:   },\n      11:   \&quot;parameters\&quot;: {\n      12:     \&quot;kustoClusterName\&quot;: {\n      13:       \&quot;type\&quot;: \&quot;string\&quot;,\n      14:       \&quot;defaultValue\&quot;: \&quot;cloudsprouttest\&quot;\n      15:     },\n      16:     \&quot;kustoDatabaseName\&quot;: {\n      17:       \&quot;type\&quot;: \&quot;string\&quot;,\n      18:       \&quot;defaultValue\&quot;: \&quot;Telemetry\&quot;\n      19:     },\n      20:     \&quot;deploymentStartTime\&quot;: {\n      21:       \&quot;type\&quot;: \&quot;string\&quot;,\n      22:       \&quot;defaultValue\&quot;: \&quot;[utcNow()]\&quot;\n      23:     },\n      24:     \&quot;effectiveDateTime\&quot;: {\n      25:       \&quot;type\&quot;: \&quot;string\&quot;,\n      26:       \&quot;defaultValue\&quot;: \&quot;[dateTimeAdd(utcNow(), '-P14D')]\&quot;\n      27:     },\n      28:     \&quot;effectiveDateTimePlaceholder\&quot;: {\n      29:       \&quot;type\&quot;: \&quot;string\&quot;,\n      30:       \&quot;defaultValue\&quot;: \&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\&quot;\n      31:     }\n      32:   },\n      33:   \&quot;variables\&quot;: {\n      34:     \&quot;$fxv#0\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient retrieval of the latest LabelData records\\\&quot;\\r\\n  ) LabelDataView on table Snapshots\\r\\n{\\r\\n  Snapshots\\r\\n    | where entity == \\\&quot;LabelData\\\&quot; and Role =~ \\\&quot;BE\\\&quot;\\r\\n    | extend moniker = strcat(namespace, \\\&quot;.\\\&quot;, label)\\r\\n    | project-rename exported_at = env_time\\r\\n    | project \\r\\n      exported_at\\r\\n    , moniker\\r\\n    , label\\r\\n    , deprecated\\r\\n    , namespace\\r\\n    , last_updated\\r\\n    , date_created\\r\\n    , version\\r\\n    , tags\\r\\n    , label_imports\\r\\n    , schema_type\\r\\n    , type_constraint\\r\\n    , integrations\\r\\n    , allow_chaining\\r\\n    , allow_empty_string\\r\\n    , has_projected_labels\\r\\n    , is_projected_label\\r\\n    | extend _version = parse_version(version)\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by moniker, version\\r\\n}\&quot;,\n      35:     \&quot;$fxv#1\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient debugging of the unique incoming snapshot events\\\&quot;\\r\\n  ) LabelDataViewDebug on table Snapshots\\r\\n{\\r\\n  Snapshots\\r\\n    | where entity == \\\&quot;LabelData\\\&quot; and Role =~ \\\&quot;BE\\\&quot;\\r\\n    | extend moniker = strcat(namespace, \\\&quot;.\\\&quot;, label)\\r\\n    | project-rename exported_at = env_time\\r\\n    | project \\r\\n      exported_at\\r\\n    , moniker\\r\\n    , label\\r\\n    , deprecated\\r\\n    , namespace\\r\\n    , last_updated\\r\\n    , date_created\\r\\n    , version\\r\\n    , tags\\r\\n    , label_imports\\r\\n    , schema_type\\r\\n    , type_constraint\\r\\n    , integrations\\r\\n    , allow_chaining\\r\\n    , allow_empty_string\\r\\n    , has_projected_labels\\r\\n    , is_projected_label\\r\\n    , kusto_export_mechanism\\r\\n    , Environment\\r\\n    , RoleInstance\\r\\n    | extend _version = parse_version(version)\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by moniker, version, kusto_export_mechanism, Environment, RoleInstance\\r\\n}\&quot;,\n      36:     \&quot;$fxv#10\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n// data upstream source: https://msazure.visualstudio.com/One/_git/OneDCMT?path=/src/Datacenter\\r\\n.create-or-alter function with (\\r\\n  docstring = \\\&quot;Given a list of monikers and a specified region, return the latest value for each moniker. If a region-specific value is not available, the cloud-scoped value is returned. Region can be referenced by using any of its unique aliases. If no environment scope is provided, 'prod' is assumed.\\\&quot;,\\r\\n  folder = \\\&quot;LabelValuesByUniqueRegionEntity\\\&quot;\\r\\n) fun_FetchLabelValueByRegionsInJson (\\r\\n  _input_monikers:dynamic,\\r\\n  _input_scope_region:string,\\r\\n  _input_scope_env:string=\\\&quot;prod\\\&quot;\\r\\n)\\r\\n{\\r\\n  // Convert input dynamic into a datatable\\r\\n  let _input_monikers_data = print _input_monikers\\r\\n      | mv-expand moniker = _input_monikers\\r\\n      | extend moniker = tolower(tostring(moniker))\\r\\n      | project moniker;\\r\\n  // Normalize the inputs\\r\\n  let region_record = materialize(\\r\\n      fun_LookupByUniqueAttribute_Region(_input_scope_region)\\r\\n          | take 1\\r\\n      );\\r\\n  let _normalized_scope_region = toscalar(\\r\\n      region_record\\r\\n          | project ID\\r\\n      );\\r\\n  // derive the cloud name from the input region:\\r\\n  let _normalized_scope_cloud = toscalar(\\r\\n      region_record\\r\\n          | project ParentCloud\\r\\n      );\\r\\n  // Step 1: LabelValueDataView contains every version of every moniker at every scope. \\r\\n  // first thing we need to do is only grab the latest version of each moniker:\\r\\n  let latest_values = materialize(\\r\\n      materialized_view('LabelValueDataView')\\r\\n          | extend scope_geo_type=tolower(scope_geo_type)\\r\\n          , scope_geo_value=tolower(scope_geo_value)\\r\\n          | where isnotempty(scope_geo_type) and isnotempty(scope_geo_value)\\r\\n          | extend _version = parse_version(version)\\r\\n          | summarize arg_max(_version, *) by moniker, scope_geo_type, scope_geo_value, scope_env\\r\\n      );\\r\\n  // Step 2: partition cloud-scoped values into a separate table:\\r\\n  let cloud_values = materialize(\\r\\n      latest_values\\r\\n          | where scope_geo_type =~ \\\&quot;cloud\\\&quot; and scope_geo_value =~ _normalized_scope_cloud and scope_env =~ _input_scope_env\\r\\n      );\\r\\n  // Step 3: partition region-scoped values into a separate table:\\r\\n  let region_values = materialize(\\r\\n      latest_values\\r\\n          | where scope_geo_type =~ \\\&quot;region\\\&quot; and scope_geo_value =~ _normalized_scope_region and scope_env =~ _input_scope_env\\r\\n      );\\r\\n  // Step 4: Perform the actual query. At a high level, we will give precedence to region-scoped values. If\\r\\n  // a moniker does not have a value for the region, it defaults to the cloud-scoped value:\\r\\n  _input_monikers_data\\r\\n      // Obtain the region-scoped value for each moniker, if present\\r\\n      | lookup kind=leftouter (region_values) on moniker\\r\\n      // Obtain the cloud-scoped value for each moniker, if present. Since this is a second lookup, we'll get\\r\\n      // a second column with \\\&quot;1\\\&quot; appended to the name\\r\\n      | lookup kind=leftouter (cloud_values) on moniker\\r\\n      // Merge the columns. If value is present in \\\&quot;region\\\&quot; column, use it. Otherwise, use the \\\&quot;cloud\\\&quot; column (columns with \\\&quot;1\\\&quot; suffix):\\r\\n      | extend value = iff(isnotempty(rendered_value), rendered_value, rendered_value1)\\r\\n      | extend scope_env = iff(isnotempty(scope_env), scope_env, scope_env1)\\r\\n      | extend version = iff(isnotempty(version), version, version1)\\r\\n      | extend scope_geo_type = iff(isnotempty(scope_geo_type), scope_geo_type, scope_geo_type1)\\r\\n      | extend scope_geo_value = iff(isnotempty(scope_geo_value), scope_geo_value, scope_geo_value1)\\r\\n      | project moniker, scope_geo_type, scope_geo_value, scope_env, value, version\\r\\n      | order by moniker asc\\r\\n      // Convert to a single json blob\\r\\n      | project moniker, value\\r\\n      | summarize jsonResult = make_list(bag_pack(\\\&quot;key\\\&quot;, moniker, \\\&quot;value\\\&quot;, value))\\r\\n      | project jsonResult\\r\\n}\&quot;,\n      37:     \&quot;$fxv#11\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n// data upstream source: https://msazure.visualstudio.com/One/_git/OneDCMT?path=/src/Datacenter\\r\\n.create-or-alter function with (\\r\\n  docstring = \\\&quot;Given a list of monikers and a specified region, return the latest value for each moniker. If a region-specific value is not available, the cloud-scoped value is returned. Region can be referenced by using any of its unique aliases. If no environment scope is provided, 'prod' is assumed.\\\&quot;,\\r\\n  folder = \\\&quot;LabelValuesByUniqueRegionEntity\\\&quot;\\r\\n) fetchLabelValueByRegionsInJson (\\r\\n  _input_monikers:dynamic,\\r\\n  _input_scope_region:string,\\r\\n  _input_scope_env:string=\\\&quot;prod\\\&quot;\\r\\n)\\r\\n{\\r\\n    // Note: the function \\\&quot;fetchLabelValuesByRegionsInJson\\\&quot; is currently used in SharedSettings, \\r\\n    // wrapping it into the new naming convention for compatibility:\\r\\n    fun_FetchLabelValueByRegionsInJson(_input_monikers, _input_scope_region, _input_scope_env);\\r\\n}\&quot;,\n      38:     \&quot;$fxv#2\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient retrieval of each LabelValueData record\\\&quot;\\r\\n  ) LabelValueDataView on table Snapshots\\r\\n{\\r\\n  Snapshots\\r\\n    | where entity == \\\&quot;LabelValueData\\\&quot; and Role =~ \\\&quot;BE\\\&quot;\\r\\n    | extend moniker = strcat(namespace, \\\&quot;.\\\&quot;, label)\\r\\n    | extend scope_env = tolower(scope_env)\\r\\n    | extend scope_geo_type = tolower(scope_geo_type)\\r\\n    | extend scope_geo_value = tolower(scope_geo_value)\\r\\n    | project-rename exported_at = env_time\\r\\n    | project \\r\\n      moniker\\r\\n    , exported_at\\r\\n    , env_dt_traceId\\r\\n    , env_dt_spanId\\r\\n    , namespace\\r\\n    , label\\r\\n    , scope_cloud\\r\\n    , scope_env\\r\\n    , scope_geo_type\\r\\n    , scope_geo_value\\r\\n    , last_updated\\r\\n    , templated_value\\r\\n    , rendered_value\\r\\n    , modified_by\\r\\n    , version\\r\\n    , value_type\\r\\n    | extend _version = parse_version(version)\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by moniker, scope_env, scope_geo_type, scope_geo_value, version\\r\\n}\&quot;,\n      39:     \&quot;$fxv#3\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient debugging of the unique incoming snapshot events\\\&quot;\\r\\n  ) LabelValueDataViewDebug on table Snapshots\\r\\n{\\r\\n  Snapshots\\r\\n    | where entity == \\\&quot;LabelValueData\\\&quot; and Role =~ \\\&quot;BE\\\&quot;\\r\\n    | extend moniker = strcat(namespace, \\\&quot;.\\\&quot;, label)\\r\\n    | extend scope_env = tolower(scope_env)\\r\\n    | extend scope_geo_type = tolower(scope_geo_type)\\r\\n    | extend scope_geo_value = tolower(scope_geo_value)\\r\\n    | project-rename exported_at = env_time\\r\\n    | project \\r\\n      moniker\\r\\n    , exported_at\\r\\n    , env_dt_traceId\\r\\n    , env_dt_spanId\\r\\n    , namespace\\r\\n    , label\\r\\n    , scope_cloud\\r\\n    , scope_env\\r\\n    , scope_geo_type\\r\\n    , scope_geo_value\\r\\n    , last_updated\\r\\n    , templated_value\\r\\n    , rendered_value\\r\\n    , modified_by\\r\\n    , version\\r\\n    , value_type\\r\\n    , kusto_export_mechanism\\r\\n    , Environment\\r\\n    , RoleInstance\\r\\n    | extend _version = parse_version(version)\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by moniker, scope_env, scope_geo_type, scope_geo_value, version, kusto_export_mechanism, Environment, RoleInstance\\r\\n}\&quot;,\n      40:     \&quot;$fxv#4\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient retrieval of the latest NamespaceData records\\\&quot;\\r\\n  ) NamespaceDataView on table Snapshots\\r\\n{\\r\\n  Snapshots\\r\\n    | where entity == \\\&quot;NamespaceData\\\&quot; and Role =~ \\\&quot;BE\\\&quot;\\r\\n    | project-rename exported_at = env_time\\r\\n    | project \\r\\n      exported_at\\r\\n    , namespace\\r\\n    , owner_service_tree_id\\r\\n    , enabled\\r\\n    , version\\r\\n    , email\\r\\n    , icm_teams\\r\\n    , documentation_urls\\r\\n    , service_owners\\r\\n    | extend _version = parse_version(version)\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by namespace, version\\r\\n}\&quot;,\n      41:     \&quot;$fxv#5\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient debugging of the unique incoming snapshot events\\\&quot;\\r\\n  ) NamespaceDataViewDebug on table Snapshots\\r\\n{\\r\\n  Snapshots\\r\\n    | where entity == \\\&quot;NamespaceData\\\&quot; and Role =~ \\\&quot;BE\\\&quot;\\r\\n    | project-rename exported_at = env_time\\r\\n    | project \\r\\n      exported_at\\r\\n    , namespace\\r\\n    , owner_service_tree_id\\r\\n    , enabled\\r\\n    , version\\r\\n    , email\\r\\n    , icm_teams\\r\\n    , documentation_urls\\r\\n    , service_owners\\r\\n    , kusto_export_mechanism\\r\\n    , Environment\\r\\n    , RoleInstance\\r\\n    | extend _version = parse_version(version)\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by namespace, version, kusto_export_mechanism, Environment, RoleInstance\\r\\n}\&quot;,\n      42:     \&quot;$fxv#6\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n// data upstream source: https://msazure.visualstudio.com/One/_git/OneDCMT?path=/src/Datacenter\\r\\n//\\r\\n// Note: strings in the format &lt;&lt;...&gt;&gt; such as \\\&quot;&lt;&lt;EFFECTIVE_DATE&gt;&gt;\\\&quot; get replaced within Templates/KustoScripts.bicep\\r\\n.create-or-alter materialized-view with (\\r\\n    //backfill=true, \\r\\n    //effectiveDateTime=datetime(&lt;&lt;EFFECTIVE_DATE&gt;&gt;),\\r\\n    autoUpdateSchema=true,\\r\\n    docString=\\\&quot;materialized view that enables efficient lookup of the latest DCMT GeoX Metadata entities\\\&quot;\\r\\n  ) TopologyMetadataView on table TopologyMetadata\\r\\n{\\r\\n  TopologyMetadata\\r\\n    | where Role =~ \\\&quot;BE\\\&quot;\\r\\n    | project-rename exported_at = env_time\\r\\n    | summarize \\r\\n        arg_max(exported_at, *) \\r\\n        by GUID\\r\\n}\&quot;,\n      43:     \&quot;$fxv#7\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n// data upstream source: https://msazure.visualstudio.com/One/_git/OneDCMT?path=/src/Datacenter\\r\\n.create-or-alter function with (\\r\\n  docstring = \\\&quot;Given a input string, search all known globally-unique columns for a match\\\&quot;,\\r\\n  folder = \\\&quot;Topology\\\&quot;\\r\\n) fun_LookupByUniqueAttribute_Cloud (\\r\\n  _input_attribute_value:string\\r\\n) \\r\\n{\\r\\n  materialized_view(\\\&quot;TopologyMetadataView\\\&quot;)\\r\\n    | where Entity =~ \\\&quot;CloudMetadata\\\&quot;\\r\\n    // shim: AlternateName is not defined for most clouds. In clouds like Mooncake, since we never\\r\\n    // have a value for this column, geneva never shows it in GCS in the TopologyMetadata event, thus\\r\\n    // GDS's auto schema never creates a column for it. This is a way to gracefully handle missing columns:\\r\\n    | extend EmptyColumn = \\\&quot;\\\&quot;\\r\\n    | extend AlternateName = column_ifexists(\\\&quot;AlternateName\\\&quot;, EmptyColumn)\\r\\n    | project-away EmptyColumn\\r\\n    // search all columns for a case-insensitive match\\r\\n    | where Name =~ _input_attribute_value\\r\\n      or GUID =~ _input_attribute_value\\r\\n      or AlternateName =~ _input_attribute_value\\r\\n}\&quot;,\n      44:     \&quot;$fxv#8\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n// data upstream source: https://msazure.visualstudio.com/One/_git/OneDCMT?path=/src/Datacenter\\r\\n.create-or-alter function with (\\r\\n  docstring = \\\&quot;Given a input string, search all known globally-unique columns for a match. If _input_scope_cloud is specified, the search will be targeted to all records defined within the matching ParentCloud\\\&quot;,\\r\\n  folder = \\\&quot;Topology\\\&quot;\\r\\n) fun_LookupByUniqueAttribute_Geography (\\r\\n  _input_attribute_value:string,\\r\\n  _input_scope_cloud:string=\\\&quot;\\\&quot;\\r\\n) \\r\\n{\\r\\n  materialized_view(\\\&quot;TopologyMetadataView\\\&quot;)\\r\\n    | where Entity =~ \\\&quot;GeographyMetadata\\\&quot;\\r\\n    // If scope cloud is provided, only search for unique records within that cloud\\r\\n    | where isempty(_input_scope_cloud) or ParentCloud =~ _input_scope_cloud\\r\\n    // search all columns for a case-insensitive match\\r\\n    | where Name =~ _input_attribute_value\\r\\n      or GUID =~ _input_attribute_value\\r\\n}\&quot;,\n      45:     \&quot;$fxv#9\&quot;: \&quot;// https://learn.microsoft.com/en-us/kusto/management/materialized-views/materialized-view-create-or-alter?view=azure-data-explorer\\r\\n// data upstream source: https://msazure.visualstudio.com/One/_git/OneDCMT?path=/src/Datacenter\\r\\n.create-or-alter function with (\\r\\n  docstring = \\\&quot;Given a input string, search all known globally-unique columns for a match. If _input_scope_cloud is specified, the search will be targeted to all records defined within the matching ParentCloud\\\&quot;,\\r\\n  folder = \\\&quot;Topology\\\&quot;\\r\\n) fun_LookupByUniqueAttribute_Region (\\r\\n  _input_attribute_value:string,\\r\\n  _input_scope_cloud:string=\\\&quot;\\\&quot;\\r\\n) \\r\\n{\\r\\n  materialized_view(\\\&quot;TopologyMetadataView\\\&quot;)\\r\\n    | where Entity =~ \\\&quot;RegionMetadata\\\&quot;\\r\\n    // If scope cloud is provided, only search for unique records within that cloud\\r\\n    | where isempty(_input_scope_cloud) or ParentCloud =~ _input_scope_cloud\\r\\n    // search all columns for a case-insensitive match\\r\\n    | where ID =~ _input_attribute_value\\r\\n      or ArmLocation =~ _input_attribute_value\\r\\n      or FriendlyName =~ _input_attribute_value\\r\\n      or GUID =~ _input_attribute_value\\r\\n}\&quot;\n      46:   },\n      47:   \&quot;resources\&quot;: [\n      48:     {\n      49:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n      50:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n      51:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'LabelDataView')]\&quot;,\n      52:       \&quot;properties\&quot;: {\n      53:         \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#0'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,\n      54:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n      55:         \&quot;continueOnErrors\&quot;: true\n      56:       }\n      57:     },\n      58:     {\n      59:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n      60:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n      61:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'LabelDataViewDebug')]\&quot;,\n      62:       \&quot;properties\&quot;: {\n      63:         \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#1'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,\n      64:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n      65:         \&quot;continueOnErrors\&quot;: true\n      66:       }\n      67:     },\n      68:     {\n      69:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n      70:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n      71:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'LabelValueDataView')]\&quot;,\n      72:       \&quot;properties\&quot;: {\n      73:         \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#2'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,\n      74:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n      75:         \&quot;continueOnErrors\&quot;: true\n      76:       }\n      77:     },\n      78:     {\n      79:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n      80:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n      81:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'LabelValueDataViewDebug')]\&quot;,\n      82:       \&quot;properties\&quot;: {\n      83:         \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#3'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,\n      84:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n      85:         \&quot;continueOnErrors\&quot;: true\n      86:       }\n      87:     },\n      88:     {\n      89:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n      90:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n      91:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'NamespaceDataView')]\&quot;,\n      92:       \&quot;properties\&quot;: {\n      93:         \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#4'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,\n      94:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n      95:         \&quot;continueOnErrors\&quot;: true\n      96:       }\n      97:     },\n      98:     {\n      99:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n     100:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n     101:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'NamespaceDataViewDebug')]\&quot;,\n     102:       \&quot;properties\&quot;: {\n     103:         \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#5'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,\n     104:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n&gt;&gt;&gt;  105:         \&quot;continueOnErrors\&quot;: true\n     106:       }\n     107:     },\n     108:     {\n     109:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n     110:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n     111:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'TopologyMetadataView')]\&quot;,\n     112:       \&quot;properties\&quot;: {\n     113:         \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#6'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,\n     114:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n     115:         \&quot;continueOnErrors\&quot;: true\n     116:       }\n     117:     },\n     118:     {\n     119:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n     120:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n     121:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_LookupByUniqueAttribute_Cloud')]\&quot;,\n     122:       \&quot;properties\&quot;: {\n     123:         \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#7')]\&quot;,\n     124:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n     125:         \&quot;continueOnErrors\&quot;: true\n     126:       },\n     127:       \&quot;dependsOn\&quot;: [\n     128:         \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'TopologyMetadataView')]\&quot;\n     129:       ]\n     130:     },\n     131:     {\n     132:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n     133:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n     134:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_LookupByUniqueAttribute_Geography')]\&quot;,\n     135:       \&quot;properties\&quot;: {\n     136:         \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#8')]\&quot;,\n     137:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n     138:         \&quot;continueOnErrors\&quot;: true\n     139:       },\n     140:       \&quot;dependsOn\&quot;: [\n     141:         \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'TopologyMetadataView')]\&quot;\n     142:       ]\n     143:     },\n     144:     {\n     145:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n     146:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n     147:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_LookupByUniqueAttribute_Region')]\&quot;,\n     148:       \&quot;properties\&quot;: {\n     149:         \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#9')]\&quot;,\n     150:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n     151:         \&quot;continueOnErrors\&quot;: true\n     152:       },\n     153:       \&quot;dependsOn\&quot;: [\n     154:         \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'TopologyMetadataView')]\&quot;\n     155:       ]\n     156:     },\n     157:     {\n     158:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n     159:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n     160:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_FetchLabelValueByRegionsInJson')]\&quot;,\n     161:       \&quot;properties\&quot;: {\n     162:         \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#10')]\&quot;,\n     163:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n     164:         \&quot;continueOnErrors\&quot;: true\n     165:       },\n     166:       \&quot;dependsOn\&quot;: [\n     167:         \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_LookupByUniqueAttribute_Region')]\&quot;,\n     168:         \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'LabelValueDataView')]\&quot;\n     169:       ]\n     170:     },\n     171:     {\n     172:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n     173:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n     174:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fetchLabelValueByRegionsInJson')]\&quot;,\n     175:       \&quot;properties\&quot;: {\n     176:         \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#11')]\&quot;,\n     177:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n     178:         \&quot;continueOnErrors\&quot;: true\n     179:       },\n     180:       \&quot;dependsOn\&quot;: [\n     181:         \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_FetchLabelValueByRegionsInJson')]\&quot;\n     182:       ]\n     183:     }\n     184:   ]\n     185: }&quot;, &quot;highlighted_line_content&quot;: &quot;\&quot;continueOnErrors\&quot;: true&quot;}
                </script>
                <script type="application/json" data-finding-context="KustoScripts.template.json_176" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;KustoScripts.template.json&quot;, &quot;line_number&quot;: 176, &quot;total_lines&quot;: 185, &quot;start_line&quot;: 76, &quot;end_line&quot;: 185, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 76, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 77, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 78, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 79, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 80, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 81, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'LabelValueDataViewDebug')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 82, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 83, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#3'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 84, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 85, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 86, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 87, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 88, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 89, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 90, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 91, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'NamespaceDataView')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 92, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 93, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#4'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 94, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 95, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 96, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 97, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 98, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 99, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 100, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 101, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'NamespaceDataViewDebug')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 102, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 103, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#5'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 104, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 105, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 106, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 107, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 108, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 109, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 110, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 111, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'TopologyMetadataView')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 112, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 113, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#6'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 114, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 115, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 116, &quot;content&quot;: &quot;      }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 117, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 118, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 119, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 120, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 121, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_LookupByUniqueAttribute_Cloud')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 122, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 123, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#7')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 124, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 125, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 126, &quot;content&quot;: &quot;      },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 127, &quot;content&quot;: &quot;      \&quot;dependsOn\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 128, &quot;content&quot;: &quot;        \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'TopologyMetadataView')]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 129, &quot;content&quot;: &quot;      ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 130, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 131, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 132, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 133, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 134, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_LookupByUniqueAttribute_Geography')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 135, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 136, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#8')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 137, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 138, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 139, &quot;content&quot;: &quot;      },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 140, &quot;content&quot;: &quot;      \&quot;dependsOn\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 141, &quot;content&quot;: &quot;        \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'TopologyMetadataView')]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 142, &quot;content&quot;: &quot;      ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 143, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 144, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 145, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 146, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 147, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_LookupByUniqueAttribute_Region')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 148, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 149, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#9')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 150, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 151, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 152, &quot;content&quot;: &quot;      },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 153, &quot;content&quot;: &quot;      \&quot;dependsOn\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 154, &quot;content&quot;: &quot;        \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'TopologyMetadataView')]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 155, &quot;content&quot;: &quot;      ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 156, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 157, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 158, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 159, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 160, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_FetchLabelValueByRegionsInJson')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 161, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 162, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#10')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 163, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 164, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 165, &quot;content&quot;: &quot;      },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 166, &quot;content&quot;: &quot;      \&quot;dependsOn\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 167, &quot;content&quot;: &quot;        \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_LookupByUniqueAttribute_Region')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 168, &quot;content&quot;: &quot;        \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'LabelValueDataView')]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 169, &quot;content&quot;: &quot;      ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 170, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 171, &quot;content&quot;: &quot;    {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 172, &quot;content&quot;: &quot;      \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 173, &quot;content&quot;: &quot;      \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 174, &quot;content&quot;: &quot;      \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fetchLabelValueByRegionsInJson')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 175, &quot;content&quot;: &quot;      \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 176, &quot;content&quot;: &quot;        \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#11')]\&quot;,&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 177, &quot;content&quot;: &quot;        \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 178, &quot;content&quot;: &quot;        \&quot;continueOnErrors\&quot;: true&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 179, &quot;content&quot;: &quot;      },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 180, &quot;content&quot;: &quot;      \&quot;dependsOn\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 181, &quot;content&quot;: &quot;        \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_FetchLabelValueByRegionsInJson')]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 182, &quot;content&quot;: &quot;      ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 183, &quot;content&quot;: &quot;    }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 184, &quot;content&quot;: &quot;  ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 185, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;      76:       }\n      77:     },\n      78:     {\n      79:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n      80:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n      81:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'LabelValueDataViewDebug')]\&quot;,\n      82:       \&quot;properties\&quot;: {\n      83:         \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#3'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,\n      84:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n      85:         \&quot;continueOnErrors\&quot;: true\n      86:       }\n      87:     },\n      88:     {\n      89:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n      90:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n      91:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'NamespaceDataView')]\&quot;,\n      92:       \&quot;properties\&quot;: {\n      93:         \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#4'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,\n      94:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n      95:         \&quot;continueOnErrors\&quot;: true\n      96:       }\n      97:     },\n      98:     {\n      99:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n     100:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n     101:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'NamespaceDataViewDebug')]\&quot;,\n     102:       \&quot;properties\&quot;: {\n     103:         \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#5'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,\n     104:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n     105:         \&quot;continueOnErrors\&quot;: true\n     106:       }\n     107:     },\n     108:     {\n     109:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n     110:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n     111:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'TopologyMetadataView')]\&quot;,\n     112:       \&quot;properties\&quot;: {\n     113:         \&quot;scriptContent\&quot;: \&quot;[replace(variables('$fxv#6'), parameters('effectiveDateTimePlaceholder'), parameters('effectiveDateTime'))]\&quot;,\n     114:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n     115:         \&quot;continueOnErrors\&quot;: true\n     116:       }\n     117:     },\n     118:     {\n     119:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n     120:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n     121:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_LookupByUniqueAttribute_Cloud')]\&quot;,\n     122:       \&quot;properties\&quot;: {\n     123:         \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#7')]\&quot;,\n     124:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n     125:         \&quot;continueOnErrors\&quot;: true\n     126:       },\n     127:       \&quot;dependsOn\&quot;: [\n     128:         \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'TopologyMetadataView')]\&quot;\n     129:       ]\n     130:     },\n     131:     {\n     132:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n     133:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n     134:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_LookupByUniqueAttribute_Geography')]\&quot;,\n     135:       \&quot;properties\&quot;: {\n     136:         \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#8')]\&quot;,\n     137:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n     138:         \&quot;continueOnErrors\&quot;: true\n     139:       },\n     140:       \&quot;dependsOn\&quot;: [\n     141:         \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'TopologyMetadataView')]\&quot;\n     142:       ]\n     143:     },\n     144:     {\n     145:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n     146:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n     147:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_LookupByUniqueAttribute_Region')]\&quot;,\n     148:       \&quot;properties\&quot;: {\n     149:         \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#9')]\&quot;,\n     150:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n     151:         \&quot;continueOnErrors\&quot;: true\n     152:       },\n     153:       \&quot;dependsOn\&quot;: [\n     154:         \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'TopologyMetadataView')]\&quot;\n     155:       ]\n     156:     },\n     157:     {\n     158:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n     159:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n     160:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_FetchLabelValueByRegionsInJson')]\&quot;,\n     161:       \&quot;properties\&quot;: {\n     162:         \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#10')]\&quot;,\n     163:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n     164:         \&quot;continueOnErrors\&quot;: true\n     165:       },\n     166:       \&quot;dependsOn\&quot;: [\n     167:         \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_LookupByUniqueAttribute_Region')]\&quot;,\n     168:         \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'LabelValueDataView')]\&quot;\n     169:       ]\n     170:     },\n     171:     {\n     172:       \&quot;type\&quot;: \&quot;Microsoft.Kusto/clusters/databases/scripts\&quot;,\n     173:       \&quot;apiVersion\&quot;: \&quot;2023-08-15\&quot;,\n     174:       \&quot;name\&quot;: \&quot;[format('{0}/{1}/{2}', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fetchLabelValueByRegionsInJson')]\&quot;,\n     175:       \&quot;properties\&quot;: {\n&gt;&gt;&gt;  176:         \&quot;scriptContent\&quot;: \&quot;[variables('$fxv#11')]\&quot;,\n     177:         \&quot;forceUpdateTag\&quot;: \&quot;[parameters('deploymentStartTime')]\&quot;,\n     178:         \&quot;continueOnErrors\&quot;: true\n     179:       },\n     180:       \&quot;dependsOn\&quot;: [\n     181:         \&quot;[resourceId('Microsoft.Kusto/clusters/databases/scripts', parameters('kustoClusterName'), parameters('kustoDatabaseName'), 'fun_FetchLabelValueByRegionsInJson')]\&quot;\n     182:       ]\n     183:     }\n     184:   ]\n     185: }&quot;, &quot;highlighted_line_content&quot;: &quot;\&quot;scriptContent\&quot;: \&quot;[variables('$fxv#11')]\&quot;,&quot;}
                </script>
                <script type="application/json" data-finding-context="KustoScripts.template.json_225" style="display: none;">
{&quot;success&quot;: false, &quot;error&quot;: &quot;Line 225 out of range (1-185)&quot;, &quot;fallback_content&quot;: &quot;// Line 225 out of range\n// File has 185 lines\n// Security issue reported at invalid line number&quot;}
                </script>
                <script type="application/json" data-finding-context="KustoScripts.template.json_237" style="display: none;">
{&quot;success&quot;: false, &quot;error&quot;: &quot;Line 237 out of range (1-185)&quot;, &quot;fallback_content&quot;: &quot;// Line 237 out of range\n// File has 185 lines\n// Security issue reported at invalid line number&quot;}
                </script>
                <script type="application/json" data-finding-context="KustoScripts.template.json_291" style="display: none;">
{&quot;success&quot;: false, &quot;error&quot;: &quot;Line 291 out of range (1-185)&quot;, &quot;fallback_content&quot;: &quot;// Line 291 out of range\n// File has 185 lines\n// Security issue reported at invalid line number&quot;}
                </script>
                <script type="application/json" data-finding-context="KustoScripts.template.json_319" style="display: none;">
{&quot;success&quot;: false, &quot;error&quot;: &quot;Line 319 out of range (1-185)&quot;, &quot;fallback_content&quot;: &quot;// Line 319 out of range\n// File has 185 lines\n// Security issue reported at invalid line number&quot;}
                </script>
                <script type="application/json" data-finding-context="roleAssignment.deploymentTemplate.json_11" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;roleAssignment.deploymentTemplate.json&quot;, &quot;line_number&quot;: 11, &quot;total_lines&quot;: 32, &quot;start_line&quot;: 1, &quot;end_line&quot;: 32, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;{&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;    \&quot;$schema\&quot;: \&quot;https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;    \&quot;contentVersion\&quot;: \&quot;*******\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;    \&quot;parameters\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;        \&quot;principalId\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;            \&quot;type\&quot;: \&quot;string\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;            \&quot;metadata\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;                \&quot;description\&quot;: \&quot;The principal id to assign the owner role to\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;            }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;        },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;        \&quot;builtInRoleType\&quot;: {&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;            \&quot;type\&quot;: \&quot;string\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;            \&quot;defaultValue\&quot;: \&quot;Owner\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;            \&quot;metadata\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;                \&quot;description\&quot;: \&quot;The built-in role type to be assigned to the principalId. Default is Owner.\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;            }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;    \&quot;variables\&quot;: {},&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;    \&quot;resources\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;        {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;            \&quot;type\&quot;: \&quot;Microsoft.Authorization/roleAssignments\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;            \&quot;apiVersion\&quot;: \&quot;2018-09-01-preview\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;            \&quot;name\&quot;: \&quot;[guid(concat(subscription().id,parameters('Owner'),parameters('principalId')))]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;            \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;                \&quot;roleDefinitionId\&quot;: \&quot;[concat('/subscriptions/', subscription().subscriptionId, '/providers/Microsoft.Authorization/roleDefinitions/', '8e3af657-a8ff-443c-a75c-2fe8c4bcb635')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;                \&quot;principalId\&quot;: \&quot;[parameters('principalId')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;                \&quot;roleDefinitionName\&quot;: \&quot;[parameters('builtInRoleType')]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;            }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;    ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: {\n       2:     \&quot;$schema\&quot;: \&quot;https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#\&quot;,\n       3:     \&quot;contentVersion\&quot;: \&quot;*******\&quot;,\n       4:     \&quot;parameters\&quot;: {\n       5:         \&quot;principalId\&quot;: {\n       6:             \&quot;type\&quot;: \&quot;string\&quot;,\n       7:             \&quot;metadata\&quot;: {\n       8:                 \&quot;description\&quot;: \&quot;The principal id to assign the owner role to\&quot;\n       9:             }\n      10:         },\n&gt;&gt;&gt;   11:         \&quot;builtInRoleType\&quot;: {\n      12:             \&quot;type\&quot;: \&quot;string\&quot;,\n      13:             \&quot;defaultValue\&quot;: \&quot;Owner\&quot;,\n      14:             \&quot;metadata\&quot;: {\n      15:                 \&quot;description\&quot;: \&quot;The built-in role type to be assigned to the principalId. Default is Owner.\&quot;\n      16:             }\n      17:         }\n      18:     },\n      19:     \&quot;variables\&quot;: {},\n      20:     \&quot;resources\&quot;: [\n      21:         {\n      22:             \&quot;type\&quot;: \&quot;Microsoft.Authorization/roleAssignments\&quot;,\n      23:             \&quot;apiVersion\&quot;: \&quot;2018-09-01-preview\&quot;,\n      24:             \&quot;name\&quot;: \&quot;[guid(concat(subscription().id,parameters('Owner'),parameters('principalId')))]\&quot;,\n      25:             \&quot;properties\&quot;: {\n      26:                 \&quot;roleDefinitionId\&quot;: \&quot;[concat('/subscriptions/', subscription().subscriptionId, '/providers/Microsoft.Authorization/roleDefinitions/', '8e3af657-a8ff-443c-a75c-2fe8c4bcb635')]\&quot;,\n      27:                 \&quot;principalId\&quot;: \&quot;[parameters('principalId')]\&quot;,\n      28:                 \&quot;roleDefinitionName\&quot;: \&quot;[parameters('builtInRoleType')]\&quot;\n      29:             }\n      30:         }\n      31:     ]\n      32: }&quot;, &quot;highlighted_line_content&quot;: &quot;\&quot;builtInRoleType\&quot;: {&quot;}
                </script>
                <script type="application/json" data-finding-context="roleAssignment.deploymentTemplate.json_13" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;roleAssignment.deploymentTemplate.json&quot;, &quot;line_number&quot;: 13, &quot;total_lines&quot;: 32, &quot;start_line&quot;: 1, &quot;end_line&quot;: 32, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;{&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;    \&quot;$schema\&quot;: \&quot;https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;    \&quot;contentVersion\&quot;: \&quot;*******\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;    \&quot;parameters\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;        \&quot;principalId\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;            \&quot;type\&quot;: \&quot;string\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;            \&quot;metadata\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;                \&quot;description\&quot;: \&quot;The principal id to assign the owner role to\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;            }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;        },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;        \&quot;builtInRoleType\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;            \&quot;type\&quot;: \&quot;string\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;            \&quot;defaultValue\&quot;: \&quot;Owner\&quot;,&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;            \&quot;metadata\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;                \&quot;description\&quot;: \&quot;The built-in role type to be assigned to the principalId. Default is Owner.\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;            }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;    \&quot;variables\&quot;: {},&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;    \&quot;resources\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;        {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;            \&quot;type\&quot;: \&quot;Microsoft.Authorization/roleAssignments\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;            \&quot;apiVersion\&quot;: \&quot;2018-09-01-preview\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;            \&quot;name\&quot;: \&quot;[guid(concat(subscription().id,parameters('Owner'),parameters('principalId')))]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;            \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;                \&quot;roleDefinitionId\&quot;: \&quot;[concat('/subscriptions/', subscription().subscriptionId, '/providers/Microsoft.Authorization/roleDefinitions/', '8e3af657-a8ff-443c-a75c-2fe8c4bcb635')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;                \&quot;principalId\&quot;: \&quot;[parameters('principalId')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;                \&quot;roleDefinitionName\&quot;: \&quot;[parameters('builtInRoleType')]\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;            }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;    ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: {\n       2:     \&quot;$schema\&quot;: \&quot;https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#\&quot;,\n       3:     \&quot;contentVersion\&quot;: \&quot;*******\&quot;,\n       4:     \&quot;parameters\&quot;: {\n       5:         \&quot;principalId\&quot;: {\n       6:             \&quot;type\&quot;: \&quot;string\&quot;,\n       7:             \&quot;metadata\&quot;: {\n       8:                 \&quot;description\&quot;: \&quot;The principal id to assign the owner role to\&quot;\n       9:             }\n      10:         },\n      11:         \&quot;builtInRoleType\&quot;: {\n      12:             \&quot;type\&quot;: \&quot;string\&quot;,\n&gt;&gt;&gt;   13:             \&quot;defaultValue\&quot;: \&quot;Owner\&quot;,\n      14:             \&quot;metadata\&quot;: {\n      15:                 \&quot;description\&quot;: \&quot;The built-in role type to be assigned to the principalId. Default is Owner.\&quot;\n      16:             }\n      17:         }\n      18:     },\n      19:     \&quot;variables\&quot;: {},\n      20:     \&quot;resources\&quot;: [\n      21:         {\n      22:             \&quot;type\&quot;: \&quot;Microsoft.Authorization/roleAssignments\&quot;,\n      23:             \&quot;apiVersion\&quot;: \&quot;2018-09-01-preview\&quot;,\n      24:             \&quot;name\&quot;: \&quot;[guid(concat(subscription().id,parameters('Owner'),parameters('principalId')))]\&quot;,\n      25:             \&quot;properties\&quot;: {\n      26:                 \&quot;roleDefinitionId\&quot;: \&quot;[concat('/subscriptions/', subscription().subscriptionId, '/providers/Microsoft.Authorization/roleDefinitions/', '8e3af657-a8ff-443c-a75c-2fe8c4bcb635')]\&quot;,\n      27:                 \&quot;principalId\&quot;: \&quot;[parameters('principalId')]\&quot;,\n      28:                 \&quot;roleDefinitionName\&quot;: \&quot;[parameters('builtInRoleType')]\&quot;\n      29:             }\n      30:         }\n      31:     ]\n      32: }&quot;, &quot;highlighted_line_content&quot;: &quot;\&quot;defaultValue\&quot;: \&quot;Owner\&quot;,&quot;}
                </script>
                <script type="application/json" data-finding-context="roleAssignment.deploymentTemplate.json_28" style="display: none;">
{&quot;success&quot;: true, &quot;file_path&quot;: &quot;roleAssignment.deploymentTemplate.json&quot;, &quot;line_number&quot;: 28, &quot;total_lines&quot;: 32, &quot;start_line&quot;: 1, &quot;end_line&quot;: 32, &quot;context_size&quot;: 100, &quot;content_lines&quot;: [{&quot;number&quot;: 1, &quot;content&quot;: &quot;{&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 2, &quot;content&quot;: &quot;    \&quot;$schema\&quot;: \&quot;https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 3, &quot;content&quot;: &quot;    \&quot;contentVersion\&quot;: \&quot;*******\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 4, &quot;content&quot;: &quot;    \&quot;parameters\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 5, &quot;content&quot;: &quot;        \&quot;principalId\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 6, &quot;content&quot;: &quot;            \&quot;type\&quot;: \&quot;string\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 7, &quot;content&quot;: &quot;            \&quot;metadata\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 8, &quot;content&quot;: &quot;                \&quot;description\&quot;: \&quot;The principal id to assign the owner role to\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 9, &quot;content&quot;: &quot;            }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 10, &quot;content&quot;: &quot;        },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 11, &quot;content&quot;: &quot;        \&quot;builtInRoleType\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 12, &quot;content&quot;: &quot;            \&quot;type\&quot;: \&quot;string\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 13, &quot;content&quot;: &quot;            \&quot;defaultValue\&quot;: \&quot;Owner\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 14, &quot;content&quot;: &quot;            \&quot;metadata\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 15, &quot;content&quot;: &quot;                \&quot;description\&quot;: \&quot;The built-in role type to be assigned to the principalId. Default is Owner.\&quot;&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 16, &quot;content&quot;: &quot;            }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 17, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 18, &quot;content&quot;: &quot;    },&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 19, &quot;content&quot;: &quot;    \&quot;variables\&quot;: {},&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 20, &quot;content&quot;: &quot;    \&quot;resources\&quot;: [&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 21, &quot;content&quot;: &quot;        {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 22, &quot;content&quot;: &quot;            \&quot;type\&quot;: \&quot;Microsoft.Authorization/roleAssignments\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 23, &quot;content&quot;: &quot;            \&quot;apiVersion\&quot;: \&quot;2018-09-01-preview\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 24, &quot;content&quot;: &quot;            \&quot;name\&quot;: \&quot;[guid(concat(subscription().id,parameters('Owner'),parameters('principalId')))]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 25, &quot;content&quot;: &quot;            \&quot;properties\&quot;: {&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 26, &quot;content&quot;: &quot;                \&quot;roleDefinitionId\&quot;: \&quot;[concat('/subscriptions/', subscription().subscriptionId, '/providers/Microsoft.Authorization/roleDefinitions/', '8e3af657-a8ff-443c-a75c-2fe8c4bcb635')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 27, &quot;content&quot;: &quot;                \&quot;principalId\&quot;: \&quot;[parameters('principalId')]\&quot;,&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 28, &quot;content&quot;: &quot;                \&quot;roleDefinitionName\&quot;: \&quot;[parameters('builtInRoleType')]\&quot;&quot;, &quot;highlighted&quot;: true}, {&quot;number&quot;: 29, &quot;content&quot;: &quot;            }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 30, &quot;content&quot;: &quot;        }&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 31, &quot;content&quot;: &quot;    ]&quot;, &quot;highlighted&quot;: false}, {&quot;number&quot;: 32, &quot;content&quot;: &quot;}&quot;, &quot;highlighted&quot;: false}], &quot;content_string&quot;: &quot;       1: {\n       2:     \&quot;$schema\&quot;: \&quot;https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#\&quot;,\n       3:     \&quot;contentVersion\&quot;: \&quot;*******\&quot;,\n       4:     \&quot;parameters\&quot;: {\n       5:         \&quot;principalId\&quot;: {\n       6:             \&quot;type\&quot;: \&quot;string\&quot;,\n       7:             \&quot;metadata\&quot;: {\n       8:                 \&quot;description\&quot;: \&quot;The principal id to assign the owner role to\&quot;\n       9:             }\n      10:         },\n      11:         \&quot;builtInRoleType\&quot;: {\n      12:             \&quot;type\&quot;: \&quot;string\&quot;,\n      13:             \&quot;defaultValue\&quot;: \&quot;Owner\&quot;,\n      14:             \&quot;metadata\&quot;: {\n      15:                 \&quot;description\&quot;: \&quot;The built-in role type to be assigned to the principalId. Default is Owner.\&quot;\n      16:             }\n      17:         }\n      18:     },\n      19:     \&quot;variables\&quot;: {},\n      20:     \&quot;resources\&quot;: [\n      21:         {\n      22:             \&quot;type\&quot;: \&quot;Microsoft.Authorization/roleAssignments\&quot;,\n      23:             \&quot;apiVersion\&quot;: \&quot;2018-09-01-preview\&quot;,\n      24:             \&quot;name\&quot;: \&quot;[guid(concat(subscription().id,parameters('Owner'),parameters('principalId')))]\&quot;,\n      25:             \&quot;properties\&quot;: {\n      26:                 \&quot;roleDefinitionId\&quot;: \&quot;[concat('/subscriptions/', subscription().subscriptionId, '/providers/Microsoft.Authorization/roleDefinitions/', '8e3af657-a8ff-443c-a75c-2fe8c4bcb635')]\&quot;,\n      27:                 \&quot;principalId\&quot;: \&quot;[parameters('principalId')]\&quot;,\n&gt;&gt;&gt;   28:                 \&quot;roleDefinitionName\&quot;: \&quot;[parameters('builtInRoleType')]\&quot;\n      29:             }\n      30:         }\n      31:     ]\n      32: }&quot;, &quot;highlighted_line_content&quot;: &quot;\&quot;roleDefinitionName\&quot;: \&quot;[parameters('builtInRoleType')]\&quot;&quot;}
                </script>

    
    <script>
// Report functionality
// Modern JavaScript for enhanced interactivity with multi-select filtering
let searchTimeout;
let allFindings = [];
let activeFilters = new Set(['all']); // Support multiple active filters

document.addEventListener('DOMContentLoaded', function() {
    initializeReport();
    setupEventListeners();
    loadFindings();
});

function initializeReport() {
    // Create code dialog on page load
    createCodeDialog();
    console.log('✅ Code dialog created');

    // Initialize filter buttons with multi-select support
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            // Allow Ctrl/Cmd + click for multi-select
            const isMultiSelect = e.ctrlKey || e.metaKey;
            toggleFilter(this.dataset.severity, isMultiSelect);
        });
    });

    // Initialize search
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.addEventListener('input', debounceSearch);
    }

    // Initialize collapsible sections
    const severityHeaders = document.querySelectorAll('.severity-header');
    severityHeaders.forEach(header => {
        header.addEventListener('click', function() {
            toggleSeverityGroup(this);
        });
    });

    // Initialize domain section interactions
    const domainHeaders = document.querySelectorAll('.domain-header');
    domainHeaders.forEach(header => {
        header.addEventListener('click', function() {
            toggleDomainSection(this);
        });
        // Make domain headers look clickable
        header.style.cursor = 'pointer';
        header.title = 'Click to collapse/expand domain section';
    });

    // Add instructions for multi-select
    addMultiSelectInstructions();
}

function addMultiSelectInstructions() {
    const controlsSection = document.querySelector('.controls-section');
    if (controlsSection) {
        const instructions = document.createElement('div');
        instructions.className = 'multi-select-info';
        instructions.innerHTML = `
            <small style="color: var(--gray-500); font-size: 0.75rem; margin-top: 0.5rem; display: block;">
                <i class="fas fa-info-circle"></i>
                Tip: Hold Ctrl/Cmd and click to select multiple severity levels
            </small>
        `;
        controlsSection.appendChild(instructions);
    }
}

function setupEventListeners() {
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            clearSearch();
            resetFilters();
        }
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            focusSearch();
        }
    });
}

function debounceSearch() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        performSearch();
    }, 300);
}

function performSearch() {
    const searchTerm = document.querySelector('.search-input').value.toLowerCase();
    applyFilters(searchTerm);
}

function toggleFilter(severity, isMultiSelect = false) {
    if (severity === 'all') {
        // If "All" is clicked, reset to show all
        activeFilters.clear();
        activeFilters.add('all');
    } else {
        if (isMultiSelect) {
            // Multi-select mode
            if (activeFilters.has('all')) {
                activeFilters.clear();
            }

            if (activeFilters.has(severity)) {
                activeFilters.delete(severity);
            } else {
                activeFilters.add(severity);
            }

            // If no filters selected, default to "all"
            if (activeFilters.size === 0) {
                activeFilters.add('all');
            }
        } else {
            // Single select mode (default behavior)
            activeFilters.clear();
            activeFilters.add(severity);
        }
    }

    updateFilterButtons();
    applyFilters();
    updateUrlHash();
}

function updateFilterButtons() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(btn => {
        const severity = btn.dataset.severity;
        const isActive = activeFilters.has(severity);
        btn.classList.toggle('active', isActive);

        // Add visual indicator for multi-select
        if (activeFilters.size > 1 && !activeFilters.has('all')) {
            btn.style.position = 'relative';
            if (isActive && !btn.querySelector('.multi-indicator')) {
                const indicator = document.createElement('span');
                indicator.className = 'multi-indicator';
                indicator.innerHTML = '✓';
                indicator.style.cssText = `
                    position: absolute;
                    top: -2px;
                    right: -2px;
                    background: var(--success-green);
                    color: white;
                    border-radius: 50%;
                    width: 16px;
                    height: 16px;
                    font-size: 10px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: bold;
                `;
                btn.appendChild(indicator);
            }
        } else {
            // Remove multi-select indicators
            const indicator = btn.querySelector('.multi-indicator');
            if (indicator) {
                indicator.remove();
            }
        }
    });
}

function applyFilters(searchTerm = '') {
    if (!searchTerm) {
        searchTerm = document.querySelector('.search-input').value.toLowerCase();
    }

    const severityGroups = document.querySelectorAll('.severity-group');
    let totalVisibleCount = 0;

    severityGroups.forEach(group => {
        const groupSeverity = group.dataset.severity;
        const findings = group.querySelectorAll('.finding-item');
        let groupVisibleCount = 0;

        // Check if this severity group should be visible
        const severityMatches = activeFilters.has('all') || activeFilters.has(groupSeverity);

        findings.forEach(finding => {
            const text = finding.textContent.toLowerCase();
            const searchMatches = searchTerm === '' || text.includes(searchTerm);
            const isVisible = severityMatches && searchMatches;

            finding.style.display = isVisible ? 'block' : 'none';
            if (isVisible) {
                groupVisibleCount++;
                totalVisibleCount++;
            }
        });

        // Show/hide the entire severity group
        group.style.display = groupVisibleCount > 0 ? 'block' : 'none';
    });

    updateNoResultsMessage(totalVisibleCount === 0);
    updateFilterSummary();
}

function updateFilterSummary() {
    // Update or create filter summary
    let summary = document.querySelector('.filter-summary');
    if (!summary) {
        summary = document.createElement('div');
        summary.className = 'filter-summary';
        summary.style.cssText = `
            margin-top: 0.5rem;
            padding: 0.5rem;
            background: var(--gray-100);
            border-radius: var(--border-radius-sm);
            font-size: 0.8125rem;
            color: var(--gray-600);
        `;
        document.querySelector('.controls-section').appendChild(summary);
    }

    if (activeFilters.has('all')) {
        summary.textContent = 'Showing all severity levels';
    } else {
        const filterList = Array.from(activeFilters).map(f => f.charAt(0).toUpperCase() + f.slice(1)).join(', ');
        summary.textContent = `Showing: ${filterList} severity levels`;
    }
}

function resetFilters() {
    activeFilters.clear();
    activeFilters.add('all');
    updateFilterButtons();
    applyFilters();
    updateUrlHash();
}

function updateUrlHash() {
    const params = new URLSearchParams();
    if (!activeFilters.has('all')) {
        params.set('filters', Array.from(activeFilters).join(','));
    }
    const searchTerm = document.querySelector('.search-input').value;
    if (searchTerm) {
        params.set('search', searchTerm);
    }

    const hash = params.toString();
    if (hash) {
        window.location.hash = hash;
    } else {
        window.history.replaceState(null, null, window.location.pathname);
    }
}

function loadFromUrlHash() {
    const hash = window.location.hash.substring(1);
    if (hash) {
        const params = new URLSearchParams(hash);
        const filters = params.get('filters');
        const search = params.get('search');

        if (filters) {
            activeFilters.clear();
            filters.split(',').forEach(filter => activeFilters.add(filter.trim()));
            updateFilterButtons();
        }

        if (search) {
            document.querySelector('.search-input').value = search;
        }

        applyFilters();
    }
}

function toggleSeverityGroup(header) {
    const group = header.parentElement;
    const findingsList = group.querySelector('.findings-list');
    const isCollapsed = header.classList.contains('collapsed');

    if (isCollapsed) {
        header.classList.remove('collapsed');
        findingsList.classList.remove('collapsed');
        findingsList.style.maxHeight = findingsList.scrollHeight + 'px';
    } else {
        header.classList.add('collapsed');
        findingsList.classList.add('collapsed');
        findingsList.style.maxHeight = '0';
    }
}

function toggleDomainSection(header) {
    const domainSection = header.parentElement;
    const severityGroups = domainSection.querySelectorAll('.severity-group');
    const isCollapsed = header.classList.contains('collapsed');

    if (isCollapsed) {
        // Expand domain section
        header.classList.remove('collapsed');
        severityGroups.forEach(group => {
            group.style.display = 'block';
        });
        console.log('✅ Expanded domain section');
    } else {
        // Collapse domain section
        header.classList.add('collapsed');
        severityGroups.forEach(group => {
            group.style.display = 'none';
        });
        console.log('✅ Collapsed domain section');
    }
}

function clearSearch() {
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.value = '';
        applyFilters();
    }
}

function focusSearch() {
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.focus();
        searchInput.select();
    }
}

function updateNoResultsMessage(show) {
    const noResults = document.querySelector('.no-findings');
    if (noResults) {
        noResults.style.display = show ? 'block' : 'none';
        if (show) {
            // Update message based on active filters
            const message = noResults.querySelector('p');
            if (activeFilters.has('all')) {
                message.textContent = 'Try adjusting your search terms';
            } else {
                const filterList = Array.from(activeFilters).join(', ');
                message.textContent = `No ${filterList} severity findings match your search. Try different filters or search terms.`;
            }
        }
    }
}

function loadFindings() {
    // Initialize findings data - this would be populated with actual findings
    allFindings = [];
}


// Utility functions (must come before dialogs.js since dialogs.js depends on utils.js functions)
// Utility Functions for IaC Guardian Reports

// Glass UI Enhancement Functions
function addGlassEffects() {
    // Add parallax scrolling effect to background elements
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const parallax = document.querySelector('body::before');
        if (parallax) {
            parallax.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
    });

    // Add hover effects to glass cards
    document.querySelectorAll('.stat-card, .severity-badge, .finding-item').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
            this.style.boxShadow = 'var(--glass-shadow-xl)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = 'var(--glass-shadow)';
        });
    });

    // Add glass ripple effect to buttons
    document.querySelectorAll('.filter-btn, .export-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

// Line Number Interaction Functions
function initLineNumberHighlighting() {
    document.querySelectorAll('.meta-item.line-number').forEach(lineItem => {
        lineItem.addEventListener('click', function() {
            const lineText = this.querySelector('span').textContent;
            const lineNumber = lineText.replace(/[^0-9]/g, '');
            const fileName = this.closest('.finding-item').querySelector('.meta-item:first-child span').textContent;

            // Copy line reference to clipboard
            const lineReference = `${fileName}:${lineNumber}`;
            navigator.clipboard.writeText(lineReference).then(() => {
                showLineNumberFeedback(this, 'Copied to clipboard!');
            }).catch(() => {
                showLineNumberFeedback(this, 'Line: ' + lineNumber);
            });
        });

        // Add hover effect for line numbers
        lineItem.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05) translateY(-2px)';
        });

        lineItem.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) translateY(0)';
        });
    });
}

function showLineNumberFeedback(element, message) {
    const feedback = document.createElement('div');
    feedback.textContent = message;
    feedback.style.cssText = `
        position: absolute;
        top: -40px;
        left: 50%;
        transform: translateX(-50%);
        background: var(--success-green);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: var(--border-radius-sm);
        font-size: 0.75rem;
        font-weight: 600;
        box-shadow: var(--glass-shadow-lg);
        z-index: 1001;
        animation: feedback-bounce 2s ease forwards;
        pointer-events: none;
    `;

    element.style.position = 'relative';
    element.appendChild(feedback);

    setTimeout(() => {
        if (feedback.parentNode) {
            feedback.parentNode.removeChild(feedback);
        }
    }, 2000);
}

// File Content Fetching Functions
async function fetchRealFileContentWithContext(filePath, lineNumber) {
    try {
        console.log('🔍 Fetching real file content with context for:', filePath, 'line:', lineNumber);

        // Clear any cached results to ensure fresh fetch
        const timestamp = Date.now();
        console.log('🕒 Fetch timestamp:', timestamp);

        // Create finding ID to match embedded context data
        const findingId = `${filePath}:${lineNumber}`;
        console.log('🔧 Original finding ID:', findingId);

        // Enhanced normalization patterns that match Python exactly
        const normalizedIds = [
            // Primary approach - matches Python normalization exactly
            findingId.replace(/\\\\/g, '/').replace(/:/g, '_').replace(/\//g, '_'),
            // Alternative approaches for robustness
            findingId.split('\\').join('/').split(':').join('_').split('/').join('_'),
            findingId.replace(/\\\\/g, '/').replace(/:/g, '_').replace(/\//g, '_'),
            findingId.replace(/\\\\/g, '_').replace(/:/g, '_').replace(/\//g, '_'),
            // Keep original separators for debugging
            findingId,
            findingId.replace(/\\\\/g, '/'),
            findingId.split('\\\\').join('/'),
            // URL-encoded version
            encodeURIComponent(findingId),
            // Base64 encoded version (fallback)
            btoa(findingId).replace(/[+\/=]/g, '_'),
            // Additional patterns for edge cases
            findingId.replace(/\\\\/g, '_').replace(/:/g, '_'),
            findingId.replace(/[\\/:]/g, '_'),
            // Try with just filename
            `${filePath.split(/[\\\/]/).pop()}:${lineNumber}`.replace(/:/g, '_'),
            // Try with relative path normalization
            findingId.replace(/^.*[\\\/]/, '').replace(/:/g, '_')
        ];

        console.log('🔧 Trying normalized IDs:', normalizedIds);

        // Debug: List all available embedded context elements
        const allContextElements = document.querySelectorAll('[data-finding-context]');
        console.log('📄 Available embedded context IDs:', Array.from(allContextElements).map(el => el.getAttribute('data-finding-context')));

        // Helper function to escape CSS selector values
        function escapeCSSSelector(value) {
            // Escape special characters that are invalid in CSS selectors
            return value.replace(/([\\"'])/g, '\\$1')
                        .replace(/\n/g, '\\A ')
                        .replace(/\r/g, '\\D ')
                        .replace(/\t/g, '\\9 ');
        }

        // Try to find embedded context data with any of the normalized IDs
        let contextElement = null;
        let usedId = null;

        for (const normalizedId of normalizedIds) {
            try {
                const escapedId = escapeCSSSelector(normalizedId);
                contextElement = document.querySelector(`[data-finding-context="${escapedId}"]`);
                if (contextElement) {
                    usedId = normalizedId;
                    console.log('✅ Found context element with ID:', usedId);
                    break;
                }
            } catch (selectorError) {
                console.warn('⚠️ Invalid CSS selector for ID:', normalizedId, 'Error:', selectorError.message);
                // Try alternative approach with getAttribute
                const allElements = document.querySelectorAll('[data-finding-context]');
                for (const element of allElements) {
                    if (element.getAttribute('data-finding-context') === normalizedId) {
                        contextElement = element;
                        usedId = normalizedId;
                        console.log('✅ Found context element with ID (fallback method):', usedId);
                        break;
                    }
                }
                if (contextElement) break;
            }
        }

        console.log('🎯 Found embedded context element:', !!contextElement, 'using ID:', usedId);

        if (contextElement) {
            let contextJson = contextElement.textContent || contextElement.innerText;
            console.log('📝 Context JSON length:', contextJson.length);
            console.log('📝 First 200 chars of raw JSON:', contextJson.substring(0, 200));

            // Unescape HTML entities that were escaped during embedding
            contextJson = contextJson
                .replace(/&quot;/g, '"')
                .replace(/&lt;/g, '<')
                .replace(/&gt;/g, '>')
                .replace(/&amp;/g, '&');

            console.log('📝 First 200 chars of unescaped JSON:', contextJson.substring(0, 200));

            try {
                const contextData = JSON.parse(contextJson);
                console.log('✅ Parsed context data successfully:', contextData.success);

                if (contextData.success) {
                    console.log('📊 Context info:', {
                        file_path: contextData.file_path,
                        total_lines: contextData.total_lines,
                        start_line: contextData.start_line,
                        end_line: contextData.end_line,
                        content_lines_count: contextData.content_lines?.length
                    });

                    // Validate that we have the expected data structure
                    if (!contextData.content_lines || !Array.isArray(contextData.content_lines)) {
                        console.error('❌ Invalid context data structure - missing content_lines array');
                        return { success: false, error: 'Invalid context data structure' };
                    }

                    return contextData;
                } else {
                    console.warn('⚠️ Context data indicates failure:', contextData.error);
                    return contextData; // Return the error data for fallback handling
                }
            } catch (parseError) {
                console.error('❌ Error parsing context JSON:', parseError);
                console.error('❌ Raw JSON content (first 500 chars):', contextJson.substring(0, 500));
                return { success: false, error: `JSON parse error: ${parseError.message}` };
            }
        }

        console.log('❌ No content available from any source');
        console.log('❌ Debug info - Finding ID:', findingId, 'Normalized IDs tried:', normalizedIds);
        return { success: false, error: 'File content not available - no embedded data found for any path variation' };

    } catch (error) {
        console.error('❌ Error fetching file content with context:', error);
        return { success: false, error: error.message };
    }
}

function displayRealCodeWithLineNumbers(contextData, highlightLine) {
    const lineNumbersEl = document.getElementById('line-numbers');
    const codeContentEl = document.getElementById('code-content');

    if (!lineNumbersEl || !codeContentEl) {
        console.error('❌ Line numbers or code content elements not found');
        return;
    }

    const startLine = contextData.start_line;
    const contentLines = contextData.content_lines;

    // Generate line numbers and code content
    let lineNumbersHTML = '';
    let codeHTML = '';

    contentLines.forEach((line, index) => {
        const lineNum = startLine + index;
        const isHighlighted = lineNum === parseInt(highlightLine);
        
        lineNumbersHTML += `<div class="line-number ${isHighlighted ? 'highlighted' : ''}">${lineNum}</div>`;
        codeHTML += `<div class="code-line ${isHighlighted ? 'highlighted' : ''}">${escapeHtml(line)}</div>`;
    });

    lineNumbersEl.innerHTML = lineNumbersHTML;
    codeContentEl.innerHTML = codeHTML;

    // Scroll to highlighted line
    setTimeout(() => {
        const highlightedLine = codeContentEl.querySelector('.code-line.highlighted');
        if (highlightedLine) {
            highlightedLine.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }, 100);
}

function showContentWarning(message) {
    const dialogContent = document.querySelector('.code-dialog-content');
    if (dialogContent) {
        // Remove any existing warning
        const existingWarning = dialogContent.querySelector('.content-warning');
        if (existingWarning) {
            existingWarning.remove();
        }

        // Create new warning
        const warning = document.createElement('div');
        warning.className = 'content-warning';
        warning.style.cssText = `
            background: var(--warning-amber);
            color: var(--text-on-dark);
            padding: 0.75rem 1rem;
            margin-bottom: 1rem;
            border-radius: var(--border-radius-sm);
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        `;
        warning.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            <span>${message}</span>
        `;

        // Insert at the beginning of dialog content
        dialogContent.insertBefore(warning, dialogContent.firstChild);
    }
}

// Animation keyframes (add to CSS)
const animationCSS = `
@keyframes feedback-bounce {
    0% { opacity: 0; transform: translateX(-50%) translateY(10px) scale(0.8); }
    20% { opacity: 1; transform: translateX(-50%) translateY(0) scale(1.1); }
    40% { transform: translateX(-50%) translateY(0) scale(1); }
    100% { opacity: 0; transform: translateX(-50%) translateY(-10px) scale(0.9); }
}

@keyframes tooltip-fade-in {
    from { opacity: 0; transform: translateX(-50%) translateY(5px); }
    to { opacity: 1; transform: translateX(-50%) translateY(0); }
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}
`;

// Add animation CSS to document
if (!document.getElementById('utils-animations')) {
    const style = document.createElement('style');
    style.id = 'utils-animations';
    style.textContent = animationCSS;
    document.head.appendChild(style);
}

// Additional utility functions for enhanced user experience
function initTooltips() {
    // Add tooltips to control IDs and severity badges
    document.querySelectorAll('.control-id').forEach(controlId => {
        controlId.addEventListener('mouseenter', function(e) {
            showTooltip(e.target, `Azure Security Benchmark Control: ${this.textContent}`);
        });

        controlId.addEventListener('mouseleave', function() {
            hideTooltip();
        });
    });

    // Add tooltips to severity badges
    document.querySelectorAll('.severity-badge').forEach(badge => {
        const severity = badge.classList[1]; // Get severity class
        const severityDescriptions = {
            'critical': 'Critical: Immediate action required - high risk of security breach',
            'high': 'High: Important security issue that should be addressed soon',
            'medium': 'Medium: Moderate security concern that should be reviewed',
            'low': 'Low: Minor security improvement opportunity'
        };

        badge.addEventListener('mouseenter', function(e) {
            showTooltip(e.target, severityDescriptions[severity] || 'Security finding');
        });

        badge.addEventListener('mouseleave', function() {
            hideTooltip();
        });
    });
}

function showTooltip(element, text) {
    // Remove existing tooltip
    hideTooltip();

    const tooltip = document.createElement('div');
    tooltip.id = 'custom-tooltip';
    tooltip.textContent = text;
    tooltip.style.cssText = `
        position: absolute;
        background: var(--dark-gray100);
        color: var(--text-on-glass);
        padding: 0.5rem 0.75rem;
        border-radius: var(--border-radius-sm);
        font-size: 0.75rem;
        font-weight: 500;
        box-shadow: var(--glass-shadow-lg);
        z-index: 10002;
        max-width: 300px;
        word-wrap: break-word;
        border: 1px solid var(--glass-border);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        animation: tooltip-fade-in 0.2s ease;
        pointer-events: none;
    `;

    document.body.appendChild(tooltip);

    // Position tooltip
    const rect = element.getBoundingClientRect();
    const tooltipRect = tooltip.getBoundingClientRect();

    let left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
    let top = rect.top - tooltipRect.height - 8;

    // Adjust if tooltip goes off screen
    if (left < 8) left = 8;
    if (left + tooltipRect.width > window.innerWidth - 8) {
        left = window.innerWidth - tooltipRect.width - 8;
    }
    if (top < 8) {
        top = rect.bottom + 8;
    }

    tooltip.style.left = left + 'px';
    tooltip.style.top = top + 'px';
}

function hideTooltip() {
    const tooltip = document.getElementById('custom-tooltip');
    if (tooltip) {
        tooltip.remove();
    }
}

// Keyboard shortcuts
function initKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K: Focus search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            focusSearch();
        }

        // Ctrl/Cmd + E: Export JSON
        if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
            e.preventDefault();
            if (typeof exportToJson === 'function') {
                exportToJson();
            }
        }

        // Ctrl/Cmd + P: Print
        if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
            e.preventDefault();
            window.print();
        }

        // Escape: Clear search and reset filters
        if (e.key === 'Escape') {
            if (typeof clearSearch === 'function') clearSearch();
            if (typeof resetFilters === 'function') resetFilters();
        }

        // Arrow keys for navigation between findings
        if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
            navigateFindings(e.key === 'ArrowDown' ? 'next' : 'prev');
        }
    });
}

function navigateFindings(direction) {
    const visibleFindings = Array.from(document.querySelectorAll('.finding-item'))
        .filter(item => item.style.display !== 'none');

    if (visibleFindings.length === 0) return;

    const currentFocused = document.querySelector('.finding-item.keyboard-focused');
    let currentIndex = currentFocused ? visibleFindings.indexOf(currentFocused) : -1;

    // Remove current focus
    if (currentFocused) {
        currentFocused.classList.remove('keyboard-focused');
    }

    // Calculate next index
    if (direction === 'next') {
        currentIndex = (currentIndex + 1) % visibleFindings.length;
    } else {
        currentIndex = currentIndex <= 0 ? visibleFindings.length - 1 : currentIndex - 1;
    }

    // Focus new finding
    const nextFinding = visibleFindings[currentIndex];
    nextFinding.classList.add('keyboard-focused');
    nextFinding.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

// Performance monitoring
function initPerformanceMonitoring() {
    // Monitor search performance
    let searchStartTime;
    const originalPerformSearch = window.performSearch;

    if (originalPerformSearch) {
        window.performSearch = function() {
            searchStartTime = performance.now();
            originalPerformSearch.apply(this, arguments);

            // Log search performance
            requestAnimationFrame(() => {
                const searchTime = performance.now() - searchStartTime;
                if (searchTime > 100) { // Log slow searches
                    console.warn(`Slow search detected: ${searchTime.toFixed(2)}ms`);
                }
            });
        };
    }

    // Monitor filter performance
    let filterStartTime;
    const originalApplyFilters = window.applyFilters;

    if (originalApplyFilters) {
        window.applyFilters = function() {
            filterStartTime = performance.now();
            originalApplyFilters.apply(this, arguments);

            requestAnimationFrame(() => {
                const filterTime = performance.now() - filterStartTime;
                if (filterTime > 50) { // Log slow filters
                    console.warn(`Slow filter detected: ${filterTime.toFixed(2)}ms`);
                }
            });
        };
    }
}

// Add keyboard focus styles
const keyboardFocusCSS = `
.finding-item.keyboard-focused {
    outline: 2px solid var(--primary500);
    outline-offset: 2px;
    background: var(--glass-white-strong);
    transform: translateY(-2px);
    box-shadow: var(--glass-shadow-lg);
}

.keyboard-shortcuts-help {
    position: fixed;
    bottom: 1rem;
    left: 1rem;
    background: var(--glass-white);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-sm);
    padding: 0.75rem;
    font-size: 0.75rem;
    color: var(--text-secondary);
    z-index: 1000;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.keyboard-shortcuts-help:hover {
    opacity: 1;
}
`;

if (!document.getElementById('keyboard-focus-styles')) {
    const style = document.createElement('style');
    style.id = 'keyboard-focus-styles';
    style.textContent = keyboardFocusCSS;
    document.head.appendChild(style);
}

// Add keyboard shortcuts help
function addKeyboardShortcutsHelp() {
    const help = document.createElement('div');
    help.className = 'keyboard-shortcuts-help';
    help.innerHTML = `
        <div><strong>Keyboard Shortcuts:</strong></div>
        <div>Ctrl+K: Search</div>
        <div>Ctrl+E: Export</div>
        <div>Ctrl+P: Print</div>
        <div>↑↓: Navigate</div>
        <div>Esc: Reset</div>
    `;
    document.body.appendChild(help);
}

// Initialize utility functions when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    addGlassEffects();
    initLineNumberHighlighting();
    initTooltips();
    initKeyboardShortcuts();
    initPerformanceMonitoring();
    addKeyboardShortcutsHelp();
});


// Dialog functionality
// Code Dialog Functions for IaC Guardian Reports

// Global variable to track current dialog request
let currentDialogRequestId = 0;

function showCodeDialog(filePath, lineNumber, controlId, severity) {
    // Create dialog if it doesn't exist
    let dialog = document.getElementById('code-dialog-overlay');
    if (!dialog) {
        createCodeDialog();
        dialog = document.getElementById('code-dialog-overlay');
    }

    // Update dialog content
    updateCodeDialog(filePath, lineNumber, controlId, severity);

    // Show dialog
    dialog.style.display = 'flex';
    document.body.style.overflow = 'hidden';

    // Focus trap
    dialog.querySelector('.code-dialog-close').focus();
}

function createCodeDialog() {
    const dialogHTML = `
        <div id="code-dialog-overlay" class="code-dialog-overlay" onclick="closeCodeDialog(event)">
            <div class="code-dialog" onclick="event.stopPropagation()">
                <div class="code-dialog-header">
                    <div class="code-dialog-title">
                        <i class="fas fa-file-code"></i>
                        <span id="dialog-title">Code Snippet</span>
                    </div>
                    <button class="code-dialog-close" onclick="closeCodeDialog()" title="Close dialog">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="code-dialog-content">
                    <div class="code-snippet-container">
                        <div class="code-snippet-with-lines">
                            <div class="line-numbers" id="line-numbers"></div>
                            <div class="code-content" id="code-content"></div>
                        </div>
                    </div>
                </div>
                <div class="code-dialog-footer">
                    <div class="code-dialog-info">
                        <span id="dialog-file-info"></span>
                        <span id="dialog-severity-info"></span>
                    </div>
                    <div class="code-dialog-actions">
                        <button class="code-dialog-btn" onclick="copyCodeSnippet()" title="Copy code to clipboard">
                            <i class="fas fa-copy"></i>
                            Copy Code
                        </button>
                        <button class="code-dialog-btn primary" onclick="closeCodeDialog()">
                            <i class="fas fa-check"></i>
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', dialogHTML);

    // Add dialog-specific CSS styles
    addDialogStyles();

    // Add keyboard event listener
    document.addEventListener('keydown', handleDialogKeydown);
}

function addDialogStyles() {
    if (!document.getElementById('code-dialog-styles')) {
        const dialogCSS = `
        <style id="code-dialog-styles">
        /* Code Dialog Overlay */
        .code-dialog-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            padding: 1rem;
        }

        /* Code Dialog Container */
        .code-dialog {
            background: var(--glass-white);
            backdrop-filter: var(--glass-blur-strong);
            -webkit-backdrop-filter: var(--glass-blur-strong);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--glass-shadow-xl);
            max-width: 90vw;
            max-height: 90vh;
            width: 1000px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* Dialog Header */
        .code-dialog-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid var(--glass-border);
            background: var(--glass-white-light);
        }

        .code-dialog-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-on-glass);
        }

        .code-dialog-close {
            background: none;
            border: none;
            color: var(--text-interactive);
            font-size: 1.25rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: var(--border-radius-sm);
            transition: all 0.2s ease;
        }

        .code-dialog-close:hover {
            background: var(--glass-white-strong);
            color: var(--text-hover);
            transform: scale(1.1);
        }

        /* Dialog Content */
        .code-dialog-content {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .code-snippet-container {
            flex: 1;
            overflow: auto;
            background: var(--dark-gray100);
            border-radius: var(--border-radius-sm);
            margin: 1rem;
        }

        .code-snippet-with-lines {
            display: flex;
            min-height: 100%;
        }

        /* Line Numbers */
        .line-numbers {
            background: var(--dark-gray50);
            color: var(--text-muted);
            padding: 1rem 0.75rem;
            font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
            font-size: 0.875rem;
            line-height: 1.5;
            text-align: right;
            user-select: none;
            border-right: 1px solid var(--glass-border);
            min-width: 4rem;
        }

        .line-number {
            padding: 0.125rem 0;
            transition: all 0.2s ease;
        }

        .line-number.highlighted {
            background: var(--critical-glass);
            color: var(--critical-text);
            font-weight: 600;
            border-radius: 4px;
            margin: 0 -0.25rem;
            padding: 0.125rem 0.25rem;
        }

        .line-number.highlighted-security {
            background: var(--danger-red);
            color: white;
        }

        /* Code Content */
        .code-content {
            flex: 1;
            padding: 1rem;
            font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
            font-size: 0.875rem;
            line-height: 1.5;
            color: var(--text-on-glass);
            overflow-x: auto;
        }

        .code-line {
            padding: 0.125rem 0;
            white-space: pre;
            transition: all 0.2s ease;
        }

        .code-line.highlighted {
            background: var(--critical-glass);
            border-radius: 4px;
            margin: 0 -0.5rem;
            padding: 0.125rem 0.5rem;
            border-left: 4px solid var(--critical-text);
        }

        .code-line.highlighted-security {
            background: var(--danger-red);
            color: white;
        }

        .security-marker {
            color: var(--critical-text);
            font-weight: 600;
            margin-left: 1rem;
            font-size: 0.75rem;
            opacity: 0.8;
        }

        /* Dialog Footer */
        .code-dialog-footer {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1.5rem 2rem;
            border-top: 1px solid var(--glass-border);
            background: var(--glass-white-light);
        }

        .code-dialog-info {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .code-dialog-actions {
            display: flex;
            gap: 1rem;
        }

        .code-dialog-btn {
            padding: 0.75rem 1.5rem;
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-sm);
            background: var(--glass-white-light);
            color: var(--text-interactive);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.875rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .code-dialog-btn:hover {
            background: var(--glass-white-strong);
            transform: translateY(-2px);
            box-shadow: var(--glass-shadow);
        }

        .code-dialog-btn.primary {
            background: var(--primary500);
            color: var(--text-on-dark);
            border-color: var(--primary600);
        }

        .code-dialog-btn.primary:hover {
            background: var(--primary600);
        }

        /* Content Warning */
        .content-warning {
            background: var(--warning-amber);
            color: var(--text-on-dark);
            padding: 0.75rem 1rem;
            margin-bottom: 1rem;
            border-radius: var(--border-radius-sm);
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: var(--glass-shadow);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .code-dialog {
                width: 95vw;
                max-height: 85vh;
            }

            .code-dialog-header,
            .code-dialog-footer {
                padding: 1rem;
            }

            .code-dialog-footer {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }

            .code-dialog-actions {
                justify-content: center;
            }

            .line-numbers {
                min-width: 3rem;
                padding: 1rem 0.5rem;
            }

            .code-content {
                padding: 1rem 0.5rem;
            }
        }
        </style>
        `;
        document.head.insertAdjacentHTML('beforeend', dialogCSS);
    }
}

function updateCodeDialog(filePath, lineNumber, controlId, severity) {
    // Generate unique request ID to prevent race conditions
    const requestId = ++currentDialogRequestId;
    console.log('🔄 Updating code dialog for:', filePath, 'line:', lineNumber, 'control:', controlId, 'requestId:', requestId);

    // Clear any existing content and warnings first
    clearCodeDialogContent();

    // Show loading state
    showCodeDialogLoading();

    // Ensure dialog exists before updating
    if (!document.getElementById('code-dialog-overlay')) {
        createCodeDialog();
    }

    // Update title and info with safety checks
    const titleEl = document.getElementById('dialog-title');
    const fileInfoEl = document.getElementById('dialog-file-info');
    const severityInfoEl = document.getElementById('dialog-severity-info');

    if (titleEl) {
        titleEl.textContent = `${controlId} - Code Snippet`;
    } else {
        console.error('❌ dialog-title element not found');
    }

    if (fileInfoEl) {
        fileInfoEl.innerHTML = `<i class="fas fa-file"></i> ${filePath}`;
    } else {
        console.error('❌ dialog-file-info element not found');
    }

    if (severityInfoEl) {
        severityInfoEl.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${severity.toUpperCase()} Severity`;
    } else {
        console.error('❌ dialog-severity-info element not found');
    }

    // Fetch real file content with ±100 lines context
    fetchRealFileContentWithContext(filePath, lineNumber).then(content => {
        // Check if this is still the current request (prevent race conditions)
        if (requestId !== currentDialogRequestId) {
            console.log('🚫 Ignoring outdated request', requestId, 'current is', currentDialogRequestId);
            return;
        }

        // Hide loading state
        hideCodeDialogLoading();

        if (content.success) {
            displayRealCodeWithLineNumbers(content, lineNumber);
            console.log('✅ Successfully displayed real file content for', filePath, 'line', lineNumber, 'requestId:', requestId);
        } else {
            // Fallback to sample code if real content unavailable
            const sampleCode = generateSampleCode(filePath, lineNumber, severity);
            displayCodeWithLineNumbers(sampleCode, lineNumber);

            // Show detailed warning about fallback content
            const errorMsg = content.error || 'File content not embedded in report';
            showContentWarning(`Real file content unavailable: ${errorMsg}. Showing representative example.`);
            console.warn('⚠️ Using fallback sample code for', filePath, ':', errorMsg);
        }
    }).catch(error => {
        // Check if this is still the current request
        if (requestId !== currentDialogRequestId) {
            console.log('🚫 Ignoring outdated error for request', requestId);
            return;
        }

        // Hide loading state
        hideCodeDialogLoading();

        console.error('❌ Failed to fetch real file content for', filePath, ':', error);
        const sampleCode = generateSampleCode(filePath, lineNumber, severity);
        displayCodeWithLineNumbers(sampleCode, lineNumber);
        showContentWarning(`Error loading file content: ${error.message || error}. Showing representative example.`);
    });
}

function clearCodeDialogContent() {
    // Ensure dialog exists
    if (!document.getElementById('code-dialog-overlay')) {
        console.log('🧹 Dialog not found, creating it first');
        createCodeDialog();
        return;
    }

    // Clear previous content with safety checks
    const lineNumbersEl = document.getElementById('line-numbers');
    const codeContentEl = document.getElementById('code-content');

    if (lineNumbersEl) {
        lineNumbersEl.innerHTML = '';
    } else {
        console.warn('⚠️ line-numbers element not found');
    }

    if (codeContentEl) {
        codeContentEl.innerHTML = '';
    } else {
        console.warn('⚠️ code-content element not found');
    }

    // Remove any existing warnings
    const dialogContent = document.querySelector('.code-dialog-content');
    if (dialogContent) {
        const existingWarning = dialogContent.querySelector('.content-warning');
        if (existingWarning) {
            existingWarning.remove();
        }
    } else {
        console.warn('⚠️ code-dialog-content element not found');
    }

    console.log('🧹 Cleared previous code dialog content');
}

function showCodeDialogLoading() {
    // Ensure dialog exists
    if (!document.getElementById('code-dialog-overlay')) {
        console.log('📤 Dialog not found, creating it first');
        createCodeDialog();
    }

    const codeContentEl = document.getElementById('code-content');
    if (codeContentEl) {
        codeContentEl.innerHTML = `
            <div style="
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 2rem;
                color: var(--text-accent);
                font-size: 0.875rem;
            ">
                <i class="fas fa-spinner fa-spin" style="margin-right: 0.5rem;"></i>
                Loading file content...
            </div>
        `;
    } else {
        console.error('❌ code-content element not found for loading state');
    }
}

function hideCodeDialogLoading() {
    // Loading content will be replaced by actual content, so no explicit action needed
    console.log('📤 Loading state will be replaced by content');
}

function closeCodeDialog(event) {
    if (event && event.target !== event.currentTarget) {
        return; // Don't close if clicking inside dialog
    }

    const dialog = document.getElementById('code-dialog-overlay');
    if (dialog) {
        dialog.style.display = 'none';
        document.body.style.overflow = '';
    }
}

function handleDialogKeydown(event) {
    if (event.key === 'Escape') {
        closeCodeDialog();
    }
}

function copyCodeSnippet() {
    const codeContent = document.getElementById('code-content');
    if (codeContent) {
        // Get clean text content without HTML tags
        let text = '';
        const codeLines = codeContent.querySelectorAll('.code-line');

        if (codeLines.length > 0) {
            // Extract text from each line, preserving line breaks
            codeLines.forEach((line, index) => {
                // Remove security markers from copied text
                const lineText = line.textContent || line.innerText;
                const cleanText = lineText.replace(/\s*← Security Issue\s*$/, '');
                text += cleanText;
                if (index < codeLines.length - 1) {
                    text += '\n';
                }
            });
        } else {
            // Fallback to full content
            text = codeContent.textContent || codeContent.innerText;
        }

        // Copy to clipboard
        navigator.clipboard.writeText(text).then(() => {
            // Show feedback
            const btn = event.target.closest('button');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
            btn.style.background = 'var(--success-green)';
            btn.style.borderColor = 'var(--success-green)';

            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.style.background = '';
                btn.style.borderColor = '';
            }, 2000);

            // Also show a toast notification
            showCopyToast('Code snippet copied to clipboard!');
        }).catch(err => {
            console.error('Failed to copy code:', err);

            // Fallback for older browsers
            try {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.opacity = '0';
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                showCopyToast('Code snippet copied to clipboard!');
            } catch (fallbackErr) {
                console.error('Fallback copy also failed:', fallbackErr);
                showCopyToast('Failed to copy code snippet', 'error');
            }
        });
    }
}

function showCopyToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: ${type === 'success' ? 'var(--success-green)' : 'var(--danger-red)'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: var(--border-radius-sm);
        box-shadow: var(--glass-shadow-xl);
        z-index: 10003;
        font-size: 0.875rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        animation: copyToastAnimation 0.3s ease;
    `;

    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : 'exclamation-triangle'}"></i>
        ${message}
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.style.animation = 'copyToastFadeOut 0.3s ease';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 2000);
}

// Add copy toast animations
const copyToastCSS = `
@keyframes copyToastAnimation {
    0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
    100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
}

@keyframes copyToastFadeOut {
    0% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
    100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
}
`;

if (!document.getElementById('copy-toast-styles')) {
    const style = document.createElement('style');
    style.id = 'copy-toast-styles';
    style.textContent = copyToastCSS;
    document.head.appendChild(style);
}

function generateSampleCode(filePath, lineNumber, severity) {
    // This is a sample code generator - in real implementation, you'd fetch actual file content
    const fileExtension = filePath.split('.').pop().toLowerCase();

    if (fileExtension === 'bicep') {
        return `// Azure Bicep Template
param location string = resourceGroup().location
param storageAccountName string

resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {
  name: storageAccountName
  location: location
  kind: 'StorageV2'
  sku: {
    name: 'Standard_LRS'  // ← Security issue on this line
  }
  properties: {
    allowBlobPublicAccess: true  // ← Potential security risk
    minimumTlsVersion: 'TLS1_0'  // ← Outdated TLS version
    supportsHttpsTrafficOnly: false
  }
}`;
    } else if (fileExtension === 'tf') {
        return `# Terraform Configuration
resource "azurerm_storage_account" "example" {
  name                     = var.storage_account_name
  resource_group_name      = var.resource_group_name
  location                 = var.location
  account_tier             = "Standard"
  account_replication_type = "LRS"  # ← Security issue on this line
  
  # Security configurations
  allow_blob_public_access = true    # ← Potential security risk
  min_tls_version         = "TLS1_0" # ← Outdated TLS version
  https_traffic_only      = false    # ← Should be true
}`;
    } else {
        return `{
  "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
  "contentVersion": "*******",
  "parameters": {
    "storageAccountName": {
      "type": "string"
    }
  },
  "resources": [
    {
      "type": "Microsoft.Storage/storageAccounts",
      "apiVersion": "2021-04-01",
      "name": "[parameters('storageAccountName')]",
      "location": "[resourceGroup().location]",
      "sku": {
        "name": "Standard_LRS"
      },
      "properties": {
        "allowBlobPublicAccess": true,
        "minimumTlsVersion": "TLS1_0",
        "supportsHttpsTrafficOnly": false
      }
    }
  ]
}`;
    }
}

function displayCodeWithLineNumbers(code, highlightLine) {
    const lines = code.split('\n');
    const lineNumbersEl = document.getElementById('line-numbers');
    const codeContentEl = document.getElementById('code-content');

    if (!lineNumbersEl || !codeContentEl) {
        console.error('❌ Line numbers or code content elements not found');
        return;
    }

    // Generate line numbers
    let lineNumbersHTML = '';
    let codeHTML = '';

    lines.forEach((line, index) => {
        const lineNum = index + 1;
        const isHighlighted = lineNum === parseInt(highlightLine);
        
        lineNumbersHTML += `<div class="line-number ${isHighlighted ? 'highlighted' : ''}">${lineNum}</div>`;
        codeHTML += `<div class="code-line ${isHighlighted ? 'highlighted' : ''}">${escapeHtml(line)}</div>`;
    });

    lineNumbersEl.innerHTML = lineNumbersHTML;
    codeContentEl.innerHTML = codeHTML;

    // Scroll to highlighted line
    setTimeout(() => {
        const highlightedLine = codeContentEl.querySelector('.code-line.highlighted');
        if (highlightedLine) {
            highlightedLine.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }, 100);
}

function displayRealCodeWithLineNumbers(contextData, highlightLine) {
    const lineNumbersEl = document.getElementById('line-numbers');
    const codeContentEl = document.getElementById('code-content');

    if (!lineNumbersEl || !codeContentEl) {
        console.error('❌ Line numbers or code content elements not found');
        return;
    }

    console.log('📊 Displaying real code with context:', {
        start_line: contextData.start_line,
        end_line: contextData.end_line,
        total_lines: contextData.total_lines,
        content_lines_count: contextData.content_lines?.length,
        highlight_line: highlightLine
    });

    const contentLines = contextData.content_lines;
    if (!contentLines || !Array.isArray(contentLines)) {
        console.error('❌ Invalid content_lines data:', contentLines);
        return;
    }

    // Generate line numbers and code content
    let lineNumbersHTML = '';
    let codeHTML = '';

    contentLines.forEach((lineInfo) => {
        const lineNum = lineInfo.number;
        const lineContent = lineInfo.content || '';
        const isHighlighted = lineInfo.highlighted || lineNum === parseInt(highlightLine);

        // Add severity-based styling for highlighted lines
        const severityClass = isHighlighted ? 'highlighted-security' : '';
        const securityMarker = isHighlighted ? ' <span class="security-marker">← Security Issue</span>' : '';

        lineNumbersHTML += `<div class="line-number ${isHighlighted ? 'highlighted' : ''} ${severityClass}">${lineNum}</div>`;
        codeHTML += `<div class="code-line ${isHighlighted ? 'highlighted' : ''} ${severityClass}">${escapeHtml(lineContent)}${securityMarker}</div>`;
    });

    lineNumbersEl.innerHTML = lineNumbersHTML;
    codeContentEl.innerHTML = codeHTML;

    // Scroll to highlighted line
    setTimeout(() => {
        const highlightedLine = codeContentEl.querySelector('.code-line.highlighted');
        if (highlightedLine) {
            highlightedLine.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }, 100);

    console.log('✅ Successfully displayed real code with line numbers');
}

function showContentWarning(message) {
    const dialogContent = document.querySelector('.code-dialog-content');
    if (dialogContent) {
        // Remove any existing warning
        const existingWarning = dialogContent.querySelector('.content-warning');
        if (existingWarning) {
            existingWarning.remove();
        }

        // Create new warning
        const warning = document.createElement('div');
        warning.className = 'content-warning';
        warning.style.cssText = `
            background: var(--warning-amber);
            color: var(--text-on-dark);
            padding: 0.75rem 1rem;
            margin-bottom: 1rem;
            border-radius: var(--border-radius-sm);
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: var(--glass-shadow);
        `;
        warning.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            <span>${message}</span>
        `;

        // Insert at the beginning of dialog content
        dialogContent.insertBefore(warning, dialogContent.firstChild);
    }
}

function parseFileContentWithAccurateLines(content, targetLine, contextLines) {
    // Split content into lines (preserving empty lines)
    const lines = content.split('\n');
    const totalLines = lines.length;
    const targetLineNum = parseInt(targetLine);

    // Validate target line
    if (targetLineNum < 1 || targetLineNum > totalLines) {
        return {
            success: false,
            error: `Line ${targetLineNum} out of range (1-${totalLines})`
        };
    }

    // Calculate context range with specified context lines
    const startLine = Math.max(1, targetLineNum - contextLines);
    const endLine = Math.min(totalLines, targetLineNum + contextLines);

    // Build content lines with accurate numbering
    const contentLines = [];
    for (let lineNum = startLine; lineNum <= endLine; lineNum++) {
        const lineIndex = lineNum - 1; // Convert to 0-based index
        contentLines.push({
            number: lineNum,
            content: lines[lineIndex] || '',
            highlighted: lineNum === targetLineNum
        });
    }

    return {
        success: true,
        file_path: filePath,
        total_lines: totalLines,
        highlighted_line: targetLineNum,
        start_line: startLine,
        end_line: endLine,
        context_size: contextLines,
        content_lines: contentLines
    };
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

    </script>
    
    

            <script>
            // Enhanced Validation Metadata (for internal use only)
            window.validationMetadata = {
  "generation_timestamp": "2025-06-24T17:07:42.611095",
  "total_findings": 17,
  "validation_statistics": {
    "total_validations": 0,
    "successful_validations": 0,
    "corrections_made": 0,
    "fictional_ids_prevented": 0,
    "domain_corrections": 0
  },
  "analysis_statistics": {},
  "control_id_validation": {
    "total_validations": 0,
    "success_rate": 100.0,
    "corrections_made": 0,
    "fictional_ids_prevented": 0
  },
  "cross_reference_analysis": {
    "cross_ref_findings": 0,
    "template_relationships": 3,
    "security_boundaries_analyzed": 0
  },
  "domain_distribution": {
    "Network Security": 5,
    "Data Protection": 7,
    "Identity Management": 5
  },
  "severity_distribution": {
    "CRITICAL": 10,
    "HIGH": 7
  },
  "resource_type_coverage": {
    "unique_resource_types": 0,
    "analyzed_files": 3,
    "resource_types": []
  },
  "benchmark_version": "Azure Security Benchmark v3.0",
  "analysis_configuration": {
    "domain_priority_enabled": true,
    "optimized_prompts_enabled": true,
    "control_id_validation_enabled": true,
    "cross_reference_analysis_enabled": true
  }
};

            // Tooltip Links Data
            window.tooltipLinks = {
  "NS-2": {
    "formatted_links": "[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)",
    "azure_guidance": "Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview\n\u2022 Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints\n\u2022 SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview\n\u2022 Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service\n\u2022 Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nCompliance Mappings:\n\u2022 CIS Controls v8: 3.12, 4.4\n\u2022 NIST SP800-53 r4: AC-4, SC-2, SC-7\n\u2022 PCI-DSS v3.2.1: 1.1, 1.2, 1.3\n\nAzure Policy Examples:\n\u2022 Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers\n\u2022 Storage accounts should use private link\n\u2022 Azure SQL Database should disable public network access\n\u2022 Cognitive Services accounts should restrict network access\n\u2022 Container registries should use private link",
    "raw_links": [
      "https://docs.microsoft.com/azure/private-link/private-link-overview",
      "https://docs.microsoft.com/azure/storage/common/storage-private-endpoints",
      "https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview",
      "https://docs.microsoft.com/azure/key-vault/general/private-link-service",
      "https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture"
    ]
  },
  "NS-1": {
    "formatted_links": "[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)",
    "azure_guidance": "Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices\n\u2022 Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet\n\u2022 NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic\n\u2022 Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups\n\u2022 Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nCompliance Mappings:\n\u2022 CIS Controls v8: 3.12, 13.4, 4.4\n\u2022 NIST SP800-53 r4: AC-4, SC-2, SC-7\n\u2022 PCI-DSS v3.2.1: 1.1, 1.2, 1.3\n\nAzure Policy Examples:\n\u2022 Adaptive network hardening recommendations should be applied on internet facing virtual machines\n\u2022 All network ports should be restricted on network security groups associated to your virtual machine\n\u2022 Subnets should be associated with a Network Security Group",
    "raw_links": [
      "https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices",
      "https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet",
      "https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic",
      "https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups",
      "https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture"
    ]
  },
  "NS-3": {
    "formatted_links": "[Enhanced Implementation Context](https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal) | [Virtual network traffic routing](https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview) | [Azure Firewall Manager](https://docs.microsoft.com/azure/firewall-manager/overview) | [Hub-spoke topology](https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)",
    "azure_guidance": "Use Azure Firewall for stateful application layer traffic restriction and central management. Create user-defined routes (UDR) for complex topologies. Block known bad IPs and high-risk protocols.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Azure Firewall deployment: https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal\n\u2022 Virtual network traffic routing: https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview\n\u2022 Azure Firewall Manager: https://docs.microsoft.com/azure/firewall-manager/overview\n\u2022 Hub-spoke topology: https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke\n\u2022 Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nCompliance Mappings:\n\u2022 CIS Controls v8: 4.4, 4.8, 13.10\n\u2022 NIST SP800-53 r4: AC-4, SC-7, CM-7\n\u2022 PCI-DSS v3.2.1: 1.1, 1.2, 1.3\n\nAzure Policy Examples:\n\u2022 Management ports should be closed on your virtual machines\n\u2022 Management ports of virtual machines should be protected with just-in-time network access control\n\u2022 IP Forwarding on your virtual machine should be disabled\n\u2022 All Internet traffic should be routed via your deployed Azure Firewall",
    "raw_links": [
      "https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal",
      "https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview",
      "https://docs.microsoft.com/azure/firewall-manager/overview",
      "https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke",
      "https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture"
    ]
  },
  "NS-5": {
    "formatted_links": "[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/manage-ddos-protection) | [DDoS response strategy](https://docs.microsoft.com/azure/ddos-protection/ddos-response-strategy) | [DDoS monitoring and alerting](https://docs.microsoft.com/azure/ddos-protection/telemetry-monitoring-alerting) | [DDoS best practices](https://docs.microsoft.com/azure/security/fundamentals/ddos-best-practices) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)",
    "azure_guidance": "Enable DDoS Standard protection plan on your VNet to protect resources exposed to public networks. Configure DDoS policies and monitoring.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Azure DDoS Protection Standard: https://docs.microsoft.com/azure/virtual-network/manage-ddos-protection\n\u2022 DDoS response strategy: https://docs.microsoft.com/azure/ddos-protection/ddos-response-strategy\n\u2022 DDoS monitoring and alerting: https://docs.microsoft.com/azure/ddos-protection/telemetry-monitoring-alerting\n\u2022 DDoS best practices: https://docs.microsoft.com/azure/security/fundamentals/ddos-best-practices\n\u2022 Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nCompliance Mappings:\n\u2022 CIS Controls v8: 13.10\n\u2022 NIST SP800-53 r4: SC-5, SC-7\n\u2022 PCI-DSS v3.2.1: 1.1, 1.2, 1.3, 6.6\n\nAzure Policy Examples:\n\u2022 Azure DDoS Protection Standard should be enabled\n\u2022 Monitor DDoS attack metrics and configure alerts\n\u2022 Implement DDoS response procedures",
    "raw_links": [
      "https://docs.microsoft.com/azure/virtual-network/manage-ddos-protection",
      "https://docs.microsoft.com/azure/ddos-protection/ddos-response-strategy",
      "https://docs.microsoft.com/azure/ddos-protection/telemetry-monitoring-alerting",
      "https://docs.microsoft.com/azure/security/fundamentals/ddos-best-practices",
      "https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture"
    ]
  },
  "NS-8": {
    "formatted_links": "[Enhanced Implementation Context](https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Secure protocol configuration](https://docs.microsoft.com/azure/security/fundamentals/network-best-practices) | [Network security monitoring](https://docs.microsoft.com/azure/security/fundamentals/network-monitoring) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)",
    "azure_guidance": "Use Azure Sentinel's Insecure Protocol Workbook to discover insecure services like SSL/TLSv1 SSHv1 SMBv1 LM/NTLMv1. Disable insecure protocols or use compensating controls like NSG/Firewall blocking.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Azure Sentinel insecure protocols workbook: https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks\n\u2022 TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem\n\u2022 Secure protocol configuration: https://docs.microsoft.com/azure/security/fundamentals/network-best-practices\n\u2022 Network security monitoring: https://docs.microsoft.com/azure/security/fundamentals/network-monitoring\n\u2022 Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture\n\nCompliance Mappings:\n\u2022 CIS Controls v8: 4.4, 4.8\n\u2022 NIST SP800-53 r4: CM-2, CM-6, CM-7\n\u2022 PCI-DSS v3.2.1: 4.1, A2.1, A2.2, A2.3\n\nAzure Policy Examples:\n\u2022 Latest TLS version should be used in your API App\n\u2022 Latest TLS version should be used in your Web App\n\u2022 Latest TLS version should be used in your Function App\n\u2022 Secure transfer to storage accounts should be enabled",
    "raw_links": [
      "https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks",
      "https://docs.microsoft.com/security/engineering/solving-tls1-problem",
      "https://docs.microsoft.com/azure/security/fundamentals/network-best-practices",
      "https://docs.microsoft.com/azure/security/fundamentals/network-monitoring",
      "https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture"
    ]
  },
  "DP-2": {
    "formatted_links": "[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)",
    "azure_guidance": "Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql\n\u2022 Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center\n\u2022 Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights\n\u2022 Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp\n\u2022 Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip\n\nCompliance Mappings:\n\u2022 CIS Controls v8: 3.13\n\u2022 NIST SP800-53 r4: AC-4, SI-4\n\u2022 PCI-DSS v3.2.1: A3.2\n\nAzure Policy Examples:\n\u2022 Azure Defender for open-source relational databases should be enabled\n\u2022 Azure Defender for Storage should be enabled\n\u2022 Azure Defender for SQL servers on machines should be enabled\n\u2022 Azure Defender for Azure SQL Database servers should be enabled\n\u2022 Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",
    "raw_links": [
      "https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql",
      "https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center",
      "https://docs.microsoft.com/azure/purview/concept-insights",
      "https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp",
      "https://docs.microsoft.com/azure/information-protection/reports-aip"
    ]
  },
  "DP-3": {
    "formatted_links": "[Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account)",
    "azure_guidance": "Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windows remote management.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Double encryption for Azure data in transit: https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit\n\u2022 Encryption in transit overview: https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit\n\u2022 TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem\n\u2022 Azure Storage secure transfer: https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account\n\nCompliance Mappings:\n\u2022 CIS Controls v8: 3.10\n\u2022 NIST SP800-53 r4: SC-8\n\u2022 PCI-DSS v3.2.1: 3.5, 3.6, 4.1\n\nAzure Policy Examples:\n\u2022 Kubernetes clusters should be accessible only over HTTPS\n\u2022 Only secure connections to your Azure Cache for Redis should be enabled\n\u2022 FTPS only should be required in your Function App\n\u2022 Secure transfer to storage accounts should be enabled\n\u2022 Function App should only be accessible over HTTPS\n\u2022 Latest TLS version should be used in your API App\n\u2022 Web Application should only be accessible over HTTPS\n\u2022 Enforce SSL connection should be enabled for PostgreSQL database servers\n\u2022 Latest TLS version should be used in your Web App",
    "raw_links": [
      "https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit",
      "https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit",
      "https://docs.microsoft.com/security/engineering/solving-tls1-problem",
      "https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account"
    ]
  },
  "DP-1": {
    "formatted_links": "[Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources)",
    "azure_guidance": "Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other locations.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Data classification overview: https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification\n\u2022 Azure Purview sensitivity labels: https://docs.microsoft.com/azure/purview/create-sensitivity-label\n\u2022 Azure Information Protection: https://docs.microsoft.com/azure/information-protection/what-is-information-protection\n\u2022 Azure SQL Data Discovery: https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification\n\u2022 Azure Purview data sources: https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources\n\nCompliance Mappings:\n\u2022 CIS Controls v8: 3.2, 3.7, 3.13\n\u2022 NIST SP800-53 r4: RA-2, SC-28\n\u2022 PCI-DSS v3.2.1: A3.2\n\nAzure Policy Examples:\n\u2022 Sensitive data in your SQL databases should be classified\n\u2022 Implement data discovery and classification across all data stores\n\u2022 Deploy Azure Purview for enterprise data governance\n\u2022 Configure sensitivity labels for all data assets",
    "raw_links": [
      "https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification",
      "https://docs.microsoft.com/azure/purview/create-sensitivity-label",
      "https://docs.microsoft.com/azure/information-protection/what-is-information-protection",
      "https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification",
      "https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources"
    ]
  },
  "IM-1": {
    "formatted_links": "[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)",
    "azure_guidance": "Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applications to Azure AD when technically feasible.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Azure AD tenancy concepts: https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps\n\u2022 Create and configure Azure AD: https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant\n\u2022 Define Azure AD tenants: https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/\n\u2022 External identity providers: https://docs.microsoft.com/azure/active-directory/b2b/identity-providers\n\u2022 Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys\n\nCompliance Mappings:\n\u2022 CIS Controls v8: 6.7, 12.5\n\u2022 NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8\n\u2022 PCI-DSS v3.2.1: 7.2, 8.3\n\nAzure Policy Examples:\n\u2022 An Azure Active Directory administrator should be provisioned for SQL servers\n\u2022 Service Fabric clusters should only use Azure Active Directory for client authentication\n\u2022 Standardize identity provider across all applications and services",
    "raw_links": [
      "https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps",
      "https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant",
      "https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/",
      "https://docs.microsoft.com/azure/active-directory/b2b/identity-providers",
      "https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys"
    ]
  },
  "IM-3": {
    "formatted_links": "[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview) | [Services supporting managed identities](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities) | [Azure service principal creation](https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps) | [Service principal with certificates](https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)",
    "azure_guidance": "Use Azure managed identities for services supporting Azure AD authentication. For services without managed identity support use Azure AD service principals with certificate credentials and restricted permissions.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Azure managed identities overview: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview\n\u2022 Services supporting managed identities: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities\n\u2022 Azure service principal creation: https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps\n\u2022 Service principal with certificates: https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell\n\u2022 Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys\n\nCompliance Mappings:\n\u2022 CIS Controls v8: Not specified\n\u2022 NIST SP800-53 r4: AC-2, AC-3, IA-4, IA-5, IA-9\n\u2022 PCI-DSS v3.2.1: Not applicable\n\nAzure Policy Examples:\n\u2022 Managed identity should be used in your Function App\n\u2022 Managed identity should be used in your Web App\n\u2022 Service principals should be used to protect your subscriptions instead of management certificates\n\u2022 Managed identity should be used in your API App\n\u2022 Virtual machines' Guest Configuration extension should be deployed with system-assigned managed identity",
    "raw_links": [
      "https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview",
      "https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities",
      "https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps",
      "https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell",
      "https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys"
    ]
  },
  "IM-2": {
    "formatted_links": "[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score) | [Active Directory security best practices](https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory) | [Azure AD security baseline](https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline) | [Privileged Identity Management](https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)",
    "azure_guidance": "Use Azure AD security baseline and Identity Secure Score to evaluate security posture. Implement limited administrative roles user risk policies MFA for administrative roles and block legacy authentication.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Azure AD Identity Secure Score: https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score\n\u2022 Active Directory security best practices: https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory\n\u2022 Azure AD security baseline: https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline\n\u2022 Privileged Identity Management: https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure\n\u2022 Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys\n\nCompliance Mappings:\n\u2022 CIS Controls v8: 5.4, 6.5\n\u2022 NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8, SI-4\n\u2022 PCI-DSS v3.2.1: 8.2, 8.3\n\nAzure Policy Examples:\n\u2022 No applicable built-in policy (requires configuration-based implementation)\n\u2022 Use Azure AD Identity Secure Score recommendations\n\u2022 Implement Azure AD security baseline configurations\n\u2022 Monitor privileged account activities through Azure AD logs",
    "raw_links": [
      "https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score",
      "https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory",
      "https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline",
      "https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure",
      "https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys"
    ]
  },
  "IM-6": {
    "formatted_links": "[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/authentication/howto-mfa-getstarted) | [Passwordless authentication options](https://docs.microsoft.com/azure/active-directory/authentication/concept-authentication-passwordless) | [Azure AD password policies](https://docs.microsoft.com/azure/active-directory/authentication/concept-sspr-policy#password-policies-that-only-apply-to-cloud-user-accounts) | [Azure AD Password Protection](https://docs.microsoft.com/azure/active-directory/authentication/concept-password-ban-bad) | [Block legacy authentication](https://docs.microsoft.com/azure/active-directory/conditional-access/block-legacy-authentication)",
    "azure_guidance": "Use Azure AD passwordless authentication as default method (Windows Hello Microsoft Authenticator FIDO2 Keys). Enable Azure MFA for all users with conditional access policies. Block legacy authentication.",
    "implementation_context": "Enhanced Implementation Context:\n\u2022 Azure MFA deployment: https://docs.microsoft.com/azure/active-directory/authentication/howto-mfa-getstarted\n\u2022 Passwordless authentication options: https://docs.microsoft.com/azure/active-directory/authentication/concept-authentication-passwordless\n\u2022 Azure AD password policies: https://docs.microsoft.com/azure/active-directory/authentication/concept-sspr-policy#password-policies-that-only-apply-to-cloud-user-accounts\n\u2022 Azure AD Password Protection: https://docs.microsoft.com/azure/active-directory/authentication/concept-password-ban-bad\n\u2022 Block legacy authentication: https://docs.microsoft.com/azure/active-directory/conditional-access/block-legacy-authentication\n\nCompliance Mappings:\n\u2022 CIS Controls v8: 6.3, 6.4\n\u2022 NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-5, IA-8\n\u2022 PCI-DSS v3.2.1: 7.2, 8.2, 8.3, 8.4\n\nAzure Policy Examples:\n\u2022 Authentication to Linux machines should require SSH keys\n\u2022 MFA should be enabled on accounts with write permissions on your subscription\n\u2022 MFA should be enabled on accounts with owner permissions on your subscription\n\u2022 MFA should be enabled on accounts with read permissions on your subscription",
    "raw_links": [
      "https://docs.microsoft.com/azure/active-directory/authentication/howto-mfa-getstarted",
      "https://docs.microsoft.com/azure/active-directory/authentication/concept-authentication-passwordless",
      "https://docs.microsoft.com/azure/active-directory/authentication/concept-sspr-policy#password-policies-that-only-apply-to-cloud-user-accounts",
      "https://docs.microsoft.com/azure/active-directory/authentication/concept-password-ban-bad",
      "https://docs.microsoft.com/azure/active-directory/conditional-access/block-legacy-authentication"
    ]
  }
};

            // Initialize tooltip functionality
            document.addEventListener('DOMContentLoaded', function() {
                // Add tooltip functionality to control IDs
                addTooltipFunctionality();
            });

            function addTooltipFunctionality() {
                // Find all control ID elements and add tooltips
                const controlElements = document.querySelectorAll('.control-id, .finding-control');

                controlElements.forEach(element => {
                    const controlId = element.textContent.trim();
                    if (window.tooltipLinks[controlId]) {
                        const tooltipData = window.tooltipLinks[controlId];

                        // Add tooltip icon
                        const tooltipIcon = document.createElement('span');
                        tooltipIcon.className = 'tooltip-icon';
                        tooltipIcon.innerHTML = ' 📚';
                        tooltipIcon.style.cursor = 'pointer';
                        tooltipIcon.style.marginLeft = '5px';
                        tooltipIcon.style.fontSize = '0.8em';

                        // Create tooltip content
                        let tooltipContent = '<div class="tooltip-content">';

                        if (tooltipData.azure_guidance) {
                            tooltipContent += `<div class="tooltip-section">
                                <strong>🔵 Azure Guidance:</strong><br>
                                ${tooltipData.azure_guidance.substring(0, 200)}${tooltipData.azure_guidance.length > 200 ? '...' : ''}
                            </div>`;
                        }

                        if (tooltipData.raw_links && tooltipData.raw_links.length > 0) {
                            tooltipContent += '<div class="tooltip-section"><strong>📚 Reference Links:</strong><br>';
                            tooltipData.raw_links.forEach((link, index) => {
                                const linkText = link.length > 50 ? link.substring(0, 50) + '...' : link;
                                tooltipContent += `<a href="${link}" target="_blank" class="tooltip-link">${linkText}</a><br>`;
                            });
                            tooltipContent += '</div>';
                        }

                        tooltipContent += '</div>';

                        // Add tooltip functionality
                        tooltipIcon.setAttribute('data-tooltip', tooltipContent);
                        element.appendChild(tooltipIcon);

                        // Add click handler to show tooltip
                        tooltipIcon.addEventListener('click', function(e) {
                            e.preventDefault();
                            showTooltip(e.target, tooltipContent);
                        });
                    }
                });
            }

            function showTooltip(element, content) {
                // Remove existing tooltips
                const existingTooltips = document.querySelectorAll('.custom-tooltip');
                existingTooltips.forEach(tooltip => tooltip.remove());

                // Create new tooltip
                const tooltip = document.createElement('div');
                tooltip.className = 'custom-tooltip';
                tooltip.innerHTML = content;

                // Position tooltip
                document.body.appendChild(tooltip);
                const rect = element.getBoundingClientRect();
                tooltip.style.position = 'fixed';
                tooltip.style.top = (rect.bottom + 10) + 'px';
                tooltip.style.left = rect.left + 'px';
                tooltip.style.zIndex = '10000';

                // Auto-hide after 10 seconds
                setTimeout(() => {
                    if (tooltip.parentNode) {
                        tooltip.remove();
                    }
                }, 10000);

                // Hide on click outside
                document.addEventListener('click', function hideTooltip(e) {
                    if (!tooltip.contains(e.target) && e.target !== element) {
                        tooltip.remove();
                        document.removeEventListener('click', hideTooltip);
                    }
                });
            }
            </script>

            <style>
            /* Removed validation statistics CSS styles - not relevant for end users */

            .tooltip-icon {
                transition: all 0.2s ease;
            }

            .tooltip-icon:hover {
                transform: scale(1.2);
                filter: brightness(1.3);
            }

            .custom-tooltip {
                background: rgba(0, 0, 0, 0.9);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 15px;
                max-width: 400px;
                color: white;
                font-size: 0.9em;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                animation: tooltipFadeIn 0.2s ease;
            }

            @keyframes tooltipFadeIn {
                from { opacity: 0; transform: translateY(-10px); }
                to { opacity: 1; transform: translateY(0); }
            }

            .tooltip-section {
                margin-bottom: 10px;
            }

            .tooltip-section:last-child {
                margin-bottom: 0;
            }

            .tooltip-link {
                color: #4ade80;
                text-decoration: none;
                word-break: break-all;
            }

            .tooltip-link:hover {
                color: #6ee7b7;
                text-decoration: underline;
            }
            </style>
            </body>
</html>
