#!/usr/bin/env python
"""
Verify Control Coverage Script

This script verifies that all control IDs from the Azure Security Benchmark
are covered in the analysis output from security_pr_review.py.
"""

import json
import argparse
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple

def load_benchmark(benchmark_path: str) -> Tuple[Set[str], Dict]:
    """
    Load the benchmark JSON and extract all control IDs.
    
    Args:
        benchmark_path: Path to the Azure Security Benchmark JSON file
        
    Returns:
        Tuple of (set of control IDs, full benchmark data)
    """
    try:
        with open(benchmark_path, 'r', encoding='utf-8') as f:
            benchmark_data = json.load(f)
        
        control_ids = set()
        
        # Extract control IDs from the controls array
        if 'controls' in benchmark_data:
            for control in benchmark_data['controls']:
                if 'id' in control:
                    control_ids.add(control['id'])
        
        print(f"[OK] Loaded benchmark from: {benchmark_path}")
        print(f"  Found {len(control_ids)} control IDs in benchmark")
        
        return control_ids, benchmark_data
        
    except FileNotFoundError:
        print(f"[ERROR] Benchmark file not found: {benchmark_path}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"[ERROR] Invalid JSON in benchmark file: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"[ERROR] Error loading benchmark: {e}")
        sys.exit(1)

def load_analysis_output(output_path: str) -> Tuple[Set[str], List[Dict]]:
    """
    Load the analysis output and extract all referenced control IDs.
    
    Args:
        output_path: Path to the analysis output JSON file
        
    Returns:
        Tuple of (set of control IDs referenced, full findings data)
    """
    try:
        with open(output_path, 'r', encoding='utf-8') as f:
            findings = json.load(f)
        
        # Handle both single finding and array of findings
        if not isinstance(findings, list):
            findings = [findings]
        
        control_ids = set()
        
        # Extract control IDs from findings
        for finding in findings:
            if 'control_id' in finding:
                control_ids.add(finding['control_id'])
        
        print(f"[OK] Loaded analysis output from: {output_path}")
        print(f"  Found {len(control_ids)} unique control IDs in recommendations")
        
        return control_ids, findings
        
    except FileNotFoundError:
        print(f"[ERROR] Analysis output file not found: {output_path}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"[ERROR] Invalid JSON in analysis output: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"[ERROR] Error loading analysis output: {e}")
        sys.exit(1)

def analyze_coverage(benchmark_ids: Set[str], analysis_ids: Set[str]) -> Dict:
    """
    Analyze the coverage of control IDs.
    
    Args:
        benchmark_ids: Set of all control IDs from the benchmark
        analysis_ids: Set of control IDs referenced in analysis
        
    Returns:
        Dictionary with coverage statistics
    """
    # Calculate coverage
    covered_ids = benchmark_ids.intersection(analysis_ids)
    missing_ids = benchmark_ids - analysis_ids
    extra_ids = analysis_ids - benchmark_ids
    
    # Calculate percentage
    coverage_percentage = (len(covered_ids) / len(benchmark_ids) * 100) if benchmark_ids else 0
    
    return {
        'total_benchmark_controls': len(benchmark_ids),
        'total_referenced_controls': len(analysis_ids),
        'covered_controls': len(covered_ids),
        'missing_controls': len(missing_ids),
        'extra_controls': len(extra_ids),
        'coverage_percentage': coverage_percentage,
        'missing_control_ids': sorted(list(missing_ids)),
        'extra_control_ids': sorted(list(extra_ids)),
        'covered_control_ids': sorted(list(covered_ids))
    }

def print_summary(coverage_stats: Dict, benchmark_data: Dict):
    """
    Print a formatted summary of the coverage analysis.
    
    Args:
        coverage_stats: Coverage statistics dictionary
        benchmark_data: Full benchmark data for additional context
    """
    print("\n" + "="*60)
    print("CONTROL COVERAGE ANALYSIS SUMMARY")
    print("="*60)
    
    print(f"\n[STATS] Coverage Statistics:")
    print(f"  - Total controls in benchmark: {coverage_stats['total_benchmark_controls']}")
    print(f"  - Controls covered in analysis: {coverage_stats['covered_controls']}")
    print(f"  - Coverage percentage: {coverage_stats['coverage_percentage']:.1f}%")
    
    if coverage_stats['missing_controls'] > 0:
        print(f"\n[WARNING] Missing Controls: {coverage_stats['missing_controls']} controls not referenced")
        print("  Control IDs not appearing in any recommendations:")
        
        # Group missing controls by domain if possible
        missing_by_domain = {}
        controls_dict = {c['id']: c for c in benchmark_data.get('controls', [])}
        
        for control_id in coverage_stats['missing_control_ids']:
            control = controls_dict.get(control_id, {})
            domain = control.get('domain', 'Unknown Domain')
            if domain not in missing_by_domain:
                missing_by_domain[domain] = []
            missing_by_domain[domain].append({
                'id': control_id,
                'name': control.get('name', 'Unknown')
            })
        
        # Print missing controls grouped by domain
        for domain, controls in sorted(missing_by_domain.items()):
            print(f"\n  {domain}:")
            for control in controls:
                print(f"    - {control['id']}: {control['name']}")
    else:
        print("\n[SUCCESS] All benchmark controls are covered!")
    
    if coverage_stats['extra_controls'] > 0:
        print(f"\n[INFO] Additional Controls: {coverage_stats['extra_controls']} controls not in benchmark")
        print("  These control IDs appear in recommendations but not in the benchmark:")
        for control_id in coverage_stats['extra_control_ids']:
            print(f"    - {control_id}")
    
    print("\n" + "="*60)

def export_missing_controls(coverage_stats: Dict, benchmark_data: Dict, output_file: str):
    """
    Export missing controls to a JSON file for further analysis.
    
    Args:
        coverage_stats: Coverage statistics dictionary
        benchmark_data: Full benchmark data
        output_file: Path to export the missing controls
    """
    controls_dict = {c['id']: c for c in benchmark_data.get('controls', [])}
    
    missing_controls = []
    for control_id in coverage_stats['missing_control_ids']:
        control = controls_dict.get(control_id, {'id': control_id})
        missing_controls.append(control)
    
    export_data = {
        'summary': {
            'total_missing': coverage_stats['missing_controls'],
            'coverage_percentage': coverage_stats['coverage_percentage']
        },
        'missing_controls': missing_controls
    }
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(export_data, f, indent=2)
    
    print(f"\n[EXPORT] Missing controls exported to: {output_file}")

def main():
    """Main function to run the control verification."""
    parser = argparse.ArgumentParser(
        description='Verify that all Azure Security Benchmark control IDs are covered in analysis output'
    )
    parser.add_argument(
        'benchmark_file',
        help='Path to the Azure Security Benchmark JSON file'
    )
    parser.add_argument(
        'analysis_output',
        help='Path to the analysis output JSON file containing findings'
    )
    parser.add_argument(
        '--export-missing',
        help='Export missing controls to a JSON file',
        default=None
    )
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Show detailed output including all control IDs'
    )
    
    args = parser.parse_args()
    
    # Verify files exist
    if not Path(args.benchmark_file).exists():
        print(f"[ERROR] Benchmark file does not exist: {args.benchmark_file}")
        sys.exit(1)
    
    if not Path(args.analysis_output).exists():
        print(f"[ERROR] Analysis output file does not exist: {args.analysis_output}")
        sys.exit(1)
    
    # Load data
    print("[INFO] Loading files...")
    benchmark_ids, benchmark_data = load_benchmark(args.benchmark_file)
    analysis_ids, findings = load_analysis_output(args.analysis_output)
    
    # Analyze coverage
    print("\n[INFO] Analyzing control coverage...")
    coverage_stats = analyze_coverage(benchmark_ids, analysis_ids)
    
    # Print summary
    print_summary(coverage_stats, benchmark_data)
    
    # Export missing controls if requested
    if args.export_missing:
        export_missing_controls(coverage_stats, benchmark_data, args.export_missing)
    
    # Verbose output if requested
    if args.verbose:
        print("\n[DETAILS] Detailed Control Lists:")
        print(f"\nCovered Controls ({len(coverage_stats['covered_control_ids'])}):")
        for control_id in coverage_stats['covered_control_ids']:
            print(f"  [OK] {control_id}")
        
        if coverage_stats['missing_control_ids']:
            print(f"\nMissing Controls ({len(coverage_stats['missing_control_ids'])}):")
            for control_id in coverage_stats['missing_control_ids']:
                print(f"  [MISSING] {control_id}")
    
    # Exit with appropriate code
    if coverage_stats['missing_controls'] > 0:
        sys.exit(1)  # Exit with error if controls are missing
    else:
        sys.exit(0)  # Success if all controls are covered

if __name__ == "__main__":
    main()
