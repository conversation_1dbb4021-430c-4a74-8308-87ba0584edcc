#!/usr/bin/env python
"""
Test script to demonstrate control verification workflow.

This script:
1. Runs security_pr_review.py on sample files
2. Captures the analysis output
3. Runs verify_controls.py to check coverage
"""

import json
import subprocess
import sys
from pathlib import Path
from security_pr_review import SecurityPRReviewer

def create_test_analysis():
    """
    Create a test analysis output by running the security reviewer on sample files.
    """
    print("🔧 Creating test analysis output...")
    
    # Create a mock SecurityPRReviewer
    reviewer = SecurityPRReviewer("test-repo", 1)
    
    # Prepare the benchmark
    reviewer.prepare_benchmark()
    
    # Create sample files for testing
    test_files = [
        {
            "path": "test_storage.tf",
            "content": """
resource "azurerm_storage_account" "test" {
    name                     = "teststorage"
    resource_group_name      = "test-rg"
    location                 = "eastus"
    account_tier             = "Standard"
    account_replication_type = "LRS"
    
    # Security issues for testing
    enable_https_traffic_only = false
    min_tls_version          = "TLS1_0"
    
    network_rules {
        default_action = "Allow"
    }
}
""",
            "resource_type": "Storage"
        },
        {
            "path": "test_keyvault.bicep",
            "content": """
resource kv 'Microsoft.KeyVault/vaults@2021-04-01-preview' = {
  name: 'testkeyvault'
  location: resourceGroup().location
  properties: {
    sku: {
      family: 'A'
      name: 'standard'
    }
    tenantId: tenant().tenantId
    // Missing: enableSoftDelete: true
    // Missing: enablePurgeProtection: true
    networkAcls: {
      defaultAction: 'Allow'  // Should be 'Deny'
    }
  }
}
""",
            "resource_type": "KeyVault"
        }
    ]
    
    # Analyze the files
    print("📝 Analyzing test files...")
    findings = reviewer.analyze_files(test_files)
    
    # Save findings to JSON
    output_file = "test_analysis_output.json"
    with open(output_file, 'w') as f:
        json.dump(findings, f, indent=2)
    
    print(f"✅ Saved {len(findings)} findings to {output_file}")
    return output_file, findings

def run_verification(benchmark_file: str, analysis_file: str):
    """
    Run the verify_controls.py script.
    """
    print("\n🔍 Running control verification...")
    
    cmd = [
        sys.executable,
        "verify_controls.py",
        benchmark_file,
        analysis_file,
        "--verbose"
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    print("\nVerification Output:")
    print(result.stdout)
    
    if result.stderr:
        print("\nErrors:")
        print(result.stderr)
    
    return result.returncode

def main():
    """Main test function."""
    print("="*60)
    print("CONTROL VERIFICATION TEST")
    print("="*60)
    
    # Check if benchmark exists
    benchmark_file = "SecurityBenchmarks/Azure_Security_Benchmark_v3.json"
    if not Path(benchmark_file).exists():
        print(f"⚠️  Warning: Benchmark file not found at {benchmark_file}")
        print("   The security reviewer will create a simplified benchmark.")
    
    # Create test analysis
    analysis_file, findings = create_test_analysis()
    
    # Print findings summary
    print(f"\n📊 Analysis Summary:")
    control_ids = set()
    for finding in findings:
        if 'control_id' in finding:
            control_ids.add(finding['control_id'])
    print(f"  • Total findings: {len(findings)}")
    print(f"  • Unique control IDs referenced: {len(control_ids)}")
    print(f"  • Control IDs: {', '.join(sorted(control_ids))}")
    
    # Run verification if benchmark exists
    if Path(benchmark_file).exists():
        exit_code = run_verification(benchmark_file, analysis_file)
        
        if exit_code == 0:
            print("\n✅ All controls are covered!")
        else:
            print("\n⚠️  Some controls are missing from the analysis.")
            
            # Export missing controls
            print("\n📄 Exporting missing controls for review...")
            export_cmd = [
                sys.executable,
                "verify_controls.py",
                benchmark_file,
                analysis_file,
                "--export-missing",
                "missing_controls.json"
            ]
            subprocess.run(export_cmd, capture_output=True)
            print("   Missing controls saved to: missing_controls.json")
    else:
        print("\n⚠️  Skipping verification - benchmark file not found")
    
    print("\n" + "="*60)
    print("Test completed!")

if __name__ == "__main__":
    main()
