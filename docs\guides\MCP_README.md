# IaC Guardian MCP Server

A Model Context Protocol (MCP) server that provides Infrastructure-as-Code security analysis tools for VS Code Copilot integration.

## Overview

This MCP server exposes the IaC Guardian GPT security analysis capabilities as tools that can be used directly within VS Code through GitHub Copilot Chat. It provides real-time security analysis of Azure ARM templates, Bicep files, and other Infrastructure-as-Code files.

## Features

### 🛡️ Security Analysis Tools

1. **`analyze_iac_file`** - Analyze a single IaC file for security issues
2. **`analyze_iac_folder`** - Analyze all IaC files in a folder
3. **`get_security_controls`** - Get Azure Security Benchmark controls for resource types
4. **`validate_security_config`** - Validate specific resource configurations

### 🎯 Key Capabilities

- **Domain-Prioritized Analysis** - Issues ordered by: Identity → Network → Data Protection
- **Azure Security Benchmark Compliance** - Based on ASB v3.0 controls
- **Multiple Output Formats** - JSON, Markdown, Summary, HTML reports
- **Real-time Analysis** - Instant feedback in VS Code
- **Comprehensive Coverage** - ARM templates, Bicep files, parameter files

## Quick Setup

### 1. Install Dependencies

```bash
python setup_mcp.py
```

This will:
- Install required Python packages
- Configure VS Code settings for MCP integration
- Create launch and test scripts

### 2. Restart VS Code

Restart VS Code to load the new MCP server configuration.

### 3. Test the Server

```bash
python test_mcp.py
```

## Manual Setup

If you prefer manual setup:

### 1. Install Requirements

```bash
pip install -r requirements-mcp.txt
```

### 2. Configure VS Code

Add to your `.vscode/settings.json`:

```json
{
  "github.copilot.chat.experimental.mcp.enabled": true,
  "github.copilot.chat.experimental.mcp.servers": {
    "iac-guardian": {
      "command": "python",
      "args": ["mcp_server.py"],
      "cwd": "c:\\Users\\<USER>\\REPOS\\IaCGuardianGPT",
      "env": {
        "ENFORCE_DOMAIN_PRIORITY": "true",
        "USE_OPTIMIZED_PROMPTS": "true",
        "ANALYSIS_SEED": "42"
      }
    }
  }
}
```

## Usage in VS Code

Once configured, you can use the tools in GitHub Copilot Chat:

### Analyze a Single File

```
@iac-guardian analyze_iac_file file_path="./templates/storage.json" format="markdown"
```

### Analyze a Folder

```
@iac-guardian analyze_iac_folder folder_path="./templates" format="summary" export_report=true
```

### Get Security Controls

```
@iac-guardian get_security_controls resource_type="Storage" domain="Data Protection"
```

### Validate Configuration

```
@iac-guardian validate_security_config resource_type="Microsoft.Storage/storageAccounts" resource_config='{"type":"Microsoft.Storage/storageAccounts","properties":{"supportsHttpsTrafficOnly":false}}'
```

## Tool Parameters

### analyze_iac_file

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `file_path` | string | Yes | Path to the IaC file |
| `file_content` | string | No | File content (if not reading from path) |
| `format` | enum | No | Output format: `json`, `markdown`, `summary` |

### analyze_iac_folder

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `folder_path` | string | Yes | Path to folder containing IaC files |
| `format` | enum | No | Output format: `json`, `markdown`, `summary`, `html` |
| `export_report` | boolean | No | Export detailed HTML/CSV reports |

### get_security_controls

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `resource_type` | string | Yes | Azure resource type (e.g., 'Storage') |
| `domain` | enum | No | Security domain filter |

### validate_security_config

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `resource_config` | string | Yes | JSON configuration to validate |
| `resource_type` | string | Yes | Azure resource type |
| `control_ids` | array | No | Specific control IDs to check |

## Output Formats

### Markdown Format
```markdown
# Security Analysis Report for template.json

## 🛡️ Identity Management

### 🔴 CRITICAL Issues

#### IM-1
**File:** `template.json` (Line 15)
**Issue:** Missing Azure AD integration
**Remediation:** Configure Azure AD as identity provider
```

### Summary Format
```
✅ No security issues found in template.json

OR

# Security Analysis Summary for template.json
**Total Issues Found:** 3
**Severity Breakdown:**
- 🔴 CRITICAL: 1 issues
- 🟠 HIGH: 2 issues
```

### JSON Format
```json
[
  {
    "control_id": "IM-1",
    "severity": "CRITICAL",
    "file_path": "template.json",
    "line": 15,
    "description": "Missing Azure AD integration",
    "remediation": "Configure Azure AD as identity provider"
  }
]
```

## Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `ENFORCE_DOMAIN_PRIORITY` | `true` | Enable domain-prioritized analysis |
| `USE_OPTIMIZED_PROMPTS` | `true` | Use optimized AI prompts |
| `ANALYSIS_SEED` | `42` | Seed for consistent results |

## Troubleshooting

### MCP Server Not Found

1. Ensure VS Code is restarted after configuration
2. Check that the `cwd` path in settings.json is correct
3. Verify Python and dependencies are installed

### Analysis Errors

1. Check that Azure OpenAI credentials are configured in `.env`
2. Ensure the file paths are accessible
3. Verify the file format is supported (JSON, Bicep)

### Permission Issues

1. Ensure the MCP server has read access to the files
2. Check that the working directory is correct
3. Verify environment variables are set

## Testing

### Run All Tests
```bash
python test_mcp.py
```

### Test Individual Tools
```bash
python -c "
import asyncio
from mcp_server import handle_call_tool
asyncio.run(handle_call_tool('get_security_controls', {'resource_type': 'Storage'}))
"
```

## Development

### Adding New Tools

1. Add tool definition to `handle_list_tools()`
2. Implement handler function
3. Add to `handle_call_tool()` dispatcher
4. Update documentation

### Debugging

Enable debug logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Integration Examples

### VS Code Copilot Chat Examples

**Quick Security Check:**
```
@iac-guardian analyze_iac_file file_path="./main.json"
```

**Comprehensive Folder Analysis:**
```
@iac-guardian analyze_iac_folder folder_path="./infrastructure" export_report=true
```

**Get Storage Security Controls:**
```
@iac-guardian get_security_controls resource_type="Storage"
```

**Validate Specific Configuration:**
```
@iac-guardian validate_security_config resource_type="Microsoft.Network/networkSecurityGroups" resource_config='{"properties":{"securityRules":[{"properties":{"sourceAddressPrefix":"*"}}]}}'
```

## Support

For issues and questions:
1. Check the troubleshooting section
2. Run the test suite to verify functionality
3. Review VS Code and MCP server logs
4. Ensure all dependencies are correctly installed

## License

This MCP server is part of the IaC Guardian GPT project and follows the same licensing terms.
