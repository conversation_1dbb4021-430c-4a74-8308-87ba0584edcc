
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Security Findings Report</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    h1 { color: #333; }
                    .summary { background: #f5f5f5; padding: 15px; margin: 20px 0; border-radius: 5px; }
                    .severity-group { margin: 20px 0; }
                    .severity-header { 
                        padding: 10px; 
                        border-radius: 5px;
                        margin: 10px 0;
                        color: white;
                        font-weight: bold;
                    }
                    .critical { background-color: #dc3545; }
                    .high { background-color: #fd7e14; }
                    .medium { background-color: #ffc107; color: black; }
                    .low { background-color: #17a2b8; }
                    .finding {
                        border: 1px solid #ddd;
                        padding: 15px;
                        margin: 10px 0;
                        border-radius: 5px;
                        background: white;
                    }
                    .code-snippet {
                        background: #f8f9fa;
                        padding: 10px;
                        border-radius: 3px;
                        font-family: monospace;
                        white-space: pre-wrap;
                        margin: 10px 0;
                    }
                    .label { font-weight: bold; color: #555; }
                </style>
            </head>
            <body>
                <h1>🔒 Security Findings Report</h1>
                
            <div class="summary">
                <h2>Summary</h2>
                <p><strong>Total Findings:</strong> 77</p>
                <p><strong>Files Affected:</strong> 14</p>
                <p><strong>Findings by Severity:</strong></p>
                <ul>
            <li>🔴 CRITICAL: 4</li><li>🟠 HIGH: 33</li><li>🟡 MEDIUM: 28</li><li>🔵 LOW: 12</li></ul></div>
                
                    <div class="severity-group">
                        <div class="severity-header critical">🔴 CRITICAL Severity Findings</div>
                    
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> keyvault.bicep</p>
                            <p><span class="label">Line:</span> 22</p>
                            <p><span class="label">Issue:</span> The Key Vault &#x27;networkAcls&#x27; property sets &#x27;defaultAction&#x27; to &#x27;Allow&#x27;, which means the vault is accessible from all public networks except for those explicitly denied. This provides broad network access and significantly increases the attack surface, violating network segmentation best practices.</p>
                            <p><span class="label">Remediation:</span> Set &#x27;networkAcls.defaultAction&#x27; to &#x27;Deny&#x27; to ensure the Key Vault is only accessible via defined IP rules, virtual network rules, or trusted Azure services. Only grant exceptions for specific, necessary sources.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> keyvault.bicep</p>
                            <p><span class="label">Line:</span> 22</p>
                            <p><span class="label">Issue:</span> The Key Vault permits public GET/PUT access to anyone on the internet due to &#x27;networkAcls.defaultAction&#x27; set to &#x27;Allow&#x27;. This exposes sensitive secrets in the vault to potential unauthorized access.</p>
                            <p><span class="label">Remediation:</span> Set Key Vault &#x27;networkAcls.defaultAction&#x27; to &#x27;Deny&#x27; and define access only from necessary trusted IPs and VNets to avoid public endpoint exposure.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> storage-accounts.bicep</p>
                            <p><span class="label">Line:</span> 21</p>
                            <p><span class="label">Issue:</span> The &#x27;networkAcls&#x27; for all storage accounts sets &#x27;defaultAction&#x27; to &#x27;Allow&#x27;, which permits access from all networks except those explicitly blocked, exposing storage accounts to attacks from unauthorized networks. ASB NS-1 recommends restricting access using NSGs/Azure Firewall and minimizing public accessibility.</p>
                            <p><span class="label">Remediation:</span> Change &#x27;defaultAction&#x27; in &#x27;networkAcls&#x27; to &#x27;Deny&#x27; and ensure that only required IP addresses (e.g., via ipRules or virtualNetworkRules) are granted access to the storage accounts. This will restrict unauthorized access and minimize the attack surface.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> storage-accounts.bicep</p>
                            <p><span class="label">Line:</span> 21</p>
                            <p><span class="label">Issue:</span> Setting &#x27;networkAcls.defaultAction&#x27; to &#x27;Allow&#x27; leaves storage accounts with public endpoints exposed to the Internet unless restricted by IP rules or VNET rules, which may not always be sufficiently narrow. ASB NS-2 requires public endpoints to be secured or disabled.</p>
                            <p><span class="label">Remediation:</span> Set &#x27;defaultAction&#x27; to &#x27;Deny&#x27; in &#x27;networkAcls&#x27;. Review and limit IP rules and virtual network rules to only those required. If possible, disable public endpoints entirely for these storage accounts.</p>
                        </div></div>
                    <div class="severity-group">
                        <div class="severity-header high">🟠 HIGH Severity Findings</div>
                    
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-5</p>
                            <p><span class="label">File:</span> app-config.bicep</p>
                            <p><span class="label">Line:</span> 7</p>
                            <p><span class="label">Issue:</span> The App Configuration resource does not configure a private endpoint, meaning management and data plane access are potentially exposed over public internet by default, violating the secure access requirement for sensitive configuration stores.</p>
                            <p><span class="label">Remediation:</span> Configure a private endpoint for the App Configuration resource to restrict access solely to approved virtual networks. Add a &#x27;privateEndpointConnections&#x27; property or deploy a &#x27;Microsoft.Network/privateEndpoints&#x27; resource referencing the AppConfiguration resource.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> app-config.bicep</p>
                            <p><span class="label">Line:</span> 7</p>
                            <p><span class="label">Issue:</span> The App Configuration service is publicly accessible by default and no access restrictions or public network disablement is present. This increases risk of unauthorized access.</p>
                            <p><span class="label">Remediation:</span> Disable public network access for the App Configuration resource or implement network access rules to restrict access to only approved IP ranges or virtual networks using the &#x27;publicNetworkAccess&#x27; property set to &#x27;Disabled&#x27;.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> app-config.bicep</p>
                            <p><span class="label">Line:</span> 19</p>
                            <p><span class="label">Issue:</span> Key-values and potentially sensitive configurations may be stored inline via the &#x27;keyValues&#x27; object, and no integration with Azure Key Vault for storing secrets or sensitive information is enforced.</p>
                            <p><span class="label">Remediation:</span> Ensure that secrets, such as connection strings or keys, are stored securely in Azure Key Vault and only references (e.g., Key Vault URIs) are placed in App Configuration. Implement validation or clearly document secure usage for contributors.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-5</p>
                            <p><span class="label">File:</span> event-grid.bicep</p>
                            <p><span class="label">Line:</span> 9</p>
                            <p><span class="label">Issue:</span> No implementation of Private Endpoints for Event Grid system topics or referenced storage accounts. Without private endpoints, Event Grid topics and their Storage Account sources may be exposed over the public Internet, increasing risk of unauthorized access or data exfiltration.</p>
                            <p><span class="label">Remediation:</span> Integrate Private Endpoints for each storage account source referenced in &#x27;storageFuzzId&#x27; and &#x27;storageCorpusIds&#x27;, and, if supported and required, for system topics as well. Ensure all data flows remain within the Azure backbone, without traversing the public Internet.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-3</p>
                            <p><span class="label">File:</span> event-grid.bicep</p>
                            <p><span class="label">Line:</span> 9</p>
                            <p><span class="label">Issue:</span> No configuration or reference for Network Security Groups (NSGs) to protect associated storage account(s) or service endpoints. Lack of NSGs may allow unwanted network access to the referenced storage accounts and function destinations.</p>
                            <p><span class="label">Remediation:</span> Associate NSGs with all relevant subnets that host storage accounts or function apps. Create restrictive NSG rules to allow only required traffic from known sources.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> event-grid.bicep</p>
                            <p><span class="label">Line:</span> 9</p>
                            <p><span class="label">Issue:</span> File does not define any Azure Firewall or similar centralized network control for storage accounts or system topics. Critical Azure resources are potentially left exposed to wider network access.</p>
                            <p><span class="label">Remediation:</span> Implement Azure Firewall or a similar network security appliance between critical storage accounts and external/public endpoints. Restrict allowed traffic to necessary internal sources only.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> event-grid.bicep</p>
                            <p><span class="label">Line:</span> 9</p>
                            <p><span class="label">Issue:</span> Potential exposure of public endpoints on referenced storage accounts or Event Grid topics due to lack of access restriction or IP whitelisting configuration.</p>
                            <p><span class="label">Remediation:</span> Restrict storage account and Event Grid topic endpoints to only required IP ranges. Deny public Internet access unless explicitly justified and mitigated.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> function-settings.bicep</p>
                            <p><span class="label">Line:</span> 32</p>
                            <p><span class="label">Issue:</span> The storage account &#x27;funcStorage&#x27; is referenced as an existing resource, with no configuration for network access restrictions or integration with network security groups (NSGs) or Azure Firewall. Lack of explicit access controls puts the storage account at risk of unauthorized or broad public access, violating the requirement to protect storage accounts with network controls.</p>
                            <p><span class="label">Remediation:</span> Ensure the storage account is configured to deny public network access and is protected with private endpoints, NSGs, or Azure Firewall. If managed outside this template, confirm that corresponding configurations exist for &#x27;funcStorage&#x27;.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> function-settings.bicep</p>
                            <p><span class="label">Line:</span> 32</p>
                            <p><span class="label">Issue:</span> No properties are set to restrict public network access on the storage account &#x27;funcStorage&#x27;. Azure Storage accounts are public by default unless explicitly locked down, creating exposure to the internet.</p>
                            <p><span class="label">Remediation:</span> Update the storage account configuration to set &#x27;publicNetworkAccess&#x27; to &#x27;Disabled&#x27; or ensure that only required networks/subnets have access. Use private endpoints for the storage account whenever possible.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> function-settings.bicep</p>
                            <p><span class="label">Line:</span> 58</p>
                            <p><span class="label">Issue:</span> Sensitive information such as the Application Insights instrumentation key (&#x27;APPINSIGHTS_INSTRUMENTATIONKEY&#x27;), application ID, and potentially credential-like parameters are set as app settings. While &#x27;app_insights_key&#x27; uses the @secure() attribute, settings are stored in the app configuration and may be accessible unless further restricted.</p>
                            <p><span class="label">Remediation:</span> Move secrets and high-sensitivity configurations (e.g., instrumentation keys, secret URIs) into Azure Key Vault, and reference them using Key Vault references in the app settings. This ensures secrets are centrally managed and protected in accordance with best practices.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> function.bicep</p>
                            <p><span class="label">Line:</span> 69</p>
                            <p><span class="label">Issue:</span> The storage account &#x27;funcStorage&#x27; is referenced as &#x27;existing&#x27; and no network security controls (NSGs, firewalls, or private endpoints) are applied or validated in this template. Unrestricted storage accounts could be accessible over public networks, violating the requirement to protect sensitive resources.</p>
                            <p><span class="label">Remediation:</span> Ensure the referenced storage account limits access to trusted networks only (using Private Endpoints, firewall rules, and/or NSGs). Update the template or deployment process to enforce network-based access restrictions on the storage account per ASB NS-1.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-1</p>
                            <p><span class="label">File:</span> function.bicep</p>
                            <p><span class="label">Line:</span> 69</p>
                            <p><span class="label">Issue:</span> The storage account &#x27;funcStorage&#x27; is referenced as &#x27;existing&#x27; but there is no validation or enforcement of encryption at rest. Without explicit enforcement, the storage account may not protect data with encryption as required by ASB.</p>
                            <p><span class="label">Remediation:</span> Ensure that the storage account has encryption at rest enabled (with Microsoft-managed or customer-managed keys). Validate or enforce this setting out-of-band or by querying the existing resource properties in the deployment pipeline.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> function.bicep</p>
                            <p><span class="label">Line:</span> 69</p>
                            <p><span class="label">Issue:</span> The storage account &#x27;funcStorage&#x27; may expose its endpoints publicly because there are no firewall or private endpoint configurations specified, nor are any public access restrictions validated in the template.</p>
                            <p><span class="label">Remediation:</span> Restrict storage account public endpoints with network rules. Enable the firewall and use private endpoints. Confirm that &#x27;funcStorage&#x27; permits traffic only from trusted Azure networks or on-premises networks as per business requirements.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> function.bicep</p>
                            <p><span class="label">Line:</span> 109</p>
                            <p><span class="label">Issue:</span> A SAS token for the storage account is constructed in the template using parameters, and a connection string with the SAS token is constructed for log storage. There are no references to Azure Key Vault or secured parameter usage for sensitive information. SAS tokens should be tightly controlled and not exposed in templates.</p>
                            <p><span class="label">Remediation:</span> Store sensitive information such as SAS tokens and storage secrets in Azure Key Vault and reference them securely from your deployments. Avoid constructing or exposing such tokens directly in templates or as parameters, and rotate SAS tokens regularly.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> hub-network.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> The virtual network and subnet are defined without any associated Network Security Groups (NSGs) or Azure Firewall. According to ASB Control NS-1, resources should be protected with NSGs or Azure Firewall to control and restrict network traffic.</p>
                            <p><span class="label">Remediation:</span> Associate an NSG to the &#x27;hub-subnet&#x27; and define appropriate inbound and outbound security rules to restrict access to only necessary sources and destinations. Alternately, deploy Azure Firewall for advanced network protection as required for your environment.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-3</p>
                            <p><span class="label">File:</span> hub-network.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> No Network Security Groups (NSGs) are implemented or referenced for the subnets in this virtual network, violating ASB NS-3. Absence of NSGs means there are no explicit controls on allowed or denied traffic flow.</p>
                            <p><span class="label">Remediation:</span> Create an NSG resource and associate it with the &#x27;hub-subnet&#x27;. Define rules to restrict traffic according to the needs of your workload and best practices (e.g., deny all inbound except specific required ports, allow legitimate outbound as needed).</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> instance-config.bicep</p>
                            <p><span class="label">Line:</span> 24</p>
                            <p><span class="label">Issue:</span> The network configuration defines an address space and subnet, but there is no evidence of the deployment or association of Network Security Groups (NSGs) or Azure Firewall to protect the subnets or VMs. This leaves resources potentially open to unfiltered network traffic and violates the requirement to use NSGs or Azure Firewall for protecting sensitive resources.</p>
                            <p><span class="label">Remediation:</span> Explicitly deploy and associate NSGs to the defined subnet(s) and/or VMs. Restrict both inbound and outbound traffic to only that which is required. Ensure all subnets and VM NICs have appropriate NSG rules as per the workload needs.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-3</p>
                            <p><span class="label">File:</span> instance-config.bicep</p>
                            <p><span class="label">Line:</span> 24</p>
                            <p><span class="label">Issue:</span> No Network Security Groups (NSGs) are defined or referenced for the subnet or resources. This violates the requirement to use NSGs to control inbound and outbound traffic at both the subnet and NIC level.</p>
                            <p><span class="label">Remediation:</span> Define NSGs within the template and associate them to the subnet and/or VM NICs. Create granular security rules that explicitly permit only necessary traffic and deny all others by default.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-4</p>
                            <p><span class="label">File:</span> instance-config.bicep</p>
                            <p><span class="label">Line:</span> 0</p>
                            <p><span class="label">Issue:</span> There are VMs or VM images defined, but there is no indication that managed disks are used and configured for encryption at rest.</p>
                            <p><span class="label">Remediation:</span> Explicitly define VM disks as managed disks (default since Azure ARM V2) and verify that encryption is enabled, ideally with customer-managed keys (CMKs) where required.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-1</p>
                            <p><span class="label">File:</span> instance-config.bicep</p>
                            <p><span class="label">Line:</span> 0</p>
                            <p><span class="label">Issue:</span> The configuration does not explicitly specify disk encryption at rest for VM disks or any data storage, which could leave data unprotected if the default settings are changed or not enabled in the deployment.</p>
                            <p><span class="label">Remediation:</span> Explicitly set &#x27;encryptionSettings&#x27; or equivalent for all managed disks and configure encryption at rest with Azure-managed or customer-managed keys for all storage resources.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> instance-config.bicep</p>
                            <p><span class="label">Line:</span> 3</p>
                            <p><span class="label">Issue:</span> Sensitive values such as admin lists, tenant IDs, client IDs, and domains are pulled from parameters, but their secure storage or retrieval is not specified. Without proper protection, these secrets could be exposed in logs or accidental misconfiguration.</p>
                            <p><span class="label">Remediation:</span> Do not store sensitive values directly in parameters or outputs. Use Azure Key Vault to securely store and retrieve credentials, tenant information, client IDs, and other secrets during deployment and at runtime. Reference Key Vault secrets securely within templates.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> IM-2</p>
                            <p><span class="label">File:</span> instance-config.bicep</p>
                            <p><span class="label">Line:</span> 3</p>
                            <p><span class="label">Issue:</span> No controls or settings are defined to require or enforce Multi-Factor Authentication (MFA) for users or administrators listed in the &#x27;Admins&#x27; parameter.</p>
                            <p><span class="label">Remediation:</span> Configure Azure AD conditional access policies to enforce MFA for all administrator and user accounts with access to this deployment. Clearly document the requirement for MFA usage in operational procedures.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> IM-3</p>
                            <p><span class="label">File:</span> instance-config.bicep</p>
                            <p><span class="label">Line:</span> 3</p>
                            <p><span class="label">Issue:</span> There are no policies or technical controls in place to enforce conditional access policies for admin or user access to the VMs or their management interfaces.</p>
                            <p><span class="label">Remediation:</span> Ensure conditional access policies are in place in Azure AD that restrict access based on risk, location, or device compliance. Reference or link conditional access requirements in your deployment documentation.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> ip-rules.bicep</p>
                            <p><span class="label">Line:</span> 3</p>
                            <p><span class="label">Issue:</span> The &#x27;corpNetIps&#x27; variable allows very broad public IP ranges (e.g., &#x27;*******/8&#x27;, &#x27;********/8&#x27;, etc.) which represent entire Class A/B public address spaces. Allowing such broad public IPs as trusted sources to critical resources (e.g., Storage, LogicApps) increases the potential attack surface and defeats network isolation. ASB NS-2 requires minimizing public exposure.</p>
                            <p><span class="label">Remediation:</span> Restrict allowed IP addresses to the smallest possible set necessary for business operations. Replace broad ranges like &#x27;/8&#x27; or &#x27;/16&#x27; with narrowly scoped ranges or specific addresses. Where possible, use private IPs/VNET integration or trusted NAT gateways rather than wide public prefixes.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-3</p>
                            <p><span class="label">File:</span> keyvault.bicep</p>
                            <p><span class="label">Line:</span> None</p>
                            <p><span class="label">Issue:</span> There is no evidence of Network Security Groups (NSGs) being applied to subnets accessing the Key Vault. NSGs are needed to filter and restrict which resources and users can communicate with the Key Vault in accordance with least privilege principles.</p>
                            <p><span class="label">Remediation:</span> Ensure that NSGs are applied to the relevant subnets (such as &#x27;hubSubnetId&#x27;) with rules restricting both inbound and outbound access to only required addresses and ports. Document or reference the NSG implementation.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> operational-insights.bicep</p>
                            <p><span class="label">Line:</span> 74</p>
                            <p><span class="label">Issue:</span> The outputs &#x27;appInsightsAppId&#x27; and &#x27;appInsightsInstrumentationKey&#x27; expose sensitive instrumentation keys and IDs as deployment outputs. Instrumentation keys can be used to send telemetry to the Application Insights resource, potentially leading to data leakage if accessed by unauthorized parties. This contravenes the requirement to keep sensitive secrets or keys secure and not exposed in output.</p>
                            <p><span class="label">Remediation:</span> Remove sensitive outputs such as instrumentation keys and AppIds from the outputs section. If these outputs are absolutely needed, restrict their exposure to authorized automation only, or preferably retrieve such values at runtime through secure mechanisms like managed identities and Azure Key Vault (ASB DP-3).</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> scaleset-networks.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> There are no Network Security Groups (NSGs) defined or associated with the virtual network or its subnets. This means that network-layer access controls are missing, leaving resources unprotected from unauthorized or malicious traffic. ASB NS-1 recommends protecting resources using NSGs or Azure Firewall.</p>
                            <p><span class="label">Remediation:</span> Define and associate NSGs with each subnet, specifying restrictive inbound and outbound traffic rules aligning with the least privilege principle. Consider using an Azure Firewall for centralized control if appropriate.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-3</p>
                            <p><span class="label">File:</span> scaleset-networks.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> The template does not define or reference any Network Security Groups (NSGs) for the scaleset subnet or the virtual network. ASB NS-3 mandates using NSGs to control inbound and outbound traffic at the subnet and/or NIC level.</p>
                            <p><span class="label">Remediation:</span> Add resources for NSGs and associate them with the scaleset subnet. Create NSG rules to explicitly allow only necessary traffic (e.g., from management or application security groups), and deny all other traffic by default.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> server-farms.bicep</p>
                            <p><span class="label">Line:</span> 38</p>
                            <p><span class="label">Issue:</span> The Key Vaults referenced in &#x27;genevaCertVaultId&#x27; variables are likely used to store sensitive certificates. The template does not define any network security controls (e.g., private endpoints, firewall rules, or NSGs) to restrict access to these Key Vaults as recommended by ASB. Without such controls, the Key Vaults may be accessible from the public internet, increasing the attack surface.</p>
                            <p><span class="label">Remediation:</span> Define Key Vault resources in the template with network ACLs to limit access only to required networks or private endpoints. Ensure the referenced Key Vaults are configured to block public access and allow access only from trusted subnets or private endpoints, per ASB control NS-1.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> IM-6</p>
                            <p><span class="label">File:</span> server-farms.bicep</p>
                            <p><span class="label">Line:</span> 101</p>
                            <p><span class="label">Issue:</span> The Key Vault settings for certificate retrieval (&#x27;serverFarms_AntMDS_CERTIFICATE_PFX_GENEVACERT&#x27;) are specified but there is no evidence of assigning access permissions using Role-Based Access Control (RBAC). Without explicit RBAC or Access Policies, applications or users may have broader or implicit access.</p>
                            <p><span class="label">Remediation:</span> Explicitly define and assign roles (e.g., Key Vault Secrets User or Key Vault Certificates Officer) to the managed identities or applications that need access. Limit access to least privilege necessary and audit assigned roles regularly.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> signalR.bicep</p>
                            <p><span class="label">Line:</span> 4</p>
                            <p><span class="label">Issue:</span> The SignalR resource is deployed without explicit network access controls or restriction of public accessibility. By default, Azure SignalR creates a public endpoint that can be accessed over the internet, increasing the risk of unauthorized access or exposure.</p>
                            <p><span class="label">Remediation:</span> Restrict public network access to the SignalR resource by configuring &#x27;networkACLs&#x27; with access modes set to &#x27;Private&#x27; or allow only specific IP ranges or subnets. Consider using Azure Private Link to make the SignalR resource accessible only from within your private network.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> storage-accounts.bicep</p>
                            <p><span class="label">Line:</span> 63</p>
                            <p><span class="label">Issue:</span> The CORS configuration for blobs uses &#x27;allowedHeaders&#x27; and &#x27;exposedHeaders&#x27; set to &#x27;*&#x27;, which may disclose sensitive information and increases risk of cross-origin attacks. ASB DP-3 requires minimizing sensitive information disclosure.</p>
                            <p><span class="label">Remediation:</span> Restrict &#x27;allowedHeaders&#x27; and &#x27;exposedHeaders&#x27; to only the necessary headers. Avoid using wildcard &#x27;*&#x27; unless strictly needed and fully risk-assessed. Regularly review CORS rules to ensure they are as restrictive as possible.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> storage-accounts.bicep</p>
                            <p><span class="label">Line:</span> 97</p>
                            <p><span class="label">Issue:</span> CORS rules for each dynamically generated corpus storage account also allow &#x27;allowedHeaders&#x27; and &#x27;exposedHeaders&#x27; as &#x27;*&#x27;, risking excessive data exposure and cross-origin attacks, in violation of ASB DP-3.</p>
                            <p><span class="label">Remediation:</span> Limit &#x27;allowedHeaders&#x27; and &#x27;exposedHeaders&#x27; to only those required by client applications. Periodically audit CORS settings to ensure minimum required exposure.</p>
                        </div></div>
                    <div class="severity-group">
                        <div class="severity-header medium">🟡 MEDIUM Severity Findings</div>
                    
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> autoscale-settings.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> The autoscale settings reference resources such as server farms and storage accounts, but there are no associated network security configurations (like NSGs or firewalls) defined or enforced to protect these associated resources, per ASB NS-1.</p>
                            <p><span class="label">Remediation:</span> Ensure that the underlying resources (App Service plans, storage accounts) referenced by autoscale rules are protected by appropriate Network Security Groups or Azure Firewall rules. Update the deployment to link or reference these protections for critical resources.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-6</p>
                            <p><span class="label">File:</span> event-grid.bicep</p>
                            <p><span class="label">Line:</span> 9</p>
                            <p><span class="label">Issue:</span> No reference to Virtual Network Service Endpoints for storage accounts; this may permit traffic from outside the trusted network.</p>
                            <p><span class="label">Remediation:</span> Enable Service Endpoints on subnets accessing storage accounts, and restrict storage accounts to accept traffic only from designated subnets with service endpoints enabled.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-9</p>
                            <p><span class="label">File:</span> event-grid.bicep</p>
                            <p><span class="label">Line:</span> 28</p>
                            <p><span class="label">Issue:</span> There is no configuration for enabling diagnostics, logging, or monitoring on the Event Grid system topics or event subscriptions. Lack of logging may hamper forensic and incident response efforts.</p>
                            <p><span class="label">Remediation:</span> Enable diagnostic settings on Event Grid system topics and associated storage resources. Stream logs to Log Analytics, Event Hub, or another centralized log solution.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-1</p>
                            <p><span class="label">File:</span> event-grid.bicep</p>
                            <p><span class="label">Line:</span> 9</p>
                            <p><span class="label">Issue:</span> No explicit configuration to enforce encryption at rest for referenced storage accounts or event destination (StorageQueue). While encryption may be enabled by default, explicit configuration is recommended to enforce compliance.</p>
                            <p><span class="label">Remediation:</span> Specify and enforce encryption at rest on all referenced storage accounts. Where possible, use customer-managed keys (CMK) for sensitive data.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-2</p>
                            <p><span class="label">File:</span> event-grid.bicep</p>
                            <p><span class="label">Line:</span> 9</p>
                            <p><span class="label">Issue:</span> No explicit enforcement of encryption in transit (TLS 1.2+) for storage accounts, queues, or Event Grid endpoints. Data in transit may be vulnerable if not enforced.</p>
                            <p><span class="label">Remediation:</span> Enforce minimum TLS version to 1.2+ for all storage account services and explicitly require https-only connections.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-1</p>
                            <p><span class="label">File:</span> function-settings.bicep</p>
                            <p><span class="label">Line:</span> 32</p>
                            <p><span class="label">Issue:</span> Encryption at rest is not explicitly enabled for the storage account &#x27;funcStorage&#x27;, nor is there any specification of customer-managed keys (CMK). If the storage account&#x27;s encryption is not configured, it may default to platform-managed keys, which may not meet all compliance requirements.</p>
                            <p><span class="label">Remediation:</span> Explicitly check and, if necessary, set the storage account to use encryption at rest, ideally with customer-managed keys (CMK) from Azure Key Vault, depending on compliance needs. If this is managed externally, document the encryption configuration.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-4</p>
                            <p><span class="label">File:</span> hub-network.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> No Azure Firewall or third-party firewall is deployed to provide advanced network protection as per ASB NS-4. While this may be intentional for simplicity, lacking explicit firewall protection may expose resources if NSGs are bypassed or misconfigured.</p>
                            <p><span class="label">Remediation:</span> Consider provisioning Azure Firewall or a supported third-party firewall solution and integrate it with the virtual network to provide centralized governance and additional security controls over network flows.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-8</p>
                            <p><span class="label">File:</span> hub-network.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> No indication that DDoS protection is enabled for the virtual network, per ASB NS-8. Without DDoS protection, workloads in this VNet may be vulnerable to distributed denial-of-service attacks.</p>
                            <p><span class="label">Remediation:</span> Enable Azure DDoS Protection Standard on the virtual network to safeguard against volumetric, protocol, and application layer DDoS attacks.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> instance-config.bicep</p>
                            <p><span class="label">Line:</span> 24</p>
                            <p><span class="label">Issue:</span> The template does not specify whether public endpoints (e.g., public IPs for VMs) are created or restricted. Without explicit restriction, there is a risk of unintentional public exposure of VMs or resources.</p>
                            <p><span class="label">Remediation:</span> Ensure VM NICs and load balancers do not attach public IPs unless absolutely necessary. When required, use whitelisted IP ranges and service endpoints. Otherwise, enforce that VMs are only accessible through private endpoints or secure jump hosts.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-7</p>
                            <p><span class="label">File:</span> instance-config.bicep</p>
                            <p><span class="label">Line:</span> 0</p>
                            <p><span class="label">Issue:</span> There is no evidence that Just-In-Time (JIT) VM access is enabled or configured for the deployed VMs. Without JIT, management ports (SSH/RDP) may be exposed for longer durations than necessary.</p>
                            <p><span class="label">Remediation:</span> Enable JIT VM access on all management ports of deployed VMs by integrating with Azure Security Center configuration post-deployment or via ARM/Bicep extensions where supported.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-10</p>
                            <p><span class="label">File:</span> instance-config.bicep</p>
                            <p><span class="label">Line:</span> 0</p>
                            <p><span class="label">Issue:</span> No Azure Bastion host or equivalent is defined for secure VM access. Without Bastion, administrators may be forced to use less secure access mechanisms (e.g., direct RDP/SSH).</p>
                            <p><span class="label">Remediation:</span> Deploy an Azure Bastion host in the subnet and ensure VM management is routed exclusively through Bastion for administrative access. Remove direct public access to SSH/RDP.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> IM-1</p>
                            <p><span class="label">File:</span> instance-config.bicep</p>
                            <p><span class="label">Line:</span> 3</p>
                            <p><span class="label">Issue:</span> Although Azure AD tenant and client IDs are passed and referenced, the template does not explicitly configure the deployed VMs or applications to use Azure AD for authentication and authorization. This could lead to inconsistent or insecure access controls.</p>
                            <p><span class="label">Remediation:</span> Integrate all VM and application authentication/authorization flows with Azure AD. Use managed identities where possible and require Azure AD accounts for all administrator and application access.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-5</p>
                            <p><span class="label">File:</span> ip-rules.bicep</p>
                            <p><span class="label">Line:</span> 3</p>
                            <p><span class="label">Issue:</span> The IP whitelist approach in &#x27;corpNetIps&#x27;, &#x27;sawVnetIps&#x27;, and &#x27;ingestionServiceIps&#x27; implies public IP-based access control rather than leveraging Private Endpoints. ASB NS-5 recommends using Private Endpoints for secure and private access to Azure resources, minimizing the need to expose public IPs at all.</p>
                            <p><span class="label">Remediation:</span> Use Azure Private Endpoints for all supported resources (e.g., Storage, LogicApps), and remove public IP address whitelisting from resource firewall/network rules where possible.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-6</p>
                            <p><span class="label">File:</span> ip-rules.bicep</p>
                            <p><span class="label">Line:</span> 22</p>
                            <p><span class="label">Issue:</span> No virtual network service endpoints are defined or referenced. ASB NS-6 recommends the use of service endpoints to restrict access to Azure resources only from trusted Azure VNets, not from public address ranges.</p>
                            <p><span class="label">Remediation:</span> Implement virtual network service endpoints on the resources being protected to ensure only traffic from trusted VNets is allowed. Avoid relying only on public IP allow-listing.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> ip-rules.bicep</p>
                            <p><span class="label">Line:</span> 3</p>
                            <p><span class="label">Issue:</span> The file defines IP allow rules, but there is no evidence of enforcement using network security groups (NSGs) or Azure Firewall. ASB NS-1 requires the use of NSGs or Azure Firewall to restrict and monitor access to critical resources.</p>
                            <p><span class="label">Remediation:</span> Apply these rules using an NSG or Azure Firewall resource in the deployment, ensuring that only explicitly allowed and necessary traffic is permitted. Document how these IP ranges are enforced at the network layer.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-3</p>
                            <p><span class="label">File:</span> ip-rules.bicep</p>
                            <p><span class="label">Line:</span> 3</p>
                            <p><span class="label">Issue:</span> There is no indication that NSGs are used to enforce these IP restriction rules. ASB NS-3 expects the use of NSGs to control inbound and outbound traffic.</p>
                            <p><span class="label">Remediation:</span> Attach NSGs with appropriate rules to the relevant subnet or network interfaces, ensuring that only required inbound and outbound traffic as per business justification is allowed.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-6</p>
                            <p><span class="label">File:</span> keyvault.bicep</p>
                            <p><span class="label">Line:</span> 17</p>
                            <p><span class="label">Issue:</span> The Key Vault deployment does not specify the use of customer-managed keys (CMK) for encrypting data at rest, potentially limiting control over encryption key lifecycle and compliance requirements for sensitive environments.</p>
                            <p><span class="label">Remediation:</span> Enable and configure customer-managed keys (CMK) for the Key Vault by creating a Key Vault key and assigning it as the encryption key for Key Vault, if compliance or data governance necessitates.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> keyvault.bicep</p>
                            <p><span class="label">Line:</span> 36</p>
                            <p><span class="label">Issue:</span> Secrets are passed as plain values in the &#x27;secrets&#x27; object and could, depending on deployment process, be exposed in deployment logs or state files. Inline or non-protected secret values may lead to accidental disclosure.</p>
                            <p><span class="label">Remediation:</span> Use secure parameters and avoid inline secret values in your templates. Pass secrets via secure mechanisms (such as Azure Key Vault references or deployment pipeline secrets) and ensure all secrets are injected at runtime and not hardcoded or exposed in code.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> IM-6</p>
                            <p><span class="label">File:</span> keyvault.bicep</p>
                            <p><span class="label">Line:</span> 31</p>
                            <p><span class="label">Issue:</span> Key Vault is deployed with &#x27;enableRbacAuthorization&#x27; set to true, but no Azure RBAC role assignments are defined in the template. Without explicit least-privilege role assignments, vault access could either default to too permissive rights or be misconfigured later.</p>
                            <p><span class="label">Remediation:</span> Define and assign appropriate Azure RBAC roles (e.g., Key Vault Reader, Secrets User) to managed identities, users, or service principals that require access. Ensure only the minimum set of privileges necessary for operational requirements are granted.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-2</p>
                            <p><span class="label">File:</span> scaleset-networks.bicep</p>
                            <p><span class="label">Line:</span> 11</p>
                            <p><span class="label">Issue:</span> A public IPv4 address is created and associated with the NAT Gateway for outbound traffic, but there are no corresponding controls (such as NSGs or firewalls) mentioned to restrict or monitor access from the public network. ASB NS-2 recommends securing all public endpoints to minimize exposure.</p>
                            <p><span class="label">Remediation:</span> Ensure that NSGs are in place to restrict outbound and inbound access to only required ports and IPs. Consider Azure Firewall or restricting NAT Gateway outbound access to approved destinations only.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-9</p>
                            <p><span class="label">File:</span> scaleset-networks.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> There is no evidence of enabling network traffic monitoring via Azure Monitor or Network Watcher within the template. ASB NS-9 recommends monitoring and logging network traffic for auditing and threat detection.</p>
                            <p><span class="label">Remediation:</span> Deploy and configure Azure Network Watcher and Diagnostic Settings for relevant network resources (such as public IPs, NAT Gateway, vNET, and subnets) to collect and analyze traffic logs and metrics.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> server-farms.bicep</p>
                            <p><span class="label">Line:</span> 101</p>
                            <p><span class="label">Issue:</span> Sensitive certificate secrets are referenced via Key Vault, which is appropriate. However, the deployment does not ensure that the Key Vault resource enforcing secret storage exists or is securely configured within this scope, risking unintended data exposure if the referenced Key Vaults are not compliant.</p>
                            <p><span class="label">Remediation:</span> Ensure that the referenced Key Vaults exist and are securely configured. Consider provisioning and configuring Key Vaults in this (or a prerequisite) template with secure access, logging, and monitoring.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-1</p>
                            <p><span class="label">File:</span> server-farms.bicep</p>
                            <p><span class="label">Line:</span> 101</p>
                            <p><span class="label">Issue:</span> The template references secret storage in Key Vault but does not show explicit configuration for encryption at rest for the Key Vaults used. While Azure Key Vault encrypts by default, explicit configuration is necessary to comply with strict requirements and prove compliance.</p>
                            <p><span class="label">Remediation:</span> Document and, if possible, explicitly configure Key Vaults for encryption at rest with required compliance-level keys (e.g., specify use of HSM-backed keys or CMK).</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-1</p>
                            <p><span class="label">File:</span> signalR.bicep</p>
                            <p><span class="label">Line:</span> 4</p>
                            <p><span class="label">Issue:</span> No Network Security Groups (NSG) or Azure Firewall controls are referenced or associated with the SignalR resource. While SignalR is a managed service, network access can still be limited by using Private Link or service endpoints.</p>
                            <p><span class="label">Remediation:</span> Deploy the SignalR resource with Private Endpoint enabled, reducing reliance on public internet exposure and enforce NSG controls on the virtual network. Document compensating controls or mitigations if NSGs or Azure Firewall are not directly applicable.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-1</p>
                            <p><span class="label">File:</span> signalR.bicep</p>
                            <p><span class="label">Line:</span> 4</p>
                            <p><span class="label">Issue:</span> The configuration does not specify encryption at rest settings or customer-managed keys (CMK) for the SignalR resource. While Azure manages encryption by default, best practice is to enable customer-managed keys for greater control.</p>
                            <p><span class="label">Remediation:</span> Enable encryption at rest with customer-managed keys for the SignalR resource by specifying &#x27;encryption&#x27; property with a Key Vault reference in the resource definition.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-3</p>
                            <p><span class="label">File:</span> storage-accounts.bicep</p>
                            <p><span class="label">Line:</span> 21</p>
                            <p><span class="label">Issue:</span> &#x27;networkAcls&#x27; are defined, but there is no evidence of associated Network Security Groups (NSGs) protecting the underlying storage subnet, as required by ASB NS-3. Relying solely on storage firewall increases risk if VNET/network configuration changes.</p>
                            <p><span class="label">Remediation:</span> Apply Network Security Groups (NSGs) to the &#x27;hubSubnetId&#x27; or relevant subnet to enforce additional network-layer inbound traffic restrictions for storage account traffic.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-1</p>
                            <p><span class="label">File:</span> storage-accounts.bicep</p>
                            <p><span class="label">Line:</span> 16</p>
                            <p><span class="label">Issue:</span> There is no explicit configuration in the storage account resources to enable customer-managed keys (CMK) for encryption at rest. While Azure storage accounts provide Microsoft-managed key encryption by default, ASB DP-1 recommends using CMK for sensitive or critical workloads.</p>
                            <p><span class="label">Remediation:</span> Configure storage accounts to use Azure Key Vault customer-managed keys (CMK) for encryption at rest, if sensitive data is stored, by adding the &#x27;encryption.keySource&#x27; and &#x27;encryption.keyVaultProperties&#x27; properties.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> IM-1</p>
                            <p><span class="label">File:</span> storage-accounts.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> No Azure AD-based authentication (e.g., Azure AD DS integration, Azure RBAC, managed identities) is configured for storage accounts, relying solely on Shared Key and SAS tokens. ASB IM-1 recommends using Azure AD for authentication/authorization.</p>
                            <p><span class="label">Remediation:</span> Configure storage accounts to require Azure AD authentication for data plane access. Assign appropriate RBAC roles (such as &#x27;Storage Blob Data Reader/Contributor&#x27;) to users and applications. Set &#x27;isLocalUserEnabled&#x27; to &#x27;false&#x27; (already set) and disable Shared Key access (already set), but also enforce Azure AD auth.</p>
                        </div></div>
                    <div class="severity-group">
                        <div class="severity-header low">🔵 LOW Severity Findings</div>
                    
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-3</p>
                            <p><span class="label">File:</span> autoscale-settings.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> Parameters &#x27;server_farm_id&#x27;, &#x27;func_storage_account_id&#x27;, and &#x27;workspaceId&#x27; appear to reference potentially sensitive resources, but there is no evidence of secure referencing such as using Key Vault integration for secrets or sensitive identifiers. ASB DP-3 recommends using Key Vault for storing such information.</p>
                            <p><span class="label">Remediation:</span> Where possible, use Azure Key Vault reference for sensitive parameter values or connection strings. Review parameter passing to ensure no secrets or sensitive data are stored in plaintext or configuration files.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> AM-1</p>
                            <p><span class="label">File:</span> autoscale-settings.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> No role assignments or RBAC configurations are specified, and there is no indication of least-privilege access for managing autoscale settings or diagnostic configurations. This could result in excessive privileges if managed outside IaC.</p>
                            <p><span class="label">Remediation:</span> Explicitly define least-privilege RBAC assignments (e.g., using Microsoft.Authorization/roleAssignments) within the template, or ensure that only minimal required permissions are granted to principals that can modify these resources.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-9</p>
                            <p><span class="label">File:</span> hub-network.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> There is no evidence of configuring network monitoring or logging via Azure Monitor or Network Watcher, as required by ASB NS-9. This limits visibility into network activities and troubleshooting.</p>
                            <p><span class="label">Remediation:</span> Enable and configure Azure Network Watcher for the subscription and region. Set up diagnostic logging (flow logs, NSG events, etc.) to monitor and analyze network traffic within the virtual network.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-4</p>
                            <p><span class="label">File:</span> ip-rules.bicep</p>
                            <p><span class="label">Line:</span> 3</p>
                            <p><span class="label">Issue:</span> There is no reference to the use of Azure Firewall or a third-party firewall. For critical resource protection, ASB NS-4 recommends using such firewalls for advanced threat protection and centralized policy management.</p>
                            <p><span class="label">Remediation:</span> Deploy Azure Firewall or an approved third-party firewall if the resources protected by these IP rules are considered sensitive or critical.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-8</p>
                            <p><span class="label">File:</span> ip-rules.bicep</p>
                            <p><span class="label">Line:</span> 3</p>
                            <p><span class="label">Issue:</span> The template does not include any settings or resource declarations for Azure DDoS Protection. ASB NS-8 recommends enabling DDoS Protection for resources exposed to the Internet.</p>
                            <p><span class="label">Remediation:</span> Add Azure DDoS Protection Standard at the virtual network level for Internet-exposed workloads.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-9</p>
                            <p><span class="label">File:</span> ip-rules.bicep</p>
                            <p><span class="label">Line:</span> 3</p>
                            <p><span class="label">Issue:</span> There is no mention of network monitoring, traffic analytics, or diagnostic logging. NS-9 requires logging and monitoring of network traffic for detection and audit.</p>
                            <p><span class="label">Remediation:</span> Integrate network logging using Azure Network Watcher, enable NSG flow logs, and ensure diagnostic logs are sent to a Log Analytics Workspace or SIEM.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-7</p>
                            <p><span class="label">File:</span> ip-rules.bicep</p>
                            <p><span class="label">Line:</span> 3</p>
                            <p><span class="label">Issue:</span> No Just-in-Time (JIT) VM access controls are in place. If any of these IP rules are for management ports to VMs, this risks overexposure. ASB NS-7 recommends restricting direct management access using JIT access.</p>
                            <p><span class="label">Remediation:</span> For VM management, enable JIT VM access in Defender for Cloud, and configure NSGs to only allow management access when JIT is active.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-8</p>
                            <p><span class="label">File:</span> scaleset-networks.bicep</p>
                            <p><span class="label">Line:</span> 1</p>
                            <p><span class="label">Issue:</span> The template does not specify enabling Azure DDoS Protection for the virtual network. This may leave resources exposed to volumetric attacks. ASB NS-8 recommends enabling DDoS Protection for critical workloads.</p>
                            <p><span class="label">Remediation:</span> Add a resource to enable Azure DDoS Protection on the virtual network if it supports critical services or public exposure.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-6</p>
                            <p><span class="label">File:</span> scaleset-networks.bicep</p>
                            <p><span class="label">Line:</span> 35</p>
                            <p><span class="label">Issue:</span> Service endpoints are defined as an empty array for the scaleset subnet, implying no service endpoints are used for secure access to Azure PaaS services. ASB NS-6 recommends using service endpoints to restrict Azure resource access to specific networks.</p>
                            <p><span class="label">Remediation:</span> If the workload will access Azure platform services (e.g., Storage, SQL), define service endpoints in the subnet configuration for those services to enforce private, secure access.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> NS-5</p>
                            <p><span class="label">File:</span> scaleset-networks.bicep</p>
                            <p><span class="label">Line:</span> 37</p>
                            <p><span class="label">Issue:</span> &#x27;privateEndpointNetworkPolicies&#x27; and &#x27;privateLinkServiceNetworkPolicies&#x27; are both set to &#x27;Enabled&#x27;, but no private endpoint resources are present. ASB NS-5 recommends using Private Endpoints to secure access to Azure services, which requires disabling some network policies as needed.</p>
                            <p><span class="label">Remediation:</span> If service access requires it, deploy Azure Private Endpoints and adjust network policy settings accordingly to fully leverage private connectivity. Otherwise, review if policies should be set to &#x27;Disabled&#x27; for future private endpoint integration.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-6</p>
                            <p><span class="label">File:</span> server-farms.bicep</p>
                            <p><span class="label">Line:</span> 101</p>
                            <p><span class="label">Issue:</span> There is no indication that Customer-Managed Keys (CMKs) are utilized for the Key Vaults containing sensitive certificates. Without CMK, control over encryption keys and data residency may not be sufficient for highly sensitive workloads.</p>
                            <p><span class="label">Remediation:</span> Configure the Key Vaults to use customer-managed keys for controlling encryption at rest for certificates and secrets stored in Key Vault, especially for confidential or regulated data.</p>
                        </div>
                        <div class="finding">
                            <p><span class="label">Control:</span> DP-2</p>
                            <p><span class="label">File:</span> server-farms.bicep</p>
                            <p><span class="label">Line:</span> 101</p>
                            <p><span class="label">Issue:</span> There is no explicit enforcement of using TLS 1.2 or higher for accessing Key Vaults. Depending on default settings may not satisfy compliance needs or future best practices.</p>
                            <p><span class="label">Remediation:</span> Explicitly configure Key Vaults to require TLS 1.2 (or higher) through deployment scripts or Azure Policy. Update all consuming applications and services to use TLS 1.2+.</p>
                        </div></div>
            </body>
            </html>
            