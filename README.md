# IaC Guardian GPT

Infrastructure as Code (IaC) Security Analysis Tool powered by Azure OpenAI and Azure Security Benchmark v3.0.

## Overview

IaC Guardian GPT is a comprehensive security analysis tool that reviews Infrastructure as Code templates (ARM, Bicep, Terraform) against the Azure Security Benchmark v3.0. It provides detailed security recommendations, generates HTML reports, and integrates with development workflows through MCP (Model Context Protocol) server support.

## Features

- **Multi-format Support**: Analyzes ARM templates, Bicep files, and Terraform configurations
- **Azure Security Benchmark v3.0**: Comprehensive security analysis based on Microsoft's official security framework
- **Intelligent Report Generation**: Creates responsive HTML reports with Glass UI design
- **MCP Server Integration**: Integrates with VSCode Copilot and other AI development tools
- **Template Parameter Expansion**: Advanced parameter resolution and cross-reference analysis
- **Domain Priority Ordering**: Prioritizes security controls by domain (Identity → Network → Data Protection)
- **Consistent Analysis**: Deterministic analysis with caching for reproducible results

## Project Structure

```
IaCGuardianGPT/
├── src/                          # Source code
│   ├── core/                     # Core application modules
│   │   ├── security_opt.py       # Main security analysis engine
│   │   ├── security_pr_review.py # PR review functionality
│   │   ├── enhanced_resource_control_mappings.py # Resource mapping
│   │   └── template_parameter_expander.py # Template expansion
│   ├── analysis/                 # Security analysis modules
│   │   ├── azure_security_benchmark_patterns.py
│   │   └── get_security_controls.py
│   ├── reporting/                # Report generation
│   │   └── generate_html_report.py
│   └── mcp/                      # MCP server implementation
│       └── mcp_server.py
├── tests/                        # Test files
│   ├── unit/                     # Unit tests
│   └── integration/              # Integration tests
├── docs/                         # Documentation
│   ├── guides/                   # User guides and tutorials
│   └── architecture/             # Architecture documentation
├── config/                       # Configuration files
│   ├── requirements.txt          # Python dependencies
│   └── mcp_config.json          # MCP server configuration
├── data/                         # Data files
│   ├── benchmarks/              # Azure Security Benchmark data
│   └── mappings/                # Resource-control mappings
├── reports/                      # Generated reports
│   ├── security_findings/       # Security analysis reports
│   ├── demo/                    # Demo reports
│   └── archived/                # Archived reports
├── scripts/                      # Utility scripts
│   ├── setup/                   # Setup and installation scripts
│   └── utilities/               # Utility scripts
├── demo_files/                   # Sample IaC files for testing
├── test_code_snippets/          # Code snippets for testing
├── assets/                       # Static assets
├── database/                     # Database schemas
└── master_controls_db/          # Master controls database system
```

## Prerequisites

- Python 3.8 or higher
- Azure DevOps organization with repositories
- Azure OpenAI service instance
- Required Python packages (see `requirements.txt`)

## Installation

1. Clone this repository
2. Install dependencies:
```sh
pip install -r requirements.txt
```

## Configuration

Create a `.env` file in the project root with the following environment variables:

```sh
# Azure DevOps Configuration
AZURE_DEVOPS_PAT=<your-personal-access-token>
AZURE_DEVOPS_ORG=<your-org-name>
AZURE_DEVOPS_PROJECT=<your-project-name>

# Azure OpenAI Configuration
AZURE_OPENAI_ENDPOINT=<your-openai-endpoint>
AZURE_OPENAI_API_KEY=<your-openai-api-key>
AZURE_OPENAI_DEPLOYMENT=<your-deployment-name>
AZURE_OPENAI_API_VERSION=2024-02-01
AZURE_OPENAI_USE_AD_AUTH=false  # Set to true for Azure AD authentication
```

## Usage

Run the script with required arguments:

```sh
python security_pr_review.py --repo-id <repository-id> --pr-id <pr-number>
```

### Example

```sh
python security_pr_review.py --repo-id "********-90ab-cdef-1234-567890abcdef" --pr-id 123
```

## Supported File Types

- Terraform (`.tf`)
- Bicep (`.bicep`)
- ARM Templates (`.json`, `.arm`)
- YAML (`.yml`, `.yaml`)
- Scripts (`.ps1`, `.sh`)

## Security Rules

The tool checks for various security issues including:

- Network security group configurations
- Storage account security settings
- Key Vault access policies
- Encryption settings
- Authentication and authorization
- Network access controls
- Resource security configurations

## Output

The tool provides:

1. Detailed PR comments for each security finding
2. Summary comment with overall security assessment
3. Severity-based categorization (CRITICAL, HIGH, MEDIUM, LOW)
4. Specific recommendations for remediation

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Azure Security Benchmark v3.0
- Azure OpenAI Service
- Azure DevOps REST APIs

## Support

For issues and questions, please open a GitHub issue in this repository.