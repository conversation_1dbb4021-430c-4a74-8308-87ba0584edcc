File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
Grafana.deploymentTemplate.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,38.0,"The 'publicNetworkAccess' property is set to 'Enabled' for the Microsoft.Dashboard/grafana resource. This exposes the Grafana instance to the public internet, enabling initial access and remote exploitation vectors such as brute force, credential stuffing, or exploitation of zero-day vulnerabilities. The blast radius includes potential compromise of monitoring data, lateral movement into other Azure resources, and exposure of sensitive dashboards.","Set 'publicNetworkAccess' to 'Disabled' to restrict access to the Grafana instance. Implement Azure Private Link or private endpoints to ensure only trusted networks can access the resource. Review and enforce network security group (NSG) rules to limit inbound and outbound traffic. Reference: Azure Security Benchmark v3.0, NS-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
Grafana.deploymentTemplate.json,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,38.0,"The Grafana instance is configured with 'publicNetworkAccess' set to 'Enabled', which means it is accessible from any network, bypassing network segmentation and deny-by-default principles. This increases the risk of network compromise, unauthorized access, and lateral movement within the Azure environment.","Disable public network access by setting 'publicNetworkAccess' to 'Disabled'. Place the Grafana instance within a segmented virtual network and apply network security groups (NSGs) to restrict access to only required subnets or IP ranges. Reference: Azure Security Benchmark v3.0, NS-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
Grafana.deploymentTemplate.json,NS-3,Network Security,Deploy firewall at the edge of enterprise network,CRITICAL,38.0,"With 'publicNetworkAccess' set to 'Enabled', there is no evidence of Azure Firewall or equivalent stateful filtering protecting the Grafana instance. This allows unrestricted inbound and outbound traffic, increasing the risk of exploitation and data exfiltration.","Deploy Azure Firewall or a similar stateful firewall solution to control and monitor traffic to and from the Grafana instance. Ensure only required ports and protocols are allowed, and all other traffic is denied by default. Reference: Azure Security Benchmark v3.0, NS-3.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal) | [Virtual network traffic routing](https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview) | [Azure Firewall Manager](https://docs.microsoft.com/azure/firewall-manager/overview) | [Hub-spoke topology](https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Use Azure Firewall for stateful application layer traffic restriction and central management. Create user-defined routes (UDR) for complex topologies. Block known bad IPs and high-risk protocols.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal) | [Virtual network traffic routing](https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview) | [Azure Firewall Manager](https://docs.microsoft.com/azure/firewall-manager/overview) | [Hub-spoke topology](https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Use Azure Firewall for stateful application layer traffic restriction and central management. Create user-defined routes (UDR) for complex topologies. Block known bad IPs and high-risk protocols.,"Enhanced Implementation Context:
• Azure Firewall deployment: https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal
• Virtual network traffic routing: https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview
• Azure Firewall Manager: https://docs.microsoft.com/azure/firewall-manager/overview
• Hub-spoke topology: https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 4.4, 4.8, 13.10
• NIST SP800-53 r4: AC-4, SC-7, CM-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Management ports should be closed on your virtual machines
• Management ports of virtual machines should be protected with just-in-time network access control
• IP Forwarding on your virtual machine should be disabled
• All Internet traffic should be routed via your deployed Azure Firewall",ai_analysis,,Validated
Grafana.deploymentTemplate.json,NS-10,Network Security,Ensure Domain Name System (DNS) security,HIGH,27.0,"The 'autoGeneratedDomainNameLabelScope' property is set to 'TenantReuse', which may allow the reuse of DNS labels across the tenant. This increases the risk of DNS subdomain takeover if resources are deleted and labels are reused, potentially enabling attackers to impersonate services or intercept traffic.","Set 'autoGeneratedDomainNameLabelScope' to 'ResourceGroupReuse' or a more restrictive value to minimize the risk of DNS label reuse and subdomain takeover. Regularly audit DNS records and remove unused labels. Reference: Azure Security Benchmark v3.0, NS-10.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/dns/dns-overview) | [NIST DNS Deployment Guide](https://csrc.nist.gov/publications/detail/sp/800-81/2/final) | [Azure Private DNS](https://docs.microsoft.com/azure/dns/private-dns-overview) | [Azure Defender for DNS](https://docs.microsoft.com/azure/security-center/defender-for-dns-introduction) | [Prevent dangling DNS entries](https://docs.microsoft.com/azure/security/fundamentals/subdomain-takeover) | [DNS security best practices](https://docs.microsoft.com/azure/security/fundamentals/network-best-practices#secure-name-resolution) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Use Azure recursive DNS or trusted external DNS servers. Use Azure Private DNS for private zones. Use Azure Defender for DNS for advanced protection against DNS threats including data exfiltration mal...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/dns/dns-overview) | [NIST DNS Deployment Guide](https://csrc.nist.gov/publications/detail/sp/800-81/2/final) | [Azure Private DNS](https://docs.microsoft.com/azure/dns/private-dns-overview) | [Azure Defender for DNS](https://docs.microsoft.com/azure/security-center/defender-for-dns-introduction) | [Prevent dangling DNS entries](https://docs.microsoft.com/azure/security/fundamentals/subdomain-takeover) | [DNS security best practices](https://docs.microsoft.com/azure/security/fundamentals/network-best-practices#secure-name-resolution) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Use Azure recursive DNS or trusted external DNS servers. Use Azure Private DNS for private zones. Use Azure Defender for DNS for advanced protection against DNS threats including data exfiltration malware communication and DNS attacks.,"Enhanced Implementation Context:
• Azure DNS overview: https://docs.microsoft.com/azure/dns/dns-overview
• NIST DNS Deployment Guide: https://csrc.nist.gov/publications/detail/sp/800-81/2/final
• Azure Private DNS: https://docs.microsoft.com/azure/dns/private-dns-overview
• Azure Defender for DNS: https://docs.microsoft.com/azure/security-center/defender-for-dns-introduction
• Prevent dangling DNS entries: https://docs.microsoft.com/azure/security/fundamentals/subdomain-takeover
• DNS security best practices: https://docs.microsoft.com/azure/security/fundamentals/network-best-practices#secure-name-resolution
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 4.9, 9.2
• NIST SP800-53 r4: SC-20, SC-21
• PCI-DSS v3.2.1: Not specified

Azure Policy Examples:
• Azure Defender for DNS should be enabled
• Configure trusted DNS servers for virtual networks
• Monitor DNS queries for malicious activity
• Implement DNS filtering and threat protection",ai_analysis,,Validated
KustoScripts.template.json,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,53.0,"The 'scriptContent' property at line 53 references a variable ('variables('$fxv#0')') whose content is not classified or labeled for sensitivity. Without explicit data classification and labeling, sensitive data processed or stored by this script could be exposed to unauthorized access or misuse. Attackers gaining access to this script could exfiltrate or manipulate sensitive data, increasing the blast radius of a compromise.","Implement Azure Purview or Azure Information Protection to classify and label all data processed by Kusto scripts. Ensure that all script content variables are scanned for sensitive data and labeled appropriately. Update deployment pipelines to enforce data classification before deployment. Reference ASB control DP-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources)

🔵 Azure Guidance: Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other loca...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources),Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other locations.,"Enhanced Implementation Context:
• Data classification overview: https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification
• Azure Purview sensitivity labels: https://docs.microsoft.com/azure/purview/create-sensitivity-label
• Azure Information Protection: https://docs.microsoft.com/azure/information-protection/what-is-information-protection
• Azure SQL Data Discovery: https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification
• Azure Purview data sources: https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources

Compliance Mappings:
• CIS Controls v8: 3.2, 3.7, 3.13
• NIST SP800-53 r4: RA-2, SC-28
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Sensitive data in your SQL databases should be classified
• Implement data discovery and classification across all data stores
• Deploy Azure Purview for enterprise data governance
• Configure sensitivity labels for all data assets",ai_analysis,,Validated
KustoScripts.template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,55.0,"The 'continueOnErrors' property is set to true at line 55, which can allow scripts to proceed even after encountering errors. This can mask unauthorized data access or exfiltration attempts, as failures in data protection or validation logic may be ignored, enabling attackers to bypass controls and increase the blast radius.","Set 'continueOnErrors' to false for all production Kusto scripts to ensure that any error, especially those related to data access or validation, halts execution and triggers alerting. Implement monitoring and alerting for script failures to detect anomalous activity. Reference ASB control DP-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
KustoScripts.template.json,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,128.0,"The script 'fun_LookupByUniqueAttribute_Cloud' (line 123) processes input attributes and searches across all known globally-unique columns for a match, including 'Name', 'GUID', and 'AlternateName'. There is no evidence of data classification, labeling, or inventory of sensitive data within the script or its configuration. This lack of data discovery and classification enables attackers who gain access to the Kusto database or script to enumerate and exfiltrate sensitive or regulated data without detection or control, increasing the blast radius of a potential breach.","Implement Azure Purview or Azure Information Protection to classify and label sensitive data in the Kusto database. Ensure all data sources referenced by the script are inventoried and classified. Apply sensitivity labels and configure access controls based on data classification. Reference: DP-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources)

🔵 Azure Guidance: Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other loca...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources),Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other locations.,"Enhanced Implementation Context:
• Data classification overview: https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification
• Azure Purview sensitivity labels: https://docs.microsoft.com/azure/purview/create-sensitivity-label
• Azure Information Protection: https://docs.microsoft.com/azure/information-protection/what-is-information-protection
• Azure SQL Data Discovery: https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification
• Azure Purview data sources: https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources

Compliance Mappings:
• CIS Controls v8: 3.2, 3.7, 3.13
• NIST SP800-53 r4: RA-2, SC-28
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Sensitive data in your SQL databases should be classified
• Implement data discovery and classification across all data stores
• Deploy Azure Purview for enterprise data governance
• Configure sensitivity labels for all data assets",ai_analysis,,Validated
KustoScripts.template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,128.0,"The script 'fun_LookupByUniqueAttribute_Cloud' (line 123) enables searching for globally-unique attributes, but there is no monitoring or alerting for anomalous access or data exfiltration attempts. Attackers could use this function to enumerate and extract sensitive data attributes without triggering alerts, facilitating data exfiltration and increasing the risk of undetected data loss.","Enable Azure Defender for SQL, Storage, and Kusto to monitor for anomalous data access and exfiltration. Configure alerts for unusual query patterns or large data exports. Integrate monitoring with SIEM solutions for real-time detection and response. Reference: DP-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
KustoScripts.template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,176.0,"The property 'continueOnErrors' is set to true in the Kusto script deployment. This setting allows the deployment to proceed even if errors occur during script execution, which can mask failed data protection logic or incomplete data classification steps. Attackers could exploit this by introducing or leveraging failed scripts to bypass data monitoring, leading to undetected data exfiltration or unauthorized data access. The blast radius includes potential loss of visibility into sensitive data operations and increased risk of data exposure.","Set 'continueOnErrors' to false to ensure that any error in script execution halts the deployment, enforcing strict data protection and monitoring. Additionally, implement robust error handling and alerting for all data-related scripts to ensure failures are immediately visible and remediated. Reference: Azure Security Benchmark DP-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
KustoScripts.template.json,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,231.0,"The script at line 230 (let _input_monikers_data = print _input_monikers ...) processes dynamic input data without any evidence of data classification, discovery, or labeling. This enables an attack vector where sensitive data could be ingested, processed, or exfiltrated without proper inventory or classification, increasing the blast radius in the event of data leakage or unauthorized access.","Integrate Azure Purview or Azure Information Protection to automatically scan, classify, and label all data ingested or processed by this function. Ensure that all dynamic or user-supplied data is subject to data discovery and classification policies before further processing. Reference: DP-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources)

🔵 Azure Guidance: Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other loca...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources),Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other locations.,"Enhanced Implementation Context:
• Data classification overview: https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification
• Azure Purview sensitivity labels: https://docs.microsoft.com/azure/purview/create-sensitivity-label
• Azure Information Protection: https://docs.microsoft.com/azure/information-protection/what-is-information-protection
• Azure SQL Data Discovery: https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification
• Azure Purview data sources: https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources

Compliance Mappings:
• CIS Controls v8: 3.2, 3.7, 3.13
• NIST SP800-53 r4: RA-2, SC-28
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Sensitive data in your SQL databases should be classified
• Implement data discovery and classification across all data stores
• Deploy Azure Purview for enterprise data governance
• Configure sensitivity labels for all data assets",ai_analysis,,Validated
KustoScripts.template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,231.0,"The function at line 230 (let _input_monikers_data = print _input_monikers ...) accepts and processes dynamic input data, but there is no evidence of monitoring for anomalous data access or exfiltration. This lack of monitoring enables an attack vector for unauthorized data transfer or exfiltration, increasing the risk of data compromise.","Enable Azure Defender for Storage, SQL, and other relevant services to monitor for anomalous data access and exfiltration. Implement logging and alerting for all data access and transfer operations within this function. Reference: DP-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
KustoScripts.template.json,IM-8,Identity Management,Restrict the exposure of credential and secrets,HIGH,231.0,"At line 231 (let _input_monikers_data = print _input_monikers | mv-expand moniker = _input_monikers ...), dynamic input is expanded and processed without any evidence of secret scanning or validation. If secrets or credentials are accidentally passed in _input_monikers, they could be exposed or logged, enabling credential exposure and lateral movement attack vectors.","Implement secret scanning and validation for all dynamic inputs. Enforce the use of Azure Key Vault for secret management and ensure that no secrets or credentials are accepted as part of user input. Reference: IM-8.

📚 References: [Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops)

🔵 Azure Guidance: Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.",[Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops),Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.,"Enhanced Implementation Context:
• Azure DevOps Credential Scanner setup: https://secdevtools.azurewebsites.net/helpcredscan.html
• GitHub secret scanning: https://docs.github.com/github/administering-a-repository/about-secret-scanning
• Azure Key Vault developer guide: https://docs.microsoft.com/azure/key-vault/general/developers-guide
• Secure coding practices: https://docs.microsoft.com/azure/security/develop/secure-dev-overview
• Application security guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops

Compliance Mappings:
• CIS Controls v8: 16.9, 16.12
• NIST SP800-53 r4: IA-5
• PCI-DSS v3.2.1: 3.5, 6.3, 8.2

Azure Policy Examples:
• No applicable built-in policy (requires development process implementation)
• Implement credential scanning in CI/CD pipelines
• Enforce Azure Key Vault usage for secret storage
• Monitor and audit secret access patterns
• Require managed identities for Azure service authentication",ai_analysis,,Validated
KustoScripts.template.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,291.0,"""continueOnErrors"": true disables fail-fast behavior for deployment scripts, allowing the deployment to proceed even if errors occur. This can result in incomplete or inconsistent resource states, potentially exposing sensitive data or leaving resources in a misconfigured state. Attackers could exploit these gaps to gain unauthorized access or escalate privileges, increasing the blast radius of a compromise.","Set ""continueOnErrors"" to false to enforce fail-fast deployment. This ensures that any error halts the deployment, preventing partial or insecure configurations. Review all deployment scripts to ensure error handling is strict and that sensitive resources are not left in an indeterminate state. Reference: Azure Security Benchmark DP-3 (Protect data in transit and ensure secure configuration).

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account)

🔵 Azure Guidance: Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windo...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account),Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windows remote management.,"Enhanced Implementation Context:
• Double encryption for Azure data in transit: https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit
• Encryption in transit overview: https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit
• TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem
• Azure Storage secure transfer: https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account

Compliance Mappings:
• CIS Controls v8: 3.10
• NIST SP800-53 r4: SC-8
• PCI-DSS v3.2.1: 3.5, 3.6, 4.1

Azure Policy Examples:
• Kubernetes clusters should be accessible only over HTTPS
• Only secure connections to your Azure Cache for Redis should be enabled
• FTPS only should be required in your Function App
• Secure transfer to storage accounts should be enabled
• Function App should only be accessible over HTTPS
• Latest TLS version should be used in your API App
• Web Application should only be accessible over HTTPS
• Enforce SSL connection should be enabled for PostgreSQL database servers
• Latest TLS version should be used in your Web App",ai_analysis,,Validated
KustoScripts.template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,319.0,"The property 'continueOnErrors' is set to true, which allows the deployment to proceed even if errors occur during script execution. This can enable attackers to bypass critical data validation or error handling, potentially leading to unauthorized data access or exfiltration if failures in security controls are ignored. The blast radius includes undetected data exfiltration or corruption due to unmonitored or failed operations.","Set 'continueOnErrors' to false to ensure that any error during script execution halts the deployment, enforcing strict error handling and preventing silent failures. Additionally, implement monitoring and alerting for all failed operations to ensure immediate response to potential data protection violations. Reference: Azure Security Benchmark DP-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
roleAssignment.deploymentTemplate.json,IM-2,Identity Management,Protect identity and authentication systems,CRITICAL,11.0,"The 'builtInRoleType' parameter is set to 'Owner' by default, and line 28 explicitly assigns the 'Owner' role to the specified principal. Assigning the Owner role grants full administrative privileges over the entire subscription or resource group, enabling initial access, privilege escalation, and lateral movement if the principal is compromised. This configuration dramatically increases the blast radius of any identity compromise, as an attacker with this role can create, modify, or delete any resource, manage access, and disable security controls.","Restrict the assignment of the 'Owner' role to only those identities that require full administrative privileges. Use least privilege by assigning more restrictive roles such as 'Contributor' or custom roles with only the necessary permissions. Implement Privileged Identity Management (PIM) to require approval and just-in-time elevation for Owner assignments. Monitor and audit all role assignments for high-privilege roles. Reference: Azure Security Benchmark v3.0 IM-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score) | [Active Directory security best practices](https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory) | [Azure AD security baseline](https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline) | [Privileged Identity Management](https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Use Azure AD security baseline and Identity Secure Score to evaluate security posture. Implement limited administrative roles user risk policies MFA for administrative roles and block legacy authentic...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score) | [Active Directory security best practices](https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory) | [Azure AD security baseline](https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline) | [Privileged Identity Management](https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),Use Azure AD security baseline and Identity Secure Score to evaluate security posture. Implement limited administrative roles user risk policies MFA for administrative roles and block legacy authentication.,"Enhanced Implementation Context:
• Azure AD Identity Secure Score: https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score
• Active Directory security best practices: https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory
• Azure AD security baseline: https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline
• Privileged Identity Management: https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 5.4, 6.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8, SI-4
• PCI-DSS v3.2.1: 8.2, 8.3

Azure Policy Examples:
• No applicable built-in policy (requires configuration-based implementation)
• Use Azure AD Identity Secure Score recommendations
• Implement Azure AD security baseline configurations
• Monitor privileged account activities through Azure AD logs",ai_analysis,,Validated
roleAssignment.deploymentTemplate.json,IM-6,Identity Management,Use strong authentication controls,CRITICAL,13.0,"The template assigns the 'Owner' role to a principal without enforcing strong authentication controls such as Multi-Factor Authentication (MFA) or passwordless authentication. If the principalId is compromised and MFA is not enforced, attackers can immediately gain full administrative access, leading to total environment compromise. This configuration enables privilege escalation and initial access attack vectors.","Enforce MFA or passwordless authentication for all accounts assigned the 'Owner' role. Use Conditional Access policies in Azure AD to require strong authentication for privileged roles. Regularly review and audit privileged accounts to ensure compliance. Reference: Azure Security Benchmark v3.0 IM-6.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/authentication/howto-mfa-getstarted) | [Passwordless authentication options](https://docs.microsoft.com/azure/active-directory/authentication/concept-authentication-passwordless) | [Azure AD password policies](https://docs.microsoft.com/azure/active-directory/authentication/concept-sspr-policy#password-policies-that-only-apply-to-cloud-user-accounts) | [Azure AD Password Protection](https://docs.microsoft.com/azure/active-directory/authentication/concept-password-ban-bad) | [Block legacy authentication](https://docs.microsoft.com/azure/active-directory/conditional-access/block-legacy-authentication)

🔵 Azure Guidance: Use Azure AD passwordless authentication as default method (Windows Hello Microsoft Authenticator FIDO2 Keys). Enable Azure MFA for all users with conditional access policies. Block legacy authenticat...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/authentication/howto-mfa-getstarted) | [Passwordless authentication options](https://docs.microsoft.com/azure/active-directory/authentication/concept-authentication-passwordless) | [Azure AD password policies](https://docs.microsoft.com/azure/active-directory/authentication/concept-sspr-policy#password-policies-that-only-apply-to-cloud-user-accounts) | [Azure AD Password Protection](https://docs.microsoft.com/azure/active-directory/authentication/concept-password-ban-bad) | [Block legacy authentication](https://docs.microsoft.com/azure/active-directory/conditional-access/block-legacy-authentication),Use Azure AD passwordless authentication as default method (Windows Hello Microsoft Authenticator FIDO2 Keys). Enable Azure MFA for all users with conditional access policies. Block legacy authentication.,"Enhanced Implementation Context:
• Azure MFA deployment: https://docs.microsoft.com/azure/active-directory/authentication/howto-mfa-getstarted
• Passwordless authentication options: https://docs.microsoft.com/azure/active-directory/authentication/concept-authentication-passwordless
• Azure AD password policies: https://docs.microsoft.com/azure/active-directory/authentication/concept-sspr-policy#password-policies-that-only-apply-to-cloud-user-accounts
• Azure AD Password Protection: https://docs.microsoft.com/azure/active-directory/authentication/concept-password-ban-bad
• Block legacy authentication: https://docs.microsoft.com/azure/active-directory/conditional-access/block-legacy-authentication

Compliance Mappings:
• CIS Controls v8: 6.3, 6.4
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-5, IA-8
• PCI-DSS v3.2.1: 7.2, 8.2, 8.3, 8.4

Azure Policy Examples:
• Authentication to Linux machines should require SSH keys
• MFA should be enabled on accounts with write permissions on your subscription
• MFA should be enabled on accounts with owner permissions on your subscription
• MFA should be enabled on accounts with read permissions on your subscription",ai_analysis,,Validated
roleAssignment.deploymentParameters.json,IM-2,Identity Management,Protect identity and authentication systems,CRITICAL,6.0,"The 'builtInRoleType' parameter is set to 'Owner', which grants full administrative privileges to the assigned principal. Assigning the Owner role via deployment parameters enables an attacker with access to this file or deployment process to escalate privileges, gain unrestricted access to all resources in the subscription, and perform lateral movement or data exfiltration. The blast radius includes full control over all Azure resources, including deletion, modification, and access to sensitive data.","Restrict assignment of the 'Owner' role to only the most essential break-glass accounts. Use least privilege by assigning more restrictive roles (such as 'Contributor' or custom roles with limited permissions) whenever possible. Implement Privileged Identity Management (PIM) to require approval and just-in-time elevation for Owner assignments. Monitor and audit all role assignments for privileged roles. Reference: Azure Security Benchmark v3.0 IM-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score) | [Active Directory security best practices](https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory) | [Azure AD security baseline](https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline) | [Privileged Identity Management](https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Use Azure AD security baseline and Identity Secure Score to evaluate security posture. Implement limited administrative roles user risk policies MFA for administrative roles and block legacy authentic...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score) | [Active Directory security best practices](https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory) | [Azure AD security baseline](https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline) | [Privileged Identity Management](https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),Use Azure AD security baseline and Identity Secure Score to evaluate security posture. Implement limited administrative roles user risk policies MFA for administrative roles and block legacy authentication.,"Enhanced Implementation Context:
• Azure AD Identity Secure Score: https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score
• Active Directory security best practices: https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory
• Azure AD security baseline: https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline
• Privileged Identity Management: https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 5.4, 6.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8, SI-4
• PCI-DSS v3.2.1: 8.2, 8.3

Azure Policy Examples:
• No applicable built-in policy (requires configuration-based implementation)
• Use Azure AD Identity Secure Score recommendations
• Implement Azure AD security baseline configurations
• Monitor privileged account activities through Azure AD logs",ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 17,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 0,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-24T16:27:19.373737,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
