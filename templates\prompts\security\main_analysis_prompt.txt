=== ADVANCED THREAT ACTOR PERSPECTIVE ANALYSIS INSTRUCTIONS ===

STEP 1: MULTI-FRAMEWORK ADVERSARIAL ANALYSIS
Analyze this infrastructure from a sophisticated threat actor's perspective using multiple security frameworks:

🎯 MITRE ATT&CK INITIAL ACCESS (TA0001):
- T1190: Exploit Public-Facing Application (exposed endpoints, weak WAF, unpatched services)
- T1078: Valid Accounts (weak authentication, default credentials, credential stuffing)
- T1133: External Remote Services (VPN/RDP misconfigurations, weak access controls)

🔓 MITRE ATT&CK PRIVILEGE ESCALATION (TA0004):
- T1068: Exploitation for Privilege Escalation (service misconfigurations, container escapes)
- T1078.004: Cloud Accounts (excessive Azure RBAC permissions, service principal abuse)

🌐 MITRE ATT&CK LATERAL MOVEMENT (TA0008):
- T1021: Remote Services (network segmentation failures, trust relationships)
- T1550: Use Alternate Authentication Material (service principal tokens, certificates)

💾 MITRE ATT&CK EXFILTRATION (TA0010):
- T1041: Exfiltration Over C2 Channel (unencrypted data transfer, DNS tunneling)
- T1537: Transfer Data to Cloud Account (storage misconfigurations, public access)

🛡️ MITRE ATT&CK DEFENSE EVASION (TA0005):
- T1562: Impair Defenses (disable logging, modify security policies)
- T1070: Indicator Removal (clear logs, modify audit trails)

🔍 MITRE ATT&CK COLLECTION & DISCOVERY (TA0007/TA0009):
- T1083: File and Directory Discovery (enumerate storage, find sensitive data)
- T1057: Process Discovery (identify running services, find vulnerabilities)

STEP 2: BLAST RADIUS ASSESSMENT
For each potential vulnerability, assess the damage scope:
- How many resources could be compromised?
- What is the cascading failure potential?
- What sensitive data could be exposed?
- How would this impact business operations?

STEP 3: AI-ENHANCED CONTEXT EVALUATION (CRITICAL FOR FALSE POSITIVE REDUCTION)
Before flagging any security issue, perform comprehensive contextual analysis:
- **Semantic Variable Analysis**: Understand parameter usage patterns beyond naming conventions
- **Cross-Template Dependency Analysis**: Evaluate security implications across related templates
- **Conditional Logic Understanding**: Analyze if-then-else deployment scenarios and conditions
- **Environment Context Differentiation**: Distinguish dev/test vs production security requirements
- **Historical Pattern Recognition**: Apply machine learning insights from previous analyses
- **Value Analysis**: Examine actual values assigned to variables and their security implications
- **Infrastructure Context**: Consider the broader infrastructure pattern and attack surface
- **Deployment Worthiness**: Ensure findings justify development team investment (>70% confidence)

STEP 4: COMPREHENSIVE MULTI-FRAMEWORK SYSTEMATIC REVIEW
- **Line-by-Line Analysis**: Process template content systematically, resource by resource
- **Complete Control Coverage**: Check against ALL {total_controls} Azure Security Benchmark controls
- **Extended Framework Analysis**: Evaluate Container Security (CS-*), API Security (AS-*), DevOps Security (DS-*)
- **MITRE ATT&CK Mapping**: Map findings to specific tactics and techniques for threat context
- **Industry Standards Cross-Reference**: Validate against CIS, NIST, SOC 2, ISO 27001 requirements
- **Context-Aware Processing**: Apply AI-enhanced context analysis before making recommendations
- **Threat-Prioritized Findings**: Focus on configurations that enable attack vectors or increase blast radius

STEP 5: ENHANCED VIOLATION DETECTION WITH THREAT FOCUS
- A violation exists when template configuration contradicts a control requirement
- Focus on EXPLICIT configurations that enable attack vectors or increase blast radius
- Use the severity indicators provided with each control
- PRIORITIZE findings that:
  * Enable initial access (public exposure, weak authentication)
  * Allow privilege escalation (excessive permissions, admin access)
  * Enable lateral movement (network misconfigurations, firewall gaps)
  * Compromise data protection (encryption disabled, weak TLS)
  * Impair detection capabilities (logging disabled, monitoring off)
- AVOID false positives by considering:
  * Variables named "secret" that are boolean flags or configuration names
  * UI/display related configurations that aren't actual security issues
  * Reference patterns that point to configuration rather than sensitive data

STEP 6: THREAT-AWARE FINDING DOCUMENTATION
For each violation found, document:
- control_id: MUST be one of the EXACT control IDs from the VALID list above
- severity: Apply based on control's severity indicators and violation impact
- line: CRITICAL - Use the exact line number from "Line XXX:" format in the template content above
- description: Specific violation with reference to template content AND context analysis, include the actual configuration property name
- remediation: Precise steps to fix the violation with exact configuration changes needed

🎯 LINE NUMBER ACCURACY REQUIREMENTS:
- Template content above shows "Line 001:", "Line 002:", etc.
- Your "line" field must match these exact numbers
- Find the specific line containing the security violation
- Do NOT guess line numbers - use only what you can see in the numbered content

STEP 7: CONTROL ID ISOLATION AND VALIDATION (CRITICAL)
🚨 CONTROL ID ISOLATION RULES:
- Each control ID is ISOLATED and INDEPENDENT - DO NOT mix or combine control IDs
- Use EXACTLY ONE control ID per finding - never create hybrid or combined IDs
- DO NOT modify control IDs (e.g., don't change "NS-1" to "NS-1a" or "NS-1-modified")
- DO NOT create variations of control IDs (e.g., don't use "NS-1.1" if only "NS-1" exists)
- DO NOT combine multiple control IDs into one (e.g., don't use "NS-1+DP-2" or "NS-1,DP-2")

🔒 CONTROL ID VALIDATION WITH AZURE GUIDANCE:
- ONLY use control IDs from the VALID CONTROL IDs list above
- DO NOT create fictional control IDs like those in the FICTIONAL examples
- Each control includes official Azure Guidance from Microsoft's Security Benchmark CSV
- Use the Azure Guidance, Security Principles, and Implementation Context provided for each control
- Validate findings against the specific Azure Policies mentioned for each control
- If unsure which control applies, use the most relevant one from the VALID list
- Each control_id in your response MUST match exactly one from the VALID list above
- Before including any control_id, verify it exists in the VALID list

🎯 CONTROL ID SELECTION PROCESS:
1. Identify the security issue in the template
2. Review EACH control individually (don't mix them)
3. Find the ONE control that best matches the issue
4. Use that control's EXACT ID without modification
5. Apply that control's specific Azure Guidance
6. Create ONE finding per control ID

STEP 8: AZURE GUIDANCE VALIDATION CHECKLIST
For each potential finding, validate against the Azure Guidance provided:
- Does the template configuration violate the specific Azure Guidance for this control?
- Is the Security Principle from the CSV being followed or violated?
- Does the Implementation Context support flagging this as a violation?
- Are the Related Azure Policies mentioned in the control relevant to this finding?
- Would the Customer Security Stakeholders listed for this control consider this a real issue?

STEP 9: FALSE POSITIVE PREVENTION CHECKLIST
Before flagging any issue, verify:
- Is this variable actually used for sensitive data or just named with security keywords?
- Does the variable value indicate configuration (true/false, names, types) rather than secrets?
- Is this a UI/display setting rather than a security control?
- Are you looking at the actual security risk or just a naming pattern?
- Does the context analysis above indicate this is likely a false positive?
- Does the Azure Guidance from the CSV support this as a legitimate security concern?

STEP 10: CONSISTENCY REQUIREMENTS
- Same template + same controls = identical findings
- Process controls in provided order
- Use deterministic severity assignment based on actual security impact
- Reference exact line numbers and configuration values
- Apply context analysis consistently across all variables
- Apply Azure Guidance consistently for the same control across different templates

STEP 11: PRE-SUBMISSION VALIDATION
Before submitting your response:
1. Check each control_id against the VALID CONTROL IDs list above
2. Ensure no control_id matches the FICTIONAL examples
3. Verify all control_ids are from the {total_controls} controls provided
4. Confirm each finding represents a real security issue, not a false positive
5. Validate that context analysis was considered for each recommendation
6. Ensure Azure Guidance from CSV was properly applied for validation
7. Verify that findings align with the Security Principles and Implementation Context

STEP 12: ENHANCED OUTPUT FORMAT
Return ONLY this comprehensive JSON structure:
{{{{
  "findings": [
    {{{{
      "control_id": "MUST_BE_FROM_VALID_LIST_ABOVE",
      "severity": "CRITICAL|HIGH|MEDIUM|LOW",
      "line": LINE_NUMBER,
      "description": "Specific violation description with template reference and context justification",
      "remediation": "Actionable fix with specific configuration changes and code examples",
      "confidence_score": 85,
      "exploitation_complexity": "Low|Medium|High|Very High",
      "remediation_effort_hours": "1-4|4-8|8-16|16+",
      "mitre_attack_techniques": ["T1190", "T1078"],
      "compliance_frameworks": ["CIS", "NIST", "SOC2", "ISO27001"],
      "azure_policy_definitions": ["Policy Name 1", "Policy Name 2"],
      "compensating_controls": "Alternative security measures if direct fix not feasible"
    }}}}
  ],
  "analysis_metadata": {{{{
    "total_controls_evaluated": {total_controls},
    "framework_coverage": ["ASB", "MITRE", "CIS", "NIST"],
    "analysis_confidence": 92,
    "false_positive_risk": "Low|Medium|High"
  }}}}
}}}}

If no violations found, return: {{{{"findings": [], "analysis_metadata": {{"total_controls_evaluated": {total_controls}, "framework_coverage": ["ASB", "MITRE", "CIS", "NIST"], "analysis_confidence": 95, "false_positive_risk": "Low"}}}}}}

🚨 CRITICAL REMINDERS:
- Any control_id not in the VALID CONTROL IDs list will be considered a hallucination and corrected
- Variables named with security keywords are NOT automatically security issues
- Always consider the context analysis provided above
- Focus on actual security risks, not naming conventions
- Use the Azure Guidance from CSV to validate that findings are legitimate security concerns
- Apply the Security Principles and Implementation Context from Microsoft's official guidance
- Consider the Customer Security Stakeholders who would be responsible for addressing each finding

🔒 CONTROL ID ISOLATION ENFORCEMENT:
- NEVER modify control IDs (e.g., don't change "NS-1" to "NS-1a", "NS-1.1", or "NS-1-ext")
- NEVER combine control IDs (e.g., don't use "NS-1+DP-2", "NS-1,DP-2", or "NS-1/DP-2")
- NEVER create variations (e.g., don't use "NS1", "NS_1", or "NS.1" instead of "NS-1")
- Use EXACTLY the control ID as shown in the VALID list above
- Each finding must reference exactly ONE control ID
- If multiple controls apply, create separate findings for each control
