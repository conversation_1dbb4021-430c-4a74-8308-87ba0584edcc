#!/usr/bin/env python3
"""
Debug script to examine the CSV data structure and understand why URLs are not being extracted.
"""

import sys
import pandas as pd
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from security_opt import SecurityPRReviewer
    print("✅ Successfully imported SecurityPRReviewer")
except ImportError as e:
    print(f"❌ Failed to import SecurityPRReviewer: {e}")
    sys.exit(1)

def debug_csv_data():
    """Debug the CSV data structure to understand the issue."""
    print("\n🔍 DEBUGGING CSV DATA STRUCTURE")
    print("=" * 60)
    
    # Read the enhanced CSV directly
    csv_file = Path("SecurityBenchmarks/network_security_with_urls.csv")
    if not csv_file.exists():
        print(f"❌ CSV file not found: {csv_file}")
        return False
    
    print(f"📄 Reading CSV file: {csv_file}")
    
    # Read with pandas
    df = pd.read_csv(csv_file)
    print(f"📊 CSV shape: {df.shape}")
    print(f"📋 Columns: {list(df.columns)}")
    
    # Check first few rows
    print(f"\n📋 First 3 rows:")
    for i, row in df.head(3).iterrows():
        print(f"\nRow {i+1}:")
        for col, val in row.items():
            print(f"  {col}: {repr(val)}")
    
    # Check specifically for URLs in the implementation context
    print(f"\n🔗 Checking for URLs in 'Implementation and additional context' column:")
    
    import re
    url_pattern = r'https?://[^\s\n\r]+'
    
    for i, row in df.iterrows():
        control_id = row.get("ASB ID", f"Row {i+1}")
        context = row.get("Implementation and additional context", "")
        
        if pd.isna(context):
            context = ""
        
        urls = re.findall(url_pattern, str(context))
        print(f"  {control_id}: {len(urls)} URLs found")
        if urls:
            for j, url in enumerate(urls, 1):
                print(f"    {j}. {url}")
        
        # Show raw context for first control
        if i == 0:
            print(f"    Raw context: {repr(context)}")
    
    return True

def debug_reviewer_data_loading():
    """Debug how the reviewer loads and processes the CSV data."""
    print("\n🔍 DEBUGGING REVIEWER DATA LOADING")
    print("=" * 60)
    
    try:
        # Create reviewer instance
        reviewer = SecurityPRReviewer(local_folder=".")
        
        # Check benchmark data structure
        print(f"📊 Benchmark data keys: {list(reviewer.benchmark_data.keys()) if reviewer.benchmark_data else 'None'}")
        
        if reviewer.benchmark_data and "controls" in reviewer.benchmark_data:
            controls = reviewer.benchmark_data["controls"]
            print(f"📋 Number of controls: {len(controls)}")
            
            # Check first few controls
            for i, control in enumerate(controls[:3]):
                control_id = control.get("id", f"Control {i+1}")
                print(f"\nControl {control_id}:")
                for key, value in control.items():
                    if isinstance(value, str) and len(value) > 100:
                        print(f"  {key}: {repr(value[:100])}...")
                    else:
                        print(f"  {key}: {repr(value)}")
        
        # Test link extraction for NS-1
        print(f"\n🔗 Testing link extraction for NS-1:")
        links_info = reviewer._extract_control_links("NS-1")
        
        print(f"  Raw links: {links_info.get('raw_links', [])}")
        print(f"  Azure guidance: {repr(links_info.get('azure_guidance', ''))}")
        print(f"  Implementation context: {repr(links_info.get('implementation_context', ''))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during reviewer debugging: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run debugging tests."""
    print("🐛 CSV DATA STRUCTURE DEBUGGING")
    print("=" * 70)
    
    tests = [
        ("CSV Data Structure", debug_csv_data),
        ("Reviewer Data Loading", debug_reviewer_data_loading)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running {test_name} Debug...")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} debug COMPLETED")
            else:
                print(f"❌ {test_name} debug FAILED")
        except Exception as e:
            print(f"❌ {test_name} debug ERROR: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 DEBUG SUMMARY")
    print("=" * 70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ COMPLETE" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} debug tests completed")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
