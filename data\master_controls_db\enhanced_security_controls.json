{"metadata": {"version": "4.0", "source": "IaC Guardian Enhanced Security Framework", "created_date": "2025-06-20T00:00:00.000000", "frameworks_integrated": ["Azure Security Benchmark v3.0", "MITRE ATT&CK for Cloud", "Container Security (CS-*)", "API Security (AS-*)", "DevOps Security (DS-*)", "CIS Azure Foundations Benchmark", "NIST SP 800-53", "SOC 2 Type II", "ISO 27001"], "total_controls": 50, "ai_enhanced": true, "false_positive_reduction": true, "confidence_scoring": true}, "azure_security_benchmark": {"identity_management": [{"id": "IM-1", "name": "Use centralized identity and authentication system", "domain": "Identity Management", "severity": "HIGH", "confidence_baseline": 85, "exploitation_complexity": "Medium", "remediation_effort_hours": "4-8", "mitre_attack_techniques": ["T1078", "T1133"], "compliance_frameworks": {"CIS": "6.7, 12.5", "NIST": "AC-2, AC-3, IA-2, IA-8", "SOC2": "CC6.1, CC6.2", "ISO27001": "A.9.2.1, A.9.2.2"}, "azure_policy_definitions": ["RequireAADAuth", "StandardizeIdentityProvider"], "false_positive_patterns": ["display_name variables", "ui_configuration flags", "tenant_id references"], "deployment_worthiness_threshold": 75}], "network_security": [{"id": "NS-1", "name": "Establish network segmentation boundaries", "domain": "Network Security", "severity": "HIGH", "confidence_baseline": 90, "exploitation_complexity": "Low", "remediation_effort_hours": "1-4", "mitre_attack_techniques": ["T1021", "T1190"], "compliance_frameworks": {"CIS": "3.12, 13.4, 4.4", "NIST": "AC-4, SC-2, SC-7", "SOC2": "CC6.1", "ISO27001": "A.13.1.3"}, "azure_policy_definitions": ["RequireNSG", "DenyDefaultInbound", "AdaptiveNetworkHardening"], "false_positive_patterns": ["development_environment flags", "testing_configuration variables", "display_network_name settings"], "deployment_worthiness_threshold": 80}], "data_protection": [{"id": "DP-1", "name": "Discover classify and label sensitive data", "domain": "Data Protection", "severity": "HIGH", "confidence_baseline": 70, "exploitation_complexity": "Medium", "remediation_effort_hours": "8-16", "mitre_attack_techniques": ["T1083", "T1005"], "compliance_frameworks": {"CIS": "3.2, 3.7, 3.13", "NIST": "RA-2, SC-28", "SOC2": "CC6.7", "ISO27001": "A.8.2.1, A.8.2.2"}, "azure_policy_definitions": ["ImplementDataClassification", "DeployPurview", "EnableSensitivityLabels"], "false_positive_patterns": ["data_source_name variables", "table_display_name settings", "schema_configuration flags"], "deployment_worthiness_threshold": 70}]}, "extended_security_frameworks": {"container_security": [{"id": "CS-1", "name": "Container Image Security", "domain": "Container Security", "severity": "HIGH", "confidence_baseline": 85, "exploitation_complexity": "Medium", "remediation_effort_hours": "4-8", "mitre_attack_techniques": ["T1610", "T1611"], "compliance_frameworks": {"CIS": "5.1, 5.2", "NIST": "SI-3, SI-7", "SOC2": "CC6.8", "ISO27001": "A.12.2.1"}, "azure_policy_definitions": ["RequireImageScanning", "UseTrustedRegistry", "EnforceImageSigning"], "false_positive_patterns": ["image_display_name variables", "container_ui_settings", "registry_configuration_names"], "deployment_worthiness_threshold": 80}], "api_security": [{"id": "AS-1", "name": "API Authentication and Authorization", "domain": "API Security", "severity": "CRITICAL", "confidence_baseline": 95, "exploitation_complexity": "Low", "remediation_effort_hours": "1-4", "mitre_attack_techniques": ["T1190", "T1078"], "compliance_frameworks": {"CIS": "6.1, 6.2", "NIST": "AC-2, AC-3, IA-2", "SOC2": "CC6.1, CC6.2", "ISO27001": "A.9.1.1, A.9.1.2"}, "azure_policy_definitions": ["RequireAPIAuthentication", "EnforceRateLimiting", "ImplementOAuth"], "false_positive_patterns": ["api_display_name variables", "endpoint_description settings", "swagger_ui_configuration"], "deployment_worthiness_threshold": 90}], "devops_security": [{"id": "DS-1", "name": "Secure CI/CD Pipelines", "domain": "DevOps Security", "severity": "HIGH", "confidence_baseline": 80, "exploitation_complexity": "Medium", "remediation_effort_hours": "8-16", "mitre_attack_techniques": ["T1195", "T1552"], "compliance_frameworks": {"CIS": "16.9, 16.12", "NIST": "SA-3, SA-4", "SOC2": "CC8.1", "ISO27001": "A.14.2.1, A.14.2.5"}, "azure_policy_definitions": ["RequireCredentialScanning", "EnforceSecurePipelines", "ImplementCodeSigning"], "false_positive_patterns": ["pipeline_display_name variables", "build_configuration_names", "deployment_stage_labels"], "deployment_worthiness_threshold": 75}]}, "ai_enhancement_rules": {"confidence_scoring": {"high_confidence_indicators": ["explicit_security_misconfigurations", "public_exposure_risks", "authentication_bypasses", "encryption_disabled"], "medium_confidence_indicators": ["missing_security_controls", "weak_configurations", "incomplete_implementations"], "low_confidence_indicators": ["naming_convention_issues", "display_configuration_flags", "ui_related_settings"]}, "false_positive_reduction": {"semantic_analysis_patterns": ["variable_usage_context", "value_type_analysis", "configuration_vs_secret_distinction", "environment_specific_requirements"], "historical_correlation_weights": {"previous_false_positives": 0.3, "confirmed_vulnerabilities": 0.7, "context_similarity": 0.5}}, "deployment_worthiness": {"scoring_factors": ["security_impact_severity", "exploitation_likelihood", "remediation_complexity", "business_risk_level", "compliance_requirement"], "minimum_threshold": 70, "critical_override_threshold": 90}}}