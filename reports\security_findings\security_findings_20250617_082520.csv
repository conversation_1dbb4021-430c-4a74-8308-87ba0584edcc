Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P1-Identity-HIGH,Identity Management,HIGH,IM-1,template.json,44,App Service does not explicitly integrate with Azure Active Directory for identity management.,Enable Azure Active Directory authentication for the App Service to ensure secure identity management.,N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-2,template.json,44,App Service does not enforce Multi-Factor Authentication (MFA) for users and administrators.,Configure Azure Active Directory authentication with MFA enforcement for all users and administrators accessing the App Service.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-1,template.json,44,App Service is not protected by network security groups (NSGs) or Azure Firewall. 'ipSecurityRestrictions' allow all traffic ('Any').,Implement network security groups or Azure Firewall to restrict access to the App Service. Update 'ipSecurityRestrictions' to allow only trusted IPs.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,44,"App Service configuration allows public network access ('publicNetworkAccess': 'Enabled') and has 'ipSecurityRestrictions' with 'ipAddress': 'Any' and 'action': 'Allow', exposing the app to the public internet without restriction.",Restrict 'ipSecurityRestrictions' to only allow trusted IP ranges. Set 'publicNetworkAccess' to 'Disabled' or configure access restrictions to limit public exposure.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,44,"App Service SCM site ('scmIpSecurityRestrictions') is configured with 'ipAddress': 'Any' and 'action': 'Allow', exposing the SCM endpoint to the public internet.",Restrict 'scmIpSecurityRestrictions' to only allow trusted IP ranges. Do not use 'Any' for SCM endpoint access.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-3,template.json,44,App Service does not implement Network Security Groups (NSGs) to control inbound and outbound traffic. 'ipSecurityRestrictions' allow all traffic.,Apply NSGs to the subnet hosting the App Service or configure 'ipSecurityRestrictions' to limit access to trusted IP addresses.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,44,App Service is accessible via public endpoints and does not use private endpoints for secure access.,Configure a private endpoint for the App Service to restrict access to trusted networks only.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,44,App Service does not specify use of customer-managed keys or explicit encryption at rest settings. Default platform encryption may not meet all compliance requirements.,Configure App Service to use customer-managed keys for encryption at rest if required by your compliance standards.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,44,"App Service hostNameSslStates for 'onefuzz-daily-ui.azurewebsites.net' and 'onefuzz-daily-ui.scm.azurewebsites.net' have 'sslState' set to 'Disabled', allowing unencrypted HTTP connections. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates to enforce HTTPS. Ensure all endpoints require TLS 1.2+.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,120,"App Service configuration includes 'publishingUsername' in plain text within the template, risking sensitive information disclosure.",Remove 'publishingUsername' from the template and use Azure Key Vault references or secure parameters for sensitive information.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-6,template.json,44,App Service does not specify use of customer-managed keys (CMK) for encryption at rest.,Configure App Service to use customer-managed keys for data encryption at rest to enhance data security.,N/A,AI,Generic
P4-Access-HIGH,Access Management,HIGH,AM-1,template.json,44,"App Service 'ipSecurityRestrictions' and 'scmIpSecurityRestrictions' allow unrestricted access ('Any'), violating least privilege access principles.",Restrict 'ipSecurityRestrictions' and 'scmIpSecurityRestrictions' to only necessary IP addresses or ranges.,N/A,AI,Generic
