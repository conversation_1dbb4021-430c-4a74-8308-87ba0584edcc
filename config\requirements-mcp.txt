# MCP Server Requirements for IaC Guardian GPT
# Core MCP dependencies
mcp>=1.0.0

# Existing dependencies from security_opt.py
azure-devops>=7.1.0b4
azure-identity>=1.15.0
azure-mgmt-resource>=23.0.1
openai>=1.12.0
pandas>=2.0.0
openpyxl>=3.1.0
requests>=2.31.0
python-dotenv>=1.0.0
python-Levenshtein>=0.21.1

# Additional utilities (asyncio and pathlib are built-in for Python 3.8+)
typing-extensions>=4.8.0
