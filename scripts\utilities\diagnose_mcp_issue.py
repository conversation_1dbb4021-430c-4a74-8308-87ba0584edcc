#!/usr/bin/env python3
"""
Diagnose MCP integration issues with VS Code.
"""

import json
import os
import subprocess
import sys
from pathlib import Path

def check_vscode_settings():
    """Check VS Code settings for MCP configuration."""
    print("🔍 CHECKING VS CODE SETTINGS")
    print("=" * 40)
    
    # Check local .vscode/settings.json
    local_settings = Path(".vscode/settings.json")
    if local_settings.exists():
        try:
            with open(local_settings, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"📁 Local settings file exists: {local_settings}")
                print(f"📄 Content preview: {content[:100]}...")
                
                # Try to parse as JSON
                try:
                    settings = json.loads(content)
                    if "github.copilot.chat.experimental.mcp" in str(settings):
                        print("✅ MCP configuration found in local settings")
                    else:
                        print("❌ No MCP configuration in local settings")
                except json.JSONDecodeError as e:
                    print(f"❌ Invalid JSON in settings file: {e}")
        except Exception as e:
            print(f"❌ Error reading local settings: {e}")
    else:
        print("❌ No local .vscode/settings.json found")
    
    # Check global VS Code settings
    print("\n🌐 GLOBAL VS CODE SETTINGS")
    print("-" * 30)
    
    # Common VS Code settings locations
    settings_paths = [
        Path.home() / "AppData/Roaming/Code/User/settings.json",  # Windows
        Path.home() / ".config/Code/User/settings.json",         # Linux
        Path.home() / "Library/Application Support/Code/User/settings.json"  # macOS
    ]
    
    for settings_path in settings_paths:
        if settings_path.exists():
            print(f"📁 Found global settings: {settings_path}")
            try:
                with open(settings_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if "mcp" in content.lower():
                        print("✅ MCP configuration found in global settings")
                    else:
                        print("⚠️ No MCP configuration in global settings")
            except Exception as e:
                print(f"❌ Error reading global settings: {e}")
            break
    else:
        print("❌ No global VS Code settings found")

def check_mcp_server():
    """Check if MCP server can start properly."""
    print("\n🧪 TESTING MCP SERVER STARTUP")
    print("=" * 35)
    
    try:
        # Test import
        import mcp_server
        print("✅ MCP server module imports successfully")
        
        # Test if server can be created
        from mcp_server import server
        print("✅ MCP server object created")
        
        # Test tools listing
        tools = server._tool_handlers
        print(f"✅ Found {len(tools)} tools registered")
        
        return True
    except Exception as e:
        print(f"❌ MCP server test failed: {e}")
        return False

def check_vs_code_version():
    """Check VS Code version and extensions."""
    print("\n📋 VS CODE ENVIRONMENT")
    print("=" * 25)
    
    try:
        # Try to get VS Code version
        result = subprocess.run(['code', '--version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            print(f"📦 VS Code Version: {lines[0] if lines else 'Unknown'}")
        else:
            print("⚠️ Could not get VS Code version")
    except Exception as e:
        print(f"⚠️ VS Code version check failed: {e}")
    
    try:
        # Try to list extensions
        result = subprocess.run(['code', '--list-extensions'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            extensions = result.stdout.strip().split('\n')
            copilot_extensions = [ext for ext in extensions if 'copilot' in ext.lower()]
            if copilot_extensions:
                print("✅ GitHub Copilot extensions found:")
                for ext in copilot_extensions:
                    print(f"   - {ext}")
            else:
                print("❌ No GitHub Copilot extensions found")
        else:
            print("⚠️ Could not list VS Code extensions")
    except Exception as e:
        print(f"⚠️ Extension check failed: {e}")

def create_alternative_config():
    """Create alternative MCP configurations."""
    print("\n🔧 ALTERNATIVE CONFIGURATIONS")
    print("=" * 35)
    
    current_dir = Path.cwd().as_posix()
    python_exe = sys.executable
    
    # Configuration 1: Relative paths
    config1 = {
        "github.copilot.chat.experimental.mcp.enabled": True,
        "github.copilot.chat.experimental.mcp.servers": {
            "iac-guardian": {
                "command": "python",
                "args": ["-u", "mcp_server.py"],
                "cwd": current_dir,
                "env": {
                    "ENFORCE_DOMAIN_PRIORITY": "true",
                    "USE_OPTIMIZED_PROMPTS": "true",
                    "ANALYSIS_SEED": "42"
                }
            }
        }
    }
    
    # Configuration 2: Absolute paths
    config2 = {
        "github.copilot.chat.experimental.mcp.enabled": True,
        "github.copilot.chat.experimental.mcp.servers": {
            "iac-guardian": {
                "command": python_exe,
                "args": ["-u", str(Path(current_dir) / "mcp_server.py")],
                "env": {
                    "ENFORCE_DOMAIN_PRIORITY": "true",
                    "USE_OPTIMIZED_PROMPTS": "true",
                    "ANALYSIS_SEED": "42"
                }
            }
        }
    }
    
    # Configuration 3: Simplified
    config3 = {
        "github.copilot.chat.experimental.mcp.enabled": True,
        "github.copilot.chat.experimental.mcp.servers": {
            "iac-guardian": {
                "command": "python",
                "args": ["mcp_server.py"],
                "cwd": current_dir
            }
        }
    }
    
    print("📋 Try these configurations in VS Code settings.json:")
    print("\n🔧 Configuration 1 (Recommended):")
    print("```json")
    print(json.dumps(config1, indent=2))
    print("```")
    
    print("\n🔧 Configuration 2 (Absolute paths):")
    print("```json")
    print(json.dumps(config2, indent=2))
    print("```")
    
    print("\n🔧 Configuration 3 (Minimal):")
    print("```json")
    print(json.dumps(config3, indent=2))
    print("```")

def test_stdio_communication():
    """Test if MCP server responds to stdio communication."""
    print("\n📡 TESTING STDIO COMMUNICATION")
    print("=" * 35)
    
    try:
        # Test if we can start the server process
        import subprocess
        import json
        
        # Start the MCP server
        process = subprocess.Popen(
            [sys.executable, "-u", "mcp_server.py"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=Path.cwd()
        )
        
        # Send initialize request
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        # Send the request
        request_str = json.dumps(init_request) + "\n"
        process.stdin.write(request_str)
        process.stdin.flush()
        
        # Wait for response (with timeout)
        try:
            stdout, stderr = process.communicate(timeout=10)
            if stdout:
                print("✅ MCP server responded to stdio communication")
                print(f"📤 Response: {stdout[:200]}...")
            else:
                print("❌ No response from MCP server")
                if stderr:
                    print(f"📤 Error: {stderr}")
        except subprocess.TimeoutExpired:
            print("⚠️ MCP server response timeout")
            process.kill()
        
    except Exception as e:
        print(f"❌ Stdio communication test failed: {e}")

def main():
    """Run all diagnostics."""
    print("🔍 IaC Guardian MCP Server - Diagnostic Tool")
    print("=" * 55)
    
    # Run all checks
    check_vscode_settings()
    check_mcp_server()
    check_vs_code_version()
    test_stdio_communication()
    create_alternative_config()
    
    print("\n" + "=" * 55)
    print("📋 DIAGNOSIS SUMMARY")
    print("=" * 55)
    
    print("\n🎯 LIKELY ISSUE:")
    print("VS Code is trying to connect via HTTP (port 8123) instead of stdio.")
    print("This suggests the MCP configuration isn't being read correctly.")
    
    print("\n🔧 SOLUTIONS TO TRY:")
    print("1. Manually add MCP configuration to VS Code settings.json")
    print("2. Restart VS Code completely after configuration")
    print("3. Check VS Code Developer Tools for errors")
    print("4. Update GitHub Copilot extension")
    print("5. Try the alternative configurations shown above")
    
    print("\n💡 IMMEDIATE WORKAROUND:")
    print("Use the security analysis tools directly:")
    print("   python security_opt.py --local-folder ./templates --export-format html")
    print("   python test_mcp_simple.py")
    
    print("\n📚 For detailed help: MCP_TROUBLESHOOTING.md")

if __name__ == "__main__":
    main()
