{"test_configuration": {"template_system": {"validation": {"enabled": true, "strict_mode": false, "validate_variables": true, "validate_security": true, "forbidden_patterns": ["eval\\(", "exec\\(", "__import__", "subprocess", "os\\.system"], "max_template_size_mb": 5, "required_placeholders": {"html": ["title", "content"], "prompts": ["numbered_content", "controls_section"], "context": ["file_path", "resource_types"]}}, "performance": {"monitoring_enabled": true, "slow_load_threshold_ms": 100, "cache_enabled": true, "cache_ttl_seconds": 300, "max_cache_size": 50, "log_slow_loads": true, "metrics_file": "test_reports/test_metrics.json"}, "hot_reloading": {"enabled": false, "watch_directories": ["templates"], "watch_extensions": [".html", ".css", ".js", ".txt"], "debounce_ms": 100, "auto_reload_on_change": false}, "versioning": {"enabled": true, "version_header": "<!-- Template Version: {version} -->", "compatibility_check": true, "min_supported_version": "1.0.0", "version_file": "VERSION"}, "security": {"sanitize_variables": true, "escape_html": true, "validate_file_paths": true, "allowed_template_dirs": ["templates"], "max_include_depth": 3}}}, "test_data": {"valid_templates": {"html": {"basic": "<!DOCTYPE html><html><head><title>{title}</title></head><body><h1>{heading}</h1></body></html>", "with_css": "<!DOCTYPE html><html><head><meta charset=\"UTF-8\"><title>{title}</title><style>{styles}</style></head><body>{content}</body></html>", "with_js": "<!DOCTYPE html><html><head><title>{title}</title></head><body><script>{script}</script></body></html>"}, "css": {"basic": ".container { background: {bg_color}; color: {text_color}; }", "responsive": "@media (max-width: 768px) { .container { padding: {mobile_padding}; } }", "glass_ui": ".glass { background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); }"}, "js": {"basic": "function init() { console.log('{message}'); }", "with_vars": "const config = { apiUrl: '{api_url}', debug: {debug_mode} };", "event_handler": "document.addEventListener('DOMContentLoaded', function() { init('{app_name}'); });"}, "prompts": {"system_role": "You are a {role}. Your expertise includes {expertise}. Analyze the following: {content}", "analysis": "Perform {analysis_type} analysis on: {target}. Focus on: {focus_areas}. Return: {output_format}", "context": "File: {file_path}\\nType: {file_type}\\nResources: {resource_list}\\nControls: {control_ids}"}}, "invalid_templates": {"html": {"missing_doctype": "<html><head><title>Test</title></head><body></body></html>", "unclosed_tags": "<html><head><title>Test</head><body><h1>Header<p>Content</body></html>", "malformed": "<html><head><title>Test</title><body><h1>Test</h1></body>"}, "css": {"mismatched_braces": ".container { background: red; .inner { color: blue; }", "invalid_syntax": ".container { background: ; color: invalid-color; }"}, "js": {"syntax_error": "function test() { console.log('test'; }", "mismatched_parens": "function test() { if (true { return false; }", "security_issue": "eval('malicious code'); exec('dangerous command');"}}, "template_variables": {"common": {"title": "IaC Guardian Test Report", "heading": "Security Analysis Results", "content": "Test content for template validation", "timestamp": "2024-01-01T00:00:00Z", "version": "1.0.0"}, "security_analysis": {"file_path": "/test/template.json", "resource_types": "azurerm_storage_account, azurerm_virtual_network", "numbered_content": "1: resource \"azurerm_storage_account\" \"test\" {\\n2:   name = \"teststorage\"\\n3: }", "controls_section": "NS-1: Network Security\\nDP-1: Data Protection\\nIM-1: Identity Management", "total_controls": 27, "analysis_type": "security", "output_format": "JSON"}, "styling": {"primary_color": "#667eea", "secondary_color": "#764ba2", "text_color": "#333333", "bg_color": "#ffffff", "accent_color": "#f093fb"}}}, "test_scenarios": {"performance_tests": {"load_time_thresholds": {"small_template_ms": 10, "medium_template_ms": 50, "large_template_ms": 200}, "cache_hit_rate_target": 80, "concurrent_load_count": 10}, "validation_tests": {"security_patterns_to_detect": ["eval(", "exec(", "document.write(", "innerHTML =", "outerHTML =", "javascript:", "vbscript:", "onload=", "onerror="], "required_html_elements": ["<!DOCTYPE html>", "<meta charset=", "<title>", "</html>"]}, "integration_tests": {"complete_report_generation": {"template_count": 5, "variable_substitution_count": 15, "embedded_css_lines": 100, "embedded_js_lines": 50}, "security_analysis_workflow": {"prompt_templates": 3, "context_variables": 8, "analysis_controls": 27}}}, "expected_results": {"validation": {"valid_html_templates": 3, "valid_css_templates": 3, "valid_js_templates": 3, "valid_prompt_templates": 3, "invalid_templates_detected": 7, "security_issues_detected": 2}, "performance": {"cache_hit_rate_min": 75, "avg_load_time_max_ms": 100, "memory_usage_max_mb": 50}, "integration": {"complete_html_report_size_min_kb": 10, "complete_html_report_size_max_kb": 500, "template_variable_substitutions": 15, "embedded_resources_count": 2}}, "test_environment": {"temp_directory_prefix": "iac_guardian_test_", "cleanup_after_tests": true, "preserve_test_artifacts": false, "log_level": "INFO", "test_timeout_seconds": 30}}