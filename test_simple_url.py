#!/usr/bin/env python3
"""
Simple test to check URL extraction.
"""

import re

def test_url_extraction():
    print("🔍 Testing URL extraction logic...")
    
    # Sample text from the CSV file
    sample_text = """Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3"""

    print(f"📄 Sample text length: {len(sample_text)} characters")
    
    # Test URL extraction pattern
    url_pattern = r'https?://[^\s\n\r]+'
    urls = re.findall(url_pattern, sample_text)
    
    print(f"📊 Found {len(urls)} URLs:")
    for i, url in enumerate(urls, 1):
        print(f"  {i}. {url}")
        
    # Test the specific pattern used in the code
    print(f"\n🔍 Testing with different patterns...")
    
    patterns = [
        r'https?://[^\s\n\r]+',
        r'https?://[^\s]+',
        r'https?://\S+',
        r'https?://[^\s\n\r\t]+'
    ]
    
    for i, pattern in enumerate(patterns, 1):
        urls = re.findall(pattern, sample_text)
        print(f"  Pattern {i}: {len(urls)} URLs found")

if __name__ == "__main__":
    test_url_extraction()
