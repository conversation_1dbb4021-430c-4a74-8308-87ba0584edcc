# Database Configuration
DATABASE_URL=postgresql://controls_user:controls_password@localhost:5432/controls_db
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
DATABASE_POOL_TIMEOUT=30

# Redis Configuration (for caching)
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0
CACHE_TTL=3600
ENABLE_CACHING=true

# Azure OpenAI Configuration (for AI processing)
AZURE_OPENAI_ENDPOINT=https://your-endpoint.openai.azure.com
AZURE_OPENAI_API_KEY=your-api-key-here
AZURE_OPENAI_DEPLOYMENT=gpt-4o
AZURE_OPENAI_API_VERSION=2024-02-01
AZURE_OPENAI_TEMPERATURE=0.1
AZURE_OPENAI_MAX_COMPLETION_TOKENS=2000  # Updated for Azure OpenAI O model compatibility

# Model type configuration (auto-detected from deployment name, but can be overridden)
# Supported values: gpt-4o, o1-preview, o1-mini, gpt-35-turbo, gpt-4
# AZURE_OPENAI_MODEL_TYPE=gpt-4o

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4
API_RELOAD=false
API_DEBUG=false

# Security
SECRET_KEY=your-secret-key-here-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# Features
ENABLE_AI_PROCESSING=true
ENABLE_CUSTOM_CONTROLS=true
ENABLE_ANALYTICS=true
ENABLE_AUDIT_LOGGING=true

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=logs/master_controls.log

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30

# Migration Settings
MIGRATION_BATCH_SIZE=1000
MIGRATION_PARALLEL_WORKERS=4
BACKUP_BEFORE_MIGRATION=true

# Legacy Integration
LEGACY_BENCHMARK_DIR=../SecurityBenchmarks
LEGACY_RESOURCE_MAPPINGS=../azure_resource_mappings.json
ENABLE_LEGACY_COMPATIBILITY=true

# Performance Tuning
MAX_CONTROLS_PER_REQUEST=50
DEFAULT_CONTROLS_LIMIT=15
QUERY_TIMEOUT=30
CONNECTION_TIMEOUT=10

# Development Settings
DEVELOPMENT_MODE=true
AUTO_RELOAD=true
DEBUG_SQL=false
ENABLE_CORS=true
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# Testing
TEST_DATABASE_URL=postgresql://test_user:test_password@localhost:5432/test_controls_db
TEST_REDIS_URL=redis://localhost:6379/1
PYTEST_TIMEOUT=300

# Deployment
ENVIRONMENT=development
VERSION=1.0.0
BUILD_NUMBER=
DEPLOYMENT_DATE=
