#!/usr/bin/env python3
"""
Test script to generate Glass UI HTML security reports with the new color palette.
Demonstrates the updated glassmorphism design with blue-teal theme.
"""

import os
import sys
from pathlib import Path
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from security_opt import SecurityPRReviewer

def create_sample_findings():
    """Create sample security findings for testing the new Glass UI color palette."""
    return [
        {
            "control_id": "IM-1",
            "severity": "CRITICAL",
            "file_path": "identity.bicep",
            "line": 45,
            "description": "Azure Key Vault access policy allows overly broad permissions. The current configuration grants 'all' permissions to service principals, which violates the principle of least privilege and creates significant security risks.",
            "remediation": "Restrict Key Vault access policies to specific required permissions only. Use role-based access control (RBAC) instead of access policies where possible. Implement just-in-time access for administrative operations.",
            "domain": "Identity Management"
        },
        {
            "control_id": "NS-2",
            "severity": "CRITICAL",
            "file_path": "network-security.bicep",
            "line": 23,
            "description": "Network Security Group allows inbound traffic from any source (0.0.0.0/0) on port 22 (SSH). This creates a critical security vulnerability by exposing SSH access to the entire internet.",
            "remediation": "Restrict SSH access to specific IP ranges or use Azure Bastion for secure remote access. Implement network segmentation and consider using just-in-time (JIT) access for administrative connections.",
            "domain": "Network Security"
        },
        {
            "control_id": "DP-3",
            "severity": "HIGH",
            "file_path": "data-storage.bicep",
            "line": 67,
            "description": "Storage account is configured without encryption at rest using customer-managed keys. Data is encrypted with Microsoft-managed keys only, which may not meet strict compliance requirements.",
            "remediation": "Configure customer-managed encryption keys (CMK) using Azure Key Vault. Enable infrastructure encryption for additional security layer. Implement key rotation policies.",
            "domain": "Data Protection"
        },
        {
            "control_id": "AM-2",
            "severity": "HIGH",
            "file_path": "access-management.bicep",
            "line": 12,
            "description": "Role assignment grants excessive permissions at subscription scope. The 'Contributor' role is assigned when more specific roles would be appropriate for the intended use case.",
            "remediation": "Use principle of least privilege by assigning specific roles like 'Storage Blob Data Contributor' or 'Virtual Machine Contributor' instead of broad 'Contributor' role. Review and audit role assignments regularly.",
            "domain": "Access Management"
        },
        {
            "control_id": "LM-1",
            "severity": "MEDIUM",
            "file_path": "logging-monitoring.bicep",
            "line": 89,
            "description": "Diagnostic settings are not configured for critical Azure resources. This limits visibility into security events and compliance monitoring capabilities.",
            "remediation": "Enable diagnostic settings for all critical resources. Configure log forwarding to Azure Monitor, Log Analytics workspace, or SIEM solution. Set up appropriate retention policies.",
            "domain": "Logging and Monitoring"
        },
        {
            "control_id": "NS-4",
            "severity": "MEDIUM",
            "file_path": "firewall-config.bicep",
            "line": 156,
            "description": "Azure Firewall is not configured with threat intelligence-based filtering. This reduces the effectiveness of network-level threat detection and prevention.",
            "remediation": "Enable threat intelligence-based filtering on Azure Firewall. Configure appropriate threat intelligence feeds and alerting mechanisms. Regularly update threat intelligence sources.",
            "domain": "Network Security"
        },
        {
            "control_id": "DP-1",
            "severity": "LOW",
            "file_path": "database-security.bicep",
            "line": 34,
            "description": "Azure SQL Database is not configured with Advanced Threat Protection. This limits detection of suspicious database activities and potential security threats.",
            "remediation": "Enable Advanced Threat Protection for Azure SQL Database. Configure appropriate alerting and response procedures for detected threats. Implement database activity monitoring.",
            "domain": "Data Protection"
        },
        {
            "control_id": "IM-3",
            "severity": "LOW",
            "file_path": "identity-governance.bicep",
            "line": 78,
            "description": "Privileged Identity Management (PIM) is not configured for administrative roles. This reduces oversight and control over privileged access operations.",
            "remediation": "Configure Azure AD Privileged Identity Management for all administrative roles. Implement approval workflows and time-limited access for privileged operations.",
            "domain": "Identity Management"
        }
    ]

def test_new_color_palette():
    """Test the Glass UI HTML report with the new blue-teal color palette."""
    print("🎨 Testing Glass UI with New Blue-Teal Color Palette...")
    
    # Create test directory
    test_dir = Path("glass_ui_new_palette")
    test_dir.mkdir(exist_ok=True)
    
    # Create sample findings
    sample_findings = create_sample_findings()
    
    # Initialize reviewer (using local mode for testing)
    reviewer = SecurityPRReviewer(local_folder="./test")
    
    # Generate Glass UI HTML report with new palette
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    html_path = test_dir / f"glass_ui_blue_teal_report_{timestamp}.html"
    
    print(f"📄 Generating Glass UI report with new color palette...")
    print(f"🎯 Sample findings: {len(sample_findings)} across {len(set(f['domain'] for f in sample_findings))} domains")
    
    reviewer._export_findings_to_html(sample_findings, str(html_path))
    
    print(f"✅ Glass UI report with new palette generated successfully!")
    print(f"📍 Report location: {html_path.absolute()}")
    print(f"💾 File size: {html_path.stat().st_size:,} bytes")
    
    return html_path

def display_color_palette_info():
    """Display information about the new color palette."""
    print("\n🎨 New Glass UI Color Palette:")
    print("┌─────────────────────────┬─────────────────────────────────────┐")
    print("│ Color Category          │ HSL Values & Usage                  │")
    print("├─────────────────────────┼─────────────────────────────────────┤")
    print("│ 🎨 Primary (Blue)       │ hsl(223, 90%, 50-70%) - Main theme │")
    print("│ 🟢 Secondary (Teal)     │ hsl(178, 90%, 80%) - Accents       │")
    print("│ 🌑 Dark Grays           │ hsl(223, 90%, 5-10%) - Backgrounds │")
    print("│ ⚪ White Transparency   │ hsla(0, 0%, 100%, 0-0.5) - Glass   │")
    print("│ 🔴 Critical Severity    │ hsl(0, 84%, 60-80%) - Danger       │")
    print("│ 🟠 High Severity        │ hsl(38, 92%, 50-75%) - Warning     │")
    print("│ 🟡 Medium Severity      │ hsl(45, 93%, 47-75%) - Caution     │")
    print("│ 🔵 Low Severity         │ Teal secondary - Information        │")
    print("└─────────────────────────┴─────────────────────────────────────┘")

    print("\n📝 Updated Font Color Palette:")
    print("┌─────────────────────────┬─────────────────────────────────────┐")
    print("│ Text Type               │ Color & Usage                       │")
    print("├─────────────────────────┼─────────────────────────────────────┤")
    print("│ 📄 Primary Text         │ White (100%) - Main content        │")
    print("│ 🔤 Secondary Text       │ White (85%) - Supporting content    │")
    print("│ 💬 Muted Text           │ White (65%) - Subtle information    │")
    print("│ 🎯 Accent Text          │ Teal (85%) - Highlights & subtitles │")
    print("│ 🔗 Interactive Text     │ Blue (85%) - Links & buttons        │")
    print("│ 🎪 Hover Text           │ Teal (90%) - Hover states           │")
    print("│ 🌟 On Glass Text        │ White (95%) - Glass components      │")
    print("│ 🌙 On Dark Text         │ White (100%) - Dark backgrounds     │")
    print("└─────────────────────────┴─────────────────────────────────────┘")

    print("\n📍 Professional Severity-Specific Line Number Colors:")
    print("┌─────────────────────────┬─────────────────────────────────────┐")
    print("│ Severity Level          │ Line Number Theme & Styling         │")
    print("├─────────────────────────┼─────────────────────────────────────┤")
    print("│ 🔴 Critical            │ Bold red gradient with strong shadow │")
    print("│ 🟠 High                │ Vibrant orange with clear borders   │")
    print("│ 🟡 Medium              │ Bright yellow with subtle depth     │")
    print("│ 🔵 Low                 │ Elegant teal with gentle highlight  │")
    print("└─────────────────────────┴─────────────────────────────────────┘")

def display_glass_ui_features():
    """Display Glass UI features with the new palette."""
    print("\n✨ Glass UI Features with Code Dialog Integration:")
    print("   🎭 Sophisticated blue-teal glassmorphism design")
    print("   🌊 Smooth gradient backgrounds with HSL color harmony")
    print("   💎 Multi-level transparency effects (0% to 50%)")
    print("   🎨 Consistent hue-based color relationships")
    print("   📝 Optimized font colors for better readability")
    print("   🎯 Professional severity-specific line number highlighting:")
    print("      🔴 Critical: Bold red theme with enhanced shadows")
    print("      🟠 High: Vibrant orange theme with clear borders")
    print("      🟡 Medium: Bright yellow theme with subtle depth")
    print("      🔵 Low: Elegant teal theme with gentle highlighting")
    print("   📄 Interactive code snippet dialog with:")
    print("      👁️ 'View Code' button for each finding")
    print("      🔢 Line numbers with highlighted problematic line")
    print("      📋 Copy code functionality")
    print("      🎨 Glass UI modal design with backdrop blur")
    print("      📱 Fully responsive for mobile devices")
    print("      ⌨️ Keyboard navigation (ESC to close)")
    print("   🌟 Enhanced visual depth with backdrop blur")
    print("   📱 Responsive design optimized for all devices")
    print("   ⚡ Improved performance with CSS custom properties")
    print("   🎯 Accessibility-compliant contrast ratios")
    print("   🔄 Subtle hover effects without distractions")
    print("   🎪 Professional interactive elements")
    print("   💫 Text shadows for improved glass effect depth")
    print("   📍 Clickable line numbers with copy functionality")
    print("   🎨 Clean, distraction-free design for focus")

if __name__ == "__main__":
    try:
        # Display color palette information
        display_color_palette_info()
        
        # Generate Glass UI report with new palette
        report_path = test_new_color_palette()
        
        # Display Glass UI features
        display_glass_ui_features()
        
        print(f"\n🌐 Open in browser: file://{report_path.absolute()}")
        print("\n🔍 Test the Code Dialog and Line Number Features:")
        print("   1. Opening the HTML file in a modern browser")
        print("   2. Observing clean, distinct line number colors by severity:")
        print("      🔴 Critical findings - Bold red with strong shadows")
        print("      🟠 High findings - Vibrant orange with clear borders")
        print("      🟡 Medium findings - Bright yellow with subtle depth")
        print("      🔵 Low findings - Elegant teal with gentle highlighting")
        print("   3. Clicking 'View Code' buttons to open code snippet dialogs")
        print("   4. Viewing highlighted problematic lines in context")
        print("   5. Using 'Copy Code' button to copy snippets")
        print("   6. Testing keyboard navigation (ESC to close dialogs)")
        print("   7. Hovering over line numbers for subtle professional effects")
        print("   8. Clicking line numbers to copy file:line references")
        print("   9. Testing glass transparency effects")
        print("   10. Verifying responsive design behavior on mobile")
        print("   11. Enjoying distraction-free, professional presentation")
        
        # Optional: Open in default browser
        import webbrowser
        user_input = input("\n🚀 Would you like to open the Glass UI report in your browser? (y/n): ")
        if user_input.lower() in ['y', 'yes']:
            webbrowser.open(f"file://{report_path.absolute()}")
            print("🌐 Glass UI report with new palette opened in your browser!")
        
        print("\n🎉 Glass UI with new blue-teal palette test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error testing Glass UI with new palette: {str(e)}")
        import traceback
        traceback.print_exc()
