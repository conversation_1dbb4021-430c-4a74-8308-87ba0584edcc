#!/usr/bin/env python3
"""
Test script to verify multiple resource type detection in templates.
"""

import os
import json
import tempfile
import unittest
from pathlib import Path
from security_opt import SecurityPRReviewer


class TestMultipleResourceTypes(unittest.TestCase):
    """Test cases for multiple resource type detection"""

    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.test_path = Path(self.test_dir)
        
        # Set up minimal environment for SecurityPRReviewer
        os.environ["ENABLE_PARAMETER_EXPANSION"] = "false"  # Disable for simpler testing

    def tearDown(self):
        """Clean up test environment"""
        import shutil
        shutil.rmtree(self.test_dir, ignore_errors=True)

    def test_multiple_resource_types_in_arm_template(self):
        """Test detection of multiple resource types in a single ARM template"""
        
        # Create ARM template with multiple resource types
        arm_template = {
            "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
            "contentVersion": "*******",
            "parameters": {
                "storageAccountName": {
                    "type": "string"
                },
                "keyVaultName": {
                    "type": "string"
                }
            },
            "resources": [
                {
                    "type": "Microsoft.Storage/storageAccounts",
                    "apiVersion": "2021-04-01",
                    "name": "[parameters('storageAccountName')]",
                    "location": "[resourceGroup().location]",
                    "sku": {
                        "name": "Standard_LRS"
                    },
                    "kind": "StorageV2",
                    "properties": {
                        "enableHttpsTrafficOnly": False,  # Security issue
                        "minimumTlsVersion": "TLS1_0"     # Security issue
                    }
                },
                {
                    "type": "Microsoft.KeyVault/vaults",
                    "apiVersion": "2021-10-01",
                    "name": "[parameters('keyVaultName')]",
                    "location": "[resourceGroup().location]",
                    "properties": {
                        "enablePurgeProtection": False,   # Security issue
                        "enableSoftDelete": False,        # Security issue
                        "tenantId": "[subscription().tenantId]",
                        "sku": {
                            "family": "A",
                            "name": "standard"
                        }
                    }
                }
            ]
        }
        
        # Write template to file
        template_file = self.test_path / "multi-resource-template.json"
        template_file.write_text(json.dumps(arm_template, indent=2))
        
        # Initialize reviewer and analyze
        reviewer = SecurityPRReviewer(local_folder=str(self.test_path))
        
        # Test resource type detection
        content = template_file.read_text()
        detected_types = reviewer._determine_resource_type(str(template_file), content)
        
        print(f"Detected resource types: {detected_types}")
        
        # Should detect both Storage and KeyVault
        self.assertIsInstance(detected_types, list)
        self.assertIn("Storage", detected_types)
        self.assertIn("KeyVault", detected_types)
        self.assertGreaterEqual(len(detected_types), 2)

    def test_multiple_resource_types_analysis(self):
        """Test that security analysis covers all detected resource types"""
        
        # Create ARM template with multiple resource types
        arm_template = {
            "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
            "contentVersion": "*******",
            "resources": [
                {
                    "type": "Microsoft.Storage/storageAccounts",
                    "apiVersion": "2021-04-01",
                    "name": "teststorage",
                    "location": "eastus",
                    "properties": {
                        "enableHttpsTrafficOnly": False  # Should trigger Storage security pattern
                    }
                },
                {
                    "type": "Microsoft.KeyVault/vaults",
                    "apiVersion": "2021-10-01", 
                    "name": "testkeyvault",
                    "location": "eastus",
                    "properties": {
                        "enablePurgeProtection": False  # Should trigger KeyVault security pattern
                    }
                }
            ]
        }
        
        # Write template to file
        template_file = self.test_path / "multi-resource-template.json"
        template_file.write_text(json.dumps(arm_template, indent=2))
        
        # Initialize reviewer and analyze
        reviewer = SecurityPRReviewer(local_folder=str(self.test_path))
        
        # Analyze the folder
        files = reviewer.analyze_folder(str(self.test_path))
        
        # Verify file was processed and resource types detected
        self.assertEqual(len(files), 1)
        file_info = files[0]
        
        # Check that multiple resource types were detected
        resource_types = file_info.get("resource_types", [])
        print(f"File resource types: {resource_types}")
        
        self.assertIsInstance(resource_types, list)
        self.assertGreaterEqual(len(resource_types), 2)
        
        # Analyze for security issues
        findings = reviewer.analyze_files(files)
        
        # Should find security issues for both resource types
        storage_findings = [f for f in findings if f.get("control_id") == "DP-2" and "storage" in f.get("description", "").lower()]
        keyvault_findings = [f for f in findings if f.get("control_id") == "DP-1" and "vault" in f.get("description", "").lower()]
        
        print(f"Storage findings: {len(storage_findings)}")
        print(f"KeyVault findings: {len(keyvault_findings)}")
        
        # Should have findings for both resource types
        self.assertGreater(len(storage_findings), 0, "Should find Storage security issues")
        self.assertGreater(len(keyvault_findings), 0, "Should find KeyVault security issues")

    def test_bicep_multiple_resource_types(self):
        """Test detection of multiple resource types in Bicep template"""
        
        bicep_content = """
        param storageAccountName string = 'teststorage'
        param keyVaultName string = 'testkeyvault'
        param location string = resourceGroup().location

        resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {
          name: storageAccountName
          location: location
          sku: {
            name: 'Standard_LRS'
          }
          kind: 'StorageV2'
          properties: {
            enableHttpsTrafficOnly: false
            minimumTlsVersion: 'TLS1_0'
          }
        }

        resource keyVault 'Microsoft.KeyVault/vaults@2021-10-01' = {
          name: keyVaultName
          location: location
          properties: {
            enablePurgeProtection: false
            enableSoftDelete: false
            tenantId: subscription().tenantId
            sku: {
              family: 'A'
              name: 'standard'
            }
          }
        }
        """
        
        # Write Bicep template to file
        bicep_file = self.test_path / "multi-resource-template.bicep"
        bicep_file.write_text(bicep_content)
        
        # Initialize reviewer and test resource type detection
        reviewer = SecurityPRReviewer(local_folder=str(self.test_path))
        
        detected_types = reviewer._determine_resource_type(str(bicep_file), bicep_content)
        
        print(f"Bicep detected resource types: {detected_types}")
        
        # Should detect both Storage and KeyVault
        self.assertIsInstance(detected_types, list)
        self.assertIn("Storage", detected_types)
        self.assertIn("KeyVault", detected_types)

    def test_nested_resources_multiple_types(self):
        """Test detection of multiple resource types including nested resources"""
        
        arm_template = {
            "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
            "contentVersion": "*******",
            "resources": [
                {
                    "type": "Microsoft.Storage/storageAccounts",
                    "apiVersion": "2021-04-01",
                    "name": "teststorage",
                    "location": "eastus",
                    "properties": {
                        "enableHttpsTrafficOnly": False
                    },
                    "resources": [
                        {
                            "type": "blobServices",
                            "apiVersion": "2021-04-01",
                            "name": "default",
                            "properties": {
                                "deleteRetentionPolicy": {
                                    "enabled": False
                                }
                            }
                        }
                    ]
                },
                {
                    "type": "Microsoft.Network/virtualNetworks",
                    "apiVersion": "2021-02-01",
                    "name": "testvnet",
                    "location": "eastus",
                    "properties": {
                        "addressSpace": {
                            "addressPrefixes": ["10.0.0.0/16"]
                        }
                    }
                }
            ]
        }
        
        # Write template to file
        template_file = self.test_path / "nested-resources-template.json"
        template_file.write_text(json.dumps(arm_template, indent=2))
        
        # Initialize reviewer and test resource type detection
        reviewer = SecurityPRReviewer(local_folder=str(self.test_path))
        
        detected_types = reviewer._determine_resource_type(str(template_file), json.dumps(arm_template))
        
        print(f"Nested resources detected types: {detected_types}")
        
        # Should detect Storage and Network types
        self.assertIsInstance(detected_types, list)
        self.assertIn("Storage", detected_types)
        self.assertIn("Network", detected_types)


if __name__ == "__main__":
    # Run tests with verbose output
    unittest.main(verbosity=2)
