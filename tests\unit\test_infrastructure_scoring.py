#!/usr/bin/env python3
"""
Test script to verify enhanced scoring for infrastructure security controls (DDoS, WAF, UDR)
"""

import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_infrastructure_security_scoring():
    """Test that DDoS, WAF, and UDR recommendations get high deployment scores."""
    print("🔒 Testing Infrastructure Security Control Scoring")
    print("=" * 60)
    print("Verifying that DDoS Protection, WAF, and UDR recommendations get high scores")
    print()
    
    try:
        from security_opt import SecurityPRReviewer
        
        reviewer = SecurityPRReviewer(local_folder=".")
        
        # Test infrastructure security findings
        test_findings = [
            {
                "control_id": "NS-5",
                "severity": "HIGH",
                "description": "DDoS protection not enabled on virtual network - critical infrastructure vulnerability",
                "line": 10,
                "file": "network.tf"
            },
            {
                "control_id": "NS-6", 
                "severity": "HIGH",
                "description": "Web Application Firewall not enabled on Application Gateway - application layer vulnerability",
                "line": 25,
                "file": "appgateway.tf"
            },
            {
                "control_id": "NS-3",
                "severity": "MEDIUM",
                "description": "User defined routes missing for network segmentation - Azure Firewall deployment required",
                "line": 15,
                "file": "routing.tf"
            },
            {
                "control_id": "NS-7",
                "severity": "MEDIUM", 
                "description": "Azure Firewall Manager not used for centralized policy management",
                "line": 30,
                "file": "firewall.tf"
            },
            {
                "control_id": "IM-1",
                "severity": "HIGH",
                "description": "Multi-factor authentication not enabled for admin accounts",
                "line": 5,
                "file": "identity.tf"
            }
        ]
        
        file_info = {
            "path": "test_infrastructure.tf",
            "content": "# Test infrastructure file",
            "size": 1000
        }
        
        print("🧪 Testing deployment impact scoring for infrastructure controls:")
        print("-" * 60)
        
        results = []
        for finding in test_findings:
            try:
                assessment = reviewer._assess_deployment_impact(finding, file_info)
                
                score = assessment["enhanced_finding"]["deployment_impact_score"]
                is_worthy = assessment["deployment_worthy"]
                reasons = assessment["enhanced_finding"]["impact_reasons"]
                
                results.append({
                    "control_id": finding["control_id"],
                    "description": finding["description"][:60] + "...",
                    "score": score,
                    "deployment_worthy": is_worthy,
                    "reasons": reasons
                })
                
                status = "✅ DEPLOYMENT WORTHY" if is_worthy else "❌ FILTERED OUT"
                print(f"{status} {finding['control_id']}: {score} points")
                print(f"   📋 Description: {finding['description'][:80]}...")
                print(f"   🔍 Key reasons: {', '.join(reasons[:3])}")
                print()
                
            except Exception as e:
                print(f"❌ Error testing {finding['control_id']}: {e}")
                results.append({
                    "control_id": finding["control_id"],
                    "score": 0,
                    "deployment_worthy": False,
                    "error": str(e)
                })
        
        # Analysis
        print("=" * 60)
        print("📊 INFRASTRUCTURE SECURITY SCORING ANALYSIS")
        print("=" * 60)
        
        infrastructure_controls = ["NS-3", "NS-5", "NS-6", "NS-7"]
        infrastructure_results = [r for r in results if r["control_id"] in infrastructure_controls]
        
        worthy_infrastructure = [r for r in infrastructure_results if r.get("deployment_worthy", False)]
        total_infrastructure = len(infrastructure_results)
        
        print(f"Infrastructure controls tested: {total_infrastructure}")
        print(f"Infrastructure controls deployment-worthy: {len(worthy_infrastructure)}")
        
        if len(worthy_infrastructure) == total_infrastructure:
            print("🎉 SUCCESS: All infrastructure security controls are deployment-worthy!")
        else:
            print("⚠️ WARNING: Some infrastructure controls are being filtered out")
            
            filtered_out = [r for r in infrastructure_results if not r.get("deployment_worthy", False)]
            for result in filtered_out:
                print(f"   ❌ {result['control_id']}: {result.get('score', 0)} points (threshold: 80)")
        
        # Detailed scoring breakdown
        print(f"\n🔍 DETAILED SCORING BREAKDOWN:")
        print("-" * 40)
        
        for result in results:
            control_id = result["control_id"]
            score = result.get("score", 0)
            worthy = result.get("deployment_worthy", False)
            
            if control_id in infrastructure_controls:
                control_type = "🏗️ INFRASTRUCTURE"
            else:
                control_type = "🔐 IDENTITY"
            
            print(f"{control_type} {control_id}: {score} points ({'✅' if worthy else '❌'})")
        
        # Test specific patterns
        print(f"\n🔍 TESTING INFRASTRUCTURE SECURITY PATTERNS:")
        print("-" * 50)
        
        pattern_tests = [
            ("ddos protection not enabled", "DDoS Protection"),
            ("web application firewall not enabled", "WAF Protection"),
            ("user defined routes missing", "UDR Configuration"),
            ("azure firewall not deployed", "Azure Firewall"),
            ("firewall manager not used", "Centralized Management")
        ]
        
        for pattern, description in pattern_tests:
            test_finding = {
                "control_id": "NS-5",
                "severity": "HIGH", 
                "description": pattern,
                "line": 1,
                "file": "test.tf"
            }
            
            try:
                assessment = reviewer._assess_deployment_impact(test_finding, file_info)
                score = assessment["enhanced_finding"]["deployment_impact_score"]
                worthy = assessment["deployment_worthy"]
                
                status = "✅" if worthy else "❌"
                print(f"{status} {description}: {score} points")
                
            except Exception as e:
                print(f"❌ {description}: Error - {e}")
        
        return len(worthy_infrastructure) == total_infrastructure
        
    except Exception as e:
        print(f"❌ Error: {e}")
        logger.exception("Detailed error:")
        return False

def test_control_id_bonus_scoring():
    """Test that specific infrastructure control IDs get bonus scoring."""
    print(f"\n🎯 Testing Infrastructure Control ID Bonus Scoring")
    print("=" * 60)
    
    try:
        from security_opt import SecurityPRReviewer
        
        reviewer = SecurityPRReviewer(local_folder=".")
        
        # Test specific infrastructure control IDs
        infrastructure_controls = [
            ("NS-3", "Azure Firewall deployment"),
            ("NS-5", "DDoS Protection"),
            ("NS-6", "WAF deployment"), 
            ("NS-7", "Network security management"),
            ("NS-9", "Private connectivity")
        ]
        
        file_info = {"path": "test.tf", "content": "# Test", "size": 100}
        
        print("Testing control ID bonus scoring:")
        print("-" * 40)
        
        all_passed = True
        for control_id, description in infrastructure_controls:
            test_finding = {
                "control_id": control_id,
                "severity": "MEDIUM",  # Start with medium to test bonus effect
                "description": f"Infrastructure security issue: {description}",
                "line": 1,
                "file": "test.tf"
            }
            
            try:
                assessment = reviewer._assess_deployment_impact(test_finding, file_info)
                score = assessment["enhanced_finding"]["deployment_impact_score"]
                worthy = assessment["deployment_worthy"]
                reasons = assessment["enhanced_finding"]["impact_reasons"]
                
                # Check if infrastructure bonus was applied
                has_bonus = any("Critical infrastructure security control" in reason for reason in reasons)
                
                status = "✅" if worthy else "❌"
                bonus_status = "🎯" if has_bonus else "⚠️"
                
                print(f"{status} {bonus_status} {control_id}: {score} points")
                print(f"   📋 {description}")
                print(f"   🔍 Bonus applied: {'Yes' if has_bonus else 'No'}")
                
                if not worthy:
                    all_passed = False
                    print(f"   ❌ Below threshold (80 points)")
                
                print()
                
            except Exception as e:
                print(f"❌ {control_id}: Error - {e}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main test function."""
    print("🔒 INFRASTRUCTURE SECURITY SCORING VALIDATION")
    print("=" * 80)
    print("Testing enhanced scoring for DDoS Protection, WAF, and UDR controls")
    print("=" * 80)
    
    success = True
    
    # Test infrastructure security scoring
    if not test_infrastructure_security_scoring():
        print("❌ Infrastructure security scoring test failed")
        success = False
    
    # Test control ID bonus scoring
    if not test_control_id_bonus_scoring():
        print("❌ Control ID bonus scoring test failed")
        success = False
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("🔒 INFRASTRUCTURE SECURITY SCORING ENHANCED")
        print()
        print("✅ DDoS Protection recommendations will be deployment-worthy")
        print("✅ WAF recommendations will be deployment-worthy")
        print("✅ UDR recommendations will be deployment-worthy")
        print("✅ Azure Firewall recommendations will be deployment-worthy")
        print("✅ Infrastructure controls get priority scoring")
        print()
        print("Your next security analysis will include comprehensive infrastructure protection!")
    else:
        print("❌ TESTS FAILED - Infrastructure scoring needs adjustment")
        print("Please review the scoring logic and thresholds")

if __name__ == "__main__":
    main()
