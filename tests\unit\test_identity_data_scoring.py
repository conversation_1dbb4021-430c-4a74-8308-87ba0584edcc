#!/usr/bin/env python3
"""
Test script to verify enhanced scoring for Identity Management and Data Protection controls
"""

import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_identity_management_scoring():
    """Test that Identity Management recommendations get high deployment scores."""
    print("🔐 Testing Identity Management Control Scoring")
    print("=" * 60)
    print("Verifying that IM-* controls get high scores for deployment worthiness")
    print()
    
    try:
        from security_opt import SecurityPRReviewer
        
        reviewer = SecurityPRReviewer(local_folder=".")
        
        # Test Identity Management findings
        identity_findings = [
            {
                "control_id": "IM-1",
                "severity": "HIGH",
                "description": "Azure AD not configured for centralized identity management - multiple identity providers detected",
                "line": 10,
                "file": "identity.tf"
            },
            {
                "control_id": "IM-2", 
                "severity": "CRITICAL",
                "description": "Privileged roles unprotected - legacy authentication enabled for admin accounts",
                "line": 25,
                "file": "rbac.tf"
            },
            {
                "control_id": "IM-3",
                "severity": "HIGH",
                "description": "Managed identity not used - hardcoded credentials detected in application configuration",
                "line": 15,
                "file": "app.tf"
            },
            {
                "control_id": "IM-6",
                "severity": "CRITICAL", 
                "description": "MFA not enabled for administrative accounts - weak password policy detected",
                "line": 30,
                "file": "auth.tf"
            },
            {
                "control_id": "IM-8",
                "severity": "CRITICAL",
                "description": "Secrets in code detected - Key Vault not used for credential storage",
                "line": 5,
                "file": "config.tf"
            }
        ]
        
        file_info = {
            "path": "test_identity.tf",
            "content": "# Test identity file",
            "size": 1000
        }
        
        print("🧪 Testing deployment impact scoring for Identity Management controls:")
        print("-" * 60)
        
        results = []
        for finding in identity_findings:
            try:
                assessment = reviewer._assess_deployment_impact(finding, file_info)
                
                score = assessment["enhanced_finding"]["deployment_impact_score"]
                is_worthy = assessment["deployment_worthy"]
                reasons = assessment["enhanced_finding"]["impact_reasons"]
                
                results.append({
                    "control_id": finding["control_id"],
                    "description": finding["description"][:60] + "...",
                    "score": score,
                    "deployment_worthy": is_worthy,
                    "reasons": reasons
                })
                
                status = "✅ DEPLOYMENT WORTHY" if is_worthy else "❌ FILTERED OUT"
                print(f"{status} {finding['control_id']}: {score} points")
                print(f"   📋 Description: {finding['description'][:80]}...")
                print(f"   🔍 Key reasons: {', '.join(reasons[:3])}")
                print()
                
            except Exception as e:
                print(f"❌ Error testing {finding['control_id']}: {e}")
                results.append({
                    "control_id": finding["control_id"],
                    "score": 0,
                    "deployment_worthy": False,
                    "error": str(e)
                })
        
        # Analysis
        identity_controls = [r["control_id"] for r in results]
        worthy_identity = [r for r in results if r.get("deployment_worthy", False)]
        
        print("=" * 60)
        print("📊 IDENTITY MANAGEMENT SCORING ANALYSIS")
        print("=" * 60)
        print(f"Identity controls tested: {len(identity_controls)}")
        print(f"Identity controls deployment-worthy: {len(worthy_identity)}")
        
        if len(worthy_identity) == len(identity_controls):
            print("🎉 SUCCESS: All Identity Management controls are deployment-worthy!")
            return True
        else:
            print("⚠️ WARNING: Some Identity Management controls are being filtered out")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        logger.exception("Detailed error:")
        return False

def test_data_protection_scoring():
    """Test that Data Protection recommendations get high deployment scores."""
    print("\n🛡️ Testing Data Protection Control Scoring")
    print("=" * 60)
    print("Verifying that DP-* controls get high scores for deployment worthiness")
    print()
    
    try:
        from security_opt import SecurityPRReviewer
        
        reviewer = SecurityPRReviewer(local_folder=".")
        
        # Test Data Protection findings
        data_findings = [
            {
                "control_id": "DP-1",
                "severity": "MEDIUM",
                "description": "Data classification missing - sensitive data unlabeled in storage accounts",
                "line": 10,
                "file": "storage.tf"
            },
            {
                "control_id": "DP-3", 
                "severity": "HIGH",
                "description": "Encryption in transit disabled - HTTPS not enforced for web applications",
                "line": 25,
                "file": "webapp.tf"
            },
            {
                "control_id": "DP-4",
                "severity": "CRITICAL",
                "description": "Encryption at rest disabled - Transparent Data Encryption not enabled for SQL database",
                "line": 15,
                "file": "database.tf"
            },
            {
                "control_id": "DP-6",
                "severity": "HIGH", 
                "description": "Key Vault not secured - key rotation disabled and expiration not set",
                "line": 30,
                "file": "keyvault.tf"
            },
            {
                "control_id": "DP-8",
                "severity": "HIGH",
                "description": "Key Vault access unrestricted - network exposed and logging disabled",
                "line": 5,
                "file": "vault.tf"
            }
        ]
        
        file_info = {
            "path": "test_data.tf",
            "content": "# Test data protection file",
            "size": 1000
        }
        
        print("🧪 Testing deployment impact scoring for Data Protection controls:")
        print("-" * 60)
        
        results = []
        for finding in data_findings:
            try:
                assessment = reviewer._assess_deployment_impact(finding, file_info)
                
                score = assessment["enhanced_finding"]["deployment_impact_score"]
                is_worthy = assessment["deployment_worthy"]
                reasons = assessment["enhanced_finding"]["impact_reasons"]
                
                results.append({
                    "control_id": finding["control_id"],
                    "description": finding["description"][:60] + "...",
                    "score": score,
                    "deployment_worthy": is_worthy,
                    "reasons": reasons
                })
                
                status = "✅ DEPLOYMENT WORTHY" if is_worthy else "❌ FILTERED OUT"
                print(f"{status} {finding['control_id']}: {score} points")
                print(f"   📋 Description: {finding['description'][:80]}...")
                print(f"   🔍 Key reasons: {', '.join(reasons[:3])}")
                print()
                
            except Exception as e:
                print(f"❌ Error testing {finding['control_id']}: {e}")
                results.append({
                    "control_id": finding["control_id"],
                    "score": 0,
                    "deployment_worthy": False,
                    "error": str(e)
                })
        
        # Analysis
        data_controls = [r["control_id"] for r in results]
        worthy_data = [r for r in results if r.get("deployment_worthy", False)]
        
        print("=" * 60)
        print("📊 DATA PROTECTION SCORING ANALYSIS")
        print("=" * 60)
        print(f"Data Protection controls tested: {len(data_controls)}")
        print(f"Data Protection controls deployment-worthy: {len(worthy_data)}")
        
        if len(worthy_data) == len(data_controls):
            print("🎉 SUCCESS: All Data Protection controls are deployment-worthy!")
            return True
        else:
            print("⚠️ WARNING: Some Data Protection controls are being filtered out")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        logger.exception("Detailed error:")
        return False

def test_comprehensive_control_scoring():
    """Test comprehensive scoring across all control domains."""
    print("\n🔒 Testing Comprehensive Control Domain Scoring")
    print("=" * 60)
    
    try:
        from security_opt import SecurityPRReviewer
        
        reviewer = SecurityPRReviewer(local_folder=".")
        
        # Test representative controls from each domain
        comprehensive_findings = [
            # Identity Management
            {"control_id": "IM-6", "severity": "CRITICAL", "description": "MFA not enabled for admin accounts", "line": 1, "file": "test.tf"},
            {"control_id": "IM-8", "severity": "HIGH", "description": "Secrets in code - Key Vault not used", "line": 2, "file": "test.tf"},
            
            # Network Security  
            {"control_id": "NS-5", "severity": "HIGH", "description": "DDoS protection not enabled", "line": 3, "file": "test.tf"},
            {"control_id": "NS-6", "severity": "HIGH", "description": "WAF not enabled", "line": 4, "file": "test.tf"},
            
            # Data Protection
            {"control_id": "DP-3", "severity": "HIGH", "description": "Encryption in transit disabled", "line": 5, "file": "test.tf"},
            {"control_id": "DP-4", "severity": "CRITICAL", "description": "Encryption at rest disabled", "line": 6, "file": "test.tf"}
        ]
        
        file_info = {"path": "test.tf", "content": "# Test", "size": 100}
        
        print("Testing comprehensive control domain scoring:")
        print("-" * 60)
        
        domain_results = {"Identity": [], "Network": [], "Data": []}
        
        for finding in comprehensive_findings:
            try:
                assessment = reviewer._assess_deployment_impact(finding, file_info)
                score = assessment["enhanced_finding"]["deployment_impact_score"]
                worthy = assessment["deployment_worthy"]
                
                control_id = finding["control_id"]
                if control_id.startswith("IM-"):
                    domain = "Identity"
                elif control_id.startswith("NS-"):
                    domain = "Network"
                elif control_id.startswith("DP-"):
                    domain = "Data"
                else:
                    domain = "Other"
                
                domain_results[domain].append({
                    "control_id": control_id,
                    "score": score,
                    "worthy": worthy
                })
                
                status = "✅" if worthy else "❌"
                print(f"{status} {control_id} ({domain}): {score} points")
                
            except Exception as e:
                print(f"❌ {finding['control_id']}: Error - {e}")
        
        # Summary by domain
        print(f"\n📊 DOMAIN SCORING SUMMARY:")
        print("-" * 40)
        
        all_passed = True
        for domain, results in domain_results.items():
            if results:
                worthy_count = sum(1 for r in results if r["worthy"])
                total_count = len(results)
                avg_score = sum(r["score"] for r in results) / total_count
                
                status = "✅" if worthy_count == total_count else "❌"
                print(f"{status} {domain} Management: {worthy_count}/{total_count} deployment-worthy (avg: {avg_score:.0f} points)")
                
                if worthy_count != total_count:
                    all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main test function."""
    print("🔒 IDENTITY & DATA PROTECTION SCORING VALIDATION")
    print("=" * 80)
    print("Testing enhanced scoring for Identity Management and Data Protection controls")
    print("=" * 80)
    
    success = True
    
    # Test Identity Management scoring
    if not test_identity_management_scoring():
        print("❌ Identity Management scoring test failed")
        success = False
    
    # Test Data Protection scoring
    if not test_data_protection_scoring():
        print("❌ Data Protection scoring test failed")
        success = False
    
    # Test comprehensive scoring
    if not test_comprehensive_control_scoring():
        print("❌ Comprehensive control scoring test failed")
        success = False
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("🔒 COMPREHENSIVE SECURITY CONTROL SCORING ENHANCED")
        print()
        print("✅ Identity Management controls will be deployment-worthy")
        print("✅ Data Protection controls will be deployment-worthy")
        print("✅ Infrastructure controls will be deployment-worthy")
        print("✅ All critical security domains get priority scoring")
        print()
        print("Your next security analysis will include comprehensive coverage across:")
        print("   🔐 Identity Management (IM-1 through IM-9)")
        print("   🛡️ Data Protection (DP-1 through DP-8)")
        print("   🏗️ Network Security (NS-1 through NS-10)")
    else:
        print("❌ TESTS FAILED - Some control domains need scoring adjustment")
        print("Please review the scoring logic and patterns")

if __name__ == "__main__":
    main()
