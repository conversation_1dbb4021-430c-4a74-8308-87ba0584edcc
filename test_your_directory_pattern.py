#!/usr/bin/env python3
"""
Test script to verify parameter file matching for your specific directory structure
"""

import os
import sys
import logging
from pathlib import Path

# Add src/core to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'core'))

from template_parameter_expander import TemplateParameterExpander

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_your_directory_pattern():
    """Test parameter file matching with your specific file patterns"""
    
    # Create test directory
    test_dir = Path("test_your_pattern")
    test_dir.mkdir(exist_ok=True)
    
    # Create test files matching your exact patterns
    test_files = {
        # Template files (should be identified as ARM templates)
        "Grafana.deploymentTemplate.json": '''
{
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
    "contentVersion": "*******",
    "parameters": {
        "grafanaName": {
            "type": "string"
        }
    },
    "resources": [
        {
            "type": "Microsoft.Dashboard/grafana",
            "apiVersion": "2022-08-01",
            "name": "[parameters('grafanaName')]",
            "location": "[resourceGroup().location]"
        }
    ]
}''',
        
        "KustoScripts.template.json": '''
{
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
    "contentVersion": "*******",
    "parameters": {
        "clusterName": {
            "type": "string"
        }
    },
    "resources": [
        {
            "type": "Microsoft.Kusto/clusters",
            "apiVersion": "2022-02-01",
            "name": "[parameters('clusterName')]",
            "location": "[resourceGroup().location]"
        }
    ]
}''',
        
        "roleAssignment.deploymentTemplate.json": '''
{
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
    "contentVersion": "*******",
    "parameters": {
        "principalId": {
            "type": "string"
        }
    },
    "resources": [
        {
            "type": "Microsoft.Authorization/roleAssignments",
            "apiVersion": "2022-04-01",
            "name": "[guid(resourceGroup().id)]",
            "properties": {
                "principalId": "[parameters('principalId')]"
            }
        }
    ]
}''',
        
        # Parameter files (should be identified as ARM parameter files)
        "Grafana.deploymentParameters.json": '''
{
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#",
    "contentVersion": "*******",
    "parameters": {
        "grafanaName": {
            "value": "my-grafana-instance"
        }
    }
}''',
        
        "KustoScripts.parameters.json": '''
{
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#",
    "contentVersion": "*******",
    "parameters": {
        "clusterName": {
            "value": "my-kusto-cluster"
        }
    }
}''',
        
        "roleAssignment.deploymentParameters.json": '''
{
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#",
    "contentVersion": "*******",
    "parameters": {
        "principalId": {
            "value": "12345678-1234-1234-1234-123456789012"
        }
    }
}''',

        # Additional test cases for Template/Parameters pattern
        "webapp.deploymentTemplate.json": '''
{
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
    "contentVersion": "*******",
    "parameters": {
        "webAppName": {
            "type": "string"
        }
    },
    "resources": [
        {
            "type": "Microsoft.Web/sites",
            "apiVersion": "2022-03-01",
            "name": "[parameters('webAppName')]",
            "location": "[resourceGroup().location]"
        }
    ]
}''',

        "webapp.deploymentParameters.json": '''
{
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#",
    "contentVersion": "*******",
    "parameters": {
        "webAppName": {
            "value": "my-web-app"
        }
    }
}''',

        "storage.Template.json": '''
{
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
    "contentVersion": "*******",
    "parameters": {
        "storageAccountName": {
            "type": "string"
        }
    },
    "resources": [
        {
            "type": "Microsoft.Storage/storageAccounts",
            "apiVersion": "2022-09-01",
            "name": "[parameters('storageAccountName')]",
            "location": "[resourceGroup().location]"
        }
    ]
}''',

        "storage.Parameters.json": '''
{
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#",
    "contentVersion": "*******",
    "parameters": {
        "storageAccountName": {
            "value": "mystorageaccount"
        }
    }
}''',
    }
    
    # Write test files
    for filename, content in test_files.items():
        (test_dir / filename).write_text(content)
    
    # Initialize expander
    expander = TemplateParameterExpander()
    
    print("🧪 Testing Your Directory Pattern")
    print("=" * 60)
    
    # Test file type identification
    print("\n📋 File Type Identification:")
    print("-" * 40)
    
    for filename, content in test_files.items():
        template_type = expander._determine_template_type(content)
        is_template = 'template' in filename.lower() and 'parameters' not in filename.lower()
        is_parameter = 'parameters' in filename.lower()
        
        print(f"📄 {filename}")
        print(f"   Type: {template_type}")
        
        if is_template and template_type == "ARM":
            print(f"   ✅ Correctly identified as ARM template")
        elif is_parameter and template_type == "ARM_PARAMETER":
            print(f"   ✅ Correctly identified as ARM parameter file")
        else:
            print(f"   ❌ Incorrect identification!")
        print()
    
    # Test parameter file matching
    print("\n🔗 Parameter File Matching:")
    print("-" * 40)

    template_files = [f for f in test_files.keys() if 'template' in f.lower() and 'parameters' not in f.lower()]

    # Expected matches based on Template/Parameters suffix pattern
    expected_matches = {
        "Grafana.deploymentTemplate.json": "Grafana.deploymentParameters.json",
        "KustoScripts.template.json": "KustoScripts.parameters.json",
        "roleAssignment.deploymentTemplate.json": "roleAssignment.deploymentParameters.json",
        "webapp.deploymentTemplate.json": "webapp.deploymentParameters.json",
        "storage.Template.json": "storage.Parameters.json"
    }

    for template_file in template_files:
        template_path = str(test_dir / template_file)
        print(f"\n📄 Template: {template_file}")

        # Find matching parameter files
        found_params = expander._find_matching_parameter_files(template_path)
        found_basenames = [os.path.basename(p) for p in found_params]

        print(f"   Found parameters: {found_basenames}")

        # Check expected match
        expected = expected_matches.get(template_file, "unknown")

        if expected in found_basenames:
            print(f"   ✅ Successfully matched with {expected}")

            # Verify base name logic
            template_base = template_file.replace('.json', '')
            param_base = expected.replace('.json', '')

            # Extract root names (removing Template/Parameters suffixes)
            template_root = template_base.replace('Template', '').replace('template', '')
            param_root = param_base.replace('Parameters', '').replace('parameters', '')

            print(f"   📋 Base name analysis:")
            print(f"      Template root: '{template_root}'")
            print(f"      Parameter root: '{param_root}'")
            print(f"      Match: {template_root == param_root}")
        else:
            print(f"   ❌ Failed to match with expected {expected}")
            print(f"   🔍 Debug: Available parameter files in directory:")
            param_files_in_dir = [f for f in test_files.keys() if 'parameter' in f.lower()]
            for pf in param_files_in_dir:
                print(f"      - {pf}")
    
    # Test parameter loading
    print(f"\n📥 Parameter Loading Test:")
    print("-" * 40)
    
    for template_file in template_files:
        template_path = str(test_dir / template_file)
        print(f"\n📄 Loading parameters for: {template_file}")
        
        param_values = expander._load_parameter_values(template_path)
        if param_values:
            print(f"   ✅ Successfully loaded parameters: {list(param_values.keys())}")
        else:
            print(f"   ❌ Failed to load parameters")
    
    # Cleanup
    import shutil
    shutil.rmtree(test_dir)
    
    print(f"\n✨ Test completed!")

if __name__ == "__main__":
    test_your_directory_pattern()
