{"version": "1.0.0", "template_system": {"validation": {"enabled": true, "strict_mode": false, "validate_on_load": true, "validate_variables": true, "required_placeholders": {"html": ["title", "content"], "prompts": ["numbered_content", "controls_section"], "context": ["file_path", "resource_types"]}, "forbidden_patterns": ["eval\\(", "exec\\(", "__import__", "subprocess", "os\\.system"], "max_template_size_mb": 10, "max_nesting_depth": 5}, "hot_reloading": {"enabled": false, "watch_directories": ["templates"], "watch_extensions": [".html", ".css", ".js", ".txt", ".json"], "debounce_ms": 500, "auto_reload_on_change": true}, "versioning": {"enabled": true, "version_header": "<!-- Template Version: {version} -->", "compatibility_check": true, "min_supported_version": "1.0.0", "version_file": "templates/VERSION"}, "performance": {"monitoring_enabled": true, "cache_enabled": true, "cache_ttl_seconds": 3600, "max_cache_size": 100, "log_slow_loads": true, "slow_load_threshold_ms": 1000, "metrics_file": "logs/template_metrics.json"}, "security": {"sanitize_variables": true, "escape_html": true, "validate_file_paths": true, "allowed_template_dirs": ["templates"], "max_include_depth": 3}}, "template_types": {"html": {"base_dir": "html", "extensions": [".html", ".htm"], "validation_rules": {"require_doctype": true, "require_charset": true, "validate_html5": false}}, "css": {"base_dir": "css", "extensions": [".css"], "validation_rules": {"validate_syntax": true, "check_vendor_prefixes": false}}, "js": {"base_dir": "js", "extensions": [".js"], "validation_rules": {"validate_syntax": true, "check_es6_compatibility": false}}, "prompts": {"base_dir": "prompts", "extensions": [".txt", ".md"], "validation_rules": {"require_placeholders": true, "validate_formatting": true}}}, "logging": {"level": "INFO", "log_file": "logs/template_loader.log", "log_validation_errors": true, "log_performance_metrics": true, "log_cache_operations": false}}