Priority,Domain,Severity,Control ID,File,Line,Description,Remediation,Code Snippet,Source,Resource Type
P1-Identity-HIGH,Identity Management,HIGH,IM-1,template.json,153,"App Service 'onefuzz-daily-ui' does not explicitly integrate with Azure Active Directory for authentication, which is required for secure identity and access management.",Enable Azure Active Directory authentication for the App Service to ensure secure identity management.,N/A,AI,Generic
P1-Identity-HIGH,Identity Management,HIGH,IM-2,template.json,153,"App Service 'onefuzz-daily-ui' does not enforce Multi-Factor Authentication (MFA) for users or administrators, weakening access security.",Configure Azure Active Directory authentication with MFA enforcement for all users and administrators accessing the App Service.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,44,"App Service 'onefuzz-daily-ui' exposes a public endpoint ('onefuzz-daily-ui.azurewebsites.net') with 'sslState' set to 'Disabled', allowing unsecured public access. This violates the requirement to secure all public endpoints.",Enable HTTPS by setting 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all public hostNameSslStates and ensure 'httpsOnly' is set to true.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,51,"App Service 'onefuzz-daily-ui' exposes a public SCM endpoint ('onefuzz-daily-ui.scm.azurewebsites.net') with 'sslState' set to 'Disabled', allowing unsecured public access. This violates the requirement to secure all public endpoints.",Enable HTTPS by setting 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all public SCM hostNameSslStates and ensure 'httpsOnly' is set to true.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,153,"App Service 'onefuzz-daily-ui' allows public network access ('publicNetworkAccess': 'Enabled') in 'siteConfig', exposing the app to the internet. This violates the requirement to secure all public endpoints.",Set 'publicNetworkAccess' to 'Disabled' in 'siteConfig' to restrict public access and use private endpoints or access restrictions.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,153,"App Service 'onefuzz-daily-ui' has 'ipSecurityRestrictions' with 'ipAddress': 'Any' and 'action': 'Allow', which allows unrestricted public access. This violates the requirement to secure all public endpoints.",Remove the 'Allow all' rule from 'ipSecurityRestrictions' and restrict access to only trusted IP addresses or ranges.,N/A,AI,Generic
P2-Network-CRITICAL,Network Security,CRITICAL,NS-2,template.json,153,"App Service 'onefuzz-daily-ui' has 'scmIpSecurityRestrictions' with 'ipAddress': 'Any' and 'action': 'Allow', which allows unrestricted public SCM access. This violates the requirement to secure all public endpoints.",Remove the 'Allow all' rule from 'scmIpSecurityRestrictions' and restrict SCM access to only trusted IP addresses or ranges.,N/A,AI,Generic
P2-Network-HIGH,Network Security,HIGH,NS-5,template.json,153,"App Service 'onefuzz-daily-ui' does not use private endpoints ('publicNetworkAccess': 'Enabled'), increasing exposure to the public internet. This violates the recommendation to use private endpoints for secure access.",Configure a private endpoint for the App Service and set 'publicNetworkAccess' to 'Disabled' to restrict access to private networks only.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-1,template.json,153,App Service 'onefuzz-daily-ui' does not specify customer-managed keys (CMK) or explicit encryption at rest settings. This violates the requirement to enable encryption at rest for all data storage.,"Configure encryption at rest for the App Service by integrating with Azure Key Vault and, if required, enable customer-managed keys (CMK) for storage.",N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,44,"App Service 'onefuzz-daily-ui' has 'hostNameSslStates' with 'sslState' set to 'Disabled' for 'onefuzz-daily-ui.azurewebsites.net', which allows unencrypted HTTP connections. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates to enforce HTTPS and ensure TLS 1.2+ is used for all endpoints.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-2,template.json,51,"App Service 'onefuzz-daily-ui' has 'hostNameSslStates' with 'sslState' set to 'Disabled' for 'onefuzz-daily-ui.scm.azurewebsites.net', which allows unencrypted HTTP connections. This violates the requirement to use TLS 1.2+ for all data transfers.",Set 'sslState' to 'SniEnabled' or 'IpBasedEnabled' for all hostNameSslStates to enforce HTTPS and ensure TLS 1.2+ is used for all endpoints.,N/A,AI,Generic
P3-Data-CRITICAL,Data Protection,CRITICAL,DP-3,template.json,153,"App Service 'onefuzz-daily-ui' does not reference Azure Key Vault for application secrets or sensitive configuration, violating the requirement to store sensitive data like keys in Azure Key Vault.",Store all application secrets and sensitive configuration in Azure Key Vault and reference them securely in the App Service configuration.,N/A,AI,Generic
P3-Data-MEDIUM,Data Protection,MEDIUM,DP-6,template.json,153,"App Service 'onefuzz-daily-ui' does not specify the use of customer-managed keys (CMK) for encryption, reducing control over data protection.",Integrate the App Service with Azure Key Vault and configure customer-managed keys (CMK) for enhanced encryption control.,N/A,AI,Generic
P4-Access-HIGH,Access Management,HIGH,AM-1,template.json,153,"App Service 'onefuzz-daily-ui' allows unrestricted public access via 'ipSecurityRestrictions' and 'scmIpSecurityRestrictions', violating the least privilege access principle.",Restrict 'ipSecurityRestrictions' and 'scmIpSecurityRestrictions' to only allow trusted IP addresses or ranges.,N/A,AI,Generic
