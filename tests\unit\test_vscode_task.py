#!/usr/bin/env python3
"""
Test the VS Code task functionality.
"""

import sys
import tempfile
import json
from pathlib import Path

# Add current directory to path
sys.path.append('.')

def test_file_analysis():
    """Test file analysis task."""
    print("🧪 Testing VS Code File Analysis Task")
    print("=" * 40)
    
    try:
        from security_opt import SecurityPRReviewer
        
        # Create a test ARM template
        test_template = {
            "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
            "contentVersion": "1.0.0.0",
            "resources": [
                {
                    "type": "Microsoft.Storage/storageAccounts",
                    "apiVersion": "2021-04-01",
                    "name": "teststorage",
                    "location": "[resourceGroup().location]",
                    "properties": {
                        "supportsHttpsTrafficOnly": False  # Security issue
                    }
                }
            ]
        }
        
        # Simulate the VS Code task logic
        temp_dir = tempfile.mkdtemp()
        temp_file = Path(temp_dir) / "test.json"
        temp_file.write_text(json.dumps(test_template, indent=2))
        
        reviewer = SecurityPRReviewer(local_folder=temp_dir)
        files = reviewer.analyze_folder(temp_dir)
        findings = reviewer.analyze_files(files)
        
        # Format output like the VS Code task
        print('\n🛡️ SECURITY ANALYSIS RESULTS')
        print('='*50)
        
        if findings:
            for f in findings:
                severity = f.get("severity", "UNKNOWN")
                control_id = f.get("control_id", "N/A")
                description = f.get("description", "N/A")
                file_path = f.get("file_path", "N/A")
                line = f.get("line", "N/A")
                remediation = f.get("remediation", "N/A")
                
                severity_emoji = {
                    "CRITICAL": "🔴",
                    "HIGH": "🟠", 
                    "MEDIUM": "🟡",
                    "LOW": "🔵"
                }.get(severity, "⚪")
                
                print(f'{severity_emoji} {severity} - {control_id}: {description}')
                print(f'   📄 File: {file_path} (Line {line})')
                print(f'   🔧 Fix: {remediation}\n')
            
            print(f'📊 Total Issues: {len(findings)}')
        else:
            print('✅ No security issues found!')
        
        return True
        
    except Exception as e:
        print(f"❌ File analysis test failed: {e}")
        return False

def test_security_controls():
    """Test security controls task."""
    print("\n🧪 Testing Security Controls Task")
    print("=" * 35)
    
    try:
        from security_opt import SecurityPRReviewer
        
        reviewer = SecurityPRReviewer(local_folder='.')
        controls = reviewer._find_relevant_controls('Storage')
        
        print('\n🛡️ AZURE SECURITY CONTROLS FOR STORAGE')
        print('='*50)
        
        for c in controls[:5]:  # Show first 5
            control_id = c.get("id", "N/A")
            name = c.get("name", "N/A")
            description = c.get("description", "N/A")
            
            print(f'📋 {control_id}: {name}')
            print(f'   📝 {description[:100]}...\n')
        
        print(f'📊 Total Controls: {len(controls)}')
        return True
        
    except Exception as e:
        print(f"❌ Security controls test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing VS Code Task Integration")
    print("=" * 45)
    
    tests = [
        ("File Analysis Task", test_file_analysis),
        ("Security Controls Task", test_security_controls)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results[test_name] = "PASSED" if success else "FAILED"
        except Exception as e:
            results[test_name] = f"ERROR: {str(e)}"
    
    # Summary
    print("\n" + "=" * 45)
    print("📊 VS CODE TASK TEST SUMMARY")
    print("=" * 45)
    
    passed = sum(1 for result in results.values() if result == "PASSED")
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅" if result == "PASSED" else "❌"
        print(f"{status} {test_name}: {result}")
    
    print(f"\n📈 Overall: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 All VS Code tasks are working!")
        print("\n🚀 Ready to use in VS Code:")
        print("1. Press Ctrl+Shift+P")
        print("2. Type 'Tasks: Run Task'")
        print("3. Select any 'IaC Security' task")
        print("4. Get instant security analysis!")
    else:
        print("\n⚠️ Some tasks failed - check the errors above")

if __name__ == "__main__":
    main()
