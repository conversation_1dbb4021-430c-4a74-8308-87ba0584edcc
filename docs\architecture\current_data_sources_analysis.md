# Current Data Sources Analysis

## Overview
This document analyzes the existing static data files and their schemas in the IaC Guardian project to understand the current security control data structure and identify opportunities for consolidation.

## Data Sources Inventory

### 1. CSV Files (SecurityBenchmarks/)
- **identity_management.csv** (38 lines)
- **data_protection.csv** (34 lines) 
- **network_security.csv** (40 lines)
- **network_security_with_urls.csv** (19 lines) - Appears to be a subset/variant

### 2. JSON Files
- **Azure_Security_Benchmark_v3.json** (4,949 lines)
- **missing_controls.json** (Coverage analysis file)

### 3. Excel Files
- **Azure_Security_Benchmark_v3.xlsx** (Official Microsoft source)

### 4. Configuration Files
- **azure_resource_mappings.json** (1,000+ lines) - Resource type mappings
- **AzureSecurityCenter.json** (8,000+ lines) - Azure Policy definitions

## Schema Analysis

### CSV Schema (Consistent across all CSV files)
```
Columns:
1. ASB ID - Azure Security Benchmark ID (e.g., IM-1, NS-2, DP-3)
2. Control Domain - Domain category (Identity Management, Network Security, Data Protection)
3. Recommendation - Brief control title/name
4. Security Principle - High-level security principle
5. Azure Guidance - Specific Azure implementation guidance
6. Implementation and additional context - Detailed implementation with URLs
7. Customer Security Stakeholders - Responsible roles
8. Azure Policy Mapping - Related Azure policies (semicolon-separated)
```

### JSON Schema (Azure_Security_Benchmark_v3.json)
```json
{
  "metadata": {
    "version": "3.0",
    "source": "Microsoft Azure Security Benchmark",
    "processed_date": "timestamp",
    "rag_optimized": true
  },
  "controls": [
    {
      "id": "control_id",
      "name": "", // Often empty in current data
      "description": "", // Often empty in current data
      "sheet": "source_sheet",
      "resource_types": ["General"],
      "severity": "HIGH|MEDIUM|LOW",
      "related_controls": [
        {
          "framework": "framework_name",
          "control": "control_mappings"
        }
      ]
    }
  ]
}
```

## Data Quality Issues Identified

### 1. Inconsistent Data Sources
- CSV files contain rich, human-readable content with detailed guidance
- JSON file has empty name/description fields for most controls
- Duplicate control IDs across different sources with varying content quality

### 2. Schema Mismatches
- CSV files have 8 structured columns with consistent formatting
- JSON structure focuses on framework mappings but lacks implementation details
- No standardized severity mapping between sources

### 3. Data Completeness
- JSON file: 4,949 lines but many controls have empty core fields
- CSV files: Rich content but limited coverage (only 3 domains covered)
- Missing controls analysis shows 80 missing controls (4.76% coverage)

### 4. Redundancy and Conflicts
- network_security.csv vs network_security_with_urls.csv (duplicate with variations)
- Same control IDs may have different content across CSV vs JSON sources
- No clear source of truth for conflicting information

## Resource Mapping Analysis

### Current Resource Mapping Structure
- **azure_resource_mappings.json** contains comprehensive mappings:
  - ARM resource types
  - Terraform resource types  
  - Bicep resource types
  - Keywords for pattern matching
- 20+ resource categories (Storage, KeyVault, SQL, Container, etc.)
- Each category has 50-200+ specific resource type mappings

### Integration Points
- Used by `security_opt.py` for resource-to-control correlation
- Loaded via `_load_azure_resource_mappings()` method
- Cached in `resource_control_cache` for performance

## Current Data Flow

### 1. Data Loading Priority (from .env)
```
BENCHMARK_SOURCE_PRIORITY=csv,json,excel,fallback
```

### 2. Processing Pipeline
1. **CSV Loading** (`_load_optimized_csv_benchmark()`)
   - Prioritized by domain: Identity → Network → Data Protection → Access → Monitoring
   - Maps CSV columns to standardized control structure
   - Extracts resource types from guidance text

2. **JSON Fallback** (`_load_json_benchmark()`)
   - Used when CSV files unavailable
   - Limited content quality due to empty fields

3. **Excel Processing** (`_load_excel_benchmark()`)
   - Downloads official Microsoft Excel file
   - Converts to JSON format for caching

4. **Resource Correlation** (`_build_resource_control_correlations()`)
   - Maps resource types to relevant controls
   - Applies domain priority ordering
   - Caches results for performance

## Key Findings

### Strengths
- Domain priority system ensures consistent ordering
- Multiple data source fallback provides resilience
- Resource mapping system is comprehensive
- Caching improves performance

### Critical Issues
- **Data Fragmentation**: Same controls scattered across multiple files
- **Quality Inconsistency**: CSV files rich, JSON files sparse
- **Manual Maintenance**: Adding new controls requires updating multiple files
- **Conflict Resolution**: No systematic approach to handle conflicting data
- **Limited Coverage**: Only 3 domains have detailed CSV coverage

### Opportunities
- **Single Source of Truth**: Consolidate all control data into master database
- **AI-Powered Merging**: Intelligently combine best content from multiple sources
- **Dynamic Updates**: Enable adding custom controls without code changes
- **Validation Logic**: Cross-reference controls to reduce false positives
- **Extensible Schema**: Support multiple frameworks and custom control types

## Recommendations for Master Database

### 1. Unified Schema Design
- Combine best aspects of CSV richness and JSON structure flexibility
- Support multiple control frameworks (ASB, CIS, NIST, custom)
- Include versioning and source tracking

### 2. Data Consolidation Strategy
- Use Control ID as primary key
- Merge content from multiple sources with conflict resolution
- Preserve source attribution for audit trails

### 3. AI-Enhanced Processing
- Intelligent content merging from multiple sources
- Automated resource type extraction and validation
- Cross-validation to reduce recommendation inconsistencies

### 4. Dynamic Extension Support
- API for adding custom controls
- Template system for organization-specific requirements
- Automated schema validation and migration
