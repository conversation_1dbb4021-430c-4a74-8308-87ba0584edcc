File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
simple_test.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,25.0,"The 'supportsHttpsTrafficOnly' property for the Microsoft.Storage/storageAccounts resource is set to false. This allows unencrypted HTTP connections, enabling attackers to intercept or modify data in transit. Attackers can exploit this to perform man-in-the-middle attacks, leading to data exfiltration or tampering. The blast radius includes all data transferred to and from the storage account, potentially exposing sensitive information.","Set 'supportsHttpsTrafficOnly' to true in the storage account properties to enforce encrypted HTTPS connections for all data in transit. Example: ""supportsHttpsTrafficOnly"": true",,,,ai_analysis,,Validated
simple_test.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,27.0,"The 'minimumTlsVersion' property for the Microsoft.Storage/storageAccounts resource is set to 'TLS1_0', which is an outdated and insecure protocol. Attackers can exploit known vulnerabilities in TLS 1.0 to decrypt or manipulate data in transit. This increases the risk of data interception and compromise across all services using this storage account.","Set 'minimumTlsVersion' to 'TLS1_2' or higher in the storage account properties to enforce strong encryption for data in transit. Example: ""minimumTlsVersion"": ""TLS1_2""",,,,ai_analysis,,Validated
simple_test.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,29.0,"The 'networkAcls.defaultAction' property for the Microsoft.Storage/storageAccounts resource is set to 'Allow', which permits public network access to the storage account. This exposes the storage account to the internet, enabling attackers to attempt unauthorized access, brute force, or data exfiltration. The blast radius includes all data stored in the account and any connected resources.","Set 'networkAcls.defaultAction' to 'Deny' and configure specific network rules or private endpoints to restrict access. Example: ""defaultAction"": ""Deny""",,,,ai_analysis,,Validated
simple_test.json,DP-4,Data Protection,Enable data at rest encryption by default,MEDIUM,26.0,"The 'allowBlobPublicAccess' property for the Microsoft.Storage/storageAccounts resource is set to true, allowing public anonymous access to blob data. This increases the risk of unintentional data exposure, as attackers or unauthorized users can access blobs without authentication.","Set 'allowBlobPublicAccess' to false in the storage account properties to prevent anonymous public access to blob data. Example: ""allowBlobPublicAccess"": false",,,,ai_analysis,,Validated
simple_test.json,NS-8,Network Security,Detect and disable insecure services and protocols,HIGH,27.0,"The 'minimumTlsVersion' property for the Microsoft.Storage/storageAccounts resource is set to 'TLS1_0', which is an insecure protocol. Attackers can exploit weaknesses in TLS 1.0 to decrypt or manipulate network traffic, increasing the risk of credential theft and data compromise.","Set 'minimumTlsVersion' to 'TLS1_2' or higher to disable insecure protocols and enforce strong encryption. Example: ""minimumTlsVersion"": ""TLS1_2""",,,,ai_analysis,,Validated
simple_test.json,DP-8,Data Protection,Ensure security of key and certificate repository,CRITICAL,50.0,"The 'enableSoftDelete' property for the Microsoft.KeyVault/vaults resource is set to false, disabling soft delete. Attackers who gain access could permanently delete keys and secrets, making recovery impossible and enabling denial-of-service or destructive attacks. The blast radius includes all applications and services dependent on this Key Vault.","Set 'enableSoftDelete' to true in the Key Vault properties to ensure deleted keys and secrets can be recovered. Example: ""enableSoftDelete"": true",,,,ai_analysis,,Validated
simple_test.json,DP-8,Data Protection,Ensure security of key and certificate repository,CRITICAL,51.0,"The 'enablePurgeProtection' property for the Microsoft.KeyVault/vaults resource is set to false, disabling purge protection. Attackers with sufficient permissions can permanently delete (purge) keys and secrets, bypassing soft delete and making recovery impossible. This increases the risk of data loss and service disruption.","Set 'enablePurgeProtection' to true in the Key Vault properties to prevent permanent deletion of keys and secrets. Example: ""enablePurgeProtection"": true",,,,ai_analysis,,Validated
simple_test.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,29.0,"The 'networkAcls.defaultAction' property for the Microsoft.KeyVault/vaults resource is set to 'Allow', permitting public network access to the Key Vault. This exposes cryptographic keys and secrets to the internet, increasing the risk of unauthorized access, data theft, and compromise of all dependent resources.","Set 'networkAcls.defaultAction' to 'Deny' and configure private endpoints or specific network rules to restrict access. Example: ""defaultAction"": ""Deny""",,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 8,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 0,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T11:06:21.506753,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
