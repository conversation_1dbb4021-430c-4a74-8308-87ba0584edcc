File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
simple_test.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,25.0,"The 'supportsHttpsTrafficOnly' property for the Microsoft.Storage/storageAccounts resource is set to false, allowing unencrypted HTTP connections. This enables attackers to intercept or modify data in transit to the storage account, exposing sensitive data and authentication tokens. The blast radius includes all data stored in the account and any connected resources, such as Key Vaults that depend on this storage.","Set 'supportsHttpsTrafficOnly' to true in the storage account properties to enforce HTTPS for all data transfers. This ensures data in transit is encrypted and protected from interception. Example: ""supportsHttpsTrafficOnly"": true.",,,,ai_analysis,,Validated
simple_test.json,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,27.0,"The 'minimumTlsVersion' property for the Microsoft.Storage/storageAccounts resource is set to 'TLS1_0', which is an outdated and insecure protocol. Attackers can exploit known vulnerabilities in TLS 1.0 to decrypt or tamper with data in transit, increasing the risk of data exfiltration and man-in-the-middle attacks. The blast radius includes all clients and services communicating with this storage account.","Set 'minimumTlsVersion' to 'TLS1_2' or higher in the storage account properties to enforce strong encryption for data in transit. Example: ""minimumTlsVersion"": ""TLS1_2"".",,,,ai_analysis,,Validated
simple_test.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,29.0,"The 'networkAcls.defaultAction' property for the Microsoft.Storage/storageAccounts resource is set to 'Allow', which permits public network access from any source. This exposes the storage account to the internet, enabling attackers to attempt unauthorized access, brute force, or data exfiltration. The blast radius includes all data in the storage account and any dependent resources.","Set 'networkAcls.defaultAction' to 'Deny' and explicitly allow only trusted networks or private endpoints. Example: ""defaultAction"": ""Deny"". Configure private endpoints for secure access.",,,,ai_analysis,,Validated
simple_test.json,DP-4,Data Protection,Enable data at rest encryption by default,MEDIUM,26.0,"The 'allowBlobPublicAccess' property for the Microsoft.Storage/storageAccounts resource is set to true, allowing blobs to be publicly accessible. This increases the risk of accidental or intentional data exposure, enabling attackers to enumerate and exfiltrate sensitive data without authentication.","Set 'allowBlobPublicAccess' to false to prevent anonymous public access to blob data. Example: ""allowBlobPublicAccess"": false.",,,,ai_analysis,,Validated
simple_test.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,29.0,"The 'networkAcls.defaultAction' property for the Microsoft.KeyVault/vaults resource is set to 'Allow', permitting public network access to the Key Vault. This exposes cryptographic keys and secrets to the internet, enabling attackers to attempt unauthorized access or brute force attacks. The blast radius includes all secrets and keys managed by this Key Vault and any connected resources.","Set 'networkAcls.defaultAction' to 'Deny' and configure private endpoints or trusted virtual networks for access. Example: ""defaultAction"": ""Deny"".",,,,ai_analysis,,Validated
simple_test.json,DP-8,Data Protection,Ensure security of key and certificate repository,MEDIUM,50.0,"The 'enableSoftDelete' property for the Microsoft.KeyVault/vaults resource is set to false, disabling soft delete protection. Attackers or malicious insiders could permanently delete keys and secrets, making recovery impossible and evading forensic investigation. The blast radius includes all applications and services relying on this Key Vault.","Set 'enableSoftDelete' to true to enable soft delete protection, allowing recovery of deleted secrets and keys. Example: ""enableSoftDelete"": true.",,,,ai_analysis,,Validated
simple_test.json,DP-8,Data Protection,Ensure security of key and certificate repository,MEDIUM,51.0,"The 'enablePurgeProtection' property for the Microsoft.KeyVault/vaults resource is set to false, allowing immediate and irreversible deletion of keys and secrets. This enables attackers or insiders to destroy cryptographic material and evade detection, increasing the risk of data loss and operational disruption.","Set 'enablePurgeProtection' to true to prevent permanent deletion of Key Vault objects until the retention period expires. Example: ""enablePurgeProtection"": true.",,,,ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 7,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 0,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-20T11:03:27.279594,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
