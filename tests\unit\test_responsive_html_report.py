#!/usr/bin/env python3
"""
Test script for responsive HTML report optimizations.
"""

import os
import tempfile
import unittest
import re
from pathlib import Path
from security_opt import SecurityPRReviewer


class TestResponsiveHTMLReport(unittest.TestCase):
    """Test cases for responsive HTML report optimizations"""

    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.test_path = Path(self.test_dir)
        
        # Set up minimal environment for SecurityPRReviewer
        os.environ["ENABLE_PARAMETER_EXPANSION"] = "false"

    def tearDown(self):
        """Clean up test environment"""
        import shutil
        shutil.rmtree(self.test_dir, ignore_errors=True)

    def test_responsive_breakpoints(self):
        """Test responsive design breakpoints in CSS"""
        
        sample_findings = [
            {
                "control_id": "NS-1",
                "severity": "CRITICAL",
                "file_path": "network.bicep",
                "line": 25,
                "description": "Network Security Group not configured",
                "remediation": "Add NSG resource"
            }
        ]
        
        reviewer = SecurityPRReviewer(local_folder=str(self.test_path))
        html_path = self.test_path / "responsive_test.html"
        reviewer._export_findings_to_html(sample_findings, str(html_path))
        
        html_content = html_path.read_text(encoding='utf-8')
        
        # Test for all responsive breakpoints
        breakpoints = [
            "min-width: 1400px",  # Extra Large
            "min-width: 1200px",  # Large
            "min-width: 992px",   # Medium-Large
            "min-width: 768px",   # Medium (Tablets)
            "min-width: 576px",   # Small (Large Phones)
            "max-width: 575px"    # Extra Small (Small Phones)
        ]
        
        for breakpoint in breakpoints:
            self.assertIn(breakpoint, html_content, f"Missing breakpoint: {breakpoint}")
        
        print("✅ All responsive breakpoints found")

    def test_touch_optimizations(self):
        """Test touch device optimizations"""
        
        sample_findings = [
            {
                "control_id": "DP-1",
                "severity": "HIGH",
                "file_path": "storage.bicep",
                "line": 10,
                "description": "Storage account security issue",
                "remediation": "Fix storage configuration"
            }
        ]
        
        reviewer = SecurityPRReviewer(local_folder=str(self.test_path))
        html_path = self.test_path / "touch_test.html"
        reviewer._export_findings_to_html(sample_findings, str(html_path))
        
        html_content = html_path.read_text(encoding='utf-8')
        
        # Test for touch optimizations
        touch_features = [
            ".touch-device",
            "min-height: 44px",  # iOS recommended touch target
            "-webkit-tap-highlight-color",
            "touchstart",
            "touchend",
            "ontouchstart"
        ]
        
        for feature in touch_features:
            self.assertIn(feature, html_content, f"Missing touch feature: {feature}")
        
        print("✅ Touch optimizations verified")

    def test_accessibility_features(self):
        """Test accessibility enhancements"""
        
        sample_findings = [
            {
                "control_id": "AC-1",
                "severity": "MEDIUM",
                "file_path": "access.bicep",
                "line": 5,
                "description": "Access control issue",
                "remediation": "Update access policies"
            }
        ]
        
        reviewer = SecurityPRReviewer(local_folder=str(self.test_path))
        html_path = self.test_path / "accessibility_test.html"
        reviewer._export_findings_to_html(sample_findings, str(html_path))
        
        html_content = html_path.read_text(encoding='utf-8')
        
        # Test for ARIA attributes and semantic HTML
        accessibility_features = [
            'role="toolbar"',
            'role="group"',
            'role="button"',
            'role="article"',
            'role="region"',
            'aria-label=',
            'aria-pressed=',
            'aria-expanded=',
            'aria-controls=',
            'aria-labelledby=',
            'aria-hidden="true"',
            'tabindex="0"',
            'class="sr-only"',
            'h3 class="severity-header',  # Removed < and " to match partial string
            'h4 class="finding-title',    # Removed < and " to match partial string
            'article class="finding',     # Removed < and " to match partial string
            'header class="finding-header'  # Removed < and " to match partial string
        ]
        
        for feature in accessibility_features:
            self.assertIn(feature, html_content, f"Missing accessibility feature: {feature}")
        
        print("✅ Accessibility features verified")

    def test_performance_optimizations(self):
        """Test performance optimization features"""
        
        sample_findings = [
            {
                "control_id": "PF-1",
                "severity": "LOW",
                "file_path": "performance.bicep",
                "line": 1,
                "description": "Performance optimization needed",
                "remediation": "Optimize resource configuration"
            }
        ]
        
        reviewer = SecurityPRReviewer(local_folder=str(self.test_path))
        html_path = self.test_path / "performance_test.html"
        reviewer._export_findings_to_html(sample_findings, str(html_path))
        
        html_content = html_path.read_text(encoding='utf-8')
        
        # Test for performance features
        performance_features = [
            "requestAnimationFrame",
            "setTimeout",
            "clearTimeout",
            "passive: true",
            "window.performance",
            "performance.mark",
            "searchTimeout",  # This is the actual debounce implementation
            "loading"
        ]
        
        for feature in performance_features:
            self.assertIn(feature, html_content, f"Missing performance feature: {feature}")
        
        print("✅ Performance optimizations verified")

    def test_dark_mode_support(self):
        """Test dark mode CSS support"""
        
        sample_findings = [
            {
                "control_id": "DM-1",
                "severity": "HIGH",
                "file_path": "theme.bicep",
                "line": 15,
                "description": "Theme configuration issue",
                "remediation": "Update theme settings"
            }
        ]
        
        reviewer = SecurityPRReviewer(local_folder=str(self.test_path))
        html_path = self.test_path / "darkmode_test.html"
        reviewer._export_findings_to_html(sample_findings, str(html_path))
        
        html_content = html_path.read_text(encoding='utf-8')
        
        # Test for dark mode features
        dark_mode_features = [
            "prefers-color-scheme: dark",
            "--light-bg: #2d3748",
            "--border-color: #4a5568",
            "--text-color: #e2e8f0",
            "background: #1a202c",
            "prefers-contrast: high",
            "prefers-reduced-motion: reduce"
        ]
        
        for feature in dark_mode_features:
            self.assertIn(feature, html_content, f"Missing dark mode feature: {feature}")
        
        print("✅ Dark mode support verified")

    def test_print_optimizations(self):
        """Test print-specific CSS optimizations"""
        
        sample_findings = [
            {
                "control_id": "PR-1",
                "severity": "CRITICAL",
                "file_path": "print.bicep",
                "line": 20,
                "description": "Print layout issue",
                "remediation": "Fix print styles"
            }
        ]
        
        reviewer = SecurityPRReviewer(local_folder=str(self.test_path))
        html_path = self.test_path / "print_test.html"
        reviewer._export_findings_to_html(sample_findings, str(html_path))
        
        html_content = html_path.read_text(encoding='utf-8')
        
        # Test for print optimizations
        print_features = [
            "@media print",
            "break-inside: avoid",
            "page-break-inside: avoid",
            "page-break-after: avoid",
            "color: black !important",
            "background: white !important",
            "display: none !important"
        ]
        
        for feature in print_features:
            self.assertIn(feature, html_content, f"Missing print feature: {feature}")
        
        print("✅ Print optimizations verified")

    def test_keyboard_navigation(self):
        """Test keyboard navigation features"""
        
        sample_findings = [
            {
                "control_id": "KB-1",
                "severity": "MEDIUM",
                "file_path": "keyboard.bicep",
                "line": 8,
                "description": "Keyboard navigation issue",
                "remediation": "Improve keyboard support"
            }
        ]
        
        reviewer = SecurityPRReviewer(local_folder=str(self.test_path))
        html_path = self.test_path / "keyboard_test.html"
        reviewer._export_findings_to_html(sample_findings, str(html_path))
        
        html_content = html_path.read_text(encoding='utf-8')
        
        # Test for keyboard navigation features
        keyboard_features = [
            "keydown",
            "ArrowRight",
            "ArrowLeft",
            "ArrowUp",
            "ArrowDown",
            "Enter",
            "Escape",
            "focus()",
            "outline: 2px solid",
            "outline-offset: 2px"
        ]
        
        for feature in keyboard_features:
            self.assertIn(feature, html_content, f"Missing keyboard feature: {feature}")
        
        print("✅ Keyboard navigation verified")

    def test_url_hash_management(self):
        """Test URL hash management for bookmarking"""
        
        sample_findings = [
            {
                "control_id": "URL-1",
                "severity": "LOW",
                "file_path": "url.bicep",
                "line": 3,
                "description": "URL management issue",
                "remediation": "Fix URL handling"
            }
        ]
        
        reviewer = SecurityPRReviewer(local_folder=str(self.test_path))
        html_path = self.test_path / "url_test.html"
        reviewer._export_findings_to_html(sample_findings, str(html_path))
        
        html_content = html_path.read_text(encoding='utf-8')
        
        # Test for URL management features
        url_features = [
            "updateUrlHash",
            "loadFromUrlHash",
            "URLSearchParams",
            "window.location.hash",
            "window.history.replaceState",
            "hashchange"
        ]
        
        for feature in url_features:
            self.assertIn(feature, html_content, f"Missing URL feature: {feature}")
        
        print("✅ URL hash management verified")

    def test_css_custom_properties(self):
        """Test CSS custom properties for theming"""
        
        sample_findings = [
            {
                "control_id": "CSS-1",
                "severity": "HIGH",
                "file_path": "styles.bicep",
                "line": 12,
                "description": "CSS theming issue",
                "remediation": "Update CSS variables"
            }
        ]
        
        reviewer = SecurityPRReviewer(local_folder=str(self.test_path))
        html_path = self.test_path / "css_test.html"
        reviewer._export_findings_to_html(sample_findings, str(html_path))
        
        html_content = html_path.read_text(encoding='utf-8')
        
        # Test for CSS custom properties
        css_properties = [
            ":root {",
            "--primary-color:",
            "--secondary-color:",
            "--success-color:",
            "--warning-color:",
            "--danger-color:",
            "--info-color:",
            "--light-bg:",
            "--dark-bg:",
            "--border-color:",
            "--text-color:",
            "--shadow:",
            "--border-radius:",
            "var(--primary-color)",
            "var(--secondary-color)"
        ]
        
        for prop in css_properties:
            self.assertIn(prop, html_content, f"Missing CSS property: {prop}")
        
        print("✅ CSS custom properties verified")


if __name__ == "__main__":
    # Run tests with verbose output
    unittest.main(verbosity=2)
