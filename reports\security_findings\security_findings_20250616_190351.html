
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Security Findings Report - IaC Guardian</title>
                <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
                <style>
                    :root {
                        --primary-color: #2c3e50;
                        --secondary-color: #3498db;
                        --success-color: #27ae60;
                        --warning-color: #f39c12;
                        --danger-color: #e74c3c;
                        --info-color: #17a2b8;
                        --light-bg: #f8f9fa;
                        --dark-bg: #343a40;
                        --border-color: #dee2e6;
                        --text-color: #495057;
                        --shadow: 0 2px 4px rgba(0,0,0,0.1);
                        --border-radius: 8px;
                    }

                    * {
                        margin: 0;
                        padding: 0;
                        box-sizing: border-box;
                    }

                    body {
                        font-family: 'Se<PERSON>e <PERSON>', Tahoma, Geneva, Verdana, sans-serif;
                        line-height: 1.6;
                        color: var(--text-color);
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        min-height: 100vh;
                    }

                    .container {
                        max-width: 1200px;
                        margin: 0 auto;
                        padding: 20px;
                    }

                    .header {
                        background: white;
                        border-radius: var(--border-radius);
                        padding: 30px;
                        margin-bottom: 30px;
                        box-shadow: var(--shadow);
                        text-align: center;
                    }

                    .header h1 {
                        color: var(--primary-color);
                        font-size: 2.5rem;
                        margin-bottom: 10px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 15px;
                    }

                    .header .subtitle {
                        color: var(--text-color);
                        font-size: 1.1rem;
                        opacity: 0.8;
                    }

                    .controls {
                        background: white;
                        border-radius: var(--border-radius);
                        padding: 20px;
                        margin-bottom: 30px;
                        box-shadow: var(--shadow);
                        display: flex;
                        flex-wrap: wrap;
                        gap: 15px;
                        align-items: center;
                    }

                    .search-box {
                        flex: 1;
                        min-width: 250px;
                        position: relative;
                    }

                    .search-box input {
                        width: 100%;
                        padding: 12px 45px 12px 15px;
                        border: 2px solid var(--border-color);
                        border-radius: var(--border-radius);
                        font-size: 14px;
                        transition: border-color 0.3s;
                    }

                    .search-box input:focus {
                        outline: none;
                        border-color: var(--secondary-color);
                    }

                    .search-box i {
                        position: absolute;
                        right: 15px;
                        top: 50%;
                        transform: translateY(-50%);
                        color: var(--text-color);
                        opacity: 0.5;
                    }

                    .filter-buttons {
                        display: flex;
                        gap: 10px;
                        flex-wrap: wrap;
                    }

                    .filter-btn {
                        padding: 8px 16px;
                        border: 2px solid;
                        border-radius: 20px;
                        background: white;
                        cursor: pointer;
                        transition: all 0.3s;
                        font-size: 12px;
                        font-weight: 600;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                    }

                    .filter-btn.active {
                        color: white !important;
                    }

                    .filter-btn.critical {
                        border-color: var(--danger-color);
                        color: var(--danger-color);
                    }

                    .filter-btn.critical.active {
                        background: var(--danger-color);
                    }

                    .filter-btn.high {
                        border-color: var(--warning-color);
                        color: var(--warning-color);
                    }

                    .filter-btn.high.active {
                        background: var(--warning-color);
                    }

                    .filter-btn.medium {
                        border-color: #ffc107;
                        color: #856404;
                    }

                    .filter-btn.medium.active {
                        background: #ffc107;
                        color: #856404;
                    }

                    .filter-btn.low {
                        border-color: var(--info-color);
                        color: var(--info-color);
                    }

                    .filter-btn.low.active {
                        background: var(--info-color);
                    }

                    .filter-btn.all {
                        border-color: var(--secondary-color);
                        color: var(--secondary-color);
                    }

                    .filter-btn.all.active {
                        background: var(--secondary-color);
                    }

                    .summary {
                        background: white;
                        border-radius: var(--border-radius);
                        padding: 30px;
                        margin-bottom: 30px;
                        box-shadow: var(--shadow);
                    }

                    .summary h2 {
                        color: var(--primary-color);
                        margin-bottom: 20px;
                        font-size: 1.5rem;
                    }

                    .stats-grid {
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                        gap: 20px;
                        margin-bottom: 20px;
                    }

                    .stat-card {
                        background: var(--light-bg);
                        padding: 20px;
                        border-radius: var(--border-radius);
                        text-align: center;
                        border-left: 4px solid var(--secondary-color);
                    }

                    .stat-number {
                        font-size: 2rem;
                        font-weight: bold;
                        color: var(--primary-color);
                    }

                    .stat-label {
                        color: var(--text-color);
                        font-size: 0.9rem;
                        margin-top: 5px;
                    }

                    .severity-breakdown {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 15px;
                        margin-top: 20px;
                    }

                    .severity-stat {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        padding: 10px 15px;
                        border-radius: 20px;
                        background: var(--light-bg);
                        font-weight: 600;
                    }

                    .severity-stat.critical {
                        background: rgba(231, 76, 60, 0.1);
                        color: var(--danger-color);
                    }

                    .severity-stat.high {
                        background: rgba(243, 156, 18, 0.1);
                        color: var(--warning-color);
                    }

                    .severity-stat.medium {
                        background: rgba(255, 193, 7, 0.1);
                        color: #856404;
                    }

                    .severity-stat.low {
                        background: rgba(23, 162, 184, 0.1);
                        color: var(--info-color);
                    }

                    .findings-container {
                        background: white;
                        border-radius: var(--border-radius);
                        box-shadow: var(--shadow);
                        overflow: hidden;
                    }

                    .severity-group {
                        margin-bottom: 0;
                    }

                    .severity-header {
                        padding: 20px 30px;
                        color: white;
                        font-weight: bold;
                        font-size: 1.2rem;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        cursor: pointer;
                        transition: all 0.3s;
                        position: relative;
                    }

                    .severity-header:hover {
                        opacity: 0.9;
                    }

                    .severity-header .toggle-icon {
                        margin-left: auto;
                        transition: transform 0.3s;
                    }

                    .severity-header.collapsed .toggle-icon {
                        transform: rotate(-90deg);
                    }

                    .severity-header .count {
                        background: rgba(255, 255, 255, 0.2);
                        padding: 4px 12px;
                        border-radius: 15px;
                        font-size: 0.9rem;
                    }

                    .critical {
                        background: linear-gradient(135deg, #e74c3c, #c0392b);
                    }

                    .high {
                        background: linear-gradient(135deg, #f39c12, #e67e22);
                    }

                    .medium {
                        background: linear-gradient(135deg, #ffc107, #f39c12);
                        color: #856404 !important;
                    }

                    .low {
                        background: linear-gradient(135deg, #17a2b8, #138496);
                    }

                    .findings-list {
                        max-height: 600px;
                        overflow-y: auto;
                        transition: max-height 0.3s ease-out;
                    }

                    .findings-list.collapsed {
                        max-height: 0;
                        overflow: hidden;
                    }

                    .finding {
                        border-bottom: 1px solid var(--border-color);
                        padding: 25px 30px;
                        transition: background-color 0.3s;
                        position: relative;
                    }

                    .finding:last-child {
                        border-bottom: none;
                    }

                    .finding:hover {
                        background: var(--light-bg);
                    }

                    .finding-header {
                        display: flex;
                        align-items: flex-start;
                        gap: 15px;
                        margin-bottom: 15px;
                    }

                    .finding-icon {
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: white;
                        font-size: 1.2rem;
                        flex-shrink: 0;
                    }

                    .finding-icon.critical {
                        background: var(--danger-color);
                    }

                    .finding-icon.high {
                        background: var(--warning-color);
                    }

                    .finding-icon.medium {
                        background: #ffc107;
                        color: #856404;
                    }

                    .finding-icon.low {
                        background: var(--info-color);
                    }

                    .finding-content {
                        flex: 1;
                    }

                    .finding-title {
                        font-size: 1.1rem;
                        font-weight: 600;
                        color: var(--primary-color);
                        margin-bottom: 8px;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                    }

                    .control-badge {
                        background: var(--secondary-color);
                        color: white;
                        padding: 2px 8px;
                        border-radius: 12px;
                        font-size: 0.8rem;
                        font-weight: 500;
                    }

                    .finding-meta {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 15px;
                        margin-bottom: 15px;
                        font-size: 0.9rem;
                        color: var(--text-color);
                    }

                    .meta-item {
                        display: flex;
                        align-items: center;
                        gap: 5px;
                    }

                    .meta-item i {
                        color: var(--secondary-color);
                    }

                    .finding-description {
                        margin-bottom: 15px;
                        line-height: 1.6;
                    }

                    .finding-remediation {
                        background: var(--light-bg);
                        padding: 15px;
                        border-radius: var(--border-radius);
                        border-left: 4px solid var(--success-color);
                    }

                    .remediation-title {
                        font-weight: 600;
                        color: var(--success-color);
                        margin-bottom: 8px;
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    }

                    .code-snippet {
                        background: #2d3748;
                        color: #e2e8f0;
                        padding: 15px;
                        border-radius: var(--border-radius);
                        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                        font-size: 0.9rem;
                        line-height: 1.4;
                        white-space: pre-wrap;
                        margin: 15px 0;
                        overflow-x: auto;
                        border: 1px solid #4a5568;
                    }

                    .no-findings {
                        text-align: center;
                        padding: 60px 30px;
                        color: var(--text-color);
                    }

                    .no-findings i {
                        font-size: 3rem;
                        color: var(--border-color);
                        margin-bottom: 20px;
                    }

                    .footer {
                        background: white;
                        border-radius: var(--border-radius);
                        padding: 20px 30px;
                        margin-top: 30px;
                        box-shadow: var(--shadow);
                        text-align: center;
                        color: var(--text-color);
                        font-size: 0.9rem;
                    }

                    .export-buttons {
                        display: flex;
                        gap: 10px;
                        justify-content: center;
                        margin-bottom: 15px;
                    }

                    .export-btn {
                        padding: 8px 16px;
                        border: none;
                        border-radius: var(--border-radius);
                        background: var(--secondary-color);
                        color: white;
                        cursor: pointer;
                        transition: background-color 0.3s;
                        font-size: 0.9rem;
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    }

                    .export-btn:hover {
                        background: #2980b9;
                    }

                    /* Advanced Responsive Design */

                    /* Extra Large Screens (1400px+) */
                    @media (min-width: 1400px) {
                        .container {
                            max-width: 1400px;
                            padding: 30px;
                        }

                        .stats-grid {
                            grid-template-columns: repeat(4, 1fr);
                        }

                        .finding {
                            padding: 30px 40px;
                        }

                        .header h1 {
                            font-size: 3rem;
                        }
                    }

                    /* Large Screens (1200px - 1399px) */
                    @media (min-width: 1200px) and (max-width: 1399px) {
                        .container {
                            max-width: 1200px;
                            padding: 25px;
                        }

                        .stats-grid {
                            grid-template-columns: repeat(4, 1fr);
                        }

                        .header h1 {
                            font-size: 2.8rem;
                        }
                    }

                    /* Medium-Large Screens (992px - 1199px) */
                    @media (min-width: 992px) and (max-width: 1199px) {
                        .container {
                            max-width: 992px;
                            padding: 20px;
                        }

                        .stats-grid {
                            grid-template-columns: repeat(2, 1fr);
                        }

                        .header h1 {
                            font-size: 2.6rem;
                        }

                        .controls {
                            flex-wrap: wrap;
                        }

                        .search-box {
                            min-width: 300px;
                        }
                    }

                    /* Medium Screens (768px - 991px) - Tablets */
                    @media (min-width: 768px) and (max-width: 991px) {
                        .container {
                            max-width: 768px;
                            padding: 20px 15px;
                        }

                        .header {
                            padding: 25px 20px;
                        }

                        .header h1 {
                            font-size: 2.3rem;
                            gap: 12px;
                        }

                        .controls {
                            flex-direction: column;
                            gap: 20px;
                        }

                        .search-box {
                            min-width: 100%;
                        }

                        .filter-buttons {
                            justify-content: center;
                            flex-wrap: wrap;
                        }

                        .stats-grid {
                            grid-template-columns: repeat(2, 1fr);
                            gap: 15px;
                        }

                        .stat-card {
                            padding: 15px;
                        }

                        .stat-number {
                            font-size: 1.8rem;
                        }

                        .severity-breakdown {
                            justify-content: center;
                            gap: 10px;
                        }

                        .severity-stat {
                            padding: 8px 12px;
                            font-size: 0.9rem;
                        }

                        .finding {
                            padding: 20px 15px;
                        }

                        .finding-icon {
                            width: 35px;
                            height: 35px;
                            font-size: 1.1rem;
                        }

                        .finding-title {
                            font-size: 1rem;
                        }

                        .control-badge {
                            font-size: 0.75rem;
                            padding: 1px 6px;
                        }

                        .finding-meta {
                            gap: 12px;
                        }

                        .code-snippet {
                            font-size: 0.8rem;
                            padding: 12px;
                        }
                    }

                    /* Small Screens (576px - 767px) - Large Phones */
                    @media (min-width: 576px) and (max-width: 767px) {
                        .container {
                            padding: 15px 10px;
                        }

                        .header {
                            padding: 20px 15px;
                            margin-bottom: 20px;
                        }

                        .header h1 {
                            font-size: 2rem;
                            flex-direction: column;
                            gap: 8px;
                        }

                        .header .subtitle {
                            font-size: 1rem;
                        }

                        .controls {
                            flex-direction: column;
                            padding: 15px;
                            gap: 15px;
                        }

                        .search-box input {
                            padding: 10px 40px 10px 12px;
                            font-size: 16px; /* Prevents zoom on iOS */
                        }

                        .filter-buttons {
                            justify-content: center;
                            gap: 8px;
                        }

                        .filter-btn {
                            padding: 6px 12px;
                            font-size: 11px;
                            border-radius: 15px;
                        }

                        .summary {
                            padding: 20px 15px;
                            margin-bottom: 20px;
                        }

                        .summary h2 {
                            font-size: 1.3rem;
                            margin-bottom: 15px;
                        }

                        .stats-grid {
                            grid-template-columns: repeat(2, 1fr);
                            gap: 12px;
                        }

                        .stat-card {
                            padding: 12px;
                        }

                        .stat-number {
                            font-size: 1.6rem;
                        }

                        .stat-label {
                            font-size: 0.8rem;
                        }

                        .severity-breakdown {
                            justify-content: center;
                            gap: 8px;
                        }

                        .severity-stat {
                            padding: 6px 10px;
                            font-size: 0.85rem;
                            border-radius: 15px;
                        }

                        .severity-header {
                            padding: 15px 20px;
                            font-size: 1.1rem;
                        }

                        .severity-header .count {
                            font-size: 0.8rem;
                            padding: 3px 8px;
                        }

                        .finding {
                            padding: 15px 12px;
                        }

                        .finding-header {
                            flex-direction: row;
                            gap: 12px;
                        }

                        .finding-icon {
                            width: 32px;
                            height: 32px;
                            font-size: 1rem;
                        }

                        .finding-title {
                            font-size: 0.95rem;
                            flex-direction: column;
                            align-items: flex-start;
                            gap: 5px;
                        }

                        .control-badge {
                            font-size: 0.7rem;
                            padding: 1px 5px;
                        }

                        .finding-meta {
                            flex-direction: column;
                            gap: 6px;
                            font-size: 0.85rem;
                        }

                        .finding-description {
                            font-size: 0.9rem;
                            margin-bottom: 12px;
                        }

                        .finding-remediation {
                            padding: 12px;
                        }

                        .remediation-title {
                            font-size: 0.9rem;
                            margin-bottom: 6px;
                        }

                        .code-snippet {
                            font-size: 0.75rem;
                            padding: 10px;
                            margin: 10px 0;
                        }

                        .export-buttons {
                            flex-direction: column;
                            gap: 8px;
                        }

                        .export-btn {
                            padding: 10px 16px;
                            font-size: 0.85rem;
                        }
                    }

                    /* Extra Small Screens (up to 575px) - Small Phones */
                    @media (max-width: 575px) {
                        .container {
                            padding: 10px 5px;
                        }

                        .header {
                            padding: 15px 10px;
                            margin-bottom: 15px;
                        }

                        .header h1 {
                            font-size: 1.8rem;
                            flex-direction: column;
                            gap: 5px;
                        }

                        .header .subtitle {
                            font-size: 0.9rem;
                        }

                        .controls {
                            padding: 12px;
                            gap: 12px;
                        }

                        .search-box input {
                            padding: 8px 35px 8px 10px;
                            font-size: 16px; /* Prevents zoom on iOS */
                        }

                        .search-box i {
                            right: 10px;
                        }

                        .filter-buttons {
                            gap: 6px;
                        }

                        .filter-btn {
                            padding: 5px 10px;
                            font-size: 10px;
                            border-radius: 12px;
                        }

                        .summary {
                            padding: 15px 10px;
                            margin-bottom: 15px;
                        }

                        .summary h2 {
                            font-size: 1.2rem;
                            margin-bottom: 12px;
                        }

                        .stats-grid {
                            grid-template-columns: 1fr;
                            gap: 10px;
                        }

                        .stat-card {
                            padding: 10px;
                            text-align: left;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                        }

                        .stat-number {
                            font-size: 1.4rem;
                        }

                        .stat-label {
                            font-size: 0.75rem;
                        }

                        .severity-breakdown {
                            flex-direction: column;
                            gap: 6px;
                        }

                        .severity-stat {
                            padding: 5px 8px;
                            font-size: 0.8rem;
                            border-radius: 12px;
                            justify-content: center;
                        }

                        .severity-header {
                            padding: 12px 15px;
                            font-size: 1rem;
                        }

                        .severity-header .count {
                            font-size: 0.75rem;
                            padding: 2px 6px;
                        }

                        .finding {
                            padding: 12px 8px;
                        }

                        .finding-header {
                            flex-direction: column;
                            align-items: flex-start;
                            gap: 8px;
                        }

                        .finding-icon {
                            width: 28px;
                            height: 28px;
                            font-size: 0.9rem;
                            align-self: flex-start;
                        }

                        .finding-content {
                            width: 100%;
                        }

                        .finding-title {
                            font-size: 0.9rem;
                            flex-direction: column;
                            align-items: flex-start;
                            gap: 4px;
                        }

                        .control-badge {
                            font-size: 0.65rem;
                            padding: 1px 4px;
                        }

                        .finding-meta {
                            flex-direction: column;
                            gap: 4px;
                            font-size: 0.8rem;
                        }

                        .meta-item {
                            gap: 3px;
                        }

                        .meta-item i {
                            font-size: 0.75rem;
                        }

                        .finding-description {
                            font-size: 0.85rem;
                            margin-bottom: 10px;
                            line-height: 1.4;
                        }

                        .finding-remediation {
                            padding: 10px;
                        }

                        .remediation-title {
                            font-size: 0.85rem;
                            margin-bottom: 5px;
                        }

                        .code-snippet {
                            font-size: 0.7rem;
                            padding: 8px;
                            margin: 8px 0;
                            line-height: 1.3;
                        }

                        .footer {
                            padding: 15px 10px;
                            margin-top: 20px;
                        }

                        .export-buttons {
                            flex-direction: column;
                            gap: 6px;
                        }

                        .export-btn {
                            padding: 8px 12px;
                            font-size: 0.8rem;
                        }

                        .footer p {
                            font-size: 0.75rem;
                            line-height: 1.3;
                        }
                    }

                    /* Landscape Orientation Optimizations */
                    @media (max-height: 500px) and (orientation: landscape) {
                        .header {
                            padding: 10px 20px;
                            margin-bottom: 10px;
                        }

                        .header h1 {
                            font-size: 1.5rem;
                            margin-bottom: 5px;
                        }

                        .header .subtitle {
                            font-size: 0.8rem;
                        }

                        .controls {
                            padding: 10px;
                        }

                        .summary {
                            padding: 15px;
                            margin-bottom: 10px;
                        }

                        .stats-grid {
                            grid-template-columns: repeat(4, 1fr);
                            gap: 8px;
                        }

                        .stat-card {
                            padding: 8px;
                        }

                        .stat-number {
                            font-size: 1.2rem;
                        }

                        .severity-breakdown {
                            gap: 5px;
                        }

                        .severity-stat {
                            padding: 4px 8px;
                            font-size: 0.75rem;
                        }

                        .finding {
                            padding: 10px;
                        }

                        .findings-list {
                            max-height: 300px;
                        }
                    }

                    /* High DPI / Retina Display Optimizations */
                    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
                        .finding-icon {
                            border: 0.5px solid rgba(255, 255, 255, 0.1);
                        }

                        .code-snippet {
                            border-width: 0.5px;
                        }

                        .control-badge {
                            border: 0.5px solid rgba(255, 255, 255, 0.2);
                        }
                    }

                    /* Reduced Motion Preferences */
                    @media (prefers-reduced-motion: reduce) {
                        * {
                            animation-duration: 0.01ms !important;
                            animation-iteration-count: 1 !important;
                            transition-duration: 0.01ms !important;
                        }

                        .severity-header .toggle-icon {
                            transition: none;
                        }

                        .findings-list {
                            transition: none;
                        }
                    }

                    /* Dark Mode Support (if system preference) */
                    @media (prefers-color-scheme: dark) {
                        :root {
                            --light-bg: #2d3748;
                            --border-color: #4a5568;
                            --text-color: #e2e8f0;
                        }

                        body {
                            background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
                        }

                        .header, .controls, .summary, .findings-container, .footer {
                            background: #1a202c;
                            color: #e2e8f0;
                        }

                        .search-box input {
                            background: #2d3748;
                            color: #e2e8f0;
                            border-color: #4a5568;
                        }

                        .search-box input:focus {
                            border-color: var(--secondary-color);
                        }

                        .filter-btn {
                            background: #2d3748;
                            color: #e2e8f0;
                        }

                        .stat-card {
                            background: #2d3748;
                        }

                        .finding:hover {
                            background: #2d3748;
                        }

                        .finding-remediation {
                            background: #2d3748;
                        }
                    }

                    /* Touch Device Optimizations */
                    .touch-device .filter-btn {
                        min-height: 44px; /* iOS recommended touch target size */
                        min-width: 44px;
                    }

                    .touch-device .severity-header {
                        min-height: 48px;
                        cursor: pointer;
                        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
                    }

                    .touch-device .export-btn {
                        min-height: 44px;
                        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
                    }

                    /* Focus Management */
                    .filter-btn:focus,
                    .severity-header:focus,
                    .export-btn:focus {
                        outline: 2px solid var(--secondary-color);
                        outline-offset: 2px;
                    }

                    .search-box input:focus {
                        outline: 2px solid var(--secondary-color);
                        outline-offset: 2px;
                    }

                    /* Screen Reader Only Content */
                    .sr-only {
                        position: absolute;
                        width: 1px;
                        height: 1px;
                        padding: 0;
                        margin: -1px;
                        overflow: hidden;
                        clip: rect(0, 0, 0, 0);
                        white-space: nowrap;
                        border: 0;
                    }

                    /* Loading States */
                    .loading {
                        opacity: 0.6;
                        pointer-events: none;
                    }

                    .loading::after {
                        content: '';
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        width: 20px;
                        height: 20px;
                        margin: -10px 0 0 -10px;
                        border: 2px solid var(--border-color);
                        border-top-color: var(--secondary-color);
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                    }

                    @keyframes spin {
                        to { transform: rotate(360deg); }
                    }

                    /* Smooth Scrolling */
                    html {
                        scroll-behavior: smooth;
                    }

                    /* Selection Styling */
                    ::selection {
                        background: var(--secondary-color);
                        color: white;
                    }

                    ::-moz-selection {
                        background: var(--secondary-color);
                        color: white;
                    }

                    /* Scrollbar Styling for Webkit Browsers */
                    .findings-list::-webkit-scrollbar {
                        width: 8px;
                    }

                    .findings-list::-webkit-scrollbar-track {
                        background: var(--light-bg);
                        border-radius: 4px;
                    }

                    .findings-list::-webkit-scrollbar-thumb {
                        background: var(--border-color);
                        border-radius: 4px;
                    }

                    .findings-list::-webkit-scrollbar-thumb:hover {
                        background: var(--text-color);
                    }

                    /* Print Optimizations */
                    @media print {
                        body {
                            background: white !important;
                            color: black !important;
                        }

                        .container {
                            max-width: none !important;
                            padding: 0 !important;
                        }

                        .controls, .export-buttons {
                            display: none !important;
                        }

                        .findings-list {
                            max-height: none !important;
                            overflow: visible !important;
                        }

                        .finding {
                            break-inside: avoid;
                            page-break-inside: avoid;
                            border: 1px solid #ccc !important;
                            margin-bottom: 10px !important;
                        }

                        .severity-header {
                            break-after: avoid;
                            page-break-after: avoid;
                        }

                        .code-snippet {
                            background: #f5f5f5 !important;
                            color: black !important;
                            border: 1px solid #ccc !important;
                        }

                        .header h1 {
                            color: black !important;
                        }

                        .summary h2 {
                            color: black !important;
                        }

                        /* Ensure all text is black for printing */
                        * {
                            color: black !important;
                            background: white !important;
                        }

                        .critical, .high, .medium, .low {
                            background: white !important;
                            color: black !important;
                            border: 2px solid black !important;
                        }

                        .finding-icon {
                            background: white !important;
                            color: black !important;
                            border: 2px solid black !important;
                        }
                    }

                    /* Accessibility Enhancements */
                    @media (prefers-contrast: high) {
                        :root {
                            --border-color: #000;
                            --text-color: #000;
                        }

                        .finding {
                            border: 2px solid #000;
                        }

                        .filter-btn {
                            border-width: 2px;
                        }

                        .search-box input {
                            border-width: 2px;
                        }
                    }

                    /* Large Text Support */
                    @media (min-resolution: 120dpi) {
                        body {
                            font-size: 18px;
                        }

                        .filter-btn {
                            font-size: 14px;
                            padding: 10px 18px;
                        }

                        .search-box input {
                            font-size: 16px;
                            padding: 14px 50px 14px 18px;
                        }
                    }

                    @media print {
                        body {
                            background: white;
                        }

                        .container {
                            max-width: none;
                            padding: 0;
                        }

                        .controls, .export-buttons {
                            display: none;
                        }

                        .findings-list {
                            max-height: none !important;
                        }

                        .finding {
                            break-inside: avoid;
                            page-break-inside: avoid;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>
                            <i class="fas fa-shield-alt"></i>
                            Security Findings Report
                        </h1>
                        <div class="subtitle">Infrastructure as Code Security Analysis</div>
                    </div>

                    <div class="controls" role="toolbar" aria-label="Search and filter controls">
                        <div class="search-box">
                            <label for="searchInput" class="sr-only">Search findings</label>
                            <input type="text"
                                   id="searchInput"
                                   placeholder="Search findings by description, file, or control ID..."
                                   aria-label="Search findings by description, file, or control ID"
                                   autocomplete="off"
                                   spellcheck="false">
                            <i class="fas fa-search" aria-hidden="true"></i>
                        </div>
                        <div class="filter-buttons" role="group" aria-label="Filter by severity level">
                            <button class="filter-btn all active"
                                    data-severity="all"
                                    aria-pressed="true"
                                    aria-label="Show all findings">
                                <i class="fas fa-list" aria-hidden="true"></i> All
                            </button>
                            <button class="filter-btn critical"
                                    data-severity="critical"
                                    aria-pressed="false"
                                    aria-label="Show only critical severity findings">
                                <span aria-hidden="true">🔴</span> Critical
                            </button>
                            <button class="filter-btn high"
                                    data-severity="high"
                                    aria-pressed="false"
                                    aria-label="Show only high severity findings">
                                <span aria-hidden="true">🟠</span> High
                            </button>
                            <button class="filter-btn medium"
                                    data-severity="medium"
                                    aria-pressed="false"
                                    aria-label="Show only medium severity findings">
                                <span aria-hidden="true">🟡</span> Medium
                            </button>
                            <button class="filter-btn low"
                                    data-severity="low"
                                    aria-pressed="false"
                                    aria-label="Show only low severity findings">
                                <span aria-hidden="true">🔵</span> Low
                            </button>
                        </div>
                    </div>

                    
                <div class="summary">
                    <h2><i class="fas fa-chart-bar"></i> Executive Summary</h2>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">53</div>
                            <div class="stat-label">Total Findings</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">13</div>
                            <div class="stat-label">Files Affected</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">29</div>
                            <div class="stat-label">High Priority Issues</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">13</div>
                            <div class="stat-label">Security Controls</div>
                        </div>
                    </div>
                    <div class="severity-breakdown">
                        <div class="severity-stat critical">
                            <span>🔴</span>
                            <span><strong>2</strong> Critical</span>
                        </div>
                        <div class="severity-stat high">
                            <span>🟠</span>
                            <span><strong>27</strong> High</span>
                        </div>
                        <div class="severity-stat medium">
                            <span>🟡</span>
                            <span><strong>18</strong> Medium</span>
                        </div>
                        <div class="severity-stat low">
                            <span>🔵</span>
                            <span><strong>6</strong> Low</span>
                        </div>
                    </div>
                </div>

                    <div class="findings-container">
                        
                    <section class="severity-group" data-severity="critical" aria-labelledby="severity-critical-header">
                        <h3 class="severity-header critical"
                            id="severity-critical-header"
                            role="button"
                            tabindex="0"
                            aria-expanded="true"
                            aria-controls="severity-critical-list"
                            aria-label="CRITICAL severity findings section, 2 findings">
                            <span><span aria-hidden="true">🔴</span> CRITICAL Severity Findings</span>
                            <span class="count" aria-label="2 findings">2</span>
                            <i class="fas fa-chevron-down toggle-icon" aria-hidden="true"></i>
                        </h3>
                        <div class="findings-list"
                             id="severity-critical-list"
                             role="region"
                             aria-labelledby="severity-critical-header">
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-critical-7954244557532711994"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon critical"
                                         role="img"
                                         aria-label="CRITICAL severity indicator">
                                        <i class="fas fa-exclamation-triangle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-critical-7954244557532711994">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-2">NS-2</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">keyvault.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">17</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The Key Vault resource has &#x27;networkAcls.defaultAction&#x27; set to &#x27;Allow&#x27;, meaning it will allow access from all public networks unless specifically denied via rules. This exposes the Key Vault to the public internet, violating the ASB requirement to secure all public endpoints.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Set &#x27;networkAcls.defaultAction&#x27; to &#x27;Deny&#x27; and explicitly whitelist only required IP addresses and vNets using &#x27;ipRules&#x27; and &#x27;virtualNetworkRules&#x27; to restrict access.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-critical--4674574599379220216"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon critical"
                                         role="img"
                                         aria-label="CRITICAL severity indicator">
                                        <i class="fas fa-exclamation-triangle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-critical--4674574599379220216">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID DP-3">DP-3</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">operational-insights.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">79</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The template outputs &#x27;appInsightsInstrumentationKey&#x27;, which may be sensitive, as an output parameter. The Application Insights Instrumentation Key is considered sensitive, as exposure can allow unauthorized telemetry data ingestion and leakage of monitoring context.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Remove the &#x27;appInsightsInstrumentationKey&#x27; output or use Azure Key Vault to securely store and reference instrumentation keys. Do not output or expose instrumentation keys in templates or logs.</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <section class="severity-group" data-severity="high" aria-labelledby="severity-high-header">
                        <h3 class="severity-header high"
                            id="severity-high-header"
                            role="button"
                            tabindex="0"
                            aria-expanded="true"
                            aria-controls="severity-high-list"
                            aria-label="HIGH severity findings section, 27 findings">
                            <span><span aria-hidden="true">🟠</span> HIGH Severity Findings</span>
                            <span class="count" aria-label="27 findings">27</span>
                            <i class="fas fa-chevron-down toggle-icon" aria-hidden="true"></i>
                        </h3>
                        <div class="findings-list"
                             id="severity-high-list"
                             role="region"
                             aria-labelledby="severity-high-header">
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high-1286305974581310339"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high-1286305974581310339">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-5">NS-5</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">app-config.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">7</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The App Configuration resource (Microsoft.AppConfiguration/configurationStores) is deployed without Private Endpoint configuration. By default, it will be accessible over a public endpoint, increasing the risk of unauthorized access.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Configure a Private Endpoint for the App Configuration resource to ensure access is restricted to internal networks only and not exposed to the public internet. Add a &#x27;privateEndpointConnections&#x27; resource referencing a Private Endpoint in the Bicep template.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high--1710729889623337056"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high--1710729889623337056">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-2">NS-2</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">app-config.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">7</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The App Configuration resource is created without restricting public network access. This exposes public endpoints that may be accessible from the internet, violating secure access best practices.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Restrict public network access by setting the &#x27;publicNetworkAccess&#x27; property to &#x27;Disabled&#x27; in the App Configuration resource properties, and depend solely on private endpoints for connectivity.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high-9071275585452152093"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high-9071275585452152093">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID DP-3">DP-3</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">app-config.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">20</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> Key-value pairs for App Configuration are parameterized, but the template does not integrate with Azure Key Vault for storing sensitive application settings (e.g., secrets, API keys). Storing secrets directly in configuration or parameters can cause sensitive information disclosure.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Store secrets and sensitive values in Azure Key Vault and reference them securely from your application code, instead of including them as App Configuration key-values. Avoid placing secrets in IaC parameters or in App Configuration unless Key Vault references are used.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high-7317094655760669320"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high-7317094655760669320">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-5">NS-5</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">event-grid.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">15</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> Event subscriptions are configured to deliver events directly to a storage queue via the &#x27;StorageQueue&#x27; endpoint type, but there is no indication that Private Endpoints are being used for secure communication between Event Grid and the storage account. This may expose the storage queue endpoint to the public internet.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Update the target storage account to use Private Endpoints, and configure Event Grid system topics/subscriptions to send events via the private endpoint, ensuring all event traffic remains on the trusted Microsoft backbone.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high--5404787128448585949"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high--5404787128448585949">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID DP-3">DP-3</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">event-grid.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">2</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The storage account resource IDs (storageFuncId, storageFuzzId, storageCorpusIds[].id) and possibly queue names are accepted as plain string parameters. There is no indication these values are protected, nor whether secrets (such as connection strings, access keys) will be handled securely elsewhere.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Where sensitive data such as keys or connection strings are used, store them in Azure Key Vault and reference them securely. Ensure that the Bicep template does not require secrets as parameters, but retrieves them securely at deployment time.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high-4103234854228603783"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high-4103234854228603783">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-1">NS-1</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">function-settings.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">28</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The Storage Account resource &#x27;funcStorage&#x27; is declared as an existing resource, but there is no evidence in this template that appropriate network protections (such as NSGs or Azure Firewall rules) are applied. This potentially leaves the storage account exposed to the public internet.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Ensure the referenced Storage Account (funcStorage) is configured with network security controls. Restrict access using private endpoints, service endpoints, or firewall rules to limit exposure only to required services/networks.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high--6865484140998836306"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high--6865484140998836306">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-2">NS-2</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">function-settings.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">53</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> App Service function exposes endpoints by default and there is no evidence of access restriction (e.g., IP restrictions, VNet integration, or private endpoints) being configured. Public accessibility without restrictions increases the attack surface.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Configure access restrictions on the App Service to allow access only from trusted networks, IPs, or via private endpoints and service endpoints. Consider enabling Azure App Service Private Endpoint where possible.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high--3829220217153935713"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high--3829220217153935713">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID DP-3">DP-3</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">function-settings.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">49</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> Sensitive information such as &#x27;APPINSIGHTS_INSTRUMENTATIONKEY&#x27; is set directly from parameters. There is no evidence that secrets such as instrumentation keys or connection strings are sourced from Azure Key Vault or another secure secret store.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Move all sensitive values, such as App Insights keys and connection strings, to Azure Key Vault. Reference these secrets from the template using secure mechanisms to avoid exposure in configuration or code.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high-7195010664625482560"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high-7195010664625482560">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-5">NS-5</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">function-settings.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">28</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> Network access to the Storage Account (&#x27;funcStorage&#x27;) is not protected by a Private Endpoint or restricted to specific networks. Without private endpoints, sensitive data in storage may be accessible via public networks.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Integrate the Storage Account with an Azure Private Endpoint and disable public network access to prevent data exposure.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high-7737213461647848552"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high-7737213461647848552">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-1">NS-1</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">function.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">54</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The Azure Storage Account (logs_storage) is referenced as an existing resource, but there is no validation or enforcement ensuring that it is protected by Network Security Groups (NSGs) or Azure Firewall. Storage accounts are often public by default, which can expose sensitive log data if not properly restricted.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Ensure the referenced storage account has access restrictions enforced using NSGs or Azure Firewall and allow access only from trusted networks (such as the function app&#x27;s subnet or private endpoints).</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high--4625147777717143195"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high--4625147777717143195">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID DP-3">DP-3</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">function.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">87</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The storage account SAS token is generated and interpolated directly into the log configuration (&#x27;sasUrl&#x27;) for diagnostic logs. There is no use of Azure Key Vault to manage or reference sensitive data like SAS tokens, increasing risk of sensitive information exposure.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Use Azure Key Vault to securely store and reference sensitive tokens and connection strings. Avoid direct interpolation of secrets in resource properties.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high--7012021148038307520"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high--7012021148038307520">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-5">NS-5</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">function.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">62</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The function app configures VNet integration but does not make use of Azure Private Endpoints to securely connect to the storage account or limit its exposure to the public internet.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Implement Private Endpoints for both the storage account and the function app to ensure that data flows privately within the Azure backbone and is not exposed to the public internet.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high--6251265674435853511"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high--6251265674435853511">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-1">NS-1</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">hub-network.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">2</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The virtual network &#x27;hub-vnet&#x27; and its subnet &#x27;hub-subnet&#x27; are defined without any associated Network Security Group (NSG) or Azure Firewall to control network traffic, leaving resources unprotected from unauthorized access. This violates ASB control NS-1, which requires protecting resources using NSGs or Azure Firewall.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Associate an NSG with the subnet, configuring restrictive rules to permit only necessary inbound and outbound traffic. If advanced inspection is needed, deploy Azure Firewall and direct traffic through it using appropriate routes.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high--3225699959264957128"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high--3225699959264957128">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-3">NS-3</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">hub-network.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">14</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The &#x27;hub-subnet&#x27; is not associated with any Network Security Group (NSG), which is required to enforce explicit access controls on network traffic. This violates ASB control NS-3 requiring the use of NSGs to control traffic into and out of subnets.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Create and associate an NSG with the &#x27;hub-subnet&#x27;, defining inbound and outbound security rules to restrict traffic to only what is necessary for applications and services in this subnet.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high--5225640588749726956"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high--5225640588749726956">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID DP-3">DP-3</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">instance-config.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">9</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> Sensitive information such as Azure AD tenant IDs, client IDs, allowed tenants, and domain details are passed into the template as parameters (specificConfig) and surfaced as plain object values or outputs, without guaranteed use of Azure Key Vault for secure storage or referencing. Storing or handling secrets and sensitive configuration data in plain text or output variables increases the risk of disclosure.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Ensure any sensitive values (e.g., tenant_id, cli_client_id, allowed_aad_tenants) are referenced from Azure Key Vault or securely injected at runtime (e.g., using Key Vault references or secure parameter mechanisms). Remove direct outputs of sensitive fields unless strictly necessary and protected.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high--282089264736356908"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high--282089264736356908">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-2">NS-2</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">ip-rules.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">2</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The &#x27;corpNetIps&#x27; variable includes multiple large public IP ranges (e.g., &#x27;*******/8&#x27;, &#x27;********/8&#x27;) that vastly exceed the intended principle of least privilege for network access. Allowing large public IP blocks increases attack surface for resources using these rules, violating the benchmark&#x27;s mandate to minimize public exposure.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Restrict IP allow lists to the smallest possible range required for business needs. Instead of large public IPv4 ranges, specify only necessary, trusted IP addresses or tightly scoped ranges. Review and reduce allowed IP addresses to the minimum required.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high-848265778026899267"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high-848265778026899267">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-1">NS-1</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">keyvault.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">17</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> Key Vault is not sufficiently protected with network restrictions; &#x27;defaultAction&#x27; is set to &#x27;Allow&#x27;, bypassing network controls and exposing the resource to the internet. Critical resources must be protected by network security controls such as NSGs or firewalls.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Use &#x27;networkAcls.defaultAction&#x27; set to &#x27;Deny&#x27; and ensure access is only allowed through specific vNet or IP rules. Implement further restrictions in associated subnets using NSGs where possible.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high-663441490521192748"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high-663441490521192748">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-1">NS-1</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">scaleset-networks.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">1</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> No Network Security Groups (NSGs) or Azure Firewall resources are defined on the virtual network or subnets to protect resources. This exposes the subnet and its resources to potentially unfiltered network traffic.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Define and associate an NSG with the scale set subnet to explicitly allow/deny required inbound and outbound traffic according to the least privilege principle. Alternatively, deploy and configure Azure Firewall to protect the network perimeter.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high--2071434967533793644"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high--2071434967533793644">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-3">NS-3</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">scaleset-networks.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">1</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The template does not deploy or associate any Network Security Groups (NSGs) with the virtual network or subnet. NSGs are required to control and restrict inbound and outbound traffic to resources within the subnet (e.g., scale set VMs).
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Associate an NSG resource with the &#x27;scaleset&#x27; subnet, defining rules to permit only required traffic and deny all other traffic.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high-8788423207487961410"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high-8788423207487961410">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-2">NS-2</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">scaleset-networks.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">7</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> A public IP address is provisioned (&#x27;scaleset-outbound-ip&#x27;) and attached to the NAT gateway for outbound communication. There is no reference to NSG or firewall controls, which could leave public endpoints unprotected.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Ensure that inbound traffic to the public IP is blocked via NSG or firewall rules unless explicitly required, and limit the exposure of public endpoints as much as possible.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high-3235013193636305808"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high-3235013193636305808">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-1">NS-1</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">server-farms.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">1</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> There is no evidence of network security controls such as Network Security Groups (NSGs) or Azure Firewall being applied to protect the App Service or referenced Key Vault resources. This increases the attack surface and can allow unauthorized network access to sensitive resources.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Implement appropriate NSGs or Azure Firewall rules to restrict access to the App Service and Key Vault resources to only required sources and services, in line with the principle of least privilege.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high--5320846332021322091"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high--5320846332021322091">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-2">NS-2</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">server-farms.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">1</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The template does not enforce restriction of public endpoints or specify private networking for the App Service or referenced Key Vaults. App Services, by default, are publicly accessible unless configured otherwise. Exposing resources to the public internet unnecessarily increases exposure to threats.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Configure App Service access restrictions to explicitly deny all except trusted IP addresses or subnets. Use private endpoints or service endpoints for internal access where possible. Similarly, restrict Key Vault public access by enabling firewall rules or private endpoint connections.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high--7031821263864042137"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high--7031821263864042137">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID DP-3">DP-3</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">server-farms.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">98</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The &#x27;serverFarms_AntMDS_CERTIFICATE_PASSWORD_GENEVACERT&#x27; setting is configured with an empty string as the certificate password. This could lead to misconfiguration where a password is expected for certificates, potentially resulting in weak protection or application/service failures.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Store sensitive certificate passwords securely in Azure Key Vault and reference them as secrets. Never use empty or hardcoded values for sensitive credential parameters.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high-7225267624436668225"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high-7225267624436668225">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID IM-6">IM-6</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">server-farms.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">1</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> There is no explicit assignment or enforcement of RBAC roles for either App Service or Key Vault within the template. Not limiting privileges according to least privilege may lead to excessive permissions and increased risk of misuse or compromise.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Explicitly assign required RBAC roles (at a minimum, &quot;Key Vault Reader/Secrets User&quot; for the managed identity) either within this template or as a separate automation step, scoped as narrowly as possible.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high-1368012191068322098"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high-1368012191068322098">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-2">NS-2</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">signalR.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">5</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The SignalR resource is deployed with the default configuration, which exposes a public endpoint by default. Not specifying network ACLs, private endpoints, or restricted access may leave the service accessible from the public internet, increasing the attack surface.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Restrict public access to the SignalR service by configuring IP ACLs to trusted ranges, enabling private endpoints, or integrating with Azure Virtual Network service endpoints. Explicitly define &#x27;networkAcls&#x27; to restrict access as appropriate.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high--8168741583741088580"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high--8168741583741088580">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-2">NS-2</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">storage-accounts.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">24</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> Storage accounts are configured with `networkAcls.defaultAction = &#x27;Allow&#x27;`, which allows access to the storage account from any network unless explicitly blocked. This creates a public endpoint for the storage account, violating the principle of securing public endpoints.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Set `networkAcls.defaultAction` to &#x27;Deny&#x27;, and only explicitly allow trusted IPs or virtual network rules. This will ensure that storage accounts are not exposed to the public internet.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-high--1207827060454077101"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon high"
                                         role="img"
                                         aria-label="HIGH severity indicator">
                                        <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-high--1207827060454077101">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-1">NS-1</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">storage-accounts.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">24</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> `networkAcls.bypass` includes &#x27;AzureServices&#x27;, &#x27;Logging&#x27;, and &#x27;Metrics&#x27;, which can allow privileged Azure service traffic to the storage account. Overuse of bypass options may permit excessive or unnecessary access, potentially exposing sensitive data.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Restrict the `bypass` setting to only the services necessary for your application. Remove &#x27;AzureServices&#x27; unless there&#x27;s a strict business requirement, or use managed identities to limit service access.</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <section class="severity-group" data-severity="medium" aria-labelledby="severity-medium-header">
                        <h3 class="severity-header medium"
                            id="severity-medium-header"
                            role="button"
                            tabindex="0"
                            aria-expanded="true"
                            aria-controls="severity-medium-list"
                            aria-label="MEDIUM severity findings section, 18 findings">
                            <span><span aria-hidden="true">🟡</span> MEDIUM Severity Findings</span>
                            <span class="count" aria-label="18 findings">18</span>
                            <i class="fas fa-chevron-down toggle-icon" aria-hidden="true"></i>
                        </h3>
                        <div class="findings-list"
                             id="severity-medium-list"
                             role="region"
                             aria-labelledby="severity-medium-header">
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-medium--2527215996569974177"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon medium"
                                         role="img"
                                         aria-label="MEDIUM severity indicator">
                                        <i class="fas fa-exclamation" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-medium--2527215996569974177">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-6">NS-6</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">event-grid.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">15</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> Event subscription destination references a storage queue without any enforcement of Virtual Network Service Endpoints. Without service endpoints, access to the storage account could be exposed to the public internet.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Restrict access to the storage account using Virtual Network Service Endpoints so that only traffic from selected VNETs can access the queues.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-medium--8274961823520723366"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon medium"
                                         role="img"
                                         aria-label="MEDIUM severity indicator">
                                        <i class="fas fa-exclamation" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-medium--8274961823520723366">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID DP-2">DP-2</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">function-settings.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">53</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> There is no explicit specification of minimum TLS version for the App Service, which may permit legacy and insecure protocols for data in transit.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Set the &#x27;minTlsVersion&#x27; property on the App Service resource configuration to &#x27;1.2&#x27; or higher to enforce TLS 1.2+ for all client connections.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-medium--6769462210231744380"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon medium"
                                         role="img"
                                         aria-label="MEDIUM severity indicator">
                                        <i class="fas fa-exclamation" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-medium--6769462210231744380">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-2">NS-2</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">function.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">62</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The function app enables VNet integration by setting &#x27;virtualNetworkSubnetId&#x27;, but there is no explicit restriction of public inbound access using access restrictions or firewall rules. This can expose public endpoints unnecessarily.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Explicitly configure access restrictions or Azure Firewall rules on the function app to restrict public inbound access, allowing only trusted network locations.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-medium-935473083376627132"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon medium"
                                         role="img"
                                         aria-label="MEDIUM severity indicator">
                                        <i class="fas fa-exclamation" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-medium-935473083376627132">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID DP-2">DP-2</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">function.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">40</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> There is no explicit configuration of minimum TLS version for the function app, which should be set to at least TLS 1.2 to ensure encryption in transit.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Specify &#x27;minTlsVersion&#x27; in the function app&#x27;s siteConfig with a value of &#x27;1.2&#x27; or higher.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-medium--858500652982738391"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon medium"
                                         role="img"
                                         aria-label="MEDIUM severity indicator">
                                        <i class="fas fa-exclamation" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-medium--858500652982738391">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-2">NS-2</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">ip-rules.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">34</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The &#x27;ingestionServiceIps&#x27; array is present but empty, with a &#x27;// TODO&#x27; indicating placeholders for public IP authorization. This ambiguity may result in overly permissive rules or allow for insecure expansion in future. Any omission or later inclusion of broad/public ranges could expose endpoints in violation of secure access controls.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Explicitly define trusted, minimal IP addresses for &#x27;ingestionServiceIps&#x27;. Remove the TODO and use least-privilege principles when authorizing IPs. Regularly audit and update IP allowlists based on current requirements.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-medium--6046564063703297536"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon medium"
                                         role="img"
                                         aria-label="MEDIUM severity indicator">
                                        <i class="fas fa-exclamation" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-medium--6046564063703297536">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID DP-6">DP-6</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">keyvault.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">10</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The Key Vault resource does not define or enforce the use of a Key Vault encryption key with a customer-managed key (CMK). Relying solely on platform-managed keys may not be sufficient for sensitive or regulated data.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Configure the Key Vault to use customer-managed keys by specifying the &#x27;encryption&#x27; property with a user-assigned key, if organizational policy or compliance requires.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-medium--2967222657125072267"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon medium"
                                         role="img"
                                         aria-label="MEDIUM severity indicator">
                                        <i class="fas fa-exclamation" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-medium--2967222657125072267">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID DP-3">DP-3</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">keyvault.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">33</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> Secrets are created directly from parameter input using the &#x27;secrets&#x27; object, with potential risk of sensitive values being supplied inline rather than referenced securely (e.g., from Key Vault references or managed identities). This may expose secrets in deployment logs or histories.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Ensure &#x27;secrets&#x27; values are sourced securely—avoid passing sensitive values directly via parameter files or pipelines. Use secure input mechanisms such as Azure Key Vault references or secure pipeline variables for secrets.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-medium-6883756849878631237"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon medium"
                                         role="img"
                                         aria-label="MEDIUM severity indicator">
                                        <i class="fas fa-exclamation" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-medium-6883756849878631237">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID DP-3">DP-3</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">operational-insights.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">78</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The template outputs &#x27;appInsightsAppId&#x27;, which could reveal potentially sensitive operational information. While AppId is less sensitive than keys, its exposure should be minimized to prevent information disclosure.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Avoid outputting &#x27;appInsightsAppId&#x27; unless necessary for automation securely managed downstream. If required, consider limiting access to deployment outputs.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-medium--4028046255980968426"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon medium"
                                         role="img"
                                         aria-label="MEDIUM severity indicator">
                                        <i class="fas fa-exclamation" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-medium--4028046255980968426">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID DP-3">DP-3</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">operational-insights.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">80</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The template outputs &#x27;workspaceId&#x27; and &#x27;logAnalyticsWorkspaceId&#x27;, which reveal operational workspace resource identifiers. Directly exposing resource IDs may facilitate information gathering for attackers.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Restrict access to deployment outputs via RBAC and avoid outputting resource IDs unless required for downstream automation that is securely managed.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-medium-9117653135392117826"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon medium"
                                         role="img"
                                         aria-label="MEDIUM severity indicator">
                                        <i class="fas fa-exclamation" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-medium-9117653135392117826">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-4">NS-4</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">scaleset-networks.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">1</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> No Azure Firewall or third-party firewall has been deployed or referenced within the template to provide centralized traffic inspection and threat protection.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Implement an Azure Firewall resource on critical points of the network topology, particularly if internet connectivity is allowed, and integrate with your network architecture, using UDRs to direct traffic through the firewall where necessary.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-medium-4373179118274173114"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon medium"
                                         role="img"
                                         aria-label="MEDIUM severity indicator">
                                        <i class="fas fa-exclamation" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-medium-4373179118274173114">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-5">NS-5</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">scaleset-networks.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">31</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> Private endpoint policies are set to &#x27;Enabled&#x27;, but no private endpoints are defined for accessing Azure resources (e.g., Storage, Key Vault, SQL), missing an opportunity to restrict data access to only within the VNet.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Define appropriate private endpoints for backend services (such as Azure Storage, SQL, or Key Vault) that should not be accessible from public networks.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-medium--8765661432014944402"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon medium"
                                         role="img"
                                         aria-label="MEDIUM severity indicator">
                                        <i class="fas fa-exclamation" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-medium--8765661432014944402">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-5">NS-5</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">server-farms.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">1</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The template does not configure private endpoints for App Service or referenced Key Vault deployments. This omits a stronger layer of network isolation for sensitive resources.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Implement Azure Private Endpoints for App Service and Key Vault resources to ensure all traffic traverses secure, private Azure backbone network paths. Restrict public network access.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-medium-7841905191406165599"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon medium"
                                         role="img"
                                         aria-label="MEDIUM severity indicator">
                                        <i class="fas fa-exclamation" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-medium-7841905191406165599">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID IM-8">IM-8</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">server-farms.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">1</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> There is no usage or assignment of Managed Identities for the App Service resource, nor evidence of Managed Identity being used to access Key Vault secrets. Omitting managed identities increases the risk of credential exposure and complicates secure resource-to-resource authentication.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Enable a system-assigned or user-assigned Managed Identity for the App Service and use this identity for accessing Key Vault and other Azure resources. Update access policies or RBAC as appropriate.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-medium-8539611317302900414"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon medium"
                                         role="img"
                                         aria-label="MEDIUM severity indicator">
                                        <i class="fas fa-exclamation" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-medium-8539611317302900414">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID IM-1">IM-1</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">signalR.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">13</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> Azure SignalR authentication for clients is not specified, and by default, clients may use local authentication keys. Although &#x27;disableLocalAuth&#x27; is set to true, making Azure AD authentication more likely, the template does not explicitly show the use of Azure AD for identity and access management.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Ensure that SignalR is configured to use Azure Active Directory authentication for all access. Document or automate Azure AD integration in the template when possible.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-medium--6805400682243687171"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon medium"
                                         role="img"
                                         aria-label="MEDIUM severity indicator">
                                        <i class="fas fa-exclamation" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-medium--6805400682243687171">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-3">NS-3</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">storage-accounts.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">24</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> There is no evidence of Network Security Groups (NSGs) being used to protect access to the storage accounts. NSGs offer granular control over inbound and outbound traffic.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Implement NSGs on the subnets where the storage accounts are accessed (for example, the subnet referenced by `hubSubnetId`) to restrict access to only trusted IP ranges or resources.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-medium--6254977877153739511"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon medium"
                                         role="img"
                                         aria-label="MEDIUM severity indicator">
                                        <i class="fas fa-exclamation" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-medium--6254977877153739511">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID DP-1">DP-1</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">storage-accounts.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">25</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> Storage account resources do not specify customer-managed keys (CMKs) for encryption at rest. The default provider-managed keys are used, which may not meet stricter organizational or regulatory requirements.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Configure storage accounts with customer-managed keys from Azure Key Vault to enhance control over encryption at rest.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-medium-8297109956617828505"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon medium"
                                         role="img"
                                         aria-label="MEDIUM severity indicator">
                                        <i class="fas fa-exclamation" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-medium-8297109956617828505">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID DP-2">DP-2</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">storage-accounts.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">25</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> `supportsHttpsTrafficOnly` is set to `true`, enforcing HTTPS. However, the minimum TLS version is not specified, and may default to a less secure version.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Explicitly set the `minimumTlsVersion` property to &#x27;TLS1_2&#x27; or higher for all storage accounts to enforce strong encryption in transit.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-medium--1138958416940029532"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon medium"
                                         role="img"
                                         aria-label="MEDIUM severity indicator">
                                        <i class="fas fa-exclamation" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-medium--1138958416940029532">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID DP-3">DP-3</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">storage-accounts.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">90</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> CORS configuration for blob services allows all headers (`allowedHeaders: [&#x27;*&#x27;]` and `exposedHeaders: [&#x27;*&#x27;]`) and origins (configurable via `cors_origins`). If `cors_origins` includes broad or wildcard origins, this could disclose sensitive information.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Limit allowed headers and exposed headers to only what is strictly needed. Do not use wildcards unless necessary. Restrict `cors_origins` to trusted domains only.</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <section class="severity-group" data-severity="low" aria-labelledby="severity-low-header">
                        <h3 class="severity-header low"
                            id="severity-low-header"
                            role="button"
                            tabindex="0"
                            aria-expanded="true"
                            aria-controls="severity-low-list"
                            aria-label="LOW severity findings section, 6 findings">
                            <span><span aria-hidden="true">🔵</span> LOW Severity Findings</span>
                            <span class="count" aria-label="6 findings">6</span>
                            <i class="fas fa-chevron-down toggle-icon" aria-hidden="true"></i>
                        </h3>
                        <div class="findings-list"
                             id="severity-low-list"
                             role="region"
                             aria-labelledby="severity-low-header">
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-low--1926221304535081782"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon low"
                                         role="img"
                                         aria-label="LOW severity indicator">
                                        <i class="fas fa-info-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-low--1926221304535081782">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID DP-1">DP-1</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">function.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">54</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The storage account (logs_storage) is referenced as existing, but there is no enforcement or validation of encryption at rest (such as requiring infrastructure or customer-managed keys).
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Ensure that the storage account enabling diagnostic logs uses encryption at rest (and, if compliance requires, customer-managed keys).</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-low--1434705122185159214"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon low"
                                         role="img"
                                         aria-label="LOW severity indicator">
                                        <i class="fas fa-info-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-low--1434705122185159214">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID IM-8">IM-8</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">function.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">70</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The function app enables both SystemAssigned and UserAssigned managed identities but does not assign any RBAC roles or limit permissions to least privilege for these identities in the template.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Explicitly assign least privilege RBAC roles to the managed identities, granting only the necessary access for the function app to resources such as Key Vault, Storage, etc.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-low--1078794519046265973"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon low"
                                         role="img"
                                         aria-label="LOW severity indicator">
                                        <i class="fas fa-info-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-low--1078794519046265973">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID NS-6">NS-6</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">scaleset-networks.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">35</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> Service endpoints are deliberately left empty. While this may be intentional, it does not leverage service endpoints to secure traffic to Azure PaaS resources.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Add relevant service endpoints to the subnet (e.g., Microsoft.Storage, Microsoft.Sql) to secure connections from this subnet to those services, reducing the attack surface.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-low--6854488424820030414"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon low"
                                         role="img"
                                         aria-label="LOW severity indicator">
                                        <i class="fas fa-info-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-low--6854488424820030414">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID DP-1">DP-1</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">signalR.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">5</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> There is no explicit configuration for encryption at rest for the SignalR resource. While Azure SignalR Service encryption at rest is enabled by default, the template does not enforce or document this, and customers may miss validating this essential security measure.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Add documentation or configuration to verify or enforce encryption at rest for the SignalR resource, and monitor compliance with encryption policies.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-low--3273573217393083408"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon low"
                                         role="img"
                                         aria-label="LOW severity indicator">
                                        <i class="fas fa-info-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-low--3273573217393083408">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID DP-2">DP-2</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">signalR.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">5</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> The template does not specify a requirement for encryption in transit, such as enforcing TLS 1.2+ for client connections to SignalR. While the service defaults to secure protocols, the lack of explicit specification may cause ambiguity or compliance gaps.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Explicitly configure or document transport security requirements for SignalR, ensuring only TLS 1.2 or above is allowed for client and server communications.</div>
                                </div>
                            </div>
                            <article class="finding"
                                     role="article"
                                     aria-labelledby="finding-low--7419217361201526507"
                                     tabindex="0">
                                <header class="finding-header">
                                    <div class="finding-icon low"
                                         role="img"
                                         aria-label="LOW severity indicator">
                                        <i class="fas fa-info-circle" aria-hidden="true"></i>
                                    </div>
                                    <div class="finding-content">
                                        <h4 class="finding-title"
                                            id="finding-low--7419217361201526507">
                                            Security Issue Found
                                            <span class="control-badge"
                                                  role="note"
                                                  aria-label="Control ID IM-1">IM-1</span>
                                        </h4>
                                        <div class="finding-meta" role="group" aria-label="Finding location">
                                            <div class="meta-item">
                                                <i class="fas fa-file-code" aria-hidden="true"></i>
                                                <span>File: <span class="file-name">storage-accounts.bicep</span></span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                                <span>Line: <span class="line-number">27</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </header>
                                <div class="finding-description" role="region" aria-label="Issue description">
                                    <strong>Issue:</strong> Storage accounts do not explicitly enable Azure Active Directory (Azure AD) authentication for data plane access. This means access may rely on legacy shared keys or SAS tokens.
                                </div>
                                <div class="finding-remediation" role="region" aria-label="Recommended solution">
                                    <div class="remediation-title">
                                        <i class="fas fa-tools" aria-hidden="true"></i>
                                        Recommended Fix
                                    </div>
                                    <div class="remediation-content">Enable Azure AD authentication for storage accounts, and migrate from using shared key/SAS access where possible.</div>
                                </div>
                            </div>
                        </div>
                    </div>
                        <div id="noFindings" class="no-findings" style="display: none;">
                            <i class="fas fa-search"></i>
                            <h3>No findings match your search criteria</h3>
                            <p>Try adjusting your search terms or filters</p>
                        </div>
                    </div>

                    <div class="footer">
                        <div class="export-buttons">
                            <button class="export-btn" onclick="window.print()">
                                <i class="fas fa-print"></i> Print Report
                            </button>
                            <button class="export-btn" onclick="exportToJson()">
                                <i class="fas fa-download"></i> Export JSON
                            </button>
                        </div>
                        <p>Generated by IaC Guardian GPT • June 16, 2025 at 07:03 PM</p>
                        <p><strong>Disclaimer:</strong> This report is AI-generated and should be reviewed by security professionals.</p>
                    </div>
                </div>

                <script>
                    // Enhanced JavaScript for interactivity and mobile optimization
                    document.addEventListener('DOMContentLoaded', function() {
                        const searchInput = document.getElementById('searchInput');
                        const filterButtons = document.querySelectorAll('.filter-btn');
                        const severityHeaders = document.querySelectorAll('.severity-header');
                        const findings = document.querySelectorAll('.finding');
                        const noFindings = document.getElementById('noFindings');

                        let currentFilter = 'all';
                        let currentSearch = '';
                        let searchTimeout;
                        let isTouch = false;

                        // Detect touch device
                        if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
                            isTouch = true;
                            document.body.classList.add('touch-device');
                        }

                        // Enhanced search functionality with debouncing
                        searchInput.addEventListener('input', function() {
                            clearTimeout(searchTimeout);
                            searchTimeout = setTimeout(() => {
                                currentSearch = this.value.toLowerCase();
                                filterFindings();
                            }, 300); // Debounce for better performance
                        });

                        // Clear search on escape key
                        searchInput.addEventListener('keydown', function(e) {
                            if (e.key === 'Escape') {
                                this.value = '';
                                currentSearch = '';
                                filterFindings();
                            }
                        });

                        // Enhanced filter functionality with keyboard support
                        filterButtons.forEach((button, index) => {
                            button.addEventListener('click', function() {
                                setActiveFilter(this);
                            });

                            // Keyboard navigation for filters
                            button.addEventListener('keydown', function(e) {
                                if (e.key === 'Enter' || e.key === ' ') {
                                    e.preventDefault();
                                    setActiveFilter(this);
                                } else if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                                    e.preventDefault();
                                    const nextButton = filterButtons[index + 1] || filterButtons[0];
                                    nextButton.focus();
                                } else if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                                    e.preventDefault();
                                    const prevButton = filterButtons[index - 1] || filterButtons[filterButtons.length - 1];
                                    prevButton.focus();
                                }
                            });
                        });

                        function setActiveFilter(button) {
                            filterButtons.forEach(btn => {
                                btn.classList.remove('active');
                                btn.setAttribute('aria-pressed', 'false');
                            });
                            button.classList.add('active');
                            button.setAttribute('aria-pressed', 'true');
                            currentFilter = button.dataset.severity;
                            filterFindings();

                            // Announce to screen readers
                            announceToScreenReader(`Filtered to ${currentFilter === 'all' ? 'all findings' : currentFilter + ' severity findings'}`);
                        }

                        // Enhanced collapsible sections with touch support
                        severityHeaders.forEach(header => {
                            let touchStartY = 0;
                            let touchEndY = 0;

                            // Mouse/keyboard events
                            header.addEventListener('click', function(e) {
                                if (!isTouch) {
                                    toggleSection(this);
                                }
                            });

                            header.addEventListener('keydown', function(e) {
                                if (e.key === 'Enter' || e.key === ' ') {
                                    e.preventDefault();
                                    toggleSection(this);
                                }
                            });

                            // Touch events for mobile
                            if (isTouch) {
                                header.addEventListener('touchstart', function(e) {
                                    touchStartY = e.touches[0].clientY;
                                }, { passive: true });

                                header.addEventListener('touchend', function(e) {
                                    touchEndY = e.changedTouches[0].clientY;
                                    const touchDiff = Math.abs(touchStartY - touchEndY);

                                    // Only toggle if it's a tap, not a scroll
                                    if (touchDiff < 10) {
                                        toggleSection(this);
                                    }
                                }, { passive: true });
                            }
                        });

                        function toggleSection(header) {
                            const isCollapsed = header.classList.contains('collapsed');
                            header.classList.toggle('collapsed');
                            const findingsList = header.nextElementSibling;
                            findingsList.classList.toggle('collapsed');

                            // Update ARIA attributes for accessibility
                            header.setAttribute('aria-expanded', !isCollapsed);

                            // Announce to screen readers
                            const severityText = header.textContent.trim();
                            announceToScreenReader(`${severityText} section ${isCollapsed ? 'expanded' : 'collapsed'}`);
                        }

                        // Enhanced filtering with performance optimization
                        function filterFindings() {
                            let visibleCount = 0;
                            const severityGroups = document.querySelectorAll('.severity-group');

                            // Use requestAnimationFrame for smooth UI updates
                            requestAnimationFrame(() => {
                                severityGroups.forEach(group => {
                                    const severity = group.dataset.severity;
                                    const groupFindings = group.querySelectorAll('.finding');
                                    let groupVisibleCount = 0;

                                    groupFindings.forEach(finding => {
                                        const text = finding.textContent.toLowerCase();
                                        const matchesSearch = currentSearch === '' || text.includes(currentSearch);
                                        const matchesFilter = currentFilter === 'all' || severity === currentFilter;

                                        if (matchesSearch && matchesFilter) {
                                            finding.style.display = 'block';
                                            groupVisibleCount++;
                                            visibleCount++;
                                        } else {
                                            finding.style.display = 'none';
                                        }
                                    });

                                    // Show/hide entire severity group
                                    if (groupVisibleCount > 0 && (currentFilter === 'all' || severity === currentFilter)) {
                                        group.style.display = 'block';
                                    } else {
                                        group.style.display = 'none';
                                    }
                                });

                                // Show/hide no findings message
                                if (visibleCount === 0) {
                                    noFindings.style.display = 'block';
                                    announceToScreenReader('No findings match your search criteria');
                                } else {
                                    noFindings.style.display = 'none';
                                    announceToScreenReader(`Showing ${visibleCount} finding${visibleCount !== 1 ? 's' : ''}`);
                                }

                                // Update URL hash for bookmarking (optional)
                                updateUrlHash();
                            });
                        }

                        // Screen reader announcements
                        function announceToScreenReader(message) {
                            const announcement = document.createElement('div');
                            announcement.setAttribute('aria-live', 'polite');
                            announcement.setAttribute('aria-atomic', 'true');
                            announcement.className = 'sr-only';
                            announcement.textContent = message;
                            document.body.appendChild(announcement);

                            setTimeout(() => {
                                document.body.removeChild(announcement);
                            }, 1000);
                        }

                        // URL hash management for bookmarking
                        function updateUrlHash() {
                            const params = new URLSearchParams();
                            if (currentFilter !== 'all') params.set('filter', currentFilter);
                            if (currentSearch) params.set('search', currentSearch);

                            const hash = params.toString();
                            if (hash) {
                                window.history.replaceState(null, null, '#' + hash);
                            } else {
                                window.history.replaceState(null, null, window.location.pathname);
                            }
                        }

                        // Load state from URL hash
                        function loadFromUrlHash() {
                            const hash = window.location.hash.substring(1);
                            if (hash) {
                                const params = new URLSearchParams(hash);
                                const filter = params.get('filter');
                                const search = params.get('search');

                                if (filter && filter !== 'all') {
                                    const filterButton = document.querySelector(`[data-severity="${filter}"]`);
                                    if (filterButton) {
                                        setActiveFilter(filterButton);
                                    }
                                }

                                if (search) {
                                    searchInput.value = search;
                                    currentSearch = search.toLowerCase();
                                    filterFindings();
                                }
                            }
                        }

                        // Keyboard shortcuts
                        document.addEventListener('keydown', function(e) {
                            // Ctrl/Cmd + F to focus search
                            if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                                e.preventDefault();
                                searchInput.focus();
                            }

                            // Escape to clear search and filters
                            if (e.key === 'Escape' && document.activeElement !== searchInput) {
                                searchInput.value = '';
                                currentSearch = '';
                                const allButton = document.querySelector('[data-severity="all"]');
                                if (allButton) setActiveFilter(allButton);
                            }
                        });

                        // Initialize from URL hash
                        loadFromUrlHash();

                        // Handle browser back/forward
                        window.addEventListener('hashchange', loadFromUrlHash);

                        // Performance monitoring (optional)
                        if (window.performance && window.performance.mark) {
                            window.performance.mark('report-interactive');
                        }
                    });

                    function exportToJson() {
                        const findings = [];
                        document.querySelectorAll('.finding').forEach(finding => {
                            const control = finding.querySelector('.control-badge')?.textContent || '';
                            const description = finding.querySelector('.finding-description')?.textContent || '';
                            const remediation = finding.querySelector('.finding-remediation')?.textContent || '';
                            const file = finding.querySelector('.meta-item:nth-child(1)')?.textContent.replace('File: ', '') || '';
                            const line = finding.querySelector('.meta-item:nth-child(2)')?.textContent.replace('Line: ', '') || '';

                            findings.push({
                                control_id: control,
                                description: description.trim(),
                                remediation: remediation.trim(),
                                file_path: file,
                                line: line
                            });
                        });

                        const dataStr = JSON.stringify(findings, null, 2);
                        const dataBlob = new Blob([dataStr], {type: 'application/json'});
                        const url = URL.createObjectURL(dataBlob);
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = 'security_findings.json';
                        link.click();
                        URL.revokeObjectURL(url);
                    }
                </script>
            </body>
            </html>
            